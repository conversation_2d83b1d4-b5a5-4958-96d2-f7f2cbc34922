$(document).ready(function() {
   var pathValue="berry-bus-mailpost-mailpost2-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /** "待处理"页签 */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"editInfo",title: "邮寄审核"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_MAIL_POST_2_view","objects":[["待审核"]]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="STATUS"){
                        setJsonParam(cols[i],"template",getTemplate("#= STATUS #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        /** "已提交"页签 */
        var toolbar2=getButtonTemplates(pathValue,[]);//工具条
        //请求参数
        var tablesGridJson2={
        		url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
        		sort: "",//排序
        		toolbar: toolbar2,
        		height: fullh-112,
        		read:{"query":"query_BR_MAIL_POST_2_view","objects":[["待发件", "待收件确认", "已收件确认", "审核不通过"]]},
        		headerFilter:function(cols,i){
        			if(i){
        				if(cols[i]["field"]&&cols[i]["field"]=="STATUS"){
        					setJsonParam(cols[i],"template",getTemplate("#= STATUS #","funcExce(\'"+pathValue+"open2\',\'#= ID #\');","txt"));
        				}
        			}
        		}
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取
        }
     };
     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var open2=function(ID){
         openWindow({
             url:"berry/bus/mailpost/mailpost2/add/add",
             title:"查看: 邮寄单.."
         },{"ID":ID, "IS_EDIT":"view"});
     }
    var open=function(ID){
        openWindow({
            url:"berry/bus/mailpost/mailpost2/add/add",
            title:"确认: 邮寄审核.."
        },{"ID":ID, "IS_EDIT":"edit"});
    }
     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行审核!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行审核操作!");
            return ;
        }
        var winOpts={
            url:"berry/bus/mailpost/mailpost2/add/add",
            title:"确认: 邮寄审核.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0], "IS_EDIT":"edit"});//传递id
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BR_MAIL_POST","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "open2":open2,//打开添加表单
         "editInfo":editInfo,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
