$(document).ready(function() {
    var pathValue="berry-bus-mailpost-mailpost4-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_MAIL_POST"
        };
    }
    
    var tables_CONTRACT_Grid;
    var tables_INVOICE_Grid;
    var tables_OTHERATTA_Grid;

    var M_ID = "";
    var IS_EDIT = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	// 控制保存按钮显示隐藏
    	IS_EDIT = params.IS_EDIT;
    	if (IS_EDIT == 'edit') {
    		$("#submit_"+pathValue).show();
    	}
    	if ( !IS_EDIT || IS_EDIT != 'edit' ) {
    		$("#submit_"+pathValue).hide();
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        M_ID = params.ID;
        M_ID = M_ID ? M_ID : "";
        // --------------------------------------------  邮寄合同
        var toolbar_CONTRACT=getButtonTemplates(pathValue,[
        ]);//工具条
        var tablesGridJson_CONTRACT={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_CONTRACT,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_INVOICE","objects":[ M_ID ]}
        };
        tables_CONTRACT_Grid = initKendoGrid("#tables_CONTRACT_Grid"+pathValue,tablesGridJson_CONTRACT);//初始化表格的方法
        
        // --------------------------------------------  邮寄发票
        var toolbar_INVOICE=getButtonTemplates(pathValue,[
        ]);//工具条
        var tablesGridJson_INVOICE={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_INVOICE,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_INVOICE","objects":[ M_ID ]}
        };
        tables_INVOICE_Grid = initKendoGrid("#tables_INVOICE_Grid"+pathValue,tablesGridJson_INVOICE);//初始化表格的方法
        
        // --------------------------------------------  邮寄其他附件
        var toolbar_OTHERATTA=getButtonTemplates(pathValue,[
        ]);//工具条
        var tablesGridJson_OTHERATTA={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_OTHERATTA,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_OTHERATTA","objects":[ M_ID ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="DL"){
                    	var templateInfo="<font class=\"theme-m-txt\" style=\"cursor:pointer;\" onclick=\"funcExce(\'"+pathValue+"download\'\'#= OTHER_ATTA #\');\" >下载 </font>";
                        setJsonParam(cols[i],"template", templateInfo);
                    }
                }
            }
        };
        tables_OTHERATTA_Grid = initKendoGrid("#tables_OTHERATTA_Grid"+pathValue,tablesGridJson_OTHERATTA);//初始化表格的方法
    }

    var submit=function(){
    	$("#STATUS"+pathValue).val("已收件确认");
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 