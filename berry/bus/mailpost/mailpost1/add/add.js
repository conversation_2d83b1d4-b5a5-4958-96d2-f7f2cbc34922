$(document).ready(function() {
    var pathValue="berry-bus-mailpost-mailpost1-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_MAIL_POST"
        };
    }
    
    var tables_CONTRACT_Grid;
    var tables_INVOICE_Grid;
    var tables_OTHERATTA_Grid;

    var M_ID = "";
    var IS_EDIT = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	// 控制保存按钮显示隐藏
    	IS_EDIT = params.IS_EDIT;
    	if (IS_EDIT == 'edit') {
    		$("#submit1_"+pathValue).show();
    		$("#submit0_"+pathValue).show();
    	}
    	if ( !IS_EDIT || IS_EDIT != 'edit' ) {
    		$("#submit1_"+pathValue).hide();
    		$("#submit0_"+pathValue).hide();
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        M_ID = params.ID;
        M_ID = M_ID ? M_ID : "";
        // --------------------------------------------  邮寄合同
        var toolbar_CONTRACT;//工具条
        if (IS_EDIT == 'edit') {
	        toolbar_CONTRACT=getButtonTemplates(pathValue,[
	            {name:"add",target:"addOpen_CONTRACT",title: "添加邮寄合同"},
	            {name:"delete",target:"deleteInfo_CONTRACT",title: "删除邮寄合同"},
	        ]);
        }
        if ( !IS_EDIT || IS_EDIT != 'edit' ) {
        	toolbar_CONTRACT=getButtonTemplates(pathValue,[ ]);
    	}
        var tablesGridJson_CONTRACT={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_CONTRACT,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_CONTRACT","objects":[ M_ID ]}
        };
        tables_CONTRACT_Grid = initKendoGrid("#tables_CONTRACT_Grid"+pathValue,tablesGridJson_CONTRACT);//初始化表格的方法
        
        // --------------------------------------------  邮寄发票
        var toolbar_INVOICE;//工具条
        if (IS_EDIT == 'edit') {
        	toolbar_INVOICE=getButtonTemplates(pathValue,[
                {name:"add",target:"addOpen_INVOICE",title: "添加邮寄发票"},
                {name:"delete",target:"deleteInfo_INVOICE",title: "删除邮寄发票"},
            ]);
        }
        if ( !IS_EDIT || IS_EDIT != 'edit' ) {
        	toolbar_INVOICE=getButtonTemplates(pathValue,[ ]);
    	}
        var tablesGridJson_INVOICE={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_INVOICE,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_INVOICE","objects":[ M_ID ]}
        };
        tables_INVOICE_Grid = initKendoGrid("#tables_INVOICE_Grid"+pathValue,tablesGridJson_INVOICE);//初始化表格的方法
        
        // --------------------------------------------  邮寄其他附件
        var toolbar_OTHERATTA;//工具条
        if (IS_EDIT == 'edit') {
        	toolbar_OTHERATTA=getButtonTemplates(pathValue,[
                {name:"add",target:"addOpen_OTHERATTA",title: "添加邮寄附件"},
                {name:"delete",target:"deleteInfo_OTHERATTA",title: "删除邮寄附件"},
            ]);
        }
        if ( !IS_EDIT || IS_EDIT != 'edit' ) {
        	toolbar_OTHERATTA=getButtonTemplates(pathValue,[ ]);
    	}
        var tablesGridJson_OTHERATTA={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_OTHERATTA,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_OTHERATTA","objects":[ M_ID ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="DL"){
                    	var templateInfo="<font class=\"theme-m-txt\" style=\"cursor:pointer;\" onclick=\"funcExce(\'"+pathValue+"download\',\'#= OTHER_ATTA #\');\" >下载 </font>";
                        setJsonParam(cols[i],"template", templateInfo);
                    }
                }
            }
        };
        tables_OTHERATTA_Grid = initKendoGrid("#tables_OTHERATTA_Grid"+pathValue,tablesGridJson_OTHERATTA);//初始化表格的方法
        
//        // 重置合同字段选择框参数
//        resetSelectComponentAttr({
//        		fieldID : "CONTRACT_NAME",
//		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
//		        	if (obj.CONTRACT_NAME != value) {
//		        		obj.CONTRACT_NAME = value;
//			        	$("#INVOICE_ID"+pathValue).val("");
//			        	$("#INVOICE_NO"+pathValue).val("");
//		        	}
//		        }
//        }, "CONTRACT_NAME"+pathValue);
//    	// 重置发票字段选择框参数
//        resetSelectComponentAttr({
//        		fieldID : "INVOICE_NO",
//        		openBeforeCheck: function() {// 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
//        			var CONTRACT_NO = $("#CONTRACT_NO"+pathValue).val();
//        			if (!CONTRACT_NO) {
//        				alertMsg("请选择合同，再选择发票","error");
//            			return false;
//        			}
//        			return true;
//        		},
//        		searchparamsSettings : function(o) {// 设置查询条件参数
//        			var CONTRACT_NO = $("#CONTRACT_NO"+pathValue).val();
//        			// 以下代码固定格式
//        			o.params = o.params ? o.params : {};
//        			o.params.search = { "CONTRACT_NO":CONTRACT_NO };
//		        },
//		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
//		        }
//        }, "INVOICE_NO"+pathValue);
    }
    var callBack=function(){
    	refreshGrid_CONTRACT();
    	refreshGrid_INVOICE();
    	refreshGrid_OTHERATTA();
    };
    var refreshGrid_CONTRACT=function(){
        var tablesGridJson_CONTRACT={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_CONTRACT","objects":[ M_ID ]}
        };
        setGridDataSource("#tables_CONTRACT_Grid"+pathValue,tablesGridJson_CONTRACT);
    }
    var refreshGrid_INVOICE=function(){
        var tablesGridJson_INVOICE={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_INVOICE","objects":[ M_ID ]}
        };
        setGridDataSource("#tables_INVOICE_Grid"+pathValue,tablesGridJson_INVOICE);
     }
    var refreshGrid_OTHERATTA=function(){
        var tablesGridJson_OTHERATTA={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-500,
            read:{"query":"query_BR_MAIL_POST_MX_view_OTHERATTA","objects":[ M_ID ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="DL"){
                    	var templateInfo="<font class=\"theme-m-txt\" style=\"cursor:pointer;\" onclick=\"funcExce(\'"+pathValue+"download\',\'#= OTHER_ATTA #\');\" >下载 </font>";
                        setJsonParam(cols[i],"template", templateInfo);
                    }
                }
            }
        };
        setGridDataSource("#tables_OTHERATTA_Grid"+pathValue,tablesGridJson_OTHERATTA);
     }
    
    var submit0 = function() {//存为草稿
    	submit(0);
    }
    var submit1 = function() {//保存提交
    	submit(1);
    }

    var submit=function(i){
    	var ID = $("#ID"+pathValue).val();
    	var STATUS = $("#STATUS"+pathValue).val();
    	if (i == 1 && (!STATUS || STATUS=="草稿")) {
        	$("#STATUS"+pathValue).val("待审核");
    	} else if (i != 1 && !STATUS) {
    		$("#STATUS"+pathValue).val("草稿");
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	if (i == 0) {
                		if ( !M_ID ) {
	                		$("#ID"+pathValue).val(result.ID);
	                		M_ID=result.ID;
                		}
                        alertMsg("保存成功","success",function(){
                            funcExce(pathValue+"pageCallBack");//执行回调
                        });
                	}
                	if (i == 1) {
                        alertMsg("提交成功","success",function(){
                            funcExce(pathValue+"pageCallBack");//执行回调
                            funcExce(pathValue+"close");//关闭页面
                        });
                	}
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var deleteInfo_CONTRACT=function(){
    	deleteInfoMX(tables_CONTRACT_Grid);
    }
    var deleteInfo_INVOICE=function(){
    	deleteInfoMX(tables_INVOICE_Grid);
    }
    var deleteInfo_OTHERATTA=function(){
    	deleteInfoMX(tables_OTHERATTA_Grid);
    }
     var deleteInfoMX=function(delGrid){
        var arrIds=getSelectData(delGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BR_MAIL_POST_MX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }

     var addOpen_CONTRACT = function() {
    	 if ( !M_ID ) {
    		 alertMsg("请先保存主单，再添加明细");
    		 return;
    	 }
         var winOpts={
             url:"berry/bus/mailpost/mailpost1/add/addMxCONTRACT",
             title:"新增: 邮寄合同.."
         };
         openWindow(winOpts,{"M_ID":M_ID, "pPathValue":pathValue});
     }
     var addOpen_INVOICE = function() {
    	 if ( !M_ID ) {
    		 alertMsg("请先保存主单，再添加明细");
    		 return;
    	 }
         var winOpts={
             url:"berry/bus/mailpost/mailpost1/add/addMxINVOICE",
             title:"新增: 邮寄发票.."
         };
         openWindow(winOpts,{"M_ID":M_ID, "pPathValue":pathValue});
     }
     var addOpen_OTHERATTA = function() {
    	 if ( !M_ID ) {
    		 alertMsg("请先保存主单，再添加明细");
    		 return;
    	 }
         var winOpts={
             url:"berry/bus/mailpost/mailpost1/add/addMxOTHERATTA",
             title:"新增: 邮寄其他附件.."
         };
         openWindow(winOpts,{"M_ID":M_ID, "pPathValue":pathValue});
     }
     
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit0":submit0,
        "submit1":submit1,
        
        "deleteInfo_CONTRACT":deleteInfo_CONTRACT,
        "deleteInfo_INVOICE":deleteInfo_INVOICE,
        "deleteInfo_OTHERATTA":deleteInfo_OTHERATTA,
        
        "refreshGrid_CONTRACT":refreshGrid_CONTRACT,
        "refreshGrid_INVOICE":refreshGrid_INVOICE,
        "refreshGrid_OTHERATTA":refreshGrid_OTHERATTA,
        
        "addOpen_CONTRACT":addOpen_CONTRACT,
        "addOpen_INVOICE":addOpen_INVOICE,
        "addOpen_OTHERATTA":addOpen_OTHERATTA,
    });
 
 });
 