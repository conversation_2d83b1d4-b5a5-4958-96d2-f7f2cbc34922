$(document).ready(function() {
    var pathValue="berry-bus-transferLetter-transferLetter2-approve-approve";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_TRANSFER_LETTER"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }

    var submit=function(){

    	var shyj = $("#APPROVE_RS"+pathValue).val();
    	
    	if (shyj=="通过") {
    		$("#STATUS"+pathValue).val("审核通过");
    	}
    	if (shyj=="不通过") {
    		$("#STATUS"+pathValue).val("审核不通过");
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 