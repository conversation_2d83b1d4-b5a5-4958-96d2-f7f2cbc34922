$(document).ready(function() {
    var pathValue="berry-bus-transferLetter-transferLetter1-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_TRANSFER_LETTER"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        
        // 重置发票字段选择框参数
        resetSelectComponentAttr({
    		fieldID : "INVOICE_NO",
    		openBeforeCheck: function() {// 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
    			var OUT_CONTRACT_NO = $("#OUT_CONTRACT_NO"+pathValue).val();
    			if (!OUT_CONTRACT_NO) {
    				alertMsg("请选择转出合同，再选择发票","error");
        			return false;
    			}
    			return true;
    		},
    		searchparamsSettings : function(o) {// 设置查询条件参数
    			var CONTRACT_NO = $("#OUT_CONTRACT_NO"+pathValue).val();
    			// 以下代码固定格式
    			o.params = o.params ? o.params : {};
    			o.params.search = { "CONTRACT_NO":CONTRACT_NO };
	        },
	        addSettings : function(obj, value) {// 返回值后,追加自定义操作
	        }
	    }, "INVOICE_NO"+pathValue);
    }

    var submit1 = function() {
    	submit(1);
    }
    var submit2 = function() {
    	submit(2);
    }
    
    var submit=function(submitType){
    	var ID = $("#ID"+pathValue).val();
    	var APPLY_NO = $("#APPLY_NO"+pathValue).val();
    	
    	if (submitType=="1") {
    		$("#STATUS"+pathValue).val("草稿");
    	}
    	if (submitType=="2") {
    		$("#STATUS"+pathValue).val("待审核");
    	}
    	
    	var url= "berry/serialNumberManage/transferLetter/applyNORepeatCheck";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: { "ID":ID, "APPLY_NO":APPLY_NO},
            succeed:function(result){
	            if(result["code"]>0){
	            	if(result.count >= 1){
	            		alertMsg("申请单编号已存在，请重新获取！","error");
	            		$("#APPLY_NO"+pathValue).val("");
	            		return;
	            	}else{
	            		formSubmit({
	                        url:"system/jdbc/save/one/table",
	                        formId:"form",
	                        pathValue:pathValue,
	                        succeed:function(result){
	                            if(result["code"]>0){
	                                //提交成功
	                                alertMsg("提交成功","success",function(){
	                                    funcExce(pathValue+"pageCallBack");//执行回调
	                                    funcExce(pathValue+"close");//关闭页面
	                                });
	                            }else{
	                                alertMsg("提交失败","error");
	                            }
	                        }
	                    });
	            	}
	            }else{
	                alertMsg("提示:提交失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:提交异常!","error");
            }
        });
    }
    
    //获取申请单编号
    var getApplyNO=function(){
    	var APPLY_NO = $("#APPLY_NO"+pathValue).val();
    	var url= "berry/serialNumberManage/transferLetter/applyNO";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: {"APPLY_NO":APPLY_NO},
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#APPLY_NO"+pathValue).val(result.APPLY_NO);
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit1":submit1,
        "submit2":submit2,
        "getApplyNO":getApplyNO,
    });
 
 });
 