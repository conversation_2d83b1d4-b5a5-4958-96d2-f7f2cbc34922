
var berry_bus_bus_area_index_nodeId = "";
var berry_bus_bus_area_index_nodeText = "";
var berry_bus_bus_area_index_nodeCode = "";
var berry_bus_bus_area_index_pCode = "";
var berry_bus_bus_area_index_topCode = "";
var berry_bus_bus_area_index_pathValue = "berry-bus-bus-area-index";

$(document).ready(function() {
   var pathValue = berry_bus_bus_area_index_pathValue;
   
   intiBusAreaIndex();
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
//            {name:"add",title:"添加顶级",target:"addOpen2"},
            {name:"add",title:"添加同级",target:"addOpen"},
            {name:"add",title:"添加下级",target:"addOpen1"},
            {name:"delete",title:"删除节点",target:"deleteInfo"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-72,
            read:{"query":"query_BR_BUS_AREA_view","objects":[ berry_bus_bus_area_index_nodeId ]},
			headerFilter:function(cols,i){
			    if(i){
			        if(cols[i]["field"]&&cols[i]["field"]=="BUS_AREA_NAME"){
			            setJsonParam(cols[i],"template",getTemplate("#= BUS_AREA_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_BUS_AREA_NAME #\',\'#= TOP_BUS_AREA_NAME #\');","txt"));
			        }
			    }
			}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }

    var addOpen=function(){
    	if (!berry_bus_bus_area_index_nodeCode) {
    		alertMsg("请先选择左边树型结构节点，再进行添加同级操作");
    		return;
    	}
    	if (berry_bus_bus_area_index_nodeCode=="root") {
    		alertMsg("“"+berry_bus_bus_area_index_nodeText+"”节点不能添加同级操作");
    		return;
    	}
        openWindow({
            url:"berry/bus/bus/area/add/add",
            title:"新增业务分区.."
        },{
        	"P_BUS_AREA_CODE": berry_bus_bus_area_index_pCode,
        	"TOP_BUS_AREA_CODE": berry_bus_bus_area_index_topCode,
        });
    }
    var addOpen1=function(){
    	if (!berry_bus_bus_area_index_nodeCode) {
    		alertMsg("请先选择左边树型结构节点，再进行添加下级操作");
    		return;
    	}
        openWindow({
            url:"berry/bus/bus/area/add/add",
            title:"新增业务分区.."
        },{
        	"P_BUS_AREA_CODE": berry_bus_bus_area_index_nodeCode,
        	"TOP_BUS_AREA_CODE": berry_bus_bus_area_index_topCode ? berry_bus_bus_area_index_topCode : berry_bus_bus_area_index_nodeCode == "root" ? "" : berry_bus_bus_area_index_nodeCode,
        });
    }
    var addOpen2=function(){
        openWindow({
            url:"berry/bus/bus/area/add/add",
            title:"新增业务分区.."
        },{
        	"P_BUS_AREA_CODE": "root",
        	"TOP_BUS_AREA_CODE": "",
        });
    }

    var open=function(id, P_BUS_AREA_NAME, TOP_BUS_AREA_NAME){
        openWindow({
            url:"berry/bus/bus/area/add/add",
            title:"编辑业务分区.."
        },{"ID":id,"P_BUS_AREA_NAME":P_BUS_AREA_NAME,"TOP_BUS_AREA_NAME":TOP_BUS_AREA_NAME});
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
    	 // 回调刷新树型
    	 initBusAreaIndexSetData();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     var deleteInfo=function(){
        if( ! berry_bus_bus_area_index_nodeCode ){
            alertMsg("请选择要删除节点!");
            return ;
        }
    	if (berry_bus_bus_area_index_nodeCode=="root") {
    		alertMsg("“"+berry_bus_bus_area_index_nodeText+"”节点不能删除操作");
    		return;
    	}
        alertMsg("确定删除选中的“"+berry_bus_bus_area_index_nodeText+"”节点及其所有子节点吗？", "success", function() {
        	var url="berry/bus/bus/area/delnodes";
        	$.fn.ajaxPost({
    		   ajaxUrl: url,
    		   ajaxType: "post",
    		   ajaxData: { code: berry_bus_bus_area_index_nodeCode },
    		   succeed: function(rs) {
    			   initBusAreaIndexSetData();
    			   
    			   setBerryBusBusAreaIndexValues(null);
    			   
    			   initBusAreaIndexGrid();
    		   }
    	   });
        });
     }
     
     var importExcel=function(){
    	 alertMsg(123);
     }


     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "addOpen1":addOpen1,//打开添加表单
         "addOpen2":addOpen2,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});

function intiBusAreaIndex() {
   var pathValuE = berry_bus_bus_area_index_pathValue;
   $("#treeview"+pathValuE).kendoTreeView({
	   dataTextField:"BUS_AREA_NAME",
	   template:function (dataItem) {
		   var value = "";
		   if(dataItem && dataItem["item"]){
			   if (dataItem["item"]["childNodeCount"] && dataItem["item"]["childNodeCount"]>0) {
				   // 有子节点
				   value = '<span class="k-sprite folder"></span>';
			   } else {
				   // 没有有子节点
				   value = '<span class="k-icon theme-m-txt k-i-paste-plain-text"></span>';
			   }
			   value += dataItem["item"]["BUS_AREA_NAME"];
		   }
		   return value;
	   },
	   select: function(e) {
		   
		   this.expand(e.node);
		   
	     var dataItem = this.dataItem(e.node);
	     setBerryBusBusAreaIndexValues(dataItem);
	     
	     initBusAreaIndexGrid();
	   }
   });

   initBusAreaIndexSetData();
}

function setBerryBusBusAreaIndexValues(dataItem) {
	if (dataItem) {
	    berry_bus_bus_area_index_nodeId = dataItem.ID;
	    berry_bus_bus_area_index_nodeText = dataItem.BUS_AREA_NAME;
	    berry_bus_bus_area_index_nodeCode = dataItem.BUS_AREA_CODE;
	    berry_bus_bus_area_index_pCode = dataItem.P_BUS_AREA_CODE ? dataItem.P_BUS_AREA_CODE : "";
	    berry_bus_bus_area_index_topCode = dataItem.TOP_BUS_AREA_CODE ? dataItem.TOP_BUS_AREA_CODE : "";
	} else {
	    berry_bus_bus_area_index_nodeId = "";
	    berry_bus_bus_area_index_nodeText = "";
	    berry_bus_bus_area_index_nodeCode = "";
	    berry_bus_bus_area_index_pCode = "";
	    berry_bus_bus_area_index_topCode = "";
	}
}

function initBusAreaIndexGrid() {
    var tableId="#tablesGrid"+berry_bus_bus_area_index_pathValue;
        if ($(tableId).data("kendoGrid") != undefined) {
           //请求参数
           var tablesGridJson={
               url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
               sort: "",//排序
               toolbar: toolbar,
               read:{"query":"query_BR_BUS_AREA_view","objects":[ berry_bus_bus_area_index_nodeId ]},
			headerFilter:function(cols,i){
			    if(i){
			        if(cols[i]["field"]&&cols[i]["field"]=="BUS_AREA_NAME"){
			            setJsonParam(cols[i],"template",getTemplate("#= BUS_AREA_NAME #","funcExce(\'"+berry_bus_bus_area_index_pathValue+"open\',\'#= ID #\',\'#= P_BUS_AREA_NAME #\',\'#= TOP_BUS_AREA_NAME #\');","txt"));
			        }
			    }
			}
           };
           setGridDataSource(tableId,tablesGridJson);
        }
}

function initBusAreaIndexSetData() {
   var pathValuE = berry_bus_bus_area_index_pathValue;
   $.fn.ajaxPost({
	   ajaxUrl: "berry/bus/bus/area/treeview",
	   ajaxType: "post",
	   ajaxData: {},
	   succeed: function(rs) {
		   var treeDataSource = rs.treeview;
		   var treeview = $("#treeview"+pathValuE).data("kendoTreeView");
		   treeview.setDataSource(new kendo.data.HierarchicalDataSource({
			   data: treeDataSource
		   }));
		   /*
		   if (berry_bus_bus_area_index_nodeId) {
			   var node = treeview.findByText(berry_bus_bus_area_index_nodeText);
			   var d = treeview.dataItem(node);
			   treeview.expandTo(d);
			   treeview.select(node);
		   }*/
	   }
   });
}