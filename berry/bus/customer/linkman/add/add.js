$(document).ready(function() {
    var pathValue="berry-bus-customer-linkman-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CUSTOMER_LINKMAN"
        };
    }

    var COMPANY_NAME_VAL = "";
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
    	// 重置    客户单位名称    字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "COMPANY_NAME",
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        	if (COMPANY_NAME_VAL != value) {
		        		COMPANY_NAME_VAL = value;
			        	$("#SUBJECT_GROUP_ID"+pathValue).val("");
			        	$("#SUBJECT_GROUP_CODE"+pathValue).val("");
			        	$("#SUBJECT_GROUP_NAME"+pathValue).val("");
		        	}
		        }
        }, "COMPANY_NAME"+pathValue);
    	// 重置    课题组名称    字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "SUBJECT_GROUP_NAME",
        		openBeforeCheck: function() {// 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
        			var COMPANY_ID = $("#COMPANY_ID"+pathValue).val();
        			if (!COMPANY_ID) {
        				alertMsg("请选择客户单位，再选择课题组名称","error");
            			return false;
        			}
        			return true;
        		},
        		searchparamsSettings : function(o) {// 设置查询条件参数
        			var COMPANY_ID = $("#COMPANY_ID"+pathValue).val();
        			// 以下代码固定格式
        			o.params = o.params ? o.params : {};
        			o.params.search = { "COMPANY_ID":COMPANY_ID };
		        },
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        }
        }, "SUBJECT_GROUP_NAME"+pathValue);
    }

    var submit0 = function() {
    	submit(0);
    }
    var submit1 = function() {
    	submit(1);
    }
    var submit=function(i){
    	var STATUS = $("#STATUS"+pathValue).val();
    	if (i == 1 && (!STATUS || STATUS=="草稿")) {
        	$("#STATUS"+pathValue).val("正常");
    	} else if (i != 1 && !STATUS) {
    		$("#STATUS"+pathValue).val("草稿");
    	}
    	
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	var CUSTOMER_CODE = $.trim( $("#CUSTOMER_CODE"+pathValue).val() );
    	
    	$("#ID"+pathValue).val(ID);
    	$("#CUSTOMER_CODE"+pathValue).val(CUSTOMER_CODE);
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","error");
            return false;
        }
    	
    	if (ID) {
    		submit2();
    	} else {
        	var params={"ID":ID, "CUSTOMER_CODE":CUSTOMER_CODE};
            var url="berry/serialNumberManage/customer/customerCodeRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0 && result["count"]==0){
    	            	submit2();
    	            }else if(result["code"]>0){
    	            	$("#CUSTOMER_CODE"+pathValue).val("");
    	                alertMsg("编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    var submit2 = function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var getCustomerCode=function(){
    	var ID = $("#ID"+pathValue).val();
    	var CUSTOMER_CODE = $("#CUSTOMER_CODE"+pathValue).val();
    	if (!ID || !CUSTOMER_CODE) {// ID为空，新增数据允许获取编号, 或 编号为空允许获取编号
    		var params={"ID":ID};
            var url="berry/serialNumberManage/customer/customerCode";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	            	$("#CUSTOMER_CODE"+pathValue).val(result["CUSTOMER_CODE"]);
    	            }else{
    	                alertMsg("获取编码失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit0":submit0,
        "submit1":submit1,
        "getCustomerCode":getCustomerCode,
    });
 
 });
 