$(document).ready(function() {
    var pathValue="berry-bus-customer-company-addSubjectGroup-add";
    
    var addPathValue;
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CUSTOMER_SUBJECT_GROUP"
        };
    }

    var PROVINCE_NAME_VAL = "";
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	addPathValue = params.addPathValue;
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        // 去除客户单位字段选择按钮
        var COMPANY_NAME_OBJ = $("#COMPANY_NAME"+pathValue);
        var COMPANY_NAME_NEXT_OBJ = COMPANY_NAME_OBJ ? $(COMPANY_NAME_OBJ).next() : null;
        if (COMPANY_NAME_NEXT_OBJ) {
        	$(COMPANY_NAME_NEXT_OBJ).hide();
        }
        
    	// 重置省份字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "PROVINCE_NAME",
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        	if (PROVINCE_NAME_VAL != value) {
		        		PROVINCE_NAME_VAL = value;
			        	$("#CITY_CODE"+pathValue).val("");
			        	$("#CITY_NAME"+pathValue).val("");
		        	}
		        }
        }, "PROVINCE_NAME"+pathValue);
    	// 重置城市字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "CITY_NAME",
        		openBeforeCheck: function() {// 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
        			var PROVINCE_CODE = $("#PROVINCE_CODE"+pathValue).val();
        			if (!PROVINCE_CODE) {
        				alertMsg("请选择省份，再选择城市","error");
            			return false;
        			}
        			return true;
        		},
        		searchparamsSettings : function(o) {// 设置查询条件参数
        			var PROVINCE_CODE = $("#PROVINCE_CODE"+pathValue).val();
        			// 以下代码固定格式
        			o.params = o.params ? o.params : {};
        			o.params.search = { "P_ITEM_CODE":PROVINCE_CODE };
		        },
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        }
        }, "CITY_NAME"+pathValue);
    }
    
    var submit0 = function() {
    	submit(0);
    }
    var submit1 = function() {
    	submit(1);
    }
    var submit = function(i) {
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	var SUBJECT_GROUP_CODE = $.trim( $("#SUBJECT_GROUP_CODE"+pathValue).val() );
    	
    	$("#ID"+pathValue).val(ID);
    	$("#SUBJECT_GROUP_CODE"+pathValue).val(SUBJECT_GROUP_CODE);
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","error");
            return false;
        }
    	
    	if (ID) {
    		submit2(i);
    	} else {
        	var params={"ID":ID, "SUBJECT_GROUP_CODE":SUBJECT_GROUP_CODE};
            var url="berry/serialNumberManage/customer/subjectGroupCodeRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0 && result["count"]==0){
    	            	submit2(i);
    	            }else if(result["code"]>0){
    	            	$("#SUBJECT_GROUP_CODE"+pathValue).val("");
    	                alertMsg("编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    var submit2 = function(i){
    	var ID = $("#ID"+pathValue).val();
    	var STATUS = $("#STATUS"+pathValue).val();
    	if (i == 1 && (!STATUS || STATUS=="草稿")) {
        	$("#STATUS"+pathValue).val("正常");
    	} else if (i != 1 && !STATUS) {
    		$("#STATUS"+pathValue).val("草稿");
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(addPathValue+"refreshGrid_2");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var getSubjectGroupCode=function(){
    	var ID = $("#ID"+pathValue).val();
    	var SUBJECT_GROUP_CODE = $("#SUBJECT_GROUP_CODE"+pathValue).val();
    	if (!ID || !SUBJECT_GROUP_CODE) {// ID为空，新增数据允许获取编号, 或 编号为空允许获取编号
    		var params={"ID":ID};
            var url="berry/serialNumberManage/customer/subjectGroupCode";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	            	$("#SUBJECT_GROUP_CODE"+pathValue).val(result["SUBJECT_GROUP_CODE"]);
    	            }else{
    	                alertMsg("获取编码失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit0":submit0,
        "submit1":submit1,
        "getSubjectGroupCode":getSubjectGroupCode,
    });
 
 });
 