$(document).ready(function() {
   var pathValue="berry-bus-customer-company-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen"},
            {name:"edit",target:"editInfo1"},
            {name:"delete",target:"deleteInfo1"},
            {name:"submit",target:"submitNormal",title: "提交"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_CUSTOMER_COMPANY_view","objects":[ ["草稿"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"edit",target:"editInfo2"},
//            {name:"delete",target:"deleteInfo2"},
            {name:"obsolete",target:"obsolete",title: "标为作废"},
            {name:"recover",target:"recoverNormal",title: "恢复正常"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
           height: fullh-112,
            read:{"query":"query_BR_CUSTOMER_COMPANY_view","objects":[ ["正常", "作废"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/customer/company/add/add",
            title:"新增: 客户单位.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
        openWindow({
            url:"berry/bus/customer/company/add/add",
            title:"编辑: 客户单位.."
        },{"ID":id});
    }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     var refreshGrid2=function(){
        if(tablesGrid2){
            tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo1=function(){
    	 editInfo_F(1);
     }
     var editInfo2=function(){
    	 editInfo_F(2);
     }
     var editInfo_F=function(tgIndex){
        var arrIds=getSelectData(tablesGrid);
        if (tgIndex == 2) {
        	arrIds=getSelectData(tablesGrid2);
        }
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var winOpts={
            url:"berry/bus/customer/company/add/add",
            title:"编辑: 客户单位.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0]});//传递id
     }

     var deleteInfo1=function(){
    	 deleteInfo_F(1);
     }
     var deleteInfo2=function(){
    	 deleteInfo_F(2); 
     }
     var deleteInfo_F=function(tgIndex){
         var arrIds=getSelectData(tablesGrid);
         if (tgIndex == 2) {
         	arrIds=getSelectData(tablesGrid2);
         }
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
//	        var params={"tableName":"BR_CUSTOMER_COMPANY","ids":arrIds};
//	        var url="system/jdbc/delete/batch/table";
	        var params={"ids":arrIds};
	        var url="berry/bus/customer/company/del";
	        deleteGridDataByIds(url,params,callBack);
    	});
     }
     
     var obsolete = function(){
         var arrIds = getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
        // 提示再次确认
     	confirmMsg("确认", "确定要作废选中的数据吗?", "warn", function() {
	         var params={"ids":arrIds};
	         var url="berry/bus/customer/company/obsolete";
	         $.fn.ajaxPost({
		  		   ajaxUrl: url,
		  		   ajaxType: "post",
		  		   ajaxData: params,
		  		   succeed: function(rs) {
		  			   if (rs.code=='1') {
			  				 refreshGrid2();
			  			   }
		  			   alertMsg("操作"+rs.message);
		  		   }
		  	   });
  	    });
     }
     
     var recoverNormal = function(){
         var arrIds = getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
         
        // 提示再次确认
     	confirmMsg("确认", "确定要恢复选中的数据吗?", "warn", function() {
	         var params={"ids":arrIds};
	         var url="berry/bus/customer/company/recoverNormal";
	         $.fn.ajaxPost({
		  		   ajaxUrl: url,
		  		   ajaxType: "post",
		  		   ajaxData: params,
		  		   succeed: function(rs) {
		  			   if (rs.code=='1') {
		  				 refreshGrid2();
		  			   }
			  		   alertMsg("操作"+rs.message);
		  		   }
		  	 });
  	    });
     }

	var submitNormal = function(){
        var arrIds = getSelectData(tablesGrid);
	    if(arrIds.length==0){
	        alertMsg("请至少选择一条数据!");
	        return ;
	    }
	    
	    // 提示再次确认
    	confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
		    var params={"ids":arrIds};
		    var url="berry/bus/customer/company/submitNormal";
		    $.fn.ajaxPost({
	 		   ajaxUrl: url,
	 		   ajaxType: "post",
	 		   ajaxData: params,
	 		   succeed: function(rs) {
	 			   if (rs.code=='1') {
	 				 refreshGrid();
	 				 refreshGrid2();
	 			   }
		  		   alertMsg("操作"+rs.message);
	 		   }
	 	   });
  	   });
	}

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo1":editInfo1,
         "editInfo2":editInfo2,
         "deleteInfo1":deleteInfo1,
         "deleteInfo2":deleteInfo2,
         "refreshGrid":refreshGrid,
         "refreshGrid2":refreshGrid2,
         "callBack":callBack,
         "obsolete":obsolete,
         "recoverNormal":recoverNormal,
         "submitNormal":submitNormal,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
