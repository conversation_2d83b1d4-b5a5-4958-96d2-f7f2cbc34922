$(document).ready(function() {
    var pathValue="berry-bus-customer-company-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CUSTOMER_COMPANY"
        };
    }

    var PROVINCE_NAME_VAL = "";
    
    var PAGR_DATA_ID = "-";
    
    var tablesGrid_1;
    var tablesGrid_2;
    var tablesGrid_3;
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	if (params && params.ID) {
    		PAGR_DATA_ID = params.ID;
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
    	// 重置省份字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "PROVINCE_NAME",
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        	if (PROVINCE_NAME_VAL != value) {
		        		PROVINCE_NAME_VAL = value;
			        	$("#CITY_CODE"+pathValue).val("");
			        	$("#CITY_NAME"+pathValue).val("");
		        	}
		        }
        }, "PROVINCE_NAME"+pathValue);
    	// 重置城市字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "CITY_NAME",
        		openBeforeCheck: function() {// 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
        			var PROVINCE_CODE = $("#PROVINCE_CODE"+pathValue).val();
        			if (!PROVINCE_CODE) {
        				alertMsg("请选择省份，再选择城市","error");
            			return false;
        			}
        			return true;
        		},
        		searchparamsSettings : function(o) {// 设置查询条件参数
        			var PROVINCE_CODE = $("#PROVINCE_CODE"+pathValue).val();
        			// 以下代码固定格式
        			o.params = o.params ? o.params : {};
        			o.params.search = { "P_ITEM_CODE":PROVINCE_CODE };
		        },
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        }
        }, "CITY_NAME"+pathValue);
        
        searchGrid_1();
        searchGrid_2();
        searchGrid_3();
    }
    
    var searchGrid_1 = function() {
 	   var searchparams = { "COMPANY_ID":PAGR_DATA_ID };
 	   
 	  var toolbar=getButtonTemplates(pathValue,[
          {name:"add",target:"addOpenGrid_1",title:"新增"},
          {name:"edit",target:"editInfoGrid_1",title:"修改"},
          {name:"delete",target:"deleteInfoGrid_1",title:"删除"},
          {name:"submit",target:"submitGrid_1",title:"提交"},
      ]);//工具条
 	   var tablesGridJson = {
 	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
 	            sort: "",//排序
 	            toolbar: toolbar,
 	            height: fullh-460,
 	            read:{"query":"query_BR_CUSTOMER_COMPANY_INFO_view","objects":[ ["草稿", "正常", "作废"] , ["草稿", "正常", "作废"] ],
 	            	"search": searchparams
 	            }
 	        };
 	   if (tablesGrid_1) {
 	 	  setGridDataSource("#tablesGrid_1_"+pathValue,tablesGridJson);
 	   } else {
 		  tablesGrid_1 = initKendoGrid("#tablesGrid_1_"+pathValue,tablesGridJson);//初始化表格的方法
 	   }
    }
    var searchGrid_2 = function() {
  	   var searchparams = { "COMPANY_ID":PAGR_DATA_ID };
  	   
  	  var toolbar=getButtonTemplates(pathValue,[
           {name:"add",target:"addOpenGrid_2",title:"新增"},
           {name:"edit",target:"editInfoGrid_2",title:"修改"},
           {name:"delete",target:"deleteInfoGrid_2",title:"删除"},
           {name:"submit",target:"submitGrid_2",title:"提交"},
       ]);//工具条
  	   var tablesGridJson = {
  	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
  	            sort: "",//排序
  	            toolbar: toolbar,
  	            height: fullh-460,
  	            read:{"query":"query_BR_CUSTOMER_SUBJECT_GROUP_view","objects":[ ["草稿", "正常", "作废"] , ["草稿", "正常", "作废"] ],
  	            	"search": searchparams
  	            }
  	        };
  	   if (tablesGrid_2) {
  	 	  setGridDataSource("#tablesGrid_2_"+pathValue,tablesGridJson);
  	   } else {
  		  tablesGrid_2 = initKendoGrid("#tablesGrid_2_"+pathValue,tablesGridJson);//初始化表格的方法
  	   }
     }
    var searchGrid_3 = function() {
   	   var searchparams = { "COMPANY_ID":PAGR_DATA_ID };
   	   
   	  var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpenGrid_3",title:"新增"},
            {name:"edit",target:"editInfoGrid_3",title:"修改"},
            {name:"delete",target:"deleteInfoGrid_3",title:"删除"},
            {name:"submit",target:"submitGrid_3",title:"提交"},
        ]);//工具条
   	   var tablesGridJson = {
   	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
   	            sort: "",//排序
   	            toolbar: toolbar,
   	           height: fullh-460,
   	            read:{"query":"query_BR_CUSTOMER_LINKMAN_view","objects":[ ["草稿", "正常", "作废"] , ["草稿", "正常", "作废"] ],
   	            	"search": searchparams
   	            }
   	        };
   	   if (tablesGrid_3) {
   	 	  setGridDataSource("#tablesGrid_3_"+pathValue,tablesGridJson);
   	   } else {
   		  tablesGrid_3 = initKendoGrid("#tablesGrid_3_"+pathValue,tablesGridJson);//初始化表格的方法
   	   }
      }
    var refreshGrid_1=function(){
        if(tablesGrid_1){
            tablesGrid_1.dataSource.read();//重新读取--刷新
        }
     }
    var refreshGrid_2=function(){
        if(tablesGrid_2){
            tablesGrid_2.dataSource.read();//重新读取--刷新
        }
     }
    var refreshGrid_3=function(){
        if(tablesGrid_3){
            tablesGrid_3.dataSource.read();//重新读取--刷新
        }
     }

    var submit0 = function() {
    	submit(0);
    }
    var submit1 = function() {
    	submit(1);
    }
    var submit=function(i){
    	var STATUS = $("#STATUS"+pathValue).val();
    	if (i == 1 && (!STATUS || STATUS=="草稿")) {
        	$("#STATUS"+pathValue).val("正常");
    	} else if (i != 1 && !STATUS) {
    		$("#STATUS"+pathValue).val("草稿");
    	}
    	
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	var COMPANY_CODE = $.trim( $("#COMPANY_CODE"+pathValue).val() );
    	
    	$("#ID"+pathValue).val(ID);
    	$("#COMPANY_CODE"+pathValue).val(COMPANY_CODE);
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","error");
            return false;
        }
    	
    	if (ID) {
    		submit2();
    	} else {
        	var params={"ID":ID, "COMPANY_CODE":COMPANY_CODE};
            var url="berry/serialNumberManage/customer/companyCodeRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0 && result["count"]==0){
    	            	submit2();
    	            }else if(result["code"]>0){
    	            	$("#COMPANY_CODE"+pathValue).val("");
    	                alertMsg("编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    var submit2 = function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	PAGR_DATA_ID = result.ID;
                    //提交成功
                    alertMsg("提交成功","success",function(){
                    	// 刷新主表
                        funcExce(pathValue+"pageCallBack");//执行回调
                        // 刷新3个子表
                        searchGrid_1();
                        searchGrid_2();
                        searchGrid_3();
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var getCompanyCode=function(){
    	var ID = $("#ID"+pathValue).val();
    	var COMPANY_CODE = $("#COMPANY_CODE"+pathValue).val();
    	if (!ID || !COMPANY_CODE) {// ID为空，新增数据允许获取编号, 或 编号为空允许获取编号
    		var params={"ID":ID};
            var url="berry/serialNumberManage/customer/companyCode";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	            	$("#COMPANY_CODE"+pathValue).val(result["COMPANY_CODE"]);
    	            }else{
    	                alertMsg("获取编码失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    
    var addOpenGrid_1 = function() {
    	var COMPANY_ID = $("#ID"+pathValue).val();
    	var COMPANY_CODE = $("#COMPANY_CODE"+pathValue).val();
    	var COMPANY_NAME = $("#COMPANY_NAME"+pathValue).val();
    	
        if(!COMPANY_ID && PAGR_DATA_ID == "-"){
            alertMsg("请提交客户单位再添加客户资料!");
            return;
        }
        var winOpts={
            url:"berry/bus/customer/company/addInfo/add",
            title: COMPANY_NAME+": 添加客户资料.."
        };
        var dialog = openWindow(winOpts,{ "COMPANY_ID":COMPANY_ID, "COMPANY_CODE":COMPANY_CODE, "COMPANY_NAME":COMPANY_NAME, "addPathValue":pathValue });//传递id
    }
    var editInfoGrid_1 = function() {
    	var arrIds=getSelectData(tablesGrid_1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid_1);
        var status = "";
        for (var i = 0; i < rData.length; i++) {
        	if (rData[i].STATUS == "已提交") {
        		status = rData[i].STATUS;
        		break;
        	}
        }
        if (status == "已提交") {
        	alertMsg("不能修改“已提交”的数据!");
            return ;
        }
    	var COMPANY_NAME = $("#COMPANY_NAME"+pathValue).val();
        var winOpts={
            url:"berry/bus/customer/company/addInfo/add",
            title: COMPANY_NAME+": 编辑客户资料.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0], "addPathValue":pathValue});//传递id
    }
    var deleteInfoGrid_1 = function() {
        var arrIds=getSelectData(tablesGrid_1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/bus/customer/companyInfo/del";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid_1();
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
    }
    var submitGrid_1 = function() {
        var arrIds=getSelectData(tablesGrid_1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行提交操作!");
            return ;
        }
        var params={"tableName":"BR_CUSTOMER_COMPANY_INFO","ids":arrIds};
        var url="berry/bus/customer/company/submitSubGrid";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	refreshGrid_1();
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var addOpenGrid_2 = function() {
    	var COMPANY_ID = $("#ID"+pathValue).val();
    	var COMPANY_CODE = $("#COMPANY_CODE"+pathValue).val();
    	var COMPANY_NAME = $("#COMPANY_NAME"+pathValue).val();
    	
        if(!COMPANY_ID){
            alertMsg("请提交客户单位再添加课题组!");
            return;
        }
        var winOpts={
            url:"berry/bus/customer/company/addSubjectGroup/add",
            title: COMPANY_NAME+": 添加课题组.."
        };
        var dialog = openWindow(winOpts,{ "COMPANY_ID":COMPANY_ID, "COMPANY_CODE":COMPANY_CODE, "COMPANY_NAME":COMPANY_NAME, "addPathValue":pathValue });//传递id
    }
    var editInfoGrid_2 = function() {
    	var arrIds=getSelectData(tablesGrid_2);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid_2);
        var status = "";
        for (var i = 0; i < rData.length; i++) {
        	if (rData[i].STATUS == "已提交") {
        		status = rData[i].STATUS;
        		break;
        	}
        }
        if (status == "已提交") {
        	alertMsg("不能修改“已提交”的数据!");
            return ;
        }
    	var COMPANY_NAME = $("#COMPANY_NAME"+pathValue).val();
        var winOpts={
            url:"berry/bus/customer/company/addSubjectGroup/add",
            title: COMPANY_NAME+": 编辑课题组.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0], "addPathValue":pathValue});//传递id
    }
    var deleteInfoGrid_2 = function() {
        var arrIds=getSelectData(tablesGrid_2);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/bus/customer/subjectGroup/del";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid_2();
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
    }
    var submitGrid_2 = function() {
        var arrIds=getSelectData(tablesGrid_2);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行提交操作!");
            return ;
        }
        var params={"tableName":"BR_CUSTOMER_SUBJECT_GROUP","ids":arrIds};
        var url="berry/bus/customer/company/submitSubGrid";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	refreshGrid_2();
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var addOpenGrid_3 = function() {
    	var COMPANY_ID = $("#ID"+pathValue).val();
    	var COMPANY_CODE = $("#COMPANY_CODE"+pathValue).val();
    	var COMPANY_NAME = $("#COMPANY_NAME"+pathValue).val();
    	
        if(!COMPANY_ID){
            alertMsg("请提交客户单位再添加客户联系人!");
            return;
        }
        var winOpts={
            url:"berry/bus/customer/company/addLinkman/add",
            title: COMPANY_NAME+": 添加客户联系人.."
        };
        var dialog = openWindow(winOpts,{ "COMPANY_ID":COMPANY_ID, "COMPANY_CODE":COMPANY_CODE, "COMPANY_NAME":COMPANY_NAME, "addPathValue":pathValue });//传递id
    }
    var editInfoGrid_3 = function() {
    	var arrIds=getSelectData(tablesGrid_3);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid_3);
        var status = "";
        for (var i = 0; i < rData.length; i++) {
        	if (rData[i].STATUS == "已提交") {
        		status = rData[i].STATUS;
        		break;
        	}
        }
        if (status == "已提交") {
        	alertMsg("不能修改“已提交”的数据!");
            return ;
        }
    	var COMPANY_NAME = $("#COMPANY_NAME"+pathValue).val();
        var winOpts={
            url:"berry/bus/customer/company/addLinkman/add",
            title: COMPANY_NAME+": 编辑客户联系人.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0], "addPathValue":pathValue});//传递id
    }
    var deleteInfoGrid_3 = function() {
        var arrIds=getSelectData(tablesGrid_3);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/bus/customer/linkman/del";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid_3();
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
    }
    var submitGrid_3 = function() {
        var arrIds=getSelectData(tablesGrid_3);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行提交操作!");
            return ;
        }
        var params={"tableName":"BR_CUSTOMER_LINKMAN","ids":arrIds};
        var url="berry/bus/customer/company/submitSubGrid";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	refreshGrid_3();
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit0":submit0,
        "submit1":submit1,
        "getCompanyCode":getCompanyCode,
        
        "searchGrid_1":searchGrid_1,
        "searchGrid_2":searchGrid_2,
        "searchGrid_3":searchGrid_3,
        
        "refreshGrid_1":refreshGrid_1,
        "refreshGrid_2":refreshGrid_2,
        "refreshGrid_3":refreshGrid_3,
        
        "addOpenGrid_1":addOpenGrid_1,
        "editInfoGrid_1":editInfoGrid_1,
        "deleteInfoGrid_1":deleteInfoGrid_1,
        "submitGrid_1":submitGrid_1,
        
        "addOpenGrid_2":addOpenGrid_2,
        "editInfoGrid_2":editInfoGrid_2,
        "deleteInfoGrid_2":deleteInfoGrid_2,
        "submitGrid_2":submitGrid_2,
        
        "addOpenGrid_3":addOpenGrid_3,
        "editInfoGrid_3":editInfoGrid_3,
        "deleteInfoGrid_3":deleteInfoGrid_3,
        "submitGrid_3":submitGrid_3,
    });
 });