$(document).ready(function() {
   var pathValue="berry-bus-customer-subjectGroup-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen"},
            {name:"edit",target:"editInfo1"},
            {name:"delete",target:"deleteInfo1"},
            {name:"submit",target:"submitNormal",title: "提交"},
            {name:"designee",target:"designee1",title: "指定人员"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-112,
            read:{"query":"query_BR_CUSTOMER_SUBJECT_GROUP_view","objects":[ ["草稿"], ["正常", "作废"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"edit",target:"editInfo2"},
//            {name:"delete",target:"deleteInfo2"},
		    {name:"obsolete",target:"obsolete",title: "标为作废"},
		    {name:"recover",target:"recoverNormal",title: "恢复正常"},
            {name:"designee",target:"designee2",title: "指定人员"},
           // {name:"importExcel",target:"importExcel",title: "导入Excel"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-112,
            read:{"query":"query_BR_CUSTOMER_SUBJECT_GROUP_view","objects":[ ["正常", "作废"], ["正常", "作废"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/customer/subjectGroup/add/add",
            title:"新增: 课题组.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
        openWindow({
            url:"berry/bus/customer/subjectGroup/add/add",
            title:"编辑: 课题组.."
        },{"ID":id});
    }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     var refreshGrid2=function(){
        if(tablesGrid2){
            tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo1=function(){
    	 editInfo_F(1);
     }
     var editInfo2=function(){
    	 editInfo_F(2);
     }
     var editInfo_F=function(tgIndex){
        var arrIds=getSelectData(tablesGrid);
        if (tgIndex == 2) {
        	arrIds=getSelectData(tablesGrid2);
        }
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        
        var winOpts={
            url:"berry/bus/customer/subjectGroup/add/add",
            title:"编辑: 课题组.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0]});//传递id
     }

     var deleteInfo1=function(){
    	 deleteInfo_F(1);
     }
     var deleteInfo2=function(){
    	 deleteInfo_F(2); 
     }
     var deleteInfo_F=function(tgIndex){
         var arrIds=getSelectData(tablesGrid);
         if (tgIndex == 2) {
         	arrIds=getSelectData(tablesGrid2);
         }
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"tableName":"BR_CUSTOMER_SUBJECT_GROUP","ids":arrIds};
	        var url="system/jdbc/delete/batch/table";
	        deleteGridDataByIds(url,params,callBack);
	 	});
     }
     
     var importExcel=function(){
    	 alert("待开发");
    	 return;
     }
     
     var designee1=function(){
    	 designee_F(1);
     }
     var designee2=function(){
    	 designee_F(2);
     }
     var designee_F=function(tgIndex){
    	 alert("待开发");
    	 return;
         var arrIds=getSelectData(tablesGrid);
         if (tgIndex == 2) {
           	arrIds=getSelectData(tablesGrid2);
           }
     }
     
     var obsolete = function(){
         var arrIds = getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
        // 提示再次确认
     	confirmMsg("确认", "确定要作废选中的数据吗?", "warn", function() {
	         var params={"ids":arrIds};
	         var url="berry/bus/customer/subjectGroup/obsolete";
	         $.fn.ajaxPost({
		  		   ajaxUrl: url,
		  		   ajaxType: "post",
		  		   ajaxData: params,
		  		   succeed: function(rs) {
		  			   if (rs.code=='1') {
			  				 refreshGrid2();
			  			   }
		  			   alertMsg("操作"+rs.message);
		  		   }
		  	   });
  	    });
     }
     
     var recoverNormal = function(){
         var arrIds = getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
         
        // 提示再次确认
     	confirmMsg("确认", "确定要恢复选中的数据吗?", "warn", function() {
	         var params={"ids":arrIds};
	         var url="berry/bus/customer/subjectGroup/recoverNormal";
	         $.fn.ajaxPost({
		  		   ajaxUrl: url,
		  		   ajaxType: "post",
		  		   ajaxData: params,
		  		   succeed: function(rs) {
		  			   if (rs.code=='1') {
		  				 refreshGrid2();
		  			   }
			  		   alertMsg("操作"+rs.message);
		  		   }
		  	 });
  	    });
     }

 	var submitNormal = function(){
         var arrIds = getSelectData(tablesGrid);
 	    if(arrIds.length==0){
 	        alertMsg("请至少选择一条数据!");
 	        return ;
 	    }
 	    
 	    // 提示再次确认
     	confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
 		    var params={"ids":arrIds};
 		    var url="berry/bus/customer/subjectGroup/submitNormal";
 		    $.fn.ajaxPost({
 	 		   ajaxUrl: url,
 	 		   ajaxType: "post",
 	 		   ajaxData: params,
 	 		   succeed: function(rs) {
 	 			   if (rs.code=='1') {
 	 				 refreshGrid();
 	 				 refreshGrid2();
 	 			   }
 		  		   alertMsg("操作"+rs.message);
 	 		   }
 	 	   });
   	   });
 	}

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "refreshGrid":refreshGrid,
         "refreshGrid2":refreshGrid2,
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
         "editInfo1":editInfo1,
         "deleteInfo1":deleteInfo1,
         "designee1":designee1,
         "editInfo2":editInfo2,
         "deleteInfo2":deleteInfo2,
         "designee2":designee2,
         "obsolete":obsolete,
         "recoverNormal":recoverNormal,
         "submitNormal":submitNormal,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
