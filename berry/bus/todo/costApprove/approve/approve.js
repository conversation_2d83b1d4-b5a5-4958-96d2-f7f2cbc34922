$(document).ready(function() {
    var pathValue="berry-bus-todo-costApprove-approve-approve";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_COST_SETTLE"
        };
    }

    var ID = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	if (params && params.ID) {
    		ID = params.ID;
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        // 初始化Grid
        tablesGrid_init();
        
        if (ID) {
        	searchGrid();
        }
    }
    
    var searchGrid = function() {
    	$.fn.ajaxPost({
    		ajaxUrl: "system/jdbc/query/one/table",
    		ajaxType: "post",
    		ajaxData: {"query":"query_BR_COST_SETTLE_DETAIL_view","objects":[ID]},
    		succeed: function(rs) {
    			if (rs && rs.rows) {
    				$("#tablesGrid"+pathValue).data("kendoGrid").setDataSource(new kendo.data.DataSource({ data: rs.rows ? rs.rows : [] }));
    			}
    		}
    	});
    }
    
    var tablesGrid_init = function(tablesGridData) {
    	$("#tablesGrid"+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"费用结算主表ID",field:"COST_SETTLE_ID",type:"string",width:20,hidden:true},
    			  {title:"费用名称",field:"COST_NAME",type:"string",width:100,
			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["COST_NAME"]){
			    				value = dataItem["COST_NAME"]["value"] ? dataItem["COST_NAME"]["value"] : dataItem["COST_NAME"];
			    			}
	  			    		return value;
	                	},
	  	                editor: function (container, options) {
	  	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
	  	                        .appendTo(container)
	  	                        .kendoDropDownList({
	  	                            dataSource: {
	  	                                data: [
	  	            	                    { text: '建库费用', value: '建库费用' },
	  	            	                    { text: '上机费用', value: '上机费用' },
	  	            	                    { text: '分析费用', value: '分析费用' }
	  	                                ]
	  	                            },
	  	                            optionLabel: "",
	  	                            dataValueField: 'value',
	  	                            dataTextField: 'text',
	  	                            index:1
	  	                        });
	  	                }
	  			  }, // 测序类型/测序模式
    			  {title:"单价",field:"PRICE",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"数量",field:"QUANTITY",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 0,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"金额",field:"AMOUNT",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"备注",field:"REMARK",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  COST_SETTLE_ID: { type : "string" },
    		    	  COST_NAME: { type : "string" },
    		    	  PRICE: { type : "number" },
    		    	  QUANTITY: { type : "number" },
    		    	  AMOUNT: { type : "number" },
        			  REMARK: { type : "string" },
    		      }
    		    }
    		  },
    		  editable: true,// popup inline true
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [ ]
    		});
    }
    
    var submit=function(){
//    	//表单校验
//    	var formJson = { formId:"form", pathValue:pathValue };
//    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
//    	if ( !validator.validate() ) {
//            alertMsg("主信息表单验证未通过","wran");
//            return false;
//        }
//    	
//    	//验证grid
//    	var gridData = $("#tablesGrid"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
//    	if (gridData.length<1) {
//    		alertMsg("提示:没有填写费用明细!");
//            return false;
//    	}
//    	var gridMsg = null;
//    	for (var i=0; i<gridData.length; i++) {
//    		gridData[i].COST_NAME = gridData[i].COST_NAME ? (gridData[i].COST_NAME["value"]!=undefined ? gridData[i].COST_NAME["value"] : gridData[i].COST_NAME) : "";
//    		var COST_NAME = gridData[i].COST_NAME;//费用名称
//    		
//    		var PRICE = gridData[i].PRICE;//单价
//    		var QUANTITY = gridData[i].QUANTITY;//数量
//    		var AMOUNT = gridData[i].AMOUNT;//金额
//    		var REMARK = gridData[i].REMARK;//备注
//    		if (!COST_NAME || !PRICE || !QUANTITY || !AMOUNT) {
//    			gridMsg = "未填必填项";
//    			break;
//    		}
//    	}
//    	if (gridMsg) {
//    		alertMsg("提示:费用明细,“费用名称,单价,数量,金额”字段必填!");
//            return false;
//    	}
    	
    	$("#form"+pathValue+" #STATUS_SALE"+pathValue).val("已确认");
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	ID = result.ID;
                	var gridDataArray = $("#tablesGrid"+pathValue).data("kendoGrid").dataSource.data();
                	var params = { "COST_SETTLE_ID":ID, "DETAIL_ARRAY":gridDataArray };
                	submitDETAIL(params);
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    //提交费用明细信息
    var submitDETAIL = function(params) {
        alertMsg("操作成功", "success", function(){
            funcExce(pathValue+"pageCallBack");//执行回调
            funcExce(pathValue+"close");//关闭页面
        });
//    	var url= "berry/project/costSettle/saveCostDetails";
//        $.fn.ajaxPost({
//            ajaxUrl: url,
//            ajaxType: "post",
//            ajaxData: params,
//            succeed:function(result){
//	            if(result["code"]>0){
//                    alertMsg("操作成功", "success", function(){
//                        funcExce(pathValue+"pageCallBack");//执行回调
//                        funcExce(pathValue+"close");//关闭页面
//                    });
//	            }else{
//	                alertMsg("提示:操作失败!","error");
//	            }
//            },
//            failed:function(result){
//                alertMsg("提示:操作异常!","error");
//            }
//        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 