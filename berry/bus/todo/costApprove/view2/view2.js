$(document).ready(function() {
    var pathValue="berry-bus-todo-costApprove-view2-view2";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_COST_SETTLE"
        };
    }

    var ID = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	if (params && params.ID) {
    		ID = params.ID;
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        // 初始化Grid
        tablesGrid_init();
        
        if (ID) {
        	searchGrid();
        }
    }
    
    var searchGrid = function() {
    	$.fn.ajaxPost({
    		ajaxUrl: "system/jdbc/query/one/table",
    		ajaxType: "post",
    		ajaxData: {"query":"query_BR_COST_SETTLE_DETAIL_view","objects":[ID]},
    		succeed: function(rs) {
    			if (rs && rs.rows) {
    				$("#tablesGrid"+pathValue).data("kendoGrid").setDataSource(new kendo.data.DataSource({ data: rs.rows ? rs.rows : [] }));
    			}
    		}
    	});
    }
    var tablesGrid_init = function(tablesGridData) {
    	$("#tablesGrid"+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"费用结算主表ID",field:"COST_SETTLE_ID",type:"string",width:20,hidden:true},
    			  {title:"费用名称",field:"COST_NAME",type:"string",width:100,
			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["COST_NAME"]){
			    				value = dataItem["COST_NAME"]["value"] ? dataItem["COST_NAME"]["value"] : dataItem["COST_NAME"];
			    			}
	  			    		return value;
	                	},
	  	                editor: function (container, options) {
	  	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
	  	                        .appendTo(container)
	  	                        .kendoDropDownList({
	  	                            dataSource: {
	  	                                data: [
	  	            	                    { text: '建库费用', value: '建库费用' },
	  	            	                    { text: '上机费用', value: '上机费用' },
	  	            	                    { text: '分析费用', value: '分析费用' }
	  	                                ]
	  	                            },
	  	                            optionLabel: "",
	  	                            dataValueField: 'value',
	  	                            dataTextField: 'text',
	  	                            index:1
	  	                        });
	  	                }
	  			  }, // 测序类型/测序模式
    			  {title:"单价",field:"PRICE",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"数量",field:"QUANTITY",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 0,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"金额",field:"AMOUNT",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"备注",field:"REMARK",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  COST_SETTLE_ID: { type : "string" },
    		    	  COST_NAME: { type : "string" },
    		    	  PRICE: { type : "number" },
    		    	  QUANTITY: { type : "number" },
    		    	  AMOUNT: { type : "number" },
        			  REMARK: { type : "string" },
    		      }
    		    }
    		  },
    		  editable: true,// popup inline true
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [ ]
    		});
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
    });
 
 });
 