$(document).ready(function() {
    var pathValue="berry-bus-specialApprove-specialApprove1-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
        	tableName:"BR_SPECIAL_APPROVE"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
    
    var submit0 = function() {
    	submit(0);
    }
    var submit1 = function() {
    	submit(1);
    }

    var submit=function(i){
    	var ID = $("#ID"+pathValue).val();
    	var STATUS = $("#STATUS"+pathValue).val();
    	if (i == 1 && (!STATUS || STATUS=="草稿")) {
        	$("#STATUS"+pathValue).val("待审批");
    	} else if (i != 1 && !STATUS) {
    		$("#STATUS"+pathValue).val("草稿");
    	}
    	
    	var APPLY_NO = $("#APPLY_NO"+pathValue).val();
    	var url= "berry/serialNumberManage/specialApprove/applyNORepeatCheck";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: { "ID":ID, "APPLY_NO":APPLY_NO},
            succeed:function(result){
	            if(result["code"]>0){
	            	if(result.count >= 1){
	            		alertMsg("申请单编号已存在，请重新选择！","error");
	            		$("#APPLY_NO"+pathValue).val("");
	            		return;
	            	}else{
	            		formSubmit({
	                        url:"system/jdbc/save/one/table",
	                        formId:"form",
	                        pathValue:pathValue,
	                        succeed:function(result){
	                            if(result["code"]>0){
	                                //提交成功
	                                alertMsg("提交成功","success",function(){
	                                    funcExce(pathValue+"pageCallBack");//执行回调
	                                    funcExce(pathValue+"close");//关闭页面
	                                });
	                            }else{
	                                alertMsg("提交失败","error");
	                            }
	                        }
	                    });
	            	}
	            }else{
	                alertMsg("提示:提交失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:提交异常!","error");
            }
        });
    }
    
    //获取申请单编号
    var getApplyNO=function(){
    	var APPLY_NO = $("#APPLY_NO"+pathValue).val();
    	var url= "berry/serialNumberManage/specialApprove/applyNO";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: {"APPLY_NO":APPLY_NO},
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#APPLY_NO"+pathValue).val(result.APPLY_NO);
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit0":submit0,
        "submit1":submit1,
        "getApplyNO":getApplyNO,
    });
 
 });
 