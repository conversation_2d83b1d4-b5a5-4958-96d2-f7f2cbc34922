$(document).ready(function() {
    var pathValue="berry-bus-invoice-invoiceRepeal2-approve-approve";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_INVOICE_REPEAL"
        };
    }

    var tablesGrid;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        	// {name:"add",target:"checkedFPIDS",title: "确认选择"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_INVOICE_REPEAL2_approve_view","objects":["-", "-"]}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        tablesGrid.bind("dataBinding", function(e) {
        	var dataArray = tablesGrid.dataSource.data();
        	if (dataArray.length>1) {
        		var selectRows = "";
        		for (var i = 0; i < dataArray.length; i++) {
        			if (dataArray[i].C == 1) {
        				selectRows += selectRows.length > 0 ? ", " : "";
        				selectRows += "tr:eq("+i+")";
        			}
        		}
        		if (selectRows.length > 0) {
        			setTimeout(function(selectStr) {
        				tablesGrid.select(selectStr);
        			}, 1000, selectRows);
        		}
        	}
        });
        searchGrid(params.ID);
    }
    var searchGrid = function(ID) {
 	    var tablesGridJson = {
 	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
 	            sort: "",//排序
 	            toolbar: toolbar,
 	           height: fullh-112,
 	            read:{"query":"query_BR_INVOICE_REPEAL2_approve_view","objects":[ID, ID]}
 	        };
 	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
    }

    var submit=function(){
  	   
    	var APPROVE_RS = $("#APPROVE_RS"+pathValue).val();
    	
    	if (APPROVE_RS) {
        	$("#STATUS"+pathValue).val("审核"+APPROVE_RS);
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功,保存明细
                	submitDetail(result.ID, APPROVE_RS);
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var submitDetail=function(mId, APPROVE_RS){
    	var params={"mId":mId, "APPROVE_RS":APPROVE_RS};
        var url="berry/bus/invoice/invoiceRepeal2/approveDetail";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 