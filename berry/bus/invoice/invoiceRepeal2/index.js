$(document).ready(function() {
   var pathValue="berry-bus-invoice-invoiceRepeal2-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /** "待处理"页签 */
        var toolbar=getButtonTemplates(pathValue,[
        	{name:"edit",target:"editInfo",title: "合同退票审核"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_INVOICE_REPEAL2_view","objects":[["待审核"]]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        /** "待处理"页签 */
        var toolbar2=getButtonTemplates(pathValue,[ ]);//工具条
        //请求参数
        var tablesGridJson2={
        		url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
        		sort: "",//排序
        		toolbar: toolbar2,
        		height: fullh-112,
        		read:{"query":"query_BR_INVOICE_REPEAL2_view","objects":[["审核通过", "审核不通过"]]},
        		headerFilter:function(cols,i){
        			if(i){
        				if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
        					setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
        				}
        			}
        		}
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_INVOICE_REPEAL2_view","objects":[["待审核"]],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   var searchGrid2 = function() {
	   var searchparams = getJsonByForm("searchform2_",pathValue);
	   var tablesGridJson = {
			   url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
			   sort: "",//排序
			   toolbar: toolbar,
			   height: fullh-112,
			   read:{"query":"query_BR_INVOICE_REPEAL2_view","objects":[["审核通过", "审核不通过"]],
				   "search": searchparams
			   },
			   headerFilter:function(cols,i){
				   if(i){
					   if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
						   setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
					   }
				   }
			   }
	   };
	   setGridDataSource("#tablesGrid2_"+pathValue,tablesGridJson);
	   // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGridWindow = function() {
	   var searchparams = getJsonByForm("searchWindowForm",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_INVOICE_REPEAL2_view","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchWindowOpen = function() {
	   $("#searchWindow").kendoWindow({
           width: "600px",
           height: "500px",
           title: "高级查询",
           visible: false,
           actions: [
               "Close"
           ]
       }).data("kendoWindow").center().open();
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/invoice/invoiceRepeal1/add/add",
            title:"发票审核.."
        };
        openWindow(winOpts);
    }

    var open=function(id, P_COMPANY_NAME, PROVINCE_TEXT, CITY_TEXT){
        openWindow({
            url:"berry/bus/mailpost/mailpost1/add/add",
            title:"编辑.."
        },{"ID":id, "P_COMPANY_NAME":P_COMPANY_NAME, "PROVINCE_TEXT":PROVINCE_TEXT, "CITY_TEXT":CITY_TEXT});
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行审核!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行审核操作!");
            return ;
        }
        var winOpts={
            url:"berry/bus/invoice/invoiceRepeal2/approve/approve",
            title:"退票审核.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0]});//传递id
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BR_SPECIAL_APPROVE","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }
     
     var importExcel=function(){
    	 alert(123);
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
         "searchGrid":searchGrid,
         "searchGrid2":searchGrid2,
         "searchGridWindow":searchGridWindow,
         "searchWindowOpen":searchWindowOpen,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
