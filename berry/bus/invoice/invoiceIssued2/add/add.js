$(document).ready(function() {
    var pathValue="berry-bus-invoice-invoiceIssued2-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_INVOICE_ISSUED"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        /**
         * 列表-按钮-定义
         */
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            height: fullh-112,
            read:{"query":"query_BR_INVOICE_ISSUED_DETAIL_view","objects":[params.ID]},
            editable: true
        };
        tablesGrid = initKendoGrid("#tablesGrid_"+pathValue,tablesGridJson);//初始化表格的方法
    }

    var submit=function(){

    	var shyj = $("#APPROVE_RS"+pathValue).val();
    	
    	if (shyj=="通过") {
    		$("#STATUS"+pathValue).val("待填发票号");
    	}
    	if (shyj=="不通过") {
    		$("#STATUS"+pathValue).val("审核不通过");
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 