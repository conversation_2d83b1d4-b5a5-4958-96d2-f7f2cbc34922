$(document).ready(function() {
    var pathValue="berry-bus-invoice-invoiceRepeal1-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_INVOICE_REPEAL"
        };
    }

    var tablesGrid;

    var CONTRACT_ID_VAL = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id

        var ID = (params && params.ID) ? params.ID : "-";
        var CONTRACT_ID = (params && params.CONTRACT_ID) ? params.CONTRACT_ID : "-";
        CONTRACT_ID_VAL = CONTRACT_ID;
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        	// {name:"add",target:"checkedFPIDS",title: "确认选择"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-321,
            read:{"query":"query_BR_INVOICE_REPEAL1_add_view","objects":["-", "-", "-"]} // "objects":[ID, CONTRACT_ID, ID]
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        tablesGrid.bind("dataBinding", function(e) {
        	var dataArray = tablesGrid.dataSource.data();
        	if (dataArray.length>1) {
        		var selectRows = "";
        		for (var i = 0; i < dataArray.length; i++) {
        			if (dataArray[i].C == 1) {
        				selectRows += selectRows.length > 0 ? ", " : "";
        				selectRows += "tr:eq("+i+")";
        			}
        		}
        		if (selectRows.length > 0) {
        			setTimeout(function(selectStr) {
        				tablesGrid.select(selectStr);
        			}, 1000, selectRows);
        		}
        	}
        });
        searchGrid();
        
    	// 重置    合同名称    字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "CONTRACT_NAME",
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        	var c_id = $("#CONTRACT_ID"+pathValue).val();
		        	if (CONTRACT_ID_VAL != c_id) {
		        		CONTRACT_ID_VAL = c_id;
		        		searchGrid();
		        	}
		        }
        }, "CONTRACT_NAME"+pathValue);
    }
    
    var searchGrid = function() {
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	ID = ID ? ID : "-";
    	
    	var CONTRACT_ID = $.trim( $("#CONTRACT_ID"+pathValue).val() );
    	CONTRACT_ID = CONTRACT_ID ? CONTRACT_ID : "-";
    	
 	    var tablesGridJson = {
 	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
 	            sort: "",//排序
 	            toolbar: toolbar,
 	            height: fullh-371,
 	            read:{"query":"query_BR_INVOICE_REPEAL1_add_view","objects":[ID, CONTRACT_ID, ID]}
 	        };
 	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
    }

    var submit1=function(){
    	submit(1);
    }

    var submit2=function(){
    	submit(2);
    }
    
    var submit = function(i) {
        
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	var APPLY_NO = $.trim( $("#APPLY_NO"+pathValue).val() );
    	
    	$("#ID"+pathValue).val(ID);
    	$("#APPLY_NO"+pathValue).val(APPLY_NO);
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","error");
            return false;
        }
    	
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条退票的发票数据!");
            return ;
        }
    	
    	if (ID) {
    		submit6(i);
    	} else {
        	var params={"ID":ID, "APPLY_NO":APPLY_NO};
            var url="berry/serialNumberManage/invoice/repeal/applyNoRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0 && result["count"]==0){
    	            	submit6(i);
    	            }else if(result["code"]>0){
    	            	$("#APPLY_NO"+pathValue).val("");
    	                alertMsg("退票申请编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    var submit6 = function(i){
    	
    	if (i == 2) {
        	$("#STATUS"+pathValue).val("待审核");
    	} else {
    		var ID = $("#ID"+pathValue).val();
    		var STATUS = $("#STATUS"+pathValue).val();
    		if (STATUS != "待审核" || !ID) {
    			$("#STATUS"+pathValue).val("草稿");
    		}
    	}
   	   
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条退票的发票数据!");
            return ;
        }
  	   
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功,保存明细
                	submitDetail(result.ID, arrIds);
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var submitDetail=function(mId, dIds){
    	var params={"mId":mId, "dIds":dIds};
        var url="berry/bus/invoice/invoiceRepeal1/addDetail";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var getApplyNo=function() {
    	var ID = $("#ID"+pathValue).val();
    	var APPLY_NO = $("#APPLY_NO"+pathValue).val();
    	if (!ID || !APPLY_NO) {// ID为空，新增数据允许获取编号, 或 编号为空允许获取编号
    		var params={"ID":ID};
            var url="berry/serialNumberManage/invoice/repeal/applyNo";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	            	$("#APPLY_NO"+pathValue).val(result["APPLY_NO"]);
    	            }else{
    	                alertMsg("获取退票申请编码失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "searchGrid":searchGrid,
        "getApplyNo":getApplyNo,
        "submit1":submit1,
        "submit2":submit2,
    });
 
 });
 