$(document).ready(function() {
    var pathValue="berry-bus-invoice-invoiceIssued1-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_INVOICE_ISSUED"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        /**
         * 列表-按钮-定义
         */
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: [
  			  {
  				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+'tablesGrid_addRow\',\'asdas\')">新增行</a>'
  			  },
  			  {
  				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+'tablesGrid_removeRow\',\'asdas\')">删除行</a>'
  			  }
  			],
            height: fullh-112,
            read:{"query":"query_BR_INVOICE_ISSUED_DETAIL_view","objects":[params.ID]},
            editable: true
        };
        tablesGrid = initKendoGrid("#tablesGrid_"+pathValue,tablesGridJson);//初始化表格的方法
    }
    
    var submit1=function(){
    	submit("CG");
    }
    
    var submit2=function(){
    	submit("CFM");
    }
    
    /** 表单提交及校验 */
    var submit = function(saveType){
    	var ID = $("#ID"+pathValue).val();
    	var APPLY_NO = $("#APPLY_NO"+pathValue).val();
    	if(ID){//第一次保存校验申请编号是否唯一
    		submitForm(saveType);
    	}else{
    		var url= "berry/serialNumberManage/invoice/issued/applyNoRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: { "ID":ID, "APPLY_NO":APPLY_NO},
                succeed:function(result){
                	if(result["code"]>0 && result["count"]==0){
                		submitForm();
    	            }else if(result["code"]>0){
    	            	$("#COMPANY_CODE"+pathValue).val("");
    	                alertMsg("编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:提交异常!","error");
                }
            });
    	}
    }
    /** 表单提交保存方法 */
    var submitForm = function(saveType){
    	if(saveType == 'CFM'){//"提交保存"操作
    		$("#STATUS"+pathValue).val("待审核");
    	}else{//"存为草稿"操作
    		var ID = $("#ID"+pathValue).val();
    		var STATUS = $("#STATUS"+pathValue).val();
    		if (STATUS != "待审核" || !ID) {
    			$("#STATUS"+pathValue).val("草稿");
    		}
    	}
    	
    	formSubmit({
	        url:"system/jdbc/save/one/table",
	        formId:"form",
	        pathValue:pathValue,
	        succeed:function(result){
	            if(result["code"]>0){
	            	submitDetail(result.ID);
	            }else{
	                alertMsg("提交失败","error");
	            }
	        }
	    });
    }
    //保存明细
    var submitDetail=function(ISSUED_ID){
    	var grid = $("#tablesGrid_"+pathValue).data("kendoGrid");
    	var gridData = grid.dataSource.data();
    	if(!gridData){
    		return;
    	}
    	$.each(gridData, function(i, obj){
			obj.ISSUED_ID = ISSUED_ID;
		});
        var url= "berry/bus/invoice/invoiceIssued1/addDetail";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: {"ISSUED_DETAIL":gridData, "ISSUED_ID":ISSUED_ID},
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            	
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    //获取申请单编号
    var getApplyNO=function(){
    	var APPLY_NO = $("#APPLY_NO"+pathValue).val();
    	var url= "berry/serialNumberManage/invoice/issued/applyNo";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: {"APPLY_NO":APPLY_NO},
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#APPLY_NO"+pathValue).val(result.APPLY_NO);
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    /** 新增行 */
    var tablesGrid_addRow = function() {
    	var tablesGridID = "tablesGrid_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	grid.addRow();
    	var gridData = grid.dataSource.data();
    	if (gridData.length>1) {
    		var newGridData = [];
    		for (var i = 0; i < gridData.length-1; i++) {
    			newGridData[i] = gridData[i+1];
    		}
    		newGridData[ gridData.length-1 ] = gridData[0];
    		grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    		grid.editRow( $("#"+tablesGridID+" tr:eq("+newGridData.length+")") );
    	}
    }
    /** 删除行 */
    var tablesGrid_removeRow = function() {
    	var tablesGridID = "tablesGrid_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	var selectRows = grid.select();
    	if (!selectRows || selectRows.length==0) {
    		return;
    	}
    	var selectRowsIndex = "";
    	$(selectRows).each(function(i, e){
    		selectRowsIndex += ","+this.rowIndex+",";
    	});
    	// 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	    	var gridData = grid.dataSource.data();
			var newGridData = [];
			for (var i = 0; i < gridData.length; i++) {
				if (selectRowsIndex.indexOf(","+i+",") < 0) {
					newGridData[newGridData.length] = gridData[i];
				}
			}
			grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    	});
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "submit1":submit1,
        "submit2":submit2,
        "submitForm":submitForm,
        "getApplyNO":getApplyNO,
        "tablesGrid_addRow":tablesGrid_addRow,
        "tablesGrid_removeRow":tablesGrid_removeRow,
    });
 
 });
 