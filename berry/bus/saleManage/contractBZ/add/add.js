$(document).ready(function() {
    var pathValue="berry-bus-saleManage-contractBZ-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CONTRACT"
        };
    }
    
    var MAIN_CONTRACT_NO_selectVal;
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        // 重置主合同编号字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "MAIN_CONTRACT_NO",
        		openBeforeCheck: function() {// 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
        			var ID = $("#ID"+pathValue).val();
        			if (ID) {
        				alertMsg("报账合同已保存，不能变更主合同","error");
            			return false;
        			}
        			return true;
        		},
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        	if (MAIN_CONTRACT_NO_selectVal != value) {
		        		MAIN_CONTRACT_NO_selectVal = value;
			        	$("#CONTRACT_NAME"+pathValue).val("");
			        	$("#CONTRACT_NO"+pathValue).val("");
		        	}
		        }
        }, "MAIN_CONTRACT_NO"+pathValue);
    }
    
    var submit=function(){
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	var CONTRACT_NO = $.trim( $("#CONTRACT_NO"+pathValue).val() );
    	
    	$("#ID"+pathValue).val(ID);
    	$("#CONTRACT_NO"+pathValue).val(CONTRACT_NO);
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","error");
            return false;
        }
    	
    	if (ID) {
    		submit2();
    	} else {
        	var params={"ID":ID, "CONTRACT_NO":CONTRACT_NO};
            var url="berry/serialNumberManage/contract/contractNoRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0 && result["count"]==0){
    	            	submit2();
    	            }else if(result["code"]>0){
    	            	$("#CONTRACT_NO"+pathValue).val("");
    	                alertMsg("合同编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    var submit2 = function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var mainContractChange = function() {
    	var ID = $("#ID"+pathValue).val();
    	var MAIN_CONTRACT_NO = $("#MAIN_CONTRACT_NO"+pathValue).val();
    	var CONTRACT_NO = $("#CONTRACT_NO"+pathValue).val();
    	if (CONTRACT_NO.indexOf(MAIN_CONTRACT_NO)<0 && !ID) {
        	alertMsg("主合同变更，请重新获取合同编号");
        	$("#CONTRACT_NO"+pathValue).val("");
        	$("#CONTRACT_NAME"+pathValue).val("");
    	}
    }
    
    var getContractNo=function() {
    	var ID = $("#ID"+pathValue).val();
    	var CONTRACT_NO = $("#CONTRACT_NO"+pathValue).val();
    	var MAIN_CONTRACT_NO = $("#MAIN_CONTRACT_NO"+pathValue).val();
    	var MAIN_CONTRACT_NAME = $("#MAIN_CONTRACT_NAME"+pathValue).val();
    	if ( !MAIN_CONTRACT_NO ) {
    		alertMsg("请填写主合同再获取合同编号");
    		return;
    	}
    	if (!ID || !CONTRACT_NO) {// ID为空，新增数据允许获取编号, 或 编号为空允许获取编号
    		var params={"ID":ID, "MAIN_CONTRACT_NO":MAIN_CONTRACT_NO};
            var url="berry/serialNumberManage/contract/bz/contractNo";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	            	$("#CONTRACT_NO"+pathValue).val(result["CONTRACT_NO"]);
    	            	$("#CONTRACT_NAME"+pathValue).val( MAIN_CONTRACT_NAME + result["CONTRACT_NO"].replace(MAIN_CONTRACT_NO,"") );
    	            }else{
    	                alertMsg("获取编码失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "mainContractChange":mainContractChange,
        "getContractNo":getContractNo,
    });
 
 });
 