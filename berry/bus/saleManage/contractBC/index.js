$(document).ready(function() {
   var pathValue="berry-bus-saleManage-contractBC-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"新增补充协议"},
            {name:"edit",target:"editInfo"},
			{name:"delete",target:"deleteInfo"},
            {name:"submit",target:"submit", title:"提交到审核"},
//            {name:"recordTail",target:"recordTail",title: "记录跟踪"},
           // {name:"importExcel",target:"importExcel",title: "导入Excel"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_CONTRACT_view_BC","objects":[ ["草稿"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"新增补充协议"},
            {name:"edit",target:"editInfo"},
//            {name:"delete",target:"deleteInfo"},
//            {name:"recordTail",target:"recordTail",title: "记录跟踪"},
           // {name:"importExcel",target:"importExcel",title: "导入Excel"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
           height: fullh-112,
            read:{"query":"query_BR_CONTRACT_view_BC","objects":[ ["大区经理待审核", "大区经理审核不通过", "销售总监待审核", "销售总监审核不通过", "待打印", "已打印", "待返还", "已返还"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_CONTRACT_view_BC","objects":[ ["草稿"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
   }
   
   var searchGrid2 = function() {
	   var searchparams = getJsonByForm("searchform2",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_CONTRACT_view_BC","objects":[ ["大区经理待审核", "大区经理审核不通过", "销售总监待审核", "销售总监审核不通过", "待打印", "已打印", "待返还", "已返还"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid2_"+pathValue,tablesGridJson);
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/saleManage/contractBC/add/add",
            title:"新增: 补充协议.."
        };
        openWindow(winOpts);
    }

    var open=function(id, COMPANY_NAME , PROVINCE_NAME , CITY_NAME){
        openWindow({
            url:"berry/bus/saleManage/contractBC/add/add",
            title:"编辑: 补充协议.."
        },{"ID":id, "COMPANY_NAME":COMPANY_NAME, "PROVINCE_NAME":PROVINCE_NAME, "CITY_NAME":CITY_NAME});
    }

     var submit=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
 	        var params={"ids":arrIds};
 	        var url="berry/bus/saleManage/contract/submitToApprove";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	refreshGrid();
 		            	refreshGrid2();
 		                alertMsg("提示:操作成功!","success");
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
     }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     
     var refreshGrid2=function(){
         if(tablesGrid2){
             tablesGrid2.dataSource.read();//重新读取--刷新
         }
      }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        
        var rData = getGridSelectData(tablesGrid);

        var winOpts={
            url:"berry/bus/saleManage/contractBC/add/add",
            title:"编辑: 补充协议.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0],"COMPANY_NAME":rData[0].COMPANY_NAME, "PROVINCE_NAME":rData[0].PROVINCE_NAME, "CITY_NAME":rData[0].CITY_NAME});//传递id
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
	        var params={"tableName":"BR_CONTRACT","ids":arrIds};
	        var url="system/jdbc/delete/batch/table";
	        deleteGridDataByIds(url,params,refreshGrid);
        });
     }
     
     var importExcel=function(){
    	 alert("待开发");
     }
     
     var recordTail=function(){
    	 alert("待开发");
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "searchGrid":searchGrid,
         "refreshGrid":refreshGrid,
         "searchGrid2":searchGrid2,
         "refreshGrid2":refreshGrid2,
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
         "recordTail":recordTail,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
