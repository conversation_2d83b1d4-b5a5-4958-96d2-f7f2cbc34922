$(document).ready(function() {
   var pathValue="berry-bus-saleManage-intentionAnalyze-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"editInfo",title:"意向跟进"},
            {name:"recordAnalyze",target:"recordAnalyze",title: "历史跟进记录"},
//            {name:"add",target:"addOpen"},
//            {name:"edit",target:"editInfo"},
//            {name:"delete",target:"deleteInfo"},
           // {name:"importExcel",target:"importExcel",title: "导入Excel"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_INTENTION_ANALYZE_view_MAIN","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ANALYZE_COUNT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ANALYZE_COUNT #","funcExce(\'"+pathValue+"openRecordAnalyze\',\'#= ID #\');","txt"));
                    }
                    if(cols[i]["field"]&&cols[i]["field"]=="INTENTION_CODE"){
                        setJsonParam(cols[i],"template",getTemplate("#= INTENTION_CODE #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= INTENTION_CODE #\',\'#= INTENTION_NAME #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/saleManage/intentionAnalyze/add/add",
            title:"意向跟进.."
        };
        openWindow(winOpts);
    }

    var open=function(ID, INTENTION_CODE, INTENTION_NAME){
        openWindow({
            url:"berry/bus/saleManage/intentionAnalyze/add/add",
            title:"意向跟进.."
        },{
         	"INTENTION_ID":ID,
         	"INTENTION_CODE":INTENTION_CODE,
         	"INTENTION_NAME":INTENTION_NAME
         });
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行跟进!");
             return ;
         }else if(arrIds.length!=1){
             alertMsg("请只选择一条数据进行跟进!");
             return ;
         }
         var rData = getGridSelectData(tablesGrid);
         var winOpts={
             url:"berry/bus/saleManage/intentionAnalyze/add/add",
             title:"意向跟进.."
         };
         var dialog = openWindow(winOpts,{
         	"INTENTION_ID":rData[0].ID,
         	"INTENTION_CODE":rData[0].INTENTION_CODE,
         	"INTENTION_NAME":rData[0].INTENTION_NAME
         });//传递id
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BR_INTENTION_ANALYZE","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }
     
     var recordAnalyze=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据查看历史分析记录!");
             return ;
         }else if(arrIds.length!=1){
             alertMsg("请只选择一条数据查看历史分析记录!");
             return ;
         }
         openRecordAnalyze(arrIds[0]);
     }
     var openRecordAnalyze = function(INTENTION_ID) {
         var winOpts={
             url:"berry/bus/saleManage/intentionAnalyze/recordAnalyze/recordAnalyze",
             title:"历史跟进记录.."
         };
         var dialog = openWindow(winOpts,{ "INTENTION_ID":INTENTION_ID });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "recordAnalyze":recordAnalyze,
         "openRecordAnalyze":openRecordAnalyze,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
