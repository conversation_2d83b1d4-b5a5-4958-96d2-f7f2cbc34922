$(document).ready(function() {
    var pathValue="berry-bus-saleManage-intentionAnalyze-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_INTENTION_ANALYZE"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
    
    var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                	submit2(result.ID);
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    var submit2=function(ANALYZE_ID){
    	var INTENTION_ID = $("#INTENTION_ID" + pathValue).val();
    	var params={"ANALYZE_ID":ANALYZE_ID, "INTENTION_ID":INTENTION_ID};
        var url="berry/bus/saleManage/intention/info/updateAnalyze";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 