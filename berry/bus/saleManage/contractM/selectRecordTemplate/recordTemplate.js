$(document).ready(function() {
   var pathValue="berry-bus-saleManage-contractM-selectRecordTemplate-recordTemplate";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var ID;//合同ID
   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   ID = params.ID;
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmCreateContractRecord",title:"确认生成合同电子档"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-146,
            read:{"query":"query_RECORD_TEMPLATE_view","objects":[],
            	"search":{ "SEARCH_TEMPLATE_CLASS":"合同模板", "SEARCH_TEMPLATE_STATUS":"启用" }
            },
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        tablesGrid.bind("change", function(e) {
        	var arrIds = getSelectData(this);
        	if (arrIds.length==0) {
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = arrIds[0];
        	
        	// 清空: 复选框选中状态
        	var h_ck = $("#tablesGrid"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        });
   }
     
     var confirmCreateContractRecord=function(){
         var rData=getGridSelectData(tablesGrid);
         if(rData.length==0){
             alertMsg("请选择一个模板");
             return ;
         }else if(rData.length!=1){
             alertMsg("请只选择一个模板");
             return ;
         }
         confirmMsg("确认", "确定要按照选择的模板生成合同电子档吗?", "warn", function() {
	         var params={ "ID":ID, "TEMP_ID":rData[0].ID, "TEMP_NAME":rData[0].TEMPLATE_NAME, "TEMP_FILE":rData[0].TEMPLATE_FILE };
	         var url="berry/bus/saleManage/contract/createContractRecord";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
	 	            	if (result["msg"]) {
	 	            		alertMsg("提示:"+result["msg"],"error");
	 	            	} else {
	 	                    alertMsg("提示:生成成功","success",function(){
	 	                        funcExce(pathValue+"close");//关闭页面
	 	                    });
	 	            	}
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
	     });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "confirmCreateContractRecord":confirmCreateContractRecord,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
