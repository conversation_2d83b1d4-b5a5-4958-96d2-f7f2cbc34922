$(document).ready(function() {
    var pathValue="berry-bus-saleManage-contractM-view-view";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CONTRACT"
        };
    }
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function(params) {
    	
    	// 调整表单样式
    	$("#form"+pathValue+" .col-sm-12 div").each(function() {
    		var divClass = $(this).attr("class");
    		if (divClass && divClass.indexOf("col-sm-")>=0) {
        		var divClassLength = divClass.replace("col-sm-", "") * 2;
        		divClassLength = divClassLength>12 ? 12 : divClassLength;
        		$(this).attr("class", "col-sm-"+divClassLength);
    		}
    	});
    	
        getInfo("form", pathValue, params);
        // 传入数组ids
        var url = "system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form", pathValue, params, url);//传入id
        
        showAuxiliaryInfo();
    }
    
    // 显示辅助信息
    var showAuxiliaryInfo = function() {
    	var CONTRACT_ID = $.trim( $("#ID"+pathValue).val() );
    	var CONTRACT_NO = $.trim( $("#CONTRACT_NO"+pathValue).val() );
    	if (CONTRACT_ID || CONTRACT_NO) {// 判断合同ID 合同编号  字段
    		// 显示辅助信息 - 合同
    		showAuxiliaryInfo_HT(CONTRACT_ID, CONTRACT_NO);
    		// 显示辅助信息 - 合同回款
    		showAuxiliaryInfo_HK(CONTRACT_ID, CONTRACT_NO);
    		// 显示辅助信息 - 合同特殊审批
    		showAuxiliaryInfo_TSSP(CONTRACT_ID, CONTRACT_NO);
    	} else {
    		// 清空辅助信息
    		clearAuxiliaryInfo();
    	}
    }
    // 显示辅助信息 - 合同
    var showAuxiliaryInfo_HT = function(CONTRACT_ID, CONTRACT_NO) {
		var params={"CONTRACT_ID":CONTRACT_ID, "CONTRACT_NO":CONTRACT_NO};
        var url="berry/project/project/getContractInfo";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	var m = result.cInfoMap;
	            	$("#HT_"+pathValue+" #ht_p_1").html(m.CONTRACT_NO);//合同编号
	            	$("#HT_"+pathValue+" #ht_p_2").html(m.CONTRACT_NAME);//合同名称
	            	$("#HT_"+pathValue+" #ht_p_3").html(m.CONTRACT_STATUS);//合同状态
	            	$("#HT_"+pathValue+" #ht_p_4").html(m.CONTRACT_AMOUNT);//合同金额
	            }else{
	                alertMsg("获取合同信息失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    // 显示辅助信息 - 合同回款
    var showAuxiliaryInfo_HK = function(CONTRACT_ID, CONTRACT_NO) {
		var params={"CONTRACT_ID":CONTRACT_ID, "CONTRACT_NO":CONTRACT_NO};
        var url="berry/project/project/getContractHkInfo";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	var m_1 = result.cHkInfoMap_1;//开票申请
	            	var m_2 = result.cHkInfoMap_2;//开票待审
	            	var m_3 = result.cHkInfoMap_3;//开票通过
	            	var m_4 = result.cHkInfoMap_4;//开票不通过
	            	var m_5 = result.cHkInfoMap_5;//开票已回款
	            	var m_6 = result.cHkInfoMap_6;//退票申请
	            	var m_7 = result.cHkInfoMap_7;//退票待审
	            	var m_8 = result.cHkInfoMap_8;//退票通过
	            	var m_9 = result.cHkInfoMap_9;//退票不通过
	            	$("#HK_"+pathValue+" #hk_p_1_1").html(m_1.C ? m_1.C : 0);//开票申请数量
	            	$("#HK_"+pathValue+" #hk_p_1_2").html(m_1.S ? m_1.S : 0);//开票申请金额
	            	$("#HK_"+pathValue+" #hk_p_2_1").html(m_2.C ? m_2.C : 0);//开票待审数量
	            	$("#HK_"+pathValue+" #hk_p_2_2").html(m_2.S ? m_2.S : 0);//开票待审金额
	            	$("#HK_"+pathValue+" #hk_p_3_1").html(m_3.C ? m_3.C : 0);//开票通过数量
	            	$("#HK_"+pathValue+" #hk_p_3_2").html(m_3.S ? m_3.S : 0);//开票通过金额
	            	$("#HK_"+pathValue+" #hk_p_4_1").html(m_4.C ? m_4.C : 0);//开票不通过数量
	            	$("#HK_"+pathValue+" #hk_p_4_2").html(m_4.S ? m_4.S : 0);//开票不通过金额
	            	$("#HK_"+pathValue+" #hk_p_5_1").html(m_5.C ? m_5.C : 0);//开票已回款金额
	            	$("#HK_"+pathValue+" #hk_p_6_1").html(m_6.C ? m_6.C : 0);//退票申请数量
	            	$("#HK_"+pathValue+" #hk_p_6_2").html(m_6.S ? m_6.S : 0);//退票申请金额
	            	$("#HK_"+pathValue+" #hk_p_7_1").html(m_7.C ? m_7.C : 0);//退票待审数量
	            	$("#HK_"+pathValue+" #hk_p_7_2").html(m_7.S ? m_7.S : 0);//退票待审金额
	            	$("#HK_"+pathValue+" #hk_p_8_1").html(m_8.C ? m_8.C : 0);//退票通过数量
	            	$("#HK_"+pathValue+" #hk_p_8_2").html(m_8.S ? m_8.S : 0);//退票通过金额
	            	$("#HK_"+pathValue+" #hk_p_9_1").html(m_9.C ? m_9.C : 0);//数量
	            	$("#HK_"+pathValue+" #hk_p_9_2").html(m_9.S ? m_9.S : 0);//退票不通过金额
	            }else{
	                alertMsg("获取合同回款情况失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    // 显示辅助信息 - 合同特殊审批
    var showAuxiliaryInfo_TSSP = function(CONTRACT_ID, CONTRACT_NO) {
		var params={"CONTRACT_ID":CONTRACT_ID, "CONTRACT_NO":CONTRACT_NO};
        var url="berry/project/project/getContractTsspInfo";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	var m = result.cTsspInfoMap;
	            	$("#TSSP_"+pathValue+" #tssp_p_1").html(m.APPLY_COUNT ? m.APPLY_COUNT : 0);//特殊审批申请数
	            	$("#TSSP_"+pathValue+" #tssp_p_2").html(m.APPROVING_COUNT ? m.APPROVING_COUNT : 0);//特殊审批待审数
	            	$("#TSSP_"+pathValue+" #tssp_p_3").html(m.APPROVED_TG_COUNT ? m.APPROVED_TG_COUNT : 0);//特殊审批通过数
	            	$("#TSSP_"+pathValue+" #tssp_p_4").html(m.APPROVED_BTG_COUNT ? m.APPROVED_BTG_COUNT : 0);//特殊审批不通过数
	            }else{
	                alertMsg("获取合同特殊审批失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    // 清空辅助信息
    var clearAuxiliaryInfo = function() {
    	// 合同信息
    	$("#HT_"+pathValue+" #ht_p_1").html("");//合同编号
    	$("#HT_"+pathValue+" #ht_p_2").html("");//合同名称
    	$("#HT_"+pathValue+" #ht_p_3").html("");//合同状态
    	$("#HT_"+pathValue+" #ht_p_4").html("");//合同金额
    	
    	//合同回款信息
    	$("#HK_"+pathValue+" #hk_p_1_1").html("");//开票申请数量
    	$("#HK_"+pathValue+" #hk_p_1_2").html("");//开票申请金额
    	$("#HK_"+pathValue+" #hk_p_2_1").html("");//开票待审数量
    	$("#HK_"+pathValue+" #hk_p_2_2").html("");//开票待审金额
    	$("#HK_"+pathValue+" #hk_p_3_1").html("");//开票通过数量
    	$("#HK_"+pathValue+" #hk_p_3_2").html("");//开票通过金额
    	$("#HK_"+pathValue+" #hk_p_4_1").html("");//开票不通过数量
    	$("#HK_"+pathValue+" #hk_p_4_2").html("");//开票不通过金额
    	$("#HK_"+pathValue+" #hk_p_5_1").html("");//开票已回款金额
//    	$("#HK_"+pathValue+" #hk_p_5_2").html("");//
    	$("#HK_"+pathValue+" #hk_p_6_1").html("");//退票申请数量
    	$("#HK_"+pathValue+" #hk_p_6_2").html("");//退票申请金额
    	$("#HK_"+pathValue+" #hk_p_7_1").html("");//退票待审数量
    	$("#HK_"+pathValue+" #hk_p_7_2").html("");//退票待审金额
    	$("#HK_"+pathValue+" #hk_p_8_1").html("");//退票通过数量
    	$("#HK_"+pathValue+" #hk_p_8_2").html("");//退票通过金额
    	$("#HK_"+pathValue+" #hk_p_9_1").html("");//退票不通过数量
    	$("#HK_"+pathValue+" #hk_p_9_2").html("");//退票不通过金额
    	
    	//合同特殊审批
    	$("#TSSP_"+pathValue+" #tssp_p_1").html("");//特殊审批申请数
    	$("#TSSP_"+pathValue+" #tssp_p_2").html("");//特殊审批待审数
    	$("#TSSP_"+pathValue+" #tssp_p_3").html("");//特殊审批通过数
    	$("#TSSP_"+pathValue+" #tssp_p_4").html("");//特殊审批不通过数
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "showAuxiliaryInfo":showAuxiliaryInfo,
    });
 });