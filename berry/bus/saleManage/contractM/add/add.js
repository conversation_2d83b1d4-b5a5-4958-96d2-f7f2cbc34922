$(document).ready(function() {
    var pathValue="berry-bus-saleManage-contractM-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CONTRACT"
        };
    }
    
    var VIEW_MODE = "";
    
    var tablesGrid;
    var CONTRACT_ID = "";//合同ID
    var COMPANY_NAME_VAL = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	// 插入动态标签
    	//                           费用明细标签
    	var tablesGridDIV = '<div class="form-group row col-sm-12"><div class="col-sm-9">';
    	tablesGridDIV += '<div id="tablesGrid'+pathValue+'"></div>';
    	tablesGridDIV += "</div></div>";
    	$("#PAYMENT_AMOUNT_FIRST"+pathValue).parent().parent().before(tablesGridDIV);
    	
    	// 参数处理
    	VIEW_MODE = (params && params.VIEW_MODE) ? params.VIEW_MODE : "";
    	CONTRACT_ID = (params && params.ID) ? params.ID : "";
    	
    	//数据初始化
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
    	// 重置    客户单位名称    字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "COMPANY_NAME",
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        	if (COMPANY_NAME_VAL != value) {
		        		COMPANY_NAME_VAL = value;
			        	$("#INTENTION_ID"+pathValue).val("");
			        	$("#INTENTION_CODE"+pathValue).val("");
			        	$("#INTENTION_NAME"+pathValue).val("");
		        	}
		        }
        }, "COMPANY_NAME"+pathValue);
    	// 重置    意向项目名称    字段选择框参数
        resetSelectComponentAttr({
        		fieldID : "INTENTION_NAME",
        		openBeforeCheck: function() {// 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
        			var COMPANY_ID = $("#COMPANY_ID"+pathValue).val();
        			if (!COMPANY_ID) {
        				alertMsg("请选择客户单位，再选择意向项目名称","error");
            			return false;
        			}
        			return true;
        		},
        		searchparamsSettings : function(o) {// 设置查询条件参数
        			var COMPANY_ID = $("#COMPANY_ID"+pathValue).val();
        			// 以下代码固定格式
        			o.params = o.params ? o.params : {};
        			o.params.search = { "COMPANY_ID":COMPANY_ID };
		        },
		        addSettings : function(obj, value) {// 返回值后,追加自定义操作
		        }
        }, "INTENTION_NAME"+pathValue);
        
        //费用明细
        init_tablesGrid();
    }
    var init_tablesGrid = function() {
        /**
         * 列表-按钮-定义
         */
        var toolbar = null;
        if (VIEW_MODE=="edit") {
            toolbar = getButtonTemplates(pathValue,[
                {name:"add",target:"addOpen",title:"添加费用"},
                {name:"edit",target:"editInfo",title:"修改费用"},
        		{name:"delete",target:"deleteInfo",title:"删除费用"},
            ]);//工具条
        }
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: 266,
            read:{"query":"query_BR_CONTRACT_COST_view","objects":[ CONTRACT_ID ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COST_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COST_NAME #","funcExce(\'"+pathValue+"editInfoOpen\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        if (tablesGrid) {
        	setGridDataSource("#tablesGrid"+pathValue, tablesGridJson);
        } else {
        	tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        	tablesGrid.bind("dataBinding", function(e) {
            	$("#tablesGrid"+pathValue+" .k-grid-excel").hide();
            	$("#tablesGrid"+pathValue+" .k-grid-settings").hide();
            	$("#tablesGrid"+pathValue+" .k-grid-pager").hide();
            	$("#tablesGrid"+pathValue).css("height",236);
            });
        }
    }
    var callBack=function(){
    	refreshGrid();
    };
    var refreshGrid=function(){
    	init_tablesGrid();
    }
    var addOpen=function(){
    	if ( !CONTRACT_ID ) {
            alertMsg("请保存主合同, 再添加费用");
            return ;
    	}
        var winOpts={
            url:"berry/bus/saleManage/contractM/add/addCost",
            title:"新增: 主合同费用.."
        };
        openWindow(winOpts,{"CONTRACT_ID":CONTRACT_ID, "pPathValue":pathValue});
    }
    var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据!");
            return ;
        }
        editInfoOpen(arrIds[0]);
     }
     var editInfoOpen = function(ID) {
        var winOpts={
            url:"berry/bus/saleManage/contractM/add/addCost",
            title:"编辑: 主合同费用.."
        };
        var dialog = openWindow(winOpts,{"ID":ID, "pPathValue":pathValue});
     }
     var deleteInfo=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行删除操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
 	        var params={"tableName":"BR_CONTRACT_COST","ids":arrIds};
 	        var url="system/jdbc/delete/batch/table";
 	        deleteGridDataByIds(url,params,refreshGrid);
         });
      }
    
    var submit=function(){
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	var CONTRACT_NO = $.trim( $("#CONTRACT_NO"+pathValue).val() );
    	
    	$("#ID"+pathValue).val(ID);
    	$("#CONTRACT_NO"+pathValue).val(CONTRACT_NO);
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() || !CONTRACT_NO ) {
            alertMsg("表单验证未通过","error");
            return false;
        }
    	
    	if (ID) {
    		submit2();
    	} else {
        	var params={"ID":ID, "CONTRACT_NO":CONTRACT_NO};
            var url="berry/serialNumberManage/contract/contractNoRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0 && result["count"]==0){
    	            	submit2();
    	            }else if(result["code"]>0){
    	            	$("#CONTRACT_NO"+pathValue).val("");
    	                alertMsg("合同编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    var submit2 = function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	CONTRACT_ID = result.ID;
                	$("#ID"+pathValue).val(result.ID);
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        // funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var projectTypeChange = function() {
    	var ID = $("#ID"+pathValue).val();
    	var CONTRACT_NO = $("#CONTRACT_NO"+pathValue).val();
    	if (CONTRACT_NO && !ID) {
        	alertMsg("项目类型变更，请重新获取合同编号");
    	}
    	if ( !ID ) {
        	$("#CONTRACT_NO"+pathValue).val("");
    	} else {
    		alertMsg("合同信息已保存，项目类型变更，无法重新获取合同号");
    	}
    }
    
    var getContractNo=function() {
    	var ID = $("#ID"+pathValue).val();
    	var CONTRACT_NO = $("#CONTRACT_NO"+pathValue).val();
    	var PROJECT_TYPE = $("#PROJECT_TYPE"+pathValue).val();
    	if ( !PROJECT_TYPE ) {
    		alertMsg("请填写项目类型再获取合同编号");
    		return;
    	}
    	if (!ID && !CONTRACT_NO) {// ID为空，新增数据允许获取编号, 或 编号为空允许获取编号
    		var params={"ID":ID, "PROJECT_TYPE":PROJECT_TYPE};
            var url="berry/serialNumberManage/contract/m/contractNo";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	            	$("#CONTRACT_NO"+pathValue).val(result["CONTRACT_NO"]);
    	            }else{
    	                alertMsg("获取编码失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    
    // 测序平台：技术方法和路线文案模板
    var SEQ_PT_W_A_MODE = {
    	"illumina 建库测序": {
    		"建库测序":"3．技术方法和路线："+"\n"
    				+ "3.1 样本质检流程：利用Nanodrop/凝胶电泳技术进行样本检测，当样本合格后，采用标准的Illumina建库流程进行文库构建。"+"\n"
    				+ "3.2 文库质检流程：构建好的文库采用ABI Q3 qPCR仪进行文库的质检，当文库检测为单峰，且浓度高于3nM，体积大于25ul则判定为合格。"+"\n"
    				+ "3.3 Cluster制备：按照Illumina标准Cluster生成，pooling和定量方法进行样本文库混合，并制备Cluster。"
    		,
    		"建库测序分析":"3．技术方法和路线："+"\n"
						+ "3.1 样本质检流程：利用Nanodrop/凝胶电泳技术进行样本检测，当样本合格后，采用标准的Illumina建库流程进行文库构建。"+"\n"
						+ "3.2 文库质检流程：构建好的文库采用ABI Q3 qPCR仪进行文库的质检，当文库检测为单峰，且浓度高于3nM，体积大于25ul则判定为合格。"+"\n"
						+ "3.3 Cluster制备：按照Illumina标准Cluster生成，pooling和定量方法进行样本文库混合，并制备Cluster。"+"\n"
						+ "3.4 数据质控标准：FASTQ文件生成后会进行初级质控,质控规则如下："+"\n"
						+ "1）当单端测序reads中N个数>3时，去除此对pairedreads；"+"\n"
						+ "2）当单端测序read中质量值低于5的碱基比例>=20%时，去除此对pairedreads；"+"\n"
						+ "3）去除adapter序列时，adapter序列至少要匹配8bp。"
    	},
    	"PacBio sequel 2 DNA": {
    		"建库测序":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用Qubit/FA技术进行样本检测，当样本合格后，采用标准的 PacBio SequelII建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：构建好的文库按照PacBio标准文库QC流程进行质控。"+"\n"
				+ "3.3 文库的测序：按照PacBioSequelII标准上机流程进行。"+"\n"
				+ "4.数据格式转换：数据转换和subreads由Pacbio公司的SeuqelII自带软件及流程自动处理，形成subreads。"
			,
			"建库测序分析":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用Qubit/FA技术进行样本检测，当样本合格后，采用标准的 PacBio SequelII建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：构建好的文库按照PacBio标准文库QC流程进行质控。"+"\n"
				+ "3.3 文库的测序：按照PacBioSequelII标准上机流程进行。"+"\n"
				+ "4.数据格式转换：数据转换和subreads由Pacbio公司的SeuqelII自带软件及流程自动处理，形成subreads。"
    	},
    	"PacBio sequel 2 RNA": {
    		"建库测序":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用Agilent2100进行样本检测，当样本合格后，采用标准的 PacBio SequelII建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：构建好的文库按照PacBio标准文库QC流程进行质控。"+"\n"
				+ "3.3 文库的测序：按照PacBio SequelII标准上机流程进行。"+"\n"
				+ "4.数据格式转换：数据转换和subreads由Pacbio公司的SequelII自带软件及流程自动处理，形成subreads。"
			,
			"建库测序分析":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用Agilent2100进行样本检测，当样本合格后，采用标准的 PacBio SequelII建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：构建好的文库按照PacBio标准文库QC流程进行质控。"+"\n"
				+ "3.3 文库的测序：按照PacBio SequelII标准上机流程进行。"+"\n"
				+ "4.数据格式转换：数据转换和subreads由Pacbio公司的SequelII自带软件及流程自动处理，形成subreads。"
    	},
    	"Bionano": {
    		"建库测序":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用Bionano质检标准进行样本检测，当样本合格后，采用标准的Bionano标准建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：构建好的文库按照Bionano标准文库QC流程进行质控。"+"\n"
				+ "3.3 文库的测序：按照BionanoSaphyr上机流程进行操作。"
			,
			"建库测序分析":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用Bionano质检标准进行样本检测，当样本合格后，采用标准的Bionano标准建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：构建好的文库按照Bionano标准文库QC流程进行质控。"+"\n"
				+ "3.3 文库的测序：按照BionanoSaphyr上机流程进行操作。"
    	},
    	"10xGenomics基因组": {
    		"建库测序":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用10x Genomics质检标准进行样本检测，当样本合格后，采用标准的10x Genomics标准建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：；构建好的文库按照10xGenomics标准文库QC流程进行质控。"+"\n"
				+ "3.3 Cluster制备：按照Illumina标准Cluster生成，pooling和定量方法进行样本文库混合，并制备Cluster。"+"\n"
				+ "3.4 按照illumina上机流程进行操作。"
			,
			"建库测序分析":"3．技术方法和路线："+"\n"
				+ "3.1 样本质检流程：利用10x Genomics质检标准进行样本检测，当样本合格后，采用标准的10x Genomics标准建库流程进行文库构建。"+"\n"
				+ "3.2 文库质检流程：；构建好的文库按照10xGenomics标准文库QC流程进行质控。"+"\n"
				+ "3.3 Cluster制备：按照Illumina标准Cluster生成，pooling和定量方法进行样本文库混合，并制备Cluster。"+"\n"
				+ "3.4 按照illumina上机流程进行操作。"+"\n"
				+ "3.5 数据质控标准：FASTQ文件生成后会进行初级质控,质控规则如下："+"\n"
				+ "1）当单端测序reads中N个数>3时，去除此对pairedreads；"+"\n"
				+ "2）当单端测序read中质量值低于5的碱基比例>=20%时，去除此对pairedreads；"+"\n"
				+ "3）去除adapter序列时，adapter序列至少要匹配8bp。"
    	},
    };
    var SEQ_PT_W_A_Change = function(thisObj) {
    	var CONTENT_TEXT_A = "";//建库测序
    	var CONTENT_TEXT_B = "";//建库测序分析
    	
    	var viewModel=funcExce(pathValue+"getViewModel");
    	var SEQ_PT_W_A_OBJ = viewModel.get("SEQ_PT_W_A");
    	for(var key in SEQ_PT_W_A_OBJ){
    		var SEQ_PT_W_A_MODE_KEY = "";
    		if (SEQ_PT_W_A_OBJ[key]["value"]) {
    			SEQ_PT_W_A_MODE_KEY = SEQ_PT_W_A_OBJ[key]["value"];
    		} else if (SEQ_PT_W_A_OBJ[key]) {
    			SEQ_PT_W_A_MODE_KEY = SEQ_PT_W_A_OBJ[key];
    		}
    		if ( SEQ_PT_W_A_MODE_KEY && SEQ_PT_W_A_MODE[ SEQ_PT_W_A_MODE_KEY ] ) {
    			
    			var val_1 = SEQ_PT_W_A_MODE[ SEQ_PT_W_A_MODE_KEY ]["建库测序"];
    			CONTENT_TEXT_A += CONTENT_TEXT_A ? "\n"+val_1 : val_1;
    			
    			var val_2 = SEQ_PT_W_A_MODE[ SEQ_PT_W_A_MODE_KEY ]["建库测序分析"];
    			CONTENT_TEXT_B += CONTENT_TEXT_B ? "\n"+val_2 : val_2;
    		}
    	}
    	
    	$("#CONTENT_ANALYZE_REMARK"+pathValue).val(CONTENT_TEXT_A);
    	$("#CONTENT_ANALYZE_B"+pathValue).val(CONTENT_TEXT_B);
    }
    

    var confirmCreateContractRecord=function(){
    	var ID = $("#ID"+pathValue).val();
    	var TEMPLATE_NAME = $("#CONTRACT_ATT_TEMPLATE"+pathValue).val();
        if( !ID ){
            alertMsg("请保存合同后，再生成合同电子档");
            return ;
        }
        if( !TEMPLATE_NAME ){
            alertMsg("请选择合同模板");
            return ;
        }
        confirmMsg("确认", "确定要按照选择的模板生成合同电子档吗?", "warn", function() {
	         var params={ "ID":ID, "TEMP_NAME":TEMPLATE_NAME };
	         var url="berry/bus/saleManage/contract/createContractRecord";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
	 	            	if (result["msg"]) {
	 	            		alertMsg("提示:"+result["msg"]);
	 	            	} else {
	 	            		var viewModel=funcExce(pathValue+"getViewModel");
	 	            		viewModel.set("CONTRACT_ATT_FILEPATH", result.CONTRACT_ATT_FILEPATH);
	 	            	}
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
	     });
    }
    
    var SETTLEMENT_MODES_Change = function(thisObj) {
    	var SETTLEMENT_MODES = $(thisObj).val();
    	
    	var PAYMENT_AMOUNT_FIRST_OBJ = $("#PAYMENT_AMOUNT_FIRST"+pathValue);//首付款（元）
    	var PAYMENT_AMOUNT_MIDDLE_OBJ = $("#PAYMENT_AMOUNT_MIDDLE"+pathValue);//中期款（元）
    	var PAYMENT_AMOUNT_END_OBJ = $("#PAYMENT_AMOUNT_END"+pathValue);//尾款（元）
    	var CONTRACT_RECON_DATE_OBJ = $("#CONTRACT_RECON_DATE"+pathValue).data("kendoDropDownList");//每月对帐日期
    	var CONTRACT_COMF_DATE_OBJ = $("#CONTRACT_COMF_DATE"+pathValue);//对帐默认确认时间(天内)
    	var CONTRACT_PAY_DATE_OBJ = $("#CONTRACT_PAY_DATE"+pathValue).data("kendoDropDownList");//每月打款日期
    	
    	if (SETTLEMENT_MODES == "一次性") {
    		PAYMENT_AMOUNT_FIRST_OBJ.attr("disabled","disabled");//首付款（元）
    		PAYMENT_AMOUNT_FIRST_OBJ.val("");
    		
    		PAYMENT_AMOUNT_MIDDLE_OBJ.attr("disabled","disabled");//中期款（元）
        	PAYMENT_AMOUNT_MIDDLE_OBJ.val("");
    		
    		PAYMENT_AMOUNT_END_OBJ.attr("disabled","disabled");//尾款（元）
        	PAYMENT_AMOUNT_END_OBJ.val("");
    		
    		CONTRACT_RECON_DATE_OBJ.enable(false);//每月对帐日期
        	CONTRACT_RECON_DATE_OBJ.value("");
    		CONTRACT_RECON_DATE_OBJ.trigger("change");
    		
    		CONTRACT_COMF_DATE_OBJ.attr("disabled","disabled");//对帐默认确认时间(天内)
    		CONTRACT_COMF_DATE_OBJ.val("");
    		
    		CONTRACT_PAY_DATE_OBJ.enable(false);//每月打款日期
    		CONTRACT_PAY_DATE_OBJ.value("");
    		CONTRACT_PAY_DATE_OBJ.trigger("change");
    	}
    	if (SETTLEMENT_MODES == "分期结") {
    		PAYMENT_AMOUNT_FIRST_OBJ.removeAttr("disabled");//首付款（元）
    		//PAYMENT_AMOUNT_FIRST_OBJ.val("");
    		
    		PAYMENT_AMOUNT_MIDDLE_OBJ.removeAttr("disabled");//中期款（元）
        	//PAYMENT_AMOUNT_MIDDLE_OBJ.val("");
    		
    		PAYMENT_AMOUNT_END_OBJ.removeAttr("disabled");//尾款（元）
        	//PAYMENT_AMOUNT_END_OBJ.val("");
    		
    		CONTRACT_RECON_DATE_OBJ.enable(false);//每月对帐日期
        	CONTRACT_RECON_DATE_OBJ.value("");
    		CONTRACT_RECON_DATE_OBJ.trigger("change");
    		
    		CONTRACT_COMF_DATE_OBJ.attr("disabled","disabled");//对帐默认确认时间(天内)
    		CONTRACT_COMF_DATE_OBJ.val("");
    		
    		CONTRACT_PAY_DATE_OBJ.enable(false);//每月打款日期
    		CONTRACT_PAY_DATE_OBJ.value("");
    		CONTRACT_PAY_DATE_OBJ.trigger("change");
    	}
    	if (SETTLEMENT_MODES == "月结") {
    		PAYMENT_AMOUNT_FIRST_OBJ.attr("disabled","disabled");//首付款（元）
    		PAYMENT_AMOUNT_FIRST_OBJ.val("");
    		
    		PAYMENT_AMOUNT_MIDDLE_OBJ.attr("disabled","disabled");//中期款（元）
        	PAYMENT_AMOUNT_MIDDLE_OBJ.val("");
    		
    		PAYMENT_AMOUNT_END_OBJ.attr("disabled","disabled");//尾款（元）
        	PAYMENT_AMOUNT_END_OBJ.val("");
    		
    		CONTRACT_RECON_DATE_OBJ.enable(true);//每月对帐日期
        	CONTRACT_RECON_DATE_OBJ.value("5");
    		CONTRACT_RECON_DATE_OBJ.trigger("change");
    		
    		CONTRACT_COMF_DATE_OBJ.removeAttr("disabled");//对帐默认确认时间(天内)
    		CONTRACT_COMF_DATE_OBJ.val("5");
    		
    		CONTRACT_PAY_DATE_OBJ.enable(true);//每月打款日期
    		CONTRACT_PAY_DATE_OBJ.value("");
    		CONTRACT_PAY_DATE_OBJ.trigger("change");
    	}
    	if (SETTLEMENT_MODES == "季度结") {
    		PAYMENT_AMOUNT_FIRST_OBJ.attr("disabled","disabled");//首付款（元）
    		PAYMENT_AMOUNT_FIRST_OBJ.val("");
    		
    		PAYMENT_AMOUNT_MIDDLE_OBJ.attr("disabled","disabled");//中期款（元）
        	PAYMENT_AMOUNT_MIDDLE_OBJ.val("");
    		
    		PAYMENT_AMOUNT_END_OBJ.attr("disabled","disabled");//尾款（元）
        	PAYMENT_AMOUNT_END_OBJ.val("");
    		
    		CONTRACT_RECON_DATE_OBJ.enable(true);//每月对帐日期
        	CONTRACT_RECON_DATE_OBJ.value("5");
    		CONTRACT_RECON_DATE_OBJ.trigger("change");
    		
    		CONTRACT_COMF_DATE_OBJ.removeAttr("disabled");//对帐默认确认时间(天内)
    		CONTRACT_COMF_DATE_OBJ.val("5");
    		
    		CONTRACT_PAY_DATE_OBJ.enable(true);//每月打款日期
    		CONTRACT_PAY_DATE_OBJ.value("");
    		CONTRACT_PAY_DATE_OBJ.trigger("change");
    	}
//    	if (SETTLEMENT_MODES == "分批结") {
//    		PAYMENT_AMOUNT_FIRST_OBJ.attr("disabled","disabled");//首付款（元）
//    		PAYMENT_AMOUNT_FIRST_OBJ.val("");
//    		
//    		PAYMENT_AMOUNT_MIDDLE_OBJ.attr("disabled","disabled");//中期款（元）
//        	PAYMENT_AMOUNT_MIDDLE_OBJ.val("");
//    		
//    		PAYMENT_AMOUNT_END_OBJ.attr("disabled","disabled");//尾款（元）
//        	PAYMENT_AMOUNT_END_OBJ.val("");
//    		
//    		CONTRACT_RECON_DATE_OBJ.enable(true);//每月对帐日期
//        	CONTRACT_RECON_DATE_OBJ.value("");
//    		CONTRACT_RECON_DATE_OBJ.trigger("change");
//    		
//    		CONTRACT_COMF_DATE_OBJ.attr("disabled","disabled");//对帐默认确认时间(天内)
//    		CONTRACT_COMF_DATE_OBJ.val("");
//    		
//    		CONTRACT_PAY_DATE_OBJ.enable(true);//每月打款日期
//    		CONTRACT_PAY_DATE_OBJ.value("");
//    		CONTRACT_PAY_DATE_OBJ.trigger("change");
//    	}
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "getContractNo":getContractNo,
        "projectTypeChange":projectTypeChange,
        
        "refreshGrid":refreshGrid,
        "callBack":callBack,//回调方法
        
        "addOpen":addOpen,//打开添加表单
        "editInfo":editInfo,
        "editInfoOpen":editInfoOpen,
        "deleteInfo":deleteInfo,
        
        "SEQ_PT_W_A_Change":SEQ_PT_W_A_Change,
        "confirmCreateContractRecord":confirmCreateContractRecord,
        "SETTLEMENT_MODES_Change":SETTLEMENT_MODES_Change,
    });
 
 });
 