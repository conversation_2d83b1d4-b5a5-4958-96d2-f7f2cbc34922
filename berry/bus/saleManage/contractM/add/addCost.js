$(document).ready(function() {
    var pathValue="berry-bus-saleManage-contractM-add-addCost";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CONTRACT_COST"
        };
    }
    
    var pPathValue="";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	pPathValue = params.pPathValue;
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
    
    var submit=function() {
    	var SORT_NUM = $("#SORT_NUM"+pathValue).val();
    	if ( !SORT_NUM ) {
        	var nowTime = new Date().getTime();
        	$("#SORT_NUM"+pathValue).val(nowTime);
    	}
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pPathValue+"refreshGrid");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var setCOST_TOTAL_SUM = function() {
    	var UNIT_PRICE = $("#UNIT_PRICE"+pathValue).val();
    	var COST_NUM = $("#COST_NUM"+pathValue).val();
    	if (UNIT_PRICE && COST_NUM) {
    		$("#COST_TOTAL_SUM"+pathValue).val( UNIT_PRICE*COST_NUM );
    	}
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "setCOST_TOTAL_SUM":setCOST_TOTAL_SUM,
    });
 
 });
 