$(document).ready(function() {
   var pathValue="berry-bus-saleManage-targetCustomerArchives-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;//草稿列表
   var tablesGrid2;//已提交列表

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /** "草稿"列表 */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"创建目标客户"},
            {name:"edit",target:"editInfo"},
            {name:"delete",target:"deleteInfo"},
           // {name:"recordTail",target:"recordTail",title: "记录跟踪"},
           // {name:"importExcel",target:"importExcel",title: "导入Excel"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_TARGET_CUSTOMER_ARCHIVES_view","objects":["草稿"]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /** "已提交"列表 */
        //请求参数
        var tablesGridJson2={
        		url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
        		sort: "",//排序
        		height: fullh-112,
        		read:{"query":"query_BR_TARGET_CUSTOMER_ARCHIVES_view","objects":["已提交"]},
        		headerFilter:function(cols,i){
        			if(i){
        				if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
        					setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
        				}
        			}
        		}
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_TARGET_CUSTOMER_ARCHIVES_view","objects":["草稿"],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   /** "已提交"列表查询 */
   var searchGrid2 = function() {
	   var searchparams = getJsonByForm("searchform2",pathValue);
	   var tablesGridJson = {
			   url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
			   sort: "",//排序
			   toolbar: toolbar,
			   height: fullh-112,
			   read:{"query":"query_BR_TARGET_CUSTOMER_ARCHIVES_view","objects":["已提交"],
				   "search": searchparams
			   },
			   headerFilter:function(cols,i){
				   if(i){
					   if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
						   setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
					   }
				   }
			   }
	   };
	   setGridDataSource("#tablesGrid2_"+pathValue,tablesGridJson);
   }
   
   var searchGridWindow = function() {
	   var searchparams = getJsonByForm("searchWindowForm",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_TARGET_CUSTOMER_ARCHIVES_view","objects":["草稿"],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchWindowOpen = function() {
	   $("#searchWindow").kendoWindow({
           width: "600px",
           height: "500px",
           title: "高级查询",
           visible: false,
           actions: [
               "Close"
           ]
       }).data("kendoWindow").center().open();
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/saleManage/targetCustomerArchives/add/add",
            title:"创建目标客户.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
        openWindow({
            url:"berry/bus/saleManage/targetCustomerArchives/add/add",
            title:"编辑目标客户.."
        },{"ID":id});
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        
        var rData = getGridSelectData(tablesGrid);
        if (rData[0].STATUS!='草稿') {
            alertMsg("只能修改草稿状态的数据!");
            return ;
        }

        var winOpts={
            url:"berry/bus/saleManage/targetCustomerArchives/add/add",
            title:"编辑目标客户.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0]});//传递id
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid);
        var status = "";
        for (var i = 0; i < rData.length; i++) {
            if (rData[i].STATUS == "已提交") {
            	status = "已提交";
                break;
            }
        }
        if (status == "已提交") {
            alertMsg("不能删除已提交的数据!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"tableName":"BR_TARGET_CUSTOMER_ARCHIVES","ids":arrIds};
	        var url="system/jdbc/delete/batch/table";
	        deleteGridDataByIds(url,params,refreshGrid);
    	});
     }
     
     var importExcel=function(){
    	 alert(123);
     }
     
     var recordTail=function(){
    	 alert(456);
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
         "searchGrid":searchGrid,
         "searchGrid2":searchGrid2,
         "searchGridWindow":searchGridWindow,
         "searchWindowOpen":searchWindowOpen,
         "recordTail":recordTail,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
