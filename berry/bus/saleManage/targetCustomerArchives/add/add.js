$(document).ready(function() {
    var pathValue="berry-bus-saleManage-targetCustomerArchives-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_TARGET_CUSTOMER_ARCHIVES"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }

    var submit1=function(){
    	submit(1);
    }

    var submit2=function(){
    	submit(2);
    }
    
    var submit=function(i){
    	
    	if (i == 2) {
        	$("#STATUS"+pathValue).val("已提交");
    	} else {
    		var ID = $("#ID"+pathValue).val();
    		var STATUS = $("#STATUS"+pathValue).val();
    		if (STATUS != "已提交" || !ID) {
    			$("#STATUS"+pathValue).val("草稿");
    		}
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit1":submit1,
        "submit2":submit2,
    });
 
 });
 