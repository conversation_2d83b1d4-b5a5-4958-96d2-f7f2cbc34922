$(document).ready(function() {
    var pathValue="berry-bus-saleManage-contractAlter-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_CONTRACT_ALTER"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form1",pathValue,params);
        getInfo("form2",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form1",pathValue,params,url);//传入id
        
        getInfo("form2",pathValue,params,url);//传入id
    }
    
    var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form1",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                    //提交成功, 提交第二个表单
                	$("#form2"+pathValue+" #ID"+pathValue).val(result.ID);
                	submit2();
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var submit2=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form2",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	// 动作补偿, 修改合同表
                	submit3();
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var submit3=function(){
        var id=$("#form2"+pathValue+" #ID"+pathValue).val();
        var params={ "id":id };
        var url= "berry/bus/saleManage/contract/contractAlter/updateContractInfo";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var getContractNo=function() {
    	alert(111);
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "getContractNo":getContractNo,
    });
 
 });
 