$(document).ready(function() {
   var pathValue="berry-bus-saleManage-contractApprove1-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"editInfo",title:"审核"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_CONTRACT_view_ContractTypeALL","objects":[ ["大区经理待审核"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
           height: fullh-112,
            read:{"query":"query_BR_CONTRACT_view_ContractTypeALL","objects":[ ["大区经理审核不通过", "销售总监待审核", "销售总监审核不通过", "待打印", "已打印", "待返还", "已返还"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_CONTRACT_view_ContractTypeALL","objects":[ ["大区经理待审核"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
   }
   var searchGrid2 = function() {
	   var searchparams = getJsonByForm("searchform2",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_CONTRACT_view_ContractTypeALL","objects":[ ["大区经理审核不通过", "销售总监待审核", "销售总监审核不通过", "待打印", "已打印", "待返还", "已返还"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid2_"+pathValue,tablesGridJson);
   }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid2=function(){
         if(tablesGrid2){
             tablesGrid2.dataSource.read();//重新读取--刷新
         }
      }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        
        var rData = getGridSelectData(tablesGrid);
        
        var CONTRACT_TYPE = rData[0].CONTRACT_TYPE;
        var URL = "berry/bus/saleManage/contractApprove1/add/add";
        if (CONTRACT_TYPE == '主合同') {
        	URL = "berry/bus/saleManage/contractApprove1/approve1M/approve";
        }
        if (CONTRACT_TYPE == '报账合同') {
        	URL = "berry/bus/saleManage/contractApprove1/approve1BZ/approve";
        }
        if (CONTRACT_TYPE == '补充协议') {
        	URL = "berry/bus/saleManage/contractApprove1/approve1BC/approve";
        }

        var winOpts={
            url:URL,
            title:CONTRACT_TYPE+": 大区经理审核.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0],"COMPANY_NAME":rData[0].COMPANY_NAME, "PROVINCE_NAME":rData[0].PROVINCE_NAME, "CITY_NAME":rData[0].CITY_NAME});//传递id
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "editInfo":editInfo,
         "searchGrid":searchGrid,
         "refreshGrid":refreshGrid,
         "searchGrid2":searchGrid2,
         "refreshGrid2":refreshGrid2,
         "callBack":callBack,//回调方法
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
