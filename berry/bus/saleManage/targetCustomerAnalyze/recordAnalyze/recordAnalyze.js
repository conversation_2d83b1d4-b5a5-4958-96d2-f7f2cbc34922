$(document).ready(function() {
    var pathValue="berry-bus-saleManage-targetCustomerAnalyze-recordAnalyze-recordAnalyze";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {};
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	var toolbar = getButtonTemplates(pathValue, []);//工具条
    	var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-167,
            read:{"query":"query_BR_TARGET_CUSTOMER_ANALYZE_view_RECORD","objects":[params.ARCHIVES_ID]},
        };
        initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
    });
 
 });
 