$(document).ready(function() {
   var pathValue="berry-bus-saleManage-targetCustomerAnalyze-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"editInfo",title:"分析目标客户"},
            {name:"recordAnalyze",target:"recordAnalyze",title: "历史分析记录"},
//            {name:"add",target:"addOpen"},
//            {name:"edit",target:"editInfo"},
//            {name:"delete",target:"deleteInfo"},
           // {name:"importExcel",target:"importExcel",title: "导入Excel"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_TARGET_CUSTOMER_ANALYZE_view_MAIN","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_TARGET_CUSTOMER_ANALYZE_view_MAIN","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGridWindow = function() {
	   var searchparams = getJsonByForm("searchWindowForm",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_TARGET_CUSTOMER_ANALYZE_view_MAIN","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="SUBJECT_GROUP_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= SUBJECT_GROUP_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= COMPANY_NAME #\',\'#= PROVINCE_NAME #\',\'#= CITY_NAME #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchWindowOpen = function() {
	   $("#searchWindow").kendoWindow({
           width: "600px",
           height: "500px",
           title: "高级查询",
           visible: false,
           actions: [
               "Close"
           ]
       }).data("kendoWindow").center().open();
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/saleManage/targetCustomerAnalyze/add/add",
            title:"新增.."
        };
        openWindow(winOpts);
    }

    var open=function(id, COMPANY_NAME , PROVINCE_NAME , CITY_NAME){
        openWindow({
            url:"berry/bus/saleManage/targetCustomerAnalyze/add/add",
            title:"编辑.."
        },{"ID":id, "COMPANY_NAME":COMPANY_NAME, "PROVINCE_NAME":PROVINCE_NAME, "CITY_NAME":CITY_NAME});
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行分析!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行分析!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid);
        var winOpts={
            url:"berry/bus/saleManage/targetCustomerAnalyze/add/add",
            title:"分析目标客户.."
        };
        var dialog = openWindow(winOpts,{
        	"ARCHIVES_ID":rData[0].ID,
        	"CUSTOMER_ID":rData[0].CUSTOMER_ID,
        	"CUSTOMER_CODE":rData[0].CUSTOMER_CODE,
        	"CUSTOMER_NAME":rData[0].CUSTOMER_NAME
        });//传递id
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BR_TARGET_CUSTOMER_ANALYZE","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }
     
     var recordAnalyze=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据查看历史分析记录!");
             return ;
         }else if(arrIds.length!=1){
             alertMsg("请只选择一条数据查看历史分析记录!");
             return ;
         }
         var winOpts={
             url:"berry/bus/saleManage/targetCustomerAnalyze/recordAnalyze/recordAnalyze",
             title:"目标客户历史分析记录.."
         };
         var dialog = openWindow(winOpts,{ "ARCHIVES_ID":arrIds[0] });//传递id
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "searchGrid":searchGrid,
         "searchGridWindow":searchGridWindow,
         "searchWindowOpen":searchWindowOpen,
         "recordAnalyze":recordAnalyze,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
