$(document).ready(function() {
   var pathValue="berry-bus-saleManage-intentionInfo-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /** "新增"列表 */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"新增意向客户"},
            {name:"edit",target:"editInfo"},
            {name:"delete",target:"deleteInfo"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_INTENTION_INFO_view","objects":["草稿"]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="INTENTION_CODE"){
                        setJsonParam(cols[i],"template",getTemplate("#= INTENTION_CODE #","funcExce(\'"+pathValue+"editInfoOpen\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        
        /** "已提交"列表 */
        var toolbar2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson2={
        		url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
        		sort: "",//排序
        		toolbar: toolbar2,
        		height: fullh-112,
        		read:{"query":"query_BR_INTENTION_INFO_view","objects":["已提交"]},
        		headerFilter:function(cols,i){
        			if(i){
                        if(cols[i]["field"]&&cols[i]["field"]=="INTENTION_CODE"){
                            setJsonParam(cols[i],"template",getTemplate("#= INTENTION_CODE #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                        }
        			}
        		}
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/saleManage/intentionInfo/add/add",
            title:"新增: 意向客户.."
        };
        openWindow(winOpts);
    }

    var open=function(ID){
        openWindow({
            url:"berry/bus/saleManage/intentionInfo/view/view",
            title:"查看: 意向客户.."
        },{"ID":ID});
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        editInfoOpen(arrIds[0]);
     }
     var editInfoOpen = function(ID) {
        var winOpts={
            url:"berry/bus/saleManage/intentionInfo/add/add",
            title:"编辑: 意向客户.."
        };
        var dialog = openWindow(winOpts,{"ID":ID});
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"tableName":"BR_INTENTION_INFO","ids":arrIds};
	        var url="system/jdbc/delete/batch/table";
	        deleteGridDataByIds(url,params,refreshGrid);
    	});
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "editInfoOpen":editInfoOpen,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
