$(document).ready(function() {
    var pathValue="berry-bus-saleManage-intentionInfo-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_INTENTION_INFO"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }

    var submit1=function(){
    	submit(1);
    }

    var submit2=function(){
    	submit(2);
    }
    
    var submit = function(i) {
    	var ID = $.trim( $("#ID"+pathValue).val() );
    	var INTENTION_CODE = $.trim( $("#INTENTION_CODE"+pathValue).val() );
    	
    	$("#ID"+pathValue).val(ID);
    	$("#INTENTION_CODE"+pathValue).val(INTENTION_CODE);
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","error");
            return false;
        }
    	
    	if (ID) {
    		submit6(i);
    	} else {
        	var params={"ID":ID, "INTENTION_CODE":INTENTION_CODE};
            var url="berry/serialNumberManage/intention/intentionCodeRepeatCheck";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0 && result["count"]==0){
    	            	submit6(i);
    	            }else if(result["code"]>0){
    	            	$("#INTENTION_CODE"+pathValue).val("");
    	                alertMsg("意向编号重复，不能提交!","error");
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }
    var submit6 = function(i){
    	
    	if (i == 2) {
        	$("#STATUS"+pathValue).val("已提交");
    	} else {
    		var ID = $("#ID"+pathValue).val();
    		var STATUS = $("#STATUS"+pathValue).val();
    		if (STATUS != "已提交" || !ID) {
    			$("#STATUS"+pathValue).val("草稿");
    		}
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var getIntentionCode=function(){
    	var ID = $("#ID"+pathValue).val();
    	var INTENTION_CODE = $("#INTENTION_CODE"+pathValue).val();
    	if (!ID || !INTENTION_CODE) {// ID为空，新增数据允许获取编号, 或 编号为空允许获取编号
    		var params={"ID":ID};
            var url="berry/serialNumberManage/intention/intentionCode";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	            	$("#INTENTION_CODE"+pathValue).val(result["INTENTION_CODE"]);
    	            }else{
    	                alertMsg("获取编码失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	}
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit1":submit1,
        "submit2":submit2,
        "getIntentionCode":getIntentionCode,
    });
 
 });
 