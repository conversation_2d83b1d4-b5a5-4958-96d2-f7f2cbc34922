$(document).ready(function() {
   var pathValue="berry-bus-retAmount-retAmountCancel3-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_RET_AMOUNT_CANCEL_FW_view","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_RET_AMOUNT_CANCEL_FW_view","objects":[],
	            	"search": searchparams
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
   }

    var open=function(id, P_COMPANY_NAME, PROVINCE_TEXT, CITY_TEXT){
        openWindow({
            url:"berry/bus/retAmount/retAmount1/add/add",
            title:"编辑.."
        },{"ID":id, "P_COMPANY_NAME":P_COMPANY_NAME, "PROVINCE_TEXT":PROVINCE_TEXT, "CITY_TEXT":CITY_TEXT});
    }
     
     var callBack=function(){
    	 refreshGrid();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "searchGrid":searchGrid,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
