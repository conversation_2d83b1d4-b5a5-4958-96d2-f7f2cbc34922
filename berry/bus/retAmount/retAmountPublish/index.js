$(document).ready(function() {
   var pathValue="berry-bus-retAmount-retAmountPublish-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen"},
            {name:"edit",target:"editInfo"},
            {name:"delete",target:"deleteInfo"},
            {name:"submit",target:"submitPublish", title:"确认发布"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-112,
            read:{"query":"query_BR_RET_AMOUNT_INFO_view","objects":[ ["草稿"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
           height: fullh-112,
            read:{"query":"query_BR_RET_AMOUNT_INFO_view","objects":[ ["待核销", "已核销"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="STATUS"){
                        setJsonParam(cols[i],"template",getTemplate("#= STATUS #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_RET_AMOUNT_INFO_view","objects":[ ["草稿"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
   }
   var searchGrid2 = function() {
	   var searchparams = getJsonByForm("searchform2",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_RET_AMOUNT_INFO_view","objects":[ ["待核销", "已核销"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="STATUS"){
	                        setJsonParam(cols[i],"template",getTemplate("#= STATUS #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid2_"+pathValue,tablesGridJson);
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/bus/retAmount/retAmountPublish/add/add",
            title:"新增: 回款信息.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
        openWindow({
            url:"berry/bus/retAmount/retAmountPublish/info/info",
            title:"详情: 回款信息.."
        },{"ID":id});
    }

     var submitPublish=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
             var url="berry/bus/retAmountPublish/submitPublish";
             var params={"ids":arrIds}
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	refreshGrid();
     	            	refreshGrid2();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
      }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid2=function(){
         if(tablesGrid2){
             tablesGrid2.dataSource.read();//重新读取--刷新
         }
      }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        
        var rData = getGridSelectData(tablesGrid);

        var winOpts={
            url:"berry/bus/retAmount/retAmountPublish/add/add",
            title:"编辑: 回款信息.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0]});//传递id
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"tableName":"BR_RET_AMOUNT_INFO","ids":arrIds};
	        var url="system/jdbc/delete/batch/table";
	        deleteGridDataByIds(url,params,refreshGrid);
        });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "deleteInfo":deleteInfo,
         "submitPublish":submitPublish,//提交确认发布
         "callBack":callBack,//回调方法
         "searchGrid":searchGrid,
         "refreshGrid":refreshGrid,
         "searchGrid2":searchGrid2,
         "refreshGrid2":refreshGrid2,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
