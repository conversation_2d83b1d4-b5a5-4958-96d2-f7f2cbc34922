$(document).ready(function() {
    var pathValue="berry-bus-retAmount-retAmountCancel1-add-editCancelAmount";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return { };
    }
    
    var pPathValue;
    
    var tablesGrid;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	pPathValue = params.pPathValue;
    	if ( !pPathValue ) {
    		alertMsg("表单调用参数错误","wran");
            return false;
    	}
    	getInfo("form",pathValue,params);
    }

    var submit=function(){
        formSubmit({
            url:"berry/bus/retAmountCancel/editCancelAmount",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	if (result["msg"]) {
                		alertMsg(result["msg"]);
                		return;
                	}
                    //提交成功
                    alertMsg("保存成功","success",function(){
                        funcExce(pPathValue+"refreshGrid");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("保存失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 