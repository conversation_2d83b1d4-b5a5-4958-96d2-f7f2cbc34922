$(document).ready(function() {
    var pathValue="berry-bus-retAmount-retAmountCancel1-add-addInvoiceIssued";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return { };
    }
    
    var RET_AMOUNT_INFO_ID;//回款信息ID
    var REMIT_COMPANY_ID;// 打款客户
    var pPathValue;
    
    var tablesGrid;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	RET_AMOUNT_INFO_ID = params.RET_AMOUNT_INFO_ID;
    	pPathValue = params.pPathValue;
    	if ( !RET_AMOUNT_INFO_ID || !pPathValue ) {
    		alertMsg("表单调用参数错误","wran");
            return false;
    	}
    	
    	REMIT_COMPANY_ID = params.REMIT_COMPANY_ID;
    	
    	var searchparams = {};
    	if (REMIT_COMPANY_ID) {
    		searchparams.REMIT_COMPANY_ID=REMIT_COMPANY_ID;
    	}
    	
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addInvoiceIssued", title:"添加选中发票"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-112,
            read:{"query":"query_BR_RET_AMOUNT_CANCEL_view_ADD","objects":[ RET_AMOUNT_INFO_ID ],
            	"search": searchparams
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }
    
    var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

    var addInvoiceIssued=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据!");
            return ;
        }
        confirmMsg("确认", "确定要添加选中的数据吗?", "warn", function() {
            var url="berry/bus/retAmountCancel/addInvoiceIssued";
            var params={"RET_AMOUNT_INFO_ID":RET_AMOUNT_INFO_ID, "ids":arrIds}
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
                    	if (result["msg"]) {
                    		alertMsg(result["msg"]);
                    		return;
                    	}
    	            	//提交成功
                        alertMsg("添加成功, 请修改新增数据的“核销金额”","success",function(){
                            funcExce(pPathValue+"refreshGrid");//执行回调
                            funcExce(pathValue+"close");//关闭页面
                        });
    	            }else{
    	                alertMsg("提示:添加失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "refreshGrid":refreshGrid,
        "addInvoiceIssued":addInvoiceIssued,
    });
 
 });
 