$(document).ready(function() {
    var pathValue="berry-bus-retAmount-retAmountCancel1-info-info";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
        	tableName: "BR_RET_AMOUNT_INFO"
        };
    }
    
    var RET_AMOUNT_INFO_ID;//回款信息ID
    
    var tablesGrid;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	RET_AMOUNT_INFO_ID = params.ID;
    	if ( !RET_AMOUNT_INFO_ID ) {
    		alertMsg("表单调用参数错误","wran");
            return false;
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-416,
            read:{"query":"query_BR_RET_AMOUNT_CANCEL_view","objects":[ RET_AMOUNT_INFO_ID ]}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
    });
 
 });
 