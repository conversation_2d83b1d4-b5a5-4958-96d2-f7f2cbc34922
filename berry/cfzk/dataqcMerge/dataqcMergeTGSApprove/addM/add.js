$(document).ready(function() {
    var pathValue="berry-cfzk-dataqcMerge-dataqcMergeTGSApprove-addM-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_DATAQC_TASK_LIST"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params
     */
    var init=function(params){
        getInfo("form1",pathValue,params);
        getInfo("form2",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form2",pathValue,params,url);//传入id
    }

    var submit=function(){
    	
    	var approveRS = $("#form1"+pathValue+" #TASK_APPROVE_RS"+pathValue).val();
    	if (approveRS == "审核通过") {
    		$("#form1"+pathValue+" #TASK_FLAG"+pathValue).val("进行中");
    	} else if (approveRS == "审核不通过") {
    		$("#form1"+pathValue+" #TASK_FLAG"+pathValue).val(approveRS);
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form1",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 