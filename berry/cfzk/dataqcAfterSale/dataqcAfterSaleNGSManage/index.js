$(document).ready(function() {
   var pathValue="berry-cfzk-dataqcAfterSale-dataqcAfterSaleNGSManage-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid_M1;
   var tablesGrid_M1_selectRowID = "";
   var tablesGrid_D1;
   
   var tablesGrid_M2;
   var tablesGrid_M2_selectRowID = "";
   var tablesGrid_D2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar_M1=getButtonTemplates(pathValue,[
//            {name:"add",target:"addOpen_M",title:"新建售后任务单"},
//            {name:"edit",target:"editInfo_M",title:"修改"},
//    		{name:"delete",target:"deleteInfo_M", title:"删除"},
            {name:"submit_M",target:"submit_M", title:"确认完成"},
        ]);//工具条
        //请求参数
        var tablesGridJson_M1={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M1,
            height: (fullh-146)/2,
            read:{"query":"query_BR_DATAQC_TASK_LIST_view","objects":[ ["NGS数据售后"], ["进行中"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                    if(cols[i]["field"]&&cols[i]["field"]=="TQ_TASK_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= TQ_TASK_NO #","funcExce(\'"+pathValue+"open_M\',\'#= ID #\');","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M1 = initKendoGrid("#tablesGrid_M1_"+pathValue,tablesGridJson_M1);//初始化表格的方法
        tablesGrid_M1.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M1_selectRowID = "";
        		tablesGrid_D1.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M1_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M1_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M1_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M1_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M1_selectRowID = thisRowID;
        	searchGrid_D1();//刷新子表数据
        });
        tablesGrid_M1.bind("dataBinding", function(e) {
	        if (tablesGrid_M1_selectRowID) {
		  	    tablesGrid_M1_selectRowID = "";
			    searchGrid_D1();
	        }
        });
        
        
        /**
         * 列表-按钮-定义
         */
        var toolbar_D1=getButtonTemplates(pathValue,[
            {name:"edit",target:"openEditMX", title:"修改"},
            {name:"edit",target:"openEditMXBatch", title:"批量修改"},
        ]);//工具条
        //请求参数
        var tablesGridJson_D1={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D1,
           height: (fullh-146)/2,
            read:{"query":"query_BR_DATAQC_TASK_MX_A_S_NGS_view","objects":[""]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="TQGS_ZP"){
                        setJsonParam(cols[i],"template",getTemplate("#= TQGS_ZP #","funcExce(\'"+pathValue+"openViewTqMxResult_1\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_D1 = initKendoGrid("#tablesGrid_D1_"+pathValue,tablesGridJson_D1);//初始化表格的方法
        
        

        /**
         * 列表-按钮-定义
         */
        var toolbar_M2=getButtonTemplates(pathValue,[ ]);//工具条
        //请求参数
        var tablesGridJson_M2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M2,
           height: (fullh-146)/2,
            read:{"query":"query_BR_DATAQC_TASK_LIST_view","objects":[ ["NGS数据售后"], ["已完成", "已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M2 = initKendoGrid("#tablesGrid_M2_"+pathValue,tablesGridJson_M2);//初始化表格的方法
        tablesGrid_M2.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M2_selectRowID = "";
        		tablesGrid_D2.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M2_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M2_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M2_selectRowID = thisRowID;
        	searchGrid_D2();//刷新子表数据
        });
        tablesGrid_M2.bind("dataBinding", function(e) {
	        if (tablesGrid_M2_selectRowID) {
		  	    tablesGrid_M2_selectRowID = "";
			    searchGrid_D2();
	        }
        });
        
        
        /**
         * 列表-按钮-定义
         */
        var toolbar_D2=getButtonTemplates(pathValue,[ ]);//工具条
        //请求参数
        var tablesGridJson_D2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D2,
           height: (fullh-146)/2,
            read:{"query":"query_BR_DATAQC_TASK_MX_A_S_NGS_view","objects":[""]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="TQGS_ZP"){
                        setJsonParam(cols[i],"template",getTemplate("#= TQGS_ZP #","funcExce(\'"+pathValue+"openViewTqMxResult_1\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_D2 = initKendoGrid("#tablesGrid_D2_"+pathValue,tablesGridJson_D2);//初始化表格的方法
        
   }
   
   var searchGrid_D1 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: (fullh-146)/2,
	            read:{"query":"query_BR_DATAQC_TASK_MX_A_S_NGS_view","objects":[tablesGrid_M1_selectRowID] },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="TQGS_ZP"){
	                        setJsonParam(cols[i],"template",getTemplate("#= TQGS_ZP #","funcExce(\'"+pathValue+"openViewTqMxResult_1\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid_D1_"+pathValue,tablesGridJson);
   }
   
   var searchGrid_D2 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: (fullh-146)/2,
	            read:{"query":"query_BR_DATAQC_TASK_MX_A_S_NGS_view","objects":[tablesGrid_M2_selectRowID] },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="TQGS_ZP"){
	                        setJsonParam(cols[i],"template",getTemplate("#= TQGS_ZP #","funcExce(\'"+pathValue+"openViewTqMxResult_1\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid_D2_"+pathValue,tablesGridJson);
   }

    var addOpen_M=function(){
        var winOpts={
            url:"berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSManage/addM/add",
            title:"新增: NGS售后任务单.."
        };
        openWindow(winOpts);
    }

    var open_M=function(id){
        openWindow({
            url:"berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSManage/addM/add",
            title:"编辑: NGS售后任务单.."
        },{"ID":id});
    }
     
     var callBack=function(){
    	 refreshGrid_M1();
    	 refreshGrid_M2();
     };

     var refreshGrid_M1=function(){
        if(tablesGrid_M1){
            tablesGrid_M1.dataSource.read();//重新读取--刷新
            tablesGrid_M1_selectRowID="";
            searchGrid_D1();
        }
     }

     var refreshGrid_M2=function(){
        if(tablesGrid_M2){
            tablesGrid_M2.dataSource.read();//重新读取--刷新
            tablesGrid_M2_selectRowID="";
            searchGrid_D2();
        }
     }

     var editInfo_M=function(){
        var arrIds=getSelectData(tablesGrid_M1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }

        var winOpts={
            url:"berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSManage/addM/add",
            title:"修改: NGS售后任务单.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }

     var submit_M=function(){
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的任务单吗?", "warn", function() {
	         var params={"ids":arrIds};
	         var url="berry/cfzk/dataqcTask/dataqcAfterSaleNGS/submitFinish";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
 		            	if (result["msg"]) {
 		            		alertMsg(result["msg"]);
 		            		return;
 		            	}
	 	            	refreshGrid_M1();
	 	            	refreshGrid_M2();
	 	                alertMsg("提示:操作成功!","success");
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
         });
     }
     
     var openAddMX = function() {
         var arrIds = getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
    		 alertMsg("选择一行任务主单再添加明细!","error");
    		 return;
    	 }
         var rData = getGridSelectData(tablesGrid_M1);
    	 var winOpts={
    	    url:"berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSManage/addMX/addMX",
    	    title:"NGS售后任务单: 添加明细.."
    	 };
    	 var dialog = openWindow(winOpts,{ "TASK_ID": tablesGrid_M1_selectRowID, "pPathValue":pathValue });//传递id
     }
     var openEditMXBatch = function() {
         var arrIdsM=getSelectData(tablesGrid_M1);
         if(arrIdsM.length==0){
    		 alertMsg("选择一行任务主单!","error");
    		 return;
    	 }
         var arrIds=getSelectData(tablesGrid_D1);
         if(arrIds.length==0){
    		 alertMsg("至少选择一行任务明细数据!","error");
    		 return;
    	 }
    	 var winOpts={
    	    url:"berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSManage/editMXBatch/editMXBatch",
    	    title:"批量修改: NGS售后任务单明细.."
    	 };
    	 var dialog = openWindow(winOpts,{ "TASK_ID": tablesGrid_M1_selectRowID, "ids":arrIds, "pPathValue":pathValue });//传递id
     }
     var openEditMX = function() {
         var arrIdsM=getSelectData(tablesGrid_M1);
         if(arrIdsM.length==0){
    		 alertMsg("选择一行任务主单!","error");
    		 return;
    	 }
         var arrIds=getSelectData(tablesGrid_D1);
         if(arrIds.length==0){
    		 alertMsg("至少选择一行任务明细数据!","error");
    		 return;
    	 }
    	 var winOpts={
    		url:"berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSManage/editMX/editMX",
    	    title:"修改: NGS售后任务单明细.."
    	 };
    	 var dialog = openWindow(winOpts,{ "TASK_ID": tablesGrid_M1_selectRowID, "ID":arrIds[0], "pPathValue":pathValue });//传递id
     }
     var deleteInfoMX = function() {
         var arrIdsM=getSelectData(tablesGrid_M1);
         if(arrIdsM.length==0){
    		 alertMsg("选择一行任务主单!","error");
    		 return;
    	 }
         var arrIds=getSelectData(tablesGrid_D1);
         if(arrIds.length==0){
    		 alertMsg("至少选择一行任务明细数据!","error");
    		 return;
    	 }
         confirmMsg("确认", "确定要移除选中的明细吗?", "warn", function() {
 	        var url= "berry/cfzk/dataqcTask/dataqcAfterSaleNGS/delMX";
 	        var params = { "TASK_ID":tablesGrid_M1_selectRowID, "ids":arrIds };
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	if (result["msg"]) {
 		            		alertMsg(result["msg"]);
 		            		return;
 		            	}
 			            //提交成功
 		                alertMsg("删除成功","success",function(){
 		                	searchGrid_D1();
 		                });
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
     }
     var deleteInfo_M = function() {
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
    		 alertMsg("选择一行任务主单!","error");
    		 return;
    	 }
         confirmMsg("确认", "确定要删除选中的任务单吗?", "warn", function() {
 	        var url= "berry/cfzk/dataqcTask/dataqcAfterSaleNGS/delM";
 	        var params = { "ids":arrIds };
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	if (result["msg"]) {
 		            		alertMsg(result["msg"]);
 		            		return;
 		            	}
 			            //提交成功
 		                alertMsg("删除成功","success",function(){
 		                   refreshGrid_M1();
 		                });
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open_M":open_M,//打开新窗口方法-此方法非必须-自定义
         "addOpen_M":addOpen_M,//打开添加表单
         "editInfo_M":editInfo_M,
         "submit_M":submit_M,
         "refreshGrid_M1":refreshGrid_M1,
         "searchGrid_D1":searchGrid_D1,
         "refreshGrid_M2":refreshGrid_M2,
         "searchGrid_D2":searchGrid_D2,
         "callBack":callBack,//回调方法
         "openAddMX":openAddMX,
         "openEditMX":openEditMX,
         "openEditMXBatch":openEditMXBatch,
         "deleteInfoMX":deleteInfoMX,
         "deleteInfo_M":deleteInfo_M,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
