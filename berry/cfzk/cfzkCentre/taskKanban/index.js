$(document).ready(function() {
   var pathValue="berry-cfzk-cfzkCentre-taskKanban-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   // 获取项目看板 - 项目分派情况统计
	   getProjectFP();
	   
	   // 获取项目看板 - 提取任务统计
	   getTqTask();
	   
	   // 获取项目看板 - 核酸检测统计
	   getDNA_RNA_QC("DNA");
	   getDNA_RNA_QC("RNA");
	   
	   // 获取项目看板 - 项目情况统计
	   getProject();
	   
	   // 获取项目看板 - 生信分析情况统计
	   getSxfx();
	   
	   // 获取项目看板 - 信息分析采集情况统计
	   getSxcj();
	   
	   // 获取项目看板 - 费用结算管理统计
	   getCostSettleManage();
   }
   
   // 获取项目看板 - 项目分派情况统计
   var getProjectFP = function() {
   	var funcName = "项目分派情况统计信息";
		var params={ };
       var url="berry/project/project/projectKanban/projectFP";
       $.fn.ajaxPost({
           ajaxUrl: url,
           ajaxType: "post",
           ajaxData: params,
           succeed:function(result){
	            if(result["code"]>0){
	            	$("#projectKanban_"+pathValue+" #PROJECT_FP_C1").html(result.C1);//待分派
	            	$("#projectKanban_"+pathValue+" #PROJECT_FP_C2").html(result.C2);//已分派
	            }else{
	                alertMsg("提示:"+funcName+"失败!","error");
	            }
           },
           failed:function(result){
               alertMsg("提示:"+funcName+"异常!","error");
           }
       });
   }
   
    // 获取项目看板 - 提取任务统计
    var getTqTask = function() {
    	var funcName = "提取任务统计信息";
		var params={ };
        var url="berry/project/project/projectKanban/tqTask";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#projectKanban_"+pathValue+" #tqTask_C1").html(result.C1);//待下达
	            	$("#projectKanban_"+pathValue+" #tqTask_C2").html(result.C2);//已下达未结束
	            	$("#projectKanban_"+pathValue+" #tqTask_C3").html(result.C3);//已结束
	            }else{
	                alertMsg("提示:"+funcName+"失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:"+funcName+"异常!","error");
            }
        });
    }
    var openTqTaskAdd = function() {
	   	 var winOpts={
	   	    url:"berry/project/project/projectKanban/tqTaskOrder/addSample",
	   	    title:"提取任务单: 待下达样本列表.."
	   	 };
	   	 var dialog = openWindow(winOpts,{ "pPathValue":pathValue });
    }
   
    // 获取项目看板 - 核酸检测统计
    var getDNA_RNA_QC = function(QC_TYPE) {
    	var funcName = "核酸检测报告发送统计信息";
		var params={ "QC_TYPE":QC_TYPE };
        var url="berry/project/project/projectKanban/dnaRnaQC";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#projectKanban_"+pathValue+" #"+QC_TYPE+"_QC_C1").html(result.C1);//待下达
	            	$("#projectKanban_"+pathValue+" #"+QC_TYPE+"_QC_C2").html(result.C2);//已下达未结束
	            	$("#projectKanban_"+pathValue+" #"+QC_TYPE+"_QC_C3").html(result.C3);//已结束
	            }else{
	                alertMsg("提示:"+funcName+"失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:"+funcName+"异常!","error");
            }
        });
    }
   
    // 获取项目看板 - 项目情况统计
    var getProject = function() {
    	var funcName = "项目情况统计信息";
		var params={ };
        var url="berry/project/project/projectKanban/project";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#projectKanban_"+pathValue+" #PROJECT_C0").html(result.C0);//待立项
	            	$("#projectKanban_"+pathValue+" #PROJECT_C1").html(result.C1);//新立项
	            	$("#projectKanban_"+pathValue+" #PROJECT_C2").html(result.C2);//待审核
	            	$("#projectKanban_"+pathValue+" #PROJECT_C3").html(result.C3);//审核通过
	            	$("#projectKanban_"+pathValue+" #PROJECT_C4").html(result.C4);//审核不通过
	            	$("#projectKanban_"+pathValue+" #PROJECT_C5").html(result.C5);//在线项目
	            	$("#projectKanban_"+pathValue+" #PROJECT_C6").html(result.C6);//关闭项目
	            	$("#projectKanban_"+pathValue+" #PROJECT_C7").html(result.C7);//暂未立
	            }else{
	                alertMsg("提示:"+funcName+"失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:"+funcName+"异常!","error");
            }
        });
    }
    var openProjectCreate = function() {
	   	 var winOpts={
//	   	    url:"berry/project/project/projectKanban/projectCreate/selectMX",
//	 	   	title:"项目立项: 新增.."
	   	    url:"berry/project/project/projectKanban/projectCreate/selectGroup",
	   	    title:"项目立项: 待立项.."
	   	 };
	   	 var dialog = openWindow(winOpts,{ "pPathValue":pathValue });
   }
   
    // 获取项目看板 - 生信分析情况统计
    var getSxfx = function() {
    	var funcName = "生信分析情况统计信息";
		var params={ };
        var url="berry/project/project/projectKanban/sx";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#projectKanban_"+pathValue+" #SX_C0").html(result.C0);
	            	$("#projectKanban_"+pathValue+" #SX_C1").html(result.C1);
	            	$("#projectKanban_"+pathValue+" #SX_C2").html(result.C2);
	            	$("#projectKanban_"+pathValue+" #SX_C3").html(result.C3);
	            	$("#projectKanban_"+pathValue+" #SX_C4").html(result.C4);
	            	$("#projectKanban_"+pathValue+" #SX_C5").html(result.C5);
	            	$("#projectKanban_"+pathValue+" #SX_C6").html(result.C6);
	            	$("#projectKanban_"+pathValue+" #SX_C7").html(result.C7);
	            }else{
	                alertMsg("提示:"+funcName+"失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:"+funcName+"异常!","error");
            }
        });
    }
   
    // 获取项目看板 - 信息分析采集情况统计
    var getSxcj = function() {
    	var funcName = "信息分析采集情况统计信息";
		var params={ };
        var url="berry/project/project/projectKanban/sxcj";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#projectKanban_"+pathValue+" #SX_CJ_C1").html(result.C1);
	            	$("#projectKanban_"+pathValue+" #SX_CJ_C2").html(result.C2);
	            	$("#projectKanban_"+pathValue+" #SX_CJ_C3").html(result.C3);
	            }else{
	                alertMsg("提示:"+funcName+"失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:"+funcName+"异常!","error");
            }
        });
    }
   
    // 获取项目看板 - 费用结算管理统计
    var getCostSettleManage = function() {
    	var funcName = "获取费用结算管理统计信息";
		var params={ };
        var url="berry/project/project/projectKanban/costSettleManage";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
//	            	$("#projectKanban_"+pathValue+" #costSettleManage_C0").html(result.C0);//草稿
	            	$("#projectKanban_"+pathValue+" #costSettleManage_C1").html(result.C1);//待确认
	            	$("#projectKanban_"+pathValue+" #costSettleManage_C2").html(result.C2);//待提报
	            	$("#projectKanban_"+pathValue+" #costSettleManage_C3").html(result.C3);//待结算
	            	$("#projectKanban_"+pathValue+" #costSettleManage_C4").html(result.C4);//已结算
	            }else{
	                alertMsg("提示:"+funcName+"失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:"+funcName+"异常!","error");
            }
        });
    }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
      
         "getProjectFP":getProjectFP,// 获取项目看板 - 项目分派情况统计
         
         "getTqTask":getTqTask,// 获取项目看板 - 提取任务统计
         "openTqTaskAdd":openTqTaskAdd,// 获取项目看板 - 提取任务 - add
         
         "getDNA_RNA_QC":getDNA_RNA_QC,// 获取项目看板 - 核酸检测统计
      
         "getProject":getProject,// 获取项目看板 - 项目情况统计
         "openProjectCreate":openProjectCreate,// 获取项目看板 - 项目情况统计 - create
         
         "getSxfx":getSxfx,// 获取项目看板 - 生信分析情况统计
         "getSxcj":getSxcj,// 获取项目看板 - 信息分析采集
         
         "getCostSettleManage":getCostSettleManage,// 获取项目看板 - 费用结算管理统计
         
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
