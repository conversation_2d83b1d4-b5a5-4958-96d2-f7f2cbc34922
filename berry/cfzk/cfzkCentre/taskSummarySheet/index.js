$(document).ready(function() {
   var pathValue="berry-cfzk-cfzkCentre-taskSummarySheet-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tables_1_Grid;
   var tables_2_Grid;
   var tables_3_Grid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar_1_Grid = getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tables_1_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_1_Grid,
           height: fullh-122,
            read:{"query":"query_BR_DATAQC_TASK_LIST_view","objects":[
            	[
            		"NGS重拆", "TGS重拆"
	            	, "NGS数据评估", "TGS数据评估"
	            	, "NGS数据上传", "TGS数据上传"
	            	, "NGS数据合并", "TGS数据合并"
	            	, "NGS数据售后", "TGS数据售后"
	            	, "开发维护", "文档编写"
            	],
            	[ "待审核" ]
            ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="TASK_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= TASK_NO #","funcExce(\'"+pathValue+"openView1\',\'#= ID #\',\'#= TASK_TYPE #\');","txt"));
                    }
                }
            }
        };
        tables_1_Grid = initKendoGrid("#tables_1_Grid"+pathValue,tables_1_GridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar_2_Grid = getButtonTemplates(pathValue,[
            {name:"assign",target:"assignBatch", title:"批量指派"},
        ]);//工具条
        //请求参数
        var tables_2_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_2_Grid,
           height: fullh-122,
            read:{"query":"query_BR_DATAQC_TASK_LIST_view","objects":[
            	[
            		"NGS重拆", "TGS重拆"
	            	, "NGS数据评估", "TGS数据评估"
	            	, "NGS数据上传", "TGS数据上传"
	            	, "NGS数据合并", "TGS数据合并"
	            	, "NGS数据售后", "TGS数据售后"
	            	, "开发维护", "文档编写"
            	],
            	[ "进行中" ]
            ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="TASK_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= TASK_NO #","funcExce(\'"+pathValue+"openView2\',\'#= ID #\',\'#= TASK_TYPE #\');","txt"));
                    }
                }
            }
        };
        tables_2_Grid = initKendoGrid("#tables_2_Grid"+pathValue,tables_2_GridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar_3_Grid = getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tables_3_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_3_Grid,
           height: fullh-122,
            read:{"query":"query_BR_DATAQC_TASK_LIST_view","objects":[
            	[
            		"NGS重拆", "TGS重拆"
	            	, "NGS数据评估", "TGS数据评估"
	            	, "NGS数据上传", "TGS数据上传"
	            	, "NGS数据合并", "TGS数据合并"
	            	, "NGS数据售后", "TGS数据售后"
	            	, "开发维护", "文档编写"
            	],
            	[ "已完成", "已提交方案" ]
            ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="TASK_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= TASK_NO #","funcExce(\'"+pathValue+"openView3\',\'#= ID #\',\'#= TASK_TYPE #\');","txt"));
                    }
                }
            }
        };
        tables_3_Grid = initKendoGrid("#tables_3_Grid"+pathValue,tables_3_GridJson);//初始化表格的方法
   }
     
     var callBack=function(){
    	 refresh_1_Grid();
    	 refresh_2_Grid();
    	 refresh_3_Grid();
     };
     var refresh_1_Grid=function(){
        if(tables_1_Grid){
            tables_1_Grid.dataSource.read();//重新读取--刷新
        }
     }
     var refresh_2_Grid=function(){
         if(tables_2_Grid){
             tables_2_Grid.dataSource.read();//重新读取--刷新
         }
      }
     var refresh_3_Grid=function(){
         if(tables_3_Grid){
             tables_3_Grid.dataSource.read();//重新读取--刷新
         }
      }
     
     var openView1 = function(ID, TASK_TYPE) {
    	 if (TASK_TYPE == "NGS重拆") {
    		 linkTo("/berry/cfzk/dataqcAgain/dataqcAgainNGSApprove/index","NGS重拆任务审核","null");
    	 } else if (TASK_TYPE == "TGS重拆") {
    		 linkTo("/berry/cfzk/dataqcAgain/dataqcAgainTGSApprove/index","TGS重拆任务审核","null");
    	 } else if (TASK_TYPE == "NGS数据评估") {
    		 linkTo("/berry/cfzk/dataqcAssess/dataqcAssessNGSApprove/index","NGS数据评估任务审核","null");
    	 } else if (TASK_TYPE == "TGS数据评估") {
    		 linkTo("/berry/cfzk/dataqcAssess/dataqcAssessTGSApprove/index","TGS数据评估任务审核","null");
    	 } else if (TASK_TYPE == "NGS数据上传") {
    		 linkTo("/berry/cfzk/dataqcUpload/dataqcUploadNGSApprove/index","NGS数据上传任务审核","null");
    	 } else if (TASK_TYPE == "TGS数据上传") {
    		 linkTo("/berry/cfzk/dataqcUpload/dataqcUploadTGSApprove/index","TGS数据上传任务审核","null");
    	 } else if (TASK_TYPE == "NGS数据合并") {
    		 linkTo("/berry/cfzk/dataqcMerge/dataqcMergeNGSApprove/index","NGS数据合并任务审核","null");
    	 } else if (TASK_TYPE == "TGS数据合并") {
    		 linkTo("/berry/cfzk/dataqcMerge/dataqcMergeTGSApprove/index","TGS数据合并任务审核","null");
    	 } else if (TASK_TYPE == "NGS数据售后") {
    		 linkTo("/berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSApprove/index","NGS数据售后任务审核","null");
    	 } else if (TASK_TYPE == "TGS数据售后") {
    		 linkTo("/berry/cfzk/dataqcAfterSale/dataqcAfterSaleTGSApprove/index","TGS数据售后任务审核","null");
    	 } else if (TASK_TYPE == "开发维护") {
    		 linkTo("/berry/cfzk/dataqcDev/dataqcDevApprove/index","开发维护任务审核","null");
    	 } else if (TASK_TYPE == "文档编写") {
    		 linkTo("/berry/cfzk/dataqcDoc/dataqcDocApprove/index","文档编写任务审核","null");
    	 } else {
    		 alertMsg("任务类型错误", "error");
    	 }
     }
     var openView2 = function(ID, TASK_TYPE) {
    	 if (TASK_TYPE == "NGS重拆") {
    		 linkTo("/berry/cfzk/dataqcAgain/dataqcAgainNGSManage/index","NGS重拆任务管理","null");
    	 } else if (TASK_TYPE == "TGS重拆") {
    		 linkTo("/berry/cfzk/dataqcAgain/dataqcAgainTGSManage/index","TGS重拆任务管理","null");
    	 } else if (TASK_TYPE == "NGS数据评估") {
    		 linkTo("/berry/cfzk/dataqcAssess/dataqcAssessNGSManage/index","NGS数据评估任务管理","null");
    	 } else if (TASK_TYPE == "TGS数据评估") {
    		 linkTo("/berry/cfzk/dataqcAssess/dataqcAssessTGSManage/index","TGS数据评估任务管理","null");
    	 } else if (TASK_TYPE == "NGS数据上传") {
    		 linkTo("/berry/cfzk/dataqcUpload/dataqcUploadNGSManage/index","NGS数据上传任务管理","null");
    	 } else if (TASK_TYPE == "TGS数据上传") {
    		 linkTo("/berry/cfzk/dataqcUpload/dataqcUploadTGSManage/index","TGS数据上传任务管理","null");
    	 } else if (TASK_TYPE == "NGS数据合并") {
    		 linkTo("/berry/cfzk/dataqcMerge/dataqcMergeNGSManage/index","NGS数据合并任务管理","null");
    	 } else if (TASK_TYPE == "TGS数据合并") {
    		 linkTo("/berry/cfzk/dataqcMerge/dataqcMergeTGSManage/index","TGS数据合并任务管理","null");
    	 } else if (TASK_TYPE == "NGS数据售后") {
    		 linkTo("/berry/cfzk/dataqcAfterSale/dataqcAfterSaleNGSManage/index","NGS数据售后任务管理","null");
    	 } else if (TASK_TYPE == "TGS数据售后") {
    		 linkTo("/berry/cfzk/dataqcAfterSale/dataqcAfterSaleTGSManage/index","TGS数据售后任务管理","null");
    	 } else if (TASK_TYPE == "开发维护") {
    		 linkTo("/berry/cfzk/dataqcDev/dataqcDevManage/index","开发维护任务管理","null");
    	 } else if (TASK_TYPE == "文档编写") {
    		 linkTo("/berry/cfzk/dataqcDoc/dataqcDocManage/index","文档编写任务管理","null");
    	 } else {
    		 alertMsg("任务类型错误", "error");
    	 }
     }
     var openView3 = function(ID, TASK_TYPE) {
    	 openView2(ID, TASK_TYPE);
     }

     var assignBatch=function(){
        var arrIds=getSelectData(tables_2_Grid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据!");
            return ;
        }
        var winOpts={
            url:"berry/cfzk/cfzkCentre/taskSummarySheet/assign/assignBatch",
            title:"批量指派.."
        };
        var dialog = openWindow(winOpts,{ "ids":arrIds, "pPathValue":pathValue });
     }
     
     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refresh_1_Grid":refresh_1_Grid,
         "refresh_2_Grid":refresh_2_Grid,
         "refresh_3_Grid":refresh_3_Grid,
         "openView1":openView1,
         "openView2":openView2,
         "openView3":openView3,
         "assignBatch":assignBatch,
         "callBack":callBack,//回调方法
     });
});
