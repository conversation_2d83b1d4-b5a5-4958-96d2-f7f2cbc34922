$(document).ready(function() {
   var pathValue="berry-cfzk-dataqc-dataqcNGS-sampleInfo-infoList";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_NGS_DATAQC_view_SAMPLE","objects":[params.NGS_FLOWCELL]},
            headerFilter:function(cols,i){
                if(i){
                	// INDEX ID
                    if(cols[i]["field"]&&cols[i]["field"]=="NGS_FLWCELL_INDEX"){
                        setJsonParam(cols[i],"template",getTemplate("#= NGS_FLWCELL_INDEX #","funcExce(\'"+pathValue+"open4\',\'#= NGS_FLOWCELL #\');","txt"));
                    }
                	// 项目编号-期数
                    if(cols[i]["field"]&&cols[i]["field"]=="PROJECT_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= PROJECT_NO #","funcExce(\'"+pathValue+"open5\',\'#= NGS_FLOWCELL #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   // open4 LANE信息
   var open4=function(NGS_FLOWCELL){
       openWindow({
           url:"berry/cfzk/dataqc/dataqcNGS/laneInfo/infoList",
           title:"LANE信息.."
       },{"NGS_FLOWCELL":NGS_FLOWCELL});
   }
   // open5 项目信息
   var open5=function(NGS_FLOWCELL){
       openWindow({
           url:"berry/cfzk/dataqc/dataqcNGS/projectInfo/infoList",
           title:"项目信息.."
       },{"NGS_FLOWCELL":NGS_FLOWCELL});
   }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open4":open4,//LANE信息
         "open5":open5,//项目信息
     });
});
