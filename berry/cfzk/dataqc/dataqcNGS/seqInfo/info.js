$(document).ready(function() {
    var pathValue="berry-cfzk-dataqc-dataqcNGS-seqInfo-info";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_MODUAL_NGS_DATAQC"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
    });
 
 });
 