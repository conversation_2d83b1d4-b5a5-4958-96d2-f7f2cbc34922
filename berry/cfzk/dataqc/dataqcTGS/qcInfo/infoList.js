$(document).ready(function() {
   var pathValue="berry-cfzk-dataqc-dataqcTGS-qcInfo-infoList";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {
           tableName:"BR_MODUAL_TGS_DATAQC"
       };
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){

       getInfo("form",pathValue,params);
       // 传入数组ids
       var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
       getInfo("form",pathValue,params,url);//传入id
       
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_TGS_DATAQC_view_QC","objects":[params.TGS_EXP_ID]}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
     });
});
