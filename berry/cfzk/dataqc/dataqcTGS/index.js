$(document).ready(function() {
   var pathValue="berry-cfzk-dataqc-dataqcTGS-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
    		{name:"submit",target:"submit", title:"提交方案"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-122,
            read:{"query":"query_BR_MODUAL_TGS_DATAQC_view","objects":[ "未提交" ]},
            headerFilter:function(cols,i){
                if(i){
                	// wellid
                    if(cols[i]["field"]&&cols[i]["field"]=="TGS_WELL_ID"){
                        setJsonParam(cols[i],"template",getTemplate("#= TGS_WELL_ID #","funcExce(\'"+pathValue+"open2\',\'#= ID #\',\'#= TGS_EXP_ID #\');","txt"));
                    }
                	// 测序状态
                    if(cols[i]["field"]&&cols[i]["field"]=="TGS_SEQ_FLAG"){
                        setJsonParam(cols[i],"template",getTemplate("#= TGS_SEQ_FLAG #","funcExce(\'"+pathValue+"open2\',\'#= ID #\',\'#= TGS_EXP_ID #\');","txt"));
                    }
                	// 质控状态
                    if(cols[i]["field"]&&cols[i]["field"]=="TGS_QC_FLAG"){
                        setJsonParam(cols[i],"template",getTemplate("#= TGS_QC_FLAG #","funcExce(\'"+pathValue+"open6\',\'#= ID #\',\'#= TGS_EXP_ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        
        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
    		{name:"revoke",target:"revoke", title:"撤销方案"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-122,
            read:{"query":"query_BR_MODUAL_TGS_DATAQC_view","objects":[ "已提交" ]},
            headerFilter:function(cols,i){
                if(i){
                	// wellid
                    if(cols[i]["field"]&&cols[i]["field"]=="TGS_WELL_ID"){
                        setJsonParam(cols[i],"template",getTemplate("#= TGS_WELL_ID #","funcExce(\'"+pathValue+"open2\',\'#= ID #\',\'#= TGS_EXP_ID #\');","txt"));
                    }
                	// 测序状态
                    if(cols[i]["field"]&&cols[i]["field"]=="TGS_SEQ_FLAG"){
                        setJsonParam(cols[i],"template",getTemplate("#= TGS_SEQ_FLAG #","funcExce(\'"+pathValue+"open2\',\'#= ID #\',\'#= TGS_EXP_ID #\');","txt"));
                    }
                	// 质控状态
                    if(cols[i]["field"]&&cols[i]["field"]=="TGS_QC_FLAG"){
                        setJsonParam(cols[i],"template",getTemplate("#= TGS_QC_FLAG #","funcExce(\'"+pathValue+"open6\',\'#= ID #\',\'#= TGS_EXP_ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   var callBack=function(){
       if(tablesGrid){
           tablesGrid.dataSource.read();//重新读取
       }
    };
    var refreshGrid=function(){
       if(tablesGrid){
           tablesGrid.dataSource.read();//重新读取--刷新
       }
    }
    var refreshGrid2=function(){
       if(tablesGrid2){
           tablesGrid2.dataSource.read();//重新读取--刷新
       }
    }

     var submit=function(){
         var rData=getGridSelectData(tablesGrid);
         if(rData.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
        	var TGS_EXP_ID_LIST = [];
        	for (var i=0;i<rData.length; i++) {
        		TGS_EXP_ID_LIST[i] = rData[i].TGS_EXP_ID;
        	}
 	        var params={"TGS_EXP_ID_LIST":TGS_EXP_ID_LIST};
 	        var url="berry/prod/run/downTGSQC/commit";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	refreshGrid();
 		            	refreshGrid2();
 		                alertMsg("提示:操作成功!","success");
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
      }
     var revoke=function(){
         var rData=getGridSelectData(tablesGrid);
         if(rData.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要撤销选中的数据吗?", "warn", function() {
        	var TGS_EXP_ID_LIST = [];
        	for (var i=0;i<rData.length; i++) {
        		TGS_EXP_ID_LIST[i] = rData[i].TGS_EXP_ID;
        	}
 	        var params={"TGS_EXP_ID_LIST":TGS_EXP_ID_LIST};
 	        var url="berry/prod/run/downTGSQC/revoke";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	refreshGrid();
 		            	refreshGrid2();
 		                alertMsg("提示:操作成功!","success");
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
      }
     
     // open2 测序信息
     var open2=function(id, TGS_EXP_ID){
         openWindow({
             url:"berry/cfzk/dataqc/dataqcTGS/seqInfo/infoList",
             title:"测序信息.."
         },{"ID":id, "TGS_EXP_ID":TGS_EXP_ID});
     }
     // open6 质控信息
     var open6=function(id, TGS_EXP_ID){
         openWindow({
             url:"berry/cfzk/dataqc/dataqcTGS/qcInfo/infoList",
             title:"质控信息.."
         },{"ID":id, "TGS_EXP_ID":TGS_EXP_ID});
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refreshGrid":refreshGrid,
         "refreshGrid2":refreshGrid2,
         "callBack":callBack,//回调方法
         "submit":submit,
         "revoke":revoke,
         "open2":open2,//测序信息
         "open6":open6,//质控信息
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
