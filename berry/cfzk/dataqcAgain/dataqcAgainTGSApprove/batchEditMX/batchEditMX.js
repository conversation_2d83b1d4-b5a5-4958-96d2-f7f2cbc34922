$(document).ready(function() {
    var pathValue="berry-cfzk-dataqcAgain-dataqcAgainTGSApprove-batchEditMX-batchEditMX";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return { };
    }
    
    var M_ID;
    var ids;
    var pPathValue;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	M_ID = params.M_ID;
        ids = params.ids;
    	pPathValue = params.pPathValue;
    	
    	if ( !M_ID || !ids || !pPathValue ) {
    		alertMsg("表单调用参数错误","wran");
            return false;
    	}
    }

    var submit=function(){
    	
    	if ( !M_ID || !ids || !pPathValue ) {
    		alertMsg("表单调用参数错误","wran");
            return false;
    	}
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","wran");
            return false;
        }
    	
    	var form_params = getJsonByForm("form",pathValue);
    	form_params.M_ID = M_ID;
    	form_params.ids = ids;
    	
        var url="berry/cfzk/dataqcTask/dataqcAgainTGS/editMxBatch";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: form_params,
            succeed:function(result){
	            if(result["code"]>0){
		            if (result["msg"]) {
		            	alertMsg(result["msg"]);
		            	return;
		            }
	            	alertMsg("提交成功","success",function(){
                        funcExce(pPathValue+"searchGrid_D1");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 