$(document).ready(function() {
   var pathValue="berry-cfzk-dataqcAgain-dataqcAgainTGSApprove-addMX-addMX";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   
   var M_ID = "";
   var PROJECT_ID = "";
   var pPathValue = "";

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
	   M_ID = params.M_ID;
	   PROJECT_ID = params.PROJECT_ID;
	   pPathValue = params.pPathValue;
   	
	   	if ( !M_ID || !pPathValue ) {
	   		alertMsg("功能调用参数错误","wran");
	        return false;
	   	}
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddMX",title:"确认添加"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-196,
            read:{"query":"query_BR_DATAQC_TASK_MX_AGAIN_TGS_view_addSelect","objects":[ PROJECT_ID ]}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var refreshGrid=function(){
       if(tablesGrid){
    	   tablesGrid.dataSource.read();//重新读取--刷新
       }
    }

     var confirmAddMX=function(){
    	 if ( !M_ID || !pPathValue ) {
 	   		alertMsg("功能调用参数错误","wran");
 	        return false;
 	   	}
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据!");
            return ;
        }
        var url= "berry/cfzk/dataqcTask/dataqcAgainTGS/addMX";
        var params = { "M_ID":M_ID , "ids":arrIds };
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
 		            if (result["msg"]) {
 		            	alertMsg(result["msg"]);
 		            	return;
 		            }
		            //提交成功
	                alertMsg("提交成功","success",function(){
	                   funcExce(pPathValue+"searchGrid_D1");//执行刷新主页明细表
	                   funcExce(pathValue+"close");//关闭页面
	                });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
        
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refreshGrid":refreshGrid,
         "confirmAddMX":confirmAddMX,
     });
});
