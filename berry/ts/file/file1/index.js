
var berry_ts_file_file1_index_id = "";
var berry_ts_file_file1_index_code = "";
var berry_ts_file_file1_index_name = "";
var berry_ts_file_file1_index_pCode = "";
var berry_ts_file_file1_index_gg = "";
var berry_ts_file_file1_index_pathValue = "berry-ts-file-file1-index";

$(document).ready(function() {
   var pathValue = berry_ts_file_file1_index_pathValue;
   
   intiBrContainerIndex();
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {
    	   tableName:"BR_DOC_FILE"
       };
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
	   var toolbar=getButtonTemplates(pathValue,[
           {name:"add",target:"thisUploadFileFunc",title:"上传"},
           {name:"cancel",target:"cancelFile",title:"作废"},
           {name:"delete",target:"deleteFile",title:"删除"},
       ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-102,
            read:{"query":"query_BR_DOC_CATALOG_view","objects":['']},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= NODE_TYPE #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }

   /** 新增同级目录 */
   var addOpen=function(){
    	if (!berry_ts_file_file1_index_id) {
    		alertMsg("请先选择树型结构节点，再进行添加同级操作");
    	}
        openWindow({
            url:"berry/ts/file/file1/adddirectory/add",
            title:"新增目录.."
        },{
        	"P_CODE": berry_ts_file_file1_index_pCode
        });
    }
    /** 新增子级目录 */
    var addOpen1=function(){
    	if (!berry_ts_file_file1_index_id) {
    		alertMsg("请先选择树型结构节点，再进行添加子级操作");
    	}
        openWindow({
            url:"berry/ts/file/file1/adddirectory/add",
            title:"新增目录.."
        },{
        	"P_CODE": berry_ts_file_file1_index_id
        });
    }
    /** 删除目录 */
    var deleteInfo=function(){
    	if( ! berry_ts_file_file1_index_id ){
           alertMsg("请选择要删除节点!");
           return ;
    	}
    	alertMsg("确定删除选中的“"+berry_ts_file_file1_index_name+"”节点及其所有子节点吗？", "success", function() {
    		var url="berry/ts/file/file1/delnodes";
    		$.fn.ajaxPost({
	   		   ajaxUrl: url,
	   		   ajaxType: "post",
	   		   ajaxData: { id: berry_ts_file_file1_index_id },
	   		   succeed: function(rs) {
	   			   berry_ts_file_file1_index_id="";
	   			   intiBrContainerIndexSetData();
	   			   intiBrContainerIndexGrid();
	   		   }
    		});
       });
    }
    /** 作废目录 */
    var cancelCatalog=function(){
    	if( ! berry_ts_file_file1_index_id ){
            alertMsg("请选择要作废节点!");
            return ;
     	}
    	alertMsg("确定作废选中的“"+berry_ts_file_file1_index_name+"”节点及其所有子节点吗？", "success", function() {
    		var url="berry/ts/file/file1/cancelCatalog";
    		$.fn.ajaxPost({
	   		   ajaxUrl: url,
	   		   ajaxType: "post",
	   		   ajaxData: { id: berry_ts_file_file1_index_id },
	   		   succeed: function(rs) {
	   			   if(rs["code"]>0){
	   				   //提交成功
	   				   alertMsg("操作成功","success",function(){
	   					   berry_ts_file_file1_index_id="";
	   					   intiBrContainerIndexSetData();
	   					   intiBrContainerIndexGrid();
	   				   });
	                }else{
	                	alertMsg("操作失败","error");
	                }
	   		   }
    		});
       });
    }
    /** 上传文件 */
    var thisUploadFileFunc=function(){
    	if (!berry_ts_file_file1_index_id) {
    		alertMsg("请先选择树型结构节点，再进行文件上传操作");
    		return;
    	}
        openWindow({
            url:"berry/ts/file/file1/addfile/add",
            title:"上传文件.."
        },{
        	"CATALOG_CODE": berry_ts_file_file1_index_id
        });
    }
    /** 删除文件 */
    var deleteFile=function(){
    	var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除!");
            return ;
        }
        var params={"tableName":initData().tableName,"ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
    }
    /** 作废文件 */
    var cancelFile=function(){
    	var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行作废!");
            return ;
        }
        var url="berry/ts/file/file1/cancelFiles";
		$.fn.ajaxPost({
   		   ajaxUrl: url,
   		   ajaxType: "post",
   		   ajaxData: { ids: arrIds },
   		   succeed: function(rs) {
   			   if(rs["code"]>0){
   				   //提交成功
   				   alertMsg("操作成功","success",function(){
   					   berry_ts_file_file1_index_id="";
   					   intiBrContainerIndexSetData();
   					   intiBrContainerIndexGrid();
   				   });
                }else{
                	alertMsg("操作失败","error");
                }
   		   }
		});
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
    	 // 回调刷新树型
    	 intiBrContainerIndexSetData();
    	 refreshGrid();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     
     var importExcel=function(){
    	 alertMsg(123);
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "addOpen":addOpen,//添加同级目录
         "addOpen1":addOpen1,//添加子级目录
         "deleteInfo":deleteInfo,//删除目录
         "cancelCatalog":cancelCatalog,//作废目录
         "thisUploadFileFunc":thisUploadFileFunc,//上传文件
         "cancelFile":cancelFile,//作废文件
         "deleteFile":deleteFile,//删除文件
         "refreshGrid":refreshGrid,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});

function intiBrContainerIndex() {
	var pathValuE = berry_ts_file_file1_index_pathValue;
	$("#treeview"+pathValuE).kendoTreeView({
	   dataTextField:"NAME",
	   select: function(e) {
		   this.expand(e.node);
		   
	     var dataItem = this.dataItem(e.node);
	     berry_ts_file_file1_index_id = dataItem.ID;
	     berry_ts_file_file1_index_code = dataItem.CODE;
	     berry_ts_file_file1_index_name = dataItem.NAME;
	     berry_ts_file_file1_index_pCode = dataItem.P_CODE ? dataItem.P_CODE : "";
	     berry_ts_file_file1_index_gg = dataItem.ORIFICE_PLATE_SIZE ? dataItem.ORIFICE_PLATE_SIZE : "";
	     
	     intiBrContainerIndexGrid();
	     intiBrContainerIndexListView();
	   }
   });

   intiBrContainerIndexSetData();
}

function intiBrContainerIndexGrid() {
    var tableId="#tablesGrid"+berry_ts_file_file1_index_pathValue;
        if ($(tableId).data("kendoGrid") != undefined) {
           //请求参数
           var tablesGridJson={
               url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
               sort: "",//排序
               toolbar: toolbar,
               read:{"query":"query_BR_DOC_CATALOG_view","objects":[ berry_ts_file_file1_index_id ]},
			headerFilter:function(cols,i){
			    if(i){
			        if(cols[i]["field"]&&cols[i]["field"]=="NAME"){
			            setJsonParam(cols[i],"template",getTemplate("#= NAME #","funcExce(\'"+berry_ts_file_file1_index_pathValue+"open\',\'#= ID #\');","txt"));
			        }
			    }
			}
           };
           setGridDataSource(tableId,tablesGridJson);
        }
}

function intiBrContainerIndexSetData() {
   var pathValuE = berry_ts_file_file1_index_pathValue;
   $.fn.ajaxPost({
	   ajaxUrl: "berry/ts/file/file1/treeview",
	   ajaxType: "post",
	   ajaxData: {},
	   succeed: function(rs) {
		   var treeDataSource = rs.treeview;
		   var treeview = $("#treeview"+pathValuE).data("kendoTreeView");
		   treeview.setDataSource(new kendo.data.HierarchicalDataSource({
			   data: treeDataSource
		   }));
		   if (berry_ts_file_file1_index_id) {
			   treeview.expandTo(berry_ts_file_file1_index_id);
		   }
	   }
   });
}

function intiBrContainerIndexListView() {
	var pathValuE = berry_ts_file_file1_index_pathValue;
	
	var ss = berry_ts_file_file1_index_gg.split("*");
	var x = Number(ss[0]);
	var y = Number(ss[1]);
	$.fn.ajaxPost({
		   ajaxUrl: "system/jdbc/query/one/table",
		   ajaxType: "post",
		   ajaxData: {"query":"query_BR_CONTAINER_ORIFICE_PLATE_listView","objects":[berry_ts_file_file1_index_code]},
		   succeed: function(rs) {
				$("#listViewID"+pathValuE).html(berry_ts_file_file1_index_code);
	            var dataSource = new kendo.data.DataSource({
	                data: rs.rows?rs.rows:[],
	                pageSize: rs.curr
	            });
	            
	            var w = 46 * x + 3 * (x - 1);
	            $("#listView"+pathValuE).css('width', w );
	            
	            $("#listView"+pathValuE).kendoListView({
	                dataSource: dataSource,
	                template: kendo.template($("#listViewTemplate"+pathValuE).html())
	            });
		   }
	   });
}
