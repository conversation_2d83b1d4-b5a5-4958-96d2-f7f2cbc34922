<style>

#PROD_TASK_KANBAN_TITLE_berry-prod-prod-prodKanban-index h1 {
	color: #656565;
	font-size: 26px;
}

#PROD_TASK_KANBAN_CONTENT_berry-prod-prod-prodKanban-index {
    padding-left:20px;
    width:100%;
    overflow-y: scroll;
}
#PROD_TASK_KANBAN_CONTENT_berry-prod-prod-prodKanban-index h1 {
	color: #656565;
	font-size: 20px;
}
#PROD_TASK_KANBAN_CONTENT_berry-prod-prod-prodKanban-index h2 {
	color: #656565;
	font-size: 16px;
}

</style>

<div id="indexWindow" class="indexWindow">
    <div class="indexSection k-content" style="background-color: #EEEEEE;">
		<div id="PROD_TASK_KANBAN_TITLE_berry-prod-prod-prodKanban-index" style="text-align: center;">
			<br/>
			<h1>生产任务中心看板</h1>
			<br/>
		</div>
		
		<div id="PROD_TASK_KANBAN_CONTENT_berry-prod-prod-prodKanban-index" class="row">
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">样本接收 - TODO</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									送样信息单
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										<span id="SAMPLE_RECEIVE_C1">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2 onclick='linkTo("/berry/project/project/projectManager/index","分派项目管理员","null");' style="cursor: pointer;">
									收样登记
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										<span id="SAMPLE_RECEIVE_C2">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2 onclick='linkTo("/berry/project/project/projectManager/index","分派项目管理员","null");' style="cursor: pointer;">
									收样审核
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										<span id="SAMPLE_RECEIVE_C3">0</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">样本入库（样本数量） - TODO</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									未入库
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										Z <span id="SAMPLE_INPUT_WRK_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										D <span id="SAMPLE_INPUT_WRK_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="SAMPLE_INPUT_WRK_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										N <span id="SAMPLE_INPUT_WRK_C4">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										T <span id="SAMPLE_INPUT_WRK_C5">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										B <span id="SAMPLE_INPUT_WRK_C6">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									新入库
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										Z <span id="SAMPLE_INPUT_YRK_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										D <span id="SAMPLE_INPUT_YRK_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="SAMPLE_INPUT_YRK_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										N <span id="SAMPLE_INPUT_YRK_C4">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										T <span id="SAMPLE_INPUT_YRK_C5">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										B <span id="SAMPLE_INPUT_YRK_C6">0</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">样本提取</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									提取任务单
									&nbsp;待审核，分派中，分派完成&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTask/index","提取任务管理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										进行中 <span id="TQ_TASK_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTask/index","提取任务管理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										已完成 <span id="TQ_TASK_C2">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									待提取
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTaskMy/index","我的提取任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										D <span id="TQ_TASK_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTaskMy/index","我的提取任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="TQ_TASK_C4">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTaskMy/index","我的提取任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										D <span id="TQ_TASK_C5">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTaskMy/index","我的提取任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="TQ_TASK_C6">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已终止
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTaskMy/index","我的提取任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										D <span id="TQ_TASK_C7">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/tq/tqTaskMy/index","我的提取任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="TQ_TASK_C8">0</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">核酸质检</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									DNA
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/qc/qcTaskDNA/index","DNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										未开始 <span id="QC_TASK_DNA_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/qc/qcTaskDNA/index","DNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										进行中 <span id="QC_TASK_DNA_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/qc/qcTaskDNA/index","DNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										报告生成中 <span id="QC_TASK_DNA_C3">TODO</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/qc/qcTaskDNA/index","DNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										报告已完成 <span id="QC_TASK_DNA_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									RNA
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/qc/qcTaskRNA/index","RNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										未开始 <span id="QC_TASK_RNA_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='/berry/prod/qc/qcTaskRNA/index","RNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										进行中 <span id="QC_TASK_RNA_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='/berry/prod/qc/qcTaskRNA/index","RNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										报告生成中 <span id="QC_TASK_RNA_C3">TODO</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='/berry/prod/qc/qcTaskRNA/index","RNA检测任务","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										报告已完成 <span id="QC_TASK_RNA_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">生产任务单</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									任务单下达
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskBus/index","生产任务下达","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="PROD_TASK_XD_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskBus/index","生产任务下达","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="PROD_TASK_XD_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskBus/index","生产任务下达","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="PROD_TASK_XD_C3">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									待审核
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskApprove/index","生产任务审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="PROD_TASK_DS_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskApprove/index","生产任务审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="PROD_TASK_DS_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskApprove/index","生产任务审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="PROD_TASK_DS_C3">0</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已审核
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskApprove/index","生产任务审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="PROD_TASK_YS_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskApprove/index","生产任务审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="PROD_TASK_YS_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/prod/prodTaskApprove/index","生产任务审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="PROD_TASK_YS_C3">0</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">样本预处理</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									未处理
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="SAMPLE_READY_WCL_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="SAMPLE_READY_WCL_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="SAMPLE_READY_WCL_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="SAMPLE_READY_WCL_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="SAMPLE_READY_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="SAMPLE_READY_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="SAMPLE_READY_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/sample/ready/index","样本预处理","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="SAMPLE_READY_YWC_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">文库构建: 预文库</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									待分派
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReadyAssign/index","预文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_Y_DFP_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReadyAssign/index","预文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_Y_DFP_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReadyAssign/index","预文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_Y_DFP_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReadyAssign/index","预文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_Y_DFP_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									构建中
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_Y_GJZ_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_Y_GJZ_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_Y_GJZ_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_Y_GJZ_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_Y_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_Y_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_Y_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libReady/index","预文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_Y_YWC_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">文库构建: 终文库</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									待分派
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZAssign/index","终文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_Z_DFP_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZAssign/index","终文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_Z_DFP_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZAssign/index","终文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_Z_DFP_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZAssign/index","终文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_Z_DFP_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									构建中
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_Z_GJZ_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_Z_GJZ_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_Z_GJZ_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_Z_GJZ_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_Z_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_Z_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_Z_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libZ/index","终文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_Z_YWC_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">文库构建: NGS文库</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									待分派
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGSAssign/index","NGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_N_DFP_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGSAssign/index","NGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_N_DFP_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGSAssign/index","NGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_N_DFP_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGSAssign/index","NGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_N_DFP_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									构建中
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_N_GJZ_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_N_GJZ_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_N_GJZ_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_N_GJZ_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_N_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_N_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_N_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libNGS/index","NGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_N_YWC_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">文库构建: TGS文库</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									待分派
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGSAssign/index","TGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_T_DFP_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGSAssign/index","TGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_T_DFP_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGSAssign/index","TGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_T_DFP_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGSAssign/index","TGS文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_T_DFP_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									构建中
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_T_GJZ_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_T_GJZ_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_T_GJZ_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_T_GJZ_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_T_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_T_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_T_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libTGS/index","TGS文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_T_YWC_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">文库构建: Bionano文库</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									待分派
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionanoAssign/index","Bionano文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_B_DFP_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionanoAssign/index","Bionano文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_B_DFP_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionanoAssign/index","Bionano文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_B_DFP_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionanoAssign/index","Bionano文库构建指派","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_B_DFP_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									构建中
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_B_GJZ_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_B_GJZ_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_B_GJZ_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_B_GJZ_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="LIB_B_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="LIB_B_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="LIB_B_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libBionano/index","Bionano文库构建","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="LIB_B_YWC_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">文库QC</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									QPCR进行中
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="QPCR_WCL_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="QPCR_WCL_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										N外来文库 <span id="QPCR_WCL_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="QPCR_WCL_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									QPCR已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="QPCR_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="QPCR_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										N外来文库 <span id="QPCR_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQPCR/index","QPCR检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="QPCR_YWC_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									QUBIT进行中
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="QUBIT_WCL_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="QUBIT_WCL_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="QUBIT_WCL_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										B文库 <span id="QUBIT_WCL_C4">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="QUBIT_WCL_C5">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									QUBIT已完成
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										TD <span id="QUBIT_YWC_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										ND <span id="QUBIT_YWC_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										R <span id="QUBIT_YWC_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										B文库 <span id="QUBIT_YWC_C4">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/libQUBIT/index","Qubit检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="QUBIT_YWC_C5">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									2100
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/lib2100/index","2100检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										进行中 <span id="2100_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/lib2100/index","2100检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										已完成 <span id="2100_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/lib2100/index","2100检测","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="2100_C3">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">POOLING</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									Capture Pooling
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingCP/index","Capture Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待Pooling <span id="POOLING_C_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingCP/index","Capture Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										制定中 <span id="POOLING_C_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingCPApprove/index","Capture Pooling方案审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待审核 <span id="POOLING_C_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingCPApprove/index","Capture Pooling方案审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										已审核 <span id="POOLING_C_C4">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingCP/index","Capture Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="POOLING_C_C5">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									NGS Pooling
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingNGS/index","NGS Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待Pooling <span id="POOLING_N_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingNGS/index","NGS Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										制定中 <span id="POOLING_N_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingNGSApprove/index","NGS Pooling方案审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待审核 <span id="POOLING_N_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingNGSApprove/index","NGS Pooling方案审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										已审核 <span id="POOLING_N_C4">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingNGS/index","NGS Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="POOLING_N_C5">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									TGS Pooling
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingTGS/index","TGS Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待Pooling <span id="POOLING_T_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingTGS/index","TGS Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										制定中 <span id="POOLING_T_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingTGSApprove/index","TGS Pooling方案审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待审核 <span id="POOLING_T_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingTGSApprove/index","TGS Pooling方案审核","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										已审核 <span id="POOLING_T_C4">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/lib/poolingTGS/index","TGS Pooling方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										加急 <span id="POOLING_T_C5">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-md-6 col-xl-6">
				<div class="card mb-3">
					<div class="card-body">
						<div class="row">
							<div class="col-2">
								<div class="d-flex h-100 display-4 justify-content-center align-items-center">
									<i class="fa fa-atlas theme-m"></i>
								</div>
							</div>
							<div class="col-10">
								<h1 class="theme-m">上机信息</h1>
							</div>
						</div>
						<div class="row">
							<div class="col-1">
							</div>
							<div class="col-11">
								<br>
								<h2>
									NGS
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upNGS/index","NGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待制定 <span id="RUN_N_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upNGS/index","NGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										制定中 <span id="RUN_N_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upNGS/index","NGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										进行中 <span id="RUN_N_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upNGS/index","NGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										已完成 <span id="RUN_N_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
								<br>
								<h2>
									TGS
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upTGS/index","TGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										待制定 <span id="RUN_T_C1">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upTGS/index","TGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										制定中 <span id="RUN_T_C2">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upTGS/index","TGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										进行中 <span id="RUN_T_C3">0</span>
										&nbsp;
									</font>
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<font onclick='linkTo("/berry/prod/run/upTGS/index","TGS上机方案","null");' class="theme-m" style="border-bottom-style:dashed; border-bottom-width:1px; cursor: pointer;">
										&nbsp;
										已完成 <span id="RUN_T_C4">TODO</span>
										&nbsp;
									</font>
								</h2>
							</div>
						</div>
					</div>
				</div>
			</div>
			
		</div>
	</div>
</div>
<div id="window" ></div>
<template id="windowTemplate"></template>