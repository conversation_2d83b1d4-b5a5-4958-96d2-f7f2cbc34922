$(document).ready(function() {
   var pathValue="berry-prod-prod-prodKanban-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   // 设置面板高度 - 自适应
   $("#PROD_TASK_KANBAN_CONTENT_"+pathValue).height(fullh - (116+46));

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
	   // 生产任务中心看板 - NGS上机 情况统计
	   getNGSRunCount();
	   // 生产任务中心看板 - TGS上机 情况统计
	   getTGSRunCount();
	   
	   // 生产任务中心看板 - Capture Pooling 情况统计
	   getCapturePoolingCount();
	   // 生产任务中心看板 - NGS Pooling 情况统计
	   getNGSPoolingCount();
	   // 生产任务中心看板 - TGS Pooling 情况统计
	   getTGSPoolingCount();
	   
	   // 生产任务中心看板 - 文库QC情况统计
	   getLibQcCount();
	   
	   // 生产任务中心看板 - 文库构建情况统计
	   getLibCount("Y");//预文库
	   getLibCount("Z");//终文库
	   getLibCount("N");//NGS文库
	   getLibCount("T");//TGS文库
	   getLibCount("B");//Bionano文库
	   
	   // 生产任务中心看板 - 样本预处理情况统计
	   getSampleReadyCount();
	   
	   // 生产任务中心看板 - 生产任务情况统计
	   getProdTaskCount();
	   
	   // 生产任务中心看板 - 核酸检测DNA情况统计
	   getQcTaskCount("DNA");
	   // 生产任务中心看板 - 核酸检测RNA情况统计
	   getQcTaskCount("RNA");
	   
	   // 生产任务中心看板 - 样本提取情况统计
	   getTqTaskCount();
   }
   var refreshFullInfo = function() {
	   var links = "/berry/prod/prod/prodKanban/index";
	   $('#tab'+replaceUrl(links)).find("small").each(function(){// 判断页签是否打开
		   var params = {};
		   init(params);
		   // 自动刷新频率：回调
		   setTimeout(refreshFullInfo, 30*1000);
       });
   }
   // 自动刷新频率
   setTimeout(refreshFullInfo, 30*1000);
   
   // 生产任务中心看板 - NGS上机 情况统计
   var getNGSRunCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_ngsRunCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_N_C1").html(result.C1);//待制定
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_N_C2").html(result.C2);//制定中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_N_C3").html(result.C3);//进行中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_N_C4").html(result.C4);//已完成
				}
			}
		});
   }
   
   // 生产任务中心看板 - TGS上机 情况统计
   var getTGSRunCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_tgsRunCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_T_C1").html(result.C1);//待制定
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_T_C2").html(result.C2);//制定中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_T_C3").html(result.C3);//进行中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #RUN_T_C4").html(result.C4);//已完成
				}
			}
		});
   }
   
   // 生产任务中心看板 - Capture Pooling 情况统计
   var getCapturePoolingCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_capturePoolingCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_C_C1").html(result.C1);//待Pooling
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_C_C2").html(result.C2);//制定中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_C_C3").html(result.C3);//待审核
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_C_C4").html(result.C4);//已审核
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_C_C5").html(result.C5);//加急
				}
			}
		});
   }
   // 生产任务中心看板 - NGS Pooling 情况统计
   var getNGSPoolingCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_ngsPoolingCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_N_C1").html(result.C1);//待Pooling
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_N_C2").html(result.C2);//制定中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_N_C3").html(result.C3);//待审核
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_N_C4").html(result.C4);//已审核
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_N_C5").html(result.C5);//加急
				}
			}
		});
   }
   // 生产任务中心看板 - TGS Pooling 情况统计
   var getTGSPoolingCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_tgsPoolingCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_T_C1").html(result.C1);//待Pooling
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_T_C2").html(result.C2);//制定中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_T_C3").html(result.C3);//待审核
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_T_C4").html(result.C4);//已审核
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #POOLING_T_C5").html(result.C5);//加急
				}
			}
		});
   }
   
   // 生产任务中心看板 - 文库QC情况统计
   var getLibQcCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_libQcCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
					// ---------------------------------------------- QPCR
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_WCL_C1").html(result.QPCR_WCL_C1);//QPCR 进行中 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_WCL_C2").html(result.QPCR_WCL_C2);//QPCR 进行中 R
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_WCL_C3").html(result.QPCR_WCL_C3);//QPCR 进行中 N外来文库
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_WCL_C4").html(result.QPCR_WCL_C4);//QPCR 进行中 加急
	            	
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_YWC_C1").html(result.QPCR_YWC_C1);//QPCR 已完成 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_YWC_C2").html(result.QPCR_YWC_C2);//QPCR 已完成 R
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_YWC_C3").html(result.QPCR_YWC_C3);//QPCR 已完成 N外来文库
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QPCR_YWC_C4").html(result.QPCR_YWC_C4);//QPCR 已完成 加急
	            	
					// ---------------------------------------------- QUBIT
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_WCL_C1").html(result.QUBIT_WCL_C1);//QUBIT 进行中 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_WCL_C2").html(result.QUBIT_WCL_C2);//QUBIT 进行中 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_WCL_C3").html(result.QUBIT_WCL_C3);//QUBIT 进行中 R
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_WCL_C4").html(result.QUBIT_WCL_C4);//QUBIT 进行中 B文库
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_WCL_C5").html(result.QUBIT_WCL_C5);//QUBIT 进行中 加急

	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_YWC_C1").html(result.QUBIT_YWC_C1);//QUBIT 已完成 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_YWC_C2").html(result.QUBIT_YWC_C2);//QUBIT 已完成 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_YWC_C3").html(result.QUBIT_YWC_C3);//QUBIT 已完成 R
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_YWC_C4").html(result.QUBIT_YWC_C4);//QUBIT 已完成 B文库
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QUBIT_YWC_C5").html(result.QUBIT_YWC_C5);//QUBIT 已完成 加急
	            	
					// ---------------------------------------------- 2100
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #2100_C1").html(result.QC_2100_C1);//2100 进行中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #2100_C2").html(result.QC_2100_C2);//2100 已完成
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #2100_C3").html(result.QC_2100_C3);//2100 加急
				}
			}
		});
   }
   
    // 生产任务中心看板 - 文库构建情况统计
    var getLibCount = function(LIB_TYPE) {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_libCount"+LIB_TYPE,"objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_DFP_C1").html(result.DFP_C1);//待分派 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_DFP_C2").html(result.DFP_C2);//待分派 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_DFP_C3").html(result.DFP_C3);//待分派 R
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_DFP_C4").html(result.DFP_C4);//待分派 加急
	            	
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_GJZ_C1").html(result.GJZ_C1);//未处理 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_GJZ_C2").html(result.GJZ_C2);//未处理 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_GJZ_C3").html(result.GJZ_C3);//未处理 R
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_GJZ_C4").html(result.GJZ_C4);//构建中 加急
	            	
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_YWC_C1").html(result.YWC_C1);//已完成 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_YWC_C2").html(result.YWC_C2);//已完成 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_YWC_C3").html(result.YWC_C3);//已完成 R
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #LIB_"+LIB_TYPE+"_YWC_C4").html(result.YWC_C4);//已完成 加急
				}
			}
		});
    }
   
    // 生产任务中心看板 - 样本预处理情况统计
    var getSampleReadyCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_sampleReadyCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_WCL_C1").html(result.WCL_C1);//未处理 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_WCL_C2").html(result.WCL_C2);//未处理 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_WCL_C3").html(result.WCL_C3);//未处理 R
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_WCL_C4").html(result.WCL_C4);//未处理 加急
	            	
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_YWC_C1").html(result.YWC_C1);//已完成 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_YWC_C2").html(result.YWC_C2);//已完成 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_YWC_C3").html(result.YWC_C3);//已完成 R
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #SAMPLE_READY_YWC_C4").html(result.YWC_C4);//已完成 加急
				}
			}
		});
    }
   
    // 生产任务中心看板 - 生产任务情况统计
    var getProdTaskCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_prodTaskCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_XD_C1").html(result.XD_C1);//任务单下达 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_XD_C2").html(result.XD_C2);//任务单下达 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_XD_C3").html(result.XD_C3);//任务单下达 R
	            	
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_DS_C1").html(result.DS_C1);//待审核 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_DS_C2").html(result.DS_C2);//待审核 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_DS_C3").html(result.DS_C3);//待审核 R
	            	
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_YS_C1").html(result.YS_C1);//已审核 ND
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_YS_C2").html(result.YS_C2);//已审核 TD
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #PROD_TASK_YS_C3").html(result.YS_C3);//已审核 R
				}
			}
		});
    }
   
    // 生产任务中心看板 - 核酸检测DNA/RNA情况统计
    var getQcTaskCount = function(QC_TYPE) {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_qcTaskCount","objects":[QC_TYPE, QC_TYPE]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QC_TASK_"+QC_TYPE+"_C1").html(result.C1);//未开始
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QC_TASK_"+QC_TYPE+"_C2").html(result.C2);//已质检中
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QC_TASK_"+QC_TYPE+"_C3").html(result.C3);//报告生成中
//	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #QC_TASK_"+QC_TYPE+"_C4").html(result.C4);//报告已完成
				}
			}
		});
    }
   
    // 生产任务中心看板 - 样本提取情况统计
    var getTqTaskCount = function() {
	   	$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: {"query":"query_PROD_TASK_KANBAN_view_tqTaskCount","objects":[]},
			succeed: function(rs) {
				if (rs && rs.rows) {
					var result = rs.rows[0];
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C1").html(result.C1);//提取任务单 进行中
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C2").html(result.C2);//提取任务单 已完成
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C3").html(result.C3);//待提取 DNA
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C4").html(result.C4);//待提取 RNA
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C5").html(result.C5);//已完成 DNA
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C6").html(result.C6);//已完成 RNA
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C7").html(result.C7);//已终止 DNA
	            	$("#PROD_TASK_KANBAN_CONTENT_"+pathValue+" #TQ_TASK_C8").html(result.C8);//已终止 RNA
				}
			}
		});
    }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refreshFullInfo":refreshFullInfo,
         
         "getNGSRunCount":getNGSRunCount,// 生产任务中心看板 - NGS上机 情况统计
         "getTGSRunCount":getTGSRunCount,// 生产任务中心看板 - TGS上机 情况统计
         
         "getCapturePoolingCount":getCapturePoolingCount,// 生产任务中心看板 - Capture Pooling 情况统计
         "getNGSPoolingCount":getNGSPoolingCount,// 生产任务中心看板 - NGS Pooling 情况统计
         "getTGSPoolingCount":getTGSPoolingCount,// 生产任务中心看板 - TGS Pooling 情况统计
      
         "getLibQcCount":getLibQcCount,// 生产任务中心看板 - 文库QC情况统计
         
         "getLibCount":getLibCount,// 生产任务中心看板 - 文库构建情况统计
         
         "getSampleReadyCount":getSampleReadyCount,// 生产任务中心看板 - 样本预处理情况统计
      
         "getProdTaskCount":getProdTaskCount,// 生产任务中心看板 - 生产任务情况统计
         
         "getQcTaskCount":getQcTaskCount,// 生产任务中心看板 - 核酸检测DNA/RNA情况统计
         
         "getTqTaskCount":getTqTaskCount,// 生产任务中心看板 - 样本提取情况统计
     });
});
