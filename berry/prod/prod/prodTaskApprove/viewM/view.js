$(document).ready(function() {
    var pathValue="berry-prod-prod-prodTaskApprove-viewM-view";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_PRODU_TASK",
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        
        getInfo("form",pathValue,params);// 基本信息
        
		var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
		getInfo("form",pathValue,params,url);//传入i
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
    });
    
 });