$(document).ready(function() {
    var pathValue="berry-prod-prod-prodTaskApprove-approveM-approve";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_PRODU_TASK",
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        
        getInfo("form1",pathValue,params);// 审核信息
        getInfo("form2",pathValue,params);// 基本信息
        
		var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
		getInfo("form1",pathValue,params,url);//传入i
		getInfo("form2",pathValue,params,url);//传入i
    }
    
    var submit=function(){
    	var approveRs = $("#form1"+pathValue+" #TASK_APPROVE_RS"+pathValue).val();
    	if (approveRs) {
    		$("#form1"+pathValue+" #TASK_FLAG"+pathValue).val(approveRs);
    	}
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form1",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 