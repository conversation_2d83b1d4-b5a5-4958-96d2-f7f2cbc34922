$(document).ready(function() {
   var pathValue="berry-prod-prod-projectAssessBG-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid_M;
   var tablesGrid_M_selectRowID = "";
   var tablesGrid_M_selectRowProjectID = "";
   var tablesGrid_D;

   var tablesGrid_M2;
   var tablesGrid_M2_selectRowID = "";
   var tablesGrid_D2;
   
   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar_M=getButtonTemplates(pathValue,[
//            {name:"add",target:"addOpen_M",title:"新增"},
//            {name:"edit",target:"editInfo_M",title:"修改"},
//            {name:"delete",target:"deleteInfo_M",title:"删除"},
//            {name:"submit_sx",target:"submit_M",title:"提交评估报告单"},
        ]);//工具条
        //请求参数
        var tablesGridJson_M={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M,
            height: (fullh-146)/2,
            read:{"query":"query_BR_PROJECT_ASSESS_BG_view_PROD","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M = initKendoGrid("#tablesGrid_M_"+pathValue,tablesGridJson_M);//初始化表格的方法
        tablesGrid_M.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M_selectRowID = "";
        		tablesGrid_D.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M_selectRowID = thisRowID;
        	searchGrid_D();//刷新子表数据
        });
        tablesGrid_M.bind("dataBinding", function(e) {
	        if (tablesGrid_M_selectRowID) {
		  	    tablesGrid_M_selectRowID = "";
		  	    tablesGrid_M_selectRowProjectID = "";
			    searchGrid_D();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D=getButtonTemplates(pathValue,[
//            {name:"add",target:"addDetail",title:"添加明细"},
//            {name:"delete",target:"deleteInfo_D",title:"移除"},
        ]);//工具条
        //请求参数
        var tablesGridJson_D={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D,
           height: (fullh-146)/2,
            read:{"query":"query_BR_PROJECT_ASSESS_BG_MX_view","objects":[""]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_D = initKendoGrid("#tablesGrid_D_"+pathValue,tablesGridJson_D);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar_M2=getButtonTemplates(pathValue,[
//            {name:"revokeSubmit_M2",target:"revokeSubmit_M2",title:"撤回草稿"},
//            {name:"submit_sx",target:"commitSX",title:"提交生信"},
//            {name:"submit_kh",target:"commitCustomer",title:"提交客户"},
//            {name:"submit_scfk",target:"commitProd",title:"提交生产反馈"},
        ]);//工具条
        //请求参数
        var tablesGridJson_M2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M2,
            height: (fullh-146)/2,
            read:{"query":"query_BR_PROJECT_ASSESS_BG_view_PROD","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M2 = initKendoGrid("#tablesGrid_M2_"+pathValue,tablesGridJson_M2);//初始化表格的方法
        tablesGrid_M2.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M2_selectRowID = "";
        		tablesGrid_D2.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M2_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M2_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M2_selectRowID = thisRowID;
        	searchGrid_D2();//刷新子表数据
        });
        tablesGrid_M2.bind("dataBinding", function(e) {
	        if (tablesGrid_M2_selectRowID) {
		  	    tablesGrid_M2_selectRowID = "";
			    searchGrid_D2();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D2=getButtonTemplates(pathValue,[ ]);//工具条
        //请求参数
        var tablesGridJson_D2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D2,
           height: (fullh-146)/2,
            read:{"query":"query_BR_PROJECT_ASSESS_BG_MX_view","objects":[""]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_D2 = initKendoGrid("#tablesGrid_D2_"+pathValue,tablesGridJson_D2);//初始化表格的方法
   }
   var searchGrid_D = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: (fullh-146)/2,
	            read:{"query":"query_BR_PROJECT_ASSESS_BG_MX_view","objects":[tablesGrid_M_selectRowID] },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid_D_"+pathValue,tablesGridJson);
   }
   var searchGrid_D2 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: (fullh-146)/2,
	            read:{"query":"query_BR_PROJECT_ASSESS_BG_MX_view","objects":[tablesGrid_M2_selectRowID] },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid_D2_"+pathValue,tablesGridJson);
   }

    var addOpen_M=function(){
        var winOpts={
            url:"berry/project/project/projectAssessBG/addM/add",
            title:"新增: 评估报告单.."
        };
        openWindow(winOpts);
    }

    var open_M=function(id){
        openWindow({
            url:"berry/project/project/projectAssessBG/addM/add",
            title:"修改: 评估报告单.."
        },{"ID":id});
    }
     
     var callBack=function(){
    	 refreshGrid_M();
    	 refreshGrid_M2();
     };

     var refreshGrid_M=function(){
        if(tablesGrid_M){
            tablesGrid_M.dataSource.read();//重新读取--刷新
      	    tablesGrid_M_selectRowID = "";
      	    tablesGrid_M_selectRowProjectID = "";
    	    searchGrid_D();
        }
     }
     var refreshGrid_D=function(){
        if(tablesGrid_D){
            tablesGrid_D.dataSource.read();//重新读取--刷新
        }
     }

     var refreshGrid_M2=function(){
        if(tablesGrid_M2){
            tablesGrid_M2.dataSource.read();//重新读取--刷新
      	    tablesGrid_M2_selectRowID = "";
    	    searchGrid_D2();
        }
     }
     var refreshGrid_D2=function(){
        if(tablesGrid_D2){
            tablesGrid_D2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo_M=function(){
        var arrIds=getSelectData(tablesGrid_M);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid_M);
        if (rData[0].STATUS!='草稿') {
            alertMsg("只能修改草稿状态的数据!");
            return ;
        }
        
        var winOpts={
            url:"berry/project/project/projectAssessBG/addM/add",
            title:"修改: 评估报告单.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }

     var submit_M=function(){
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据提交操作!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/project/project/assessBG/submit";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	refreshGrid_M();
 	            	refreshGrid_M2();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }

     var revokeSubmit_M2=function(){
         var arrIds=getSelectData(tablesGrid_M2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据提交操作!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/project/project/assessBG/revokeSubmit";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	refreshGrid_M();
 	            	refreshGrid_M2();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     
     var addDetail = function() {
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
    		 alertMsg("选择一行项目评估报告单再添加样本!","error");
    		 return;
    	 }
    	 var winOpts={
    	    url:"berry/project/project/projectAssessBG/addDetail/addDetail",
    	    title:"添加明细.."
    	 };
    	 var dialog = openWindow(winOpts,{ "BG_ID": tablesGrid_M_selectRowID, "PROJECT_ID":tablesGrid_M_selectRowProjectID, "pPathValue":pathValue });//传递id
     }
     var deleteInfo_D = function() {
         var arrIds=getSelectData(tablesGrid_D);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行删除操作!");
             return ;
         }
         // 提示再次确认
     	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
 	        var params={"BG_ID":tablesGrid_M_selectRowID, "ids":arrIds};
             var url="berry/project/project/assessBG/delDetails";
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	                alertMsg("删除成功!", "success", function(){
     	                	refreshGrid_D();
 	                    });
     	            }else{
     	                alertMsg("提交失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
     	});
      }
     
     var commitSX = function() {
         var arrIds=getSelectData(tablesGrid_M2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/project/project/assessBG/commitSX";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	                alertMsg("提交生信成功!", "success", function(){
 	                	refreshGrid_M2();
	                    });
 	            }else{
 	                alertMsg("提交失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     
     var commitCustomer = function() {
         var arrIds=getSelectData(tablesGrid_M2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/project/project/assessBG/commitCustomer";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	                alertMsg("提交客户成功!", "success", function(){
 	                	refreshGrid_M2();
	                    });
 	            }else{
 	                alertMsg("提交失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     
     var commitProd = function() {
         var arrIds=getSelectData(tablesGrid_M2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/project/project/assessBG/commitProd";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	                alertMsg("提交生产成功!", "success", function(){
 	                	refreshGrid_M2();
	                    });
 	            }else{
 	                alertMsg("提交失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     
     var deleteInfo_M=function(){
        var arrIds=getSelectData(tablesGrid_M);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        // 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
            var url="berry/project/project/assessBG/del";
            $.fn.ajaxPost({
                ajaxUrl: url,
                ajaxType: "post",
                ajaxData: params,
                succeed:function(result){
    	            if(result["code"]>0){
    	                alertMsg("删除成功!", "success", function(){
    	                	refreshGrid_M();
    	                	refreshGrid_M2();
	                    });
    	            }else{
    	                alertMsg("提交失败!","error");
    	            }
                },
                failed:function(result){
                    alertMsg("提示:操作异常!","error");
                }
            });
    	});
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open_M":open_M,//打开新窗口方法-此方法非必须-自定义
         "addOpen_M":addOpen_M,//打开添加表单
         "editInfo_M":editInfo_M,
         "submit_M":submit_M,
         "revokeSubmit_M2":revokeSubmit_M2,
         "refreshGrid_M":refreshGrid_M,
         "searchGrid_D":searchGrid_D,
         "refreshGrid_D":refreshGrid_D,
         "refreshGrid_M2":refreshGrid_M2,
         "searchGrid_D2":searchGrid_D2,
         "refreshGrid_D2":refreshGrid_D2,
         "callBack":callBack,//回调方法
         "addDetail":addDetail,
         "deleteInfo_D":deleteInfo_D,
         "deleteInfo_M":deleteInfo_M,
         "commitSX":commitSX,
         "commitCustomer":commitCustomer,
         "commitProd":commitProd,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
