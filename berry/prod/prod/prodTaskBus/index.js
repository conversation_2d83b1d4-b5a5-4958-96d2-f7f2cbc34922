$(document).ready(function() {
   var pathValue="berry-prod-prod-prodTaskBus-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid_M1;
   var tablesGrid_M1_selectRowID="";
   var tablesGrid_D1;

   var tablesGrid_M2;
   var tablesGrid_M2_selectRowID="";
   var tablesGrid_D2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar_M1=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen_M",title:"新增"},
            {name:"edit",target:"editInfo_M",title:"修改"},
    		{name:"delete",target:"deleteInfo_M", title:"删除"},
    		{name:"submit",target:"submitToApprove_M", title:"提交"},
        ]);//工具条
        //请求参数
        var tablesGridJson_M1={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M1,
           height: (fullh-122)/2,
            read:{"query":"query_BR_PRODU_TASK_view","objects":[ ["草稿"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M1 = initKendoGrid("#tablesGrid_M1_"+pathValue,tablesGridJson_M1);//初始化表格的方法
        tablesGrid_M1.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M1_selectRowID = "";
        		tablesGrid_D1.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M1_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M1_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M1_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M1_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M1_selectRowID = thisRowID;
        	searchGrid_D1();//刷新子表数据
        });
        tablesGrid_M1.bind("dataBinding", function(e) {
	        if (tablesGrid_M1_selectRowID) {
		  	    tablesGrid_M1_selectRowID = "";
			    searchGrid_D1();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D1=getButtonTemplates(pathValue,[
            {name:"add",target:"selectAddSample",title:"选择添加样本"},
            {name:"add",target:"selectCopySample", title:"复制数据"},
            {name:"edit",target:"editInfo_MX",title:"修改"},
            {name:"edit",target:"editInfo_MX_batch",title:"批量修改"},
            {name:"edit",target:"editInfo_MX_setFlowID",title:"设置流程方案"},
            {name:"delete",target:"deleteInfo_MX", title:"移除"},
        ]);//工具条
        //请求参数
        var tablesGridJson_D1={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D1,
           height: (fullh-122)/2,
            read:{"query":"query_BR_PRODU_TASK_MX_view","objects":[""]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_D1 = initKendoGrid("#tablesGrid_D1_"+pathValue,tablesGridJson_D1);//初始化表格的方法
        
        /**
         * 列表-按钮-定义
         */
        var toolbar_M2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_M2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M2,
           height: (fullh-122)/2,
            read:{"query":"query_BR_PRODU_TASK_view","objects":[ ["待审核","审核通过", "审核不通过", "已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M2 = initKendoGrid("#tablesGrid_M2_"+pathValue,tablesGridJson_M2);//初始化表格的方法
        tablesGrid_M2.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M2_selectRowID = "";
        		tablesGrid_D2.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M2_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M2_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M2_selectRowID = thisRowID;
        	searchGrid_D2();//刷新子表数据
        });
        tablesGrid_M2.bind("dataBinding", function(e) {
	        if (tablesGrid_M2_selectRowID) {
		  	    tablesGrid_M2_selectRowID = "";
			    searchGrid_D2();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_D2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D2,
           height: (fullh-122)/2,
            read:{"query":"query_BR_PRODU_TASK_MX_view","objects":[""]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_D2 = initKendoGrid("#tablesGrid_D2_"+pathValue,tablesGridJson_D2);//初始化表格的方法
   }
   var searchGrid_D1 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: (fullh-122)/2,
	            read:{"query":"query_BR_PRODU_TASK_MX_view","objects":[tablesGrid_M1_selectRowID] },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid_D1_"+pathValue,tablesGridJson);
   }
   var searchGrid_D2 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: (fullh-122)/2,
	            read:{"query":"query_BR_PRODU_TASK_MX_view","objects":[tablesGrid_M2_selectRowID] },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid_D2_"+pathValue,tablesGridJson);
   }

    var addOpen_M=function(){
        var winOpts={
            url:"berry/prod/prod/prodTaskBus/addM/add",
            title:"新增: 生产任务主单.."
        };
        openWindow(winOpts);
    }

    var open_M=function(ID){
        openWindow({
            url:"berry/prod/prod/prodTaskBus/addM/add",
            title:"修改: 生产任务主单.."
        },{"ID":ID});
    }
     
     var callBack=function(){
    	 refreshGrid_M1();
    	 refreshGrid_M2();
     };

     var refreshGrid_M1=function(){
        if(tablesGrid_M1){
            tablesGrid_M1.dataSource.read();//重新读取--刷新
            tablesGrid_M1_selectRowID = "";
            searchGrid_D1();
        }
     }
     var refreshGrid_M2=function(){
         if(tablesGrid_M2){
             tablesGrid_M2.dataSource.read();//重新读取--刷新
             tablesGrid_M2_selectRowID = "";
             searchGrid_D2();
         }
      }

     var editInfo_M=function(){
        var arrIds=getSelectData(tablesGrid_M1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }

        var winOpts={
            url:"berry/prod/prod/prodTaskBus/addM/add",
            title:"修改: 生产任务主单.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }
     var editInfo_MX=function(){
         var arrIds=getSelectData(tablesGrid_D1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行修改!");
             return ;
         }else if(arrIds.length!=1){
             alertMsg("请只选择一条数据进行修改操作!");
             return ;
         }
         var winOpts={
             url:"berry/prod/prod/prodTaskBus/editMX/edit",
             title:"修改: 生产任务明细.."
         };
         var dialog = openWindow(winOpts,{ "ID":arrIds[0], "pPathValue":pathValue });
      }
     var editInfo_MX_batch=function(){
         var arrIds=getSelectData(tablesGrid_D1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行修改!");
             return ;
         }
         var IDS = "";
         for (var i=0; i<arrIds.length; i++) {
        	 IDS += IDS.length>0 ? ( ","+arrIds[i] ) : arrIds[i];
         }
         var winOpts={
             url:"berry/prod/prod/prodTaskBus/editMXBatch/edit",
             title:"批量修改: 生产任务明细.."
         };
         var dialog = openWindow(winOpts,{ "TASK_ID":tablesGrid_M1_selectRowID , "IDS":IDS, "pPathValue":pathValue });
      }
     var editInfo_MX_setFlowID=function(){
         var arrIds=getSelectData(tablesGrid_D1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行修改!");
             return ;
         }
         var IDS = "";
         for (var i=0; i<arrIds.length; i++) {
        	 IDS += IDS.length>0 ? ( ","+arrIds[i] ) : arrIds[i];
         }
         var winOpts={
             url:"berry/prod/prod/prodTaskBus/setFlowID/edit",
             title:"批量修改: 生产任务明细流程方案.."
         };
         var dialog = openWindow(winOpts,{ "TASK_ID":tablesGrid_M1_selectRowID , "IDS":IDS, "pPathValue":pathValue });
      }
     
     var selectAddSample = function() {
         var rData = getGridSelectData(tablesGrid_M1);
         if(rData.length==0){
    		 alertMsg("选择一行主单再添加建库任务!","error");
    		 return;
    	 }
    	 var winOpts={
    	    url:"berry/prod/prod/prodTaskBus/addMX/addMX",
    	    title:"添加: 选择样本.."
    	 };
    	 var params = { "TASK_ID": tablesGrid_M1_selectRowID, "PROJECT_ID":rData[0].PROJECT_ID, "pPathValue":pathValue };
    	 var dialog = openWindow(winOpts, params);
     }
     var selectCopySample = function() {
         var rData=getGridSelectData(tablesGrid_D1);
         if(rData.length==0){
             alertMsg("请至少选择一行数据");
             return ;
         }
         var arrIds = {};
         var arrLinkIds = {};
         for (var i=0; i<rData.length; i++) {
        	 arrIds[i] = rData[i].ID;
        	 arrLinkIds[i] = rData[i].LINK_ID;
         }
         var url="berry/prod/prod/prodTaskBus/addMXCopy";
         var params={"TASK_ID":tablesGrid_M1_selectRowID, "ids":arrIds, "linkIds":arrLinkIds}
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	searchGrid_D1();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     var deleteInfo_MX = function() {
         var arrIds=getSelectData(tablesGrid_D1);
         if(arrIds.length==0){
             alertMsg("请至少选择一行数据进行删除操作!");
             return ;
         }
         var url="berry/prod/prod/prodTaskBus/delMX";
         var params={"ids":arrIds, "TASK_ID":tablesGrid_M1_selectRowID}
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	searchGrid_D1();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     var deleteInfo_M = function() {
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
             alertMsg("请至少选择一行数据进行删除操作!");
             return ;
         }
         var url="berry/prod/prod/prodTaskBus/delM";
         var params={"ids":arrIds}
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	refreshGrid_M1();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     var submitToApprove_M = function() {
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         var url="berry/prod/prod/prodTaskBus/submitToApprove";
         var params={"ids":arrIds}
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	refreshGrid_M1();
 	            	refreshGrid_M2();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open_M":open_M,//打开新窗口方法-此方法非必须-自定义
         "addOpen_M":addOpen_M,//打开添加表单
         "editInfo_M":editInfo_M,
         "deleteInfo_M":deleteInfo_M,
         "submitToApprove_M":submitToApprove_M,
         "refreshGrid_M1":refreshGrid_M1,
         "refreshGrid_M2":refreshGrid_M2,
         "searchGrid_D1":searchGrid_D1,
         "searchGrid_D2":searchGrid_D2,
         "callBack":callBack,//回调方法
         "selectAddSample":selectAddSample,
         "selectCopySample":selectCopySample,
         "editInfo_MX":editInfo_MX,
         "editInfo_MX_batch":editInfo_MX_batch,
         "editInfo_MX_setFlowID":editInfo_MX_setFlowID,
         "deleteInfo_MX":deleteInfo_MX,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
