$(document).ready(function() {
   var pathValue="berry-prod-run-downNGSQC-add-add";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAdd",title:"确认添加"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_NGS_DATAQC_view_OLD","objects":[]}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	            height: fullh-196,
	            read:{"query":"query_BR_MODUAL_NGS_DATAQC_view_OLD","objects":[],
	            	"search": searchparams
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
   }

     var confirmAdd=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
        var url= "berry/prod/run/downNGSQC/add";
        var params = { "ids":arrIds };
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
		            //提交成功
	                alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
	                   funcExce(pathValue+"close");//关闭页面
	                });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
        
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "searchGrid":searchGrid,
         "confirmAdd":confirmAdd,
     });
});
