$(document).ready(function() {
    var pathValue="berry-prod-run-upNGS-addM-add";
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_NGS_BOARDING_INFO",
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);// 基本信息
        
		var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
		getInfo("form",pathValue,params,url);//传入id
    }
    
    var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 });
