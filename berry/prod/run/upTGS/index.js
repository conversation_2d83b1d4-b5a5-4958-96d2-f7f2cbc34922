$(document).ready(function() {
   var pathValue="berry-prod-run-upTGS-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }
   var tablesGrid_M1;
   var tablesGrid_D1;
   var tablesGrid_M2;
   var tablesGrid_D2;
   var tablesGrid_M3;
   var tablesGrid_D3;
   var tablesGrid_M1_selectRowID="";
   var tablesGrid_M2_selectRowID="";
   var tablesGrid_M3_selectRowID="";

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   /**
        * 列表-按钮-定义
        */
       var toolbar_M1=getButtonTemplates(pathValue,[
           {name:"add",target:"addOpen_M1",title:"新增"},
           {name:"edit",target:"editInfo_M1",title:"修改"},
           {name:"delete",target:"delete_M1",title:"删除"},
           {name:"submit",target:"commit_M1", title:"确认方案"},
       ]);//工具条
       //请求参数
       var tablesGridJson_M1={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           toolbar: toolbar_M1,
           height: (fullh-122)/2,
           read:{"query":"query_BR_TGS_BOARDING_INFO_view","objects":[ ["方案制定"] ]},
           headerFilter:function(cols,i){
               if(i){
                   if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                       setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                   }
               }
           },
           selectable: true
       };
       tablesGrid_M1 = initKendoGrid("#tablesGrid_M1_"+pathValue,tablesGridJson_M1);//初始化表格的方法
       tablesGrid_M1.bind("change", function(e) {
       	var rData = getGridSelectData(this);
    	if (rData.length==0) {
    		tablesGrid_M1_selectRowID = "";
    		tablesGrid_D1.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
    		return;
    	}
    	// 当前选中行号
    	var thisRowID = rData[0].ID;
    	
    	// 复选框选中状态控制
    	var h_ck = $("#tablesGrid_M1_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
    	var c_ck = $("#tablesGrid_M1_"+pathValue+" .k-grid-content-locked input");//内容行复选框
    	$.each(c_ck, function(i, val) {
        	$(this).removeAttr("checked");
    	});
    	$.each(h_ck, function(i, val) {
    		$(this).removeAttr("checked");
    	});
    	// 打钩选中行号
	   	var ROWS_ID = $("#tablesGrid_M1_"+pathValue+" .mainGridRowID"+pathValue);//行ID
	   	for (var i = 0; i < ROWS_ID.length; i++) {
	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
	   			$(c_ck[i]).prop("checked", true);
	   			break;
	   		}
	   	}
    	
    	if (thisRowID == tablesGrid_M1_selectRowID) {//两次选中同一行，不往下操作
    		return;
    	}
    	tablesGrid_M1_selectRowID = thisRowID;
    	searchGrid_D1();//刷新子表数据
    });
       tablesGrid_M1.bind("dataBinding", function(e) {
	        if (tablesGrid_M1_selectRowID) {
		  	    tablesGrid_M1_selectRowID = "";
			    searchGrid_D1();
	        }
       });
       /**
        * 列表-按钮-定义
        */
       var toolbar_D1=getButtonTemplates(pathValue,[
           {name:"add",target:"select_D1",title:"添加明细"},
           {name:"edit",target:"editInfo_D1",title:"修改"},
           {name:"delete",target:"deleteInfo_D1", title:"移除"},
       ]);//工具条
       //请求参数
       var tablesGridJson_D1={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           toolbar: toolbar_D1,
           height: (fullh-122)/2,
           read:{"query":"query_BR_TGS_BOARDING_MX_INFO_view","objects":[""]}
       };
       tablesGrid_D1 = initKendoGrid("#tablesGrid_D1_"+pathValue,tablesGridJson_D1);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_M2=getButtonTemplates(pathValue,[
              {name:"submit",target:"commit_M2", title:"上机完成"},
              {name:"submit",target:"commit_M2_ERR", title:"上机异常"},
        ]);//工具条
        //请求参数
        var tablesGridJson_M2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M2,
            height: (fullh-122)/2,
            read:{"query":"query_BR_TGS_BOARDING_INFO_view","objects":[ ["进行中"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true 
        };
        tablesGrid_M2 = initKendoGrid("#tablesGrid_M2_"+pathValue,tablesGridJson_M2);//初始化表格的方法
        tablesGrid_M2.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M2_selectRowID = "";
        		tablesGrid_D2.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M2_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M2_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M2_selectRowID = thisRowID;
        	searchGrid_D2();//刷新子表数据
        });
        tablesGrid_M2.bind("dataBinding", function(e) {
	        if (tablesGrid_M2_selectRowID) {
		  	    tablesGrid_M2_selectRowID = "";
			    searchGrid_D2();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_D2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D2,
            height: (fullh-122)/2,
            read:{"query":"query_BR_TGS_BOARDING_MX_INFO_view","objects":[""]}
        };
        tablesGrid_D2 = initKendoGrid("#tablesGrid_D2_"+pathValue,tablesGridJson_D2);//初始化表格的方法
        

        /**
         * 列表-按钮-定义
         */
        var toolbar_M3=getButtonTemplates(pathValue,[
        	{name:"delete",target:"revokeUPTGSWfSubmit",title: "流程撤回"},
        	{name:"delete",target:"revokeRunERR",title: "撤回上机异常"},
        ]);//工具条
        //请求参数
        var tablesGridJson_M3={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M3,
            height: (fullh-122)/2,
            read:{"query":"query_BR_TGS_BOARDING_INFO_view","objects":[ ["上机完成", "上机异常"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M3 = initKendoGrid("#tablesGrid_M3_"+pathValue,tablesGridJson_M3);//初始化表格的方法
        tablesGrid_M3.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M3_selectRowID = "";
        		tablesGrid_D3.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M3_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M3_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M3_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M3_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M3_selectRowID = thisRowID;
        	searchGrid_D3();//刷新子表数据
        });
        tablesGrid_M3.bind("dataBinding", function(e) {
	        if (tablesGrid_M3_selectRowID) {
		  	    tablesGrid_M3_selectRowID = "";
			    searchGrid_D3();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D3=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_D3={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D3,
            height: (fullh-122)/2,
            read:{"query":"query_BR_TGS_BOARDING_MX_INFO_view","objects":[""]}
        };
        tablesGrid_D3 = initKendoGrid("#tablesGrid_D3_"+pathValue,tablesGridJson_D3);//初始化表格的方法
   }
   var searchGrid_D1 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	            height: (fullh-122)/2,
	            read:{"query":"query_BR_TGS_BOARDING_MX_INFO_view","objects":[tablesGrid_M1_selectRowID] }
	        };
	  setGridDataSource("#tablesGrid_D1_"+pathValue,tablesGridJson);
   }
   var searchGrid_D2 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	            height: (fullh-122)/2,
	            read:{"query":"query_BR_TGS_BOARDING_MX_INFO_view","objects":[tablesGrid_M2_selectRowID] }
	        };
	  setGridDataSource("#tablesGrid_D2_"+pathValue,tablesGridJson);
   }
   var searchGrid_D3 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	            height: (fullh-122)/2,
	            read:{"query":"query_BR_TGS_BOARDING_MX_INFO_view","objects":[tablesGrid_M3_selectRowID] }
	        };
	  setGridDataSource("#tablesGrid_D3_"+pathValue,tablesGridJson);
   }

    var addOpen_M1=function(){
        var winOpts={
            url:"berry/prod/run/upTGS/addM/add",
            title:"新增: TGS上机方案主单.."
        };
        openWindow(winOpts);
    }
     
     var callBack=function(){
    	 refreshGrid_M1();
    	 refreshGrid_M2();
    	 refreshGrid_M3();
     };
     var refreshGrid_M1=function(){
         if(tablesGrid_M1){
             tablesGrid_M1.dataSource.read();//重新读取--刷新
             tablesGrid_M1_selectRowID="";
             searchGrid_D1();
         }
      }
     var refreshGrid_M2=function(){
        if(tablesGrid_M2){
            tablesGrid_M2.dataSource.read();//重新读取--刷新
            tablesGrid_M2_selectRowID="";
            searchGrid_D2();
        }
     }
     var refreshGrid_M3=function(){
         if(tablesGrid_M3){
             tablesGrid_M3.dataSource.read();//重新读取--刷新
             tablesGrid_M3_selectRowID="";
             searchGrid_D3();
         }
      }

     var editInfo_M1=function(){
        var arrIds=getSelectData(tablesGrid_M1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }

        var winOpts={
            url:"berry/prod/run/upTGS/addM/add",
            title:"修改: TGS上机方案主单.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }

     var editInfo_D1=function(){
        var arrIds=getSelectData(tablesGrid_D1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }

        var winOpts={
            url:"berry/prod/run/upTGS/editMX/edit",
            title:"修改: TGS上机方案明细.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0],"pPathValue":pathValue });//传递id
     }

     var commit_M1=function(){
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据提交操作!");
             return ;
         }
         confirmMsg("提示","确定执行提交吗?","question",function(){
	         var params={"ids":arrIds};
	         var url="berry/prod/run/upTGS/commitToRuning";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
	 	            	refreshGrid_M1();
	 	            	refreshGrid_M2();
	 	                alertMsg("提示:操作成功!","success");
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
         });
     }
     var commit_M2=function(){
         var arrIds=getSelectData(tablesGrid_M2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据提交操作!");
             return ;
         }
         confirmMsg("提示","确定执行提交吗?","question",function(){
	         var params={"ids":arrIds};
	         var url="berry/prod/run/upTGS/commitFlow";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
	 	            	refreshGrid_M2();
	 	            	refreshGrid_M3();
	 	                alertMsg("提示:操作成功!","success");
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
         });
     }
     var commit_M2_ERR=function(){
         var arrIds=getSelectData(tablesGrid_M2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据提交操作!");
             return ;
         }
         confirmMsg("提示","确定执行提交吗?","question",function(){
	         var params={"ids":arrIds};
	         var url="berry/prod/run/upTGS/commitToRuning";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
	 	            	refreshGrid_M2();
	 	            	refreshGrid_M3();
	 	                alertMsg("提示:操作成功!","success");
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
         });
     }
     
     var delete_M1=function(){
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据删除操作!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/prod/run/upTGS/del";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	refreshGrid_M1();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     
     var select_D1 = function() {
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
    		 alertMsg("选择一行主单再添加明细!","error");
    		 return;
    	 }
    	 var winOpts={
    	    url:"berry/prod/run/upTGS/addMX/addMX",
    	    title:"添加明细.."
    	 };
    	 var dialog = openWindow(winOpts,{ "TGS_ID": tablesGrid_M1_selectRowID, "pPathValue":pathValue });//传递id
     }
     var deleteInfo_D1 = function() {
         var arrIds=getSelectData(tablesGrid_M1);
         if(arrIds.length==0){
    		 alertMsg("选择一行主单!","error");
    		 return;
    	 }
         var arrIds2=getSelectData(tablesGrid_D1);
         if(arrIds2.length==0){
             alertMsg("请至少选择一条数据删除操作!");
             return ;
         }
         var params={"TGS_ID": tablesGrid_M1_selectRowID, "ids":arrIds2};
         var url="berry/prod/run/upTGS/delMX";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	searchGrid_D1();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     
     var revokeUPTGSWfSubmit=function(){
         var arrIds=getSelectData(tablesGrid_M3);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据执行撤回操作!");
             return ;
         }
         confirmMsg("提示","确定执行撤回?","question",function(){
             var url="berry/prod/run/upTGS/revokeFlow";
             var params={"ids":arrIds};
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	refreshGrid_M2();
     	            	refreshGrid_M3();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
     }
     var revokeRunERR=function(){
         var arrIds=getSelectData(tablesGrid_M3);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
         confirmMsg("提示","确定执行撤回吗?","question",function(){
	         var params={"ids":arrIds};
	         var url="berry/prod/run/upTGS/revokeRunERR";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
	 	            	refreshGrid_M2();
	 	            	refreshGrid_M3();
	 	                alertMsg("提示:操作成功!","success");
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "addOpen_M1":addOpen_M1,//打开添加表单
         "editInfo_M1":editInfo_M1,
         "editInfo_D1":editInfo_D1,
         "commit_M1":commit_M1,
         "commit_M2":commit_M2,
         "commit_M2_ERR":commit_M2_ERR,
         "delete_M1":delete_M1,
         "searchGrid_D1":searchGrid_D1,
         "searchGrid_D2":searchGrid_D2,
         "searchGrid_D3":searchGrid_D3,
         "refreshGrid_M1":refreshGrid_M1,
         "refreshGrid_M2":refreshGrid_M2,
         "refreshGrid_M3":refreshGrid_M3,
         "callBack":callBack,//回调方法
         "select_D1":select_D1,
         "deleteInfo_D1":deleteInfo_D1,
         "revokeUPTGSWfSubmit":revokeUPTGSWfSubmit,
         "revokeRunERR":revokeRunERR,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
