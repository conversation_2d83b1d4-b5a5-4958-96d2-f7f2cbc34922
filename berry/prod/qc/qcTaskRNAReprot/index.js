$(document).ready(function() {
   var pathValue="berry-prod-qc-qcTaskRNAReprot-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;
   var tablesGrid3;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"send",target:"sendReport", title:"发送报告"},
            //{name:"submit",target:"submitToDFK", title:"确认已发送报告"},
            {name:"back",target:"backToYSC", title:"退回生产"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_REPROT_view","objects":[ "RNA", ["待发送"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            },
            detailTemplate: '<div id="subGrid_1_#=ID#_'+pathValue+'"></div><br/>',
            detailInit: function (e) {
	        	var ROW_ID = e.data.ID;
	            // 加载表格
	            var subGrid_1_JSON={
	                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	                sort: "",//排序
	                toolbar: null,
	                height: 320,
	                read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "RNA",["待发送"] ],
	                	"search":{"QC_REPROT_FILE":ROW_ID}
	                	}
	            };
	            initKendoGrid("#subGrid_1_"+ROW_ID+"_"+pathValue,subGrid_1_JSON);
	        }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"send",target:"sendReport2", title:"发送报告"},
            {name:"submit",target:"submitToYFK", title:"确认已反馈"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_REPROT_view","objects":[ "RNA", ["待反馈"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            },
            detailTemplate: '<div id="subGrid_2_#=ID#_'+pathValue+'"></div><br/>',
            detailInit: function (e) {
	        	var ROW_ID = e.data.ID;
	            // 加载表格
	            var subGrid_2_JSON={
	                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	                sort: "",//排序
	                toolbar: null,
	                height: 320,
	                read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "RNA",["待反馈"] ],
	                	"search":{"QC_REPROT_FILE":ROW_ID}
	                }
	            };
	            initKendoGrid("#subGrid_2_"+ROW_ID+"_"+pathValue,subGrid_2_JSON);
	        }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar3=getButtonTemplates(pathValue,[
            {name:"send",target:"sendReport3", title:"发送报告"},
            {name:"submit",target:"submitFlow", title:"提交实验方案"},
        ]);//工具条
        //请求参数
        var tablesGridJson3={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar3,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_REPROT_view","objects":[ "RNA", ["已反馈","已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            },
            detailTemplate: '<div id="subGrid_3_#=ID#_'+pathValue+'"></div><br/>',
            detailInit: function (e) {
	        	var ROW_ID = e.data.ID;
	            // 加载表格
	            var subGrid_3_JSON={
	                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	                sort: "",//排序
	                toolbar: null,
	                height: 320,
	                read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "RNA",["已反馈","已提交方案"] ],
	                	"search":{"QC_REPROT_FILE":ROW_ID}
	                }
	            };
	            initKendoGrid("#subGrid_3_"+ROW_ID+"_"+pathValue,subGrid_3_JSON);
	        }
        };
        tablesGrid3 = initKendoGrid("#tablesGrid3_"+pathValue,tablesGridJson3);//初始化表格的方法
   }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/qc/qcTaskRNA/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
    	 refreshGrid3();
     };
     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid2=function(){
         if(tablesGrid2){
             tablesGrid2.dataSource.read();//重新读取--刷新
         }
      }
      var refreshGrid3=function(){
         if(tablesGrid3){
             tablesGrid3.dataSource.read();//重新读取--刷新
         }
      }
      
      var backToYSC=function(){
          var arrIds=getSelectData(tablesGrid);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据!");
              return ;
          }
          confirmMsg("确认", "确定要退回选中的数据吗?", "warn", function() {
	         var winOpts={
	             url:"berry/prod/qc/qcTaskRNAReprot/backToSC/backToSC",
	             title:"退回: 检测报告.."
	         };
	         openWindow(winOpts,{ "ids":arrIds, "pPathValue":pathValue });
          });
      }
      var submitToDFK=function(){
          var arrIds=getSelectData(tablesGrid);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据进行提交操作!");
              return ;
          }
          confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/submitToDFK";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid();
  		            	refreshGrid2();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }
      var submitToYFK=function(){
          var arrIds=getSelectData(tablesGrid2);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据进行提交操作!");
              return ;
          }
          confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/submitToYFK";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid2();
  		            	refreshGrid3();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }
      var submitFlow=function(){
          var arrIds=getSelectData(tablesGrid3);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据进行提交操作!");
              return ;
          }
          confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/submitFlow";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid3();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }
      
      var sendReport2 = function() {
    	  sendReport(2);
      }
      var sendReport3 = function() {
    	  sendReport(3);
      }
      var sendReport = function(selectGridNO) {
    	  selectGridNO = (selectGridNO==2||selectGridNO==3) ? selectGridNO : 1;
    	  var selectGrid = tablesGrid;
    	  if (selectGridNO==1) {
    		  
    	  } else if (selectGridNO==2) {
    		  selectGrid = tablesGrid2;
    	  } else if (selectGridNO==3) {
    		  selectGrid = tablesGrid3;
    	  } else {
    		  alertMsg("参数错误");
              return ;
    	  }
    	  
          var arrIds=getSelectData(selectGrid);
          if(arrIds.length==0){
              alertMsg("请选择一行待发送报告!");
              return ;
          } else if (arrIds.length>1) {
              alertMsg("只能选择一行待发送报告!");
              return ;
          }
          var rData = getGridSelectData(selectGrid);
          var r0 = rData[0];//获取选中第一行
          
          //邮件收件人
          var EMAIL_RECEIVER = "<EMAIL>; <EMAIL>;";
          //邮件抄送人
          var EMAIL_RECEIVER_COPY_TO = "<EMAIL>;";
          //邮件标题
          var EMAIL_TITLE = "RNA核酸质检报告:"
         	 	+ " " + r0.CONTRACT_NO     //合同号
         	 	+ " " + r0.CONTRACT_NAME   //合同名称
         	 	+ " " + r0.SAMPLE_SEND_NO  //送检单单号
         	 	;
          //邮件内容
          var EMAIL_CONTENT = "RNA邮件内容信息...";
          //邮件附件: 报告文件名称
          var EMAIL_ATTA = r0.QC_REPROT_FILE;
          
          //后台获取发件信息: 收件人, 抄送人, 邮件内容模板 ...
          
          
          var pageParams = {
         		 "BUS_LINK_ID":arrIds[0],
         		 "BUS_LINK_TYPE":"RNA质检报告发送",
         		 "EMAIL_TITLE":EMAIL_TITLE,
         		 "EMAIL_RECEIVER":EMAIL_RECEIVER,
         		 "EMAIL_RECEIVER_COPY_TO":EMAIL_RECEIVER_COPY_TO,
         		 "EMAIL_CONTENT":EMAIL_CONTENT,
         		 "EMAIL_ATTA":EMAIL_ATTA,
        		 "selectGridNO":selectGridNO,
         		 "pPathValue":pathValue,
         	};
          
          var winOpts={
              url:"berry/prod/qc/qcTaskRNAReprot/sendEmail/sendEmail",
              title:"发送邮件: RNA质检报告发送.."
          };
          openWindow(winOpts, pageParams);
      }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "callBack":callBack,//回调方法
         "refreshGrid":refreshGrid,
         "refreshGrid2":refreshGrid2,
         "refreshGrid3":refreshGrid3,
         "backToYSC":backToYSC,
         "submitToDFK":submitToDFK,
         "submitToYFK":submitToYFK,
         "submitFlow":submitFlow,
         "sendReport":sendReport,
         "sendReport2":sendReport2,
         "sendReport3":sendReport3,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
