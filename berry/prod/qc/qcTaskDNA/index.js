$(document).ready(function() {
   var pathValue="berry-prod-qc-qcTaskDNA-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;
   var tablesGrid3;
   var tablesGrid4;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"新增检测任务"},
    		{name:"delete",target:"deleteInfo",title:"删除检测任务"},
            {name:"edit",target:"editInfo",title:"录入结果"},
            {name:"edit",target:"editInfoBatch",title:"批量录入结果"},
            //{name:"add",target:"generateReprotBatch", title:"批量上传报告"},
            {name:"add",target:"editInfoBatchJT", title:"批量上传胶图"},
            {name:"submit",target:"submitToDSC", title:"提交检测结果"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "DNA", ["检测中", "退回"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_REPROT_view","objects":[ "DNA", ["待发送", "待反馈", "已反馈", "已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            },
            detailTemplate: '<div id="subGrid_2_#=ID#_'+pathValue+'"></div><br/>',
            detailInit: function (e) {
	        	var ROW_ID = e.data.ID;
	            // 加载表格
	            var subGrid_2_JSON={
	                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	                sort: "",//排序
	                toolbar: null,
	                height: 320,
	                read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "DNA",["已提交方案"]],
	                	"search":{"QC_REPROT_FILE":ROW_ID}
	                }
	            };
	            initKendoGrid("#subGrid_2_"+ROW_ID+"_"+pathValue,subGrid_2_JSON);
	        }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar3=getButtonTemplates(pathValue,[
            {name:"edit",target:"editInfo3",title:"录入结果"},
            {name:"edit",target:"editInfoBatch3",title:"批量录入结果"},
            {name:"add",target:"editInfoBatchJT3", title:"批量上传胶图"},
            {name:"add",target:"generateReprotBatch", title:"生成报告"},
        ]);//工具条
        //请求参数
        var tablesGridJson3={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar3,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "DNA", ["待生成报告"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid3 = initKendoGrid("#tablesGrid3_"+pathValue,tablesGridJson3);//初始化表格的方法
        
        /**
         * 列表-按钮-定义
         */
        var toolbar4=getButtonTemplates(pathValue,[
            {name:"add",target:"qcReprotDelete", title:"删除报告"},
            {name:"submit",target:"submitToDFS", title:"提交报告"},
        ]);//工具条
        //请求参数
        var tablesGridJson4={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar4,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_REPROT_view","objects":[ "DNA", ["已生成报告", "项目退回", "客户退回"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            },
            detailTemplate: '<div id="subGrid_4_#=ID#_'+pathValue+'"></div><br/>',
            detailInit: function (e) {
	        	var ROW_ID = e.data.ID;
	            // 加载表格
	            var subGrid_4_JSON={
	                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	                sort: "",//排序
	                toolbar: null,
	                height: 320,
	                read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "DNA" ,["已生成报告"]],
	                	 "search":{"QC_REPROT_FILE":ROW_ID}
	                	}
	            };
	            initKendoGrid("#subGrid_4_"+ROW_ID+"_"+pathValue,subGrid_4_JSON);
	        }
        };
        tablesGrid4 = initKendoGrid("#tablesGrid4_"+pathValue,tablesGridJson4);//初始化表格的方法
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/prod/qc/qcTaskDNA/add/add",
            title:"DNA QC: 新增.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/qc/qcTaskDNA/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
    
    /** 删除 */
    var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/prod/qc/qcTask/del";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid();
		                alertMsg("提示:操作成功!","success");
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
     }

    var submitToDSC=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行提交操作!");
            return ;
        }
        confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/prod/qc/qcTask/submitToDSC";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid();
		            	refreshGrid3();
		                alertMsg("提示:操作成功!","success");
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
    }
     var submitToDFS=function(){
         var arrIds=getSelectData(tablesGrid4);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
 	        var params={"ids":arrIds};
 	        var url="berry/prod/qc/qcTask/submitToDFS";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	refreshGrid4();
 		            	refreshGrid2();
 		                alertMsg("提示:操作成功!","success");
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
     }
     var qcReprotDelete=function(){
         var arrIds=getSelectData(tablesGrid4);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
 	        var params={"ids":arrIds};
 	        var url="berry/prod/qc/qcTask/qcReprotDelete";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	refreshGrid4();
 		            	refreshGrid3();
 		                alertMsg("提示:操作成功!","success");
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
     }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
    	 refreshGrid3();
    	 refreshGrid4();
     };
     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid2=function(){
        if(tablesGrid2){
            tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid3=function(){
        if(tablesGrid3){
            tablesGrid3.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid4=function(){
        if(tablesGrid4){
            tablesGrid4.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        editInfo_Win(arrIds);
     }
     var editInfo3=function(){
         var arrIds=getSelectData(tablesGrid3);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行修改!");
             return ;
         }else if(arrIds.length!=1){
             alertMsg("请只选择一条数据进行修改操作!");
             return ;
         }
         editInfo_Win(arrIds);
      }
     var editInfo_Win=function(arrIds){
         var winOpts={
             url:"berry/prod/qc/qcTaskDNA/edit/edit",
             title:"DNA QC: 录入结果.."
         };
         openWindow(winOpts,{ "ID":arrIds[0] });//传递id
      }
     
     var editInfoBatch=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行批量修改!");
             return ;
         }
         editInfoBatch_Win(arrIds);
      }
     var editInfoBatch3=function(){
         var arrIds=getSelectData(tablesGrid3);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行批量修改!");
             return ;
         }
         editInfoBatch_Win(arrIds);
      }
     var editInfoBatch_Win=function(arrIds){
         confirmMsg("确认", "批量录入结果会批量覆盖所有选择数据，请谨慎操作", "warn", function() {
	         var ids = "";
	         for (var i=0;i<arrIds.length;i++) {
	        	 ids += ids.length>0? "," : "";
	        	 ids += arrIds[i];
	         }
	         var winOpts={
	             url:"berry/prod/qc/qcTaskDNA/editBatch/editBatch",
	             title:"DNA QC: 批量录入结果.."
	         };
	         openWindow(winOpts,{ "IDS":ids });
         });
      }
     
     var editInfoBatchJT=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行批量修改!");
             return ;
         }
         editInfoBatchJT_Win(arrIds);
      }
     var editInfoBatchJT3=function(){
         var arrIds=getSelectData(tablesGrid3);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行批量修改!");
             return ;
         }
         editInfoBatchJT_Win(arrIds);
      }
     var editInfoBatchJT_Win=function(arrIds){
         confirmMsg("确认", "批量录入结果会批量覆盖所有选择数据，请谨慎操作", "warn", function() {
	         var ids = "";
	         for (var i=0;i<arrIds.length;i++) {
	        	 ids += ids.length>0? "," : "";
	        	 ids += arrIds[i];
	         }
	         var winOpts={
	             url:"berry/prod/qc/qcTaskDNA/editBatchJT/editBatch",
	             title:"DNA QC: 批量上传胶图.."
	         };
	         openWindow(winOpts,{ "IDS":ids });
         });
      }
     // 生成报告
     var generateReprotBatch=function(){
         var arrIds=getSelectData(tablesGrid3);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }
         var rData = getGridSelectData(tablesGrid3);
         var CONTRACT_NO_BD = "";
         for (var i=0; i<rData.length; i++) {
        	 var CONTRACT_NO = rData[i].CONTRACT_NO;
        	 if (CONTRACT_NO_BD == "") {
        		 CONTRACT_NO_BD = CONTRACT_NO;
        	 } else if (CONTRACT_NO_BD != CONTRACT_NO) {
        		 alertMsg("只能选择同一个合同下的样本合并生成报告!");
                 return ;
        	 }
         }
         
         confirmMsg("确认", "确定将选中的数据合并生成报告吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/submitToYSC";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid3();
  		            	refreshGrid4();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "editInfoBatch":editInfoBatch,
         "editInfoBatchJT":editInfoBatchJT,
         "editInfo3":editInfo3,
         "editInfoBatch3":editInfoBatch3,
         "editInfoBatchJT3":editInfoBatchJT3,
         "generateReprotBatch":generateReprotBatch,
         "refreshGrid":refreshGrid,
         "refreshGrid2":refreshGrid2,
         "refreshGrid3":refreshGrid3,
         "refreshGrid4":refreshGrid4,
         "callBack":callBack,
         "deleteInfo":deleteInfo,
         "submitToDSC":submitToDSC,
         "submitToDFS":submitToDFS,
         "qcReprotDelete":qcReprotDelete,
     });
});
