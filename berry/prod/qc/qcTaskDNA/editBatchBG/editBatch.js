$(document).ready(function() {
    var pathValue="berry-prod-qc-qcTaskDNA-editBatchBG-editBatch";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_DNA_RNA_QC"
        };
    }
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
    }
    
    var submit = function(){
    	confirmMsg("确认", "批量录入结果会批量覆盖所有选择数据，请谨慎操作", "warn", function() {
	        formSubmit({
//	            url:"system/jdbc/save/one/table",
	            url:"berry/prod/qc/qcTask/editBatch",
	            formId:"form",
	            pathValue:pathValue,
	            succeed:function(result){
	                if(result["code"]>0){
	                    //提交成功
	                    alertMsg("提交成功","success",function(){
	                        funcExce(pathValue+"pageCallBack");//执行回调
	                        funcExce(pathValue+"close");//关闭页面
	                    });
	                }else{
	                    alertMsg("提交失败","error");
	                }
	            }
	        });
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 