$(document).ready(function() {
   var pathValue="berry-prod-qc-qcTaskRNAManage-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;
   var tablesGrid3;
   var tablesGrid4;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"send",target:"sendReport", title:"发送报告"},
            {name:"submit",target:"submitToDFK", title:"确认已发送报告"},
            {name:"back",target:"backToJCZ", title:"退回检测"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "RNA", ["待发送"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"submit",target:"submitToYFK", title:"确认已反馈"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "RNA", ["待反馈"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar3=getButtonTemplates(pathValue,[
            {name:"submit",target:"submitFlow", title:"提交实验方案"},
        ]);//工具条
        //请求参数
        var tablesGridJson3={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar3,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "RNA", ["已反馈"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid3 = initKendoGrid("#tablesGrid3_"+pathValue,tablesGridJson3);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar4=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson4={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar4,
            height: fullh-122,
            read:{"query":"query_BR_DNA_RNA_QC_view","objects":[ "RNA", ["已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid4 = initKendoGrid("#tablesGrid4_"+pathValue,tablesGridJson4);//初始化表格的方法
   }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/qc/qcTaskRNA/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
    	 refreshGrid3();
    	 refreshGrid4();
     };
     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid2=function(){
         if(tablesGrid2){
             tablesGrid2.dataSource.read();//重新读取--刷新
         }
      }
      var refreshGrid3=function(){
         if(tablesGrid3){
             tablesGrid3.dataSource.read();//重新读取--刷新
         }
      }
      var refreshGrid4=function(){
         if(tablesGrid4){
             tablesGrid4.dataSource.read();//重新读取--刷新
         }
      }
      
      var backToJCZ=function(){
          var arrIds=getSelectData(tablesGrid);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据进行提交操作!");
              return ;
          }
          confirmMsg("确认", "确定要退回选中的数据吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/backToJCZ";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }
      var submitToDFK=function(){
          var arrIds=getSelectData(tablesGrid);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据进行提交操作!");
              return ;
          }
          confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/submitToDFK";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid();
  		            	refreshGrid2();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }
      var submitToYFK=function(){
          var arrIds=getSelectData(tablesGrid2);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据进行提交操作!");
              return ;
          }
          confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/submitToYFK";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid2();
  		            	refreshGrid3();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }
      var submitFlow=function(){
          var arrIds=getSelectData(tablesGrid3);
          if(arrIds.length==0){
              alertMsg("请至少选择一条数据进行提交操作!");
              return ;
          }
          confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
  	        var params={"ids":arrIds};
  	        var url="berry/prod/qc/qcTask/submitFlow";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: params,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	refreshGrid3();
  		            	refreshGrid4();
  		                alertMsg("提示:操作成功!","success");
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
          });
      }
      
      var sendReport = function() {
    	  alertMsg("TODO","error");
      }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "callBack":callBack,//回调方法
         "refreshGrid":refreshGrid,
         "refreshGrid2":refreshGrid2,
         "refreshGrid3":refreshGrid3,
         "refreshGrid4":refreshGrid4,
         "backToJCZ":backToJCZ,
         "submitToDFK":submitToDFK,
         "submitToYFK":submitToYFK,
         "submitFlow":submitFlow,
         "sendReport":sendReport,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
