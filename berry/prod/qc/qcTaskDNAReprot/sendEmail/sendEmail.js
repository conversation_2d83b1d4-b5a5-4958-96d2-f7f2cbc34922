$(document).ready(function() {
    var pathValue="berry-prod-qc-qcTaskDNAReprot-sendEmail-sendEmail";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_SEND_EMAIL"
        };
    }

    var BUS_LINK_ID;
    var selectGridNO;
    var pPathValue;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	BUS_LINK_ID = params.BUS_LINK_ID;
    	selectGridNO = params.selectGridNO;
    	pPathValue = params.pPathValue;
    	
        getInfo("form",pathValue,params);
	}

    var submit = function() {
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	//执行发送邮件
                	sendEmail(result.ID);
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    //调用发送邮件功能
    var sendEmail = function(EMAIL_ID) {
    	if (selectGridNO==1) {//待发送列表, 执行打标
        	updateSendFlag();
    	} else {
    		alertMsg("发送邮件成功","success",function(){
                funcExce(pathValue+"close");//关闭页面
            });
    	}
    }
    //更新业务信息
    var updateSendFlag = function() {
        var params={"ids":[BUS_LINK_ID]};
        var url="berry/prod/qc/qcTask/submitToDFK";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	                //成功
	                alertMsg("发送邮件成功","success",function(){
	                    funcExce(pPathValue+"refreshGrid");
	                    funcExce(pPathValue+"refreshGrid2");
	                    funcExce(pathValue+"close");//关闭页面
	                });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 