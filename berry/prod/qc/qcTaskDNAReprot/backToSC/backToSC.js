$(document).ready(function() {
    var pathValue="berry-prod-qc-qcTaskDNAReprot-backToSC-backToSC";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return { };
    }

    var ids;
    var pPathValue;
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	ids = params.ids;
    	pPathValue = params.pPathValue;
    	
//        getInfo("form",pathValue,params);
//        // 传入数组ids
//        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
//        getInfo("form",pathValue,params,url);//传入id
    }
    
    var submit = function(){
    	
    	// 表单验证
    	var formJson = { formId:"form", pathValue:pathValue };
     	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
     	if ( !validator.validate() ) {
	         alertMsg("表单验证未通过","wran");
	         return false;
	    }
    	
    	var formparams = getJsonByForm("form", pathValue);
    	formparams.ids=ids;
    	
        var url="berry/prod/qc/qcTask/backToYSC";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: formparams,
            succeed:function(result){
	            if(result["code"]>0){
					funcExce(pPathValue+"callBack");//执行回调
	                funcExce(pathValue+"close");//关闭页面
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 