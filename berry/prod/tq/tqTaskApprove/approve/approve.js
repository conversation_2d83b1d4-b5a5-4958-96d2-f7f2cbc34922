$(document).ready(function() {
    var pathValue="berry-prod-tq-tqTaskApprove-approve-approve";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_TQ_INFO"
        };
    }
    
    var pPathValue;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	pPathValue = params.pPathValue;
    	
        getInfo("form1",pathValue,params);
        getInfo("form2",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form2",pathValue,params,url,function(viewModel, params){
        	$("#form1"+pathValue+" #ID"+pathValue).val(params.ID);
        	$("#form1"+pathValue+" #STATUS"+pathValue).val(params.STATUS);
        });
        
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: (fullh-141)/2,
            read:{"query":"query_BR_TQ_MX_INFO_view","objects":[params.ID]}
        };
        initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }
    
    var submit=function(){
    	//表单校验
    	var formJson = { formId:"form1", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","wran");
            return false;
        }
    	
    	var TASK_APPROVE_RS = $("#form1"+pathValue+" #TASK_APPROVE_RS"+pathValue).val();
    	if (TASK_APPROVE_RS=="审核通过") {
    		$("#form1"+pathValue+" #STATUS"+pathValue).val("进行中");
    	} else if (TASK_APPROVE_RS=="审核不通过") {
    		$("#form1"+pathValue+" #STATUS"+pathValue).val("退回");
    	} else {
    		alertMsg("审核结果输入错误","wran");
            return false;
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form1",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pPathValue+"callBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 