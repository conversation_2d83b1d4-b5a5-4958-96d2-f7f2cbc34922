$(document).ready(function() {
    var pathValue="berry-prod-tq-tqTask-TQAssign-TQAssign";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return { };
    }
    
    var TQ_ID;
    var TQ_MX_IDS;
    var pPathValue;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	TQ_ID = params.TQ_ID;
    	TQ_MX_IDS = params.TQ_MX_IDS;
    	pPathValue = params.pPathValue;
    	
    	if ( !TQ_MX_IDS || !pPathValue ) {
    		alertMsg("表单调用参数错误","wran");
            return false;
    	}
    	
 	    // 初始化开始时间: 当前时间
    	var nowDate = new Date();
    	var nowDateString = nowDate.getFullYear()+"-"+( nowDate.getMonth()+1 )+"-"+nowDate.getDate()+" "+nowDate.getHours()+":"+nowDate.getMinutes()+":"+nowDate.getSeconds();
    	params["START_DATE"] = nowDateString;
 	    // 赋值表单默认值
        getInfo("form",pathValue,params);
        // 根据开始时间: 计算结束时间
        calcCLOSING_DATE();
    }

    var submit=function(){
    	
    	if ( !TQ_MX_IDS || !pPathValue ) {
    		alertMsg("表单调用参数错误","wran");
            return false;
    	}
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","wran");
            return false;
        }
    	
    	var form_params = getJsonByForm("form",pathValue);
    	form_params.TQ_MX_IDS = TQ_MX_IDS;
    	
        var url="berry/prod/tq/tqTask/assign";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: form_params,
            succeed:function(result){
	            if(result["code"]>0){
	            	alertMsg("提交成功","success",function(){
                        funcExce(pPathValue+"searchGrid_D");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var calcCLOSING_DATE = function() {
    	var START_DATE = $.trim( $("#START_DATE"+pathValue).val() );
    	if (START_DATE) {
    		var sDate = new Date(START_DATE);
    		if ( sDate.getHours() >= 12 ) {
    			sDate.setDate( sDate.getDate()+1 );
    		}
			sDate.setHours(23);
			sDate.setMinutes(59);
			sDate.setSeconds(59);
			
			var sDateSTR = sDate.getFullYear();
			sDateSTR += "-"+( (sDate.getMonth()+1)<10 ? "0"+(sDate.getMonth()+1) : (sDate.getMonth()+1) );
			sDateSTR += "-"+( sDate.getDate()<10 ? "0"+sDate.getDate() : sDate.getDate() );
			sDateSTR += " "+sDate.getHours();
			sDateSTR += ":"+sDate.getMinutes();
			sDateSTR += ":"+sDate.getSeconds();
			$("#CLOSING_DATE"+pathValue).val(sDateSTR);
    	} else {
    		$("#CLOSING_DATE"+pathValue).val("");
    	}
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "calcCLOSING_DATE":calcCLOSING_DATE,
    });
 
 });
 