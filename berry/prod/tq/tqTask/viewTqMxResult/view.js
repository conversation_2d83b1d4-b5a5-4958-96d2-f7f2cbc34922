$(document).ready(function() {
   var pathValue="berry-prod-tq-tqTask-viewTqMxResult-view";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
	   var TQ_MX_ID = params.TQ_MX_ID;
	   var dataScope = params.dataScope;
	   var objectsArgs = [];
	   if (dataScope == 1) {
		   objectsArgs[0] = "待提取";
		   objectsArgs[1] = "已提取";
		   objectsArgs[2] = "终止";
	   } else if (dataScope == 2) {
		   objectsArgs[0] = "待提取";
	   } else if (dataScope == 3) {
		   objectsArgs[1] = "已提取";
	   } else if (dataScope == 4) {
		   objectsArgs[2] = "终止";
	   }
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[ ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-146,
            read:{"query":"query_TQ_MX_view_RESULT","objects":[ TQ_MX_ID, objectsArgs ]}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
