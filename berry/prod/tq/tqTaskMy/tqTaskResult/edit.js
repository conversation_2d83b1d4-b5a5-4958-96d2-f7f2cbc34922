$(document).ready(function() {
    var pathValue="berry-prod-tq-tqTaskMy-tqTaskResult-edit";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_TQ_MX_RESULT"
        };
    }

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params
     */
    var init=function(params){

 	    // 初始化开始时间: 当前时间
    	var nowDate = new Date();
    	var nowDateString = nowDate.getFullYear()+"-"+( nowDate.getMonth()+1 )+"-"+nowDate.getDate()+" "+nowDate.getHours()+":"+nowDate.getMinutes()+":"+nowDate.getSeconds();
    	params["TQ_CLOSING_TIME"] = nowDateString;
    	
        getInfo("form",pathValue,params);
//        // 传入数组ids
//        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
//        getInfo("form",pathValue,params,url);//传入id
    }

    var submit = function(){
        formSubmit({
//            url:"system/jdbc/save/one/table",
            url:"berry/prod/tq/tqTaskMy/saveTqTaskResult",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 