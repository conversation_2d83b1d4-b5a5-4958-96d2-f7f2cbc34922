$(document).ready(function() {
   var pathValue="berry-prod-tq-tqTaskMy-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid_M;
   
   var tablesGrid_M2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar_M=getButtonTemplates(pathValue,[
            {name:"edit",target:"editTqTaskResult", title:"填写结果"},
            {name:"edit",target:"editTqTaskResultBatch", title:"批量填写结果"},
            {name:"edit",target:"setDNA_RNA_Code", title:"生成核酸编号"},
            {name:"submit",target:"submitFlow", title:"已提取完成"},
            // {name:"stop",target:"tqTaskStop", title:"终止任务"},//改为终止方案实现,统一由客户发起终止
        ]);//工具条
        //请求参数
        var tablesGridJson_M={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M,
            height: fullh-141,
            read:{"query":"query_BR_TQ_MX_RESULT_view","objects":[ ["待提取"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_M = initKendoGrid("#tablesGrid_M_"+pathValue,tablesGridJson_M);//初始化表格的方法
        
        /**
         * 列表-按钮-定义
         */
        var toolbar_M2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_M2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M2,
            height: fullh-141,
            read:{"query":"query_BR_TQ_MX_RESULT_view","objects":[ ["已提取", "终止"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid_M2 = initKendoGrid("#tablesGrid_M2_"+pathValue,tablesGridJson_M2);//初始化表格的方法
   }
     
     var callBack=function(){
        refreshGrid_M();
        refreshGrid_M2();
     };

     var refreshGrid_M=function(){
        if(tablesGrid_M){
            tablesGrid_M.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid_M2=function(){
        if(tablesGrid_M2){
            tablesGrid_M2.dataSource.read();//重新读取--刷新
        }
     }

     var editTqTaskResult=function(){
    	 var arrIds=getSelectData(tablesGrid_M);
    	 if (arrIds.length==0) {
    		 alertMsg("请至少选择一条数据!");
             return ;
    	 } else if (arrIds.length>1) {
    		 alertMsg("只能选择一条数据!");
             return ;
    	 }
    	 var IDS = "";
    	 for (var i=0; i<arrIds.length; i++) {
    		 IDS += IDS.length>0 ? "," : "";
    		 IDS += arrIds[i];
    	 }
        var winOpts={
            url:"berry/prod/tq/tqTaskMy/tqTaskResult/edit",
            title:"提取任务: 填写提取结果.."
        };
        var dialog = openWindow(winOpts,{ "IDS": IDS });
     }
     var editTqTaskResultBatch=function(){
    	 var arrIds=getSelectData(tablesGrid_M);
    	 if (arrIds.length==0) {
    		 alertMsg("请至少选择一条数据!");
             return ;
    	 }
    	 var IDS = "";
    	 for (var i=0; i<arrIds.length; i++) {
    		 IDS += IDS.length>0 ? "," : "";
    		 IDS += arrIds[i];
    	 }
        var winOpts={
            url:"berry/prod/tq/tqTaskMy/tqTaskResult/edit",
            title:"提取任务: 填写提取结果.."
        };
        var dialog = openWindow(winOpts,{ "IDS": IDS });
     }
     
     var setDNA_RNA_Code=function(){
    	 var arrIds=getSelectData(tablesGrid_M);
    	 if (arrIds.length==0) {
    		 alertMsg("请至少选择一条数据!");
             return ;
    	 }
         confirmMsg("提示","确定生成选中提取任务的核酸编号吗?","question",function(){
             var url="berry/prod/tq/tqTaskMy/setDNA_RNA_Code";
             var params={"ids":arrIds};
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	callBack();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
     }
     
     var submitFlow=function(){
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据执行提交操作!");
             return ;
         }
         confirmMsg("提示","确定提交选中的提取任务吗?","question",function(){
             var url="berry/prod/tq/tqTaskMy/submitFlow";
             var params={"ids":arrIds};
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	callBack();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
     }

     var tqTaskStop=function(){
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据退回操作!");
             return ;
         }
         confirmMsg("提示","确定终止选中的提取任务吗?","question",function(){
	         var params={"ids":arrIds};
	         var url="berry/prod/tq/tqTaskMy/tqTaskStop";
	         $.fn.ajaxPost({
	             ajaxUrl: url,
	             ajaxType: "post",
	             ajaxData: params,
	             succeed:function(result){
	 	            if(result["code"]>0){
	 	            	callBack();
	 	                alertMsg("提示:操作成功!","success");
	 	            }else{
	 	                alertMsg("提示:操作失败!","error");
	 	            }
	             },
	             failed:function(result){
	                 alertMsg("提示:操作异常!","error");
	             }
	         });
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refreshGrid_M":refreshGrid_M,
         "refreshGrid_M2":refreshGrid_M2,
         "callBack":callBack,//回调方法
         "editTqTaskResult":editTqTaskResult,
         "editTqTaskResultBatch":editTqTaskResultBatch,
         "setDNA_RNA_Code":setDNA_RNA_Code,
         "submitFlow":submitFlow,
         "tqTaskStop":tqTaskStop,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
