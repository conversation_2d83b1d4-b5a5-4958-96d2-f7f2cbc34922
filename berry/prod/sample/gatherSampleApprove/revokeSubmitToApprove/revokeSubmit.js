$(document).ready(function() {
    var pathValue="berry-prod-sample-gatherSampleApprove-revokeSubmitToApprove-revokeSubmit";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return { };
    }
    
    var IDS;
    var pPathValue;
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	IDS = params.IDS;
    	pPathValue = params.pPathValue;
    }
    
    var submit = function() {
    	
    	//表单校验
    	var formJson = { formId:"form", pathValue:pathValue };
    	var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    	if ( !validator.validate() ) {
            alertMsg("表单验证未通过","wran");
            return false;
        }
        confirmMsg("确认", "确定要撤回选中的数据吗?", "warn", function() {
        	var formparams = getJsonByForm("form", pathValue);
        	formparams["ids"]=IDS;
  	    	var url= "berry/prod/sample/sample/revokeSubmitToApprove";
  	        $.fn.ajaxPost({
  	            ajaxUrl: url,
  	            ajaxType: "post",
  	            ajaxData: formparams,
  	            succeed:function(result){
  		            if(result["code"]>0){
  		            	//提交成功
  	                    alertMsg("撤回成功","success",function(){
 	                    	funcExce(pPathValue+"refreshGrid1");
 	                    	funcExce(pPathValue+"refreshGrid2");
 		                    funcExce(pathValue+"close");//关闭页面
  	                    });
  		            }else{
  		                alertMsg("提示:操作失败!","error");
  		            }
  	            },
  	            failed:function(result){
  	                alertMsg("提示:操作异常!","error");
  	            }
  	        });
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 