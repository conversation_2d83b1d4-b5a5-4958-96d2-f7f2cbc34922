$(document).ready(function() {
   var pathValue="berry-prod-sample-gatherSampleApprove-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid1;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar1=getButtonTemplates(pathValue,[
            {name:"receive",target:"receiveSample",title: "确认收样"},
            {name:"delete",target:"revokeSubmitToApprove",title: "撤回草稿"},
        ]);//工具条
        //请求参数
        var tablesGridJson1={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar1,
           height: fullh-112,
            read:{"query":"query_BR_SAMPLE_INFO_BASIC_approve_view","objects":[ ["待审核", "部分已确认收样"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_SEND_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= SAMPLE_SEND_NO #","funcExce(\'"+pathValue+"openSampleInfoWin\',\'#= ID #\',\'#= SAMPLE_SEND_TYPE #\','view');","txt"));
                    }
                }
            }
        };
        tablesGrid1 = initKendoGrid("#tablesGrid1_"+pathValue,tablesGridJson1);//初始化表格的方法
        
        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"submit",target:"SMwfSubmit",title: "提交方案"},
            {name:"delete",target:"revokeSMwfSubmit",title: "撤回方案"},
            {name:"delete",target:"revokeToApprove",title: "撤回待审核"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-112,
            read:{"query":"query_BR_SAMPLE_INFO_BASIC_approve_view","objects":[ ["收样完成", "已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_SEND_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= SAMPLE_SEND_NO #","funcExce(\'"+pathValue+"openSampleInfoWin\',\'#= ID #\',\'#= SAMPLE_SEND_TYPE #\','view');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   var refreshGrid1=function(){
      if(tablesGrid1){
          tablesGrid1.dataSource.read();//重新读取--刷新
      }
   }
   var refreshGrid2=function(){
      if(tablesGrid2){
          tablesGrid2.dataSource.read();//重新读取--刷新
      }
   }
   var callBack=function(){
  	 refreshGrid1();
  	 refreshGrid2();
   };

    var openSampleInfoWin=function(ID, SAMPLE_SEND_TYPE, VIEW_MODE){
    	var winOpts;
    	if (SAMPLE_SEND_TYPE == "二代单上机文库信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addLibNgsOneRun/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "三代单上机文库信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addLibTgsOneRun/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "二代DNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addDnaNgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "二代RNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addRnaNgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "三代DNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addDnaTgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "三代RNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addRnaTgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "二代/三代待提取材料信息单") {
            winOpts={
                url:"berry/prod/sample/gatherSampleProject/addZzNgsTgs/add"
            };
    	} else if (SAMPLE_SEND_TYPE == "FFPE样品信息单") {
            winOpts={
                url:"berry/prod/sample/gatherSampleProject/addFFPE/add"
            };
    	}else if (SAMPLE_SEND_TYPE == "极速外显子建库测序样品信息单") {
            winOpts={
                url:"berry/prod/sample/gatherSampleProject/addTsdExonSq/add"
            };
    	}else if (SAMPLE_SEND_TYPE == "Bionano待提取样品信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addBionano/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "Hi-C待提取材料信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addHicSeq/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "10X单细胞建库测序样品信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addMonpSeq/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "10X Visium待提取材料信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addVisiumSeq/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "10X Visium空间转录组样品信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addVisium2Seq/add"
    		};
    	} else {
    		alertMsg("无法找到当前送检单类型对应的信息单模板", "error");
    	}
    	if (winOpts) {
    		if (VIEW_MODE == "add") {
    			winOpts.title = "新增: "+SAMPLE_SEND_TYPE+"..";
    			VIEW_MODE = "edit";
    		} else if (VIEW_MODE == "edit") {
    			winOpts.title = "编辑: "+SAMPLE_SEND_TYPE+"..";
    		} else {
    			winOpts.title = "查看: "+SAMPLE_SEND_TYPE+"..";
    			VIEW_MODE = "view";
    		}
        	var params = { "ID":ID, "pPathValue":pathValue, "VIEW_MODE":VIEW_MODE };
        	
        	winOpts.currUrl = pathValue;
			openWindow(winOpts, params);
		}
	}
     
     var revokeSubmitToApprove=function(){
         var arrIds=getSelectData(tablesGrid1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行撤回操作!");
             return ;
         }
         var winOpts={
    	     title:"提交送检单..",
             url:"berry/prod/sample/gatherSampleApprove/revokeSubmitToApprove/revokeSubmit",
             currUrl:pathValue
         };
     	 var params = { "IDS":arrIds, "pPathValue":pathValue };
 		 openWindow(winOpts, params);
      }
     var revokeToApprove=function(){
         var arrIds=getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行撤回操作!");
             return ;
         }
         confirmMsg("确认", "确定要撤回选中的数据吗?", "warn", function() {
 	        var params={"ids":arrIds};
 	    	var url= "berry/prod/sample/sample/revokeToApprove";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	//提交成功
 	                    alertMsg("撤回成功","success",function(){
 	                    	refreshGrid1();//执行回调
 	                    	refreshGrid2();//执行回调
 	                    });
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
      }

     var SMwfSubmit=function(){
         var arrIds=getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
 	        var params={"ids":arrIds};
 	    	var url= "berry/prod/sample/sample/SMwfSubmit";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	//提交成功
 	                    alertMsg("提交成功","success",function(){
 	                    	refreshGrid2();//执行回调
 	                    });
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
      }
     var revokeSMwfSubmit=function(){
         var arrIds=getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行撤回操作!");
             return ;
         }
         confirmMsg("确认", "确定要撤回选中的数据吗?", "warn", function() {
 	        var params={"ids":arrIds};
 	    	var url= "berry/prod/sample/sample/revokeSMwfSubmit";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	//提交成功
 	                    alertMsg("撤回成功","success",function(){
 	                    	refreshGrid2();//执行回调
 	                    });
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
      }
     
     var receiveSample=function(){
         var arrIds=getSelectData(tablesGrid1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据!");
             return ;
         }else if(arrIds.length!=1){
             alertMsg("请只选择一条数据!");
             return ;
         }
         var winOpts={
             url:"berry/prod/sample/gatherSampleApprove/receive/receive",
             title:"确认收样.."
         };
         var dialog = openWindow(winOpts,{"SIB_ID":arrIds[0], "pPathValue":pathValue });//传递id
     }
     
     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "openSampleInfoWin":openSampleInfoWin,
         "refreshGrid1":refreshGrid1,
         "refreshGrid2":refreshGrid2,
         "callBack":callBack,//回调方法
         "revokeSubmitToApprove":revokeSubmitToApprove,
         "revokeToApprove":revokeToApprove,
         "SMwfSubmit":SMwfSubmit,
         "revokeSMwfSubmit":revokeSMwfSubmit,
         "receiveSample":receiveSample,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
