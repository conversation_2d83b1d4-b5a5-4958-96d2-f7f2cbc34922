$(document).ready(function() {
    var pathValue="berry-prod-sample-putStorage-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
        	tableName: "BR_CONTAINER"
        };
    }

    var tablesGrid;
    
    var editID = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id

      var ORIFICE_PLATE_CODE = "";
        if (params) {
        	editID = params.ID;
        	ORIFICE_PLATE_CODE = params.CODE;
        }
        var toolbar=getButtonTemplates(pathValue,[
            {name:"addSampleOpen",target:"addSampleOpen",title:"样本选择"},
            {name:"deleteSample",target:"deleteSample", title:"删除"},
            {name:"clearOrifice",target:"clearOrifice",title:"清孔"},
            {name:"scanCode",target:"scanCode", title:"扫码"},
            {name:"importExcel",target:"importExcel", title:"导入样本"},
        ]);//工具条
        var tablesGridJson={
                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
                sort: "",//排序
                toolbar: toolbar,
                height: fullh-287,
                read:{"query":"query_BR_CONTAINER_ORIFICE_PLATE_putStorage_view","objects":[ORIFICE_PLATE_CODE]},
                headerFilter:function(cols,i){
//                    if(i){
//                        if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
//                            setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
//                        }
//                    }
                }
            };
            tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }
    
    var searchGrid = function() {
       var ORIFICE_PLATE_CODE = $("#form"+pathValue+" #CODE"+pathValue).val();
 	   var tablesGridJson = {
 	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
 	            sort: "",//排序
 	            toolbar: toolbar,
 	            height: fullh-287,
 	            read:{"query":"query_BR_CONTAINER_ORIFICE_PLATE_putStorage_view","objects":[ORIFICE_PLATE_CODE]},
 	            headerFilter:function(cols,i){
 	                if(i){
 	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
 	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
 	                    }
 	                }
 	            }
 	        };
 	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
 	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }
    var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

    var submit=function(){
//        formSubmit({
//            url:"system/jdbc/save/one/table",
//            formId:"form",
//            pathValue:pathValue,
//            succeed:function(result){
//                if(result["code"]>0){
//                    //提交成功
//                    alertMsg("提交成功","success",function(){
//                        funcExce(pathValue+"pageCallBack");//执行回调
//                        funcExce(pathValue+"close");//关闭页面
//                    });
//                }else{
//                    alertMsg("提交失败","error");
//                }
//            }
//        });
    }
    
    var deleteSample = function() {
    	
    }

    var clearOrifice = function() {
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行清孔操作!");
            return ;
        }
        confirmMsg("清孔确认", "确定要清孔选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/prod/sample/container/clearOrifice";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid();
		                alertMsg("提示:操作成功!","success");
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
    }
    
    var scanCode = function() {
    	
    }
    
    var importExcel = function() {
    	
    }
    
    /** 样本选择 */
    var addSampleOpen = function() {
    	var ORIFICE_PLATE_CODE = $("#form"+pathValue+" #CODE"+pathValue).val();
    	var RK_SX = $("#form"+pathValue+" #RK_SX"+pathValue).val();
    	if(!ORIFICE_PLATE_CODE && ORIFICE_PLATE_CODE == ''){
    		alertMsg("请选择储板编号（板号）!");
    		return;
    	}
    	var winOpts={
            url:"berry/prod/sample/putStorage/add/selectSample",
            title:"样本选择.."
        };
    	var dialog = openWindow(winOpts,{ "ORIFICE_PLATE_CODE":ORIFICE_PLATE_CODE,"RK_SX":RK_SX});
    }
    
    var selectPlateCode = function() {
    	if (!editID) {
            var winOpts={
                url:"berry/prod/sample/putStorage/add/plate",
                title:"选择板号.."
            };
            openWindow(winOpts);
    	}
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "searchGrid":searchGrid,
        "submit":submit,
        "deleteSample":deleteSample,
        "clearOrifice":clearOrifice,
        "scanCode":scanCode,
        "importExcel":importExcel,
        "addSampleOpen":addSampleOpen,
        "selectPlateCode":selectPlateCode,
    });
 
 });
 