
<style>
.listViewTemplateberry_prod_sample_putStorage_add_plate {
    float: left;
    position: relative;
    width: 46px;
    height: 46px;
    line-height:46px;
    padding: 0;
	cursor: pointer;
}
.listViewTemplateberry_prod_sample_putStorage_add_plate_img0 {
	background:url(/berry/prod/sample/container/cell_0_38.jpg) no-repeat 3px 3px;
}
.listViewTemplateberry_prod_sample_putStorage_add_plate_img1 {
	background:url(/berry/prod/sample/container/cell_1_38.jpg) no-repeat 3px 3px;
}
</style>

<div class="card">
    <div class="k-button theme-bg-m hkbutton" style="right: 35px;" onclick="∑close|this);return false;">关闭</div>
    <div class="card-body">
	    <div class="form-group row col-sm-12">
	        <div class="col-sm-5">
		    	<fieldset style="border: 0px;">
			    	<legend style="color: #63B9BE;">板信息</legend>
			    	<form id="form" page-form-key="74d159a9-f248-43bb-b635-ce9c5993ea89" class="form-horizontal"></form>
			    	<div style="width: 100%;" align="center">
				    	<button class="k-button" onclick="∑searchGrid|this);return false;">查 询</button>
				    	&nbsp;&nbsp;
			    		<button class="k-button" onclick="∑submit|this);return false;">新 增</button>
		    		</div>
		        </fieldset>
		    	<fieldset style="border: 0px;">
			    	<legend style="color: #63B9BE;">储板列表</legend>
				    <div id="tablesGrid"></div>
			    </fieldset>
	        </div>
	        <div class="col-sm-6" align="center">
		        <script type="text/x-kendo-tmpl" id="listViewTemplateberry-prod-sample-putStorage-add-plate">
    				<div class="listViewTemplateberry_prod_sample_putStorage_add_plate listViewTemplateberry_prod_sample_putStorage_add_plate_${IMG}" style="border:1px dashed red;" onclick="setS1TO9InputValuEberry_prod_sample_putStorage_add_plate('${ORIFICE_PLATE_CODE}','${ORIFICE_CODE}','${SAMPLE_CODE}','${SAMPLE_NAME}','${SAMPLE_TYPE}','${SAMPLE_SOURCE}','${SAMPLE_NUMBER}','${SAMPLE_CONCENTRATION}','${SAMPLE_VOLUME}');return;">
           				${ORIFICE_CODE}
   					</div>
				</script>
       			<fieldset style="border: 0px;">
			    	<legend style="color: #63B9BE;">储板图形</legend>
	       			<div id="listViewIDberry-prod-sample-putStorage-add-plate"></div>
	       			<br/>
		        	<div id="listViewberry-prod-sample-putStorage-add-plate" style="border:0px dashed red;"></div>
		        </fieldset>
	        </div>
       		<div class="col-sm-1">
       			<fieldset style="border: 0px;">
			    	<legend style="color: #63B9BE;">孔位信息</legend>
	        		<p>
	        			<label>板号:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S1"/>
	        		</p>
	        		<p>
	        			<label>孔号:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S2"/>
	        		</p>
	        		<p>
	        			<label>样本编号:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S3"/>
	        		</p>
	        		<p>
	        			<label>样本名称:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S4"/>
	        		</p>
	        		<p>
	        			<label>样本类型:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S5"/>
	        		</p>
	        		<p>
	        			<label>样本来源:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S6"/>
	        		</p>
	        		<p>
	        			<label>样本数量:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S7"/>
	        		</p>
	        		<p>
	        			<label>浓度:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S8"/>
	        		</p>
	        		<p>
	        			<label>体积:</label>
		        		<br/>
		        		<input class="k-textbox" style="width: 100px" type="text" id="S9"/>
	       			</p>
	       		</fieldset>
   			</div>
   			
	    </div>
    </div>
</div>