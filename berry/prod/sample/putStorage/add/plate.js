
var berry_prod_sample_putStorage_add_plate_pathValue = "berry-prod-sample-putStorage-add-plate";

$(document).ready(function() {
   var pathValue=berry_prod_sample_putStorage_add_plate_pathValue;

   var parentPathValue = "berry-prod-sample-putStorage-add-add";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"returnPlateCode",title:"确认选择"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-311,
            read:{"query":"query_BR_CONTAINER_putStorage_2_view","objects":[],
	            "search": { "CODE":"-","ORIFICE_PLATE_TYPE":"-","ORIFICE_PLATE_SIZE":"-","P_CODE":"-" }
	        },
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="CODE"){
                        setJsonParam(cols[i],"template",getTemplate("#= CODE #","intiListViewberry_prod_sample_putStorage_add_plate(\'#= CODE #\',\'#= ORIFICE_PLATE_SIZE #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("form",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-311,
	            read:{"query":"query_BR_CONTAINER_putStorage_2_view","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="CODE"){
	                        setJsonParam(cols[i],"template",getTemplate("#= CODE #","intiListViewberry_prod_sample_putStorage_add_plate(\'#= CODE #\',\'#= ORIFICE_PLATE_SIZE #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }

     var submit=function(){
//        formSubmit({
//            formId:"form",
//            pathValue:pathValue
//        });
         formSubmit({
             // url:"system/jdbc/save/one/table",
         	url:"berry/prod/sample/container/add",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                     //提交成功
                     alertMsg("新增储板成功","success",function(){
                    	 searchGrid();
                     });
                 }else{
                     alertMsg("新增储板失败","error");
                 }
             }
         });
    	 
     }

     var returnPlateCode=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请选择一条数据!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据!");
            return ;
        }
        
        var rData = getGridSelectData(tablesGrid);
        
        var code = rData[0].CODE;
        
        $("#form"+parentPathValue+" #CODE"+parentPathValue).val(code);
        
        funcExce(parentPathValue+"searchGrid");//关闭页面
        
        funcExce(pathValue+"close");//关闭页面
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "searchGrid":searchGrid,
         "submit":submit,//提交方法
         "returnPlateCode":returnPlateCode,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
// 

function intiListViewberry_prod_sample_putStorage_add_plate(code, gg) {
	var pathValuE = berry_prod_sample_putStorage_add_plate_pathValue;
	
	$("#listViewID"+pathValuE).html("");
	$("#listView"+pathValuE).html("");
	setS1TO9InputValuEberry_prod_sample_putStorage_add_plate("","","","","","","","","");
	
	var ss = gg.split("*");
	var x = Number(ss[0]);
	var y = Number(ss[1]);
	$.fn.ajaxPost({
		   ajaxUrl: "system/jdbc/query/one/table",
		   ajaxType: "post",
		   ajaxData: {"query":"query_BR_CONTAINER_ORIFICE_PLATE_listView","objects":[code]},
		   succeed: function(rs) {
	        	$("#listViewID"+pathValuE).html(code);
	            var dataSource = new kendo.data.DataSource({
	                data: rs.rows?rs.rows:[],
	                pageSize: rs.curr
	            });
	            
	            var w = 46 * x + 3 * (x - 1);
	            $("#listView"+pathValuE).css('width', w );
	            
	            $("#listView"+pathValuE).kendoListView({
	                dataSource: dataSource,
	                template: kendo.template($("#listViewTemplate"+pathValuE).html())
	            });
		   }
	   });
}
function setS1TO9InputValuEberry_prod_sample_putStorage_add_plate(s1,s2,s3,s4,s5,s6,s7,s8,s9) {
	var pathValuE = berry_prod_sample_putStorage_add_plate_pathValue;
	$("#S1"+pathValuE).val(s1 && s1!="null"?s1:"");
	$("#S2"+pathValuE).val(s2 && s2!="null"?s2:"");
	$("#S3"+pathValuE).val(s3 && s3!="null"?s3:"");
	$("#S4"+pathValuE).val(s4 && s4!="null"?s4:"");
	$("#S5"+pathValuE).val(s5 && s5!="null"?s5:"");
	$("#S6"+pathValuE).val(s6 && s6!="null"?s6:"");
	$("#S7"+pathValuE).val(s7 && s7!="null"?s7:"");
	$("#S8"+pathValuE).val(s8 && s8!="null"?s8:"");
	$("#S9"+pathValuE).val(s9 && s9!="null"?s9:"");
}