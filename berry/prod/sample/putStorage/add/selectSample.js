$(document).ready(function() {
    var pathValue="berry-prod-sample-putStorage-add-selectSample";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
        };
    }

    var tablesGrid;
    
    var editID = "";
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	var ORIFICE_PLATE_CODE = "";
        if (params) {
        	editID = params.ID;
        	ORIFICE_PLATE_CODE = params.CODE;
        }
        
        getInfo("form", pathValue, params);
        
        var toolbar=getButtonTemplates(pathValue,[
            {name:"returnSampleCodes",target:"returnSampleCodes",title:"确认选择"},
        ]);//工具条
        var tablesGridJson={
                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
                sort: "",//排序
                toolbar: toolbar,
                height: fullh-287,
                read:{"query":"query_BR_CONTAINER_ORIFICE_PLATE_selectSample_view","objects":[ORIFICE_PLATE_CODE]},
            };
            tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }
    
    var searchGrid = function() {
       var ORIFICE_PLATE_CODE = $("#form"+pathValue+" #CODE"+pathValue).val();
 	   var tablesGridJson = {
 	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
 	            sort: "",//排序
 	            toolbar: toolbar,
 	            height: fullh-287,
 	            read:{"query":"query_BR_CONTAINER_ORIFICE_PLATE_selectSample_view","objects":[ORIFICE_PLATE_CODE]},
 	            headerFilter:function(cols,i){
 	                if(i){
 	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
 	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
 	                    }
 	                }
 	            }
 	        };
 	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
 	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
    }
    var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
    
    /** 保存样本入库信息 */
    var returnSampleCodes = function() {
    	var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据!");
            return ;
        }
    	
    	var ORIFICE_PLATE_CODE = $("#form"+pathValue+" #ORIFICE_PLATE_CODE"+pathValue).val();
    	var RK_SX = $("#form"+pathValue+" #RK_SX"+pathValue).val();
        var rData = getGridSelectData(tablesGrid);//获取选中数据
        var url= "berry/prod/sample/sample/selectSample";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: {"RK_SX":RK_SX, "ORIFICE_PLATE_CODE":ORIFICE_PLATE_CODE, "SAMPLE_INFO":rData},
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            	
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "searchGrid":searchGrid,
        "returnSampleCodes":returnSampleCodes,
    });
 
 });
 