$(document).ready(function() {
   var pathValue="berry-prod-sample-ready-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /** "进行中"页签 */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"新增"},
            {name:"edit",target:"editInfo",title:"修改"},
    		{name:"delete",target:"deleteInfo",title:"删除"},
    		{name:"submit",target:"submitReady",title:"提交 "},
        ]);//工具条
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-122,
            read:{"query":"query_BR_SAMPLE_READY_views","objects":[["进行中"]]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        /** "已提交"页签 */
        var toolbar2=getButtonTemplates(pathValue,[
        	{name:"delete",target:"revokeReadyWfSubmit",title: "流程撤回"},
        ]);//工具条
        var tablesGridJson2={
        		url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
        		sort: "",//排序
        		toolbar: toolbar2,
        		height: fullh-122,
        		read:{"query":"query_BR_SAMPLE_READY_views","objects":[["已提交"]]},
        		headerFilter:function(cols,i){
        			if(i){
        				if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
        					setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
        				}
        			}
        		}
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-122,
	            read:{"query":"query_BR_SAMPLE_READY_views","objects":[["进行中"]],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   var searchGrid2 = function() {
	   var searchparams = getJsonByForm("searchform2_",pathValue);
	   var tablesGridJson = {
			   url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
			   sort: "",//排序
			   toolbar: toolbar,
			   height: fullh-122,
			   read:{"query":"query_BR_SAMPLE_READY_views","objects":[["已提交"]],
				   "search": searchparams
			   },
			   headerFilter:function(cols,i){
				   if(i){
					   if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
						   setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
					   }
				   }
			   }
	   };
	   setGridDataSource("#tablesGrid2_"+pathValue,tablesGridJson);
	   // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGridWindow = function() {
	   var searchparams = getJsonByForm("searchWindowForm",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-122,
	            read:{"query":"query_BR_SAMPLE_READY_views","objects":[["进行中"]],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchWindowOpen = function() {
	   $("#searchWindow").kendoWindow({
           width: "600px",
           height: "500px",
           title: "高级查询",
           visible: false,
           actions: [
               "Close"
           ]
       }).data("kendoWindow").center().open();
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/prod/sample/ready/add/add",
            title:"新增: 样品预处理.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/sample/ready/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
    
    /** 删除 */
    var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid);
        var msg = "";
        for (var i = 0; i < rData.length; i++) {
        	if (rData[i].Y_FLAG!='进行中') {
        		msg = "--";
        		break;
        	}
        }
        if(msg.length>0){
            alertMsg("只能删除“进行中”状态的数据");
            return ;
        }
        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/prod/sample/ready/del";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid();
		                alertMsg("提示:操作成功!","success");
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
     }

     var submitReady=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         var rData = getGridSelectData(tablesGrid);
         var msg = "";
         for (var i = 0; i < rData.length; i++) {
         	if (rData[i].Y_FLAG!='进行中') {
         		msg = "--";
         		break;
         	}
         }
         if(msg.length>0){
             alertMsg("只能提交“进行中”状态的数据");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
             var url="berry/workflow/submit/ready";
             var params={"objects":arrIds}
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	refreshGrid();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
      }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
        if(tablesGrid2){
        	tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid);
        var msg = "";
        for (var i = 0; i < rData.length; i++) {
        	if (rData[i].Y_FLAG!='进行中') {
        		msg = "--";
        		break;
        	}
        }
        if(msg.length>0){
            alertMsg("只能修改“进行中”状态的数据");
            return ;
        }
        var winOpts={
            url:"berry/prod/sample/ready/edit/edit",
            title:"编辑: 样品预处理.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }
     
     var revokeReadyWfSubmit=function(){
         var arrIds=getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据执行撤回操作!");
             return ;
         }
         confirmMsg("提示","确定对 "+arrIds.length+" 条数据执行撤回操作?","question",function(){
             var url="berry/workflow/revoke/ready";
             var params={"ids":arrIds};
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	refreshGrid();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submitReady":submitReady,//提交方法
         "searchGrid":searchGrid,
         "searchGrid2":searchGrid2,
         "callBack":callBack,//回调方法
         "searchGridWindow":searchGridWindow,
         "searchWindowOpen":searchWindowOpen,
         "revokeReadyWfSubmit":revokeReadyWfSubmit,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
