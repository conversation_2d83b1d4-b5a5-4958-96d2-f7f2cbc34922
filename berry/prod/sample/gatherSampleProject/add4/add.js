$(document).ready(function() {
    var pathValue="berry-prod-sample-gatherSampleProject-add4-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName1:"BR_SAMPLE_INFO_BASIC",
            tableName2:"BR_SAMPLE_INFO_CLASSIFY",
            tableName3:"BR_SAMPLE_INFO_DETAIL"
        };
    }
    
//    var tablesGrid2;// 组织-Illumina/Pacbio平台
//    var tablesGrid3;// 组织-Bionano平台
//    var tablesGrid4;// Illumina-gDNA
//    var tablesGrid5;// Pacbio-gDNA
//    var tablesGrid6;// RNA
//    var tablesGrid7;// Illumina文库
//    var tablesGrid8;// Pacbio文库
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        
        tablesGrid4_init();
        
        // 以下编辑模式才会执行
        if ( params ) {
            getInfo("form1",pathValue,params);// 基本信息
            getInfo("form4",pathValue,params);// Illumina-gDNA
        }
        if ( params && params.ID) {// 初始化基本信息form
			var url="system/jdbc/query/info/"+initData().tableName1;//后端请求路径
			getInfo("form1",pathValue,params,url);//传入id
        }
        if ( params && params.BIC_ID_ARRAY) {// 初始化样本信息form2-8
			var url="system/jdbc/query/info/"+initData().tableName2;//后端请求路径
			getInfo_bic_form("form4",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM4, "BIC_FORM_INDEX":4 } ,url);
        }
    }
    
    var getInfo_bic_form = function(obj,pathValue,params,url) {
        $.fn.ajaxPost({
            ajaxUrl:url,
            ajaxData:params,
            ajaxType:"post",
            succeed:function(res){
                if(res["code"]>0){
                    var jsonData=res["info"];
                    if(jsonData){
                        if(isObj(jsonData)){
                            //数据回填
                            getInfo(obj,pathValue,$.extend({}, jsonData, params));//表单回填
                            
                            // 获取样本明细
                            getInfo_bic_form_detail(params.BIC_FORM_INDEX);
                            
                            return;
                        }
                    }
                    getInfo(obj,pathValue,params);//表单回填
                }else{
                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
                }
            },
            failed:function(res){
                alertMsg("网络请求出现异常!","error");
            }
        });
    }
    var getInfo_bic_form_detail = function(BIC_FORM_INDEX) {
    	var form_id = "form"+BIC_FORM_INDEX+pathValue;
    	var tablesGrid_id = "tablesGrid"+BIC_FORM_INDEX+"_"+pathValue;
    	
    	var SIB_ID = $("#"+form_id+" #SIB_ID"+pathValue).val();
    	var SIC_ID = $("#"+form_id+" #ID"+pathValue).val();
    	
    	$.fn.ajaxPost({
    		ajaxUrl: "system/jdbc/query/one/table",
    		ajaxType: "post",
    		ajaxData: {"query":"query_BR_SAMPLE_INFO_DETAIL_editView","objects":[SIB_ID, SIC_ID]},
    		succeed: function(rs) {
    			if (rs && rs.rows) {
    				$("#"+tablesGrid_id).data("kendoGrid").setDataSource(new kendo.data.DataSource({ data: rs.rows ? rs.rows : [] }));
    			}
    		}
    	});
    	
    }
    
    var submit=function(){
    	
    	var form1_id = "form1"+pathValue;
    	
    	//表单校验
    	var form1Json = { formId:"form1", pathValue:pathValue };
    	var validator1 = $("#"+form1Json.formId+form1Json.pathValue).kendoValidator(getValidateJson(form1Json.validatorJson)).data("kendoValidator");
    	if ( !validator1.validate() ) {
            alertMsg("基本信息表单验证未通过","wran");
            return false;
        }
    	//Illumina-gDNA
		var form4Json = { formId:"form4", pathValue:pathValue };
    	var validator4 = $("#"+form4Json.formId+form4Json.pathValue).kendoValidator(getValidateJson(form4Json.validatorJson)).data("kendoValidator");
    	if ( !validator4.validate() ) {
            alertMsg("Illumina-gDNA表单验证未通过","wran");
            return false;
        }
    	//验证grid
    	var grid4Data = $("#tablesGrid4_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
    	if (grid4Data.length<1) {
    		alertMsg("提示:Illumina-gDNA没有样本明细!");
            return false;
    	}
    	var grid4Msg = null;
    	for (var i=0; i<grid4Data.length; i++) {
    		var SAMPLE_NAME = grid4Data[i].SAMPLE_NAME;//样本名称
    		
    		grid4Data[i].SEQ_TYPE = grid4Data[i].SEQ_TYPE ? (grid4Data[i].SEQ_TYPE["value"]!=undefined ? grid4Data[i].SEQ_TYPE["value"] : grid4Data[i].SEQ_TYPE) : "";
    		var SEQ_TYPE = grid4Data[i].SEQ_TYPE;//测序类型
    		
    		var CONCENTRATION = grid4Data[i].CONCENTRATION;//浓度(ng/ul)
    		var QUANTIFY_METHOD = grid4Data[i].QUANTIFY_METHOD;//定量方法
    		var VOLUME = grid4Data[i].VOLUME;//体积
    		var OD260_280 = grid4Data[i].OD260_280;//OD260/280
    		
    		grid4Data[i].PREPARATION_TIME = grid4Data[i].PREPARATION_TIME ? kendo.toString(kendo.parseDate(grid4Data[i].PREPARATION_TIME), 'yyyy-MM-dd HH:mm:ss') : grid4Data[i].PREPARATION_TIME;
    		var PREPARATION_TIME = grid4Data[i].PREPARATION_TIME;//制备时间
    		
    		var SAMPLE_SPECIES = grid4Data[i].SAMPLE_SPECIES;//物种
    		
    		grid4Data[i].CREATTIME = grid4Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid4Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid4Data[i].CREATTIME;
    		grid4Data[i].LASTUPDATETIME = grid4Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid4Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid4Data[i].LASTUPDATETIME;
    		
    		if (!SAMPLE_NAME || !SAMPLE_NAME) {
    			grid4Msg = "未填必填项";
    			break;
    		}
    	}
    	if (grid4Msg) {
    		alertMsg("提示:Illumina-gDNA样本明细,XXX字段必填!");
            return false;
    	}
    	
    	$("#"+form1_id+" #SAMPLE_SOURCE"+pathValue).val("MIS");// MIS/CSS
    	$("#"+form1_id+" #SAMPLE_TYPE"+pathValue).val("DNA");// 送样类型
    	$("#"+form1_id+" #SEQ_PLATFORM"+pathValue).val("Illumina");// 测序类型

    	confirmMsg("提示", "送样单数据格式较复杂保存较慢，在提示保存完成前，请勿重复点击保存按钮。", "warn", function() {
	        formSubmit({
	            url:"system/jdbc/save/one/table",
	            formId:"form1",
	            pathValue:pathValue,
	            succeed:function(result){
	                if(result["code"]>0){// alert(result.ID);
	                	$("#"+form1_id+" #ID"+pathValue).val(result.ID);
	                	$("#form4"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
	                	
	                	// 根据主单数据,保存子单
	                	//Illumina-gDNA
	                	submitCLASSIFY("4");
	                }else{
	                    alertMsg("提交失败","error");
	                }
	            }
	        });
    	});
    }
    //提交样本信息
    var submitCLASSIFY = function(ix) {
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form"+ix,
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	$("#form"+ix+pathValue+" #ID"+pathValue).val(result.ID);
                	
                	var SAVE_TYPE = ""+ix;
                	var SIB_ID = $("#form"+ix+pathValue+" #SIB_ID"+pathValue).val();
                	var SIC_ID = result.ID;
                	
                	var form1_id = "form1"+pathValue;
                	var SAMPLE_SEND_NO = $("#"+form1_id+" #SAMPLE_SEND_NO"+pathValue).val();
                	
                	var SEQ_PLATFORM = $("#form"+ix+pathValue+" #SEQ_PLATFORM"+pathValue).val();
                	var SAMPLE_TYPE = $("#form"+ix+pathValue+" #SAMPLE_TYPE"+pathValue).val();
                	var gridDataArray = $("#tablesGrid"+ix+"_"+pathValue).data("kendoGrid").dataSource.data();
                	
                	var params = { "SAVE_TYPE":SAVE_TYPE, "SIB_ID":SIB_ID, "SIC_ID":SIC_ID, "SAMPLE_SEND_NO":SAMPLE_SEND_NO, "SEQ_PLATFORM":SEQ_PLATFORM, "SAMPLE_TYPE":SAMPLE_TYPE, "DETAIL_ARRAY":gridDataArray };
                	submitDETAIL(params);
                	
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    //提交样本明细信息
    var submitDETAIL = function(params) {
    	var url= "berry/prod/sample/sample/saveSampleDetails";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var tablesGrid_addRow = function(index) {
    	var tablesGridID = "tablesGrid"+index+"_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	grid.addRow();
    	var gridData = grid.dataSource.data();
    	if (gridData.length>1) {
    		var newGridData = [];
    		for (var i = 0; i < gridData.length-1; i++) {
    			newGridData[i] = gridData[i+1];
    		}
    		newGridData[ gridData.length-1 ] = gridData[0];
    		grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    		grid.editRow( $("#"+tablesGridID+" tr:eq("+newGridData.length+")") );
    	}
    }
    var tablesGrid4_addRow = function() {
    	tablesGrid_addRow(4);
    }
    
    var tablesGrid_removeRow = function(index) {
    	var tablesGridID = "tablesGrid"+index+"_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	var selectRows = grid.select();
    	if (!selectRows || selectRows.length==0) {
    		return;
    	}
    	var selectRowsIndex = "";
    	$(selectRows).each(function(i, e){
    		selectRowsIndex += ","+this.rowIndex+",";
    	});
    	// 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	    	var gridData = grid.dataSource.data();
			var newGridData = [];
			for (var i = 0; i < gridData.length; i++) {
				if (selectRowsIndex.indexOf(","+i+",") < 0) {
					newGridData[newGridData.length] = gridData[i];
				}
			}
			grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    	});
    }
    var tablesGrid4_removeRow = function() {
    	tablesGrid_removeRow(4);
    }
    
    var tablesGrid4_init = function(tablesGridData) {
    	var tGridId = "tablesGrid4_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100,hidden:true},
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100,hidden:true},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100,hidden:true},
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 0,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100,hidden:true},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100,hidden:true},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100,hidden:true},
    			  {title:"备注",field:"REMARK",type:"string",width:100,hidden:true},
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100},
    			  {title:"测序类型",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
	  			    			value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
    	                editor: function (container, options) {
    	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
    	                        .appendTo(container)
    	                        .kendoDropDownList({
    	                            dataSource: {
    	                                data: [
    	            	                    { text: '重测序', value: '重测序' },
    	            	                    { text: '外显子', value: '外显子' },
    	            	                    { text: 'Denovo测序', value: 'Denovo测序' },
    	            	                    { text: 'Mate pair PCR产物测序', value: 'Mate pair PCR产物测序' },
    	            	                    { text: 'ChIP-seq', value: 'ChIP-seq' },
    	            	                    { text: 'Bisulfite甲基化测序', value: 'Bisulfite甲基化测序' },
    	            	                    { text: '10x genomics', value: '10x genomics' },
    	            	                    { text: 'FFPE-人重测序', value: 'FFPE-人重测序' },
    	            	                    { text: 'FFPE-人外显子组', value: 'FFPE-人外显子组' },
    	            	                    { text: '宏基因组', value: '宏基因组' },
    	            	                    { text: '其他', value: '其他' }
    	                                ]
    	                            },
    	                            optionLabel: "",
    	                            dataValueField: 'value',
    	                            dataTextField: 'text'
    	                        });
    	                }
    			  }, // 测序类型/测序模式
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,
    	                editor: function (container, options) {
  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
  	                        .appendTo(container)
  	                        .kendoNumericTextBox({
  	                            decimals: 2,
  	                            min: 0 // , max: 3.00
  	                        });
  	                }
  			      },
      			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
      			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,
      				  template:function (dataItem) {
			    			var value="";
			    			if(dataItem && dataItem["PREPARATION_TIME"]){
			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
			    			return value;
	                	},
      	                editor: function (container, options) {
      	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
      	                        .appendTo(container)
      	                        .kendoDatePicker({
      	                            format: 'yyyy-MM-dd HH:mm:ss',
      	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
      	                            min: new Date(2000, 0, 1),
      	                            max: new Date()
      	                        });
      	                }
      			  },
      			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
          				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
          				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "tablesGrid4_addRow":tablesGrid4_addRow,
        "tablesGrid4_removeRow":tablesGrid4_removeRow,
    });
 
 });
 