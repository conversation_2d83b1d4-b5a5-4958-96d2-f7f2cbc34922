$(document).ready(function() {
    var pathValue="berry-prod-sample-gatherSampleProject-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName1:"BR_SAMPLE_INFO_BASIC",
            tableName2:"BR_SAMPLE_INFO_CLASSIFY",
            tableName3:"BR_SAMPLE_INFO_DETAIL"
        };
    }
    
//    var tablesGrid2;// 组织-Illumina/Pacbio平台
//    var tablesGrid3;// 组织-Bionano平台
//    var tablesGrid4;// Illumina-gDNA
//    var tablesGrid5;// Pacbio-gDNA
//    var tablesGrid6;// RNA
//    var tablesGrid7;// Illumina文库
//    var tablesGrid8;// Pacbio文库
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        
        tablesGrid2_init();
        tablesGrid3_init();
        tablesGrid4_init();
        tablesGrid5_init();
        tablesGrid6_init();
        tablesGrid7_init();
        tablesGrid8_init();
        
        // 以下编辑模式才会执行
        if ( params ) {
            getInfo("form1",pathValue,params);// 基本信息
            getInfo("form2",pathValue,params);// 组织-Illumina/Pacbio平台
            getInfo("form3",pathValue,params);// 组织-Bionano平台
            getInfo("form4",pathValue,params);// Illumina-gDNA
            getInfo("form5",pathValue,params);// Pacbio-gDNA
            getInfo("form6",pathValue,params);// RNA
            getInfo("form7",pathValue,params);// Illumina文库
            getInfo("form8",pathValue,params);// Pacbio文库
        }
        if ( params && params.ID) {// 初始化基本信息form
			var url="system/jdbc/query/info/"+initData().tableName1;//后端请求路径
			getInfo("form1",pathValue,params,url);//传入id
        }
        if ( params && params.BIC_ID_ARRAY) {// 初始化样本信息form2-8
			var url="system/jdbc/query/info/"+initData().tableName2;//后端请求路径
			
			var fieldset_info2_id = "fieldset_info2_"+pathValue;//组织-Illumina/Pacbio平台
	    	var fieldset_info3_id = "fieldset_info3_"+pathValue;//组织-Bionano平台
	    	var fieldset_info4_id = "fieldset_info4_"+pathValue;//Illumina-gDNA
	    	var fieldset_info5_id = "fieldset_info5_"+pathValue;//Pacbio-gDNA
	    	var fieldset_info6_id = "fieldset_info6_"+pathValue;//RNA
	    	var fieldset_info7_id = "fieldset_info7_"+pathValue;//Illumina文库
	    	var fieldset_info8_id = "fieldset_info8_"+pathValue;//Pacbio文库
			
			if (params.BIC_ID_ARRAY.BIC_ID_FORM2) {
				getInfo_bic_form("form2",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM2, "BIC_FORM_INDEX":2 } ,url);//传入id
				$("#"+fieldset_info2_id).show();
			}
			if (params.BIC_ID_ARRAY.BIC_ID_FORM3) {
				getInfo_bic_form("form3",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM3, "BIC_FORM_INDEX":3 } ,url);//传入id
				$("#"+fieldset_info3_id).show();
			}
			if (params.BIC_ID_ARRAY.BIC_ID_FORM4) {
				getInfo_bic_form("form4",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM4, "BIC_FORM_INDEX":4 } ,url);//传入id
				$("#"+fieldset_info4_id).show();
			}
			if (params.BIC_ID_ARRAY.BIC_ID_FORM5) {
				getInfo_bic_form("form5",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM5, "BIC_FORM_INDEX":5 } ,url);//传入id
				$("#"+fieldset_info5_id).show();
			}
			if (params.BIC_ID_ARRAY.BIC_ID_FORM6) {
				getInfo_bic_form("form6",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM6, "BIC_FORM_INDEX":6 } ,url);//传入id
				$("#"+fieldset_info6_id).show();
			}
			if (params.BIC_ID_ARRAY.BIC_ID_FORM7) {
				getInfo_bic_form("form7",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM7, "BIC_FORM_INDEX":7 } ,url);//传入id
				$("#"+fieldset_info7_id).show();
			}
			if (params.BIC_ID_ARRAY.BIC_ID_FORM8) {
				getInfo_bic_form("form8",pathValue, { "ID":params.BIC_ID_ARRAY.BIC_ID_FORM8, "BIC_FORM_INDEX":8 } ,url);//传入id
				$("#"+fieldset_info8_id).show();
			}
        }
    }
    
    var getInfo_bic_form = function(obj,pathValue,params,url) {
        $.fn.ajaxPost({
            ajaxUrl:url,
            ajaxData:params,
            ajaxType:"post",
            succeed:function(res){
                console.log("请求成功:=============res:");
                console.log(res);
                if(res["code"]>0){
                    var jsonData=res["info"];
                    if(jsonData){
                        if(isObj(jsonData)){
                            //数据回填
                            getInfo(obj,pathValue,$.extend({}, jsonData, params));//表单回填
                            
                            // 获取样本明细
                            getInfo_bic_form_detail(params.BIC_FORM_INDEX);
                            
                            return;
                        }
                    }
                    getInfo(obj,pathValue,params);//表单回填
                }else{
                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
                }
            },
            failed:function(res){
                alertMsg("网络请求出现异常!","error");
            }
        });
    }
    var getInfo_bic_form_detail = function(BIC_FORM_INDEX) {
    	var form_id = "form"+BIC_FORM_INDEX+pathValue;
    	var tablesGrid_id = "tablesGrid"+BIC_FORM_INDEX+"_"+pathValue;
    	
    	var SIB_ID = $("#"+form_id+" #SIB_ID"+pathValue).val();
    	var SIC_ID = $("#"+form_id+" #ID"+pathValue).val();
    	
    	$.fn.ajaxPost({
    		ajaxUrl: "system/jdbc/query/one/table",
    		ajaxType: "post",
    		ajaxData: {"query":"query_BR_SAMPLE_INFO_DETAIL_editView","objects":[SIB_ID, SIC_ID]},
    		succeed: function(rs) {
    			if (rs && rs.rows) {
    				$("#"+tablesGrid_id).data("kendoGrid").setDataSource(new kendo.data.DataSource({ data: rs.rows ? rs.rows : [] }));
    			}
    		}
    	});
    	
    }
    
    var SAVE_DETAIL_IDS = "";
    var submit=function(){
    	
//    	if (SAVE_DETAIL_IDS!="") {
//    		alertMsg("表单正在保存,请勿重复操作","wran");
//            return false;
//    	}
    	SAVE_DETAIL_IDS = "开始进入保存";
    	
    	var form1_id = "form1"+pathValue;
    	
    	var viewModel=funcExce(pathValue+"getViewModel.form1");
    	var SAMPLE_TYPE_OBJ=viewModel.get("SAMPLE_TYPE");
    	var SEQ_PLATFORM_OBJ=viewModel.get("SEQ_PLATFORM");
    	var SAMPLE_TYPE="";
    	var SEQ_PLATFORM="";
    	for(var index in SAMPLE_TYPE_OBJ){
    		if (SAMPLE_TYPE_OBJ[index]["value"]) {
    			SAMPLE_TYPE += ","+SAMPLE_TYPE_OBJ[index]["value"];
    		} else if (SAMPLE_TYPE_OBJ[index]) {
    			SAMPLE_TYPE += ","+SAMPLE_TYPE_OBJ[index];
    		}
    	}
    	for(var index in SEQ_PLATFORM_OBJ){
    		if (SEQ_PLATFORM_OBJ[index]["value"]) {
    			SEQ_PLATFORM += ","+SEQ_PLATFORM_OBJ[index]["value"];
    		} else if (SEQ_PLATFORM_OBJ[index]) {
    			SEQ_PLATFORM += ","+SEQ_PLATFORM_OBJ[index];
    		}
    	}
    	
    	//表单校验
    	var form1Json = { formId:"form1", pathValue:pathValue };
    	var validator1 = $("#"+form1Json.formId+form1Json.pathValue).kendoValidator(getValidateJson(form1Json.validatorJson)).data("kendoValidator");
    	if ( !validator1.validate() ) {
            alertMsg("基本信息表单验证未通过","wran");
            SAVE_DETAIL_IDS = "";
            return false;
        }
    	//组织-Illumina/Pacbio平台
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && ( SEQ_PLATFORM.indexOf("Illumina")>=0 || SEQ_PLATFORM.indexOf("Pacbio")>=0 ) ) {
    		var form2Json = { formId:"form2", pathValue:pathValue };
        	var validator2 = $("#"+form2Json.formId+form2Json.pathValue).kendoValidator(getValidateJson(form2Json.validatorJson)).data("kendoValidator");
        	if ( !validator2.validate() ) {
                alertMsg("组织-Illumina/Pacbio平台表单验证未通过","wran");
                SAVE_DETAIL_IDS = "";
                return false;
            }
        	//验证grid
        	var grid2Data = $("#tablesGrid2_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
        	if (grid2Data.length<1) {
        		alertMsg("提示:组织-Illumina/Pacbio平台没有样本明细!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
        	var grid2Msg = null;
        	for (var i=0; i<grid2Data.length; i++) {
        		var SAMPLE_NAME = grid2Data[i].SAMPLE_NAME;//样本名称
        		var SAMPLE_SPECIES = grid2Data[i].SAMPLE_SPECIES;//物种
        		var SAMPLE_NUMBER = grid2Data[i].SAMPLE_NUMBER;//样本数目
        		
        		grid2Data[i].SEQ_TYPE = grid2Data[i].SEQ_TYPE ? (grid2Data[i].SEQ_TYPE["value"]!=undefined ? grid2Data[i].SEQ_TYPE["value"] : grid2Data[i].SEQ_TYPE) : "";
        		var SEQ_TYPE = grid2Data[i].SEQ_TYPE;//测序类型
        		
        		var SAMPLE_STATUS = grid2Data[i].SAMPLE_STATUS;//样本状态
        		var STORAGE_CONDITIONS = grid2Data[i].STORAGE_CONDITIONS;//存放条件
        		var EXTRACT_OPINION = grid2Data[i].EXTRACT_OPINION;//提取意见
        		var REMARK = grid2Data[i].REMARK;//备注
        		
        		grid2Data[i].CREATTIME = grid2Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid2Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid2Data[i].CREATTIME;
        		grid2Data[i].LASTUPDATETIME = grid2Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid2Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid2Data[i].LASTUPDATETIME;
        		
        		if (!SAMPLE_NAME || !SAMPLE_NAME) {
        			grid2Msg = "未填必填项";
        			break;
        		}
        	}
        	if (grid2Msg) {
        		alertMsg("提示:组织-Illumina/Pacbio平台样本明细,XXX字段必填!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
    	}
    	//组织-Bionano平台
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Bionano")>=0) {
    		var form3Json = { formId:"form3", pathValue:pathValue };
        	var validator3 = $("#"+form3Json.formId+form3Json.pathValue).kendoValidator(getValidateJson(form3Json.validatorJson)).data("kendoValidator");
        	if ( !validator3.validate() ) {
                alertMsg("组织-Bionano平台表单验证未通过","wran");
                SAVE_DETAIL_IDS = "";
                return false;
            }
        	//验证grid
        	var grid3Data = $("#tablesGrid3_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
        	if (grid3Data.length<1) {
        		alertMsg("提示:组织-Bionano平台没有样本明细!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
        	var grid3Msg = null;
        	for (var i=0; i<grid3Data.length; i++) {
        		var SAMPLE_NAME = grid3Data[i].SAMPLE_NAME;//样本名称
        		var SAMPLE_SPECIES = grid3Data[i].SAMPLE_SPECIES;//物种
        		var SAMPLE_NUMBER = grid3Data[i].SAMPLE_NUMBER;//样本数目
        		
        		grid3Data[i].SEQ_TYPE = grid3Data[i].SEQ_TYPE ? (grid3Data[i].SEQ_TYPE["value"]!=undefined ? grid3Data[i].SEQ_TYPE["value"] : grid3Data[i].SEQ_TYPE) : "";
        		var SEQ_TYPE = grid3Data[i].SEQ_TYPE;//测序类型
        		
        		var SAMPLE_STATUS = grid3Data[i].SAMPLE_STATUS;//样本状态
        		var STORAGE_CONDITIONS = grid3Data[i].STORAGE_CONDITIONS;//存放条件
        		var EXTRACT_OPINION = grid3Data[i].EXTRACT_OPINION;//提取意见
        		var REMARK = grid3Data[i].REMARK;//备注
        		
        		grid3Data[i].CREATTIME = grid3Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid3Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid3Data[i].CREATTIME;
        		grid3Data[i].LASTUPDATETIME = grid3Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid3Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid3Data[i].LASTUPDATETIME;
        		
        		if (!SAMPLE_NAME || !SAMPLE_NAME) {
        			grid3Msg = "未填必填项";
        			break;
        		}
        	}
        	if (grid3Msg) {
        		alertMsg("提示:组织-Bionano平台样本明细,XXX字段必填!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
    	}
    	//Illumina-gDNA
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
    		var form4Json = { formId:"form4", pathValue:pathValue };
        	var validator4 = $("#"+form4Json.formId+form4Json.pathValue).kendoValidator(getValidateJson(form4Json.validatorJson)).data("kendoValidator");
        	if ( !validator4.validate() ) {
                alertMsg("Illumina-gDNA表单验证未通过","wran");
                SAVE_DETAIL_IDS = "";
                return false;
            }
        	//验证grid
        	var grid4Data = $("#tablesGrid4_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
        	if (grid4Data.length<1) {
        		alertMsg("提示:Illumina-gDNA没有样本明细!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
        	var grid4Msg = null;
        	for (var i=0; i<grid4Data.length; i++) {
        		var SAMPLE_NAME = grid4Data[i].SAMPLE_NAME;//样本名称
        		
        		grid4Data[i].SEQ_TYPE = grid4Data[i].SEQ_TYPE ? (grid4Data[i].SEQ_TYPE["value"]!=undefined ? grid4Data[i].SEQ_TYPE["value"] : grid4Data[i].SEQ_TYPE) : "";
        		var SEQ_TYPE = grid4Data[i].SEQ_TYPE;//测序类型
        		
        		var CONCENTRATION = grid4Data[i].CONCENTRATION;//浓度(ng/ul)
        		var QUANTIFY_METHOD = grid4Data[i].QUANTIFY_METHOD;//定量方法
        		var VOLUME = grid4Data[i].VOLUME;//体积
        		var OD260_280 = grid4Data[i].OD260_280;//OD260/280
        		
        		grid4Data[i].PREPARATION_TIME = grid4Data[i].PREPARATION_TIME ? kendo.toString(kendo.parseDate(grid4Data[i].PREPARATION_TIME), 'yyyy-MM-dd HH:mm:ss') : grid4Data[i].PREPARATION_TIME;
        		var PREPARATION_TIME = grid4Data[i].PREPARATION_TIME;//制备时间
        		
        		var SAMPLE_SPECIES = grid4Data[i].SAMPLE_SPECIES;//物种
        		
        		grid4Data[i].CREATTIME = grid4Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid4Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid4Data[i].CREATTIME;
        		grid4Data[i].LASTUPDATETIME = grid4Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid4Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid4Data[i].LASTUPDATETIME;
        		
        		if (!SAMPLE_NAME || !SAMPLE_NAME) {
        			grid4Msg = "未填必填项";
        			break;
        		}
        	}
        	if (grid4Msg) {
        		alertMsg("提示:Illumina-gDNA样本明细,XXX字段必填!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
    	}
    	//Pacbio-gDNA
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
    		var form5Json = { formId:"form5", pathValue:pathValue };
        	var validator5 = $("#"+form5Json.formId+form5Json.pathValue).kendoValidator(getValidateJson(form5Json.validatorJson)).data("kendoValidator");
        	if ( !validator5.validate() ) {
                alertMsg("Pacbio-gDNA表单验证未通过","wran");
                SAVE_DETAIL_IDS = "";
                return false;
            }
        	//验证grid
        	var grid5Data = $("#tablesGrid5_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
        	if (grid5Data.length<1) {
        		alertMsg("提示:Pacbio-gDNA没有样本明细!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
        	var grid5Msg = null;
        	for (var i=0; i<grid5Data.length; i++) {
        		var SAMPLE_NAME = grid5Data[i].SAMPLE_NAME;//样本名称
        		
        		grid5Data[i].SEQ_TYPE = grid5Data[i].SEQ_TYPE ? (grid5Data[i].SEQ_TYPE["value"]!=undefined ? grid5Data[i].SEQ_TYPE["value"] : grid5Data[i].SEQ_TYPE) : "";
        		var SEQ_TYPE = grid5Data[i].SEQ_TYPE;//测序类型
        		
        		var CONCENTRATION = grid5Data[i].CONCENTRATION;//浓度(ng/ul)
        		var QUANTIFY_METHOD = grid5Data[i].QUANTIFY_METHOD;//定量方法
        		var VOLUME = grid5Data[i].VOLUME;//体积
        		var OD260_280 = grid5Data[i].OD260_280;//OD260/280
        		
        		grid5Data[i].PREPARATION_TIME = grid5Data[i].PREPARATION_TIME ? kendo.toString(kendo.parseDate(grid5Data[i].PREPARATION_TIME), 'yyyy-MM-dd HH:mm:ss') : grid5Data[i].PREPARATION_TIME;
        		var PREPARATION_TIME = grid5Data[i].PREPARATION_TIME;//制备时间
        		
        		var SAMPLE_SPECIES = grid5Data[i].SAMPLE_SPECIES;//物种
        		
        		grid5Data[i].CREATTIME = grid5Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid5Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid5Data[i].CREATTIME;
        		grid5Data[i].LASTUPDATETIME = grid5Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid5Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid5Data[i].LASTUPDATETIME;
        		
        		if (!SAMPLE_NAME || !SAMPLE_NAME) {
        			grid5Msg = "未填必填项";
        			break;
        		}
        	}
        	if (grid5Msg) {
        		alertMsg("提示:Pacbio-gDNA样本明细,XXX字段必填!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
    	}
    	//RNA
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("RNA")>=0) {
    		var form6Json = { formId:"form6", pathValue:pathValue };
        	var validator6 = $("#"+form6Json.formId+form6Json.pathValue).kendoValidator(getValidateJson(form6Json.validatorJson)).data("kendoValidator");
        	if ( !validator6.validate() ) {
                alertMsg("RNA表单验证未通过","wran");
                SAVE_DETAIL_IDS = "";
                return false;
            }
        	//验证grid
        	var grid6Data = $("#tablesGrid6_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
        	if (grid6Data.length<1) {
        		alertMsg("提示:RNA没有样本明细!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
        	var grid6Msg = null;
        	for (var i=0; i<grid6Data.length; i++) {
        		var SAMPLE_NAME = grid6Data[i].SAMPLE_NAME;//样本名称
        		
        		grid6Data[i].SEQ_TYPE = grid6Data[i].SEQ_TYPE ? (grid6Data[i].SEQ_TYPE["value"]!=undefined ? grid6Data[i].SEQ_TYPE["value"] : grid6Data[i].SEQ_TYPE) : "";
        		var SEQ_TYPE = grid6Data[i].SEQ_TYPE;//测序类型
        		
        		var CONCENTRATION = grid6Data[i].CONCENTRATION;//浓度(ng/ul)
        		var QUANTIFY_METHOD = grid6Data[i].QUANTIFY_METHOD;//定量方法
        		var VOLUME = grid6Data[i].VOLUME;//体积
        		var OD260_280 = grid6Data[i].OD260_280;//OD260/280
        		
        		grid6Data[i].PREPARATION_TIME = grid6Data[i].PREPARATION_TIME ? kendo.toString(kendo.parseDate(grid6Data[i].PREPARATION_TIME), 'yyyy-MM-dd HH:mm:ss') : grid6Data[i].PREPARATION_TIME;
        		var PREPARATION_TIME = grid6Data[i].PREPARATION_TIME;//制备时间
        		
        		var SAMPLE_SPECIES = grid6Data[i].SAMPLE_SPECIES;//物种
        		
        		grid6Data[i].CREATTIME = grid6Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid6Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid6Data[i].CREATTIME;
        		grid6Data[i].LASTUPDATETIME = grid6Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid6Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid6Data[i].LASTUPDATETIME;
        		
        		if (!SAMPLE_NAME || !SAMPLE_NAME) {
        			grid6Msg = "未填必填项";
        			break;
        		}
        	}
        	if (grid6Msg) {
        		alertMsg("提示:RNA样本明细,XXX字段必填!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
    	}
    	//Illumina文库
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
    		var form7Json = { formId:"form7", pathValue:pathValue };
        	var validator7 = $("#"+form7Json.formId+form7Json.pathValue).kendoValidator(getValidateJson(form7Json.validatorJson)).data("kendoValidator");
        	if ( !validator7.validate() ) {
                alertMsg("Illumina文库表单验证未通过","wran");
                SAVE_DETAIL_IDS = "";
                return false;
            }
        	//验证grid
        	var grid7Data = $("#tablesGrid7_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
        	if (grid7Data.length<1) {
        		alertMsg("提示:Illumina文库没有样本明细!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
        	var grid7Msg = null;
        	for (var i=0; i<grid7Data.length; i++) {
        		var LIB_NAME = grid7Data[i].LIB_NAME;//文库名称
        		var LIB_SIZE = grid7Data[i].LIB_SIZE;//文库片段大小
        		var LIB_INDEX_CODE = grid7Data[i].LIB_INDEX_CODE;//INDEX编号
        		var INDEX_SEQ = grid7Data[i].INDEX_SEQ;//INDEX序列
        		
        		grid7Data[i].SEQ_TYPE = grid7Data[i].SEQ_TYPE ? (grid7Data[i].SEQ_TYPE["value"]!=undefined ? grid7Data[i].SEQ_TYPE["value"] : grid7Data[i].SEQ_TYPE) : "";
        		var SEQ_TYPE = grid7Data[i].SEQ_TYPE;//测序类型
        		
        		var DATA_NUM = grid7Data[i].DATA_NUM;//数据量(M)
        		
        		grid7Data[i].CREATTIME = grid7Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid7Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid7Data[i].CREATTIME;
        		grid7Data[i].LASTUPDATETIME = grid7Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid7Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid7Data[i].LASTUPDATETIME;
        		
        		if (!LIB_NAME || !LIB_NAME) {
        			grid7Msg = "未填必填项";
        			break;
        		}
        	}
        	if (grid7Msg) {
        		alertMsg("提示:Illumina文库样本明细,XXX字段必填!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
    	}
    	//Pacbio文库
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
    		var form8Json = { formId:"form8", pathValue:pathValue };
        	var validator8 = $("#"+form8Json.formId+form8Json.pathValue).kendoValidator(getValidateJson(form8Json.validatorJson)).data("kendoValidator");
        	if ( !validator8.validate() ) {
                alertMsg("Pacbio文库表单验证未通过","wran");
                SAVE_DETAIL_IDS = "";
                return false;
            }
        	//验证grid
        	var grid8Data = $("#tablesGrid8_"+pathValue).data("kendoGrid").dataSource.data();// 获取全部表格的数据
        	if (grid8Data.length<1) {
        		alertMsg("提示:Pacbio文库没有样本明细!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
        	var grid8Msg = null;
        	for (var i=0; i<grid8Data.length; i++) {
        		var LIB_NAME = grid8Data[i].LIB_NAME;//文库名称
        		var LIB_SIZE = grid8Data[i].LIB_SIZE;//文库片段大小
        		var LIB_INDEX_CODE = grid8Data[i].LIB_INDEX_CODE;//INDEX编号
        		var INDEX_SEQ = grid8Data[i].INDEX_SEQ;//INDEX序列
        		
        		grid8Data[i].SEQ_TYPE = grid8Data[i].SEQ_TYPE ? (grid8Data[i].SEQ_TYPE["value"]!=undefined ? grid8Data[i].SEQ_TYPE["value"] : grid8Data[i].SEQ_TYPE) : "";
        		var SEQ_TYPE = grid8Data[i].SEQ_TYPE;//测序类型
        		
        		var DATA_NUM = grid8Data[i].DATA_NUM;//数据量(M)
        		
        		grid8Data[i].CREATTIME = grid8Data[i].CREATTIME ? kendo.toString(kendo.parseDate(grid8Data[i].CREATTIME), 'yyyy-MM-dd HH:mm:ss') : grid8Data[i].CREATTIME;
        		grid8Data[i].LASTUPDATETIME = grid8Data[i].LASTUPDATETIME ? kendo.toString(kendo.parseDate(grid8Data[i].LASTUPDATETIME), 'yyyy-MM-dd HH:mm:ss') : grid8Data[i].LASTUPDATETIME;
        		
        		if (!LIB_NAME || !LIB_NAME) {
        			grid8Msg = "未填必填项";
        			break;
        		}
        	}
        	if (grid8Msg) {
        		alertMsg("提示:Pacbio文库样本明细,XXX字段必填!");
        		SAVE_DETAIL_IDS = "";
                return false;
        	}
    	}
    	$("#"+form1_id+" #SAMPLE_SOURCE"+pathValue).val("MIS");// MIS/CSS
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form1",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                	$("#"+form1_id+" #ID"+pathValue).val(result.ID);
                	$("#form2"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
                	$("#form3"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
                	$("#form4"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
                	$("#form5"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
                	$("#form6"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
                	$("#form7"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
                	$("#form8"+pathValue+" #SIB_ID"+pathValue).val(result.ID);
                	
                	// 根据主单数据,记录标识
                	//组织-Illumina/Pacbio平台
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && ( SEQ_PLATFORM.indexOf("Illumina")>=0 || SEQ_PLATFORM.indexOf("Pacbio")>=0 ) ) {
                		SAVE_DETAIL_IDS += "2,";
                	}
                	//组织-Bionano平台
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Bionano")>=0) {
                		SAVE_DETAIL_IDS += "3,";
                	}
                	//Illumina-gDNA
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
                		SAVE_DETAIL_IDS += "4,";
                	}
                	//Pacbio-gDNA
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
                		SAVE_DETAIL_IDS += "5,";
                	}
                	//RNA
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("RNA")>=0) {
                		SAVE_DETAIL_IDS += "6,";
                	}
                	//Illumina文库
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
                		SAVE_DETAIL_IDS += "7,";
                	}
                	//Pacbio文库
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
                		SAVE_DETAIL_IDS += "8,";
                	}
                	SAVE_DETAIL_IDS = SAVE_DETAIL_IDS.replace("开始进入保存", "");

                	// 根据主单数据,保存子单
                	//组织-Illumina/Pacbio平台
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && ( SEQ_PLATFORM.indexOf("Illumina")>=0 || SEQ_PLATFORM.indexOf("Pacbio")>=0 ) ) {
                		submitCLASSIFY("2");
                	}
                	//组织-Bionano平台
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Bionano")>=0) {
                		submitCLASSIFY("3");
                	}
                	//Illumina-gDNA
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
                		submitCLASSIFY("4");
                	}
                	//Pacbio-gDNA
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
                		submitCLASSIFY("5");
                	}
                	//RNA
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("RNA")>=0) {
                		submitCLASSIFY("6");
                	}
                	//Illumina文库
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
                		submitCLASSIFY("7");
                	}
                	//Pacbio文库
                	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
                		submitCLASSIFY("8");
                	}
//                    //提交成功
//                    alertMsg("提交成功","success",function(){
//                        funcExce(pathValue+"pageCallBack,";//执行回调
//                        funcExce(pathValue+"close");//关闭页面
//                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    //提交样本信息
    var submitCLASSIFY = function(ix) {
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form"+ix,
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){// alert(result.ID);
                	$("#form"+ix+pathValue+" #ID"+pathValue).val(result.ID);
                	
                	var SAVE_TYPE = ""+ix;
                	var SIB_ID = $("#form"+ix+pathValue+" #SIB_ID"+pathValue).val();
                	var SIC_ID = result.ID;
                	
                	var form1_id = "form1"+pathValue;
                	var SAMPLE_SEND_NO = $("#"+form1_id+" #SAMPLE_SEND_NO"+pathValue).val();
                	
                	var SEQ_PLATFORM = $("#form"+ix+pathValue+" #SEQ_PLATFORM"+pathValue).val();
                	var SAMPLE_TYPE = $("#form"+ix+pathValue+" #SAMPLE_TYPE"+pathValue).val();
                	var gridDataArray = $("#tablesGrid"+ix+"_"+pathValue).data("kendoGrid").dataSource.data();
                	
                	var params = { "SAVE_TYPE":SAVE_TYPE, "SIB_ID":SIB_ID, "SIC_ID":SIC_ID, "SAMPLE_SEND_NO":SAMPLE_SEND_NO, "SEQ_PLATFORM":SEQ_PLATFORM, "SAMPLE_TYPE":SAMPLE_TYPE, "DETAIL_ARRAY":gridDataArray };
                	submitDETAIL(params);
                	
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    //提交样本明细信息
    var submitDETAIL = function(params) {
    	var url= "berry/prod/sample/sample/saveSampleDetails";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	SAVE_DETAIL_IDS = SAVE_DETAIL_IDS.replace(params.SAVE_TYPE+",", "");
	            	if (SAVE_DETAIL_IDS.length==0) {// 判断所有明细是否提交完成
		            	//提交成功
	                    alertMsg("提交成功","success",function(){
	                        funcExce(pathValue+"pageCallBack");//执行回调
	                        funcExce(pathValue+"close");//关闭页面
	                    });
	            	}
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var tablesGrid_addRow = function(index) {
    	var tablesGridID = "tablesGrid"+index+"_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	grid.addRow();
    	var gridData = grid.dataSource.data();
    	if (gridData.length>1) {
    		var newGridData = [];
    		for (var i = 0; i < gridData.length-1; i++) {
    			newGridData[i] = gridData[i+1];
    		}
    		newGridData[ gridData.length-1 ] = gridData[0];
    		grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    		grid.editRow( $("#"+tablesGridID+" tr:eq("+newGridData.length+")") );
    	}
    }
    var tablesGrid2_addRow = function() {
    	tablesGrid_addRow(2);
    }
    var tablesGrid3_addRow = function() {
    	tablesGrid_addRow(3);
    }
    var tablesGrid4_addRow = function() {
    	tablesGrid_addRow(4);
    }
    var tablesGrid5_addRow = function() {
    	tablesGrid_addRow(5);
    }
    var tablesGrid6_addRow = function() {
    	tablesGrid_addRow(6);
    }
    var tablesGrid7_addRow = function() {
    	tablesGrid_addRow(7);
    }
    var tablesGrid8_addRow = function() {
    	tablesGrid_addRow(8);
    }
    
    var tablesGrid_removeRow = function(index) {
    	var tablesGridID = "tablesGrid"+index+"_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	var selectRows = grid.select();
    	if (!selectRows || selectRows.length==0) {
    		return;
    	}
    	var selectRowsIndex = "";
    	$(selectRows).each(function(i, e){
    		selectRowsIndex += ","+this.rowIndex+",";
    	});
    	// 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	    	var gridData = grid.dataSource.data();
			var newGridData = [];
			for (var i = 0; i < gridData.length; i++) {
				if (selectRowsIndex.indexOf(","+i+",") < 0) {
					newGridData[newGridData.length] = gridData[i];
				}
			}
			grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    	});
    }
    var tablesGrid2_removeRow = function() {
    	tablesGrid_removeRow(2);
    }
    var tablesGrid3_removeRow = function() {
    	tablesGrid_removeRow(3);
    }
    var tablesGrid4_removeRow = function() {
    	tablesGrid_removeRow(4);
    }
    var tablesGrid5_removeRow = function() {
    	tablesGrid_removeRow(5);
    }
    var tablesGrid6_removeRow = function() {
    	tablesGrid_removeRow(6);
    }
    var tablesGrid7_removeRow = function() {
    	tablesGrid_removeRow(7);
    }
    var tablesGrid8_removeRow = function() {
    	tablesGrid_removeRow(8);
    }
    
    var tablesGrid2_init = function(tablesGridData) {
    	var tGridId = "tablesGrid2_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100,hidden:true},
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100,hidden:true},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			  },
    			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,hidden:true,
      				  template:function (dataItem) {
			    			var value="";
			    			if(dataItem && dataItem["PREPARATION_TIME"]){
			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
			    			return value;
	                	},
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoDatePicker({
    	                            format: 'yyyy-MM-dd HH:mm:ss',
    	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
    	                            min: new Date(2000, 0, 1),
    	                            max: new Date()
    	                        });
    	                }
    			  },
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100,hidden:true},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100,hidden:true},
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100},
    			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100},
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,
    	                editor: function (container, options) {
  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
  	                        .appendTo(container)
  	                        .kendoNumericTextBox({
  	                            decimals: 0,
  	                            min: 0 // , max: 3.00
  	                        });
  	                }
  			      },
    			  {title:"测序类型",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
  			    				value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
    	                editor: function (container, options) {
    	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
    	                        .appendTo(container)
    	                        .kendoDropDownList({
    	                            dataSource: {
    	                                data: [
    	            	                    { text: 'Illumina-gDNA', value: 'Illumina-gDNA' },
    	            	                    { text: '重测序', value: '重测序' },
    	            	                    { text: '外显子', value: '外显子' },
    	            	                    { text: 'Bisulfite甲基化测序', value: 'Bisulfite甲基化测序' },
    	            	                    { text: 'Mate pair-2K-5K', value: 'Mate pair-2K-5K' },
    	            	                    { text: 'Mate pair-8K-14K', value: 'Mate pair-8K-14K' },
    	            	                    { text: 'Denovo测序', value: 'Denovo测序' },
    	            	                    { text: '其他', value: '其他' }
    	                                ]
    	                            },
    	                            optionLabel: "",
    	                            dataValueField: 'value',
    	                            dataTextField: 'text',
    	                            index:1
    	                        });
    	                }
    			  }, // 测序类型/测序模式
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100},
    			  {title:"备注",field:"REMARK",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
        				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
        				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,// popup inline true
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    var tablesGrid3_init = function(tablesGridData) {
    	var tGridId = "tablesGrid3_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100,hidden:true},
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100,hidden:true},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			  },
    			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,hidden:true,
      				  template:function (dataItem) {
			    			var value="";
			    			if(dataItem && dataItem["PREPARATION_TIME"]){
			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
			    			return value;
	                	},
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoDatePicker({
    	                            format: 'yyyy-MM-dd HH:mm:ss',
    	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
    	                            min: new Date(2000, 0, 1),
    	                            max: new Date()
    	                        });
    	                }
    			  },
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100,hidden:true},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100,hidden:true},
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100},
    			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100},
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,
    	                editor: function (container, options) {
  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
  	                        .appendTo(container)
  	                        .kendoNumericTextBox({
  	                            decimals: 0,
  	                            min: 0 // , max: 3.00
  	                        });
  	                }
  			      },
    			  {title:"测序类型",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
	  			    			value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
    	                editor: function (container, options) {
    	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
    	                        .appendTo(container)
    	                        .kendoDropDownList({
    	                            dataSource: {
    	                                data: [
    	            	                    { text: 'Illumina-gDNA', value: 'Illumina-gDNA' },
    	            	                    { text: '重测序', value: '重测序' },
    	            	                    { text: '外显子', value: '外显子' },
    	            	                    { text: 'Bisulfite甲基化测序', value: 'Bisulfite甲基化测序' },
    	            	                    { text: 'Mate pair-2K-5K', value: 'Mate pair-2K-5K' },
    	            	                    { text: 'Mate pair-8K-14K', value: 'Mate pair-8K-14K' },
    	            	                    { text: 'Denovo测序', value: 'Denovo测序' },
    	            	                    { text: '其他', value: '其他' }
    	                                ]
    	                            },
    	                            optionLabel: "",
    	                            dataValueField: 'value',
    	                            dataTextField: 'text'
    	                        });
    	                }
    			  }, // 测序类型/测序模式
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100},
    			  {title:"备注",field:"REMARK",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
          				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
          				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    var tablesGrid4_init = function(tablesGridData) {
    	var tGridId = "tablesGrid4_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100,hidden:true},
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100,hidden:true},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100,hidden:true},
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 0,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100,hidden:true},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100,hidden:true},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100,hidden:true},
    			  {title:"备注",field:"REMARK",type:"string",width:100,hidden:true},
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100},
    			  {title:"测序类型",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
	  			    			value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
    	                editor: function (container, options) {
    	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
    	                        .appendTo(container)
    	                        .kendoDropDownList({
    	                            dataSource: {
    	                                data: [
    	            	                    { text: '重测序', value: '重测序' },
    	            	                    { text: '外显子', value: '外显子' },
    	            	                    { text: 'Denovo测序', value: 'Denovo测序' },
    	            	                    { text: 'Mate pair PCR产物测序', value: 'Mate pair PCR产物测序' },
    	            	                    { text: 'ChIP-seq', value: 'ChIP-seq' },
    	            	                    { text: 'Bisulfite甲基化测序', value: 'Bisulfite甲基化测序' },
    	            	                    { text: '10x genomics', value: '10x genomics' },
    	            	                    { text: 'FFPE-人重测序', value: 'FFPE-人重测序' },
    	            	                    { text: 'FFPE-人外显子组', value: 'FFPE-人外显子组' },
    	            	                    { text: '宏基因组', value: '宏基因组' },
    	            	                    { text: '其他', value: '其他' }
    	                                ]
    	                            },
    	                            optionLabel: "",
    	                            dataValueField: 'value',
    	                            dataTextField: 'text'
    	                        });
    	                }
    			  }, // 测序类型/测序模式
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,
    	                editor: function (container, options) {
  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
  	                        .appendTo(container)
  	                        .kendoNumericTextBox({
  	                            decimals: 2,
  	                            min: 0 // , max: 3.00
  	                        });
  	                }
  			      },
      			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
      			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,
      				  template:function (dataItem) {
			    			var value="";
			    			if(dataItem && dataItem["PREPARATION_TIME"]){
			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
			    			return value;
	                	},
      	                editor: function (container, options) {
      	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
      	                        .appendTo(container)
      	                        .kendoDatePicker({
      	                            format: 'yyyy-MM-dd HH:mm:ss',
      	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
      	                            min: new Date(2000, 0, 1),
      	                            max: new Date()
      	                        });
      	                }
      			  },
      			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
          				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
          				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    var tablesGrid5_init = function(tablesGridData) {
    	var tGridId = "tablesGrid5_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100,hidden:true},
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100,hidden:true},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100,hidden:true},
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 0,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100,hidden:true},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100,hidden:true},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100,hidden:true},
    			  {title:"备注",field:"REMARK",type:"string",width:100,hidden:true},
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100},
    			  {title:"测序类型",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
	  			    			value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
    	                editor: function (container, options) {
    	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
    	                        .appendTo(container)
    	                        .kendoDropDownList({
    	                            dataSource: {
    	                                data: [
    	            	                    { text: '全基因组测序-10K', value: '全基因组测序-10K' },
    	            	                    { text: '全基因组测序-20K', value: '全基因组测序-20K' },
    	            	                    { text: '全基因组测序-30K', value: '全基因组测序-30K' },
    	            	                    { text: '全基因组测序-40K', value: '全基因组测序-40K' },
    	            	                    { text: 'BAC文库测序', value: 'BAC文库测序' },
    	            	                    { text: '宏基因组测序', value: '宏基因组测序' },
    	            	                    { text: '扩增子测序', value: '扩增子测序' },
    	            	                    { text: '其他', value: '其他' }
    	                                ]
    	                            },
    	                            optionLabel: "",
    	                            dataValueField: 'value',
    	                            dataTextField: 'text'
    	                        });
    	                }
    			  }, // 测序类型/测序模式
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,
    	                editor: function (container, options) {
  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
  	                        .appendTo(container)
  	                        .kendoNumericTextBox({
  	                            decimals: 2,
  	                            min: 0 // , max: 3.00
  	                        });
  	                }
  			      },
      			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
      			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,
      				  template:function (dataItem) {
			    			var value="";
			    			if(dataItem && dataItem["PREPARATION_TIME"]){
			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
			    			return value;
	                	},
      	                editor: function (container, options) {
      	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
      	                        .appendTo(container)
      	                        .kendoDatePicker({
      	                            format: 'yyyy-MM-dd HH:mm:ss',
      	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
      	                            min: new Date(2000, 0, 1),
      	                            max: new Date()
      	                        });
      	                }
      			  },
      			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
          				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
          				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    var tablesGrid6_init = function(tablesGridData) {
    	var tGridId = "tablesGrid6_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100,hidden:true},
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100,hidden:true},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100,hidden:true},
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 0,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100,hidden:true},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100,hidden:true},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100,hidden:true},
    			  {title:"备注",field:"REMARK",type:"string",width:100,hidden:true},
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100},
    			  {title:"测序类型",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
	  			    			value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
    	                editor: function (container, options) {
    	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
    	                        .appendTo(container)
    	                        .kendoDropDownList({
    	                            dataSource: {
    	                                data: [
    	            	                    { text: '真核生物mRNA-seq', value: '真核生物mRNA-seq' },
    	            	                    { text: '原核生物mRNA-seq', value: '原核生物mRNA-seq' },
    	            	                    { text: '真核生物低起始量mRNA-seq', value: '真核生物低起始量mRNA-seq' },
    	            	                    { text: '链特异性转录组', value: '链特异性转录组' },
    	            	                    { text: 'ncRNA-seq', value: 'ncRNA-seq' },
    	            	                    { text: 'cDNA转录组', value: 'cDNA转录组' },
    	            	                    { text: '单细胞cDNA转录组', value: '单细胞cDNA转录组' },
    	            	                    { text: '小RNA测序', value: '小RNA测序' },
    	            	                    { text: 'RIP-seq', value: 'RIP-seq' },
    	            	                    { text: '其他', value: '其他' }
    	                                ]
    	                            },
    	                            optionLabel: "",
    	                            dataValueField: 'value',
    	                            dataTextField: 'text'
    	                        });
    	                }
    			  }, // 测序类型/测序模式
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,
	  	                editor: function (container, options) {
	  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	  	                        .appendTo(container)
	  	                        .kendoNumericTextBox({
	  	                            decimals: 2,
	  	                            min: 0 // , max: 3.00
	  	                        });
	  	                }
	  			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,
    	                editor: function (container, options) {
  	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
  	                        .appendTo(container)
  	                        .kendoNumericTextBox({
  	                            decimals: 2,
  	                            min: 0 // , max: 3.00
  	                        });
  	                }
  			      },
      			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
      			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,
      				  template:function (dataItem) {
  			    			var value="";
  			    			if(dataItem && dataItem["PREPARATION_TIME"]){
  			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
  			    			return value;
	                	},
      	                editor: function (container, options) {
      	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
      	                        .appendTo(container)
      	                        .kendoDatePicker({
      	                            format: 'yyyy-MM-dd HH:mm:ss',
      	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
      	                            min: new Date(2000, 0, 1),
      	                            max: new Date()
      	                        });
      	                }
      			  },
      			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100},
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
          				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
          				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    var tablesGrid7_init = function(tablesGridData) {
    	var tGridId = "tablesGrid7_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100,hidden:true},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,hidden:true,
	  	                editor: function (container, options) {
		                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
		                        .appendTo(container)
		                        .kendoNumericTextBox({
		                            decimals: 2,
		                            min: 0 // , max: 3.00
		                        });
		                }
				  },
    			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,hidden:true,
      				  template:function (dataItem) {
			    			var value="";
			    			if(dataItem && dataItem["PREPARATION_TIME"]){
			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
			    			return value;
	                	},
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoDatePicker({
    	                            format: 'yyyy-MM-dd HH:mm:ss',
    	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
    	                            min: new Date(2000, 0, 1),
    	                            max: new Date()
    	                        });
    	                }
    			  },
    			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100,hidden:true},
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 0,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100,hidden:true},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100,hidden:true},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100,hidden:true},
    			  {title:"备注",field:"REMARK",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100},
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100},
    			  {title:"测序模式",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
	  			    			value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
	  	                editor: function (container, options) {
	  	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
	  	                        .appendTo(container)
	  	                        .kendoDropDownList({
	  	                            dataSource: {
	  	                                data: [
	  	            	                    { text: 'Novaseq', value: 'Novaseq' },
	  	            	                    { text: '250PE', value: '250PE' },
	  	            	                    { text: 'X10-150PE', value: 'X10-150PE' },
	  	            	                    { text: 'CN500-75SSE', value: 'CN500-75SSE' },
	  	            	                    { text: '50SE', value: '50SE' },
	  	                                ]
	  	                            },
	  	                            optionLabel: "",
	  	                            dataValueField: 'value',
	  	                            dataTextField: 'text'
	  	                        });
	  	                }
	  			  }, // 测序类型/测序模式
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
          				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
          				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    var tablesGrid8_init = function(tablesGridData) {
    	var tGridId = "tablesGrid8_";
    	$("#"+tGridId+pathValue).kendoGrid({
    		  columns: [
    			  {selectable:true, width:26},
    			  {title:"唯一标识",field:"ID",type:"string",width:20,hidden:true},
    			  {title:"基本信息ID",field:"SIB_ID",type:"string",width:20,hidden:true},
    			  {title:"分类信息ID",field:"SIC_ID",type:"string",width:20,hidden:true},
    			  {title:"上机平台/测序平台",field:"SEQ_PLATFORM",type:"string",width:20,hidden:true},
    			  {title:"送样单号",field:"SAMPLE_SEND_NO",type:"string",width:20,hidden:true},
    			  {title:"状态",field:"STATUS",type:"string",width:20,hidden:true},
    			  {title:"创建人",field:"CREATOR",type:"string",width:20,hidden:true},
    			  {title:"创建时间",field:"CREATTIME",type:"date",width:20,hidden:true},
    			  {title:"最近修改人",field:"LASTUPDATOR",type:"string",width:20,hidden:true},
    			  {title:"最近修改时间",field:"LASTUPDATETIME",type:"date",width:20,hidden:true},
    			  {title:"账套",field:"LOGINCOMPANY",type:"string",width:20,hidden:true},
    			  {title:"样品编号",field:"SAMPLE_CODE",type:"string",width:100,hidden:true},
    			  {title:"样品名称",field:"SAMPLE_NAME",type:"string",width:100,hidden:true},
    			  {title:"文库编号",field:"LIB_CODE",type:"string",width:100,hidden:true},
    			  {title:"浓度(ng/ul)",field:"CONCENTRATION",type:"number",width:100,hidden:true,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"定量方法",field:"QUANTIFY_METHOD",type:"string",width:100,hidden:true},
    			  {title:"体积(ul)",field:"VOLUME",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"OD260/280",field:"OD260_280",type:"number",width:100,hidden:true,
	  	                editor: function (container, options) {
		                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
		                        .appendTo(container)
		                        .kendoNumericTextBox({
		                            decimals: 2,
		                            min: 0 // , max: 3.00
		                        });
		                }
				  },
    			  {title:"制备时间",field:"PREPARATION_TIME",type:"date",width:100,hidden:true,
      				  template:function (dataItem) {
			    			var value="";
			    			if(dataItem && dataItem["PREPARATION_TIME"]){
			    				value = kendo.toString(kendo.parseDate(dataItem["PREPARATION_TIME"]), 'yyyy-MM-dd HH:mm:ss');
			    			}
			    			return value;
	                	},
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="date" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoDatePicker({
    	                            format: 'yyyy-MM-dd HH:mm:ss',
    	                            // footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
    	                            min: new Date(2000, 0, 1),
    	                            max: new Date()
    	                        });
    	                }
    			  },
    			  {title:"物种",field:"SAMPLE_SPECIES",type:"string",width:100,hidden:true},
    			  {title:"样本数目",field:"SAMPLE_NUMBER",type:"number",width:100,hidden:true,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 0,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    			  {title:"样本状态",field:"SAMPLE_STATUS",type:"string",width:100,hidden:true},
    			  {title:"存放条件",field:"STORAGE_CONDITIONS",type:"string",width:100,hidden:true},
    			  {title:"提取意见",field:"EXTRACT_OPINION",type:"string",width:100,hidden:true},
    			  {title:"备注",field:"REMARK",type:"string",width:100,hidden:true},
    			  {title:"文库名称",field:"LIB_NAME",type:"string",width:100},
    			  {title:"文库片段大小",field:"LIB_SIZE",type:"number",width:100,
    	                editor: function (container, options) {
    	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
    	                        .appendTo(container)
    	                        .kendoNumericTextBox({
    	                            decimals: 2,
    	                            min: 0 // , max: 3.00
    	                        });
    	                }
    			  },
    			  {title:"INDEX编号",field:"LIB_INDEX_CODE",type:"string",width:100},
    			  {title:"INDEX序列",field:"INDEX_SEQ",type:"string",width:100},
    			  {title:"测序模式",field:"SEQ_TYPE",type:"string",width:100,
  			    	  	template:function (dataItem) {
	  			    		var value="";
	  			    		if(dataItem && dataItem["SEQ_TYPE"]){
	  			    			value = dataItem["SEQ_TYPE"]["value"] ? dataItem["SEQ_TYPE"]["value"] : dataItem["SEQ_TYPE"];
  			    			}
	  			    		return value;
  	                	},
	  	                editor: function (container, options) {
	  	                    $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
	  	                        .appendTo(container)
	  	                        .kendoDropDownList({
	  	                            dataSource: {
	  	                                data: [
	  	            	                    { text: 'Novaseq', value: 'Novaseq' },
	  	            	                    { text: '250PE', value: '250PE' },
	  	            	                    { text: 'X10-150PE', value: 'X10-150PE' },
	  	            	                    { text: 'CN500-75SSE', value: 'CN500-75SSE' },
	  	            	                    { text: '50SE', value: '50SE' },
	  	                                ]
	  	                            },
	  	                            optionLabel: "",
	  	                            dataValueField: 'value',
	  	                            dataTextField: 'text'
	  	                        });
	  	                }
	  			  }, // 测序类型/测序模式
    			  {title:"数据量(M)",field:"DATA_NUM",type:"number",width:100,
  	                editor: function (container, options) {
	                    $('<input name="'+ options.field +'" type="number" data-bind="value: '+ options.field +'">')
	                        .appendTo(container)
	                        .kendoNumericTextBox({
	                            decimals: 2,
	                            min: 0 // , max: 3.00
	                        });
	                }
			      },
    		  ],
    		  dataSource: {
    		    data: ( tablesGridData ? tablesGridData : [] ),
    		    schema: {
    		      model: { id: "ID" },
    		      fields: {
    		    	  SAMPLE_NAME: { type : "string" },// 样品名称
        			  LIB_CODE: { type : "string" },// 文库编号
        			  SEQ_TYPE: { type : "string" },// 测序类型/测序模式
    		    	  CONCENTRATION: { type : "number" },// 浓度
        			  QUANTIFY_METHOD: { type : "string" },// 定量方法
    		    	  VOLUME: { type : "number" },// 体积
        			  OD260_280: { type : "string" },// OD260/280
        			  PREPARATION_TIME: { type : "date",defaultValue: null,
        				  parse: function (e) {
          				  	return kendo.toString(kendo.parseDate(e), 'yyyy-MM-dd HH:mm:ss');
          				  }
        			  },// 制备时间
        			  SAMPLE_SPECIES: { type : "string" },// 物种
        			  LIB_SIZE: { type : "string" },// 文库片段大小
        			  LIB_INDEX_CODE: { type : "string" },// INDEX编号
        			  INDEX_SEQ: { type : "string" },// INDEX序列
    		    	  DATA_NUM: { type : "number" },// 数据量(M)
    		    	  SAMPLE_NUMBER: { type : "number" },// 样本数目
        			  SAMPLE_STATUS: { type : "string" },// 样本状态
        			  STORAGE_CONDITIONS: { type : "string" },// 存放条件
        			  EXTRACT_OPINION: { type : "string" },// 提取意见
        			  REMARK: { type : "string" },// 备注
    		      }
    		    }
    		  },
    		  editable: true,
    		  selectable: "multiple,row",
    		  height: 300,
    		  toolbar: [
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'addRow\',\'asdas\')">新增行</a>'
    			  },
    			  {
    				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+tGridId+'removeRow\',\'asdas\')">删除行</a>'
    			  }
    		  ]
    		});
    }
    
    var setViewShow = function() {
    	var fieldset_info2_id = "fieldset_info2_"+pathValue;//组织-Illumina/Pacbio平台
    	var fieldset_info3_id = "fieldset_info3_"+pathValue;//组织-Bionano平台
    	var fieldset_info4_id = "fieldset_info4_"+pathValue;//Illumina-gDNA
    	var fieldset_info5_id = "fieldset_info5_"+pathValue;//Pacbio-gDNA
    	var fieldset_info6_id = "fieldset_info6_"+pathValue;//RNA
    	var fieldset_info7_id = "fieldset_info7_"+pathValue;//Illumina文库
    	var fieldset_info8_id = "fieldset_info8_"+pathValue;//Pacbio文库
    	
    	var viewModel=funcExce(pathValue+"getViewModel.form1");
    	var SAMPLE_TYPE_OBJ=viewModel.get("SAMPLE_TYPE");
    	var SEQ_PLATFORM_OBJ=viewModel.get("SEQ_PLATFORM");
    	var SAMPLE_TYPE="";
    	var SEQ_PLATFORM="";
    	for(var index in SAMPLE_TYPE_OBJ){
    		SAMPLE_TYPE += SAMPLE_TYPE_OBJ[index]["value"] ? ","+SAMPLE_TYPE_OBJ[index]["value"] : "";
    	}
    	for(var index in SEQ_PLATFORM_OBJ){
    		SEQ_PLATFORM += SEQ_PLATFORM_OBJ[index]["value"] ? ","+SEQ_PLATFORM_OBJ[index]["value"] : "";
    	}
    	//组织-Illumina/Pacbio平台
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && ( SEQ_PLATFORM.indexOf("Illumina")>=0 || SEQ_PLATFORM.indexOf("Pacbio")>=0 ) ) {
    		$("#"+fieldset_info2_id).show();
    	} else {
    		$("#"+fieldset_info2_id).hide();
    	}
    	//组织-Bionano平台
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("组织")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Bionano")>=0) {
    		$("#"+fieldset_info3_id).show();
    	} else {
    		$("#"+fieldset_info3_id).hide();
    	}
    	//Illumina-gDNA
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
    		$("#"+fieldset_info4_id).show();
    	} else {
    		$("#"+fieldset_info4_id).hide();
    	}
    	//Pacbio-gDNA
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("DNA")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
    		$("#"+fieldset_info5_id).show();
    	} else {
    		$("#"+fieldset_info5_id).hide();
    	}
    	//RNA
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("RNA")>=0) {
    		$("#"+fieldset_info6_id).show();
    	} else {
    		$("#"+fieldset_info6_id).hide();
    	}
    	//Illumina文库
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Illumina")>=0) {
    		$("#"+fieldset_info7_id).show();
    	} else {
    		$("#"+fieldset_info7_id).hide();
    	}
    	//Pacbio文库
    	if (SAMPLE_TYPE && SAMPLE_TYPE.indexOf("文库")>=0 && SEQ_PLATFORM && SEQ_PLATFORM.indexOf("Pacbio")>=0) {
    		$("#"+fieldset_info8_id).show();
    	} else {
    		$("#"+fieldset_info8_id).hide();
    	}
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "tablesGrid2_addRow":tablesGrid2_addRow,
        "tablesGrid3_addRow":tablesGrid3_addRow,
        "tablesGrid4_addRow":tablesGrid4_addRow,
        "tablesGrid5_addRow":tablesGrid5_addRow,
        "tablesGrid6_addRow":tablesGrid6_addRow,
        "tablesGrid7_addRow":tablesGrid7_addRow,
        "tablesGrid8_addRow":tablesGrid8_addRow,
        
        "tablesGrid2_removeRow":tablesGrid2_removeRow,
        "tablesGrid3_removeRow":tablesGrid3_removeRow,
        "tablesGrid4_removeRow":tablesGrid4_removeRow,
        "tablesGrid5_removeRow":tablesGrid5_removeRow,
        "tablesGrid6_removeRow":tablesGrid6_removeRow,
        "tablesGrid7_removeRow":tablesGrid7_removeRow,
        "tablesGrid8_removeRow":tablesGrid8_removeRow,
        
        "setViewShow":setViewShow,
    });
 
 });
 