$(document).ready(function() {
   var pathValue="berry-prod-sample-gatherSampleProject-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid1;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar1=getButtonTemplates(pathValue,[
            {name:"none",title:"新增送检信息单...",select:"新增送检信息单"},
            {name:"add",target:"openAddDnaNgsSeq",title:"新增:二代DNA样品建库测序信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddDnaTgsSeq",title:"新增:三代DNA样品建库测序信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddRnaNgsSeq",title:"新增:二代RNA样品建库测序信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddRnaTgsSeq",title:"新增:三代RNA样品建库测序信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddLibNgsOneRun",title:"新增:二代单上机文库信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddLibTgsOneRun",title:"新增:三代单上机文库信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddZzNgsTgs",title:"新增:二代/三代待提取材料信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddFFPE",title:"新增:FFPE样品信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddTsdExonSq",title:"新增:极速外显子建库测序样品信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddBionano",title:"新增:Bionano待提取样品信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddHicSeq",title:"新增:Hi-C待提取材料信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddMonpSeq",title:"10X单细胞建库测序样品信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddVisiumSeq",title:"10X Visium待提取材料信息单",option:"新增送检信息单"},
            {name:"add",target:"openAddVisium2Seq",title:"10X Visium空间转录组样品信息单",option:"新增送检信息单"},
            {name:"edit",target:"editInfo",title:"编辑送检信息单"},
            {name:"delete",target:"deleteInfo",title:"删除送检信息单"},
            {name:"submit",target:"submitToApprove",title: "提交"},
        ]);//工具条
        //请求参数
        var tablesGridJson1={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar1,
            height: fullh-112,
            read:{"query":"query_BR_SAMPLE_INFO_BASIC_view","objects":[ "MIS", ["草稿"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_SEND_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= SAMPLE_SEND_NO #","funcExce(\'"+pathValue+"openSampleInfoWin\',\'#= ID #\',\'#= SAMPLE_SEND_TYPE #\','edit');","txt"));
                    }
                }
            }
        };
        tablesGrid1 = initKendoGrid("#tablesGrid1_"+pathValue,tablesGridJson1);//初始化表格的方法
        
        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"delete",target:"revokeSubmitToApprove",title: "撤回草稿"}
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-112,
            read:{"query":"query_BR_SAMPLE_INFO_BASIC_view","objects":[ "MIS", ["待审核", "部分已确认收样", "收样完成", "已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_SEND_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= SAMPLE_SEND_NO #","funcExce(\'"+pathValue+"openSampleInfoWin\',\'#= ID #\',\'#= SAMPLE_SEND_TYPE #\','view');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var refreshGrid1=function(){
      if(tablesGrid1){
          tablesGrid1.dataSource.read();//重新读取--刷新
      }
   }
   var refreshGrid2=function(){
      if(tablesGrid2){
          tablesGrid2.dataSource.read();//重新读取--刷新
      }
   }
   var callBack=function(){
  	 refreshGrid1();
  	 refreshGrid2();
   };
   
    var openAddLibNgsOneRun=function(){
        openSampleInfoWin("", "二代单上机文库信息单", "add");
    }
    var openAddLibTgsOneRun=function(){
        openSampleInfoWin("", "三代单上机文库信息单", "add");
    }
    var openAddDnaNgsSeq=function(){
        openSampleInfoWin("", "二代DNA样品建库测序信息单", "add");
    }
    var openAddRnaNgsSeq=function(){
        openSampleInfoWin("", "二代RNA样品建库测序信息单", "add");
    }
    var openAddDnaTgsSeq=function(){
        openSampleInfoWin("", "三代DNA样品建库测序信息单", "add");
    }
    var openAddRnaTgsSeq=function(){
        openSampleInfoWin("", "三代RNA样品建库测序信息单", "add");
    }
    var openAddZzNgsTgs=function(){
        openSampleInfoWin("", "二代/三代待提取材料信息单", "add");
    }
    var openAddFFPE=function(){
        openSampleInfoWin("", "FFPE样品信息单", "add");
    }
    var openAddTsdExonSq=function(){
        openSampleInfoWin("", "极速外显子建库测序样品信息单", "add");
    }
    var openAddBionano=function(){
    	openSampleInfoWin("", "Bionano待提取样品信息单", "add");
    }
    var openAddHicSeq=function(){
    	openSampleInfoWin("", "Hi-C待提取材料信息单", "add");
    }
    var openAddMonpSeq=function(){
    	openSampleInfoWin("", "10X单细胞建库测序样品信息单", "add");
    }
    var openAddVisiumSeq=function(){
    	openSampleInfoWin("", "10X Visium待提取材料信息单", "add");
    }
    var openAddVisium2Seq=function(){
    	openSampleInfoWin("", "10X Visium空间转录组样品信息单", "add");
    }
    var editInfo=function(){
        var rData = getGridSelectData(tablesGrid1);
        if(rData.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(rData.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        openSampleInfoWin(rData[0].ID, rData[0].SAMPLE_SEND_TYPE, "edit");
    }
    var openSampleInfoWin=function(ID, SAMPLE_SEND_TYPE, VIEW_MODE){
    	var winOpts;
    	if (SAMPLE_SEND_TYPE == "二代单上机文库信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addLibNgsOneRun/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "三代单上机文库信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addLibTgsOneRun/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "二代DNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addDnaNgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "二代RNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addRnaNgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "三代DNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addDnaTgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "三代RNA样品建库测序信息单") {
    		winOpts={
	            url:"berry/prod/sample/gatherSampleProject/addRnaTgsSeq/add"
	        };
    	} else if (SAMPLE_SEND_TYPE == "二代/三代待提取材料信息单") {
            winOpts={
                url:"berry/prod/sample/gatherSampleProject/addZzNgsTgs/add"
            };
    	} else if (SAMPLE_SEND_TYPE == "FFPE样品信息单") {
            winOpts={
                url:"berry/prod/sample/gatherSampleProject/addFFPE/add"
            };
    	}else if (SAMPLE_SEND_TYPE == "极速外显子建库测序样品信息单") {
            winOpts={
                url:"berry/prod/sample/gatherSampleProject/addTsdExonSq/add"
            };
    	}else if (SAMPLE_SEND_TYPE == "Bionano待提取样品信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addBionano/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "Hi-C待提取材料信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addHicSeq/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "10X单细胞建库测序样品信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addMonpSeq/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "10X Visium待提取材料信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addVisiumSeq/add"
    		};
    	} else if (SAMPLE_SEND_TYPE == "10X Visium空间转录组样品信息单") {
    		winOpts={
    			url:"berry/prod/sample/gatherSampleProject/addVisium2Seq/add"
    		};
    	} else {
    		alertMsg("无法找到当前送检单类型对应的信息单模板", "error");
    	}
    	if (winOpts) {
    		if (VIEW_MODE == "add") {
    			winOpts.title = "新增: "+SAMPLE_SEND_TYPE+"..";
    			VIEW_MODE = "edit";
    		} else if (VIEW_MODE == "edit") {
    			winOpts.title = "编辑: "+SAMPLE_SEND_TYPE+"..";
    		} else {
    			winOpts.title = "查看: "+SAMPLE_SEND_TYPE+"..";
    			VIEW_MODE = "view";
    		}
        	var params = { "ID":ID, "pPathValue":pathValue, "VIEW_MODE":VIEW_MODE };
        	
        	winOpts.currUrl = pathValue;
    		openWindow(winOpts, params);
    	}
    }

     var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid1);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
        	var params={ "ids":arrIds, "STATUS":"草稿" };
	    	var url= "berry/prod/sample/sample/delSampleInfos";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	//提交成功
	                    alertMsg("删除成功","success",function(){
	                    	refreshGrid1();//执行回调
	                    });
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
     }
     
     var submitToApprove=function(){
         var arrIds=getSelectData(tablesGrid1);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         confirmMsg("提示", "<b><span style='color: red; font-size: 18px;'>重要提示:<br/>请打印纸质版送检单，与样品一起邮寄到公司！</span></b>", "warn", function() {
	         var winOpts={
	    	     title:"提交送检单..",
	             url:"berry/prod/sample/gatherSampleProject/submitToApprove/submit",
	             currUrl:pathValue
	         };
	     	 var params = { "IDS":arrIds, "pPathValue":pathValue };
	 		 openWindow(winOpts, params);
         });
      }
     
     var revokeSubmitToApprove=function(){
         var arrIds=getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行撤回操作!");
             return ;
         }
         confirmMsg("确认", "确定要撤回选中的数据吗?", "warn", function() {
 	        var params={"ids":arrIds};
 	    	var url= "berry/prod/sample/sample/revokeSubmitToApprove";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	//提交成功
 	                    alertMsg("撤回成功","success",function(){
 	                    	refreshGrid1();//执行回调
 	                    	refreshGrid2();//执行回调
 	                    });
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
      }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "editInfo":editInfo,
         "openSampleInfoWin":openSampleInfoWin,
         "openAddLibNgsOneRun":openAddLibNgsOneRun,
         "openAddLibTgsOneRun":openAddLibTgsOneRun,
         "openAddDnaNgsSeq":openAddDnaNgsSeq,
         "openAddRnaNgsSeq":openAddRnaNgsSeq,
         "openAddDnaTgsSeq":openAddDnaTgsSeq,
         "openAddRnaTgsSeq":openAddRnaTgsSeq,
         "openAddZzNgsTgs":openAddZzNgsTgs,
         "openAddFFPE":openAddFFPE,
         "openAddTsdExonSq":openAddTsdExonSq,
         "openAddBionano":openAddBionano,
         "openAddHicSeq":openAddHicSeq,
         "openAddMonpSeq":openAddMonpSeq,
         "openAddVisiumSeq":openAddVisiumSeq,
         "openAddVisium2Seq":openAddVisium2Seq,
         "deleteInfo":deleteInfo,
         "refreshGrid1":refreshGrid1,
         "refreshGrid2":refreshGrid2,
         "callBack":callBack,//回调方法
         "submitToApprove":submitToApprove,
         "revokeSubmitToApprove":revokeSubmitToApprove,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
