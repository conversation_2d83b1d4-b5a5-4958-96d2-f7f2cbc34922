<style>
/** 送样单表头: 样式 **/
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add table {
	width: 96%;
	font-family: 宋体;
}
/** 送样单表头: 样式 **/
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_TITLE td {
	width: 25%;
	border: #000 solid 2px;
	border-bottom: none;
	padding: 10px;
	font-size: 16px;
	font-weight: bold;
}
/** 送样单内容: 样式 **/
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT th {
	width: 136px;
	border: #000 solid 2px;
	padding: 10px;
	text-align: right;
	font-size: 16px;
	font-weight: bold;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT td {
	border: #000 solid 2px;
	padding-left: 10px;
	font-size: 16px;
	padding: 10px;
	line-height: 36px;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT td span {
	border: 0px;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT_2 td {
	border: #000 solid 0px;
	padding-left: 10px;
	font-size: 18px;
	padding: 10px;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT_2 span {
	border: #C0B2D1 solid 1px;
	border-top: none;
	border-bottom: none;
	border-left: none;
	border-right: none;
	outline: none;
	padding-bottom: 3px;
}

/** 输入框: 样式 **/
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add input {
	height: 26px;
	border: #C0B2D1 solid 1px;
	border-top: none;
	border-left: none;
	border-right: none;
	outline: none;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_TITLE input[type="text"] {
	width: 226px;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT input[type="text"] {
	width: 180px;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT input[type="checkbox"],
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT_2 input[type="checkbox"] {
	width: 18px;
	height: 18px;
	border: #000 solid 1px;
}
#CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add #SAMPLE_SEND_NO_CONTENT input[type="radio"] {
	width: 18px;
	height: 18px;
	border: #000 solid 1px;
}
</style>

<div id="CARD-berry-prod-sample-gatherSampleProject-addMonpSeq-add" class="card" style="border: 0px;">
    <div class="k-button theme-bg-m hkbutton" style="right: 35px;" onclick="∑close|this);return false;">关闭</div>
    <div class="k-button theme-bg-m hkbutton" style="right: 135px; width: 106px; display: none;" onclick="∑submit|this);return false;" id="sampleSendNoSaveSubmit">保存送检主单</div>
    <div class="card-body" align="center">
    	<form id="form" action="javascript:void(0);">
    	<!-- ------------------------------------ 送样单: 页眉-标题 ------------------------------------ -->
    	<table id="SAMPLE_SEND_NO_PAGR_HEADER">
    		<tr style="border-bottom: #C0B2D1 solid 3px;">
    			<td align="left" width="50%">
    				<img style="max-height: 36%; max-width: 36%;" src="/berry/prod/sample/gatherSampleProject/ADD_PAGE_HEADER_LOGO_LEFT.png">
    			</td>
    			<td align="right" width="50%">
    				<img style="max-height: 100%; max-width: 100%;" src="/berry/prod/sample/gatherSampleProject/ADD_PAGE_HEADER_LOGO_RIGHR.png">
    			</td>
    		</tr>
    		<tr>
    			<td colspan="2" align="center" height="56px">
    				<h3 style="color: #808080;">完善的样品信息单是我们优质服务的开始</h3>
    			</td>
    		</tr>
    		<tr>
    			<td colspan="2" align="center" height="56px">
    				<h1 style="color: #000000;">10X单细胞建库测序样品信息单</h1>
    			</td>
    		</tr>
    		<tr>
    			<td colspan="2" align="center" height="56">
    				<h4 style="color: #000000;">（免责声明: 如未完整填写此表或提供信息有误，可能导致下游实验失败或产生额外费用，请务必重视！）</h4>
    			</td>
    		</tr>
    	</table>
    	<!-- ------------------------------------ 送样单: 页眉-标题 ------------------------------------ -->
    	
    	<!-- ------------------------------------ 送样单: 表头 ------------------------------------ -->
    	<table id="SAMPLE_SEND_NO_TITLE">
    		<tr>
    			<td colspan="3">
    				<input type="text" id="ID" name="ID" style="display: none;"/>
    				<input type="text" id="SAMPLE_SOURCE" name="SAMPLE_SOURCE" style="display: none;"/>
    				<input type="text" id="SAMPLE_SEND_TYPE" name="SAMPLE_SEND_TYPE" style="display: none;"/>
    				送样单号: <input type="text" id="SAMPLE_SEND_NO" name="SAMPLE_SEND_NO" readonly="readonly"/>
    			</td>
    			<td rowspan="4" align="center">
    				<!-- 
    				<img id="SAMPLE_SEND_NO_BARCODE">
    				<span id="SAMPLE_SEND_NO_BARCODE"></span>
    				-->
    				<span id="SAMPLE_SEND_NO_BARCODE"></span>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<input type="text" id="CONTRACT_ID" name="CONTRACT_ID" style="display: none;"/>
    				项目编号: <input type="text" id="CONTRACT_NO" name="CONTRACT_NO" readonly="readonly" style="cursor: pointer;" onclick="∑selectCONTRACT|this);return false;"/>
    				<br/>
    				&nbsp;&nbsp;&nbsp;&nbsp;
    				<font color="red">(点击选择项目)</font>
    			</td>
    			<td>
    				项目名称: <input type="text" id="CONTRACT_NAME" name="CONTRACT_NAME" readonly="readonly"/>
    			</td>
    			<td>
    				<input type="text" id="COMPANY_ID" name="COMPANY_ID" style="display: none;"/>
    				<input type="text" id="COMPANY_CODE" name="COMPANY_CODE" style="display: none;"/>
    				客户单位: <input type="text" id="COMPANY_NAME" name="COMPANY_NAME" readonly="readonly" required="required"/>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<input type="text" id="CUSTOMER_ID" name="CUSTOMER_ID" style="display: none;"/>
    				客户姓名: <input type="text" id="CUSTOMER_NAME" name="CUSTOMER_NAME" readonly="readonly"/>
    			</td>
    			<td>
    				E-mail: <input type="text" id="CUSTOMER_EMAIL" name="CUSTOMER_EMAIL"/>
    			</td>
    			<td>
    				预约电话: <input type="text" id="CUSTOMER_TEL" name="CUSTOMER_TEL"/>
    			</td>
    		</tr>
    		<tr>
    			<td colspan="3">
    				客户地址: <input type="text" id="CUSTOMER_ADDRESS" name="CUSTOMER_ADDRESS" style="width: 500px;"/>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<input type="text" id="SALE_MAN_ID" name="SALE_MAN_ID" style="display: none;"/>
    				销售姓名: <input type="text" id="SALE_MAN" name="SALE_MAN" readonly="readonly"/>
    			</td>
    			<td>
    				<!-- 送样日期:  -->
    				<input type="text" id="SAMPLE_SEND_DATE" name="SAMPLE_SEND_DATE" class="datetime k-input"/>
    			</td>
    			<td>
    				样品数量: <input type="text" id="SAMPLE_COUNT" name="SAMPLE_COUNT" readonly="readonly"/>
    			</td>
    		</tr>
    	</table>
    	<!-- ------------------------------------ 送样单: 表头 ------------------------------------ -->
    	
    	<!-- ------------------------------------ 送样单: 内容 ------------------------------------ -->
    	<table id="SAMPLE_SEND_NO_CONTENT">
    		<tr>
    			<th>
    				项目进展
    			</th>
    			<td colspan="7">
    				<span id="PROJECT_PROGRESS_VIEW" style="display: none;"></span>
    				<span id="PROJECT_PROGRESS_EDIT">
	    				合同例数：<input type="text" id="PROJECT_PROGRESS_A_1" name="PROJECT_PROGRESS_A_1"/>
	    				已完成样本例数：<input type="text" id="PROJECT_PROGRESS_A_2" name="PROJECT_PROGRESS_A_2"/>
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 样品物种
    			</th>
    			<td colspan="2">
    				<span id="SPECIES_NAME_VIEW" style="display: none;"></span>
    				<span id="SPECIES_NAME_EDIT">
	    				<input type="checkbox" id="SPECIES_NAME_A01" name="SPECIES_NAME_A01" val="A01"/>人
	    				&nbsp; <input type="checkbox" id="SPECIES_NAME_A02" name="SPECIES_NAME_A02" val="A02"/>动物，物种名称<input type=Text id="SPECIES_NAME_A02_TEXT" name="SPECIES_NAME_A02_TEXT"/>
	    				&nbsp; <input type="checkbox" id="SPECIES_NAME_A03" name="SPECIES_NAME_A03" val="A03"/>水产，物种名称<input type=Text id="SPECIES_NAME_A03_TEXT" name="SPECIES_NAME_A03_TEXT"/>
	    				&nbsp; <input type="checkbox" id="SPECIES_NAME_A04" name="SPECIES_NAME_A04" val="A04"/>植物，物种名称<input type=Text id="SPECIES_NAME_A04_TEXT" name="SPECIES_NAME_A04_TEXT"/>
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 合同要求捕获细胞数
    			</th>
    			<td colspan="2">
    				<span id="PY_CELL_NUM_VIEW" style="display: none;"></span>
    				<span id="PY_CELL_NUM_EDIT">
    					<input type="text" id="PY_CELL_NUM_TEXT" name="PY_CELL_NUM_TEXT"/>
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 样品类型
    			</th>
    			<td colspan="2">
    				<span id="SAMPLE_TYPE_VIEW" style="display: none;"></span>
    				<span id="SAMPLE_TYPE_EDIT">
	    				<input type="checkbox" id="SAMPLE_TYPE_A01" name="SAMPLE_TYPE_A01" val="A01"/>血液
	    				&nbsp; <input type="checkbox" id="SAMPLE_TYPE_A02" name="SAMPLE_TYPE_A02" val="A02"/>癌症（癌种<input type=Text id="SAMPLE_TYPE_A02_TEXT" name="SAMPLE_TYPE_A02_TEXT"/>）
	    				&nbsp; <input type="checkbox" id="SAMPLE_TYPE_A03" name="SAMPLE_TYPE_A03" val="A03"/>胚胎<input type=Text id="SAMPLE_TYPE_A03_TEXT" name="SAMPLE_TYPE_A03_TEXT"/>
	    				&nbsp; <input type="checkbox" id="SAMPLE_TYPE_A04" name="SAMPLE_TYPE_A04" val="A04"/>脑组织<input type=Text id="SAMPLE_TYPE_A04_TEXT" name="SAMPLE_TYPE_A04_TEXT"/>
	    				&nbsp; <input type="checkbox" id="SAMPLE_TYPE_A05" name="SAMPLE_TYPE_A05" val="A05"/>细胞系<input type=Text id="SAMPLE_TYPE_A05_TEXT" name="SAMPLE_TYPE_A05_TEXT"/>
	    				&nbsp; <input type="checkbox" id="SAMPLE_TYPE_A06" name="SAMPLE_TYPE_A06" val="A06"/>原生质体<input type=Text id="SAMPLE_TYPE_A06_TEXT" name="SAMPLE_TYPE_A06_TEXT"/>
	    				&nbsp; <input type="checkbox" id="SAMPLE_TYPE_A07" name="SAMPLE_TYPE_A07" val="A07"/>其他组织<input type=Text id="SAMPLE_TYPE_A07_TEXT" name="SAMPLE_TYPE_A07_TEXT"/>
	    				&nbsp; <input type="checkbox" id="SAMPLE_TYPE_A08" name="SAMPLE_TYPE_A08" val="A08"/>特殊说明（如同批做不同类型样本可在此说明）<input type=Text id="SAMPLE_TYPE_A08_TEXT" name="SAMPLE_TYPE_A08_TEXT"/>
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 预实验情况
    			</th>
    			<td colspan="2">
    				<span id="Y_EXP_RS_VIEW" style="display: none;"></span>
    				<span id="Y_EXP_RS_EDIT">
	    				是否进行过预实验：
	    				<br/>
	    				<input type="radio" id="Y_EXP_RS_A01" name="Y_EXP_RS_A01" val="A01"/>是(<input type="radio" id="Y_EXP_RS_A02" name="Y_EXP_RS_A02" val="A02"/>贝瑞&nbsp;<input type="radio" id="Y_EXP_RS_A03" name="Y_EXP_RS_A02" val="A03"/>客户)，
	    				预实验次数<input type="Text" id="Y_EXP_RS_A01_TEXT1" name="Y_EXP_RS_A01_TEXT1"/>，
	    				细胞平均活率<input type=Text id="Y_EXP_RS_A01_TEXT2" name="Y_EXP_RS_A01_TEXT2"/>，
	    				是否存在细胞碎片：<input type="radio" id="Y_EXP_RS_A04" name="Y_EXP_RS_A04" val="A04"/>是&nbsp;<input type="radio" id="Y_EXP_RS_A05" name="Y_EXP_RS_A04" val="A05"/>否
	    				<br/>
	    				<input type="radio" id="Y_EXP_RS_A06" name="Y_EXP_RS_A01" val="A06"/>未进行预实验
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 样品保存方式
    			</th>
    			<td colspan="2">
    				<span id="SAMPLE_SEND_MODE_VIEW" style="display: none;"></span>
    				<span id="SAMPLE_SEND_MODE_EDIT">
	    				<input type="checkbox" id="SAMPLE_SEND_MODE_A01" name="SAMPLE_SEND_MODE_A01" val="A01"/>新鲜样本
	    				&nbsp;<input type="checkbox" id="SAMPLE_SEND_MODE_A02" name="SAMPLE_SEND_MODE_A02" val="A02"/>保存液4℃避光<input type="text" id="SAMPLE_SEND_MODE_A02_TEXT" name="SAMPLE_SEND_MODE_A02_TEXT"/>小时
	    				&nbsp;<input type="checkbox" id="SAMPLE_SEND_MODE_A03" name="SAMPLE_SEND_MODE_A03" val="A03"/>液氮保存复苏样本，保存时间<input type="text" id="SAMPLE_SEND_MODE_A03_TEXT" name="SAMPLE_SEND_MODE_A03_TEXT"/>
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 服务方式
    			</th>
    			<td colspan="2">
    				<span id="SERVICE_MODE_VIEW" style="display: none;"></span>
    				<span id="SERVICE_MODE_EDIT">
	    				<input type="checkbox" id="SERVICE_MODE_A01" name="SERVICE_MODE_A01" val="A01"/>上门实验
	    				&nbsp;<input type="checkbox" id="SERVICE_MODE_A02" name="SERVICE_MODE_A02" val="A02"/>4℃寄送至贝瑞
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 样品处理方式
    			</th>
    			<td colspan="2">
    				<span id="SAMPLE_DISPOSE_MODE_VIEW" style="display: none;"></span>
    				<span id="SAMPLE_DISPOSE_MODE_EDIT">
	    				样本处理方式：
	    				<br/>
	    				<input type="checkbox" id="SAMPLE_DISPOSE_MODE_A01" name="SAMPLE_DISPOSE_MODE_A01" val="A01"/>贝瑞解离
	    				&nbsp;<input type="checkbox" id="SAMPLE_DISPOSE_MODE_A02" name="SAMPLE_DISPOSE_MODE_A02" val="A02"/>客户解离+贝瑞裂红、去死细胞
	    				&nbsp;<input type="checkbox" id="SAMPLE_DISPOSE_MODE_A03" name="SAMPLE_DISPOSE_MODE_A03" val="A03"/>客户解离+流式分选
	    				<br/>
	    				<input type="checkbox" id="SAMPLE_DISPOSE_MODE_A04" name="SAMPLE_DISPOSE_MODE_A04" val="A04"/>客户解离：细胞悬液清洗次数 <input type=text id="SAMPLE_DISPOSE_MODE_A04_TEXT1" name="SAMPLE_DISPOSE_MODE_A04_TEXT1"/>， 离心力<input type="text" id="SAMPLE_DISPOSE_MODE_A04_TEXT2" name="SAMPLE_DISPOSE_MODE_A04_TEXT2"/>，离心时间<input type="text" id="SAMPLE_DISPOSE_MODE_A04_TEXT3" name="SAMPLE_DISPOSE_MODE_A04_TEXT3"/>。
	    				<br/>
	    				细胞核制备：<input type="checkbox" id="SAMPLE_DISPOSE_MODE_A05" name="SAMPLE_DISPOSE_MODE_A05" val="A05"/>贝瑞制备&nbsp;<input type="checkbox" id="SAMPLE_DISPOSE_MODE_A06" name="SAMPLE_DISPOSE_MODE_A06" val="A06"/>客户制备
	    				<br/>
	    				<font color="red">注：客户解离请注意红细胞去除；解离后至少清洗两次，以去除解离过程中产生的游离核酸；离心建议300rcf 5min，离心力太大会造成细胞损伤，可以根据具体细胞类型设定离心转速，一般直径较小的细胞需要更高的转速，建议不超过500rcf；离心机根据转子的不同，沉淀出现的位置会有所区别，当细胞直径非常小或者是细胞量比较少的时候，因为沉淀可能会难以观察到，最好提前预判沉淀位置。</font>
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 建库类型
    			</th>
    			<td colspan="2">
    				<span id="LIB_TYPE_VIEW" style="display: none;"></span>
    				<span id="LIB_TYPE_EDIT">
	    				<input type="checkbox" id="LIB_TYPE_A01" name="LIB_TYPE_A01" val="A01"/>3’转录本+
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A02" name="LIB_TYPE_A02" val="A02"/>Feature barcoding 
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A03" name="LIB_TYPE_A03" val="A03"/>ATAC
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A04" name="LIB_TYPE_A04" val="A04"/>5’转录本+
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A05" name="LIB_TYPE_A05" val="A05"/>TCR+
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A06" name="LIB_TYPE_A06" val="A06"/>BCR+
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A07" name="LIB_TYPE_A07" val="A07"/>Feature barcoding
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A08" name="LIB_TYPE_A08" val="A08"/>CNV
	    				<br/>
	    				Feature barcoding试剂类型：Total-seq
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A09" name="LIB_TYPE_A09" val="A09"/>A
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A10" name="LIB_TYPE_A10" val="A10"/>B
	    				&nbsp;<input type="checkbox" id="LIB_TYPE_A11" name="LIB_TYPE_A11" val="A11"/>C
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 测序平台
    			</th>
    			<td colspan="2">
    				<span id="SEQ_PLATFORM_VIEW" style="display: none;"></span>
    				<span id="SEQ_PLATFORM_EDIT">
	    				<input type="checkbox" id="SEQ_PLATFORM_A01" name="SEQ_PLATFORM_A01" val="A01"/>Novaseq 150PE
	    				&nbsp;<input type="checkbox" id="SEQ_PLATFORM_A02" name="SEQ_PLATFORM_A02" val="A02"/>Novaseq 50PE
	    				&nbsp;<input type="checkbox" id="SEQ_PLATFORM_A03" name="SEQ_PLATFORM_A03" val="A03"/>CN500 75PE
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th>
    				<font color="red">*</font> 测序数据量
    			</th>
    			<td colspan="2">
    				<span id="DATA_NUM_STR_VIEW" style="display: none;"></span>
    				<span id="DATA_NUM_STR_EDIT">
	    				<input type="checkbox" id="DATA_NUM_STR_A01" name="DATA_NUM_STR_A01" val="A01"/>小测细胞数×建议数据量
	    				&nbsp;<input type="checkbox" id="DATA_NUM_STR_A02" name="DATA_NUM_STR_A02" val="A02"/>其他：<input type="text" id="DATA_NUM_STR_A02_TEXT" name="DATA_NUM_STR_A02_TEXT"/>
    				</span>
    			</td>
    		</tr>
    		<tr>
    			<th colspan="3" style="text-align: left;">
    				<font color="red">
    				以上项目为必填项: 带*的尤为重要, 如果没有填写, 将被视为无效样品; 延误的时间将不被记录到项目周期内。
    				</font>
    			</th>
    		</tr>
    		<tr>
    			<th colspan="3" style="text-align: left;">
    				<font>
    				建议测序数据量：
					转录组：30 M base /cell；TCR/BCR：3M base /cell；feature barcoding 3M base /cell ；CNV：225 M base /cell
					建议Novaseq6000上机,转录组和CNV默认先小测10G（34M），用于评估细胞数和文库质量；客户收到小数据量评估结果后确认大数据量测序。
					单细胞 ATAC文库CN500 75PE（中通量130M reads /FC；高通量 400M reads /FC）或NOVA 50PE包 lane(400M/lane)上机，建议 5万 reads/nuclei，不做小数据量测试和评估。

    				</font>
    			</th>
    		</tr>
    		<tr>
    			<th colspan="3" style="text-align: left;">
    				<font color="red">
    				注意:
    				<br/>
    				1. 样品名称请采用字母、数字和英文短横线(即-)命名, 其他字符一概不用；
    				<br/>
    				2. 字符长度请控制在10字符以内; 首位不能使用数字；
    				<br/>
    				3. 且避免使用如下名称CON, PRN, AUX, CLOCK$, NUL, COM1, COM2, COM3, COM4, COM5, COM6, COM7, COM8, COM9, LPT1等, 以免引起系统冲突。
    				</font>
    			</th>
    		</tr>
    	</table>
    	<!-- ------------------------------------ 送样单: 内容 ------------------------------------ -->
    	
    	<!-- ------------------------------------ 送样单: 样本明细 ------------------------------------ -->
    	<div id="SAMPLE_SEND_NO_MX_tablesGrid" style="text-align: left;"></div>
    	<!-- ------------------------------------ 送样单: 样本明细 ------------------------------------ -->
    	
    	<!-- ------------------------------------ 送样单: 内容附加 ------------------------------------ -->
    	<table id="SAMPLE_SEND_NO_CONTENT_2">
    		<tr>
    			<td>
    				<b>
    				<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>
    				<font color="red">告知客户：</font>单细胞需要达到一定的测序数据量（不同类型及不同时期的细胞所需数据量会有所差异），以达到期望的分析效果；如实验时您临时改变了目标细胞数，我们有义务告知您，对于一个样本增加目标细胞数不会增加建库费用，但可能需要提升样本的测序数据量，相应的测序费用会增加。
    				</b>
    			</td>
    		</tr>
    		<tr>
    			<td align="right">
    				<b>
	                <br/><br/>
	                <br/><br/>
	    			客户签字: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                <br/><br/>
					 送样日期: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	                <br/><br/>
	                <br/><br/>
	                </b>
    			</td>
    		</tr>
    		<tr>
    			<td>
    				<b>
    				注： 1.请务必认真填写此单，以免因信息疏漏带来不必要的损失；
    				<br/>
    				 &nbsp;&nbsp; 2.请在送样前提前填写此单，打印后同样品一同交给我方，并发送电子版至项目联系人。
    				</b>
    			</td>
    		</tr>
    	</table>
    	<!-- ------------------------------------ 送样单: 内容附加 ------------------------------------ -->
    	
    	<!-- ------------------------------------ 送样单: 页脚 ------------------------------------ -->
    	<br/>
    	<br/>
    	<table id="SAMPLE_SEND_NO_PAGR_FOOT">
    		<tr style="border-top: #C0B2D1 solid 3px; height: 56px;">
    			<td style="width: 33%; text-align: left;">
    				<h3 style="color: #808080;">
    					&nbsp;
    					北京贝瑞和康生物技术有限公司
    				</h3>
    			</td>
    			<td style="width: 34%; text-align: center;">
    				<h3 style="color: #808080;">
    					www.berrygenomics.com
    				</h3>
    			</td>
    			<td style="width: 33%; text-align: right;">
    				<h3 style="color: #808080;">
    					010-84409702
    					&nbsp;
    				</h3>
    			</td>
    		</tr>
    	</table>
    	<!-- ------------------------------------ 送样单: 页脚 ------------------------------------ -->
    	</form>
    </div>
</div>