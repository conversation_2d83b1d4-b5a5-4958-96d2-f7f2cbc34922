$(document).ready(function() {
    var pathValue="berry-prod-sample-gatherSampleProject-addMonpSeq-addSample";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_SAMPLE_INFO_DETAIL"
        };
    }
    
    var SIB_ID;
    var SAMPLE_ID;
    var addPathValue;
    var VIEW_MODE;
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	// 设置选框字段的选项
    	
    	// 复制最后一行数据
		var SAMPLE_GRID_LAST_ROW = params.SAMPLE_GRID_LAST_ROW;
		console.log(params)
    	if (SAMPLE_GRID_LAST_ROW) {
    		params.SAMPLE_CODE = SAMPLE_GRID_LAST_ROW.SAMPLE_CODE ? SAMPLE_GRID_LAST_ROW.SAMPLE_CODE : "";
    		params.CELL_SURVIVAL_RATE = SAMPLE_GRID_LAST_ROW.CELL_SURVIVAL_RATE ? SAMPLE_GRID_LAST_ROW.CELL_SURVIVAL_RATE : "";
    		params.CONCENTRATION = SAMPLE_GRID_LAST_ROW.CONCENTRATION ? SAMPLE_GRID_LAST_ROW.CONCENTRATION : "";
    		params.VOLUME = SAMPLE_GRID_LAST_ROW.VOLUME ? SAMPLE_GRID_LAST_ROW.VOLUME : "";
    		params.PY_CELL_NUM = SAMPLE_GRID_LAST_ROW.PY_CELL_NUM ? SAMPLE_GRID_LAST_ROW.PY_CELL_NUM : "";
    	}
    	
    	SIB_ID = params.SIB_ID;
    	SAMPLE_ID = params.ID;
        addPathValue = params.addPathValue;
        VIEW_MODE = params.VIEW_MODE;
    	if (VIEW_MODE == "edit") {
    		$("#saveSampleInfo"+pathValue).show();
    	} else {
    		$("#saveSampleInfo"+pathValue).hide();
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
	}
    
    // 校验: 样品名称
    var checkSampleName = function (sampleName) {
    	var checkRS = true;
    	var errMsg = "";
    	// 1. 样品名称请采用字母、数字和英文短横线(即-)命名, 其他字符一概不用；
    	var reg_1 = /^[0-9a-zA-Z\-]+$/;
    	if ( !sampleName.match(reg_1) ) {
    		errMsg += "样品名称请采用字母、数字和英文短横线(即-)命名, 其他字符一概不用；";
    	}
		// 2. 字符长度请控制在10字符以内; 首位不能使用数字；
    	if (sampleName.length>10) {
    		errMsg += errMsg ? "<br>" : "";
    		errMsg += "字符长度请控制在10字符以内;";
    	}
    	var reg_2 = /^[0-9]+[0-9a-zA-Z\-]+$/;
    	var reg_2 = /^[0-9].*$/;
    	if (sampleName.match(reg_2) ) {
    		errMsg += errMsg ? "<br>" : "";
    		errMsg += "首位不能使用数字；";
    	}
		// 3. 且避免使用如下名称CON, PRN, AUX, CLOCK$, NUL, COM1, COM2, COM3, COM4, COM5, COM6, COM7, COM8, COM9, LPT1等, 以免引起系统冲突。
    	if (   sampleName.indexOf("CON") >= 0
    		|| sampleName.indexOf("PRN") >= 0
    		|| sampleName.indexOf("AUX") >= 0
    		|| sampleName.indexOf("CLOCK$") >= 0
    		|| sampleName.indexOf("NUL") >= 0
    		|| sampleName.indexOf("COM1") >= 0
    		|| sampleName.indexOf("COM2") >= 0
    		|| sampleName.indexOf("COM3") >= 0
    		|| sampleName.indexOf("COM4") >= 0
    		|| sampleName.indexOf("COM5") >= 0
    		|| sampleName.indexOf("COM6") >= 0
    		|| sampleName.indexOf("COM7") >= 0
    		|| sampleName.indexOf("COM8") >= 0
    		|| sampleName.indexOf("COM9") >= 0
    		|| sampleName.indexOf("LPT1") >= 0
    	) {
    		errMsg += errMsg ? "<br>" : "";
    		errMsg += "避免使用如下名称CON, PRN, AUX, CLOCK$, NUL, COM1, COM2, COM3, COM4, COM5, COM6, COM7, COM8, COM9, LPT1等, 以免引起系统冲突。";
    	}
    	
    	if (errMsg) {
    		alertMsg(errMsg, "error");
    		checkRS = false;
    	}
    	
    	return checkRS;
    }
    var submit = function() {
    	var formparams = getJsonByForm("form", pathValue);
    	// 数据格式自定义校验
    	// CHECK SAMPLE NAME
    	if ( !checkSampleName(formparams.SAMPLE_NAME) ) {
    		return;
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	SAMPLE_ID = result.ID;
                	updateSAMPLE_COUNT();
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    var updateSAMPLE_COUNT = function() {// 更新主单样本数量
    	var params={"SIB_ID":SIB_ID, "SAMPLE_ID":SAMPLE_ID};
    	var url= "berry/prod/sample/sample/updateSAMPLE_COUNT";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#SAMPLE_COUNT"+addPathValue).val(result.SAMPLE_COUNT);
	                //提交成功
	                alertMsg("提交成功","success",function(){
	                    funcExce(addPathValue+"refreshGrid");//执行回调
	                    funcExce(pathValue+"close");//关闭页面
	                });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 