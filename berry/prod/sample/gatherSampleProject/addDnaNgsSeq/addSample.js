$(document).ready(function() {
    var pathValue="berry-prod-sample-gatherSampleProject-addDnaNgsSeq-addSample";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName:"BR_SAMPLE_INFO_DETAIL"
        };
    }
    
    var SIB_ID;
    var SAMPLE_ID;
    var addPathValue;
    var VIEW_MODE;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	setDataSourceToFormElement("SAMPLE_TYPE","form",pathValue,params.SAMPLE_TYPE_OPTIONS);
    	setDataSourceToFormElement("SAMPLE_STATUS","form",pathValue,params.SAMPLE_STATE_OPTIONS);
    	setDataSourceToFormElement("SEQ_TYPE","form",pathValue,params.SEQ_TYPE_OPTIONS);
    	// 复制最后一行数据
    	console.log(params)
		var SAMPLE_GRID_LAST_ROW = params.SAMPLE_GRID_LAST_ROW;
    	if (SAMPLE_GRID_LAST_ROW) {
    		
    		params.QUANTIFY_METHOD = SAMPLE_GRID_LAST_ROW.QUANTIFY_METHOD ? SAMPLE_GRID_LAST_ROW.QUANTIFY_METHOD : "";
    		params.CONCENTRATION = SAMPLE_GRID_LAST_ROW.CONCENTRATION ? SAMPLE_GRID_LAST_ROW.CONCENTRATION : "";
    		params.VOLUME = SAMPLE_GRID_LAST_ROW.VOLUME ? SAMPLE_GRID_LAST_ROW.VOLUME : "";
    		params.SPECIES_NAME = SAMPLE_GRID_LAST_ROW.SPECIES_NAME ? SAMPLE_GRID_LAST_ROW.SPECIES_NAME : "";
    		params.SAMPLE_TYPE = SAMPLE_GRID_LAST_ROW.SAMPLE_TYPE ? SAMPLE_GRID_LAST_ROW.SAMPLE_TYPE : "";
    		params.SAMPLE_STATUS = SAMPLE_GRID_LAST_ROW.SAMPLE_STATUS ? SAMPLE_GRID_LAST_ROW.SAMPLE_STATUS : "";
    		params.SEQ_TYPE = SAMPLE_GRID_LAST_ROW.SEQ_TYPE ? SAMPLE_GRID_LAST_ROW.SEQ_TYPE : "";
    	}
    	
    	SIB_ID = params.SIB_ID;
    	SAMPLE_ID = params.ID;
        addPathValue = params.addPathValue;
        VIEW_MODE = params.VIEW_MODE;
    	if (VIEW_MODE == "edit") {
    		$("#saveSampleInfo"+pathValue).show();
    	} else {
    		$("#saveSampleInfo"+pathValue).hide();
    	}
    	
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
	}
    
    var submit = function() {
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	SAMPLE_ID = result.ID;
                	updateSAMPLE_COUNT();
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    var updateSAMPLE_COUNT = function() {// 更新主单样本数量
    	var params={"SIB_ID":SIB_ID, "SAMPLE_ID":SAMPLE_ID};
    	var url= "berry/prod/sample/sample/updateSAMPLE_COUNT";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
	            	$("#SAMPLE_COUNT"+addPathValue).val(result.SAMPLE_COUNT);
	                //提交成功
	                alertMsg("提交成功","success",function(){
	                    funcExce(addPathValue+"refreshGrid");//执行回调
	                    funcExce(pathValue+"close");//关闭页面
	                });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });
 