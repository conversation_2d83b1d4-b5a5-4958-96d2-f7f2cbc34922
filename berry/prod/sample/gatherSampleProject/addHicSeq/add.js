$(document).ready(function() {
    var pathValue="berry-prod-sample-gatherSampleProject-addHicSeq-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName : "BR_SAMPLE_INFO_BASIC",
            // 设置表单字段: 默认值
            SAMPLE_SOURCE:"MIS",
            SAMPLE_SEND_TYPE:"Hi-C待提取材料信息单",
        };
    }
    var SIB_ID="";
    var pPathValue;
    var VIEW_MODE;
    var card_id = "CARD-"+pathValue;
    var SAMPLE_SEND_NO_MX_tablesGrid;
    
    //材料类型
    var SAMPLE_TYPE_VIEW_TEXT = {
    	"A01":"人类血液",
    	"A02":"哺乳动物血液",
    	"A03":"鸟类血液",
    	"A04":"鱼类血液",

    	"B01":"植物叶片（推荐优先级次序）：",
    	"B01_1":"子叶",
    	"B01_2":"幼苗",
    	"B01_3":"成熟植株嫩叶",
    	"B01_99":"其他：",
    	
    	"B02Y":"是",
    	"B02N":"否",
    	"B03Y":"是",
    	"B03N":"否",
    	
    	"C01":"哺乳动物组织（推荐优先级次序）：",
    	"C01_1":"脾脏",
    	"C01_2":"心脏",
    	"C01_3":"肌肉",
    	"C01_99":"其他：",
    	
    	"D01":"昆虫样本（推荐优先级次序）：",
    	"D01_1":"胚胎",
    	"D01_2":"幼虫：",
    	"D01_3":"蛹",
    	"D01_4":"成虫",
    	"D01_99":"其他：",
    	
    	"E01":"海洋软体（推荐优先级次序）：",
    	"E01_1":"内脏",
    	"E01_2":"幼虫：",
    	"E01_3":"精子",
    	"E01_4":"血液",
    	"E01_5":"肌肉",
    	"E01_99":"其他：",
    	
    	"F01":"细胞样本：",
    	"F99":"其他类型样本：",
    	
    };
    //寄送方式
    var SAMPLE_SEND_MODE_CONFIRM_VIEW_TEXT = {
    	"A01":"常温",
    	"A02":"干冰",
    	"A03":"冰袋",
    	"A04":"冷藏箱",
    	"A05":"液氮",
    	"A99":"其他： ",
    };
    //重要提示
    var IMPORTANT_NOTE_VIEW_TEXT = {
    	"A01Y":"是",
    	"A01N":"否",
    	"A02Y":"是",
    	"A02N":"否",
    	"A03Y":"是",
    	"A03N":"否",
    }
    //质控不合格材料的处理
    var SAMPLE_SURPLUS_UNQUA_DISPOSE_VIEW_TEXT = {
		"A01":"公司处理",
		"A02":"返还",
    };
    //项目完成后剩余: 处理
    var SAMPLE_SURPLUS_DISPOSE_VIEW_TEXT = {
    	"A01":"依据合同规定",
    	"A02":"返还",
    };
    
    //确认接收
    var SAMPLE_SEND_MODE_VIEW_TEXT = {
		"A01":"常温：健壮/萎蔫/冻伤",
		"A02":"干冰：充足/很少/无；  材料状态：冷冻状态/已化冻",
		"A03":"冰袋：未完全融化/已完全融化；    材料状态：冷冻状态/已化冻",
		"A99":"其他：",
    };
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	// 设置送样日期显示样式
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).before("送样日期:");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).css("width","200px");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).parent().css("border","0px");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).parent().css("font-size", "16px");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).parent().css("font-weight", "bold");
    	
    	var nowDate = new Date();
    	var nowDateString = nowDate.getFullYear()+"-"+( nowDate.getMonth()+1 )+"-"+nowDate.getDate();
    	params["SAMPLE_SEND_DATE"] = params["SAMPLE_SEND_DATE"] ? params["SAMPLE_SEND_DATE"] : nowDateString;
    	// 接收参数
    	SIB_ID = params.ID ? params.ID : "";
    	pPathValue = params.pPathValue;
    	VIEW_MODE = params.VIEW_MODE;
    	if (VIEW_MODE == "edit") {
    		$("#"+card_id+" #sampleSendNoSaveSubmit").show();
    	} else {
    		$("#"+card_id+" #sampleSendNoSaveSubmit").hide();
    	}
    	// 初始化表单
        getInfo("form", pathValue, params);
        
        if (params && params.ID) {
        	// 读取数据库获取表单数据
        	var url="system/jdbc/query/info/"+initData().tableName;
            getInfo("form", pathValue, params, url, function(viewModel, params) {
            	// ------------------------------------------------------------------------ 组合值处理
            	// 材料类型
            	var SAMPLE_TYPE = params["SAMPLE_TYPE"] ? params["SAMPLE_TYPE"] : "";
            	var SAMPLE_TYPE_A_VIEW = "";
            	var SAMPLE_TYPE_B_VIEW = "";
            	var SAMPLE_TYPE_C_VIEW = "";
            	var SAMPLE_TYPE_D_VIEW = "";
            	var SAMPLE_TYPE_E_VIEW = "";
            	var SAMPLE_TYPE_F_VIEW = "";
            	if (SAMPLE_TYPE!="") {
            		SAMPLE_TYPE = SAMPLE_TYPE.split("@V&S@");
            		for (var i=0; i<SAMPLE_TYPE.length; i++) {
            			SAMPLE_TYPE2 = SAMPLE_TYPE[i].split("@T&S@");
            			// 初始化页面显示: 编辑模式
            			if (SAMPLE_TYPE2.length>0) {
            				$("#"+card_id+" #SAMPLE_TYPE_"+SAMPLE_TYPE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_TYPE2.length>1) {
            				$("#"+card_id+" #SAMPLE_TYPE_"+SAMPLE_TYPE2[0]+"_TEXT"+pathValue).val(SAMPLE_TYPE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_TYPE2.length>0 && SAMPLE_TYPE2[0].indexOf("A")==0) {
            				SAMPLE_TYPE_A_VIEW += SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
            				SAMPLE_TYPE_A_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            				SAMPLE_TYPE_A_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			if (SAMPLE_TYPE2.length>0 && SAMPLE_TYPE2[0].indexOf("B")==0) {
        					if(SAMPLE_TYPE2[0].indexOf("B02")==0){
        						SAMPLE_TYPE_B_VIEW += "<br/>";
        						SAMPLE_TYPE_B_VIEW += "是否多酚：";
        					}
        					if(SAMPLE_TYPE2[0].indexOf("B03")==0){
        						SAMPLE_TYPE_B_VIEW += "<br/>";
        						SAMPLE_TYPE_B_VIEW += "是否高糖：";
        					}
        					SAMPLE_TYPE_B_VIEW += SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
        					SAMPLE_TYPE_B_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            			}
            			if (SAMPLE_TYPE2.length>0 && SAMPLE_TYPE2[0].indexOf("C")==0) {
            				SAMPLE_TYPE_C_VIEW += SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
            				SAMPLE_TYPE_C_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            				SAMPLE_TYPE_C_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			if (SAMPLE_TYPE2.length>0 && SAMPLE_TYPE2[0].indexOf("D")==0) {
            				SAMPLE_TYPE_D_VIEW += SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
            				SAMPLE_TYPE_D_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            				SAMPLE_TYPE_D_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			if (SAMPLE_TYPE2.length>0 && SAMPLE_TYPE2[0].indexOf("E")==0) {
            				SAMPLE_TYPE_E_VIEW += SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
            				SAMPLE_TYPE_E_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            				SAMPLE_TYPE_E_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			if (SAMPLE_TYPE2.length>0 && SAMPLE_TYPE2[0].indexOf("F")==0) {
            				SAMPLE_TYPE_F_VIEW += SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
            				SAMPLE_TYPE_F_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            				SAMPLE_TYPE_F_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
        		// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_TYPE_A_VIEW").html(SAMPLE_TYPE_A_VIEW);
    				$("#"+card_id+" #SAMPLE_TYPE_A_VIEW").show();
    				$("#"+card_id+" #SAMPLE_TYPE_A_EDIT").hide();
    				if ( !SAMPLE_TYPE_A_VIEW && (SAMPLE_TYPE_B_VIEW || SAMPLE_TYPE_C_VIEW) ) {//至少留一行空行
    					$("#"+card_id+" #SAMPLE_TYPE_A_VIEW").parent().hide();
    				}
        			$("#"+card_id+" #SAMPLE_TYPE_B_VIEW").html(SAMPLE_TYPE_B_VIEW);
    				$("#"+card_id+" #SAMPLE_TYPE_B_VIEW").show();
    				$("#"+card_id+" #SAMPLE_TYPE_B_EDIT").hide();
    				if ( !SAMPLE_TYPE_B_VIEW ) {
    					$("#"+card_id+" #SAMPLE_TYPE_B_VIEW").parent().hide();
    				}
        			$("#"+card_id+" #SAMPLE_TYPE_C_VIEW").html(SAMPLE_TYPE_C_VIEW);
    				$("#"+card_id+" #SAMPLE_TYPE_C_VIEW").show();
    				$("#"+card_id+" #SAMPLE_TYPE_C_EDIT").hide();
    				if ( !SAMPLE_TYPE_C_VIEW ) {
    					$("#"+card_id+" #SAMPLE_TYPE_C_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SAMPLE_TYPE_D_VIEW").html(SAMPLE_TYPE_D_VIEW);
    				$("#"+card_id+" #SAMPLE_TYPE_D_VIEW").show();
    				$("#"+card_id+" #SAMPLE_TYPE_D_EDIT").hide();
    				if ( !SAMPLE_TYPE_D_VIEW ) {
    					$("#"+card_id+" #SAMPLE_TYPE_D_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SAMPLE_TYPE_E_VIEW").html(SAMPLE_TYPE_E_VIEW);
    				$("#"+card_id+" #SAMPLE_TYPE_E_VIEW").show();
    				$("#"+card_id+" #SAMPLE_TYPE_E_EDIT").hide();
    				if ( !SAMPLE_TYPE_E_VIEW ) {
    					$("#"+card_id+" #SAMPLE_TYPE_E_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SAMPLE_TYPE_F_VIEW").html(SAMPLE_TYPE_F_VIEW);
    				$("#"+card_id+" #SAMPLE_TYPE_F_VIEW").show();
    				$("#"+card_id+" #SAMPLE_TYPE_F_EDIT").hide();
    				if ( !SAMPLE_TYPE_F_VIEW ) {
    					$("#"+card_id+" #SAMPLE_TYPE_F_VIEW").parent().hide();
    				}
        		}
        		
            	// 寄送方式
            	var SAMPLE_SEND_MODE = params["SAMPLE_SEND_MODE"] ? params["SAMPLE_SEND_MODE"] : "";
            	var SAMPLE_SEND_MODE_VIEW = "";
            	if (SAMPLE_SEND_MODE!="") {
            		SAMPLE_SEND_MODE = SAMPLE_SEND_MODE.split("@V&S@");
            		for (var i=0; i<SAMPLE_SEND_MODE.length; i++) {
            			SAMPLE_SEND_MODE2 = SAMPLE_SEND_MODE[i].split("@T&S@");
            			// 初始化页面显示: 编辑模式
            			if (SAMPLE_SEND_MODE2.length>0) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_"+SAMPLE_SEND_MODE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_SEND_MODE2.length>1) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_"+SAMPLE_SEND_MODE2[0]+"_TEXT"+pathValue).val(SAMPLE_SEND_MODE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_SEND_MODE2.length>0) {
            				SAMPLE_SEND_MODE_VIEW += SAMPLE_SEND_MODE_CONFIRM_VIEW_TEXT[ SAMPLE_SEND_MODE2[0] ];
            				SAMPLE_SEND_MODE_VIEW += SAMPLE_SEND_MODE2.length>1 ? SAMPLE_SEND_MODE2[1] : "";
            				SAMPLE_SEND_MODE_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
        		// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_SEND_MODE_VIEW").html(SAMPLE_SEND_MODE_VIEW);
    				$("#"+card_id+" #SAMPLE_SEND_MODE_VIEW").show();
    				$("#"+card_id+" #SAMPLE_SEND_MODE_EDIT").hide();
        		}
        		//重要提示
        		var IMPORTANT_NOTE = params["IMPORTANT_NOTE"] ? params["IMPORTANT_NOTE"] : "";
            	var IMPORTANT_NOTE_VIEW_A = "";
            	var IMPORTANT_NOTE_VIEW = "";
            	if (IMPORTANT_NOTE!="") {
            		IMPORTANT_NOTE = IMPORTANT_NOTE.split("@V&S@");
            		for (var i=0; i<IMPORTANT_NOTE.length; i++) {
            			IMPORTANT_NOTE2 = IMPORTANT_NOTE[i].split("@T&S@");
            			// 初始化页面显示: 编辑模式
            			if (IMPORTANT_NOTE2.length>0) {
            				$("#"+card_id+" #IMPORTANT_NOTE_"+IMPORTANT_NOTE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_SEND_MODE2.length>1) {
            				$("#"+card_id+" #IMPORTANT_NOTE_"+IMPORTANT_NOTE2[0]+"_TEXT"+pathValue).val(IMPORTANT_NOTE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (IMPORTANT_NOTE2.length>0) {
            				if(IMPORTANT_NOTE2[0].indexOf("A01")==0){
            					IMPORTANT_NOTE_VIEW_A += "样本是否有特殊处理？&nbsp;"
            					IMPORTANT_NOTE_VIEW_A += IMPORTANT_NOTE_VIEW_TEXT[ IMPORTANT_NOTE2[0] ];
                				IMPORTANT_NOTE_VIEW_A += IMPORTANT_NOTE2.length>1 ? IMPORTANT_NOTE2[1] : "";
                				IMPORTANT_NOTE_VIEW_A += "<br/>";
            				}
            				if(IMPORTANT_NOTE2[0].indexOf("A02")==0){
            					IMPORTANT_NOTE_VIEW_A += "冷冻样本是否有冻融过？&nbsp;"
            					IMPORTANT_NOTE_VIEW_A += IMPORTANT_NOTE_VIEW_TEXT[ IMPORTANT_NOTE2[0] ];
            					IMPORTANT_NOTE_VIEW_A += IMPORTANT_NOTE2.length>1 ? IMPORTANT_NOTE2[1] : "";
            					IMPORTANT_NOTE_VIEW_A += "<br/>";
            				}
            				if(IMPORTANT_NOTE2[0].indexOf("A03")==0){
            					IMPORTANT_NOTE_VIEW_A += "保存条件是否有异常？&nbsp;"
            					IMPORTANT_NOTE_VIEW_A += IMPORTANT_NOTE_VIEW_TEXT[ IMPORTANT_NOTE2[0] ];
            					IMPORTANT_NOTE_VIEW_A += IMPORTANT_NOTE2.length>1 ? IMPORTANT_NOTE2[1] : "";
            					IMPORTANT_NOTE_VIEW_A += "<br/>";
            				}
            			}
            			
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #IMPORTANT_NOTE_VIEW").html(IMPORTANT_NOTE_VIEW_A);
    				$("#"+card_id+" #IMPORTANT_NOTE_VIEW").show();
    				$("#"+card_id+" #IMPORTANT_NOTE_EDIT").hide();
        		}
            	// 确认接收(贝瑞填写)
            	var SAMPLE_SEND_MODE_CONFIRM = params["SAMPLE_SEND_MODE_CONFIRM"] ? params["SAMPLE_SEND_MODE_CONFIRM"] : "";
            	var SAMPLE_SEND_MODE_CONFIRM_VIEW = "";
            	if (SAMPLE_SEND_MODE_CONFIRM!="") {
            		SAMPLE_SEND_MODE_CONFIRM = SAMPLE_SEND_MODE_CONFIRM.split("@V&S@");
            		for (var i=0; i<SAMPLE_SEND_MODE_CONFIRM.length; i++) {
            			SAMPLE_SEND_MODE_CONFIRM2 = SAMPLE_SEND_MODE_CONFIRM[i].split("@T&S@");
            			// 初始化页面显示: 编辑模式
            			if (SAMPLE_SEND_MODE_CONFIRM2.length>0) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_"+SAMPLE_SEND_MODE_CONFIRM2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_SEND_MODE_CONFIRM2.length>1) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_"+SAMPLE_SEND_MODE_CONFIRM2[0]+"_TEXT"+pathValue).val(SAMPLE_SEND_MODE_CONFIRM2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_SEND_MODE_CONFIRM2.length>0) {
            				SAMPLE_SEND_MODE_CONFIRM_VIEW += SAMPLE_SEND_MODE_VIEW_TEXT[ SAMPLE_SEND_MODE_CONFIRM2[0] ];
            				SAMPLE_SEND_MODE_CONFIRM_VIEW += SAMPLE_SEND_MODE_CONFIRM2.length>1 ? SAMPLE_SEND_MODE_CONFIRM2[1] : "";
            				SAMPLE_SEND_MODE_CONFIRM_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
        		// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_VIEW").html(SAMPLE_SEND_MODE_CONFIRM_VIEW);
    				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_VIEW").show();
    				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_EDIT").hide();
        		}
        		
            	// 质控不合格材料的处理：
            	var SAMPLE_UNQUALIFIED_DISPOSE = params["SAMPLE_UNQUALIFIED_DISPOSE"] ? params["SAMPLE_UNQUALIFIED_DISPOSE"] : "";
            	var SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW = "";
            	if (SAMPLE_UNQUALIFIED_DISPOSE!="") {
            		SAMPLE_UNQUALIFIED_DISPOSE = SAMPLE_UNQUALIFIED_DISPOSE.split("@V&S@");
            		for (var i=0; i<SAMPLE_UNQUALIFIED_DISPOSE.length; i++) {
            			SAMPLE_UNQUALIFIED_DISPOSE2 = SAMPLE_UNQUALIFIED_DISPOSE[i].split("@T&S@");
            			// 初始化页面显示: 编辑模式
            			if (SAMPLE_UNQUALIFIED_DISPOSE2.length>0) {
            				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_"+SAMPLE_UNQUALIFIED_DISPOSE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_UNQUALIFIED_DISPOSE2.length>1) {
            				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_"+SAMPLE_UNQUALIFIED_DISPOSE2[0]+"_TEXT"+pathValue).val(SAMPLE_UNQUALIFIED_DISPOSE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_UNQUALIFIED_DISPOSE2.length>0 && SAMPLE_UNQUALIFIED_DISPOSE2[0].indexOf("A")==0) {
            				SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW += SAMPLE_SURPLUS_UNQUA_DISPOSE_VIEW_TEXT[ SAMPLE_UNQUALIFIED_DISPOSE2[0] ];
            				SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW += SAMPLE_UNQUALIFIED_DISPOSE2.length>1 ? SAMPLE_UNQUALIFIED_DISPOSE2[1] : "";
            				SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
        		// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW").html(SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW);
    				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW").show();
    				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_A_EDIT").hide();
        		}
            	// 项目完成后的剩余样品做如下处理
            	var SAMPLE_SURPLUS_DISPOSE = params["SAMPLE_SURPLUS_DISPOSE"] ? params["SAMPLE_SURPLUS_DISPOSE"] : "";
            	
            	var SAMPLE_SURPLUS_DISPOSE_A_VIEW = "";
            	if (SAMPLE_SURPLUS_DISPOSE!="") {
            		SAMPLE_SURPLUS_DISPOSE = SAMPLE_SURPLUS_DISPOSE.split("@V&S@");
            		for (var i=0; i<SAMPLE_SURPLUS_DISPOSE.length; i++) {
            			SAMPLE_SURPLUS_DISPOSE2 = SAMPLE_SURPLUS_DISPOSE[i].split("@T&S@");
            			// 初始化页面显示: 编辑模式
            			if (SAMPLE_SURPLUS_DISPOSE2.length>0) {
            				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_"+SAMPLE_SURPLUS_DISPOSE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_SURPLUS_DISPOSE2.length>1) {
            				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_"+SAMPLE_SURPLUS_DISPOSE2[0]+"_TEXT"+pathValue).val(SAMPLE_SURPLUS_DISPOSE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_SURPLUS_DISPOSE2.length>0 && SAMPLE_SURPLUS_DISPOSE2[0].indexOf("A")==0) {
            				SAMPLE_SURPLUS_DISPOSE_A_VIEW += SAMPLE_SURPLUS_DISPOSE_VIEW_TEXT[ SAMPLE_SURPLUS_DISPOSE2[0] ];
            				SAMPLE_SURPLUS_DISPOSE_A_VIEW += SAMPLE_SURPLUS_DISPOSE2.length>1 ? SAMPLE_SURPLUS_DISPOSE2[1] : "";
            				SAMPLE_SURPLUS_DISPOSE_A_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
        		// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_A_VIEW").html(SAMPLE_SURPLUS_DISPOSE_A_VIEW);
    				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_A_VIEW").show();
    				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_A_EDIT").hide();
        		}
            	
            	// 送样单号: 条码
            	getSAMPLE_SEND_NO_BARCODE();
            });
        } else {
        	//获取送样单号
        	var url="berry/serialNumberManage/sample/sampleSendNo";
        	var params = { "SAMPLE_SEND_TYPE":initData().SAMPLE_SEND_TYPE };
        	$.fn.ajaxPost({
                ajaxUrl:url,
                ajaxData: params,
                ajaxType:"post",
                succeed:function(res){
                    if(res["code"]>0){
			        	$("#"+card_id+" #SAMPLE_SEND_NO"+pathValue).val(res.SAMPLE_SEND_NO);
			        	// 送样单号: 条码
			        	getSAMPLE_SEND_NO_BARCODE();
	                }else{
	                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
	                }
	            },
	            failed:function(res){
	                alertMsg("网络请求出现异常!","error");
	            }
	        });
        }
    	
    	// 初始化样本明细列表
    	initSAMPLE_SEND_NO_MX_tablesGrid();
    }
    var getSAMPLE_SEND_NO_BARCODE = function() {
    	var barcodeVal = $("#"+card_id+" #SAMPLE_SEND_NO"+pathValue).val();
//    	// 后台模式
//    	var srcURL = apiPath + "berry/system/barcode/barcode4j/out?barcode=" + barcodeVal;
//    	$("#"+card_id+" #SAMPLE_SEND_NO_BARCODE").attr("src", srcURL);
    	//前端模式
        $("#"+card_id+" #SAMPLE_SEND_NO_BARCODE").kendoBarcode({
            value: barcodeVal,
            type: "code39",// ean8  code128  code39
            width: 367,
            height: 126
        });
    }
    var initSAMPLE_SEND_NO_MX_tablesGrid = function() {
//    	var gridWidth = $("#"+card_id+" SAMPLE_SEND_NO_PAGR_HEADER").width();
//    	gridWidth = fullw * 0.98;
    	
        var toolbar = null;
        if (VIEW_MODE == "edit") {
        	toolbar = getButtonTemplates(pathValue,[
                {name:"add",target:"addSample",title:"添加待提取样品"},
                {name:"edit",target:"editSample",title:"修改待提取样品"},
                {name:"delete",target:"deleteSample",title:"删除待提取样品"},
            ]);
        } else {
        	toolbar = getButtonTemplates(pathValue,[]);
        }
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            // width: gridWidth,
            height: fullh-321,
            read:{"query":"query_BR_SAMPLE_INFO_DETAIL_view_addHicSeq","objects":[ SIB_ID ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SAMPLE_NAME #","funcExce(\'"+pathValue+"openEditSample\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        if (SAMPLE_SEND_NO_MX_tablesGrid) {
        	setGridDataSource("#SAMPLE_SEND_NO_MX_tablesGrid"+pathValue, tablesGridJson);
        } else {
            SAMPLE_SEND_NO_MX_tablesGrid = initKendoGrid("#SAMPLE_SEND_NO_MX_tablesGrid"+pathValue, tablesGridJson);
        }
    }
    var refreshGrid=function(){
    	initSAMPLE_SEND_NO_MX_tablesGrid();
    }
    var getAddSampleArgs = function() {
    	var formparams = formDataInfo();
    	var SAMPLE_TYPE = formparams["SAMPLE_TYPE"] ? formparams["SAMPLE_TYPE"].trim() : "";
    	var SAMPLE_SEND_MODE = formparams["SAMPLE_SEND_MODE"] ? formparams["SAMPLE_SEND_MODE"].trim() : "";
    	
    	// 材料类型
    	var SAMPLE_TYPE_ARRAY = [];
    	if (SAMPLE_TYPE!="") {
    		SAMPLE_TYPE = SAMPLE_TYPE.split("@V&S@");
    		for (var i=0; i<SAMPLE_TYPE.length; i++) {
    			SAMPLE_TYPE2 = SAMPLE_TYPE[i].split("@T&S@");
    			if (SAMPLE_TYPE2.length>0) {
    				SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length ] = SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
	    			if (SAMPLE_TYPE2.length>1) {
	    				SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ] += SAMPLE_TYPE2[1];
	    			}
    				SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ] = { "text":SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ], "value":SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ] };
    			}
    		}
    	}
    	// 寄送方式
    	var SAMPLE_SEND_MODE_ARRAY = [];
    	if (SAMPLE_SEND_MODE!="") {
    		SAMPLE_SEND_MODE = SAMPLE_SEND_MODE.split("@V&S@");
    		for (var i=0; i<SAMPLE_SEND_MODE.length; i++) {
    			SAMPLE_SEND_MODE2 = SAMPLE_SEND_MODE[i].split("@T&S@");
    			if (SAMPLE_SEND_MODE2.length>0 && SAMPLE_SEND_MODE2[0].indexOf("B")==0) {
    				SAMPLE_SEND_MODE_ARRAY[ SAMPLE_SEND_MODE_ARRAY.length ] = SAMPLE_SEND_MODE_CONFIRM_VIEW_TEXT[ SAMPLE_SEND_MODE2[0] ];
	    			if (SAMPLE_SEND_MODE2.length>1) {
	    				SAMPLE_SEND_MODE_ARRAY[ SAMPLE_SEND_MODE_ARRAY.length-1 ] += SAMPLE_SEND_MODE2[1];
	    			}
    				SAMPLE_SEND_MODE_ARRAY[ SAMPLE_SEND_MODE_ARRAY.length-1 ] = { "text":SAMPLE_SEND_MODE_ARRAY[ SAMPLE_SEND_MODE_ARRAY.length-1 ], "value":SAMPLE_SEND_MODE_ARRAY[ SAMPLE_SEND_MODE_ARRAY.length-1 ] };
    			}
    		}
    	}
    	
    	return { "SAMPLE_TYPE":SAMPLE_TYPE_ARRAY, "SAMPLE_SEND_MODE":SAMPLE_SEND_MODE_ARRAY};
    }
    var addSample = function() {
    	if ( !SIB_ID ) {
            alertMsg("请保存送检主单, 再添加待提取样品");
            return ;
    	}
        var winOpts={
            url:"berry/prod/sample/gatherSampleProject/addHicSeq/addSample",
            title:"添加: 待提取样品.."
        };
        //主表单填写参数
        var infoArgs = getAddSampleArgs();
        //样本表最后一行
        var SAMPLE_GRID_LAST_ROW = null;
        if (SAMPLE_SEND_NO_MX_tablesGrid) {
            var mx_data = SAMPLE_SEND_NO_MX_tablesGrid.dataSource.data();
            if (mx_data && mx_data.length>0) {
            	SAMPLE_GRID_LAST_ROW = mx_data[ mx_data.length-1 ];
            }
        }
        openWindow(winOpts,{ "SIB_ID":SIB_ID, "SAMPLE_TYPE_OPTIONS":infoArgs.SAMPLE_TYPE, "SAVE_LIQUID_TYPE_OPTIONS":infoArgs.SAMPLE_SEND_MODE, "SAMPLE_GRID_LAST_ROW":SAMPLE_GRID_LAST_ROW, "VIEW_MODE":VIEW_MODE, "addPathValue":pathValue });
    }
    var editSample = function() {
    	if ( !SIB_ID ) {
            alertMsg("请保存送检主单后, 再添加待提取样品");
            return;
    	}
        var arrIds = getSelectData(SAMPLE_SEND_NO_MX_tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据");
            return;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据");
            return;
        }
        openEditSample( arrIds[0] );
    }
    var openEditSample = function(ID) {
    	if ( !SIB_ID ) {
            alertMsg("请保存送检主单后, 再添加待提取样品");
            return;
    	}
    	var title_mode = VIEW_MODE=="edit" ? "编辑" : "查看";
        var winOpts={
            url:"berry/prod/sample/gatherSampleProject/addHicSeq/addSample",
            title: title_mode+": 待提取样品.."
        };
        var infoArgs = getAddSampleArgs();
        openWindow(winOpts,{ "ID":ID, "SIB_ID":SIB_ID, "SAMPLE_TYPE_OPTIONS":infoArgs.SAMPLE_TYPE, "SAVE_LIQUID_TYPE_OPTIONS":infoArgs.SAMPLE_SEND_MODE, "VIEW_MODE":VIEW_MODE, "addPathValue":pathValue });
    }
    var deleteSample = function() {
        var arrIds = getSelectData(SAMPLE_SEND_NO_MX_tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据");
            return;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据");
            return;
        }
        var url="berry/prod/sample/sample/delSampleMx";
    	$.fn.ajaxPost({
            ajaxUrl: url,
            ajaxData: { "ids":arrIds },
            ajaxType: "post",
            succeed: function(res){
                if(res["code"]>0){
                	refreshGrid();
		        	alertMsg("删除成功","success");
                }else{
                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
                }
            },
            failed: function(res){
                alertMsg("网络请求出现异常!","error");
            }
        });
    }
    
    var formDataInfo = function() {
    	// -------------------------------------------------------- 表单数据处理
    	var formparams = getJsonByForm("form", pathValue);
    	// 单选框取值处理
    	$("#"+card_id+" input[type='radio']").each(function() {
			var key = this.name;
			key = key ? key.replace(pathValue, "") : key;
    		if (key && this.checked) {
    			formparams[key] = $(this).attr("val");
    		}
    	});
    	// 复选框取值处理
    	$("#"+card_id+" input[type='checkbox']").each(function() {
			var key = this.name;
			key = key ? key.replace(pathValue, "") : key;
    		if (key && this.checked) {
    			formparams[key] = $(this).attr("val");
    		}
    	});
    	// 组合值处理
    	var SAMPLE_TYPE = "";// 材料类型
    	var SAMPLE_SEND_MODE = "";// 寄送方式
    	var IMPORTANT_NOTE = "";// 重要提示
    	var SAMPLE_UNQUALIFIED_DISPOSE = "";// 质控不合格材料的处理：
    	var SAMPLE_SURPLUS_DISPOSE = "";// 项目完成后剩余材料的处理：
    	for (var key in formparams) {
    		// 材料类型
    		if ( key.indexOf("SAMPLE_TYPE")>-1 && key.indexOf("_TEXT")==-1 ) {
    			if (SAMPLE_TYPE.length>0) {
    				SAMPLE_TYPE += "@V&S@";
    			}
    			SAMPLE_TYPE += formparams[key];
    			if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    				SAMPLE_TYPE += "@T&S@" + formparams[key+"_TEXT"];
    			}
    		}
    		// 寄送方式
    		if ( key.indexOf("SAMPLE_SEND_MODE")>-1 && key.indexOf("_TEXT")==-1 ) {
    			if (SAMPLE_SEND_MODE.length>0) {
    				SAMPLE_SEND_MODE += "@V&S@";
    			}
    			SAMPLE_SEND_MODE += formparams[key];
    			if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    				SAMPLE_SEND_MODE += "@T&S@" + formparams[key+"_TEXT"];
    			}
    		}
    		//重要提示
    		if ( key.indexOf("IMPORTANT_NOTE")>-1 && key.indexOf("_TEXT")==-1 ) {
    			if (IMPORTANT_NOTE.length>0) {
    				IMPORTANT_NOTE += "@V&S@";
    			}
    			IMPORTANT_NOTE += formparams[key];
    			if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    				IMPORTANT_NOTE += "@T&S@" + formparams[key+"_TEXT"];
    			}
    		}
    		// 质控不合格材料的处理：
    		if ( key.indexOf("SAMPLE_UNQUALIFIED_DISPOSE")>-1 && key.indexOf("_TEXT")==-1 ) {
    			if (SAMPLE_UNQUALIFIED_DISPOSE.length>0) {
    				SAMPLE_UNQUALIFIED_DISPOSE += "@V&S@";
    			}
    			SAMPLE_UNQUALIFIED_DISPOSE += formparams[key];
    			if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    				SAMPLE_UNQUALIFIED_DISPOSE += "@T&S@" + formparams[key+"_TEXT"];
    			}
    		}
    		// 项目完成后剩余材料的处理：
    		if ( key.indexOf("SAMPLE_SURPLUS_DISPOSE")>-1 && key.indexOf("_TEXT")==-1 ) {
    			if (SAMPLE_SURPLUS_DISPOSE.length>0) {
    				SAMPLE_SURPLUS_DISPOSE += "@V&S@";
    			}
    			SAMPLE_SURPLUS_DISPOSE += formparams[key];
    			if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    				SAMPLE_SURPLUS_DISPOSE += "@T&S@" + formparams[key+"_TEXT"];
    			}
    		}
    	}
    	formparams["SAMPLE_TYPE"] = SAMPLE_TYPE;// 材料类型
    	formparams["SAMPLE_SEND_MODE"] = SAMPLE_SEND_MODE;// 寄送方式
    	formparams["IMPORTANT_NOTE"] = IMPORTANT_NOTE; // 重要提示
    	formparams["SAMPLE_UNQUALIFIED_DISPOSE"] = SAMPLE_UNQUALIFIED_DISPOSE;// 质控不合格材料的处理：
    	formparams["SAMPLE_SURPLUS_DISPOSE"] = SAMPLE_SURPLUS_DISPOSE;// 项目完成后剩余材料的处理：
    	return formparams;
    }
    var submit = function() {
    	// -------------------------------------------------------- 表单数据处理
    	var formparams = formDataInfo();
    	// -------------------------------------------------------- 表单填写校验
    	
    	// -------------------------------------------------------- 提交表单数据
    	var url="berry/prod/sample/sample/saveSampleInfoBasic";
    	$.fn.ajaxPost({
            ajaxUrl: url,
            ajaxData: formparams,
            ajaxType: "post",
            succeed: function(res){
                if(res["code"]>0){
                	if ( ! $("#"+card_id+" #ID"+pathValue).val() ) {
    		        	funcExce(pPathValue+"refreshGrid1");
                	}
		        	$("#"+card_id+" #ID"+pathValue).val(res.ID);
		        	SIB_ID=res.ID;
		        	alertMsg("保存送样主单成功","success");
                }else{
                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
                }
            },
            failed: function(res){
                alertMsg("网络请求出现异常!","error");
            }
        });
    }
    // 选择项目编号
    var selectCONTRACT = function() {
    	var options = {
			name:"数据源",
			title:"项目选择",
	        params: { title:"项目选择", "query":"query_BR_CONTRACT_M_select" },
	        //pathValue: pathValue,
	        componentId: "CONTRACT_NO"+pathValue,
	        settings: function(obj, value) {
	        	//合同信息
	        	$("#"+card_id+" #CONTRACT_ID"+pathValue).val(obj.ID);
	        	$("#"+card_id+" #CONTRACT_NO"+pathValue).val(obj.CONTRACT_NO);
	        	$("#"+card_id+" #CONTRACT_NAME"+pathValue).val(obj.CONTRACT_NAME);
	        	//客户单位
	        	$("#"+card_id+" #COMPANY_ID"+pathValue).val(obj.COMPANY_ID);
	        	$("#"+card_id+" #COMPANY_CODE"+pathValue).val(obj.COMPANY_CODE);
	        	$("#"+card_id+" #COMPANY_NAME"+pathValue).val(obj.COMPANY_NAME);
	        	//客户联系人
	        	$("#"+card_id+" #CUSTOMER_ID"+pathValue).val(obj.JF_LINKMAN_ID);
	        	$("#"+card_id+" #CUSTOMER_NAME"+pathValue).val(obj.JF_LINKMAN);//姓名
	        	$("#"+card_id+" #CUSTOMER_EMAIL"+pathValue).val(obj.JF_LINKMAN_EMAIL);//邮箱
	        	// $("#"+card_id+" #CUSTOMER_TEL"+pathValue).val(obj.JF_LINKMAN_TEL);//电话
	        	$("#"+card_id+" #CUSTOMER_TEL"+pathValue).val(obj.JF_LINKMAN_PHONE);//手机
	        	//销售
	        	$("#"+card_id+" #SALE_MAN_ID"+pathValue).val(obj.SIGN_BUS_MAN_ID);
	        	$("#"+card_id+" #SALE_MAN"+pathValue).val(obj.SIGN_BUS_MAN);
	        }
    	};
    	var windowOptions = {};
    	openComponent(options, windowOptions);
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "selectCONTRACT":selectCONTRACT,
        "refreshGrid":refreshGrid,
        "addSample":addSample,
        "editSample":editSample,
        "openEditSample":openEditSample,
        "deleteSample":deleteSample,
    });
 });