$(document).ready(function() {
    var pathValue="berry-prod-sample-gatherSampleProject-addRnaTgsSeq-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
            tableName : "BR_SAMPLE_INFO_BASIC",
            // 设置表单字段: 默认值
            SAMPLE_SOURCE:"MIS",
            SAMPLE_SEND_TYPE:"三代RNA样品建库测序信息单",
        };
    }
    
    var SIB_ID="";
    var pPathValue;
    var VIEW_MODE;
    
    var card_id = "CARD-"+pathValue;
    
    var SAMPLE_SEND_NO_MX_tablesGrid;
    

    //样品类型:材料类型
    var SAMPLE_TYPE_VIEW_TEXT = {
		"A01":"Total RNA",
		"A02":"cDNA",
		"A99":"其他：",
    };
    //样品状态
    var SAMPLE_STATE_VIEW_TEXT = {
    		"A01":"TE buffer",
    		"A02":"ddH2O",
    		"A03":"DEPC水",
    		"A99":"其他：",
        };
    //寄送方式
    var SAMPLE_SEND_MODE_VIEW_TEXT = {
    	"A01":"常温",
    	"A02":"干冰",
    	"A03":"冰袋",
    	"A04":"冷藏箱",
    	"A99":"其他: ",
    };
    //接收确认:寄送方式
    var SAMPLE_SEND_MODE_CONFIRM_VIEW_TEXT = {
    	"A01":"常温",
    	"A02":"干冰:充足/很少/无;样品状态:冷冻状态/已化冻",
    	"A03":"冰袋:未完全融化/已完全融化;样品状态:冷冻状态/已化冻",
    	"A99":"其他: ",
    };
    //初步质控
    var  SAMPLE_CBZK_VIEW_TEXT = {
    		"A01-1":"1-2次",
    		"A01-2":"3-5次",
    		"A01-3":"≥5次",
    		"A02Y":"是",
    		"A02N":"否",
    		"A03Y":"是",
    		"A03N":"否",
    		"A04Y":"是",
    		"A04N":"否",
    		"A05X2M":"两个月以内",
    		"A05C2M":"超过两个月（具体时长）：",   		
        };
  //测序类型
    var SEQ_TYPE_VIEW_TEXT = {
    	"A01":"真核生物Iso-seq",
    	"A02":"cDNA Iso-seq ",
    };
  //测序平台
    var SEQ_PLATFORM_VIEW_TEXT = {
    	"A01":"Pacbio",
    	"A02":"Sequel II",
    };
  //测序数据量
    var SEQ_CELL_NUM_VIEW_TEXT = {
    	"A01":"1个cell",
    	"A02":"个cell",
    	"A99":"其他:",
    	
    };
  //核酸样品处理方法
    var KIT_Y_ART_NO_VIEW_TEXT = {
    	"A01":"试剂盒(名称及货号):",
    	"A02":"CTAB法",
    	"A03":"Trizol法",
    	"A99":"其他:",
    };
    //质控不合格: 处理
    var SAMPLE_UNQUALIFIED_DISPOSE_VIEW_TEXT = {
    	"A01":"由贝瑞处理",
    	"A02":"返还",
    	"A99":"其他：",
    };
    //项目完成后剩余: 处理
    var SAMPLE_SURPLUS_DISPOSE_VIEW_TEXT = {
    	"A01":"依据合同规定",
    	"A02":"特殊需求: ",
    };
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	// 设置送样日期显示样式
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).before("送样日期:");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).css("width","200px");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).parent().css("border","0px");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).parent().css("font-size", "16px");
    	$("#"+card_id+" #SAMPLE_SEND_DATE"+pathValue).parent().css("font-weight", "bold");
    	
    	var nowDate = new Date();
    	var nowDateString = nowDate.getFullYear()+"-"+( nowDate.getMonth()+1 )+"-"+nowDate.getDate();
    	params["SAMPLE_SEND_DATE"] = params["SAMPLE_SEND_DATE"] ? params["SAMPLE_SEND_DATE"] : nowDateString;
    	
    	// 接收参数
    	SIB_ID = params.ID ? params.ID : "";
    	pPathValue = params.pPathValue;
    	VIEW_MODE = params.VIEW_MODE;
    	if (VIEW_MODE == "edit") {
    		$("#"+card_id+" #sampleSendNoSaveSubmit").show();
    	} else {
    		$("#"+card_id+" #sampleSendNoSaveSubmit").hide();
    	}
    	// 初始化表单
        getInfo("form", pathValue, params);
        if (params && params.ID) {
        	// 读取数据库获取表单数据
        	var url="system/jdbc/query/info/"+initData().tableName;
            getInfo("form", pathValue, params, url, function(viewModel, params) {
            	// ------------------------------------------------------------------------ 组合值处理
            	// 样品物种
            	var SPECIES_NAME = params["SPECIES_NAME"] ? params["SPECIES_NAME"] : "";
            	var SPECIES_NAME_A_VIEW="";
            	var SPECIES_NAME_B_VIEW="";
            	if (SPECIES_NAME!="") {
            		SPECIES_NAME = SPECIES_NAME.split("@V&S@");
            		if (SPECIES_NAME.length > 0) {
            			$("#"+card_id+" #SPECIES_NAME_Z"+pathValue).val(SPECIES_NAME[0]);
            		}
            		if (SPECIES_NAME.length > 1) {
            			$("#"+card_id+" #SPECIES_NAME_A_1"+pathValue).val(SPECIES_NAME[1]);
            		}
            		if (SPECIES_NAME.length > 2) {
            			$("#"+card_id+" #SPECIES_NAME_A_2"+pathValue).val(SPECIES_NAME[2]);
            		}
            		if (SPECIES_NAME.length > 3) {
            			$("#"+card_id+" #SPECIES_NAME_B_1"+pathValue).val(SPECIES_NAME[3]);
            		}
            		if (SPECIES_NAME.length > 4) {
            			$("#"+card_id+" #SPECIES_NAME_B_2"+pathValue).val(SPECIES_NAME[4]);
            		}
            		if (SPECIES_NAME.length > 5) {
            			$("#"+card_id+" #SPECIES_NAME_C_1"+pathValue).val(SPECIES_NAME[5]);
            		}
            		if (SPECIES_NAME.length > 6) {
            			$("#"+card_id+" #SPECIES_NAME_C_2"+pathValue).val(SPECIES_NAME[6]);
            		}
            		if (SPECIES_NAME!="") {           			
                		// 初始化页面显示: 只读模式
                		SPECIES_NAME_A_VIEW = "（填到种名，中文名称）：";
                		SPECIES_NAME_A_VIEW += "&nbsp;&nbsp;&nbsp;";
                	}
            		if (SPECIES_NAME!="") {
                		// 初始化页面显示: 只读模式
            			//SPECIES_NAME_B_VIEW += (SPECIES_NAME.length==0)?"<br/>":"";
            			SPECIES_NAME_B_VIEW="物种A:"
                		SPECIES_NAME_B_VIEW +=SPECIES_NAME[1]
            			SPECIES_NAME_B_VIEW +="&nbsp;&nbsp;&nbsp;基因组大小:"
            			SPECIES_NAME_B_VIEW +=SPECIES_NAME[2]
                		SPECIES_NAME_B_VIEW += "&nbsp;&nbsp;&nbsp;";
            			
            			if (SPECIES_NAME_B_VIEW.indexOf("物种B:")<0) {
            				SPECIES_NAME_B_VIEW += "<br>";
            				SPECIES_NAME_B_VIEW += "物种B:";
            				SPECIES_NAME_B_VIEW +=SPECIES_NAME[3]
                			SPECIES_NAME_B_VIEW +="&nbsp;&nbsp;&nbsp;基因组大小:"
                			SPECIES_NAME_B_VIEW +=SPECIES_NAME[4]
                    		SPECIES_NAME_B_VIEW += "&nbsp;&nbsp;&nbsp;";
                		}
            			if (SPECIES_NAME_B_VIEW.indexOf("物种C:")<0) {
            				SPECIES_NAME_B_VIEW += "<br>";
            				SPECIES_NAME_B_VIEW += "物种C:";
            				SPECIES_NAME_B_VIEW +=SPECIES_NAME[5]
                			SPECIES_NAME_B_VIEW +="&nbsp;&nbsp;&nbsp;基因组大小:"
                			SPECIES_NAME_B_VIEW +=SPECIES_NAME[6]
                    		SPECIES_NAME_B_VIEW += "&nbsp;&nbsp;&nbsp;";
                		}
            			
                	}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SPECIES_NAME_A_VIEW").html(SPECIES_NAME_A_VIEW);
    				$("#"+card_id+" #SPECIES_NAME_A_VIEW").show();
    				$("#"+card_id+" #SPECIES_NAME_A_EDIT").hide();
    				if ( !SPECIES_NAME_A_VIEW ) {//至少留一行空行
    					$("#"+card_id+" #SPECIES_NAME_A_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SPECIES_NAME_B_VIEW").html(SPECIES_NAME_B_VIEW);
    				$("#"+card_id+" #SPECIES_NAME_B_VIEW").show();
    				$("#"+card_id+" #SPECIES_NAME_B_EDIT").hide();
    				if ( !SPECIES_NAME_B_VIEW ) {//至少留一行空行
    					$("#"+card_id+" #SPECIES_NAME_B_VIEW").parent().hide();
    				}
        		}
        		
            	// 样品类型
            	var SAMPLE_TYPE = params["SAMPLE_TYPE"] ? params["SAMPLE_TYPE"] : "";
            	var SAMPLE_TYPE_VIEW = "";
            	if (SAMPLE_TYPE!="") {
            		SAMPLE_TYPE = SAMPLE_TYPE.split("@V&S@");
            		for (var i=0; i<SAMPLE_TYPE.length; i++) {
            			SAMPLE_TYPE2 = SAMPLE_TYPE[i].split("@T&S@");
            			if (SAMPLE_TYPE2.length>0) {
            				$("#"+card_id+" #SAMPLE_TYPE_"+SAMPLE_TYPE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_TYPE2.length>1) {
            				$("#"+card_id+" #SAMPLE_TYPE_"+SAMPLE_TYPE2[0]+"_TEXT"+pathValue).val(SAMPLE_TYPE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_TYPE2.length>0) {
            				SAMPLE_TYPE_VIEW += SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
            				SAMPLE_TYPE_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            				SAMPLE_TYPE_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_TYPE_VIEW").html(SAMPLE_TYPE_VIEW);
    				$("#"+card_id+" #SAMPLE_TYPE_VIEW").show();
    				$("#"+card_id+" #SAMPLE_TYPE_EDIT").hide();
        		}
            	// 样品状态
            	var SAMPLE_STATE = params["SAMPLE_STATE"] ? params["SAMPLE_STATE"] : "";
            	var SAMPLE_STATE_VIEW = "";
            	if (SAMPLE_STATE!="") {
            		SAMPLE_STATE = SAMPLE_STATE.split("@V&S@");
            		for (var i=0; i<SAMPLE_STATE.length; i++) {
            			if (SAMPLE_STATE_VIEW.indexOf("溶于:")<0) {
            				SAMPLE_STATE_VIEW += "溶于:  ";
                		}
            			SAMPLE_STATE2 = SAMPLE_STATE[i].split("@T&S@");
            			if (SAMPLE_STATE2.length>0) {
            				$("#"+card_id+" #SAMPLE_STATE_"+SAMPLE_STATE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_STATE2.length>1) {
            				$("#"+card_id+" #SAMPLE_STATE_"+SAMPLE_STATE2[0]+"_TEXT"+pathValue).val(SAMPLE_STATE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_STATE2.length>0) {
            				SAMPLE_STATE_VIEW += SAMPLE_STATE_VIEW_TEXT[ SAMPLE_STATE2[0] ];
            				SAMPLE_STATE_VIEW += SAMPLE_STATE2.length>1 ? SAMPLE_STATE2[1] : "";
            				SAMPLE_STATE_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_STATE_VIEW").html(SAMPLE_STATE_VIEW);
    				$("#"+card_id+" #SAMPLE_STATE_VIEW").show();
    				$("#"+card_id+" #SAMPLE_STATE_EDIT").hide();
        		}
            	// 寄送方式
            	var SAMPLE_SEND_MODE = params["SAMPLE_SEND_MODE"] ? params["SAMPLE_SEND_MODE"] : "";
            	var SAMPLE_SEND_MODE_VIEW = "";
            	if (SAMPLE_SEND_MODE!="") {
            		SAMPLE_SEND_MODE = SAMPLE_SEND_MODE.split("@V&S@");
            		for (var i=0; i<SAMPLE_SEND_MODE.length; i++) {
            			SAMPLE_SEND_MODE2 = SAMPLE_SEND_MODE[i].split("@T&S@");
            			if (SAMPLE_SEND_MODE2.length>0) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_"+SAMPLE_SEND_MODE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_SEND_MODE2.length>1) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_"+SAMPLE_SEND_MODE2[0]+"_TEXT"+pathValue).val(SAMPLE_SEND_MODE2[1]);
            			}
            			if (SAMPLE_SEND_MODE2.length>0) {            				
            				SAMPLE_SEND_MODE_VIEW += SAMPLE_SEND_MODE_VIEW_TEXT[ SAMPLE_SEND_MODE2[0] ];
            				SAMPLE_SEND_MODE_VIEW += SAMPLE_SEND_MODE2.length>1 ? SAMPLE_SEND_MODE2[1] : "";
            				SAMPLE_SEND_MODE_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_SEND_MODE_VIEW").html(SAMPLE_SEND_MODE_VIEW);
    				$("#"+card_id+" #SAMPLE_SEND_MODE_VIEW").show();
    				$("#"+card_id+" #SAMPLE_SEND_MODE_EDIT").hide();
        		}
            	// 确认接收(贝瑞填写)
            	var SAMPLE_SEND_MODE_CONFIRM = params["SAMPLE_SEND_MODE_CONFIRM"] ? params["SAMPLE_SEND_MODE_CONFIRM"] : "";
            	var SAMPLE_SEND_MODE_CONFIRM_VIEW = "";
            	if (SAMPLE_SEND_MODE_CONFIRM!="") {
            		SAMPLE_SEND_MODE_CONFIRM = SAMPLE_SEND_MODE_CONFIRM.split("@V&S@");
            		for (var i=0; i<SAMPLE_SEND_MODE_CONFIRM.length; i++) {
            			SAMPLE_SEND_MODE_CONFIRM2 = SAMPLE_SEND_MODE_CONFIRM[i].split("@T&S@");
            			if (SAMPLE_SEND_MODE_CONFIRM2.length>0) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_"+SAMPLE_SEND_MODE_CONFIRM2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_SEND_MODE_CONFIRM2.length>1) {
            				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_"+SAMPLE_SEND_MODE_CONFIRM2[0]+"_TEXT"+pathValue).val(SAMPLE_SEND_MODE_CONFIRM2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_SEND_MODE_CONFIRM2.length>0) {
            				SAMPLE_SEND_MODE_CONFIRM_VIEW += SAMPLE_SEND_MODE_CONFIRM_VIEW_TEXT[ SAMPLE_SEND_MODE_CONFIRM2[0] ];
            				SAMPLE_SEND_MODE_CONFIRM_VIEW += SAMPLE_SEND_MODE_CONFIRM2.length>1 ? SAMPLE_SEND_MODE_CONFIRM2[1] : "";
            				SAMPLE_SEND_MODE_CONFIRM_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_VIEW").html(SAMPLE_SEND_MODE_CONFIRM_VIEW);
    				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_VIEW").show();
    				$("#"+card_id+" #SAMPLE_SEND_MODE_CONFIRM_EDIT").hide();
        		}
        		//初步质控
            	var SAMPLE_CBZK = params["SAMPLE_CBZK"] ? params["SAMPLE_CBZK"] : "";
            	var SAMPLE_CBZK_A_VIEW = "";
            	var SAMPLE_CBZK_B_VIEW = "";
            	var SAMPLE_CBZK_C_VIEW = "";
            	var SAMPLE_CBZK_D_VIEW = "";
            	var SAMPLE_CBZK_E_VIEW = "";
            	if (SAMPLE_CBZK!="") {
            		SAMPLE_CBZK = SAMPLE_CBZK.split("@V&S@");
            		for (var i=0; i<SAMPLE_CBZK.length; i++) {
            			if (SAMPLE_CBZK_A_VIEW.indexOf(" 1、提取后冻融次数（样品邮寄计为1次）：")<0) {
            				SAMPLE_CBZK_A_VIEW += " 1、提取后冻融次数（样品邮寄计为1次）：  ";
                		}
            		if (SAMPLE_CBZK_B_VIEW.indexOf(" 2、是否经历过>65℃高温环境？ ")<0) {
            			SAMPLE_CBZK_B_VIEW += " 2、是否经历过>65℃高温环境？  ";
                		}
            		if (SAMPLE_CBZK_C_VIEW.indexOf("  3、是否经历过极端pH（<6或>9）环境？")<0) {
        				SAMPLE_CBZK_C_VIEW += "  3、是否经历过极端pH（<6或>9）环境？";
            		}
            		if (SAMPLE_CBZK_D_VIEW.indexOf("  4、是否结合过荧光染料或暴露于紫外照射下？")<0) {
        				SAMPLE_CBZK_D_VIEW += "  4、是否结合过荧光染料或暴露于紫外照射下？ ";
            		}
            		if (SAMPLE_CBZK_E_VIEW.indexOf(" 5、提取后保存时长： ")<0) {
        				SAMPLE_CBZK_E_VIEW += " 5、提取后保存时长：   ";
            		}
            			SAMPLE_CBZK2 = SAMPLE_CBZK[i].split("@T&S@");
            			if (SAMPLE_CBZK2.length>0) {
            				$("#"+card_id+" #SAMPLE_CBZK_"+SAMPLE_CBZK2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_CBZK2.length>1) {
            				$("#"+card_id+" #SAMPLE_CBZK_"+SAMPLE_CBZK2[0]+"_TEXT"+pathValue).val(SAMPLE_CBZK2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_CBZK2.length>0 && SAMPLE_CBZK2[0].indexOf("1")==2) {
            				SAMPLE_CBZK_A_VIEW += SAMPLE_CBZK_VIEW_TEXT[ SAMPLE_CBZK2[0] ];
            				SAMPLE_CBZK_A_VIEW += SAMPLE_CBZK2.length>1 ? SAMPLE_CBZK2[1] : "";
            				SAMPLE_CBZK_A_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			if (SAMPLE_CBZK2.length>0 && SAMPLE_CBZK2[0].indexOf("2")==2) {
            				SAMPLE_CBZK_B_VIEW += SAMPLE_CBZK_VIEW_TEXT[ SAMPLE_CBZK2[0] ];
            				SAMPLE_CBZK_B_VIEW += SAMPLE_CBZK2.length>1 ? SAMPLE_CBZK2[1] : "";
            				SAMPLE_CBZK_B_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			
            			if (SAMPLE_CBZK2.length>0 && SAMPLE_CBZK2[0].indexOf("3")==2) {
            				SAMPLE_CBZK_C_VIEW += SAMPLE_CBZK_VIEW_TEXT[ SAMPLE_CBZK2[0] ];
            				SAMPLE_CBZK_C_VIEW += SAMPLE_CBZK2.length>1 ? SAMPLE_CBZK2[1] : "";
            				SAMPLE_CBZK_C_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			if (SAMPLE_CBZK2.length>0 && SAMPLE_CBZK2[0].indexOf("4")==2) {
            				SAMPLE_CBZK_D_VIEW += SAMPLE_CBZK_VIEW_TEXT[ SAMPLE_CBZK2[0] ];
            				SAMPLE_CBZK_D_VIEW += SAMPLE_CBZK2.length>1 ? SAMPLE_CBZK2[1] : "";
            				SAMPLE_CBZK_D_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            			if (SAMPLE_CBZK2.length>0 && SAMPLE_CBZK2[0].indexOf("5")==2) {
            				SAMPLE_CBZK_E_VIEW += SAMPLE_CBZK_VIEW_TEXT[ SAMPLE_CBZK2[0] ];
            				SAMPLE_CBZK_E_VIEW += SAMPLE_CBZK2.length>1 ? SAMPLE_CBZK2[1] : "";
            				SAMPLE_CBZK_E_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_CBZK_A_VIEW").html(SAMPLE_CBZK_A_VIEW);
    				$("#"+card_id+" #SAMPLE_CBZK_A_VIEW").show();
    				$("#"+card_id+" #SAMPLE_CBZK_A_EDIT").hide();
    				if ( !SAMPLE_CBZK_A_VIEW ) {//至少留一行空行
    					$("#"+card_id+" #SAMPLE_CBZK_A_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SAMPLE_CBZK_B_VIEW").html(SAMPLE_CBZK_B_VIEW);
    				$("#"+card_id+" #SAMPLE_CBZK_B_VIEW").show();
    				$("#"+card_id+" #SAMPLE_CBZK_B_EDIT").hide();
    				if ( !SAMPLE_CBZK_B_VIEW ) {//至少留一行空行
    					$("#"+card_id+" #SAMPLE_CBZK_B_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SAMPLE_CBZK_C_VIEW").html(SAMPLE_CBZK_C_VIEW);
    				$("#"+card_id+" #SAMPLE_CBZK_C_VIEW").show();
    				$("#"+card_id+" #SAMPLE_CBZK_C_EDIT").hide();
    				if ( !SAMPLE_CBZK_C_VIEW ) {//至少留一行空行
    					$("#"+card_id+" #SAMPLE_CBZK_C_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SAMPLE_CBZK_D_VIEW").html(SAMPLE_CBZK_D_VIEW);
    				$("#"+card_id+" #SAMPLE_CBZK_D_VIEW").show();
    				$("#"+card_id+" #SAMPLE_CBZK_D_EDIT").hide();
    				if ( !SAMPLE_CBZK_D_VIEW ) {//至少留一行空行
    					$("#"+card_id+" #SAMPLE_CBZK_D_VIEW").parent().hide();
    				}
    				$("#"+card_id+" #SAMPLE_CBZK_E_VIEW").html(SAMPLE_CBZK_E_VIEW);
    				$("#"+card_id+" #SAMPLE_CBZK_E_VIEW").show();
    				$("#"+card_id+" #SAMPLE_CBZK_E_EDIT").hide();
    				if ( !SAMPLE_CBZK_E_VIEW ) {//至少留一行空行
    					$("#"+card_id+" #SAMPLE_CBZK_E_VIEW").parent().hide();
    				}
        		}

            	//测序类型
            	var SEQ_TYPE = params["SEQ_TYPE"] ? params["SEQ_TYPE"] : "";
            	var SEQ_TYPE_VIEW = "";
            	if (SEQ_TYPE!="") {
            		SEQ_TYPE = SEQ_TYPE.split("@V&S@");
            		for (var i=0; i<SEQ_TYPE.length; i++) {
            			SEQ_TYPE2 = SEQ_TYPE[i].split("@T&S@");
            			if (SEQ_TYPE2.length>0) {
            				$("#"+card_id+" #SEQ_TYPE_"+SEQ_TYPE2[0]+pathValue).prop("checked", true);
            			}
            			if (SEQ_TYPE2.length>1) {
            				$("#"+card_id+" #SEQ_TYPE_"+SEQ_TYPE2[0]+"_TEXT"+pathValue).val(SEQ_TYPE2[1]);
            			}
            			if (SAMPLE_TYPE2.length>0) {
            				SEQ_TYPE_VIEW+= SEQ_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
            				SEQ_TYPE_VIEW += SAMPLE_TYPE2.length>1 ? SAMPLE_TYPE2[1] : "";
            				SEQ_TYPE_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SEQ_TYPE_VIEW").html(SEQ_TYPE_VIEW);
    				$("#"+card_id+" #SEQ_TYPE_VIEW").show();
    				$("#"+card_id+" #SEQ_TYPE_EDIT").hide();
        		}
            	// 测序平台
            	var SEQ_PLATFORM = params["SEQ_PLATFORM"] ? params["SEQ_PLATFORM"] : "";
            	var SEQ_PLATFORM_VIEW = "";
            	if (SEQ_PLATFORM!="") {
            		SEQ_PLATFORM = SEQ_PLATFORM.split("@V&S@");
            		for (var i=0; i<SEQ_PLATFORM.length; i++) {
            			SEQ_PLATFORM2 = SEQ_PLATFORM[i].split("@T&S@");
            			if (SEQ_PLATFORM2.length>0) {
            				$("#"+card_id+" #SEQ_PLATFORM_"+SEQ_PLATFORM2[0]+pathValue).prop("checked", true);
            			}
            			if (SEQ_PLATFORM2.length>1) {
            				$("#"+card_id+" #SEQ_PLATFORM_"+SEQ_PLATFORM2[0]+"_TEXT"+pathValue).val(SEQ_PLATFORM2[1]);
            			}
            			if (SEQ_PLATFORM2.length>0) {
            				SEQ_PLATFORM_VIEW+= SEQ_PLATFORM_VIEW_TEXT[ SEQ_PLATFORM2[0] ];
            				SEQ_PLATFORM_VIEW += SEQ_PLATFORM2.length>1 ? SEQ_PLATFORM2[1] : "";
            				SEQ_PLATFORM_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SEQ_PLATFORM_VIEW").html(SEQ_PLATFORM_VIEW);
    				$("#"+card_id+" #SEQ_PLATFORM_VIEW").show();
    				$("#"+card_id+" #SEQ_PLATFORM_EDIT").hide();
        		}
            	// 测序数据量
            	var SEQ_CELL_NUM = params["SEQ_CELL_NUM"] ? params["SEQ_CELL_NUM"] : "";
             	var SEQ_CELL_NUM_VIEW = "";
            	if (SEQ_CELL_NUM!="") {
            		SEQ_CELL_NUM = SEQ_CELL_NUM.split("@V&S@");
            		for (var i=0; i<SEQ_CELL_NUM.length; i++) {
            			SEQ_CELL_NUM2 = SEQ_CELL_NUM[i].split("@T&S@");
            			if (SEQ_CELL_NUM2.length>0) {
            				$("#"+card_id+" #SEQ_CELL_NUM_"+SEQ_CELL_NUM2[0]+pathValue).prop("checked", true);
            			}
            			if (SEQ_CELL_NUM2.length>1) {
            				$("#"+card_id+" #SEQ_CELL_NUM_"+SEQ_CELL_NUM2[0]+"_TEXT"+pathValue).val(SEQ_CELL_NUM2[1]);
            			}
        			if (SEQ_CELL_NUM2.length>0) {
        				SEQ_CELL_NUM_VIEW+=SEQ_CELL_NUM_VIEW_TEXT[ SEQ_CELL_NUM2[0] ];
        				SEQ_CELL_NUM_VIEW += SEQ_CELL_NUM2.length>1 ? SEQ_CELL_NUM2[1] : "";
        				SEQ_CELL_NUM_VIEW += "&nbsp;&nbsp;&nbsp;";
        			}
        		}
        	}
        	// 只读模式: 显示HTML
    		if (VIEW_MODE != "edit") {
    			$("#"+card_id+" #SEQ_CELL_NUM_VIEW").html(SEQ_CELL_NUM_VIEW);
				$("#"+card_id+" #SEQ_CELL_NUM_VIEW").show();
				$("#"+card_id+" #SEQ_CELL_NUM_EDIT").hide();
    		}
            	// 核酸样品提取方法
            	var KIT_Y_ART_NO = params["KIT_Y_ART_NO"] ? params["KIT_Y_ART_NO"] : "";
            	var KIT_Y_ART_NO_A_VIEW = "";
            	if (KIT_Y_ART_NO!="") {
            		KIT_Y_ART_NO = KIT_Y_ART_NO.split("@V&S@");
            		for (var i=0; i<KIT_Y_ART_NO.length; i++) {
            			KIT_Y_ART_NO2 = KIT_Y_ART_NO[i].split("@T&S@");
            			if (KIT_Y_ART_NO2.length>0) {
            				$("#"+card_id+" #KIT_Y_ART_NO_"+KIT_Y_ART_NO2[0]+pathValue).prop("checked", true);
            			}
            			if (KIT_Y_ART_NO2.length>1) {
            				$("#"+card_id+" #KIT_Y_ART_NO_"+KIT_Y_ART_NO2[0]+"_TEXT"+pathValue).val(KIT_Y_ART_NO2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (KIT_Y_ART_NO2.length>0 && KIT_Y_ART_NO2[0].indexOf("A")==0) {
            				KIT_Y_ART_NO_A_VIEW += KIT_Y_ART_NO_VIEW_TEXT[ KIT_Y_ART_NO2[0]];
            				KIT_Y_ART_NO_A_VIEW += KIT_Y_ART_NO2.length>1 ? KIT_Y_ART_NO2[1] : "";
            				KIT_Y_ART_NO_A_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #KIT_Y_ART_NO_A_VIEW").html(KIT_Y_ART_NO_A_VIEW);
    				$("#"+card_id+" #KIT_Y_ART_NO_A_VIEW").show();
    				$("#"+card_id+" #KIT_Y_ART_NO_A_EDIT").hide();
        		}
            	// 样品详情: 如样品质检不合格，做如下处理
            	var SAMPLE_UNQUALIFIED_DISPOSE = params["SAMPLE_UNQUALIFIED_DISPOSE"] ? params["SAMPLE_UNQUALIFIED_DISPOSE"] : "";
            	var SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW = "";
            	if (SAMPLE_UNQUALIFIED_DISPOSE!="") {
            		SAMPLE_UNQUALIFIED_DISPOSE = SAMPLE_UNQUALIFIED_DISPOSE.split("@V&S@");
            		for (var i=0; i<SAMPLE_UNQUALIFIED_DISPOSE.length; i++) {
            			SAMPLE_UNQUALIFIED_DISPOSE2 = SAMPLE_UNQUALIFIED_DISPOSE[i].split("@T&S@");
            			if (SAMPLE_UNQUALIFIED_DISPOSE2.length>0) {
            				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_"+SAMPLE_UNQUALIFIED_DISPOSE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_UNQUALIFIED_DISPOSE2.length>1) {
            				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_"+SAMPLE_UNQUALIFIED_DISPOSE2[0]+"_TEXT"+pathValue).val(SAMPLE_UNQUALIFIED_DISPOSE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_UNQUALIFIED_DISPOSE2.length>0 && SAMPLE_UNQUALIFIED_DISPOSE2[0].indexOf("A")==0) {
            				SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW += SAMPLE_UNQUALIFIED_DISPOSE_VIEW_TEXT[ SAMPLE_UNQUALIFIED_DISPOSE2[0] ];
            				SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW += SAMPLE_UNQUALIFIED_DISPOSE2.length>1 ? SAMPLE_UNQUALIFIED_DISPOSE2[1] : "";
            				SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW").html(SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW);
    				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_A_VIEW").show();
    				$("#"+card_id+" #SAMPLE_UNQUALIFIED_DISPOSE_A_EDIT").hide();
        		}
            	// 样品详情: 项目完成后的剩余样品做如下处理
            	var SAMPLE_SURPLUS_DISPOSE = params["SAMPLE_SURPLUS_DISPOSE"] ? params["SAMPLE_SURPLUS_DISPOSE"] : "";
            	var SAMPLE_SURPLUS_DISPOSE_A_VIEW = "";
            	if (SAMPLE_SURPLUS_DISPOSE!="") {
            		SAMPLE_SURPLUS_DISPOSE = SAMPLE_SURPLUS_DISPOSE.split("@V&S@");
            		for (var i=0; i<SAMPLE_SURPLUS_DISPOSE.length; i++) {
            			SAMPLE_SURPLUS_DISPOSE2 = SAMPLE_SURPLUS_DISPOSE[i].split("@T&S@");
            			if (SAMPLE_SURPLUS_DISPOSE2.length>0) {
            				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_"+SAMPLE_SURPLUS_DISPOSE2[0]+pathValue).prop("checked", true);
            			}
            			if (SAMPLE_SURPLUS_DISPOSE2.length>1) {
            				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_"+SAMPLE_SURPLUS_DISPOSE2[0]+"_TEXT"+pathValue).val(SAMPLE_SURPLUS_DISPOSE2[1]);
            			}
            			// 初始化页面显示: 只读模式
            			if (SAMPLE_SURPLUS_DISPOSE2.length>0 && SAMPLE_SURPLUS_DISPOSE2[0].indexOf("A")==0) {
            				SAMPLE_SURPLUS_DISPOSE_A_VIEW += SAMPLE_SURPLUS_DISPOSE_VIEW_TEXT[ SAMPLE_SURPLUS_DISPOSE2[0] ];
            				SAMPLE_SURPLUS_DISPOSE_A_VIEW += SAMPLE_SURPLUS_DISPOSE2.length>1 ? SAMPLE_SURPLUS_DISPOSE2[1] : "";
            				SAMPLE_SURPLUS_DISPOSE_A_VIEW += "&nbsp;&nbsp;&nbsp;";
            			}
            		}
            	}
            	// 只读模式: 显示HTML
        		if (VIEW_MODE != "edit") {
        			$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_A_VIEW").html(SAMPLE_SURPLUS_DISPOSE_A_VIEW);
    				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_A_VIEW").show();
    				$("#"+card_id+" #SAMPLE_SURPLUS_DISPOSE_A_EDIT").hide();
        		}
            	
            	// 送样单号: 条码
            	getSAMPLE_SEND_NO_BARCODE();
            });
        } else {
        	//获取送样单号
        	var url="berry/serialNumberManage/sample/sampleSendNo";
        	var params = { "SAMPLE_SEND_TYPE":initData().SAMPLE_SEND_TYPE };
        	$.fn.ajaxPost({
                ajaxUrl:url,
                ajaxData: params,
                ajaxType:"post",
                succeed:function(res){
                    if(res["code"]>0){
			        	$("#"+card_id+" #SAMPLE_SEND_NO"+pathValue).val(res.SAMPLE_SEND_NO);
			        	// 送样单号: 条码
			        	getSAMPLE_SEND_NO_BARCODE();
	                }else{
	                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
	                }
	            },
	            failed:function(res){
	                alertMsg("网络请求出现异常!","error");
	            }
	        });
        }
    	
    	// 初始化样本明细列表
    	initSAMPLE_SEND_NO_MX_tablesGrid();
    }
	// 送样单号: 条码
    var getSAMPLE_SEND_NO_BARCODE = function() {
    	var barcodeVal = $("#"+card_id+" #SAMPLE_SEND_NO"+pathValue).val();
//    	// 后台模式
//    	var srcURL = apiPath + "berry/system/barcode/barcode4j/out?barcode=" + barcodeVal;
//    	$("#"+card_id+" #SAMPLE_SEND_NO_BARCODE").attr("src", srcURL);
    	//前端模式
        $("#"+card_id+" #SAMPLE_SEND_NO_BARCODE").kendoBarcode({
            value: barcodeVal,
            type: "code39",// ean8  code128  code39
            width: 367,
            height: 126
        });
    }
    
    var initSAMPLE_SEND_NO_MX_tablesGrid = function() {
//    	var gridWidth = $("#"+card_id+" SAMPLE_SEND_NO_PAGR_HEADER").width();
//    	gridWidth = fullw * 0.98;
    	
        var toolbar = null;
        if (VIEW_MODE == "edit") {
        	toolbar = getButtonTemplates(pathValue,[
                {name:"add",target:"addSample",title:"添加RNA样品"},
                {name:"edit",target:"editSample",title:"修改RNA样品"},
                {name:"delete",target:"deleteSample",title:"删除RNA样品"},
            ]);
        } else {
        	toolbar = getButtonTemplates(pathValue,[]);
        }
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            // width: gridWidth,
            height: fullh-321,
            read:{"query":"query_BR_SAMPLE_INFO_DETAIL_view_addRnaTgsSeq","objects":[ SIB_ID ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= SAMPLE_NAME #","funcExce(\'"+pathValue+"openEditSample\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        if (SAMPLE_SEND_NO_MX_tablesGrid) {
        	setGridDataSource("#SAMPLE_SEND_NO_MX_tablesGrid"+pathValue, tablesGridJson);
        } else {
            SAMPLE_SEND_NO_MX_tablesGrid = initKendoGrid("#SAMPLE_SEND_NO_MX_tablesGrid"+pathValue, tablesGridJson);
        }
    }
    var refreshGrid=function(){
    	initSAMPLE_SEND_NO_MX_tablesGrid();
    }
    
    var getAddSampleArgs = function() {
        var formparams = formDataInfo();
        var SAMPLE_TYPE = formparams["SAMPLE_TYPE"] ? formparams["SAMPLE_TYPE"].trim() : "";
        var SAMPLE_STATE = formparams["SAMPLE_STATE"] ? formparams["SAMPLE_STATE"].trim() : "";
        var SEQ_TYPE = formparams["SEQ_TYPE"] ? formparams["SEQ_TYPE"].trim() : "";
        var SEQ_PLATFORM = formparams["SEQ_PLATFORM"] ? formparams["SEQ_PLATFORM"].trim() : "";
        // 样本类型
        var SAMPLE_TYPE_ARRAY = [];
        if (SAMPLE_TYPE!="") {
              SAMPLE_TYPE = SAMPLE_TYPE.split("@V&S@");
              for (var i=0; i<SAMPLE_TYPE.length; i++) {
                     SAMPLE_TYPE2 = SAMPLE_TYPE[i].split("@T&S@");
                     if (SAMPLE_TYPE2.length>0) {
                            SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length ] = SAMPLE_TYPE_VIEW_TEXT[ SAMPLE_TYPE2[0] ];
                            if (SAMPLE_TYPE2.length>1) {
                                  SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ] += SAMPLE_TYPE2[1];
                            }
                            SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ] = { "text":SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ], "value":SAMPLE_TYPE_ARRAY[ SAMPLE_TYPE_ARRAY.length-1 ] };
                     }
              }
        }
       
        // 样品状态
        var SAMPLE_STATE_ARRAY = [];
        if (SAMPLE_STATE!="") {
              SAMPLE_STATE = SAMPLE_STATE.split("@V&S@");
              for (var i=0; i<SAMPLE_STATE.length; i++) {
                     SAMPLE_STATE2 = SAMPLE_STATE[i].split("@T&S@");
                     if ( SAMPLE_STATE2.length>0) {
                    	 SAMPLE_STATE_ARRAY[  SAMPLE_STATE_ARRAY.length ] =  SAMPLE_STATE_VIEW_TEXT[ SAMPLE_STATE2[0] ];
                         if (SAMPLE_TYPE2.length>1) {
                        	 SAMPLE_STATE_ARRAY[  SAMPLE_STATE_ARRAY.length-1 ] +=  SAMPLE_STATE2[1];
                         }
                         SAMPLE_STATE_ARRAY[  SAMPLE_STATE_ARRAY.length-1 ] = { "text": SAMPLE_STATE_ARRAY[ SAMPLE_STATE_ARRAY.length-1 ], "value": SAMPLE_STATE_ARRAY[  SAMPLE_STATE_ARRAY.length-1 ] };
                  }
           }
     }
        // 测序类型
        var SEQ_TYPE_ARRAY = [];
        if (SEQ_TYPE!="") {
              SEQ_TYPE = SEQ_TYPE.split("@V&S@");
              for (var i=0; i<SEQ_TYPE.length; i++) {
                     SEQ_TYPE2 = SEQ_TYPE[i].split("@T&S@");
                     if (SEQ_TYPE2.length>0) {
                    	  SEQ_TYPE_ARRAY[ SEQ_TYPE_ARRAY.length ] = SEQ_TYPE_VIEW_TEXT[  SEQ_TYPE2[0] ];
                         if ( SEQ_TYPE2.length>1) {
                        	  SEQ_TYPE_ARRAY[ SEQ_TYPE_ARRAY.length-1 ] += SEQ_TYPE2[1];
                         }
                         SEQ_TYPE_ARRAY[ SEQ_TYPE_ARRAY.length-1 ] = { "text": SEQ_TYPE_ARRAY[   SEQ_TYPE_ARRAY.length-1 ], "value":SEQ_TYPE_ARRAY[SEQ_TYPE_ARRAY.length-1 ] };
                  }
           }
     }
        // 测序平台
        var SEQ_PLATFORM_ARRAY = [];
        if (SEQ_PLATFORM!="") {
              SEQ_PLATFORM = SEQ_PLATFORM.split("@V&S@");
              for (var i=0; i<SEQ_PLATFORM.length; i++) {
                SEQ_PLATFORM2 = SEQ_PLATFORM[i].split("@T&S@");
                if (SEQ_PLATFORM2.length>0) {
              	  SEQ_PLATFORM_ARRAY[ SEQ_PLATFORM_ARRAY.length ] = SEQ_PLATFORM_VIEW_TEXT[  SEQ_PLATFORM2[0] ];
                   if ( SEQ_PLATFORM2.length>1) {
                  	  SEQ_PLATFORM_ARRAY[ SEQ_PLATFORM_ARRAY.length-1 ] += SEQ_PLATFORM2[1];
                   }
                   SEQ_PLATFORM_ARRAY[ SEQ_PLATFORM_ARRAY.length-1 ] = { "text": SEQ_PLATFORM_ARRAY[   SEQ_PLATFORM_ARRAY.length-1 ], "value":SEQ_PLATFORM_ARRAY[SEQ_PLATFORM_ARRAY.length-1 ] };
            }
       }
    }
        
        return { "SAMPLE_TYPE":SAMPLE_TYPE_ARRAY, "SAMPLE_STATE":SAMPLE_STATE_ARRAY,"SEQ_TYPE":SEQ_TYPE_ARRAY,"SEQ_PLATFORM":SEQ_PLATFORM_ARRAY };
     }
    
    var addSample = function() {
    	if ( !SIB_ID ) {
            alertMsg("请保存送检主单, 再添加RNA样品");
            return ;
    	}
        var winOpts={
            url:"berry/prod/sample/gatherSampleProject/addRnaTgsSeq/addSample",
            title:"添加: RNA样品.."
        };
      //主表单填写参数
        var infoArgs = getAddSampleArgs();
         //样本表最后一行
         var SAMPLE_GRID_LAST_ROW = null;
         if (SAMPLE_SEND_NO_MX_tablesGrid) {
             var mx_data = SAMPLE_SEND_NO_MX_tablesGrid.dataSource.data();
             if (mx_data && mx_data.length>0) {
             	SAMPLE_GRID_LAST_ROW = mx_data[ mx_data.length-1 ];
             }
         }
         openWindow(winOpts,{ "SIB_ID":SIB_ID, "SAMPLE_TYPE_OPTIONS":infoArgs.SAMPLE_TYPE,"SEQ_TYPE_OPTIONS":infoArgs.SEQ_TYPE, "SAMPLE_STATE_OPTIONS":infoArgs.SAMPLE_STATE,"SEQ_PLATFORM_OPTIONS":infoArgs.SEQ_PLATFORM,"SAMPLE_GRID_LAST_ROW":SAMPLE_GRID_LAST_ROW, "VIEW_MODE":VIEW_MODE, "addPathValue":pathValue });
       
     }
    var editSample = function() {
    	if ( !SIB_ID ) {
            alertMsg("请保存送检主单后, 再添加RNA样品");
            return;
    	}
        var arrIds = getSelectData(SAMPLE_SEND_NO_MX_tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据");
            return;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据");
            return;
        }
        openEditSample( arrIds[0] );
    }
    var openEditSample = function(ID) {
    	if ( !SIB_ID ) {
            alertMsg("请保存送检主单后, 再添加RNA样品");
            return;
    	}
    	var title_mode = VIEW_MODE=="edit" ? "编辑" : "查看";
        var winOpts={
            url:"berry/prod/sample/gatherSampleProject/addRnaTgsSeq/addSample",
            title: title_mode+": RNA样品.."
        };
        var infoArgs = getAddSampleArgs();
        openWindow(winOpts,{ "ID":ID, "SIB_ID":SIB_ID, "SAMPLE_TYPE_OPTIONS":infoArgs.SAMPLE_TYPE,"SEQ_TYPE_OPTIONS":infoArgs.SEQ_TYPE, "SAMPLE_STATE_OPTIONS":infoArgs.SAMPLE_STATE,"SEQ_PLATFORM_OPTIONS":infoArgs.SEQ_PLATFORM, "VIEW_MODE":VIEW_MODE, "addPathValue":pathValue });    
    }
    var deleteSample = function() {
        var arrIds = getSelectData(SAMPLE_SEND_NO_MX_tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据");
            return;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据");
            return;
        }
        var url="berry/prod/sample/sample/delSampleMx";
    	$.fn.ajaxPost({
            ajaxUrl: url,
            ajaxData: { "ids":arrIds },
            ajaxType: "post",
            succeed: function(res){
                if(res["code"]>0){
                	refreshGrid();
		        	alertMsg("删除成功","success");
                }else{
                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
                }
            },
            failed: function(res){
                alertMsg("网络请求出现异常!","error");
            }
        });
    }
    
    var formDataInfo = function() {
    	
    	var formparams = getJsonByForm("form", pathValue);
    	       // 单选框取值处理
    	       $("#"+card_id+" input[type='radio']").each(function() {
    	                    var key = this.name;
    	                    key = key ? key.replace(pathValue, "") : key;
    	             if (key && this.checked) {
    	                    formparams[key] = $(this).attr("val");
    	             }
    	       });
    	       // 复选框取值处理
    	       $("#"+card_id+" input[type='checkbox']").each(function() {
    	                    var key = this.name;
    	                    key = key ? key.replace(pathValue, "") : key;
    	             if (key && this.checked) {
    	                    formparams[key] = $(this).attr("val");
    	             }
    	       });
    	       // 组合值处理
    	       var SAMPLE_TYPE = "";// 样品类型
    	       var SAMPLE_STATE = "";// 样品状态
    	       var SAMPLE_SEND_MODE = "";// 寄送方式
    	       var SAMPLE_CBZK = "";//初步质控
    	       var SEQ_TYPE = "";//测序类型
    	       var SEQ_PLATFORM = "";// 测序平台
    	       var SEQ_CELL_NUM = "";// 测序数据量
    	       var KIT_Y_ART_NO = "";// 核酸样品提取方法
    	       var SAMPLE_UNQUALIFIED_DISPOSE = "";// 样品详情: 如样品质检不合格，做如下处理
    	       var SAMPLE_SURPLUS_DISPOSE = "";// 样品详情: 项目完成后的剩余样品做如下处理
    	       for (var key in formparams) {
    	             // 样品类型
    	             if ( key.indexOf("SAMPLE_TYPE")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SAMPLE_TYPE.length>0) {
    	                           SAMPLE_TYPE += "@V&S@";
    	                    }
    	                    SAMPLE_TYPE += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SAMPLE_TYPE += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             // 样品状态
    	             if ( key.indexOf("SAMPLE_STATE")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SAMPLE_STATE.length>0) {
    	                           SAMPLE_STATE += "@V&S@";
    	                    }
    	                    SAMPLE_STATE += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SAMPLE_STATE += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             // 寄送方式
    	             if ( key.indexOf("SAMPLE_SEND_MODE")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SAMPLE_SEND_MODE.length>0) {
    	                           SAMPLE_SEND_MODE += "@V&S@";
    	                    }
    	                    SAMPLE_SEND_MODE += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SAMPLE_SEND_MODE += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             //初步质控
    	             if ( key.indexOf("SAMPLE_CBZK")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SAMPLE_CBZK.length>0) {
    	                           SAMPLE_CBZK += "@V&S@";
    	                    }
    	                    SAMPLE_CBZK += formparams[key];
    	                    if ( formparams["SAMPLE_CBZK_"+formparams[key]+"_TEXT"] ) {//选框后输入框的值
    	                           SAMPLE_CBZK += "@T&S@" + formparams["SAMPLE_CBZK_"+formparams[key]+"_TEXT"];
    	                    }
    	             }
    	             //测序类型
    	             if ( key.indexOf("SEQ_TYPE")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SEQ_TYPE.length>0) {
    	                           SEQ_TYPE += "@V&S@";
    	                    }
    	                    SEQ_TYPE += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SEQ_TYPE += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             // 测序平台
    	             if ( key.indexOf("SEQ_PLATFORM")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SEQ_PLATFORM.length>0) {
    	                           SEQ_PLATFORM += "@V&S@";
    	                    }
    	                    SEQ_PLATFORM += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SEQ_PLATFORM += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             // 测序数据量
    	             if ( key.indexOf("SEQ_CELL_NUM")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SEQ_CELL_NUM.length>0) {
    	                           SEQ_CELL_NUM += "@V&S@";
    	                    }
    	                    SEQ_CELL_NUM += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SEQ_CELL_NUM += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             // 核酸样品提取方法
    	             if ( key.indexOf("KIT_Y_ART_NO")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (KIT_Y_ART_NO.length>0) {
    	                           KIT_Y_ART_NO += "@V&S@";
    	                    }
    	                    KIT_Y_ART_NO += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           KIT_Y_ART_NO += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             // 样品详情: 如样品质检不合格，做如下处理
    	             if ( key.indexOf("SAMPLE_UNQUALIFIED_DISPOSE")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SAMPLE_UNQUALIFIED_DISPOSE.length>0) {
    	                           SAMPLE_UNQUALIFIED_DISPOSE += "@V&S@";
    	                    }
    	                    SAMPLE_UNQUALIFIED_DISPOSE += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SAMPLE_UNQUALIFIED_DISPOSE += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	             // 样品详情: 项目完成后的剩余样品做如下处理
    	             if ( key.indexOf("SAMPLE_SURPLUS_DISPOSE")>-1 && key.indexOf("_TEXT")==-1 ) {
    	                    if (SAMPLE_SURPLUS_DISPOSE.length>0) {
    	                           SAMPLE_SURPLUS_DISPOSE += "@V&S@";
    	                    }
    	                    SAMPLE_SURPLUS_DISPOSE += formparams[key];
    	                    if ( formparams[key+"_TEXT"] ) {//选框后输入框的值
    	                           SAMPLE_SURPLUS_DISPOSE += "@T&S@" + formparams[key+"_TEXT"];
    	                    }
    	             }
    	       }
    	       formparams["SPECIES_NAME"] = formparams["SPECIES_NAME_Z"]// 样品物种
    	                                                     +"@V&S@"+formparams["SPECIES_NAME_A_1"]
    	                                                           +"@V&S@"+formparams["SPECIES_NAME_A_2"]
    	                                                           +"@V&S@"+formparams["SPECIES_NAME_B_1"]
    	                                                           +"@V&S@"+formparams["SPECIES_NAME_B_2"]
    	                                                           +"@V&S@"+formparams["SPECIES_NAME_C_1"]
    	                                                           +"@V&S@"+formparams["SPECIES_NAME_C_2"];
    	       formparams["SAMPLE_TYPE"] = SAMPLE_TYPE;// 样品类型
    	       formparams["SAMPLE_STATE"] = SAMPLE_STATE;// 样品状态
    	       formparams["SAMPLE_SEND_MODE"] = SAMPLE_SEND_MODE;// 寄送方式
    	       formparams["SAMPLE_CBZK"] = SAMPLE_CBZK;// 初步质控
    	       formparams["SEQ_TYPE"] = SEQ_TYPE;// 测序类型
    	       formparams["SEQ_PLATFORM"] = SEQ_PLATFORM;// 测序平台
    	       formparams["SEQ_CELL_NUM"] = SEQ_CELL_NUM;// 测序数据量
    	       formparams["KIT_Y_ART_NO"] = KIT_Y_ART_NO;// 核酸样品提取方法
    	       formparams["SAMPLE_UNQUALIFIED_DISPOSE"] = SAMPLE_UNQUALIFIED_DISPOSE;// 样品详情: 如样品质检不合格，做如下处理
    	       formparams["SAMPLE_SURPLUS_DISPOSE"] = SAMPLE_SURPLUS_DISPOSE;// 样品详情: 项目完成后的剩余样品做如下处理
    	       
    	  return formparams;
    	  
    	}


    var submit = function() {
    	// -------------------------------------------------------- 表单数据处理
    	
    	var formparams = formDataInfo();
    	// -------------------------------------------------------- 表单填写校验
    	
    	// -------------------------------------------------------- 提交表单数据
    	var url="berry/prod/sample/sample/saveSampleInfoBasic";
    	$.fn.ajaxPost({
            ajaxUrl: url,
            ajaxData: formparams,
            ajaxType: "post",
            succeed: function(res){
                if(res["code"]>0){
                	if ( ! $("#"+card_id+" #ID"+pathValue).val() ) {
    		        	funcExce(pPathValue+"refreshGrid1");
                	}
		        	$("#"+card_id+" #ID"+pathValue).val(res.ID);
		        	SIB_ID=res.ID;
		        	alertMsg("保存送样主单成功","success");
                }else{
                    alertMsg("请求已完成,但无法解析,原因“"+res["message"]+"”!","error");
                }
            },
            failed: function(res){
                alertMsg("网络请求出现异常!","error");
            }
        });
    }
    // 选择项目编号
    var selectCONTRACT = function() {
    	var options = {
			name:"数据源",
			title:"项目选择",
	        params: { title:"项目选择", "query":"query_BR_CONTRACT_M_select" },
	        //pathValue: pathValue,
	        componentId: "CONTRACT_NO"+pathValue,
	        settings: function(obj, value) {
	        	//合同信息
	        	$("#"+card_id+" #CONTRACT_ID"+pathValue).val(obj.ID);
	        	$("#"+card_id+" #CONTRACT_NO"+pathValue).val(obj.CONTRACT_NO);
	        	$("#"+card_id+" #CONTRACT_NAME"+pathValue).val(obj.CONTRACT_NAME);
	        	//客户单位
	        	$("#"+card_id+" #COMPANY_ID"+pathValue).val(obj.COMPANY_ID);
	        	$("#"+card_id+" #COMPANY_CODE"+pathValue).val(obj.COMPANY_CODE);
	        	$("#"+card_id+" #COMPANY_NAME"+pathValue).val(obj.COMPANY_NAME);
	        	//客户联系人
	        	$("#"+card_id+" #CUSTOMER_ID"+pathValue).val(obj.JF_LINKMAN_ID);
	        	$("#"+card_id+" #CUSTOMER_NAME"+pathValue).val(obj.JF_LINKMAN);//姓名
	        	$("#"+card_id+" #CUSTOMER_EMAIL"+pathValue).val(obj.JF_LINKMAN_EMAIL);//邮箱
	        	// $("#"+card_id+" #CUSTOMER_TEL"+pathValue).val(obj.JF_LINKMAN_TEL);//电话
	        	$("#"+card_id+" #CUSTOMER_TEL"+pathValue).val(obj.JF_LINKMAN_PHONE);//手机
	        	//销售
	        	$("#"+card_id+" #SALE_MAN_ID"+pathValue).val(obj.SIGN_BUS_MAN_ID);
	        	$("#"+card_id+" #SALE_MAN"+pathValue).val(obj.SIGN_BUS_MAN);
	        }
    	};
    	var windowOptions = {};
    	openComponent(options, windowOptions);
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "selectCONTRACT":selectCONTRACT,
        "refreshGrid":refreshGrid,
        "addSample":addSample,
        "editSample":editSample,
        "openEditSample":openEditSample,
        "deleteSample":deleteSample,
    });
 });