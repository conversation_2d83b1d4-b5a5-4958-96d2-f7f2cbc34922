
<style>
.listViewTemplateberry_prod_sample_container_index {
    float: left;
    position: relative;
    width: 46px;
    height: 46px;
    line-height:46px;
    padding: 0;
	cursor: pointer;
}
.listViewTemplateberry_prod_sample_container_index_img0 {
	background:url(/berry/prod/sample/container/cell_0_38.jpg) no-repeat 3px 3px;
}
.listViewTemplateberry_prod_sample_container_index_img1 {
	background:url(/berry/prod/sample/container/cell_1_38.jpg) no-repeat 3px 3px;
}
</style>

<div id="indexWindow" class="indexWindow">
    <div class="indexSection k-content">
	    <div class="form-group row col-sm-12">
	        <div class="col-sm-3">
	        	容器管理
	        	&nbsp;&nbsp;&nbsp;
	        	<button class="k-button" icon="add" onclick="∑addOpen|this);return;">添加同级</button>
	        	<button class="k-button" onclick="∑addOpen1|this);return;">添加子级</button>
	        	<button class="k-button" onclick="∑deleteInfo|this);return;">删除</button>
	        	<div id="treeviewberry-prod-sample-container-index"></div>
	        </div>
	        <div class="col-sm-9">
	        	<div id="tabstrip">
		        	<ul>
			            <li class="k-state-active">
							容器信息
			            </li>
			            <li>
							储板图形
			            </li>
			            <li>
							样本列表
			            </li>
			        </ul>
			        <div>
		        		<div id="tablesGrid"></div>
		        	</div>
		        	<div class="indexSection k-content">
			        	<div class="form-group row col-sm-12">
			        		<script type="text/x-kendo-tmpl" id="listViewTemplateberry-prod-sample-container-index">
    					    	<div class="listViewTemplateberry_prod_sample_container_index listViewTemplateberry_prod_sample_container_index_${IMG}" style="border:1px dashed red;" onclick="setS1TO9InputValuEberry_prod_sample_container_index('${ORIFICE_PLATE_CODE}','${ORIFICE_CODE}','${SAMPLE_CODE}','${SAMPLE_NAME}','${SAMPLE_TYPE}','${SAMPLE_SOURCE}','${SAMPLE_NUMBER}','${SAMPLE_CONCENTRATION}','${SAMPLE_VOLUME}');return;">
           							${ORIFICE_CODE}
   						    	</div>
							</script>
			        		<div class="col-sm-10" align="center">
				       			<fieldset style="border: 0px;">
							    	<legend style="color: #63B9BE;">储板图形</legend>
				        			<div id="listViewIDberry-prod-sample-container-index"></div>
				        			<br/>
				        			<div id="listViewberry-prod-sample-container-index" style="border:0px dashed red;"></div>
			        			</fieldset>
			        		</div>
			        		<div class="col-sm-2">
				       			<fieldset style="border: 0px;">
							    	<legend style="color: #63B9BE;">孔位信息</legend>
					        		<p>
					        			<label>板号:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S1"/>
					        		</p>
					        		<p>
					        			<label>孔号:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S2"/>
					        		</p>
					        		<p>
					        			<label>样本编号:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S3"/>
					        		</p>
					        		<p>
					        			<label>样本名称:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S4"/>
					        		</p>
					        		<p>
					        			<label>样本类型:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S5"/>
					        		</p>
					        		<p>
					        			<label>样本来源:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S6"/>
					        		</p>
					        		<p>
					        			<label>样本数量:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S7"/>
					        		</p>
					        		<p>
					        			<label>浓度:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S8"/>
					        		</p>
					        		<p>
					        			<label>体积:</label>
						        		<br/>
						        		<input class="k-textbox" style="width: 150px" type="text" id="S9"/>
					        		</p>
				        		</fieldset>
			        		</div>
		        		</div>
		        	</div>
			        <div>
		        		<div id="tablesGrid2berry-prod-sample-container-index"></div>
		        	</div>
	        	</div>
	        </div>
	    </div>
    </div>
</div>
<div id="window" ></div>
<template id="windowTemplate"></template>