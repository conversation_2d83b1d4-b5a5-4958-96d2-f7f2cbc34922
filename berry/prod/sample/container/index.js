
var berry_prod_sample_container_index_id = "";
var berry_prod_sample_container_index_code = "";
var berry_prod_sample_container_index_name = "";
var berry_prod_sample_container_index_nodeType = "";
var berry_prod_sample_container_index_pCode = "";
var berry_prod_sample_container_index_gg = "";
var berry_prod_sample_container_index_pathValue = "berry-prod-sample-container-index";

$(document).ready(function() {
   var pathValue = berry_prod_sample_container_index_pathValue;
   
   intiBrContainerIndexberry_prod_sample_container_index();
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[ ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-102,
            read:{"query":"query_BR_CONTAINER_view","objects":['']},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= NODE_TYPE #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        
        
        var toolbar2=getButtonTemplates(pathValue,[ ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-102,
            read:{"query":"query_BR_CONTAINER_ORIFICE_PLATE_view","objects":[]},
            headerFilter:function(cols,i){
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2"+pathValue,tablesGridJson2);//初始化表格的方法
   }

    var addOpen=function(){
    	if (!berry_prod_sample_container_index_id) {
    		alertMsg("请先选择树型结构节点，再进行添加同级操作");
    		return;
    	}
    	if (berry_prod_sample_container_index_nodeType!="1") {
    		alertMsg("请选择容器节点，再添加同级容器");
    		return;
    	}
        openWindow({
            url:"berry/prod/sample/container/add/add",
            title:"新增容器.."
        },{
        	"P_CODE": berry_prod_sample_container_index_pCode
        });
    }
    var addOpen1=function(){
    	if (!berry_prod_sample_container_index_id) {
    		alertMsg("请先选择树型结构节点，再进行添加子级操作");
    		return;
    	}
    	if (berry_prod_sample_container_index_nodeType!="1") {
    		alertMsg("请选择容器节点，再添加下级容器");
    		return;
    	}
        openWindow({
            url:"berry/prod/sample/container/add/add",
            title:"新增容器.."
        },{
        	"P_CODE": berry_prod_sample_container_index_code
        });
    }

    var open=function(id, rType){
        openWindow({
            url:"berry/prod/sample/container/add/add",
            title:"编辑容器.."
        },{"ID":id});
    }

     var submit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
    	 // 回调刷新树型
    	 intiBrContainerIndexSetDataberry_prod_sample_container_index();
    	 refreshGrid();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            //tablesGrid.refresh();//表格刷新-回调时- 当前内容刷新

            tablesGrid.dataSource.read();//重新读取--刷新

            /**
             * 重置表格-包括了表头
             */
            // var tableId="#tablesGrid"+pathValue;
            // if ($(tableId).data("kendoGrid") != undefined) {
            //     //请求参数
            //     var tablesGridJson={
            //         url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            //         sort: "",//排序
            //         toolbar: toolbar,
            //         read:{"query":"query_sys_metadata_main","objects":[]},
            //     };
            //     setGridDataSource(tableId,tablesGridJson);
            // }

        }
     }

     var deleteInfo=function(){
        if( ! berry_prod_sample_container_index_id ){
            alertMsg("请选择要删除节点!");
            return ;
        }
        alertMsg("确定删除选中的“"+berry_prod_sample_container_index_name+"”节点及其所有子节点吗？", "success", function() {
        	var url="berry/prod/sample/container/delnodes";
        	$.fn.ajaxPost({
    		   ajaxUrl: url,
    		   ajaxType: "post",
    		   ajaxData: { id: berry_prod_sample_container_index_id, code: berry_prod_sample_container_index_code },
    		   succeed: function(rs) {
    			   berry_prod_sample_container_index_id="";
    			   intiBrContainerIndexSetDataberry_prod_sample_container_index();
    			   intiBrContainerIndexGridberry_prod_sample_container_index();
    		   }
    	   });
        });
     }
     
     var importExcel=function(){
    	 alertMsg(123);
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "addOpen1":addOpen1,//打开添加表单
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});

function intiBrContainerIndexberry_prod_sample_container_index() {
	var pathValuE = berry_prod_sample_container_index_pathValue;
   $("#treeview"+pathValuE).kendoTreeView({
	   dataTextField:"NAME",
	   select: function(e) {
		   
		   this.expand(e.node);
		   
	     var dataItem = this.dataItem(e.node);
	     berry_prod_sample_container_index_id = dataItem.ID;
	     berry_prod_sample_container_index_code = dataItem.CODE;
	     berry_prod_sample_container_index_name = dataItem.NAME;
	     berry_prod_sample_container_index_nodeType = dataItem.NODE_TYPE;
	     berry_prod_sample_container_index_pCode = dataItem.P_CODE ? dataItem.P_CODE : "";
	     berry_prod_sample_container_index_gg = dataItem.ORIFICE_PLATE_SIZE ? dataItem.ORIFICE_PLATE_SIZE : "";
	     
	     intiBrContainerIndexGridberry_prod_sample_container_index();
	     intiBrContainerIndexListViewberry_prod_sample_container_index();
	   }
   });

   intiBrContainerIndexSetDataberry_prod_sample_container_index();
}

function intiBrContainerIndexGridberry_prod_sample_container_index() {
    var tableId="#tablesGrid"+berry_prod_sample_container_index_pathValue;
        if ($(tableId).data("kendoGrid") != undefined) {
           //请求参数
           var tablesGridJson={
               url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
               sort: "",//排序
               toolbar: toolbar,
               read:{"query":"query_BR_CONTAINER_view","objects":[ berry_prod_sample_container_index_id ]},
			headerFilter:function(cols,i){
			    if(i){
			        if(cols[i]["field"]&&cols[i]["field"]=="NAME"){
			            setJsonParam(cols[i],"template",getTemplate("#= NAME #","funcExce(\'"+berry_prod_sample_container_index_pathValue+"open\',\'#= ID #\');","txt"));
			        }
			    }
			}
           };
           setGridDataSource(tableId,tablesGridJson);
        }
}

function intiBrContainerIndexSetDataberry_prod_sample_container_index() {
   var pathValuE = berry_prod_sample_container_index_pathValue;
   $.fn.ajaxPost({
	   ajaxUrl: "berry/prod/sample/container/treeview",
	   ajaxType: "post",
	   ajaxData: {},
	   succeed: function(rs) {
		   var treeDataSource = rs.treeview;
		   var treeview = $("#treeview"+pathValuE).data("kendoTreeView");
		   treeview.setDataSource(new kendo.data.HierarchicalDataSource({
			   data: treeDataSource
		   }));
		   if (berry_prod_sample_container_index_id) {
			   treeview.expandTo(berry_prod_sample_container_index_id);
		   }
	   }
   });
}

function intiBrContainerIndexListViewberry_prod_sample_container_index() {
	var pathValuE = berry_prod_sample_container_index_pathValue;
	// 选中的树型不是孔板
	$("#listViewID"+pathValuE).html("");
	$("#listView"+pathValuE).html("");
	setS1TO9InputValuEberry_prod_sample_container_index("","","","","","","","","");
	
	if (berry_prod_sample_container_index_nodeType!=2 || !berry_prod_sample_container_index_gg) {
		return;
	}
	
	var ss = berry_prod_sample_container_index_gg.split("*");
	var x = Number(ss[0]);
	var y = Number(ss[1]);
	$.fn.ajaxPost({
		   ajaxUrl: "system/jdbc/query/one/table",
		   ajaxType: "post",
		   ajaxData: {"query":"query_BR_CONTAINER_ORIFICE_PLATE_listView","objects":[berry_prod_sample_container_index_code]},
		   succeed: function(rs) {
				$("#listViewID"+pathValuE).html(berry_prod_sample_container_index_code);
	            var dataSource = new kendo.data.DataSource({
	                data: rs.rows?rs.rows:[],
	                pageSize: rs.curr
	            });
	            
	            var w = 46 * x + 3 * (x - 1);
	            $("#listView"+pathValuE).css('width', w );
	            
	            $("#listView"+pathValuE).kendoListView({
	                dataSource: dataSource,
	                template: kendo.template($("#listViewTemplate"+pathValuE).html())
	            });
		   }
	   });
}
function setS1TO9InputValuEberry_prod_sample_container_index(s1,s2,s3,s4,s5,s6,s7,s8,s9) {
	var pathValuE = berry_prod_sample_container_index_pathValue;
	$("#S1"+pathValuE).val(s1 && s1!="null"?s1:"");
	$("#S2"+pathValuE).val(s2 && s2!="null"?s2:"");
	$("#S3"+pathValuE).val(s3 && s3!="null"?s3:"");
	$("#S4"+pathValuE).val(s4 && s4!="null"?s4:"");
	$("#S5"+pathValuE).val(s5 && s5!="null"?s5:"");
	$("#S6"+pathValuE).val(s6 && s6!="null"?s6:"");
	$("#S7"+pathValuE).val(s7 && s7!="null"?s7:"");
	$("#S8"+pathValuE).val(s8 && s8!="null"?s8:"");
	$("#S9"+pathValuE).val(s9 && s9!="null"?s9:"");
}