$(document).ready(function() {
    var pathValue="berry-prod-sop-materialList-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
        	tableName:"BR_MATERIAL_LIST",
        };
    }
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form", pathValue, params, url);//传入id
    }
    
    /** 保存数据 */
    var submit=function(){
    	var ID = $("#form"+pathValue+" #ID"+pathValue).val();
    	formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
            	if(result["code"]>0 && ID){
                	alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else if(result["code"]>0){
                	submitKC();
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    /** 保存物料库存数据 */
    var submitKC = function(){
    	var MATERIAL_CODE = $("#form"+pathValue+" #MATERIAL_CODE"+pathValue).val();
    	var MATERIAL_NAME = $("#form"+pathValue+" #MATERIAL_NAME"+pathValue).val();
    	$("#form2"+pathValue+" #MATERIAL_CODE"+pathValue).val(MATERIAL_CODE);
    	$("#form2"+pathValue+" #MATERIAL_NAME"+pathValue).val(MATERIAL_NAME);
    	$("#form2"+pathValue+" #tableName"+pathValue).val("BR_MATERIAL_KC");
    	formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form2",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
});
 