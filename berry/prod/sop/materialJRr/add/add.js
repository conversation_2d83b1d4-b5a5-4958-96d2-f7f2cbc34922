$(document).ready(function() {
    var pathValue="berry-prod-sop-materialJRr-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
        	tableName:"BR_MATERIAL_JR",
        };
    }
    
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form", pathValue, params, url);//传入id
    }
    
    /** 保存数据 */
    var submit=function(){
    	formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
            	if(result["code"]>0){
            		submitKC();
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    /** 保存物料库存数据 */
    var submitKC = function(){
    	var MATERIAL_CODE = $("#form"+pathValue+" #MATERIAL_CODE"+pathValue).val();
    	var JR_NUM = $("#form"+pathValue+" #JR_NUM"+pathValue).val();
        var url= "berry/prod/sop/sopmanage/updateKC";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: {"MATERIAL_CODE":MATERIAL_CODE, "JR_TYPE":"入库", "JR_NUM":JR_NUM},
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            	
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
});
 