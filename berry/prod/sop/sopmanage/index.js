$(document).ready(function() {
   var pathValue="berry-prod-sop-sopmanage-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen"},
            {name:"edit",target:"editInfo"},
            {name:"delete",target:"deleteInfo"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
        	url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-112,
            read:{"query":"query_BR_SOP_MANAGE_view","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_SOP_MANAGE_view","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGridWindow = function() {
	   var searchparams = getJsonByForm("searchWindowForm",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-112,
	            read:{"query":"query_BR_SOP_MANAGE_view","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
	                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
   }
   
   var searchWindowOpen = function() {
	   $("#searchWindow").kendoWindow({
           width: "600px",
           height: "500px",
           title: "高级查询",
           visible: false,
           actions: [
               "Close"
           ]
       }).data("kendoWindow").center().open();
   }

   /** 新增 */
   var addOpen=function(){
        var winOpts={
            url:"berry/prod/sop/sopmanage/add/add",
            title:"新增SOP.."
        };
        openWindow(winOpts,{"editType":"add", "ID":""});//传递id
    }
   /** 修改 */
   var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        
        var rData = getGridSelectData(tablesGrid);
        var winOpts={
            url:"berry/prod/sop/sopmanage/add/add",
            title:"编辑.."
        };
        var dialog = openWindow(winOpts,{"ID":arrIds[0], "editType":"edit"});//传递id
     }
   /** 删除 */
   var deleteInfo=function(){
	   var arrIds=getSelectData(tablesGrid);
       if(arrIds.length==0){
           alertMsg("请至少选择一条数据进行删除操作!");
           return ;
       }
       
       var url="berry/prod/sop/sopmanage/deleteSOPManage";
		$.fn.ajaxPost({
  		   ajaxUrl: url,
  		   ajaxType: "post",
  		   ajaxData: { SOP_MANAGE_IDS:arrIds },
  		   succeed: function(rs) {
  			   if(rs["code"]>0){
  				   //提交成功
  				   alertMsg("操作成功","success",function(){
  					   refreshGrid();
  				   });
               }else{
               	alertMsg("操作失败","error");
               }
  		   }
		});
    }

   var submit=function(){
	   formSubmit({
            formId:"form",
            pathValue:pathValue
        });
   }
     
   var callBack=function(){
	   if(tablesGrid){
            //tablesGrid.refresh();//表格刷新-回调时- 当前内容刷新
            tablesGrid.dataSource.read();//重新读取
        }
   };

   var refreshGrid=function(){
	   if(tablesGrid){
		   tablesGrid.dataSource.read();//重新读取--刷新
	   }
   }

   var importExcel=function(){
	   alert(123);
   }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "addOpen":addOpen,//打开添加表单
         "refreshGrid":refreshGrid,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "importExcel":importExcel,
         "searchGrid":searchGrid,
         "searchGridWindow":searchGridWindow,
         "searchWindowOpen":searchWindowOpen,
         "editInfo":editInfo,//修改
         "deleteInfo":deleteInfo,//删除
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
