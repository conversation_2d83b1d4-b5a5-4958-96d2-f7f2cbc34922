$(document).ready(function() {
    var pathValue="berry-prod-sop-sopmanage-add-add";
    
    /**
     * 初始化数据
     */
    var initData=function(){
        return {
        	tableName:"BR_SOP_MANAGE"
        };
    }
    
    var tablesGrid;
 
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form", pathValue, params, url);//传入id
    	
    	var editType = params.editType;
    	if(editType == "add"){
    		$("#editType_"+pathValue).html("新增明细");
    	}else{
    		$("#editType_"+pathValue).html("编辑明细");    		
    	}

    	/**
         * 列表-按钮-定义
         */
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: [
  			  {
  				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+'tablesGrid_addRow\',\'asdas\')">新增行</a>'
  			  },
  			  {
  				  template: '<a class="k-button" href="javascript:void(0)" onclick="funcExce(\''+pathValue+'tablesGrid_removeRow\',\'asdas\')">删除行</a>'
  			  }
  			],
            height: fullh-112,
            read:{"query":"query_BR_SOP_MANAGE_DETAIL_view","objects":[params.ID]},
            editable: true,
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="COMPANY_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= COMPANY_NAME #","funcExce(\'"+pathValue+"open\',\'#= ID #\',\'#= P_COMPANY_NAME #\',\'#= PROVINCE_TEXT #\',\'#= CITY_TEXT #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid_"+pathValue,tablesGridJson);//初始化表格的方法
    }
    
    /** 新增行 */
    var tablesGrid_addRow = function() {
    	var tablesGridID = "tablesGrid_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	grid.addRow();
    	var gridData = grid.dataSource.data();
    	if (gridData.length>1) {
    		var newGridData = [];
    		for (var i = 0; i < gridData.length-1; i++) {
    			newGridData[i] = gridData[i+1];
    		}
    		newGridData[ gridData.length-1 ] = gridData[0];
    		grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    		grid.editRow( $("#"+tablesGridID+" tr:eq("+newGridData.length+")") );
    	}
    }
    /** 删除行 */
    var tablesGrid_removeRow = function() {
    	var tablesGridID = "tablesGrid_"+pathValue;
    	var grid = $("#"+tablesGridID).data("kendoGrid");
    	var selectRows = grid.select();
    	if (!selectRows || selectRows.length==0) {
    		return;
    	}
    	var selectRowsIndex = "";
    	$(selectRows).each(function(i, e){
    		selectRowsIndex += ","+this.rowIndex+",";
    	});
    	// 提示再次确认
    	confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	    	var gridData = grid.dataSource.data();
			var newGridData = [];
			debugger
			for (var i = 0; i < gridData.length; i++) {
				if (selectRowsIndex.indexOf(","+i+",") < 0) {
					newGridData[newGridData.length] = gridData[i];
				}
			}
			grid.setDataSource(new kendo.data.DataSource({ data: newGridData }));
    	});
    }
    
    /** 保存数据 */
    var submit=function(){
    	formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                	submitSOPDetail(result.ID);
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    /** 保存明细 */
    var submitSOPDetail=function(sopId){
    	var grid = $("#tablesGrid_"+pathValue).data("kendoGrid");
    	var gridData = grid.dataSource.data();
    	if(!gridData){
    		return;
    	}
    	$.each(gridData, function(i, obj){
			obj.SOP_MANAGE_ID = sopId;
		});
        var url= "berry/prod/sop/sopmanage/saveSOPManage";
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: {"SOP_MANAGE_ID":sopId, "sopDetail":gridData},
            succeed:function(result){
	            if(result["code"]>0){
	            	//提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
	            	
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
        "tablesGrid_addRow":tablesGrid_addRow,
        "tablesGrid_removeRow":tablesGrid_removeRow,
        "submitSOPDetail":submitSOPDetail,
    });
});
 