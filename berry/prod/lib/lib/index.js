$(document).ready(function() {
   var pathValue="berry-prod-lib-lib-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"新增"},
            {name:"edit",target:"editInfo",title:"修改"},
    		{name:"delete",target:"deleteInfo",title:"删除"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-122,
            read:{"query":"query_BR_LIB_INFO_view","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-122,
	            read:{"query":"query_BR_LIB_INFO_view","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchGridWindow = function() {
	   var searchparams = getJsonByForm("searchWindowForm",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-122,
	            read:{"query":"query_BR_LIB_INFO_view","objects":[],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
	  // tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var searchWindowOpen = function() {
	   $("#searchWindow").kendoWindow({
           width: "600px",
           height: "500px",
           title: "高级查询",
           visible: false,
           actions: [
               "Close"
           ]
       }).data("kendoWindow").center().open();
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/prod/lib/lib/add/add",
            title:"新增预处理.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/lib/lib/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
    
    /** 删除 */
    var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
//        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
//	        var params={"ids":arrIds};
//	        var url="berry/prod/qc/qcTask/del";
//	        $.fn.ajaxPost({
//	            ajaxUrl: url,
//	            ajaxType: "post",
//	            ajaxData: params,
//	            succeed:function(result){
//		            if(result["code"]>0){
//		            	refreshGrid();
//		                alertMsg("提示:操作成功!","success");
//		            }else{
//		                alertMsg("提示:操作失败!","error");
//		            }
//	            },
//	            failed:function(result){
//	                alertMsg("提示:操作异常!","error");
//	            }
//	        });
//        });
     }

     var submit=function(){
//        formSubmit({
//            formId:"form",
//            pathValue:pathValue
//        });
     }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }

        var winOpts={
            url:"berry/prod/lib/lib/edit/edit",
            title:"编辑预处理.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submit":submit,//提交方法
         "searchGrid":searchGrid,
         "callBack":callBack,//回调方法
         "searchGridWindow":searchGridWindow,
         "searchWindowOpen":searchWindowOpen,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
