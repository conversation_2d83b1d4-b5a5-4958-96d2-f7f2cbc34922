$(document).ready(function() {
   var pathValue="berry-prod-lib-poolingBalanceTGS-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"选择添加平衡文库"},
    		{name:"delete",target:"deleteInfo",title:"移除平衡文库"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-122,
            read:{"query":"query_BR_MODUAL_BH_T_view_balanceLib","objects":[ "Y" ]}
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
   }
   
   var callBack=function(){
  	 refreshGrid();
   };
   var refreshGrid=function(){
      if(tablesGrid){
          tablesGrid.dataSource.read();//重新读取--刷新
      }
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/prod/lib/poolingBalanceTGS/add/add",
            title:"选择添加: TGS平衡文库.."
        };
        openWindow(winOpts);
    }
    
    /** 移除 */
    var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据");
            return ;
        }
        confirmMsg("确认", "确定要移除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds,"flag":"N"};
	        var url="berry/prod/lib/pooling/TGS/balanceLib";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid();
		                alertMsg("提示:操作成功!","success");
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
         "addOpen":addOpen,
         "deleteInfo":deleteInfo,
     });
});
