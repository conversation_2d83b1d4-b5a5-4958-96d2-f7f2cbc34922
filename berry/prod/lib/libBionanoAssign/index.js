$(document).ready(function() {
   var pathValue="berry-prod-lib-libBionanoAssign-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"submit",target:"assign1",title:"任务指派"},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-122,
            read:{"query":"query_BR_MODUAL_LIB_B_view","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
            {name:"edit",target:"assign2",title:"任务改派"},
    	]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
            height: fullh-122,
            read:{"query":"query_BR_LIB_INFO_B_view","objects":[ ["构建中","已提交"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/lib/libQUBIT/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid2=function(){
         if(tablesGrid2){
             tablesGrid2.dataSource.read();//重新读取--刷新
         }
      }

     var assign1=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据!");
            return ;
        }
        var winOpts={
            url:"berry/prod/lib/libBionanoAssign/assign1/assign",
            title:"任务指派: Bionano文库.."
        };
        var IDS = "";
        for (var i = 0; i < arrIds.length; i++) {
        	IDS += IDS.length > 0 ? (","+arrIds[i]) : arrIds[i];
        }
        var dialog = openWindow(winOpts,{ "IDS":IDS });
     }

     var assign2=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据!");
            return ;
        }
        var winOpts={
            url:"berry/prod/lib/libBionanoAssign/assign1/assign",
            title:"任务改派: Bionano文库.."
        };
        var IDS = "";
        for (var i = 0; i < arrIds.length; i++) {
        	IDS += IDS.length > 0 ? (","+arrIds[i]) : arrIds[i];
        }
        var dialog = openWindow(winOpts,{ "IDS":IDS });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "assign1":assign1,
         "assign2":assign2,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
