$(document).ready(function() {
   var pathValue="berry-prod-lib-poolingRDNGS-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tables_1_Grid;
   var tables_2_Grid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar_t1 = getButtonTemplates(pathValue,[
            {name:"import",target:"importData",title:"导入研发文库"},
    		{name:"delete",target:"deleteInfo",title:"删除研发文库"},
        ]);//工具条
        //请求参数
        var tablesGridJson_t1={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_t1,
            height: fullh-122,
            read:{"query":"query_BR_MODUAL_BH_view_RDLib","objects":[],
            	search:{ "POOL_ID_IS_NULL":"IS NULL" }
            }
        };
        tables_1_Grid = initKendoGrid("#tables_1_Grid"+pathValue,tablesGridJson_t1);//初始化表格的方法
        
        /**
         * 列表-按钮-定义
         */
        var toolbar_t2 = getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_t2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_t2,
            height: fullh-122,
            read:{"query":"query_BR_MODUAL_BH_view_RDLib","objects":[],
            	search:{ "POOL_ID_IS_NOT_NULL":"IS NOT NULL" }
            }
        };
        tables_2_Grid = initKendoGrid("#tables_2_Grid"+pathValue,tablesGridJson_t2);//初始化表格的方法
   }
   
   var callBack=function(){
	   refreshGrid_t1();
	   refreshGrid_t2();
   };
   var refreshGrid_t1=function(){
	      if(tables_1_Grid){
	    	  tables_1_Grid.dataSource.read();
	      }
	   }
   var refreshGrid_t2=function(){
      if(tables_2_Grid){
    	  tables_2_Grid.dataSource.read();
      }
   }

    var importData=function(componentId){
        var params={ "templateName":"NGSPooling研发文库导入模板" };
        var url="berry/prod/lib/pooling/NGS/RD/importTemplate";//获取模板文件路径
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:url,
            ajaxData:params,
            succeed:function(result){
                if(result["code"]>0){
                    openComponent({
                        name:"导入数据",//组件名称
                        componentId:componentId,
                        params:{
                        "template":function(){
                            downloadFile(result["templateFile"], "report-template-path");
                        },//第二种方式
                        "import":function(info,importPathValue){
                            $.fn.ajaxPost({
                                ajaxUrl: "berry/prod/lib/pooling/NGS/RD/import",//导入数据
                                ajaxType: "post",
                                ajaxData: {"info":info},
                                succeed:function(results){
                                    if(results["code"]>0){
                                        refreshGrid_t1();
                                        alertMsg("导入成功!","success",funcExce(importPathValue+"close"));
                                    }else{
                                        alertMsg(results["message"],"success");
                                    }
                                },
                                failed:null
                            });
                        }}
                    });
                }else{
                    alertMsg("导出模板获取失败!","success");
                }
            }
        });
    }
    
    /** 移除 */
    var deleteInfo=function(){
        var arrIds=getSelectData(tables_1_Grid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据");
            return ;
        }
        confirmMsg("确认", "确定要移除选中的数据吗?", "warn", function() {
	        var params={"ids":arrIds};
	        var url="berry/prod/lib/pooling/NGS/RD/del";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid_t1();
		                alertMsg("提示:操作成功!","success");
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refreshGrid_t1":refreshGrid_t1,
         "refreshGrid_t2":refreshGrid_t2,
         "callBack":callBack,//回调方法
         "importData":importData,
         "deleteInfo":deleteInfo,
     });
});
