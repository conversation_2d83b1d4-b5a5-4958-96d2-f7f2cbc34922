$(document).ready(function() {
   var pathValue="berry-prod-lib-libZ-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
//            {name:"add",target:"addOpen",title:"新增"},
            {name:"edit",target:"editInfo",title:"修改"},
            {name:"edit",target:"editInfoBatch",title:"批量修改"},
    		{name:"delete",target:"deleteInfo",title:"删除"},
    		{name:"submit",target:"submitLibZ",title:"提交 "},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-122,
            read:{"query":"query_BR_LIB_INFO_Z_view","objects":[ ["构建中"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法
        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
    		{name:"delete",target:"revokeLibWfSubmit",title: "流程撤回"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
           height: fullh-122,
            read:{"query":"query_BR_LIB_INFO_Z_view","objects":[ ["已提交"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/prod/lib/libZ/add/add",
            title:"新增: 终文库.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/lib/libQUBIT/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
        /** 删除 */
        var deleteInfo=function(){
            var arrIds=getSelectData(tablesGrid);
            if(arrIds.length==0){
                alertMsg("请至少选择一条数据进行删除操作!");
                return ;
            }
            confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
    	        var params={"ids":arrIds};
    	        var url="berry/prod/lib/libZ/del";
    	        $.fn.ajaxPost({
    	            ajaxUrl: url,
    	            ajaxType: "post",
    	            ajaxData: params,
    	            succeed:function(result){
    		            if(result["code"]>0){
    		            	refreshGrid();
    		                alertMsg("提示:操作成功!","success");
    		            }else{
    		                alertMsg("提示:操作失败!","error");
    		            }
    	            },
    	            failed:function(result){
    	                alertMsg("提示:操作异常!","error");
    	            }
    	        });
            });
         }

        var submitLibZ=function(){
            var arrIds=getSelectData(tablesGrid);
            if(arrIds.length==0){
                alertMsg("请至少选择一条数据进行提交操作!");
                return ;
            }
            confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
                var url="berry/workflow/submit/libZ";
                var params={"objects":arrIds}
                $.fn.ajaxPost({
                    ajaxUrl: url,
                    ajaxType: "post",
                    ajaxData: params,
                    succeed:function(result){
        	            if(result["code"]>0){
        	            	refreshGrid();
        	            	refreshGrid2();
        	                alertMsg("提示:操作成功!","success");
        	            }else{
        	                alertMsg("提示:操作失败!","error");
        	            }
                    },
                    failed:function(result){
                        alertMsg("提示:操作异常!","error");
                    }
                });
            });
         }
     
     var callBack=function(){
    	 refreshGrid();
    	 refreshGrid2();
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     var refreshGrid2=function(){
         if(tablesGrid2){
             tablesGrid2.dataSource.read();//重新读取--刷新
         }
      }
     
     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var winOpts={
            url:"berry/prod/lib/libZ/edit/edit",
            title:"编辑: 终文库.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }
     var editInfoBatch=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行批量修改!");
             return ;
         }
         confirmMsg("确认", "批量修改会批量覆盖所有选择数据，请谨慎操作", "warn", function() {
	         var ids = "";
	         for (var i=0;i<arrIds.length;i++) {
	        	 ids += ids.length>0? "," : "";
	        	 ids += arrIds[i];
	         }
	         var winOpts={
	             url:"berry/prod/lib/libZ/editBatch/editBatch",
	             title:"批量修改: 终文库.."
	         };
	         var dialog = openWindow(winOpts,{ "IDS":ids });
         });
      }
     
     var revokeLibWfSubmit=function(){
         var arrIds=getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据执行撤回操作!");
             return ;
         }
         confirmMsg("提示","确定对 "+arrIds.length+" 条数据执行撤回操作?","question",function(){
             var url="berry/workflow/revoke/lib";
             var params={"ids":arrIds, "type":"Z"};
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	refreshGrid();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "editInfoBatch":editInfoBatch,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "submitLibZ":submitLibZ,//提交方法
         "callBack":callBack,//回调方法
         "revokeLibWfSubmit":revokeLibWfSubmit,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
