$(document).ready(function() {
   var pathValue="berry-prod-lib-poolingNGS-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid_M;
   var tablesGrid_D;
   var tablesGrid_M2;
   var tablesGrid_D2;
   var tablesGrid_M_selectRowID="";
   var tablesGrid_M2_selectRowID="";

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar_M=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen_M",title:"新增"},
            {name:"edit",target:"editInfo_M",title:"修改"},
            {name:"delete",target:"delete_M",title:"删除"},
            {name:"commitToApprove_M",target:"commitToApprove_M", title:"提交审核"},
        ]);//工具条
        //请求参数
        var tablesGridJson_M={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M,
            height: (fullh-122)/2,
            read:{"query":"query_BR_POOLING_INFO_view","objects":[ ["Pooling"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M = initKendoGrid("#tablesGrid_M_"+pathValue,tablesGridJson_M);//初始化表格的方法
        tablesGrid_M.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M_selectRowID = "";
        		tablesGrid_D.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M_selectRowID = thisRowID;
        	searchGrid_D();//刷新子表数据
        });
        tablesGrid_M.bind("dataBinding", function(e) {
	        if (tablesGrid_M_selectRowID) {
		  	    tablesGrid_M_selectRowID = "";
			    searchGrid_D();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D=getButtonTemplates(pathValue,[
            {name:"add",target:"selectLIB_D",title:"添加文库"},
            {name:"delete",target:"deleteInfo_D", title:"移除"},
        ]);//工具条
        //请求参数
        var tablesGridJson_D={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D,
            height: (fullh-122)/2,
            read:{"query":"query_BR_POOLING_MX_INFO_view","objects":[""]}
        };
        tablesGrid_D = initKendoGrid("#tablesGrid_D_"+pathValue,tablesGridJson_D);//初始化表格的方法
        

        /**
         * 列表-按钮-定义
         */
        var toolbar_M2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_M2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_M2,
            height: (fullh-122)/2,
            read:{"query":"query_BR_POOLING_INFO_view","objects":[ ["待审核","已审核"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ID"){
                        setJsonParam(cols[i],"template",getTemplate("<input style='display:none;' class='mainGridRowID"+pathValue+"' value='#= ID #'>#= ID #","","txt"));
                    }
                }
            },
            selectable: true
        };
        tablesGrid_M2 = initKendoGrid("#tablesGrid_M2_"+pathValue,tablesGridJson_M2);//初始化表格的方法
        tablesGrid_M2.bind("change", function(e) {
        	var rData = getGridSelectData(this);
        	if (rData.length==0) {
        		tablesGrid_M2_selectRowID = "";
        		tablesGrid_D2.setDataSource(new kendo.data.DataSource({ data: [] }));//清空子表数据
        		return;
        	}
        	// 当前选中行号
        	var thisRowID = rData[0].ID;
        	
        	// 复选框选中状态控制
        	var h_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-header-locked input");//标题头的复选框
        	var c_ck = $("#tablesGrid_M2_"+pathValue+" .k-grid-content-locked input");//内容行复选框
        	$.each(c_ck, function(i, val) {
            	$(this).removeAttr("checked");
        	});
        	$.each(h_ck, function(i, val) {
        		$(this).removeAttr("checked");
        	});
        	// 打钩选中行号
    	   	var ROWS_ID = $("#tablesGrid_M2_"+pathValue+" .mainGridRowID"+pathValue);//行ID
    	   	for (var i = 0; i < ROWS_ID.length; i++) {
    	   		if ( thisRowID == $(ROWS_ID[i]).val() ) {
    	   			$(c_ck[i]).prop("checked", true);
    	   			break;
    	   		}
    	   	}
        	
        	if (thisRowID == tablesGrid_M2_selectRowID) {//两次选中同一行，不往下操作
        		return;
        	}
        	tablesGrid_M2_selectRowID = thisRowID;
        	searchGrid_D2();//刷新子表数据
        });
        tablesGrid_M2.bind("dataBinding", function(e) {
	        if (tablesGrid_M2_selectRowID) {
		  	    tablesGrid_M2_selectRowID = "";
			    searchGrid_D2();
	        }
        });
        /**
         * 列表-按钮-定义
         */
        var toolbar_D2=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var tablesGridJson_D2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_D2,
            height: (fullh-122)/2,
            read:{"query":"query_BR_POOLING_MX_INFO_view","objects":[""]}
        };
        tablesGrid_D2 = initKendoGrid("#tablesGrid_D2_"+pathValue,tablesGridJson_D2);//初始化表格的方法
   }
   var searchGrid_D = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	            height: (fullh-122)/2,
	            read:{"query":"query_BR_POOLING_MX_INFO_view","objects":[tablesGrid_M_selectRowID] }
	        };
	  setGridDataSource("#tablesGrid_D_"+pathValue,tablesGridJson);
   }
   var searchGrid_D2 = function() {
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	            height: (fullh-122)/2,
	            read:{"query":"query_BR_POOLING_MX_INFO_view","objects":[tablesGrid_M2_selectRowID] }
	        };
	  setGridDataSource("#tablesGrid_D2_"+pathValue,tablesGridJson);
   }

    var addOpen_M=function(){
        var winOpts={
            url:"berry/prod/lib/poolingNGS/addM/add",
            title:"新增: NGS Pooling 主单.."
        };
        openWindow(winOpts);
    }
     
     var callBack=function(){
    	 refreshGrid_M();
    	 refreshGrid_M2();
     };

     var refreshGrid_M=function(){
        if(tablesGrid_M){
            tablesGrid_M.dataSource.read();//重新读取--刷新
            tablesGrid_M_selectRowID="";
            searchGrid_D();
        }
     }
     var refreshGrid_M2=function(){
         if(tablesGrid_M2){
             tablesGrid_M2.dataSource.read();//重新读取--刷新
             tablesGrid_M2_selectRowID="";
             searchGrid_D2();
         }
      }

     var editInfo_M=function(){
        var arrIds=getSelectData(tablesGrid_M);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }

        var winOpts={
            url:"berry/prod/lib/poolingNGS/addM/add",
            title:"修改: NGS Pooling 主单.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }

     var commitToApprove_M=function(){
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据提交审核操作!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/prod/lib/pooling/NGS/commitToApprove";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	refreshGrid_M();
 	            	refreshGrid_M2();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }

     var delete_M=function(){
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据删除操作!");
             return ;
         }
         var params={"ids":arrIds};
         var url="berry/prod/lib/pooling/NGS/del";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	refreshGrid_M();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }
     
     var selectLIB_D = function() {
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
    		 alertMsg("选择一行主单再添加文库!","error");
    		 return;
    	 }
    	 var winOpts={
    	    url:"berry/prod/lib/poolingNGS/addMX/addMX",
    	    title:"添加文库.."
    	 };
    	 var dialog = openWindow(winOpts,{ "POOL_LIB_ID": tablesGrid_M_selectRowID, "pPathValue":pathValue });//传递id
     }
     var deleteInfo_D = function() {
         var arrIds=getSelectData(tablesGrid_M);
         if(arrIds.length==0){
    		 alertMsg("选择一行主单!","error");
    		 return;
    	 }
         var arrIds2=getSelectData(tablesGrid_D);
         if(arrIds2.length==0){
             alertMsg("请至少选择一条数据删除操作!");
             return ;
         }
         var params={"POOL_LIB_ID": tablesGrid_M_selectRowID, "ids":arrIds2};
         var url="berry/prod/lib/pooling/NGS/delMX";
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 	            	searchGrid_D();
 	                alertMsg("提示:操作成功!","success");
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "addOpen_M":addOpen_M,//打开添加表单
         "editInfo_M":editInfo_M,
         "commitToApprove_M":commitToApprove_M,
         "delete_M":delete_M,
         "searchGrid_D":searchGrid_D,
         "searchGrid_D2":searchGrid_D2,
         "refreshGrid_M":refreshGrid_M,
         "refreshGrid_M2":refreshGrid_M2,
         "callBack":callBack,//回调方法
         "selectLIB_D":selectLIB_D,
         "deleteInfo_D":deleteInfo_D,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
