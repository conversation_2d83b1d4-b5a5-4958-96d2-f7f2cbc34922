$(document).ready(function() {
   var pathValue="berry-prod-lib-poolingNGS-addMX-addMX";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tables_1_Grid;
   var tables_2_Grid;
   var tables_3_Grid;
   var tables_4_Grid;
   var tables_5_Grid;
   var tables_6_Grid;
   var tables_7_Grid;
   var tables_8_Grid;
   
   var POOL_LIB_ID = "";
   var pPathValue = "";

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
	   POOL_LIB_ID = params.POOL_LIB_ID;
	   pPathValue = params.pPathValue;
       if ( !POOL_LIB_ID ) {
    	   alertMsg("选中的主单传参错误!");
           return;
       }
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_1=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddLib_1",title:"确认添加"},
            {name:"delete",target:"selectionLibToIgnore_1",title:"移至暂不排"},
        ]);//工具条
        //请求参数
        var tables_1_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_1,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "N" ],
            	"search":{ "SEARCH_WHERE_1":"SEARCH_WHERE_1" }
            }
        };
        tables_1_Grid = initKendoGrid("#tables_1_Grid"+pathValue,tables_1_GridJson);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_2=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddLib_2",title:"确认添加"},
            {name:"delete",target:"selectionLibToIgnore_2",title:"移至暂不排"},
        ]);//工具条
        //请求参数
        var tables_2_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_2,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "N" ],
            	"search":{ "SEARCH_WHERE_2":"SEARCH_WHERE_2" }
            }
        };
        tables_2_Grid = initKendoGrid("#tables_2_Grid"+pathValue,tables_2_GridJson);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_3=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddLib_3",title:"确认添加"},
            {name:"delete",target:"selectionLibToIgnore_3",title:"移至暂不排"},
        ]);//工具条
        //请求参数
        var tables_3_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_3,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "N" ],
            	"search":{ "SEARCH_WHERE_3":"SEARCH_WHERE_3" }
            }
        };
        tables_3_Grid = initKendoGrid("#tables_3_Grid"+pathValue,tables_3_GridJson);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_4=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddLib_4",title:"确认添加"},
            {name:"delete",target:"selectionLibToIgnore_4",title:"移至暂不排"},
        ]);//工具条
        //请求参数
        var tables_4_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_4,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "N" ],
            	"search":{ "SEARCH_WHERE_4":"SEARCH_WHERE_4" }
            }
        };
        tables_4_Grid = initKendoGrid("#tables_4_Grid"+pathValue,tables_4_GridJson);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_5=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddLib_5",title:"确认添加"},
//            {name:"delete",target:"selectionLibToIgnore_5",title:"移至暂不排"},
        ]);//工具条
        //请求参数
        var tables_5_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_5,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "N" ],
            	"search":{ "SEARCH_WHERE_5":"SEARCH_WHERE_5" }
            }
        };
        tables_5_Grid = initKendoGrid("#tables_5_Grid"+pathValue,tables_5_GridJson);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_6=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddLib_6",title:"确认添加"},
            {name:"delete",target:"selectionLibToIgnore_6",title:"移至暂不排"},
        ]);//工具条
        //请求参数
        var tables_6_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_6,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "N" ],
            	"search":{ "SEARCH_WHERE_6":"SEARCH_WHERE_6" }
            }
        };
        tables_6_Grid = initKendoGrid("#tables_6_Grid"+pathValue,tables_6_GridJson);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_7=getButtonTemplates(pathValue,[
            {name:"add",target:"confirmAddLib_7",title:"确认添加"},
            {name:"delete",target:"selectionLibToIgnore_7",title:"移至暂不排"},
        ]);//工具条
        //请求参数
        var tables_7_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_7,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "N" ],
            	"search":{ "SEARCH_WHERE_7":"SEARCH_WHERE_7" }
            }
        };
        tables_7_Grid = initKendoGrid("#tables_7_Grid"+pathValue,tables_7_GridJson);//初始化表格的方法
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar_8=getButtonTemplates(pathValue,[
//            {name:"add",target:"confirmAddLib_8",title:"确认添加"},
            {name:"add",target:"selectionLibToIgnore_8",title:"恢复至待排"},
        ]);//工具条
        //请求参数
        var tables_8_GridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar_8,
            height: fullh-196,
            read:{"query":"query_BR_MODUAL_BH_view","objects":[ "Y" ],
            	"search":{ "SEARCH_WHERE_8":"SEARCH_WHERE_8" }
            }
        };
        tables_8_Grid = initKendoGrid("#tables_8_Grid"+pathValue,tables_8_GridJson);//初始化表格的方法
   }
   var callBack=function(){
	   refresh_1_Grid();
	   refresh_2_Grid();
	   refresh_3_Grid();
	   refresh_4_Grid();
	   refresh_5_Grid();
	   refresh_6_Grid();
	   refresh_7_Grid();
	   refresh_8_Grid();
   };
   var refresh_1_Grid=function(){
      if(tables_1_Grid){
    	  tables_1_Grid.dataSource.read();
      }
   }
   var refresh_2_Grid=function(){
      if(tables_2_Grid){
    	  tables_2_Grid.dataSource.read();
      }
   }
   var refresh_3_Grid=function(){
      if(tables_3_Grid){
    	  tables_3_Grid.dataSource.read();
      }
   }
   var refresh_4_Grid=function(){
      if(tables_4_Grid){
    	  tables_4_Grid.dataSource.read();
      }
   }
   var refresh_5_Grid=function(){
      if(tables_5_Grid){
    	  tables_5_Grid.dataSource.read();
      }
   }
   var refresh_6_Grid=function(){
      if(tables_6_Grid){
    	  tables_6_Grid.dataSource.read();
      }
   }
   var refresh_7_Grid=function(){
      if(tables_7_Grid){
    	  tables_7_Grid.dataSource.read();
      }
   }
   var refresh_8_Grid=function(){
      if(tables_8_Grid){
    	  tables_8_Grid.dataSource.read();
      }
   }

     var confirmAddLib=function(gridIndex){
        var arrIds;
        if (gridIndex == 1) {
        	arrIds = getSelectData(tables_1_Grid);
        } else if (gridIndex == 2) {
        	arrIds = getSelectData(tables_2_Grid);
        } else if (gridIndex == 3) {
        	arrIds = getSelectData(tables_3_Grid);
        } else if (gridIndex == 4) {
        	arrIds = getSelectData(tables_4_Grid);
        } else if (gridIndex == 5) {
        	arrIds = getSelectData(tables_5_Grid);
        } else if (gridIndex == 6) {
        	arrIds = getSelectData(tables_6_Grid);
        } else if (gridIndex == 7) {
        	arrIds = getSelectData(tables_7_Grid);
        } else if (gridIndex == 8) {
        	arrIds = getSelectData(tables_8_Grid);
        }
        if( !arrIds || arrIds.length==0 ){
            alertMsg("请至少选择一条数据!");
            return;
        }
        var url= "berry/prod/lib/pooling/NGS/addMX";
        var params = { "POOL_LIB_ID":POOL_LIB_ID , "ids":arrIds, "addIndexFlag":gridIndex };
        $.fn.ajaxPost({
            ajaxUrl: url,
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
	            if(result["code"]>0){
		            //提交成功
	                alertMsg("提交成功","success",function(){
	                   funcExce(pPathValue+"searchGrid_D");//执行刷新主页明细表
	                   funcExce(pathValue+"close");//关闭页面
	                });
	            }else{
	                alertMsg("提示:操作失败!","error");
	            }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
     }
     var confirmAddLib_1 = function() {
    	 confirmAddLib(1);
     }
     var confirmAddLib_2 = function() {
    	 confirmAddLib(2);
     }
     var confirmAddLib_3 = function() {
    	 confirmAddLib(3);
     }
     var confirmAddLib_4 = function() {
    	 confirmAddLib(4);
     }
     var confirmAddLib_5 = function() {
    	 confirmAddLib(5);
     }
     var confirmAddLib_6 = function() {
    	 confirmAddLib(6);
     }
     var confirmAddLib_7 = function() {
    	 confirmAddLib(7);
     }
     var confirmAddLib_8 = function() {
    	 confirmAddLib(8);
     }

     var selectionLibToIgnore = function(gridIndex){
         var arrIds;
         if (gridIndex == 1) {
         	arrIds = getSelectData(tables_1_Grid);
         } else if (gridIndex == 2) {
         	arrIds = getSelectData(tables_2_Grid);
         } else if (gridIndex == 3) {
         	arrIds = getSelectData(tables_3_Grid);
         } else if (gridIndex == 4) {
         	arrIds = getSelectData(tables_4_Grid);
         } else if (gridIndex == 5) {
         	arrIds = getSelectData(tables_5_Grid);
         } else if (gridIndex == 6) {
         	arrIds = getSelectData(tables_6_Grid);
         } else if (gridIndex == 7) {
         	arrIds = getSelectData(tables_7_Grid);
         } else if (gridIndex == 8) {
         	arrIds = getSelectData(tables_8_Grid);
         }
         if( !arrIds || arrIds.length==0 ){
             alertMsg("请至少选择一条数据!");
             return;
         }
         var url= "berry/prod/lib/pooling/NGS/addMXToIgnore";
         var params = { "ids":arrIds, "poolIgnore":gridIndex };
         $.fn.ajaxPost({
             ajaxUrl: url,
             ajaxType: "post",
             ajaxData: params,
             succeed:function(result){
 	            if(result["code"]>0){
 		            //提交成功
 	                alertMsg("提交成功","success",function(){
 	                   if (gridIndex == 1) {
 	                	  refresh_1_Grid();
 	                	  refresh_8_Grid();
 	                   } else if (gridIndex == 2) {
 	                	  refresh_2_Grid();
 	                	  refresh_8_Grid();
 	                   } else if (gridIndex == 3) {
 	                	  refresh_3_Grid();
 	                	  refresh_8_Grid();
 	                   } else if (gridIndex == 4) {
 	                	  refresh_4_Grid();
 	                	  refresh_8_Grid();
 	                   } else if (gridIndex == 5) {
 	                	  refresh_5_Grid();
 	                	  refresh_8_Grid();
 	                   } else if (gridIndex == 6) {
 	                	  refresh_6_Grid();
 	                	  refresh_8_Grid();
 	                   } else if (gridIndex == 7) {
 	                	  refresh_7_Grid();
 	                	  refresh_8_Grid();
 	                   } else if (gridIndex == 8) {
 	                	  callBack();
 	                   }
 	                });
 	            }else{
 	                alertMsg("提示:操作失败!","error");
 	            }
             },
             failed:function(result){
                 alertMsg("提示:操作异常!","error");
             }
         });
      }
      var selectionLibToIgnore_1 = function() {
     	selectionLibToIgnore(1);
     }
      var selectionLibToIgnore_2 = function() {
     	selectionLibToIgnore(2);
     }
      var selectionLibToIgnore_3 = function() {
     	selectionLibToIgnore(3);
     }
      var selectionLibToIgnore_4 = function() {
     	selectionLibToIgnore(4);
     }
      var selectionLibToIgnore_5 = function() {
     	selectionLibToIgnore(5);
     }
     var selectionLibToIgnore_6 = function() {
     	selectionLibToIgnore(6);
     }
     var selectionLibToIgnore_7 = function() {
     	selectionLibToIgnore(7);
     }
     var selectionLibToIgnore_8 = function() {
     	selectionLibToIgnore(8);
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "confirmAddLib_1":confirmAddLib_1,
         "confirmAddLib_2":confirmAddLib_2,
         "confirmAddLib_3":confirmAddLib_3,
         "confirmAddLib_4":confirmAddLib_4,
         "confirmAddLib_5":confirmAddLib_5,
         "confirmAddLib_6":confirmAddLib_6,
         "confirmAddLib_7":confirmAddLib_7,
         "confirmAddLib_8":confirmAddLib_8,
         "selectionLibToIgnore_1":selectionLibToIgnore_1,
         "selectionLibToIgnore_2":selectionLibToIgnore_2,
         "selectionLibToIgnore_3":selectionLibToIgnore_3,
         "selectionLibToIgnore_4":selectionLibToIgnore_4,
         "selectionLibToIgnore_5":selectionLibToIgnore_5,
         "selectionLibToIgnore_6":selectionLibToIgnore_6,
         "selectionLibToIgnore_7":selectionLibToIgnore_7,
         "selectionLibToIgnore_8":selectionLibToIgnore_8,
     });
});
