$(document).ready(function() {
   var pathValue="berry-prod-lib-lib2100-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var tablesGrid;
   var tablesGrid2;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addOpen",title:"新增"},
            {name:"edit",target:"editInfo",title:"修改"},
    		{name:"delete",target:"deleteInfo",title:"删除"},
    		{name:"submit",target:"submit",title:"提交 "},
        ]);//工具条
        //请求参数
        var tablesGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
           height: fullh-122,
            read:{"query":"query_BR_LIB_2100_view","objects":[ ["进行中"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid = initKendoGrid("#tablesGrid"+pathValue,tablesGridJson);//初始化表格的方法

        /**
         * 列表-按钮-定义
         */
        var toolbar2=getButtonTemplates(pathValue,[
        	{name:"delete",target:"revokeLibQCwfSubmit",title: "流程撤回"},
        ]);//工具条
        //请求参数
        var tablesGridJson2={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar2,
           height: fullh-122,
            read:{"query":"query_BR_LIB_2100_view","objects":[ ["已提交", "已提交方案"] ]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        tablesGrid2 = initKendoGrid("#tablesGrid2_"+pathValue,tablesGridJson2);//初始化表格的方法
   }
   
   var searchGrid = function() {
	   var searchparams = getJsonByForm("searchform",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-122,
	            read:{"query":"query_BR_LIB_2100_view","objects":[ ["进行中"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid"+pathValue,tablesGridJson);
   }
   
   var searchGrid2 = function() {
	   var searchparams = getJsonByForm("searchform2",pathValue);
	   var tablesGridJson = {
	            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
	            sort: "",//排序
	            toolbar: toolbar,
	           height: fullh-122,
	            read:{"query":"query_BR_LIB_2100_view","objects":[ ["已提交", "已提交方案"] ],
	            	"search": searchparams
	            },
	            headerFilter:function(cols,i){
	                if(i){
	                    if(cols[i]["field"]&&cols[i]["field"]=="ITEM_TEXT"){
	                        setJsonParam(cols[i],"template",getTemplate("#= ITEM_TEXT #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
	                    }
	                }
	            }
	        };
	  setGridDataSource("#tablesGrid2_"+pathValue,tablesGridJson);
   }

    var addOpen=function(){
        var winOpts={
            url:"berry/prod/lib/lib2100/add/add",
            title:"新增2100.."
        };
        openWindow(winOpts);
    }

    var open=function(id){
//        openWindow({
//            url:"berry/prod/lib/lib2100/add/add",
//            title:"编辑.."
//        },{"ID":id});
    }
    
    /** 删除 */
    var deleteInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid);
        var msg = "";
        for (var i = 0; i < rData.length; i++) {
        	if (rData[i].QC_2100_FLAG!='进行中') {
        		msg = "--";
        		break;
        	}
        }
        if(msg.length>0){
            alertMsg("只能删除“进行中”状态的数据");
            return ;
        }
        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function() {
	        var params={ "type":"2100", "ids":arrIds };
	        var url="berry/prod/lib/libQC/del";
	        $.fn.ajaxPost({
	            ajaxUrl: url,
	            ajaxType: "post",
	            ajaxData: params,
	            succeed:function(result){
		            if(result["code"]>0){
		            	refreshGrid();
		                alertMsg("提示:操作成功!","success");
		            }else{
		                alertMsg("提示:操作失败!","error");
		            }
	            },
	            failed:function(result){
	                alertMsg("提示:操作异常!","error");
	            }
	        });
        });
     }

     var submit=function(){
         var arrIds=getSelectData(tablesGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行提交操作!");
             return ;
         }
         var rData = getGridSelectData(tablesGrid);
         var msg = "";
         for (var i = 0; i < rData.length; i++) {
         	if (rData[i].QC_2100_FLAG!='进行中') {
         		msg = "--";
         		break;
         	}
         }
         if(msg.length>0){
             alertMsg("只能提交“进行中”状态的数据");
             return ;
         }
         confirmMsg("确认", "确定要提交选中的数据吗?", "warn", function() {
 	        var params={ "type":"2100", "ids":arrIds };
 	        var url="berry/prod/lib/libQC/commit";
 	        $.fn.ajaxPost({
 	            ajaxUrl: url,
 	            ajaxType: "post",
 	            ajaxData: params,
 	            succeed:function(result){
 		            if(result["code"]>0){
 		            	refreshGrid();
 		            	refreshGrid2();
 		                alertMsg("提示:操作成功!","success");
 		            }else{
 		                alertMsg("提示:操作失败!","error");
 		            }
 	            },
 	            failed:function(result){
 	                alertMsg("提示:操作异常!","error");
 	            }
 	        });
         });
      }
     
     var callBack=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取
        }
     };

     var refreshGrid=function(){
        if(tablesGrid){
            tablesGrid.dataSource.read();//重新读取--刷新
        }
     }
     
     var callBack2=function(){
        if(tablesGrid2){
            tablesGrid2.dataSource.read();//重新读取
        }
     };

     var refreshGrid2=function(){
        if(tablesGrid2){
            tablesGrid2.dataSource.read();//重新读取--刷新
        }
     }

     var editInfo=function(){
        var arrIds=getSelectData(tablesGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        var rData = getGridSelectData(tablesGrid);
        if (rData[0].QC_2100_FLAG!='进行中') {
            alertMsg("只能提交“进行中”状态的数据");
            return ;
        }
        var winOpts={
            url:"berry/prod/lib/lib2100/edit/edit",
            title:"编辑: 文库2100.."
        };
        var dialog = openWindow(winOpts,{ "ID":arrIds[0] });//传递id
     }
     
     var revokeLibQCwfSubmit=function(){
         var arrIds=getSelectData(tablesGrid2);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据执行撤回操作!");
             return ;
         }
         confirmMsg("提示","确定对 "+arrIds.length+" 条数据执行撤回操作?<br/>此操作会同时撤回2100 QPCR QUBIT","question",function(){
             var url="berry/prod/lib/libQC/revokeLibQCFlow";
             var params={"ids":arrIds};
             $.fn.ajaxPost({
                 ajaxUrl: url,
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
     	            if(result["code"]>0){
     	            	refreshGrid();
     	            	refreshGrid2();
     	                alertMsg("提示:操作成功!","success");
     	            }else{
     	                alertMsg("提示:操作失败!","error");
     	            }
                 },
                 failed:function(result){
                     alertMsg("提示:操作异常!","error");
                 }
             });
         });
     }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,//打开新窗口方法-此方法非必须-自定义
         "addOpen":addOpen,//打开添加表单
         "editInfo":editInfo,
         "deleteInfo":deleteInfo,
         "searchGrid":searchGrid,
         "refreshGrid":refreshGrid,
         "searchGrid2":searchGrid2,
         "refreshGrid2":refreshGrid2,
         "submit":submit,//提交方法
         "callBack":callBack,//回调方法
         "revokeLibQCwfSubmit":revokeLibQCwfSubmit,
        //  "close":close,//关闭当前页方法--已默认注册-无需注册
        //  "getDialog":getDialog,//获取当前页对象方法--已默认注册-无需注册
     });
});
