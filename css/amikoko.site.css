@charset "UTF-8";
/*!
 * Kendo UI Admin v1.0.0 by IKKI & Amikoko - https://ikki2000.github.io/
 * Copyright 2018-2019 IKKI Studio
 * Released under the MIT License - http://127.0.0.1:8072/LICENSE
 */
/* CSS for All Site Pages | Written by <PERSON><PERSON><PERSON> | 2018-02-03 */
/* 主体框架结构 ------------------------------ */
html,
body {
  overflow: hidden;
  width: 100%;
  height: 100%;
  font-family: "Microsoft YaHei", arial, simsun, sans-serif;
  font-size: 14px; }

#aside {
  overflow: hidden;
  position: fixed;
  top: 0;
  left: -160px;
  z-index: 9999;
  width: 160px;
  height: 100%;
  transition: all .3s; }

#main {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  transition: all .3s; }

#header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  width: 100%;
  height: 60px;
  background: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); }

#section {
  overflow: auto;
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
  height: calc(100% - 60px);
  background: #eee; }

#container {
  position: relative;
  min-height: 96%; }

#footer {
  height: 4%;
  font-size: 12px;
  line-height: 12px;
  text-align: center;
  color: #999;
  background: #eee;
  text-shadow: 1px 1px 0 #fff; }

#loading {
  display: none;
  z-index: 9999;
  color: #fff;
  background: rgba(0, 0, 0, 0.3); }
  #loading:before, #loading:after {
    border-width: 3px;
    box-shadow: 0 0 3px 3px rgba(255, 255, 255, 0.3); }

#template {
  display: none; }

h1 {
  margin: 0;
  width: 100%;
  height: 60px;
  text-indent: -9999px;
  background: #fff url("../img/logo.png") no-repeat center;
  background-size: auto 72%;
  box-shadow: -2px 1px 2px rgba(0, 0, 0, 0.2); }

/* 左侧导航 */
#nav {
  overflow-x: hidden;
  overflow-y: auto;
  width: 180px;
  height: calc(100% - 60px); }
  #nav small {
    margin-left: 5px;
    font-size: 11px;
    opacity: .5;
    display: inline-block;
    font-family: tahoma, sans-serif;
    -webkit-transform: scale(0.916);
    -moz-transform: none;
    -ms-transform: none; }
  #nav i {
    display: inline-block;
    width: 20px;
    height: 20px;
    font-size: 20px;
    line-height: 20px;
    text-align: center;
    vertical-align: middle;
    margin-right: 10px; }
  #nav sup {
    display: none;
    top: 8px;
    left: 8px;
    position: absolute;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    text-indent: -9999px;
    background: #ff8300; }
  #nav sub {
    display: inline-block;
    position: absolute;
    top: 15px;
    right: 30px;
    border-radius: 4px;
    padding: 2px 5px;
    height: 16px;
    font-family: "Microsoft YaHei", tahoma, sans-serif;
    font-size: 12px;
    line-height: 12px; }
  #nav img {
    margin: 0 2px 0 10px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.6); }
  #nav .k-group i {
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px;
    margin-right: 6px; }
  #nav .k-group sub {
    position: relative;
    top: 0;
    left: 5px; }

#navPanelBar {
  width: 160px; }
  #navPanelBar,
  #navPanelBar .k-item,
  #navPanelBar .k-link,
  #navPanelBar .k-icon {
    border: none;
    white-space: nowrap;
    text-decoration: none;
    color: inherit;
    background: transparent;
    box-shadow: none; }
  #navPanelBar .k-link {
    padding: 12px 0 12px 12px;
    cursor: pointer; }
  #navPanelBar .k-group {
    border: none;
    white-space: nowrap;
    text-decoration: none;
    color: inherit;
    background: transparent;
    box-shadow: none;
    background-color: rgba(255, 255, 255, 0.2); }
    #navPanelBar .k-group .k-link {
      padding: 8px 0 8px 30px;
      font-size: 12px; }
  #navPanelBar .k-state-hover {
    background-color: rgba(255, 255, 255, 0.2); }
  #navPanelBar .k-state-active > .k-link,
  #navPanelBar .k-state-selected {
    background-color: rgba(0, 0, 0, 0.08); }
  #navPanelBar .hr {
    display: none; }

/* 左侧导航动效 */
#navCkb {
  position: absolute;
  z-index: -1;
  opacity: 0; }
  #navCkb:checked ~ label #mask {
    z-index: 9998;
    opacity: 1; }
  #navCkb:checked ~ #aside {
    left: 0; }
  #navCkb:checked ~ #main #header .fa-bars {
    margin-left: 175px;
    transform: rotate(-90deg); }

#mask {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  width: 100%;
  height: calc(100% - 60px);
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: all .3s; }

/* 顶部菜单 */
#header label {
  display: none;
  float: left;
  margin-bottom: 0; }
  #header label i {
    margin: 15px;
    font-size: 30px;
    cursor: pointer;
    transition: all .3s; }
#header h1 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 160px;
  box-shadow: none; }

#path {
  float: left;
  margin-left: 175px;
  height: 60px;
  line-height: 60px;
  color: #000; }
  #path a {
    text-decoration: none;
    color: #000; }
  #path i {
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px;
    margin-right: 6px; }
  #path sup {
    display: inline-block;
    top: 16px;
    margin-left: -26px;
    position: absolute;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    text-indent: -9999px;
    background: #ff8300; }
  #path small {
    margin-left: 5px;
    font-size: 11px;
    opacity: .5; }
  #path img {
    margin: 0 2px 0 10px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.6); }
  #path sub,
  #path .k-i-arrow-60-up,
  #path .k-i-arrow-60-down {
    display: none; }
  #path .fa-angle-double-right {
    margin: 0 10px;
    color: #ccc; }

#menuH {
  float: right;
  border: none;
  white-space: nowrap;
  text-decoration: none;
  color: inherit;
  background: transparent;
  box-shadow: none; }
  #menuH .k-item,
  #menuH .k-link {
    border: none;
    white-space: nowrap;
    text-decoration: none;
    color: inherit;
    background: transparent;
    box-shadow: none;
    color: #000; }
  #menuH hr {
    border-top: none;
    border-left: 1px solid #ccc;
    width: 0;
    height: 20px; }
  #menuH sup {
    display: inline-block;
    position: absolute;
    top: 6px;
    left: 0;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    transform: scale(0.8); }
  #menuH sub {
    display: none; }
  #menuH small {
    margin-left: 5px;
    font-size: 11px;
    opacity: .5; }
  #menuH i {
    display: inline-block;
    width: 20px;
    height: 20px;
    font-size: 20px;
    line-height: 20px;
    text-align: center;
    vertical-align: middle;
    margin-right: 6px; }
  #menuH .k-group i {
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px; }
  #menuH .k-group i.color {
    margin: 0;
    border: 1px solid #ccc;
    width: 22px;
    height: 22px; }
    #menuH .k-group i.color:first-child {
      border-right: none;
      border-radius: 4px 0 0 4px; }
    #menuH .k-group i.color:last-child {
      margin-right: 6px;
      border-left: none;
      border-radius: 0 4px 4px 0; }
  #menuH .k-group i.flag-icon {
    border: 1px solid #ccc;
    width: 22px;
    height: 17px; }
  #menuH .k-group .k-link {
    border-left: 3px solid transparent;
    padding: 8px 30px 8px 20px;
    font-size: 12px; }
  #menuH .k-group .k-state-hover > .k-link,
  #menuH .k-group .k-state-active {
    background-color: rgba(0, 0, 0, 0.08);
    border-color: #ccc; }
  #menuH > .k-item > .k-link {
    border-top: 3px solid transparent;
    padding: 7px 10px 10px 10px;
    height: 60px;
    line-height: 60px; }
    #menuH > .k-item > .k-link img {
      margin: 0 2px 0 10px;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.6); }
  #menuH > .k-item > span.k-link {
    border-color: transparent !important;
    cursor: default; }
  #menuH > .k-item > span.k-state-active {
    border-color: #ccc !important;
    cursor: pointer; }
  #menuH > .k-item > .k-animation-container {
    left: auto !important;
    right: 0; }
    #menuH > .k-item > .k-animation-container > .k-group {
      overflow: visible !important; }
      #menuH > .k-item > .k-animation-container > .k-group:before {
        position: absolute;
        border-style: solid;
        border-color: transparent;
        width: 0;
        height: 0;
        content: "";
        top: -16px;
        right: 47px;
        z-index: 999;
        border-width: 8px;
        border-bottom-color: #f9f9f9; }
      #menuH > .k-item > .k-animation-container > .k-group:after {
        position: absolute;
        border-style: solid;
        border-color: transparent;
        width: 0;
        height: 0;
        content: "";
        top: -20px;
        right: 45px;
        z-index: 888;
        border-width: 10px;
        border-bottom-color: #ccc; }
  #menuH .k-state-hover > .k-link,
  #menuH .k-item > .k-state-active {
    border-color: #ccc !important; }

/* 登录 */
#login main {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-align: center;
      align-items: center;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  color: #fff;
  opacity: .6; }
  #login main form {
    padding: 15px;
    width: 100%;
    max-width: 420px; }
    #login main form #avatar {
      border-radius: 50%;
      width: 128px;
      height: 128px;
      background: #fff;
      box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.6);
      transition: all 1s; }
    #login main form .user-avatar {
      transform: rotateY(360deg); }
    #login main form h3 {
      margin: 20px 0;
      font-size: 24px;
      font-weight: 100; }
      #login main form h3 small {
        display: block;
        margin-top: 10px;
        font-size: 14px;
        line-height: 20px; }
    #login main form .form-group {
      text-align: left; }
    #login main form i {
      margin-right: 8px;
      width: 16px;
      height: 16px;
      font-size: 16px; }
    #login main form .form-control:focus {
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075), 0 0 5px rgba(128, 189, 255, 0.5); }
    #login main form .k-tooltip-validation {
      display: block;
      margin: 0;
      border: 1px solid #faa685;
      border-radius: 0;
      padding: 5px 10px;
      width: 100%;
      color: #a64515;
      background-color: #fddacc; }
    #login main form .verify-bar-area {
      position: relative;
      border: 1px solid #ced4da;
      border-radius: 4px;
      text-align: center;
      color: #495057;
      background: #fff; }
      #login main form .verify-bar-area .verify-left-bar {
        position: absolute;
        top: -1px;
        left: -1px;
        border-radius: 4px 0 0 4px; }
      #login main form .verify-bar-area .verify-move-block {
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 4px;
        color: #fff;
        background: #007bff;
        cursor: pointer; }
        #login main form .verify-bar-area .verify-move-block:hover {
          background: #0069d9; }
        #login main form .verify-bar-area .verify-move-block .verify-icon {
          margin-right: 0; }
#login footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  font-size: 12px;
  line-height: 3em;
  text-align: center;
  color: rgba(255, 255, 255, 0.6); }

/* 主要内容 */
.box {
  margin-bottom: 15px;
  border: 1px solid #ddd;
  padding: 15px;
  background: #fff; }

#goTop {
  display: none;
  position: fixed;
  right: 32px;
  bottom: 12px;
  z-index: 999;
  padding: 0;
  width: 30px;
  height: 30px;
  font-size: 24px;
  line-height: 24px;
  opacity: .6; }

/* 手机端适配 */
@media only screen and (max-width: 767px) {
  #header label {
    display: block; }
  #header h1 {
    width: 100%; }

  #navCkb:checked ~ #main #header h1,
  #menuH,
  #path span {
    display: none; }

  #path {
    margin-left: 0; } }
/* Kendo UI 细节完善 ------------------------------ */
/* 通知框 */
.k-notification-wrap {
  font-size: 14px; }

/* 对话框 */
.k-dialog .dialog-box,
.k-window .dialog-box {
  padding: 40px; }
  .k-dialog .dialog-box i.fas,
  .k-window .dialog-box i.fas {
    margin-right: 10px;
    font-size: 30px;
    vertical-align: middle; }
  .k-dialog .dialog-box i.fa-check-circle,
  .k-window .dialog-box i.fa-check-circle {
    color: #bede0b; }
  .k-dialog .dialog-box i.fa-info-circle,
  .k-dialog .dialog-box i.fa-question-circle,
  .k-window .dialog-box i.fa-info-circle,
  .k-window .dialog-box i.fa-question-circle {
    color: #84cef8; }
  .k-dialog .dialog-box i.fa-exclamation-circle,
  .k-window .dialog-box i.fa-exclamation-circle {
    color: #f7d82a; }
  .k-dialog .dialog-box i.fa-times-circle,
  .k-window .dialog-box i.fa-times-circle {
    color: #faa685; }
.k-dialog .k-dialog-buttongroup .k-button:first-child:last-child,
.k-window .k-dialog-buttongroup .k-button:first-child:last-child {
  border-radius: 0; }
.k-dialog .k-window-buttongroup,
.k-window .k-window-buttongroup {
  margin-bottom: -20px;
  padding-top: 24px;
  text-align: center; }
  .k-dialog .k-window-buttongroup .k-button,
  .k-window .k-window-buttongroup .k-button {
    margin: 0 10px; }

/* 模态框 */
.k-window-iframecontent {
  height: 100%; }

/* 按钮 */
.k-button-lg {
  padding: 8px 30px; }

/*# sourceMappingURL=amikoko.site.css.map */
