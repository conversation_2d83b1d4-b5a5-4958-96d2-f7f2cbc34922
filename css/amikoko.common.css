.k-edge .k-grid-header .k-filterable, .k-edge .k-grid-header .k-with-icon, .k-grid-header .k-filterable, .k-grid-header .k-with-icon, .k-ie .k-grid-header .k-filterable, .k-ie .k-grid-header .k-with-icon {
    /* padding-right: calc(calc(1*(2px + 8px + 1.42857em)) + 4px); */
    padding-right: 0;
}
 /*global*/
 .k-window-titlebar{
    padding: 8px 20px;
    font-weight:600;
}

.k-window-content{
    padding:6px;
} 


.floatWrap:after,.indexWindow:after{content:"";display:block;clear:both}
.floatWrap,.indexWindow{display:inline-block}
.floatWrap,.indexWindow{display:block}
.clear{clear:both}

body,h1,h2,h3,h4,p,ul,li,a,button
{
    margin:0;
    padding:0;
    list-style:none;
}

html
{
    top: 0;
    left: 0;
    overflow-y:scroll;
    font:75% Arial, Helvetica, sans-serif;
    background: #f5f7f8;
}
body
{
    margin: 0;
    padding: 0;
}

a,
li>a,
h2>a,
h3>a,
a
{
    text-decoration:none;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}

a
{
    color: #0487c4;
}

a:hover
{
    text-decoration: underline;
}

.page
{
    max-width:72%;
    margin: 2% auto;
    padding: 3% 5% 0;
    background: #fff;
    border: 1px solid #e2e4e7;
}

.offline-button {
    display: inline-block;
    margin: 0 0 30px;
    padding: 9px 23px;
    background-color: #015991;
    border-radius: 2px;
    color: #fff;
    text-decoration: none;
    font-size: 13px;
    font-weight: 700;
    line-height: 1.2;
    transition-duration: 0.2s;
    transition-property: background-color;
    transition-timing-function: ease;
}

.offline-button:hover {
    background-color: #013a5e;
    color: #fff;
    text-decoration: none;
}

.indexWindow
{
    margin: -2px 0 -5px;
    padding: 0;
    border: 0;
    background: transparent;
    font-size: 14px;
    height: 99%;
    /* position: relative; */
    /* position: fixed; */
}

.k-tabstrip-wrapper{
    width: 100%;
}

/*console*/

.console
{
    background-color: transparent;
    color: #333;
    font: 11px Consolas, Monaco, "Bitstream Vera Sans Mono", "Courier New", Courier, monospace;
    margin: 0;
    overflow-x: hidden;
    text-align: left;
    height: 200px;
    border: 1px solid rgba(20,53,80,0.1);
    background-color: #ffffff;
    text-indent: 0;
}

.indexSection .box-col .console
{
    min-width: 200px;
}

.console .count
{
    background-color: #26c6da;
    -moz-border-radius: 15px;
    -webkit-border-radius: 15px;
    border-radius: 15px;
    color: #ffffff;
    font-size: 10px;
    margin-left: 5px;
    padding: 2px 6px 2px 5px;
}

.console div
{
    background-position: 6px -95px;
    border-bottom: 1px solid #DDD;
    padding: 5px 10px;
    height: 2em;
    line-height: 2em;
    vertical-align: middle;
}

.console .error
{
    background-position: 6px -135px;
}

/*configurator*/

.centerWrap .configuration,
.configuration,
.configuration-horizontal
{
    margin: 4.5em auto;
    padding: 3em;
    background-color: rgba(20,53,80,0.038);
    border: 1px solid rgba(20,53,80,0.05);
}

.absConf .configuration
{
    position: absolute;
    top: -1px;
    right: -1px;
    height: auto;
    margin: 0;
    z-index: 2;
}

.configuration-horizontal
{
    position: static;
    height: auto;
    min-height: 0;
    margin: 0 auto;
    zoom: 1;
}

.configuration-horizontal-bottom
{
    margin: 20px -21px -21px;
    position: static;
    height: auto;
    min-height: 0;
    width: auto;
    float:none;
}

.configuration .configHead,
.configuration .infoHead,
.configuration-horizontal .configHead,
.configuration-horizontal .infoHead
{
    display: block;
    margin-bottom: 1em;
    font-size: 12px;
    line-height: 1em;
    font-weight: bold;
    text-transform: uppercase;
}


.configuration .configTitle,
.configuration-horizontal .configTitle
{
    font-size: 12px;
    display: block;
    line-height: 22px;
}

.configuration .options,
.configuration-horizontal .options
{
    list-style:none;
    margin: 0;
    padding: 0;
}

.configuration button,
.configuration-horizontal button
{
    margin: 0;
    vertical-align: middle;
}

.configuration .k-textbox,
.configuration-horizontal .k-textbox
{
    margin-left: 7px;
    width: 30px;
}

.configuration .options li { display: block; margin: 0; padding: 0.2em 0; zoom: 1; }

.configuration .options li:after,
.configuration-horizontal:after
{
    content: "";
    display: block;
    clear: both;
    height: 0;
}

.configuration-horizontal .config-section
{
    display: block;
    float: left;
    min-width: 200px;
    margin: 0;
    padding: 10px 20px 10px 10px;
}

.configuration label,
.configuration input
{
    vertical-align: middle;
    line-height: 20px;
    margin-top: 0;
}

.configuration label
{
    float: left;
}

.configuration input
{
    width: 40px;
}

.configuration input,
.configuration select,
.configuration .k-numerictextbox
{
    float: right;
}

.configuration input.k-input
{
    float: none;
}

.configuration .k-button,
.configuration .k-widget
{
    margin-bottom: 3px;
}

/* Code Viewer */
.source {
    background-color: #f5f7f8;
    margin: 0 0 5em;
    border: 1px solid rgba(20,53,80,0.05);
}
.source .code {
    background-color: #fff;
    border-top: 1px solid rgba(20,53,80,0.08);
    padding: 20px 0 0;
}
.source .code pre {
    margin: 0;
    padding: 0 20px 20px;
}
.source .offline-button {
    background: none;
    text-decoration: none;
    color: #0487c4;
    margin: 10px 0 10px 14px;
    padding: 10px;
}

.source .offline-button.selected {
    color: #000;
}

.source .code .controller {
    display: none;
}

/* Pretty Print */
.prettyprint
{
    font-size: 12px;
    overflow: auto;
}

pre .nocode { background-color: transparent; color: #000; }
pre .str,                    /* string */
pre .atv { color: #2db245; } /* attribute value */
pre .kwd { color: #ff3399; } /* keyword */
pre .com { color: #9933cc; } /* comment */
pre .typ { color: #000; } /* type */
pre .lit { color: #0099ff; } /* literal */
pre .pun { color: #333; }    /* punctuation */
pre .pln { color: #3e526b; }    /* plaintext */
pre .tag { color: #3e526b; } /* html/xml tag */
pre .atn { color: #3e526b; } /* attribute name */
pre .dec { color: #3e526b; } /* decimal */

/* Specify class=linenums on a pre to get line numbering */
ol.linenums { margin-top: 0; margin-bottom: 0; color: #333; }
li.L0,li.L1,li.L2,li.L3,li.L5,li.L6,li.L7,li.L8 { list-style-type: none }
li.L1,li.L3,li.L5,li.L7,li.L9 { background: #eee; }

/*keyboard navigation legend */
.key-button {
    display: inline-block;
    text-decoration: none;
    color: #555;
    min-width: 20px;
    margin: 0;
    padding: 3px 5px;
    font-size: 12px;
    text-align: center;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    background: #eee;
    box-shadow: 0 1px 0 1px rgba(0,0,0,0.1), 0 2px 0 rgba(0,0,0,0.1);
}
.widest {}
.wider {}
.wide {}
.leftAlign, .rightAlign, .centerAlign {text-align: left;}

.letter {
    padding-top: 14px;
    padding-bottom: 11px;
    font-weight: bold;
    font-size: 17px;
}

ul.keyboard-legend {
    list-style-type: none;
    margin: 0 auto;
    padding: 0;
    text-align: left;
}

.indexWindow ul.keyboard-legend li,
.indexSection .box-col ul.keyboard-legend li {
    display: block;
    margin: 0;
    padding: 4px 0;
    line-height: 1.5em;
}

ul.keyboard-legend li a {
    color: #0487C4;
}


.button-preview {
    display: inline-block;
    vertical-align: top;
    padding: 0 5px 0 0;
}
.button-descr {
    display: inline-block;
    vertical-align: top;
    text-align: left;
    padding: 3px 0;
}

.indexSection p a.hyperlink,
.config-section a {
    color: #e15613;
    text-decoration: none;
}

.chart-wrapper,
.map-wrapper,
.diagram-wrapper {
    position: relative;
    height: 430px;
    margin: 0 auto 15px auto;
    padding: 10px;
}

.indexWindow.absConf .chart-wrapper,
.indexWindow.absConf .map-wrapper,
.indexWindow.absConf .diagram-wrapper
{
    margin-left: 0;
}

.chart-wrapper .k-chart,
.map-wrapper .k-map,
.diagram-wrapper .k-diagram {
    height: 430px;
}

.config-section.console-section
{
    width: 400px;
    float: right;
}

#page > h2 {
    float: none;
    text-align: center;
    width: auto;
    padding: 5em 0 1em;
    font-size: 3em;
}

#suites .imgPlate,
#suites .box {
    border-width: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

#suites {
    text-align: center;
}

#suites .box {
    float: none;
    clear: none;
    display: inline-block;
    width: auto;
    min-width: auto;
}

#suites .box h2 {
    margin-top: 1em;
}


/* Box Styles */

.box {
    margin: 4.5em 7.5em;
    padding: 3em;
    background-color: rgba(20,53,80,0.038);
    border: 1px solid rgba(20,53,80,0.05);
}

.indexSection {
    margin: 0 auto 4.5em;
    padding: 2px;
    border: 1px solid rgba(20,53,80,0.14);
}

.indexSection:not(.wide),
.indexWindow .box:not(.wide) {
    width: 99.8%;
}

.box:after,
.indexSection:after {
    content: "";
    display: block;
    clear: both;
    height: 0;
}

.indexWindow .box {
    margin: 4.5em auto;
}

.indexWindow .box:first-child {
    margin-top: 0;
}

.indexSection.k-content {
    box-shadow: 0 1px 2px 1px rgba(0,0,0,.08), 0 3px 6px rgba(0,0,0,.08);
}

.k-content{
   height:100%;
}


#tab > .k-content, #hint > .k-content{
  overflow:hidden;
}
.k-tabstrip > .k-content{
  overflow:hidden;
}

.k-tabstrip-items .k-first{
    margin-left: 20px;
}
.k-tabstrip-items .k-item{
    padding-left: 8px;
}


.box h4,
.indexSection h4 {
    margin-bottom: 1em;
    font-size: 12px;
    line-height: 1em;
    font-weight: bold;
    text-transform: uppercase;
}
.box-col {
    display: block;
    float: left;
    padding: 0 3em 1.667em 0;
}

.box ul:not(.km-widget) li,
.indexSection .box-col ul:not(.km-widget) li {
    line-height: 3em;
}

.box li:last-child {
    margin-bottom: 0;
}

.box li a {
    font-size: 13px;
}

.box .k-widget {
    background-color: #ebeef0;
    border-color: #ccc;
    color: #7c7c7c;
}
.box .k-widget.k-slider {
    background-color: transparent;
}

.box .k-button {
    cursor: pointer;
    border-radius: 2px;
    font-size: inherit;
    color: #333;
    background: #e2e4e7;
    border-color: #e2e4e7;
    min-width: 90px;
    box-shadow: none;
}

.box .k-upload-status .k-button-bare {
    min-width: 0;
}

.box .k-button:hover,
.box .k-button:focus:active:not(.k-state-disabled):not([disabled]),
.box .k-button:focus:not(.k-state-disabled):not([disabled]) {
    background: #cad0d6;
    border-color: #cad0d6;
    color: #000;
    box-shadow: none;
}

.box .k-primary {
    color: #fff;
    background: #015991;
    border-color: #015991;
}

.box .k-primary:hover,
.box .k-primary:focus:active:not(.k-state-disabled):not([disabled]),
.box .k-primary:focus:not(.k-state-disabled):not([disabled]) {
    background: #013A5E;
    border-color: #013A5E;
    color: #fff;
}

.box .k-textbox,
.box textarea {
    background: #fff;
    border-color: #e2e4e7;
    color: #555;
    border-radius: 2px;
}

.box .k-textbox:hover,
.box .k-textbox:active,
.box .k-textbox:focus,
.box textarea:hover,
.box textarea:active,
.box textarea:focus {
    border-color: #cad0d6;
    background: #fff;
    color: #333;
    box-shadow: none;
}

.box.demo-description p {
    line-height: 1.5em;
    max-width: 1000px;
    padding-bottom: 1em;
}

.box.demo-description p:last-child {
    padding-bottom: 0;
}

.box.demo-description ul,
.box.demo-description ul li {
    list-style: disc inside;
    line-height: 1.5em;
    max-width: 1000px;
}

.box.demo-description ol,
.box.demo-description ol li {
    list-style: decimal inside;
    line-height: 1.5em;
    max-width: 1000px;
}

.box.demo-description ul,
.box.demo-description ol {
    margin: 1em;
    padding: 0;
}

.demo-hint {
    line-height: 22px;
    color: #aaa;
    font-style: italic;
    font-size: .9em;
    padding-top: 1em;
}

.responsive-message {
    font-size: 17px;
    display: none;
    margin: 4em auto;
    padding: 2.5em;
    text-align: center;
    background-color: #ffda3f;
    color: #000;
}

.responsive-message:before {
    content: "This demo requires browser or device screen width to be equal or greater than 1024px.";
}

@media screen and (max-width: 1023px) {
  .page {
    max-width:100%;
    margin: 0;
    padding: 10px;
    background: #fff;
    border: 0;
  }
  
  .hidden-on-narrow {
    display: none !important;
  }
  .responsive-message {
    display: block;
  }
}

    #tabstrip h2 {
        font-weight: lighter;
        font-size: 5em;
        line-height: 1;
        padding: 0 0 0 30px;
        margin: 0;
    }

    #tabstrip h2 span {
        background: none;
        padding-left: 5px;
        font-size: .3em;
        vertical-align: top;
    }

    #tabstrip p {
        margin: 0;
        padding: 0;
    }

    
.k-dateinput, .k-datepicker, .k-datetimepicker, .k-dropdown, .k-autocomplete{
    width:100%;
}

.k-invalid-msg{
float: left;
padding-top: 2px;
padding-bottom: 4px;
margin-left: 120px;
margin-top: -57px;
}

.col-sm-3 .k-invalid-msg{
    margin-left: 150px;
}
.col-sm-4 .k-invalid-msg{
    margin-left: 180px;
}

.col-sm-5 .k-invalid-msg{
    margin-left: 200px;
}

.col-sm-6 .k-invalid-msg{
    margin-left: 210px;
}

.k-tooltip{font-size: 12px;}
    

.mt-2, .my-2 {
/* margin-top: .5rem !important; */
margin-top:none;
}

.k-callout-2 {
    left: 50%;
    margin-left: -6px;
    border-top-color: none;
    border-bottom-color:none;
    border-left-color: currentColor;
    bottom: -5px;
    pointer-events: none;
}
.d-block{font-size: 14px;color:#000 }

.k-input.k-textbox, .k-textbox {
    width: 100%;
}


.col,.col-1,.col-10,.col-11,.col-12,.col-2,.col-3,.col-4,.col-5,.col-6,.col-7,.col-8,.col-9,.col-auto,.col-lg,.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-lg-auto,.col-md,.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-md-auto,.col-sm,.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-sm-auto,.col-xl,.col-xl-1,.col-xl-10,.col-xl-11,.col-xl-12,.col-xl-2,.col-xl-3,.col-xl-4,.col-xl-5,.col-xl-6,.col-xl-7,.col-xl-8,.col-xl-9,.col-xl-auto{position:relative;width:100%;padding-right:8px;padding-left:10px}



.k-invalid-msg{
    position: absolute;
    top: 60px;
}
.k-dropdown .k-invalid-msg{
    margin-top: -87px;
}
.k-picker-wrap .k-invalid-msg{
    margin-top: -87px;
}
.k-space-right .k-invalid-msg{
    margin-top: -87px;
}
.k-autocomplete .k-invalid-msg{
    margin-top: -87px;
}
.k-multiselect .k-invalid-msg{
    margin-top: -87px;
}
.k-textarea{
    width: 100%;
}

.k-grid-header .k-header .k-link{
    color:#000;
}
.k-grid .k-grid-toolbar .k-button{
    color:#000;
}

.k-textbox{
    color:#000;font-weight:bold;
}
.k-dropdown-wrap{
    color:#000;font-weight:bold;
}
.k-autocomplete{
    color:#000;font-weight:bold;
}

.k-grid td[role=gridcell]{
    color:#000;
    line-height: 0px;
    padding: 4px 6px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.k-grid .k-grid-search {
    /* width: 15.7em; */
    width: 280px;
    height: 26px;
}
.k-grid .k-grid-search.k-display-flex {
    display: none !important;
}

.k-grid .k-grid-toolbar{
    padding: 2px;
    height: 30px;
}
.k-grid .k-grid-toolbar .k-button{
    padding: 2px 6px 2px 8px;
}
.k-grid .k-grid-header th{
    padding: 4px;
}
.k-grid .k-grid-header .k-header-column-menu{
    top: 2px;
    width: 18px;
    height: 26px;
}
.k-grid .k-grid-pager{
    padding: 2px;
}
.k-grid .k-grid-pager .k-dropdown-wrap{
    height: 26px;
    width: 350px;
}
.k-grid .k-grid-pager .k-state-selected{
    height: 26px;
}
.k-grid .k-grid-pager .k-textbox{
    height: 26px;
}
.k-grid .k-grid-pager .k-link{
    height: 26px;
}
.k-grid .k-grid-pager .k-pager-refresh{
    order: 9;
}
.k-grid .k-grid-pager .k-pager-info{
    justify-content: unset;
}

.indexWindow .k-tabstrip-items .k-item{
    height: 26px;
    font-size: 14px;
    font-weight: 600;
    color: #999;
    border-radius: 0px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding:2px 0;
}

.k-listbox{
    height: 400px;
    width:100%;
}

/* 分割线 */
.line-hr{
    height: 1px;
    border-top: 3px solid ;
    text-align: left;
}
.line-hr span{
    position: relative;
    top: -13px;
    background: #fff;
    padding: 0 8px;
    margin-left:60px; 
    font-size: 16px;
    font-weight: 600;
    color: #000;
}




.k-grid[role=reportTable] td[role=gridcell]{
    line-height:20px;
    white-space:none;
}
.k-grid .k-detail-row .k-detail-cell {
    padding: 0;
}

/* div[data-role=grid] .k-grid-toolbar{
    overflow-x:auto;
} */
.k-grid-toolbar .k-button{
    margin-left: 10px;
}



/**
设置面板
*/
.k-grid-settings{ position:relative;cursor: pointer;}
.k-grid-settings .settings-panel{
    position:absolute; 
    width:356px;
    /* height: 500px; */
    min-height: 300px;
    margin-top:-24px;
    display:none;
    z-index:1000;
    margin-left: -330px;
    background:#f6f6f6;
    /* background:rgba(255, 255, 255, 0.8) none repeat scroll 0 0 !important; */
    border:1px solid;
    border-top-left-radius: 5px;
}
.settings-panel .k-grid-search{
    width: 320px;
    margin: 8px 20px;
    font-family: "Microsoft YaHei",arial,simsun,sans-serif;
    display: none !important;
}
.settings-panel .k-grid-search.k-display-flex {
    display: flex !important;
}
.k-grid-settings .settings-panel .card-body{
    font-family: none;
}


.k-font-bold{
    font-weight: 600;
}
.k-font-bold-800{
    font-weight: 800;
}
.k-font-underline{
    text-decoration:underline;
}
.k-font-italic{
    font-style:italic;
}

/**
表格按钮下拉
*/
a.grid-button-list { position:relative;cursor: pointer;}
a.grid-button-list .xl{ position:absolute; display:none;z-index:1000;top:22px;left:0}
a.grid-button-list .xlul li{ padding-top:3px; padding-bottom:2px; text-align: left;}
/* a.grid-button-list .xlul li a{}
a.grid-button-list .xlul a:hover{}  */

span.k-i-select-box { position:relative;cursor: pointer;}
span.k-i-select-box .xl{ position:absolute; display:none;z-index:1000;top:22px;left:0}
span.k-i-select-box .xlul li{ padding-top:3px; padding-bottom:2px; text-align: left;}



form .k-radio-label, form .k-checkbox-label {
    margin: 0 0 0 0;
}

.hk-file .k-invalid-msg{
    position: absolute;
    top: 30px;
}

.k-tool-group .k-combobox{
    width: 120px;
}

.k-tool-group .k-textbox{
    width: 120px;
}

span.k-i-blur{
    font-size: 24px;
}

#tab > .k-content, #hint > .k-content {
    margin-right: -17px;
    border: none;
    padding: 0;
    height: 100% !important;
    background: none;
    /* overflow-x: hidden;
    overflow-y: auto; */
}

#tab > .k-tabstrip-items small, #hint > .k-tabstrip-items small {
    margin-left: 5px;
    font-size: 14px;
    opacity: 0.8;
}

#section {
    padding: 60px 0 0 2px;
    width: 100%;
    height: calc(100% - 2px);
    background: #eee;
}
#container {
    /* position: relative; */
    min-height: calc(100% + 0px);
    /* height: 99%; */
    /* position: absolute; */
    width: 100%;
}
#footer {
    padding-top: 12px;
    height: 2px;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    color: #999;
    background: #eee;
    text-shadow: 1px 1px 0 #fff;
    position: fixed;
    bottom: 0;
    width: 100%;
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: .25rem;
    margin-bottom: 50px;
    padding-bottom: 50px;
}

#container > .k-tabstrip-wrapper {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}


.pdf-print{height:3000px}



.container-fullh-100{
    height: calc(100vh - 80px);
}
.container-fullh-95{
    height: calc(95vh - 76px);
}
.container-fullh-90{
    height: calc(90vh - 72px);
}
.container-fullh-85{
    height: calc(85vh - 68px);
}
.container-fullh-80{
    height: calc(80vh - 64px);
}
.container-fullh-75{
    height: calc(75vh - 60px);
}
.container-fullh-70{
    height: calc(70vh - 56px);
}
.container-fullh-65{
    height: calc(65vh - 52px);
}
.container-fullh-60{
    height: calc(60vh - 48px);
}
.container-fullh-55{
    height: calc(55vh - 44px);
}
.container-fullh-50{
    height: calc(50vh - 40px);
}
.container-fullh-45{
    height: calc(45vh - 36px);
}
.container-fullh-40{
    height: calc(40vh - 32px);
}
.container-fullh-35{
    height: calc(35vh - 28px);
}
.container-fullh-30{
    height: calc(30vh - 24px);
}
.container-fullh-25{
    height: calc(25vh - 20px);
}
.container-fullh-20{
    height: calc(20vh - 16px);
}
.container-fullh-15{
    height: calc(15vh - 12px);
}
.container-fullh-10{
    height: calc(10vh - 8px);
}
.container-fullh-5{
    height: calc(5vh - 4px);
}


.container-fullhs-1{
    height: calc(7vh - 7px);
}

.container-fullhs-2{
    height: calc(14vh - 14px);
}

.container-fullhs-3{
    height: calc(21vh - 21px);
}

.container-fullhs-4{
    height: calc(28vh - 28px);
}

.container-fullhs-5{
    height: calc(35vh - 35px);
}

.container-fullhs-6{
    height: calc(42vh - 42px);
}

.container-fullhs-7{
    height: calc(49vh - 49px);
}

.container-fullhs-8{
    height: calc(56vh - 56px);
}

.container-fullhs-9{
    height: calc(63vh - 63px);
}

.container-fullhs-10{
    height: calc(70vh - 70px);
}

.container-fullhs-11{
    height: calc(77vh - 77px);
}

.container-fullhs-12{
    height: calc(84vh - 84px);
}

.container-fullhs-13{
    height: calc(91vh - 91px);
}

.container-fullhs-14{
    height: calc(98vh - 98px);
}

.container-fullhs-15{
    height: calc(105vh - 105px);
}



/**
* 遮罩层
*/
.k-loading-template{
    display: none;
    position: fixed;
    z-index: 99999;
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    font-size: 2.5em;
    margin-top: 60px;
    align-content: center;
}
.k-loading-template::after, .k-loading-template::before {
    border-width: 3px;
    box-shadow: 0 0 3px 3px rgba(255, 255, 255, 0.3);
}
.k-loading-template p{
    line-height: 30px;
    font-size: 26px;
    text-align: center;
}




.closeButton {
    margin-right: 10px;
    background: none;
    background: url(../img/icon/closeButton.png) repeat;
    border: 0;
    width: 65px;
    height: 28px;
    color: #FFF;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    font-family: "寰蒋闆呴粦";
}