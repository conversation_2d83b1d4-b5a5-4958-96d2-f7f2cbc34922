.k-button[disabled],
[disabled] .k-button {
  outline: none;
  cursor: default;
  opacity: 0.6;
  filter: grayscale(0.1);
  pointer-events: none;
  box-shadow: none; }

.k-widget, .k-block,
.k-panel {
  color: #000000;
  background-color: #ffffff;
  border-color: #c2c2c2; }

.k-content, .k-window, .k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper, .k-editor, .k-scheduler-agendaview tr.k-state-hover .k-scheduler-datecolumn, .k-notification {
  color: #000000;
  background-color: #ffffff;
  border-color: #c2c2c2; }

.k-popup, .k-menu-group,
.k-menu.k-context-menu {
  color: #000000;
  background-color: #f8f8f8;
  border-color: #c2c2c2; }

.k-spreadsheet-top-corner, .k-spreadsheet-row-header,
.k-spreadsheet-column-header, .k-calendar .k-header, .k-calendar .k-content thead, .k-calendar .k-calendar-view thead, .k-time-list-wrapper .k-title, .editorToolbarWindow.k-header.k-window-content, .k-treemap .k-treemap-title, .k-gantt-toolbar, .k-scheduler-toolbar,
.k-scheduler-footer, .k-block > .k-header,
.k-panel > .k-header {
  color: #000000;
  background-color: #f5f5f5;
  border-color: #c2c2c2; }

.k-calendar .k-nav-prev:hover, .k-calendar .k-nav-prev.k-state-hover,
.k-calendar .k-nav-next:hover,
.k-calendar .k-nav-next.k-state-hover,
.k-calendar .k-nav-fast:hover,
.k-calendar .k-nav-fast.k-state-hover {
  color: #ffffff;
  background-color: #90a4ae;
  border-color: #607d8b; }

.k-calendar .k-nav-prev:active,
.k-calendar .k-nav-next:active,
.k-calendar .k-nav-fast:active {
  color: #ffffff;
  background-color: #607d8b;
  border-color: #607d8b; }

.k-list-container .k-nodata .k-button, .k-dropdown .k-dropdown-wrap, .k-dropdowntree .k-dropdown-wrap, .k-button, .k-button-group.k-state-disabled .k-button:not(.k-state-active),
.k-button-group .k-state-disabled:not(.k-state-active), .k-toolbar .k-split-button .k-button, .k-spreadsheet-filter, .k-pivot-toolbar .k-button,
.k-fieldselector .k-list li.k-item, .k-colorpicker .k-picker-wrap, .k-combobox .k-select, .k-datepicker .k-select,
.k-timepicker .k-select,
.k-datetimepicker .k-select, .k-dateinput .k-select, .k-editor .k-editor-toolbar-wrap a.k-tool,
.k-ct-popup .k-editor-toolbar a.k-tool,
.editorToolbarWindow.k-header.k-window-content a.k-tool, .k-numerictextbox .k-select, .k-switch-handle, .k-handle, .k-gantt-views li, .k-scheduler-navigation .k-nav-today,
.k-scheduler-navigation .k-nav-prev,
.k-scheduler-navigation .k-nav-next, .k-scheduler-views li, .k-scheduler-footer li {
  color: #000000;
  background-color: #f5f5f5;
  border-color: #c2c2c2; }

.k-dropdown .k-dropdown-wrap:hover, .k-dropdowntree .k-dropdown-wrap:hover, .k-dropdown .k-dropdown-wrap.k-state-hover, .k-dropdowntree .k-dropdown-wrap.k-state-hover, .k-button:hover, .k-button.k-state-hover, .k-button-group > input[type="radio"]:hover + .k-button,
.k-button-group > input[type="checkbox"]:hover + .k-button, .k-action-buttons .k-button:hover, .k-action-buttons .k-button.k-state-hover, .k-toolbar .k-split-button .k-button:hover,
.k-toolbar .k-split-button .k-button.k-state-hover, .k-spreadsheet-filter:hover, .k-pivot-toolbar .k-button:hover, .k-pivot-toolbar .k-button.k-state-hover,
.k-fieldselector .k-list li.k-item:hover,
.k-fieldselector .k-list li.k-item.k-state-hover, .k-colorpicker .k-state-hover, .k-combobox > :hover .k-select,
.k-combobox .k-state-hover .k-select, .k-datepicker:hover .k-select,
.k-datepicker .k-state-hover .k-select,
.k-timepicker:hover .k-select,
.k-timepicker .k-state-hover .k-select,
.k-datetimepicker:hover .k-select,
.k-datetimepicker .k-state-hover .k-select, .k-dateinput .k-select:hover, .k-dateinput .k-select > .k-state-selected,
.k-dateinput .k-select > .k-state-active, .k-dateinput .k-state-hover .k-select, .k-editor .k-editor-toolbar-wrap a.k-tool:hover, .k-editor .k-editor-toolbar-wrap a.k-tool.k-state-hover,
.k-ct-popup .k-editor-toolbar a.k-tool:hover,
.k-ct-popup .k-editor-toolbar a.k-tool.k-state-hover,
.editorToolbarWindow.k-header.k-window-content a.k-tool:hover,
.editorToolbarWindow.k-header.k-window-content a.k-tool.k-state-hover, .k-numerictextbox .k-select:hover, .k-numerictextbox .k-select > .k-state-selected,
.k-numerictextbox .k-select > .k-state-active, .k-numerictextbox .k-state-hover .k-select, .k-switch-off:hover .k-switch-handle, .k-handle:hover, .k-gantt-views li.k-state-hover, .k-scheduler-navigation .k-nav-today.k-state-hover,
.k-scheduler-navigation .k-nav-prev.k-state-hover,
.k-scheduler-navigation .k-nav-next.k-state-hover, .k-scheduler-views li.k-state-hover, .k-scheduler-footer li.k-state-hover {
  color: #ffffff;
  background-color: #90a4ae;
  border-color: #607d8b; }

.k-button:active, .k-button.k-state-active, .k-split-button.k-button-group .k-button:active, .k-split-button.k-button-group .k-button.k-state-active, .k-action-buttons .k-button:active, .k-action-buttons .k-button.k-state-active, .k-toolbar .k-split-button .k-button:active,
.k-toolbar .k-split-button .k-button.k-state-active, .k-spreadsheet-filter.k-state-active, .k-colorpicker .k-state-active, .k-editor .k-editor-toolbar-wrap a.k-tool:active, .k-editor .k-editor-toolbar-wrap a.k-tool.k-state-active,
.k-ct-popup .k-editor-toolbar a.k-tool:active,
.k-ct-popup .k-editor-toolbar a.k-tool.k-state-active,
.editorToolbarWindow.k-header.k-window-content a.k-tool:active,
.editorToolbarWindow.k-header.k-window-content a.k-tool.k-state-active, .k-multiselect-wrap li.k-button:active, .k-multiselect-wrap li.k-button.k-state-active {
  color: #000000;
  background-color: #f5f5f5;
  border-color: #c2c2c2;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.06); }

.k-button.k-primary, .k-slider .k-draghandle, .k-switch-on .k-switch-handle {
  color: #ffffff;
  background-color: #607d8b;
  border-color: #607d8b; }

.k-button.k-primary:hover, .k-button.k-primary.k-state-hover, .k-action-buttons .k-primary:hover, .k-action-buttons .k-primary.k-state-hvoer, .k-slider .k-draghandle:hover, .k-switch-on:hover .k-switch-handle {
  color: #ffffff;
  background-color: #607b8b;
  border-color: #607b8b; }

.k-button.k-state-selected, .k-button.k-primary:active, .k-button.k-primary.k-state-active, .k-button.k-primary.k-state-selected, .k-button-group .k-button:active,
.k-button-group .k-button.k-state-active,
.k-button-group .k-button.k-state-selected,
.k-button-group > input[type="radio"]:checked + .k-button,
.k-button-group > input[type="checkbox"]:checked + .k-button, .k-action-buttons .k-primary:active, .k-action-buttons .k-primary.k-state-active, .k-slider .k-draghandle:active, .k-slider .k-draghandle.k-pressed {
  color: #ffffff;
  background-color: #607d8b;
  border-color: #607d8b; }

.k-toolbar, .k-editor-toolbar {
  color: #000000;
  background-color: #f5f5f5;
  border-color: #c2c2c2; }

.k-autocomplete, .k-combobox .k-dropdown-wrap, .k-datepicker .k-picker-wrap,
.k-timepicker .k-picker-wrap,
.k-datetimepicker .k-picker-wrap, .k-dateinput .k-dateinput-wrap, .k-textbox,
.k-input.k-textbox,
.k-textarea, .k-multiselect-wrap, .k-numerictextbox .k-numeric-wrap {
  color: #000000;
  background-color: #ffffff;
  border-color: #c2c2c2; }

.k-autocomplete.k-state-hover, .k-combobox > :hover,
.k-combobox .k-state-hover, .k-datepicker:hover,
.k-datepicker .k-state-hover,
.k-timepicker:hover,
.k-timepicker .k-state-hover,
.k-datetimepicker:hover,
.k-datetimepicker .k-state-hover, .k-dateinput .k-state-hover, .k-multiselect-wrap:hover, .k-multiselect-wrap.k-state-hover, .k-numerictextbox .k-state-hover {
  color: #000000;
  background-color: #ffffff;
  border-color: #607d8b; }

.k-dropdown .k-dropdown-wrap.k-state-focused, .k-dropdowntree .k-dropdown-wrap.k-state-focused, .k-autocomplete.k-state-focused, .k-autocomplete.k-state-active, .k-combobox > .k-state-focused,
.k-combobox .k-state-active, .k-datepicker .k-state-focused,
.k-datepicker .k-state-active,
.k-timepicker .k-state-focused,
.k-timepicker .k-state-active,
.k-datetimepicker .k-state-focused,
.k-datetimepicker .k-state-active, .k-dateinput .k-state-focused, .k-state-focused > .k-multiselect-wrap, .k-numerictextbox .k-state-focused {
  color: #000000;
  background-color: #ffffff;
  border-color: #607d8b;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.03), 0 4px 5px 0 rgba(0, 0, 0, 0.04); }

.k-panelbar > .k-item.k-state-expanded > .k-link,
.k-panelbar > .k-item.k-state-active > .k-link {
  color: #000000;
  background-color: #f5f5f5;
  border-color: #f5f5f5; }

.k-treeview .k-in.k-state-hover, .k-calendar .k-state-hover .k-link, .k-scheduler-agendaview tr.k-state-hover, .k-panelbar > .k-item.k-state-expanded > .k-link:hover, .k-panelbar > .k-item.k-state-expanded > .k-link.k-state-hover,
.k-panelbar > .k-item.k-state-active > .k-link:hover,
.k-panelbar > .k-item.k-state-active > .k-link.k-state-hover, .k-panelbar .k-group > .k-item > .k-link:hover,
.k-panelbar .k-group > .k-item > .k-link.k-state-hover {
  color: #ffffff;
  background-color: #90a4ae;
  border-color: #90a4ae; }

.k-drag-clue, .k-treeview .k-in.k-state-selected, .k-calendar .k-state-selected .k-link,
.k-calendar .k-weekend.k-state-selected .k-link, .k-calendar:not(.k-calendar-infinite) .k-content .k-state-selected .k-link,
.k-calendar:not(.k-calendar-infinite) .k-content .k-weekend .k-state-selected .k-link, .k-event, .k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-expanded),
.k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-active), .k-panelbar .k-group > .k-item > .k-link.k-state-selected {
  color: #ffffff;
  background-color: #607d8b;
  border-color: #607d8b; }

.k-list .k-item:focus,
.k-list .k-item.k-state-focused {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }

.k-list .k-item:hover,
.k-list .k-item.k-state-hover,
.k-list-optionlabel:hover, .k-list-container .k-button:hover,
.k-list-container .k-button.k-state-hover, .k-column-list-item:hover,
.k-columnmenu-item:hover, .k-spreadsheet-filter-menu > .k-menu .k-item.k-state-hover, .k-spreadsheet-popup .k-button:hover,
.k-spreadsheet-popup .k-button.k-state-hover, .k-menu-group .k-item:hover, .k-menu-group .k-item.k-state-hover,
.k-menu.k-context-menu .k-item:hover,
.k-menu.k-context-menu .k-item.k-state-hover {
  color: #ffffff;
  background-color: #90a4ae; }

.k-list .k-item.k-state-selected,
.k-list-optionlabel.k-state-selected, .k-list-container .k-button:active,
.k-list-container .k-button.k-state-active, .k-spreadsheet-popup .k-button:active,
.k-spreadsheet-popup .k-button.k-state-active,
.k-spreadsheet-popup .k-button.k-state-selected, .k-menu-group .k-item.k-state-selected,
.k-menu.k-context-menu .k-item.k-state-selected {
  color: #ffffff;
  background-color: #607d8b; }

.k-reset {
  margin: 0;
  padding: 0;
  border-width: 0;
  outline: 0;
  text-decoration: none;
  font: inherit;
  list-style: none; }

.k-widget {
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box;
  outline: 0;
  font-size: 14px;
  line-height: 1.42857;
  display: block;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent; }

table.k-widget {
  display: table; }

kendo-sortable {
  display: block; }

.k-overlay {
  width: 100%;
  height: 100%;
  background-color: #000000;
  opacity: .5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10001; }

.k-rtl {
  direction: rtl; }

.k-link,
.k-link:hover {
  color: inherit;
  text-decoration: none; }

.k-content {
  outline: 0; }

.k-centered {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); }

.k-pdf-export-shadow {
  position: absolute;
  overflow: hidden;
  left: -15000px;
  width: 14400px; }

.kendo-pdf-hide-pseudo-elements::before,
.kendo-pdf-hide-pseudo-elements::after {
  display: none !important; }

@font-face {
  font-family: "DejaVu Sans";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSans.ttf") format("truetype"); }

@font-face {
  font-family: "DejaVu Sans";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSans-Bold.ttf") format("truetype");
  font-weight: bold; }

@font-face {
  font-family: "DejaVu Sans";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSans-Oblique.ttf") format("truetype");
  font-style: italic; }

@font-face {
  font-family: "DejaVu Sans";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSans-BoldOblique.ttf") format("truetype");
  font-weight: bold;
  font-style: italic; }

@font-face {
  font-family: "DejaVu Serif";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSerif.ttf") format("truetype"); }

@font-face {
  font-family: "DejaVu Serif";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSerif-Bold.ttf") format("truetype");
  font-weight: bold; }

@font-face {
  font-family: "DejaVu Serif";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSerif-Italic.ttf") format("truetype");
  font-style: italic; }

@font-face {
  font-family: "DejaVu Serif";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSerif-BoldItalic.ttf") format("truetype");
  font-weight: bold;
  font-style: italic; }

@font-face {
  font-family: "DejaVu Mono";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSansMono.ttf") format("truetype"); }

@font-face {
  font-family: "DejaVu Mono";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSansMono-Bold.ttf") format("truetype");
  font-weight: bold; }

@font-face {
  font-family: "DejaVu Mono";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSansMono-Oblique.ttf") format("truetype");
  font-style: italic; }

@font-face {
  font-family: "DejaVu Mono";
  src: url("https://kendo.cdn.telerik.com/2018.1.117/styles/fonts/DejaVu/DejaVuSansMono-BoldOblique.ttf") format("truetype");
  font-weight: bold;
  font-style: italic; }

.k-state-selected {
  color: #ffffff;
  background-color: #607d8b;
  border-color: #607d8b; }

.k-state-disabled,
.k-widget[disabled] {
  outline: none;
  cursor: default;
  opacity: 0.6;
  filter: grayscale(0.1);
  pointer-events: none;
  box-shadow: none; }
  .k-state-disabled .k-link,
  .k-state-disabled .k-button,
  .k-widget[disabled] .k-link,
  .k-widget[disabled] .k-button {
    cursor: default;
    outline: 0; }
  .k-state-disabled [disabled],
  .k-state-disabled .k-state-disabled,
  .k-widget[disabled] [disabled],
  .k-widget[disabled] .k-state-disabled {
    opacity: 1;
    filter: grayscale(0); }

.k-hr, .k-separator, .k-menu-group .k-item.k-separator,
.k-menu-vertical .k-item.k-separator {
  margin: 1em auto;
  height: 0;
  border-width: 1px 0 0;
  border-style: solid;
  border-color: #c2c2c2;
  display: block; }

.k-hbox > .k-hr, .k-columnset > .k-hr, .k-toolbar .k-separator, .k-menu-horizontal > .k-item.k-separator {
  margin: 0;
  width: 0;
  height: auto;
  border-width: 0 0 0 1px;
  flex: 0 0 auto; }

.k-hr {
  padding: 0;
  float: none;
  clear: both; }

.k-vbox > .k-hr, .k-rowset > .k-hr {
  margin: 0;
  flex: 0 0 auto; }

.k-dirty {
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  border-width: 3px;
  border-style: solid;
  border-color: #f44336 #f44336 transparent transparent;
  position: absolute;
  top: 0;
  right: 0; }

.k-display-inline {
  display: inline !important; }

.k-display-block {
  display: block !important; }

.k-display-inline-block {
  display: inline-block !important; }

.k-display-flex {
  display: flex !important; }

.k-display-inline-flex {
  display: inline-flex !important; }

.k-display-table {
  display: table !important; }

.k-display-inline-table {
  display: inline-table !important; }

.k-display-none, .k-hidden {
  display: none !important; }

.k-float-left {
  float: left !important; }

.k-float-right {
  float: right !important; }

.k-float-none {
  float: none !important; }

.k-floatwrap::after,
.k-grid-toolbar::after,
.k-slider-items::after {
  content: "";
  display: block;
  clear: both; }

.k-flex-wrap {
  flex-wrap: wrap; }

.k-flex-nowrap {
  flex-wrap: nowrap; }

.k-flex-wrap-reverse {
  flex-wrap: wrap-reverse; }

.k-flex {
  flex: 1 1 0; }

.k-flex-auto {
  flex: 1 1 auto; }

.k-no-flex {
  flex: 0 0 0; }

.k-no-flex-auto {
  flex: 0 0 auto; }

.k-flex-grow {
  flex-grow: 1; }

.k-no-flex-grow {
  flex-grow: 0; }

.k-flex-shrink {
  flex-shrink: 1; }

.k-no-flex-shrink {
  flex-shrink: 0; }

.k-align-items-start {
  align-items: flex-start; }

.k-align-items-end {
  align-items: flex-end; }

.k-align-items-center {
  align-items: center; }

.k-align-items-stretch {
  align-items: stretch; }

.k-align-items-baseline {
  align-items: baseline; }

.k-align-content-start {
  align-content: flex-start; }

.k-align-content-end {
  align-content: flex-end; }

.k-align-content-center {
  align-content: center; }

.k-align-content-stretch {
  align-content: stretch; }

.k-align-content-baseline {
  align-content: baseline; }

.k-align-self-start {
  align-self: flex-start; }

.k-align-self-end {
  align-self: flex-end; }

.k-align-self-center {
  align-self: center; }

.k-align-self-stretch {
  align-self: stretch; }

.k-align-self-baseline {
  align-self: baseline; }

.k-justify-content-start {
  justify-content: flex-start; }

.k-justify-content-end {
  justify-content: flex-end; }

.k-justify-content-center {
  justify-content: center; }

.k-justify-content-between {
  justify-content: space-between; }

.k-justify-content-around {
  justify-content: space-around; }

.k-justify-content-evenly {
  justify-content: space-evenly; }

.k-hbox, .k-columnset, .k-vbox, .k-rowset {
  display: flex; }

.k-ihbox, .k-ivbox {
  display: inline-flex; }

.k-hbox, .k-columnset, .k-ihbox {
  flex-direction: row; }

.k-vbox, .k-rowset, .k-ivbox {
  flex-direction: column; }

.k-column {
  flex-grow: 1;
  flex-basis: 0; }

.k-reset {
  margin: 0;
  padding: 0;
  border-width: 0;
  outline: 0;
  font: inherit;
  text-decoration: none;
  list-style: none; }

.k-text-nowrap {
  white-space: nowrap !important; }

.k-text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }

.k-text-left {
  text-align: left !important; }

.k-text-right {
  text-align: right !important; }

.k-text-center {
  text-align: center !important; }

.k-text-justify {
  text-align: justify !important; }

.k-text-lowercase {
  text-transform: lowercase !important; }

.k-text-uppercase {
  text-transform: uppercase !important; }

.k-text-capitalize {
  text-transform: capitalize !important; }

.k-font-weight-light {
  font-weight: 300 !important; }

.k-font-weight-normal {
  font-weight: 400 !important; }

.k-font-weight-bold {
  font-weight: 700 !important; }

.k-flip-h {
  transform: scaleX(-1); }

.k-flip-v {
  transform: scaleY(-1); }

.k-flip-h.k-flip-v {
  transform: scale(-1, -1); }

.k-rotate-45 {
  transform: rotate(45deg); }

.k-rotate-90 {
  transform: rotate(90deg); }

.k-rotate-135 {
  transform: rotate(135deg); }

.k-rotate-180 {
  transform: rotate(180deg); }

.k-rotate-225 {
  transform: rotate(225deg); }

.k-rotate-270 {
  transform: rotate(270deg); }

.k-rotate-315 {
  transform: rotate(315deg); }

.k-scale-0 {
  transform: scale(0, 0); }

.k-scale-1 {
  transform: scale(1, 1); }

.k-scale-2 {
  transform: scale(2, 2); }

.k-translate-0 {
  transform: translate(0, 0); }

.k-translate-0-50 {
  transform: translate(0, 50%); }

.k-translate-0-100 {
  transform: translate(0, 100%); }

.k-translate-50-0 {
  transform: translate(50%, 0); }

.k-translate-50-50 {
  transform: translate(50%, 50%); }

.k-translate-50-100 {
  transform: translate(50%, 100%); }

.k-translate-100-0 {
  transform: translate(100%, 0); }

.k-translate-100-50 {
  transform: translate(100%, 50%); }

.k-translate-100-100 {
  transform: translate(100%, 100%); }

.k-animation-container {
  position: absolute;
  overflow: hidden;
  z-index: 100; }
  .k-animation-container-fixed {
    position: fixed; }

.k-push-right-enter, .k-push-right-appear {
  transform: translate(-100%, 0); }

.k-push-right-enter-active, .k-push-right-appear-active {
  transform: translate(0, 0);
  transition: transform 300ms ease-in-out; }

.k-push-right-exit {
  transform: translate(0, 0); }

.k-push-right-exit-active {
  transform: translate(100%, 0);
  transition: transform 300ms ease-in-out; }

.k-push-left-enter, .k-push-left-appear {
  transform: translate(100%, 0); }

.k-push-left-enter-active, .k-push-left-appear-active {
  transform: translate(0, 0);
  transition: transform 300ms ease-in-out; }

.k-push-left-exit {
  transform: translate(0, 0); }

.k-push-left-exit-active {
  transform: translate(-100%, 0);
  transition: transform 300ms ease-in-out; }

.k-push-down-enter, .k-push-down-appear {
  transform: translate(0, -100%); }

.k-push-down-enter-active, .k-push-down-appear-active {
  transform: translate(0, 0);
  transition: transform 300ms ease-in-out; }

.k-push-down-exit {
  transform: translate(0, 0); }

.k-push-down-exit-active {
  transform: translate(0, 100%);
  transition: transform 300ms ease-in-out; }

.k-push-up-enter, .k-push-up-appear {
  transform: translate(0, 100%); }

.k-push-up-enter-active, .k-push-up-appear-active {
  transform: translate(0, 0);
  transition: transform 300ms ease-in-out; }

.k-push-up-exit {
  transform: translate(0, 0); }

.k-push-up-exit-active {
  transform: translate(0, -100%);
  transition: transform 300ms ease-in-out; }

.k-expand-vertical-enter, .k-expand-vertical-appear {
  transform: scaleY(0); }

.k-expand-vertical-enter-active, .k-expand-vertical-appear-active {
  transform: scaleY(1);
  transition: transform 300ms ease-in-out; }

.k-expand-vertical-exit {
  transform: scaleY(1); }

.k-expand-vertical-exit-active {
  transform: scaleY(0);
  transition: transform 300ms ease-in-out; }

.k-expand-horizontal-enter, .k-expand-horizontal-appear {
  transform: scaleX(0); }

.k-expand-horizontal-enter-active, .k-expand-horizontal-appear-active {
  transform: scaleX(1);
  transition: transform 300ms ease-in-out; }

.k-expand-horizontal-exit {
  transform: scaleX(1); }

.k-expand-horizontal-exit-active {
  transform: scaleX(0);
  transition: transform 300ms ease-in-out; }

.k-fade-enter, .k-fade-appear {
  opacity: 0; }

.k-fade-enter-active, .k-fade-appear-active {
  opacity: 1;
  transition: opacity 500ms ease-in-out; }

.k-fade-exit {
  opacity: 1; }

.k-fade-exit-active {
  opacity: 0;
  transition: opacity 500ms ease-in-out; }

.k-fade-exit-active + .k-fade-exit-active,
.k-fade-enter-active + .k-fade-enter-active {
  display: none; }

.k-zoom-in-enter, .k-zoom-in-appear {
  opacity: 0;
  transform: scale(0); }

.k-zoom-in-enter-active, .k-zoom-in-appear-active {
  opacity: 1;
  transform: scale(1);
  transition: transform, opacity 300ms ease-in-out; }

.k-zoom-in-exit {
  opacity: 1;
  transform: scale(1); }

.k-zoom-in-exit-active {
  opacity: 0;
  transform: scale(2);
  transition: transform, opacity 300ms ease-in-out; }

.k-zoom-out-enter, .k-zoom-out-appear {
  opacity: 0;
  transform: scale(2); }

.k-zoom-out-enter-active, .k-zoom-out-appear-active {
  opacity: 1;
  transform: scale(1);
  transition: transform, opacity 300ms ease-in-out; }

.k-zoom-out-exit {
  opacity: 1;
  transform: scale(1); }

.k-zoom-out-exit-active {
  opacity: 0;
  transform: scale(0);
  transition: transform, opacity 300ms ease-in-out; }

.k-slide-in-appear {
  opacity: .1;
  transform: translate(0, -3em); }
  .k-slide-in-appear .k-centered {
    transform: translate(-50%, -60%); }

.k-slide-in-appear-active {
  opacity: 1;
  transform: translate(0, 0);
  transition: transform 0.3s cubic-bezier(0.2, 0.6, 0.4, 1), opacity 0.3s cubic-bezier(0.2, 1, 0.2, 1); }
  .k-slide-in-appear-active .k-centered {
    transform: translate(-50%, -50%); }

.k-slide-down-enter, .k-slide-down-appear {
  transform: translateY(-100%); }

.k-slide-down-enter-active, .k-slide-down-appear-active {
  transform: translateY(0);
  transition: transform 300ms ease-in-out; }

.k-slide-down-exit {
  transform: translateY(0); }

.k-slide-down-exit-active {
  transform: translateY(-100%);
  transition: transform 300ms ease-in-out; }

.k-slide-up-enter, .k-slide-up-appear {
  transform: translateY(100%); }

.k-slide-up-enter-active, .k-slide-up-appear-active {
  transform: translateY(0);
  transition: transform 300ms ease-in-out; }

.k-slide-up-exit {
  transform: translateY(0); }

.k-slide-up-exit-active {
  transform: translateY(100%);
  transition: transform 300ms ease-in-out; }

.k-slide-right-enter, .k-slide-right-appear {
  transform: translateX(-100%); }

.k-slide-right-enter-active, .k-slide-right-appear-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out; }

.k-slide-right-exit {
  transform: translateX(0); }

.k-slide-right-exit-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-in-out; }

.k-slide-left-enter, .k-slide-left-appear {
  transform: translateX(100%); }

.k-slide-left-enter-active, .k-slide-left-appear-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out; }

.k-slide-left-exit {
  transform: translateX(0); }

.k-slide-left-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in-out; }

.k-reveal-vertical-enter, .k-reveal-vertical-appear {
  max-height: 0; }

.k-reveal-vertical-enter-active, .k-reveal-vertical-appear-active {
  transition: max-height 300ms ease-in-out; }

.k-reveal-vertical-exit-active {
  max-height: 0 !important;
  transition: max-height 300ms ease-in-out; }

.k-reveal-horizontal-enter, .k-reveal-horizontal-appear {
  max-width: 0; }

.k-reveal-horizontal-enter-active, .k-reveal-horizontal-appear-active {
  transition: max-width 300ms ease-in-out; }

.k-reveal-horizontal-exit-active {
  max-width: 0 !important;
  transition: max-width 300ms ease-in-out; }

.k-arrow-e,
.k-arrow-w {
  width: 0;
  height: 0;
  border: 5px solid transparent;
  position: absolute;
  top: -4px; }

.k-arrow-e {
  border-left-color: currentColor;
  right: -6px; }

.k-arrow-w {
  border-right-color: currentColor;
  left: -6px; }

.k-drag-clue {
  padding: 2px 4px;
  border-width: 1px;
  border-style: solid;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  cursor: default; }

.k-drag-status {
  margin-right: .4ex; }

.k-reorder-cue {
  position: absolute; }
  .k-reorder-cue::before, .k-reorder-cue::after {
    content: "";
    width: 0;
    height: 0;
    border: 3px solid transparent;
    position: absolute;
    transform: translateX(-50%); }
  .k-reorder-cue::before {
    border-bottom-width: 0;
    border-top-color: currentColor;
    top: -4px; }
  .k-reorder-cue::after {
    border-top-width: 0;
    border-bottom-color: currentColor;
    bottom: -4px; }

.k-autocomplete,
.k-combobox,
.k-dateinput,
.k-datepicker,
.k-datetimepicker,
.k-dropdown,
.k-dropdowntree,
.k-multiselect,
.k-numerictextbox,
.k-maskedtextbox,
.k-timepicker,
.k-dropdowntree {
  width: 12.4em;
  line-height: 1.42857;
  text-align: left;
  white-space: nowrap;
  display: inline-flex;
  vertical-align: middle;
  position: relative; }
  .k-autocomplete[dir='rtl'],
  .k-rtl .k-autocomplete,
  .k-combobox[dir='rtl'],
  .k-rtl
  .k-combobox,
  .k-dateinput[dir='rtl'],
  .k-rtl
  .k-dateinput,
  .k-datepicker[dir='rtl'],
  .k-rtl
  .k-datepicker,
  .k-datetimepicker[dir='rtl'],
  .k-rtl
  .k-datetimepicker,
  .k-dropdown[dir='rtl'],
  [dir='rtl'].k-dropdowntree,
  .k-rtl
  .k-dropdown,
  .k-rtl .k-dropdowntree,
  .k-multiselect[dir='rtl'],
  .k-rtl
  .k-multiselect,
  .k-numerictextbox[dir='rtl'],
  .k-rtl
  .k-numerictextbox,
  .k-maskedtextbox[dir='rtl'],
  .k-rtl
  .k-maskedtextbox,
  .k-timepicker[dir='rtl'],
  .k-rtl
  .k-timepicker,
  .k-dropdowntree[dir='rtl'],
  .k-rtl
  .k-dropdowntree {
    text-align: right; }
    .k-autocomplete[dir='rtl'] .k-select,
    .k-rtl .k-autocomplete .k-select,
    .k-combobox[dir='rtl'] .k-select,
    .k-rtl
    .k-combobox .k-select,
    .k-dateinput[dir='rtl'] .k-select,
    .k-rtl
    .k-dateinput .k-select,
    .k-datepicker[dir='rtl'] .k-select,
    .k-rtl
    .k-datepicker .k-select,
    .k-datetimepicker[dir='rtl'] .k-select,
    .k-rtl
    .k-datetimepicker .k-select,
    .k-dropdown[dir='rtl'] .k-select, [dir='rtl'].k-dropdowntree .k-select,
    .k-rtl
    .k-dropdown .k-select,
    .k-rtl .k-dropdowntree .k-select,
    .k-multiselect[dir='rtl'] .k-select,
    .k-rtl
    .k-multiselect .k-select,
    .k-numerictextbox[dir='rtl'] .k-select,
    .k-rtl
    .k-numerictextbox .k-select,
    .k-maskedtextbox[dir='rtl'] .k-select,
    .k-rtl
    .k-maskedtextbox .k-select,
    .k-timepicker[dir='rtl'] .k-select,
    .k-rtl
    .k-timepicker .k-select,
    .k-dropdowntree[dir='rtl'] .k-select,
    .k-rtl
    .k-dropdowntree .k-select {
      border-radius: 4px 0 0 4px; }
    .k-autocomplete[dir='rtl'] .k-clear-value,
    .k-rtl .k-autocomplete .k-clear-value,
    .k-combobox[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-combobox .k-clear-value,
    .k-dateinput[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-dateinput .k-clear-value,
    .k-datepicker[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-datepicker .k-clear-value,
    .k-datetimepicker[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-datetimepicker .k-clear-value,
    .k-dropdown[dir='rtl'] .k-clear-value, [dir='rtl'].k-dropdowntree .k-clear-value,
    .k-rtl
    .k-dropdown .k-clear-value,
    .k-rtl .k-dropdowntree .k-clear-value,
    .k-multiselect[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-multiselect .k-clear-value,
    .k-numerictextbox[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-numerictextbox .k-clear-value,
    .k-maskedtextbox[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-maskedtextbox .k-clear-value,
    .k-timepicker[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-timepicker .k-clear-value,
    .k-dropdowntree[dir='rtl'] .k-clear-value,
    .k-rtl
    .k-dropdowntree .k-clear-value {
      left: 8px;
      right: auto; }
  .k-autocomplete ::-ms-clear,
  .k-combobox ::-ms-clear,
  .k-dateinput ::-ms-clear,
  .k-datepicker ::-ms-clear,
  .k-datetimepicker ::-ms-clear,
  .k-dropdown ::-ms-clear, .k-dropdowntree ::-ms-clear,
  .k-multiselect ::-ms-clear,
  .k-numerictextbox ::-ms-clear,
  .k-maskedtextbox ::-ms-clear,
  .k-timepicker ::-ms-clear,
  .k-dropdowntree ::-ms-clear {
    display: none; }

.k-colorpicker,
.k-combobox,
.k-dateinput,
.k-datepicker,
.k-datetimepicker,
.k-dropdown,
.k-dropdowntree,
.k-multiselect,
.k-numerictextbox,
.k-maskedtextbox,
.k-timepicker,
.k-dropdowntree {
  border-width: 0;
  background-color: transparent; }

.k-nodata {
  min-height: 138px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  font-weight: lighter;
  text-align: center;
  white-space: normal; }

.k-searchbar {
  flex: 1;
  display: flex;
  flex-direction: row; }

.k-dateinput-wrap,
.k-dropdown-wrap,
.k-picker-wrap,
.k-multiselect-wrap,
.k-numeric-wrap {
  border-radius: 4px;
  padding: 0;
  width: 100%;
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box;
  position: relative;
  transition: all .1s ease;
  cursor: default;
  outline: 0; }
  .k-dateinput-wrap .k-input,
  .k-dropdown-wrap .k-input,
  .k-picker-wrap .k-input,
  .k-multiselect-wrap .k-input,
  .k-numeric-wrap .k-input {
    padding: 4px 8px;
    height: calc( 8px + 1.42857em);
    box-sizing: border-box;
    border: 0;
    outline: 0;
    color: inherit;
    background: none;
    font: inherit;
    flex: 0 1 auto;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis; }

.k-dateinput-wrap,
.k-dropdown-wrap,
.k-picker-wrap,
.k-numeric-wrap {
  display: flex;
  flex-direction: row; }
  .k-dateinput-wrap .k-input,
  .k-dropdown-wrap .k-input,
  .k-picker-wrap .k-input,
  .k-numeric-wrap .k-input {
    width: 100%; }
  .k-dateinput-wrap .k-select,
  .k-dropdown-wrap .k-select,
  .k-picker-wrap .k-select,
  .k-numeric-wrap .k-select {
    border-radius: 0 4px 4px 0;
    padding: 4px;
    border-width: 0 0 0 1px;
    box-sizing: border-box;
    border-style: solid;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    text-align: center;
    cursor: pointer; }

.k-autocomplete .k-clear-value,
.k-autocomplete .k-i-loading,
.k-dropdown-wrap .k-clear-value,
.k-dropdown-wrap .k-i-loading,
.k-multiselect-wrap .k-clear-value,
.k-multiselect-wrap .k-i-loading {
  position: absolute;
  right: 8px; }
  .k-rtl .k-autocomplete .k-clear-value,
  [dir="rtl"] .k-autocomplete .k-clear-value, .k-rtl
  .k-autocomplete .k-i-loading,
  [dir="rtl"]
  .k-autocomplete .k-i-loading, .k-rtl
  .k-dropdown-wrap .k-clear-value,
  [dir="rtl"]
  .k-dropdown-wrap .k-clear-value, .k-rtl
  .k-dropdown-wrap .k-i-loading,
  [dir="rtl"]
  .k-dropdown-wrap .k-i-loading, .k-rtl
  .k-multiselect-wrap .k-clear-value,
  [dir="rtl"]
  .k-multiselect-wrap .k-clear-value, .k-rtl
  .k-multiselect-wrap .k-i-loading,
  [dir="rtl"]
  .k-multiselect-wrap .k-i-loading {
    right: auto;
    left: 8px; }

.k-autocomplete .k-i-loading,
.k-dropdown-wrap .k-i-loading,
.k-multiselect-wrap .k-i-loading {
  margin-bottom: -.5em; }

.k-autocomplete .k-clear-value,
.k-dropdown-wrap .k-clear-value,
.k-multiselect-wrap .k-clear-value {
  height: 20px;
  line-height: 20px;
  outline: 0;
  opacity: .5;
  cursor: pointer;
  display: none;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: -10px; }

.k-autocomplete.k-state-focused .k-clear-value, .k-autocomplete.k-state-hover .k-clear-value, .k-autocomplete:hover .k-clear-value,
.k-dropdown-wrap.k-state-focused .k-clear-value,
.k-dropdown-wrap.k-state-hover .k-clear-value,
.k-dropdown-wrap:hover .k-clear-value,
.k-multiselect-wrap.k-state-focused .k-clear-value,
.k-multiselect-wrap.k-state-hover .k-clear-value,
.k-multiselect-wrap:hover .k-clear-value {
  display: inline-flex; }
  .k-autocomplete.k-state-focused .k-clear-value:hover, .k-autocomplete.k-state-hover .k-clear-value:hover, .k-autocomplete:hover .k-clear-value:hover,
  .k-dropdown-wrap.k-state-focused .k-clear-value:hover,
  .k-dropdown-wrap.k-state-hover .k-clear-value:hover,
  .k-dropdown-wrap:hover .k-clear-value:hover,
  .k-multiselect-wrap.k-state-focused .k-clear-value:hover,
  .k-multiselect-wrap.k-state-hover .k-clear-value:hover,
  .k-multiselect-wrap:hover .k-clear-value:hover {
    opacity: 1; }

.k-autocomplete .k-i-loading,
.k-autocomplete .k-clear-value,
.k-dropdown-wrap .k-i-loading,
.k-dropdown-wrap .k-clear-value {
  bottom: 50%; }

.k-multiselect-wrap .k-i-loading,
.k-multiselect-wrap .k-clear-value {
  bottom: calc(14px); }

.k-autocomplete,
.k-multiselect-wrap {
  padding-right: calc(8px + 16px); }
  .k-rtl .k-autocomplete,
  [dir="rtl"] .k-autocomplete, .k-rtl
  .k-multiselect-wrap,
  [dir="rtl"]
  .k-multiselect-wrap {
    padding-left: calc(8px + 16px);
    padding-right: 8px; }

.k-dateinput .k-select,
.k-numerictextbox .k-select {
  border-width: 0;
  padding: 0;
  width: calc( 8px + 1.42857em);
  display: flex;
  flex-direction: column;
  align-items: stretch; }

.k-dateinput .k-link,
.k-numerictextbox .k-link {
  flex: 1 1 0;
  display: block;
  overflow: hidden;
  position: relative; }
  .k-dateinput .k-link .k-icon,
  .k-numerictextbox .k-link .k-icon {
    position: absolute;
    right: 50%;
    transform: translateX(50%); }

.k-dateinput .k-link-increase .k-icon,
.k-numerictextbox .k-link-increase .k-icon {
  bottom: -2px; }

.k-dateinput .k-link-decrease .k-icon,
.k-numerictextbox .k-link-decrease .k-icon {
  top: -2px; }

.k-virtual-content {
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  position: relative; }
  .k-virtual-content > .k-virtual-list > .k-virtual-item {
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap; }

.k-textbox.k-space-left {
  padding-left: 1.9em; }

.k-textbox.k-space-right {
  padding-right: 1.9em; }

.k-textbox .k-icon {
  top: 50%;
  margin: -8px 0 0;
  position: absolute; }

.k-space-left .k-icon {
  left: 3px; }

.k-space-right .k-icon {
  right: 3px; }

.k-widget.k-state-invalid.k-autocomplete,
.k-widget.k-state-invalid.k-maskedtextbox > .k-textbox,
.ng-invalid.ng-touched.k-autocomplete,
.ng-invalid.ng-touched.k-maskedtextbox > .k-textbox,
.ng-invalid.ng-dirty.k-autocomplete,
.ng-invalid.ng-dirty.k-maskedtextbox > .k-textbox {
  color: #f44336;
  border-color: #f44336; }

.k-widget.k-state-invalid > .k-dateinput-wrap,
.k-widget.k-state-invalid > .k-dropdown-wrap,
.k-widget.k-state-invalid > .k-picker-wrap,
.k-widget.k-state-invalid > .k-multiselect-wrap,
.k-widget.k-state-invalid > .k-numeric-wrap,
.ng-invalid.ng-touched > .k-dateinput-wrap,
.ng-invalid.ng-touched > .k-dropdown-wrap,
.ng-invalid.ng-touched > .k-picker-wrap,
.ng-invalid.ng-touched > .k-multiselect-wrap,
.ng-invalid.ng-touched > .k-numeric-wrap,
.ng-invalid.ng-dirty > .k-dateinput-wrap,
.ng-invalid.ng-dirty > .k-dropdown-wrap,
.ng-invalid.ng-dirty > .k-picker-wrap,
.ng-invalid.ng-dirty > .k-multiselect-wrap,
.ng-invalid.ng-dirty > .k-numeric-wrap {
  color: #f44336;
  border-color: #f44336; }

.k-widget.form-control {
  padding: 0; }

.k-widget.form-control,
.k-maskedtextbox.form-control .k-textbox,
.k-textbox.form-control {
  width: 100%; }

.k-autocomplete.form-control .k-input {
  box-sizing: border-box; }

.input-group .k-dateinput-wrap,
.input-group .k-dropdown-wrap,
.input-group .k-multiselect-wrap,
.input-group .k-numeric-wrap,
.input-group .k-picker-wrap,
.input-group .k-maskedtextbox .k-textbox {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }
  .input-group .k-dateinput-wrap .k-select,
  .input-group .k-dropdown-wrap .k-select,
  .input-group .k-multiselect-wrap .k-select,
  .input-group .k-numeric-wrap .k-select,
  .input-group .k-picker-wrap .k-select,
  .input-group .k-maskedtextbox .k-textbox .k-select {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }

.input-group .k-widget.form-control,
.input-group .k-maskedtextbox.form-control .k-textbox,
.input-group .k-textbox.form-control {
  width: auto; }

.input-group .k-widget.form-control:not(:first-child):not(:last-child) .k-dateinput-wrap,
.input-group .k-widget.form-control:not(:first-child):not(:last-child) .k-dropdown-wrap,
.input-group .k-widget.form-control:not(:first-child):not(:last-child) .k-multiselect-wrap,
.input-group .k-widget.form-control:not(:first-child):not(:last-child) .k-numeric-wrap,
.input-group .k-widget.form-control:not(:first-child):not(:last-child) .k-picker-wrap,
.input-group .k-widget.form-control:not(:first-child):not(:last-child) > .k-textbox {
  border-radius: 0; }

.input-group-prepend + .k-widget.form-control .k-dateinput-wrap,
.input-group-prepend + .k-widget.form-control .k-dropdown-wrap,
.input-group-prepend + .k-widget.form-control .k-multiselect-wrap,
.input-group-prepend + .k-widget.form-control .k-numeric-wrap,
.input-group-prepend + .k-widget.form-control .k-picker-wrap,
.input-group-prepend + .k-widget.form-control > .k-textbox {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px; }

.k-widget.form-control .k-input {
  padding: 0.5rem 0.75rem;
  height: 2.25rem; }

.k-widget.form-control-sm .k-input {
  padding: 0.25rem 0.5rem;
  height: 1.8125rem; }

.k-widget.form-control-sm .k-dateinput-wrap .k-select,
.k-widget.form-control-sm .k-dropdown-wrap .k-select,
.k-widget.form-control-sm .k-picker-wrap .k-select {
  padding: 0.25rem; }

.k-widget.form-control-sm .k-select {
  width: 1.8125rem; }

.k-widget.form-control-lg .k-input {
  padding: 0.5rem 1rem;
  height: 2.875rem; }

.k-widget.form-control-lg .k-dateinput-wrap .k-select,
.k-widget.form-control-lg .k-dropdown-wrap .k-select,
.k-widget.form-control-lg .k-picker-wrap .k-select {
  padding: 0.5rem; }

.k-widget.form-control-lg .k-select {
  width: 2.875rem; }

.k-maskedtextbox.form-control .k-textbox {
  height: calc( 2.25rem + 2px);
  padding: 0.5rem 0.75rem; }

.k-maskedtextbox.form-control-sm .k-textbox {
  height: calc( 1.8125rem + 2px);
  padding: 0.25rem 0.5rem;
  line-height: 1.5; }

.k-maskedtextbox.form-control-lg .k-textbox {
  height: calc( 2.875rem + 2px);
  padding: 0.5rem 1rem;
  line-height: 1.5; }

.k-textbox.form-control {
  height: calc( 2.25rem + 2px);
  padding: 0.5rem 0.75rem; }
  .k-textbox.form-control-sm {
    height: calc( 1.8125rem + 2px);
    padding: 0.25rem 0.5rem;
    line-height: 1.5; }
  .k-textbox.form-control-lg {
    height: calc( 2.875rem + 2px);
    padding: 0.5rem 1rem;
    line-height: 1.5; }

.k-dateinput.form-control-sm .k-link-increase > .k-icon,
.k-numerictextbox.form-control-sm .k-link-increase > .k-icon {
  bottom: -2px; }

.k-dateinput.form-control-sm .k-link-decrease > .k-icon,
.k-numerictextbox.form-control-sm .k-link-decrease > .k-icon {
  top: -2px; }

.k-dateinput.form-control-sm .k-link,
.k-numerictextbox.form-control-sm .k-link {
  height: 0.90625rem; }

.k-dateinput.form-control-lg .k-link,
.k-numerictextbox.form-control-lg .k-link {
  height: 1.4375rem; }

.k-dateinput.form-control .k-dateinput-wrap .k-select {
  padding-top: 0;
  padding-bottom: 0; }

@font-face {
  font-family: 'WebComponentsIcons';
  font-style: normal;
  font-weight: normal;
  src: url("data:font/ttf;base64,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") format("truetype"); }

.k-icon {
  width: 1em;
  height: 1em;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-size: 16px;
  font-family: 'WebComponentsIcons';
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 1;
  speak: none;
  text-transform: none;
  text-decoration: none;
  display: inline-block;
  vertical-align: middle; }
  .k-icon::before {
    vertical-align: baseline; }
  .k-icon:hover, .k-icon:focus {
    text-decoration: none; }

.k-icon-with-modifier {
  position: relative;
  margin: .25em; }

.k-icon-modifier {
  position: absolute;
  font-size: .5em;
  bottom: 0;
  right: 0;
  margin: 0 -.5em -.5em 0; }

.k-i-none::before {
  content: "";
  display: none; }

.k-icon-xs {
  font-size: 8px; }

.k-icon-sm {
  font-size: 12px; }

.k-icon-md {
  font-size: 32px; }

.k-icon-lg {
  font-size: 48px; }

.k-icon-xl {
  font-size: 64px; }

.k-i-arrow-45-up-right::before {
  content: "\e000"; }

.k-i-collapse-ne::before {
  content: "\e000"; }

.k-i-resize-ne::before {
  content: "\e000"; }

.k-i-arrow-45-down-right::before {
  content: "\e001"; }

.k-i-collapse-se::before {
  content: "\e001"; }

.k-i-resize-se::before {
  content: "\e001"; }

.k-i-arrow-45-down-left::before {
  content: "\e002"; }

.k-i-collapse-sw::before {
  content: "\e002"; }

.k-i-resize-sw::before {
  content: "\e002"; }

.k-i-arrow-45-up-left::before, .k-i-resize-nw::before {
  content: "\e003"; }

.k-i-collapse-nw::before {
  content: "\e003"; }

.k-i-resize-new::before {
  content: "\e003"; }

.k-i-arrow-60-up::before, .k-i-arrow-n::before, .k-i-sarrow-n::before, .k-i-expand-n::before {
  content: "\e004"; }

.k-i-kpi-trend-increase::before {
  content: "\e004"; }

.k-i-arrow-60-right::before, .k-i-arrow-e::before, .k-i-sarrow-e::before, .k-i-expand::before, .k-i-expand-e::before {
  content: "\e005"; }

.k-i-arrow-60-down::before, .k-i-arrow-s::before, .k-i-sarrow-s::before, .k-i-collapse::before, .k-i-expand-s::before {
  content: "\e006"; }

.k-i-kpi-trend-decrease::before {
  content: "\e006"; }

.k-i-arrow-60-left::before, .k-i-arrow-w::before, .k-i-sarrow-w::before, .k-i-expand-w::before {
  content: "\e007"; }

.k-i-arrow-end-up::before {
  content: "\e008"; }

.k-i-arrow-end-right::before, .k-i-seek-e::before {
  content: "\e009"; }

.k-i-arrow-end-down::before {
  content: "\e00a"; }

.k-i-arrow-end-left::before, .k-i-seek-w::before {
  content: "\e00b"; }

.k-i-arrow-double-60-up::before {
  content: "\e00c"; }

.k-i-arrow-seek-up::before, .k-i-seek-n::before {
  content: "\e00c"; }

.k-i-arrow-double-60-right::before {
  content: "\e00d"; }

.k-i-arrow-seek-right::before {
  content: "\e00d"; }

.k-i-forward-sm::before {
  content: "\e00d"; }

.k-i-arrow-double-60-down::before {
  content: "\e00e"; }

.k-i-arrow-seek-down::before, .k-i-seek-s::before {
  content: "\e00e"; }

.k-i-arrow-double-60-left::before {
  content: "\e00f"; }

.k-i-arrow-seek-left::before {
  content: "\e00f"; }

.k-i-rewind-sm::before {
  content: "\e00f"; }

.k-i-arrows-kpi::before {
  content: "\e010"; }

.k-i-kpi::before {
  content: "\e010"; }

.k-i-arrows-no-change::before {
  content: "\e011"; }

.k-i-arrow-overflow-down::before {
  content: "\e012"; }

.k-i-arrow-chevron-up::before, .k-i-arrowhead-n::before {
  content: "\e013"; }

.k-i-arrow-chevron-right::before, .k-i-arrowhead-e::before {
  content: "\e014"; }

.k-i-arrow-chevron-down::before, .k-i-arrowhead-s::before {
  content: "\e015"; }

.k-i-arrow-chevron-left::before, .k-i-arrowhead-w::before {
  content: "\e016"; }

.k-i-arrow-up::before {
  content: "\e017"; }

.k-i-arrow-right::before {
  content: "\e018"; }

.k-i-arrow-down::before {
  content: "\e019"; }

.k-i-arrow-left::before {
  content: "\e01a"; }

.k-i-arrow-drill::before {
  content: "\e01b"; }

.k-i-arrow-parent::before {
  content: "\e01c"; }

.k-i-arrow-root::before {
  content: "\e01d"; }

.k-i-arrows-resizing::before {
  content: "\e01e"; }

.k-i-arrows-dimensions::before, .k-i-dimension::before {
  content: "\e01f"; }

.k-i-arrows-swap::before {
  content: "\e020"; }

.k-i-drag-and-drop::before {
  content: "\e021"; }

.k-i-categorize::before {
  content: "\e022"; }

.k-i-grid::before {
  content: "\e023"; }

.k-i-grid-layout::before {
  content: "\e024"; }

.k-i-group::before {
  content: "\e025"; }

.k-i-ungroup::before {
  content: "\e026"; }

.k-i-handler-drag::before {
  content: "\e027"; }

.k-i-layout::before {
  content: "\e028"; }

.k-i-layout-1-by-4::before {
  content: "\e029"; }

.k-i-layout-2-by-2::before, .k-i-page-layout::before {
  content: "\e02a"; }

.k-i-layout-side-by-side::before {
  content: "\e02b"; }

.k-i-layout-stacked::before {
  content: "\e02c"; }

.k-i-columns::before {
  content: "\e02d"; }

.k-i-rows::before {
  content: "\e02e"; }

.k-i-reorder::before {
  content: "\e02f"; }

.k-i-menu::before, .k-i-hamburger::before {
  content: "\e030"; }

.k-i-more-vertical::before, .k-i-vbars::before {
  content: "\e031"; }

.k-i-more-horizontal::before, .k-i-hbars::before {
  content: "\e032"; }

.k-i-undo::before, .k-i-undo-large::before {
  content: "\e100"; }

.k-i-redo::before, .k-i-redo-large::before {
  content: "\e101"; }

.k-i-reset::before {
  content: "\e102"; }

.k-i-reload::before {
  content: "\e103"; }

.k-i-refresh::before {
  content: "\e103"; }

.k-i-recurrence::before {
  content: "\e103"; }

.k-i-non-recurrence::before, .k-i-refresh-clear::before {
  content: "\e104"; }

.k-i-reset-sm::before {
  content: "\e105"; }

.k-i-reload-sm::before {
  content: "\e106"; }

.k-i-refresh-sm::before {
  content: "\e106"; }

.k-i-recurrence-sm::before {
  content: "\e106"; }

.k-i-clock::before {
  content: "\e107"; }

.k-i-calendar::before {
  content: "\e108"; }

.k-i-save::before {
  content: "\e109"; }

.k-i-floppy::before {
  content: "\e109"; }

.k-i-print::before {
  content: "\e10a"; }

.k-i-printer::before {
  content: "\e10a"; }

.k-i-edit::before {
  content: "\e10b"; }

.k-i-pencil::before {
  content: "\e10b"; }

.k-i-delete::before {
  content: "\e10c"; }

.k-i-trash::before {
  content: "\e10c"; }

.k-i-attachment::before {
  content: "\e10d"; }

.k-i-clip::before {
  content: "\e10d"; }

.k-i-attachment-45::before {
  content: "\e10e"; }

.k-i-clip-45::before {
  content: "\e10e"; }

.k-i-link-horizontal::before {
  content: "\e10f"; }

.k-i-hyperlink::before {
  content: "\e10f"; }

.k-i-unlink-horizontal::before {
  content: "\e110"; }

.k-i-hyperlink-remove::before {
  content: "\e110"; }

.k-i-link-vertical::before {
  content: "\e111"; }

.k-i-unlink-vertical::before {
  content: "\e112"; }

.k-i-lock::before {
  content: "\e113"; }

.k-i-unlock::before {
  content: "\e114"; }

.k-i-cancel::before {
  content: "\e115"; }

.k-i-cancel-outline::before, .k-i-deny::before {
  content: "\e116"; }

.k-i-cancel-circle::before {
  content: "\e117"; }

.k-i-check::before {
  content: "\e118"; }

.k-i-checkmark::before {
  content: "\e118"; }

.k-i-check-outline::before {
  content: "\e119"; }

.k-i-checkmark-outline::before {
  content: "\e119"; }

.k-i-success::before {
  content: "\e119"; }

.k-i-check-circle::before {
  content: "\e11a"; }

.k-i-checkmark-circle::before {
  content: "\e11a"; }

.k-i-close::before, .k-i-group-delete::before {
  content: "\e11b"; }

.k-i-x::before {
  content: "\e11b"; }

.k-i-close-outline::before {
  content: "\e11c"; }

.k-i-x-outline::before {
  content: "\e11c"; }

.k-i-error::before {
  content: "\e11c"; }

.k-i-close-circle::before {
  content: "\e11d"; }

.k-i-x-circle::before {
  content: "\e11d"; }

.k-i-plus::before, .k-plus::before, .k-i-add::before {
  content: "\e11e"; }

.k-i-plus-outline::before {
  content: "\e11f"; }

.k-i-plus-circle::before {
  content: "\e120"; }

.k-i-minus::before, .k-minus::before {
  content: "\e121"; }

.k-i-kpi-trend-equal::before {
  content: "\e121"; }

.k-i-minus-outline::before {
  content: "\e122"; }

.k-i-minus-circle::before {
  content: "\e123"; }

.k-i-sort-asc::before {
  content: "\e124"; }

.k-i-sort-desc::before {
  content: "\e125"; }

.k-i-unsort::before {
  content: "\e126"; }

.k-i-sort-clear::before {
  content: "\e126"; }

.k-i-sort-asc-sm::before {
  content: "\e127"; }

.k-i-sort-desc-sm::before {
  content: "\e128"; }

.k-i-filter::before {
  content: "\e129"; }

.k-i-filter-clear::before {
  content: "\e12a"; }

.k-i-filter-sm::before {
  content: "\e12b"; }

.k-i-filter-sort-asc-sm::before {
  content: "\e12c"; }

.k-i-filter-sort-desc-sm::before {
  content: "\e12d"; }

.k-i-filter-add-expression::before {
  content: "\e12e"; }

.k-i-filter-add-group::before {
  content: "\e12f"; }

.k-i-login::before {
  content: "\e130"; }

.k-i-logout::before {
  content: "\e131"; }

.k-i-download::before {
  content: "\e132"; }

.k-i-upload::before {
  content: "\e133"; }

.k-i-hyperlink-open::before {
  content: "\e134"; }

.k-i-hyperlink-open-sm::before {
  content: "\e135"; }

.k-i-launch::before {
  content: "\e136"; }

.k-i-window::before {
  content: "\e137"; }

.k-i-window-maximize::before, .k-i-maximize::before {
  content: "\e137"; }

.k-i-windows::before {
  content: "\e138"; }

.k-i-window-restore::before, .k-i-restore::before {
  content: "\e138"; }

.k-i-tiles::before {
  content: "\e138"; }

.k-i-window-minimize::before, .k-i-minimize::before {
  content: "\e139"; }

.k-i-gear::before {
  content: "\e13a"; }

.k-i-cog::before {
  content: "\e13a"; }

.k-i-custom::before {
  content: "\e13a"; }

.k-i-gears::before {
  content: "\e13b"; }

.k-i-cogs::before {
  content: "\e13b"; }

.k-i-wrench::before {
  content: "\e13c"; }

.k-i-settings::before {
  content: "\e13c"; }

.k-i-preview::before {
  content: "\e13d"; }

.k-i-eye::before {
  content: "\e13d"; }

.k-i-zoom::before {
  content: "\e13e"; }

.k-i-search::before {
  content: "\e13e"; }

.k-i-zoom-in::before {
  content: "\e13f"; }

.k-i-zoom-out::before {
  content: "\e140"; }

.k-i-pan::before {
  content: "\e141"; }

.k-i-move::before {
  content: "\e141"; }

.k-i-calculator::before {
  content: "\e142"; }

.k-i-cart::before {
  content: "\e143"; }

.k-i-shopping-cart::before {
  content: "\e143"; }

.k-i-connector::before {
  content: "\e144"; }

.k-i-plus-sm::before, .k-i-splus::before {
  content: "\e145"; }

.k-i-minus-sm::before, .k-i-sminus::before {
  content: "\e146"; }

.k-i-kpi-status-deny::before {
  content: "\e147"; }

.k-i-kpi-status-hold::before {
  content: "\e148"; }

.k-i-kpi-status-open::before {
  content: "\e149"; }

.k-i-play::before {
  content: "\e200"; }

.k-i-pause::before {
  content: "\e201"; }

.k-i-stop::before {
  content: "\e202"; }

.k-i-rewind::before {
  content: "\e203"; }

.k-i-forward::before {
  content: "\e204"; }

.k-i-volume-down::before, .k-i-volume-low::before {
  content: "\e205"; }

.k-i-volume-up::before, .k-i-volume-high::before {
  content: "\e206"; }

.k-i-volume-off::before, .k-i-volume-mute::before {
  content: "\e207"; }

.k-i-hd::before {
  content: "\e208"; }

.k-i-subtitles::before {
  content: "\e209"; }

.k-i-playlist::before {
  content: "\e20a"; }

.k-i-audio::before {
  content: "\e20b"; }

.k-i-play-sm::before {
  content: "\e20c"; }

.k-i-pause-sm::before {
  content: "\e20d"; }

.k-i-stop-sm::before {
  content: "\e20e"; }

.k-i-heart-outline::before {
  content: "\e300"; }

.k-i-fav-outline::before {
  content: "\e300"; }

.k-i-favorite-outline::before {
  content: "\e300"; }

.k-i-heart::before {
  content: "\e301"; }

.k-i-fav::before {
  content: "\e301"; }

.k-i-favorite::before {
  content: "\e301"; }

.k-i-star-outline::before {
  content: "\e302"; }

.k-i-bookmark-outline::before {
  content: "\e302"; }

.k-i-star::before {
  content: "\e303"; }

.k-i-bookmark::before {
  content: "\e303"; }

.k-i-checkbox::before {
  content: "\e304"; }

.k-i-shape-rect::before {
  content: "\e304"; }

.k-i-checkbox-checked::before {
  content: "\e305"; }

.k-i-tri-state-indeterminate::before {
  content: "\e306"; }

.k-i-tri-state-null::before {
  content: "\e307"; }

.k-i-circle::before {
  content: "\e308"; }

.k-i-radiobutton::before {
  content: "\e309"; }

.k-i-shape-circle::before {
  content: "\e309"; }

.k-i-radiobutton-checked::before {
  content: "\e30a"; }

.k-i-notification::before {
  content: "\e400"; }

.k-i-bell::before {
  content: "\e400"; }

.k-i-information::before {
  content: "\e401"; }

.k-i-info::before {
  content: "\e401"; }

.k-i-question::before {
  content: "\e402"; }

.k-i-help::before {
  content: "\e402"; }

.k-i-warning::before {
  content: "\e403"; }

.k-i-exception::before {
  content: "\e403"; }

.k-i-photo-camera::before {
  content: "\e500"; }

.k-i-image::before {
  content: "\e501"; }

.k-i-photo::before {
  content: "\e501"; }

.k-i-image-export::before {
  content: "\e502"; }

.k-i-photo-export::before {
  content: "\e502"; }

.k-i-zoom-actual-size::before {
  content: "\e503"; }

.k-i-zoom-best-fit::before {
  content: "\e504"; }

.k-i-image-resize::before {
  content: "\e505"; }

.k-i-crop::before {
  content: "\e506"; }

.k-i-mirror::before {
  content: "\e507"; }

.k-i-flip-horizontal::before {
  content: "\e508"; }

.k-i-flip-vertical::before {
  content: "\e509"; }

.k-i-rotate::before {
  content: "\e50a"; }

.k-i-rotate-right::before, .k-i-rotate-cw::before {
  content: "\e50b"; }

.k-i-rotate-left::before, .k-i-rotate-ccw::before {
  content: "\e50c"; }

.k-i-brush::before {
  content: "\e50d"; }

.k-i-palette::before {
  content: "\e50e"; }

.k-i-paint::before {
  content: "\e50f"; }

.k-i-droplet::before {
  content: "\e50f"; }

.k-i-background::before {
  content: "\e50f"; }

.k-i-line::before {
  content: "\e510"; }

.k-i-shape-line::before {
  content: "\e510"; }

.k-i-brightness-contrast::before {
  content: "\e511"; }

.k-i-saturation::before {
  content: "\e512"; }

.k-i-invert-colors::before {
  content: "\e513"; }

.k-i-transperancy::before {
  content: "\e514"; }

.k-i-opacity::before {
  content: "\e514"; }

.k-i-greyscale::before {
  content: "\e515"; }

.k-i-blur::before {
  content: "\e516"; }

.k-i-sharpen::before {
  content: "\e517"; }

.k-i-shape::before {
  content: "\e518"; }

.k-i-round-corners::before {
  content: "\e519"; }

.k-i-front-element::before {
  content: "\e51a"; }

.k-i-back-element::before {
  content: "\e51b"; }

.k-i-forward-element::before {
  content: "\e51c"; }

.k-i-backward-element::before {
  content: "\e51d"; }

.k-i-align-left-element::before {
  content: "\e51e"; }

.k-i-align-center-element::before {
  content: "\e51f"; }

.k-i-align-right-element::before {
  content: "\e520"; }

.k-i-align-top-element::before {
  content: "\e521"; }

.k-i-align-middle-element::before {
  content: "\e522"; }

.k-i-align-bottom-element::before {
  content: "\e523"; }

.k-i-thumbnails-up::before {
  content: "\e524"; }

.k-i-thumbnails-right::before {
  content: "\e525"; }

.k-i-thumbnails-down::before {
  content: "\e526"; }

.k-i-thumbnails-left::before {
  content: "\e527"; }

.k-i-full-screen::before, .k-i-fullscreen-enter::before {
  content: "\e528"; }

.k-i-fullscreen::before {
  content: "\e528"; }

.k-i-full-screen-exit::before {
  content: "\e529"; }

.k-i-fullscreen-exit::before {
  content: "\e529"; }

.k-i-reset-color::before {
  content: "\e52a"; }

.k-i-paint-remove::before {
  content: "\e52a"; }

.k-i-background-remove::before {
  content: "\e52a"; }

.k-i-page-properties::before {
  content: "\e600"; }

.k-i-bold::before {
  content: "\e601"; }

.k-i-italic::before {
  content: "\e602"; }

.k-i-underline::before {
  content: "\e603"; }

.k-i-font-family::before {
  content: "\e604"; }

.k-i-foreground-color::before, .k-i-text::before {
  content: "\e605"; }

.k-i-convert-lowercase::before {
  content: "\e606"; }

.k-i-convert-uppercase::before {
  content: "\e607"; }

.k-i-strikethrough::before, .k-i-strike-through::before {
  content: "\e608"; }

.k-i-sub-script::before, .k-i-subscript::before {
  content: "\e609"; }

.k-i-sup-script::before, .k-i-superscript::before {
  content: "\e60a"; }

.k-i-div::before {
  content: "\e60b"; }

.k-i-all::before {
  content: "\e60c"; }

.k-i-h1::before {
  content: "\e60d"; }

.k-i-h2::before {
  content: "\e60e"; }

.k-i-h3::before {
  content: "\e60f"; }

.k-i-h4::before {
  content: "\e610"; }

.k-i-h5::before {
  content: "\e611"; }

.k-i-h6::before {
  content: "\e612"; }

.k-i-list-ordered::before, .k-i-insert-ordered-list::before {
  content: "\e613"; }

.k-i-list-numbered::before {
  content: "\e613"; }

.k-i-list-unordered::before, .k-i-insert-unordered-list::before {
  content: "\e614"; }

.k-i-list-bulleted::before {
  content: "\e614"; }

.k-i-indent-increase::before {
  content: "\e615"; }

.k-i-indent::before {
  content: "\e615"; }

.k-i-indent-decrease::before {
  content: "\e616"; }

.k-i-outdent::before {
  content: "\e616"; }

.k-i-insert-up::before, .k-i-insert-n::before {
  content: "\e617"; }

.k-i-insert-top::before {
  content: "\e617"; }

.k-i-insert-middle::before, .k-i-insert-m::before {
  content: "\e618"; }

.k-i-insert-down::before, .k-i-insert-s::before {
  content: "\e619"; }

.k-i-insert-bottom::before {
  content: "\e619"; }

.k-i-align-top::before {
  content: "\e61a"; }

.k-i-align-middle::before {
  content: "\e61b"; }

.k-i-align-bottom::before {
  content: "\e61c"; }

.k-i-align-left::before, .k-i-justify-left::before {
  content: "\e61d"; }

.k-i-align-center::before, .k-i-justify-center::before {
  content: "\e61e"; }

.k-i-align-right::before, .k-i-justify-right::before {
  content: "\e61f"; }

.k-i-align-justify::before, .k-i-justify-full::before {
  content: "\e620"; }

.k-i-align-remove::before, .k-i-justify-clear::before {
  content: "\e621"; }

.k-i-text-wrap::before {
  content: "\e622"; }

.k-i-rule-horizontal::before {
  content: "\e623"; }

.k-i-table-align-top-left::before {
  content: "\e624"; }

.k-i-table-align-top-center::before {
  content: "\e625"; }

.k-i-table-align-top-right::before {
  content: "\e626"; }

.k-i-table-align-middle-left::before {
  content: "\e627"; }

.k-i-table-align-middle-center::before {
  content: "\e628"; }

.k-i-table-align-middle-right::before {
  content: "\e629"; }

.k-i-table-align-bottom-left::before {
  content: "\e62a"; }

.k-i-table-align-bottom-center::before {
  content: "\e62b"; }

.k-i-table-align-bottom-right::before {
  content: "\e62c"; }

.k-i-table-align-remove::before {
  content: "\e62d"; }

.k-i-borders-all::before, .k-i-all-borders::before {
  content: "\e62e"; }

.k-i-borders-outside::before, .k-i-outside-borders::before {
  content: "\e62f"; }

.k-i-borders-inside::before, .k-i-inside-borders::before {
  content: "\e630"; }

.k-i-borders-inside-horizontal::before, .k-i-inside-horizontal-borders::before {
  content: "\e631"; }

.k-i-borders-inside-vertical::before, .k-i-inside-vertical-borders::before {
  content: "\e632"; }

.k-i-border-top::before, .k-i-top-border::before {
  content: "\e633"; }

.k-i-border-bottom::before, .k-i-bottom-border::before {
  content: "\e634"; }

.k-i-border-left::before, .k-i-left-border::before {
  content: "\e635"; }

.k-i-border-right::before, .k-i-right-border::before {
  content: "\e636"; }

.k-i-border-no::before, .k-i-no-borders::before {
  content: "\e637"; }

.k-i-borders-show-hide::before {
  content: "\e638"; }

.k-i-form::before {
  content: "\e639"; }

.k-i-border::before {
  content: "\e639"; }

.k-i-form-element::before {
  content: "\e63a"; }

.k-i-code-snippet::before {
  content: "\e63b"; }

.k-i-select-all::before {
  content: "\e63c"; }

.k-i-button::before {
  content: "\e63d"; }

.k-i-select-box::before {
  content: "\e63e"; }

.k-i-calendar-date::before {
  content: "\e63f"; }

.k-i-group-box::before {
  content: "\e640"; }

.k-i-textarea::before {
  content: "\e641"; }

.k-i-textbox::before {
  content: "\e642"; }

.k-i-textbox-hidden::before {
  content: "\e643"; }

.k-i-password::before {
  content: "\e644"; }

.k-i-paragraph-add::before {
  content: "\e645"; }

.k-i-edit-tools::before {
  content: "\e646"; }

.k-i-template-manager::before {
  content: "\e647"; }

.k-i-change-manually::before {
  content: "\e648"; }

.k-i-track-changes::before {
  content: "\e649"; }

.k-i-track-changes-enable::before {
  content: "\e64a"; }

.k-i-track-changes-accept::before {
  content: "\e64b"; }

.k-i-track-changes-accept-all::before {
  content: "\e64c"; }

.k-i-track-changes-reject::before {
  content: "\e64d"; }

.k-i-track-changes-reject-all::before {
  content: "\e64e"; }

.k-i-document-manager::before {
  content: "\e64f"; }

.k-i-custom-icon::before {
  content: "\e650"; }

.k-i-dictionary-add::before {
  content: "\e651"; }

.k-i-image-light-dialog::before {
  content: "\e652"; }

.k-i-image-insert::before, .k-i-insert-image::before {
  content: "\e652"; }

.k-i-image-edit::before {
  content: "\e653"; }

.k-i-image-map-editor::before {
  content: "\e654"; }

.k-i-comment::before {
  content: "\e655"; }

.k-i-comment-remove::before {
  content: "\e656"; }

.k-i-comments-remove-all::before {
  content: "\e657"; }

.k-i-silverlight::before {
  content: "\e658"; }

.k-i-media-manager::before {
  content: "\e659"; }

.k-i-video-external::before {
  content: "\e65a"; }

.k-i-flash-manager::before {
  content: "\e65b"; }

.k-i-find-and-replace::before {
  content: "\e65c"; }

.k-i-find::before {
  content: "\e65c"; }

.k-i-copy::before {
  content: "\e65d"; }

.k-i-files::before {
  content: "\e65d"; }

.k-i-cut::before {
  content: "\e65e"; }

.k-i-paste::before {
  content: "\e65f"; }

.k-i-paste-as-html::before {
  content: "\e660"; }

.k-i-paste-from-word::before {
  content: "\e661"; }

.k-i-paste-from-word-strip-file::before {
  content: "\e662"; }

.k-i-paste-html::before {
  content: "\e663"; }

.k-i-paste-markdown::before {
  content: "\e664"; }

.k-i-paste-plain-text::before {
  content: "\e665"; }

.k-i-apply-format::before {
  content: "\e666"; }

.k-i-clear-css::before, .k-i-clearformat::before {
  content: "\e667"; }

.k-i-copy-format::before {
  content: "\e668"; }

.k-i-strip-all-formating::before {
  content: "\e669"; }

.k-i-strip-css-format::before {
  content: "\e66a"; }

.k-i-strip-font-elements::before {
  content: "\e66b"; }

.k-i-strip-span-elements::before {
  content: "\e66c"; }

.k-i-strip-word-formatting::before {
  content: "\e66d"; }

.k-i-format-code-block::before {
  content: "\e66e"; }

.k-i-style-builder::before {
  content: "\e66f"; }

.k-i-module-manager::before {
  content: "\e670"; }

.k-i-hyperlink-light-dialog::before {
  content: "\e671"; }

.k-i-hyperlink-insert::before {
  content: "\e671"; }

.k-i-hyperlink-globe::before {
  content: "\e672"; }

.k-i-hyperlink-globe-remove::before {
  content: "\e673"; }

.k-i-hyperlink-email::before {
  content: "\e674"; }

.k-i-anchor::before {
  content: "\e675"; }

.k-i-table-light-dialog::before, .k-i-create-table::before {
  content: "\e676"; }

.k-i-table-insert::before {
  content: "\e676"; }

.k-i-table::before {
  content: "\e677"; }

.k-i-table-properties::before {
  content: "\e678"; }

.k-i-table-wizard::before {
  content: "\e678"; }

.k-i-table-cell::before {
  content: "\e679"; }

.k-i-table-cell-properties::before {
  content: "\e67a"; }

.k-i-table-column-insert-left::before, .k-i-add-column-left::before {
  content: "\e67b"; }

.k-i-table-column-insert-right::before, .k-i-add-column-right::before {
  content: "\e67c"; }

.k-i-table-row-insert-above::before, .k-i-add-row-above::before {
  content: "\e67d"; }

.k-i-table-row-insert-below::before, .k-i-add-row-below::before {
  content: "\e67e"; }

.k-i-table-column-delete::before, .k-i-delete-column::before {
  content: "\e67f"; }

.k-i-table-row-delete::before, .k-i-delete-row::before {
  content: "\e680"; }

.k-i-table-cell-delete::before {
  content: "\e681"; }

.k-i-table-delete::before {
  content: "\e682"; }

.k-i-cells-merge::before, .k-i-merge-cells::before {
  content: "\e683"; }

.k-i-cells-merge-horizontally::before, .k-i-merge-horizontally::before {
  content: "\e684"; }

.k-i-cells-merge-vertically::before, .k-i-merge-vertically::before {
  content: "\e685"; }

.k-i-cell-split-horizontally::before {
  content: "\e686"; }

.k-i-cell-split-vertically::before {
  content: "\e687"; }

.k-i-table-unmerge::before, .k-i-normal-layout::before {
  content: "\e688"; }

.k-i-pane-freeze::before, .k-i-freeze-panes::before {
  content: "\e689"; }

.k-i-row-freeze::before, .k-i-freeze-row::before {
  content: "\e68a"; }

.k-i-column-freeze::before, .k-i-freeze-col::before {
  content: "\e68b"; }

.k-i-toolbar-float::before {
  content: "\e68c"; }

.k-i-spell-checker::before {
  content: "\e68d"; }

.k-i-validation-xhtml::before {
  content: "\e68e"; }

.k-i-validation-data::before {
  content: "\e68f"; }

.k-i-toggle-full-screen-mode::before {
  content: "\e690"; }

.k-i-formula-fx::before, .k-i-fx::before, .k-spreadsheet-formula-bar::before {
  content: "\e691"; }

.k-i-sum::before {
  content: "\e692"; }

.k-i-symbol::before {
  content: "\e693"; }

.k-i-dollar::before {
  content: "\e694"; }

.k-i-currency::before {
  content: "\e694"; }

.k-i-percent::before {
  content: "\e695"; }

.k-i-custom-format::before, .k-i-format-number::before {
  content: "\e696"; }

.k-i-decimal-increase::before, .k-i-increase-decimal::before {
  content: "\e697"; }

.k-i-decimal-decrease::before, .k-i-decrease-decimal::before {
  content: "\e698"; }

.k-i-font-size::before {
  content: "\e699"; }

.k-i-image-absolute-position::before {
  content: "\e69a"; }

.k-i-globe-outline::before {
  content: "\e700"; }

.k-i-globe::before {
  content: "\e701"; }

.k-i-marker-pin::before {
  content: "\e702"; }

.k-i-marker-pin-target::before {
  content: "\e703"; }

.k-i-pin::before {
  content: "\e704"; }

.k-i-unpin::before {
  content: "\e705"; }

.k-i-share::before {
  content: "\e800"; }

.k-i-user::before {
  content: "\e801"; }

.k-i-inbox::before {
  content: "\e802"; }

.k-i-blogger::before {
  content: "\e803"; }

.k-i-blogger-box::before {
  content: "\e804"; }

.k-i-delicious::before {
  content: "\e805"; }

.k-i-delicious-box::before {
  content: "\e806"; }

.k-i-digg::before {
  content: "\e807"; }

.k-i-digg-box::before {
  content: "\e808"; }

.k-i-email::before {
  content: "\e809"; }

.k-i-envelop::before {
  content: "\e809"; }

.k-i-letter::before {
  content: "\e809"; }

.k-i-email-box::before {
  content: "\e80a"; }

.k-i-envelop-box::before {
  content: "\e80a"; }

.k-i-letter-box::before {
  content: "\e80a"; }

.k-i-facebook::before {
  content: "\e80b"; }

.k-i-facebook-box::before {
  content: "\e80c"; }

.k-i-google::before {
  content: "\e80d"; }

.k-i-google-box::before {
  content: "\e80e"; }

.k-i-google-plus::before {
  content: "\e80f"; }

.k-i-google-plus-box::before {
  content: "\e810"; }

.k-i-linkedin::before {
  content: "\e811"; }

.k-i-linkedin-box::before {
  content: "\e812"; }

.k-i-myspace::before {
  content: "\e813"; }

.k-i-myspace-box::before {
  content: "\e814"; }

.k-i-pinterest::before {
  content: "\e815"; }

.k-i-pinterest-box::before {
  content: "\e816"; }

.k-i-reddit::before {
  content: "\e817"; }

.k-i-reddit-box::before {
  content: "\e818"; }

.k-i-stumble-upon::before {
  content: "\e819"; }

.k-i-stumble-upon-box::before {
  content: "\e81a"; }

.k-i-tell-a-friend::before {
  content: "\e81b"; }

.k-i-tell-a-friend-box::before {
  content: "\e81c"; }

.k-i-tumblr::before {
  content: "\e81d"; }

.k-i-tumblr-box::before {
  content: "\e81e"; }

.k-i-twitter::before {
  content: "\e81f"; }

.k-i-twitter-box::before {
  content: "\e820"; }

.k-i-yammer::before {
  content: "\e821"; }

.k-i-yammer-box::before {
  content: "\e822"; }

.k-i-behance::before {
  content: "\e823"; }

.k-i-behance-box::before {
  content: "\e824"; }

.k-i-dribbble::before {
  content: "\e825"; }

.k-i-dribbble-box::before {
  content: "\e826"; }

.k-i-rss::before {
  content: "\e827"; }

.k-i-rss-box::before {
  content: "\e828"; }

.k-i-vimeo::before {
  content: "\e829"; }

.k-i-vimeo-box::before {
  content: "\e82a"; }

.k-i-youtube::before {
  content: "\e82b"; }

.k-i-youtube-box::before {
  content: "\e82c"; }

.k-i-folder::before {
  content: "\e900"; }

.k-i-folder-open::before {
  content: "\e901"; }

.k-i-folder-add::before {
  content: "\e902"; }

.k-i-folder-up::before {
  content: "\e903"; }

.k-i-folder-more::before {
  content: "\e904"; }

.k-i-fields-more::before {
  content: "\e904"; }

.k-i-aggregate-fields::before {
  content: "\e905"; }

.k-i-file::before {
  content: "\e906"; }

.k-i-file-vertical::before, .k-i-page-portrait::before {
  content: "\e906"; }

.k-i-file-add::before, .k-i-insert-file::before {
  content: "\e907"; }

.k-i-file-txt::before {
  content: "\e908"; }

.k-i-txt::before {
  content: "\e908"; }

.k-i-file-csv::before {
  content: "\e909"; }

.k-i-csv::before {
  content: "\e909"; }

.k-i-file-excel::before {
  content: "\e90a"; }

.k-i-file-xls::before {
  content: "\e90a"; }

.k-i-excel::before {
  content: "\e90a"; }

.k-i-xls::before, .k-i-xlsa::before {
  content: "\e90a"; }

.k-i-file-word::before {
  content: "\e90b"; }

.k-i-file-doc::before {
  content: "\e90b"; }

.k-i-word::before {
  content: "\e90b"; }

.k-i-doc::before {
  content: "\e90b"; }

.k-i-file-mdb::before {
  content: "\e90c"; }

.k-i-mdb::before {
  content: "\e90c"; }

.k-i-file-ppt::before {
  content: "\e90d"; }

.k-i-ppt::before {
  content: "\e90d"; }

.k-i-file-pdf::before {
  content: "\e90e"; }

.k-i-pdf::before, .k-i-pdfa::before {
  content: "\e90e"; }

.k-i-file-psd::before {
  content: "\e90f"; }

.k-i-psd::before {
  content: "\e90f"; }

.k-i-file-flash::before {
  content: "\e910"; }

.k-i-flash::before {
  content: "\e910"; }

.k-i-file-config::before {
  content: "\e911"; }

.k-i-config::before {
  content: "\e911"; }

.k-i-file-ascx::before {
  content: "\e912"; }

.k-i-ascx::before {
  content: "\e912"; }

.k-i-file-bac::before {
  content: "\e913"; }

.k-i-bac::before {
  content: "\e913"; }

.k-i-file-zip::before {
  content: "\e914"; }

.k-i-zip::before {
  content: "\e914"; }

.k-i-film::before {
  content: "\e915"; }

.k-i-css3::before {
  content: "\e916"; }

.k-i-html5::before {
  content: "\e917"; }

.k-i-html::before {
  content: "\e918"; }

.k-i-source-code::before {
  content: "\e918"; }

.k-i-view-source::before {
  content: "\e918"; }

.k-i-css::before {
  content: "\e919"; }

.k-i-js::before {
  content: "\e91a"; }

.k-i-exe::before {
  content: "\e91b"; }

.k-i-csproj::before {
  content: "\e91c"; }

.k-i-vbproj::before {
  content: "\e91d"; }

.k-i-cs::before {
  content: "\e91e"; }

.k-i-vb::before {
  content: "\e91f"; }

.k-i-sln::before {
  content: "\e920"; }

.k-i-cloud::before {
  content: "\e921"; }

.k-i-file-horizontal::before, .k-i-page-landscape::before {
  content: "\e922"; }

.k-rtl .k-i-indent-increase,
.k-rtl .k-i-indent-decrease,
.k-rtl .k-i-expand,
.k-rtl .k-i-collapse {
  transform: scaleX(-1); }

.k-sprite {
  display: inline-block;
  width: 16px;
  height: 16px;
  overflow: hidden;
  background-repeat: no-repeat;
  font-size: 0;
  line-height: 0;
  text-align: center;
  -ms-high-contrast-adjust: none; }

.k-image {
  display: inline-block; }

.k-line {
  background-color: currentColor;
  border-color: currentColor; }

.k-line-h,
.k-line-v {
  position: absolute; }

.k-line-h {
  height: 2px; }

.k-line-v {
  width: 2px; }

.k-loading {
  width: 64px;
  height: 64px;
  display: block; }
  .k-loading .animate {
    animation: loading 2s infinite linear; }

.k-loading-mask,
.k-loading-image,
.k-loading-color {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0; }

.k-loading-mask {
  z-index: 100; }
  .k-loading-mask.k-opaque .k-loading-color {
    opacity: 1; }

.k-loading-text {
  text-indent: -4000px;
  text-align: center;
  position: absolute; }

.k-loading-image {
  z-index: 2; }

.k-loading-color {
  background-color: #ffffff;
  opacity: .3; }

.k-i-loading {
  position: relative;
  background-color: transparent;
  box-sizing: border-box;
  color: currentColor; }

.k-i-loading::before, .k-loading-image::before,
.k-i-loading::after,
.k-loading-image::after {
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  content: "";
  box-sizing: inherit;
  border-radius: 50%;
  border-width: .05em;
  border-style: solid;
  border-color: currentColor;
  border-top-color: transparent;
  border-bottom-color: transparent;
  background-color: transparent; }

.k-icon.k-i-loading::before, .k-icon.k-loading-image::before,
.k-icon.k-i-loading::after,
.k-icon.k-loading-image::after {
  content: ""; }

.k-i-loading::before, .k-loading-image::before {
  margin-top: -.5em;
  margin-left: -.5em;
  width: 1em;
  height: 1em;
  animation: k-loading-animation .7s linear infinite; }

.k-i-loading::after, .k-loading-image::after {
  margin-top: -.25em;
  margin-left: -.25em;
  width: .5em;
  height: .5em;
  animation: k-loading-animation reverse 1.4s linear infinite; }

.k-loading-image::before,
.k-loading-image::after {
  border-width: .015em;
  font-size: 4em; }

@keyframes loading {
  0% {
    stroke-dasharray: 0 251;
    stroke-dashoffset: 502; }
  50% {
    stroke-dasharray: 250 1; }
  100% {
    stroke-dasharray: 0 251;
    stroke-dashoffset: 0; } }

@keyframes k-loading-animation {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }

.k-resize-handle,
.k-resize-hint {
  position: absolute;
  border-color: inherit; }

.k-resize-handle {
  display: flex;
  align-items: center;
  justify-content: center; }

.k-resize-handle::before {
  content: "";
  border: 0 solid;
  border-color: inherit; }

.k-resize-n {
  width: 100%;
  height: 6px;
  flex-direction: row;
  left: 0;
  top: -3px;
  cursor: n-resize; }

.k-resize-s {
  width: 100%;
  height: 6px;
  flex-direction: row;
  left: 0;
  bottom: -3px;
  cursor: s-resize; }

.k-resize-w {
  width: 6px;
  height: 100%;
  flex-direction: col;
  top: 0;
  left: -3px;
  cursor: w-resize; }

.k-resize-e {
  width: 6px;
  height: 100%;
  flex-direction: col;
  top: 0;
  right: -3px;
  cursor: w-resize; }

.k-resize-sw,
.k-resize-se,
.k-resize-nw,
.k-resize-ne {
  width: 5px;
  height: 5px; }

.k-resize-sw {
  cursor: sw-resize;
  bottom: 0;
  left: 0; }

.k-resize-se {
  cursor: se-resize;
  bottom: 0;
  right: 0; }

.k-resize-nw {
  cursor: nw-resize;
  top: 0;
  left: 0; }

.k-resize-ne {
  cursor: ne-resize;
  top: 0;
  right: 0; }

.k-scrollbar {
  position: absolute;
  overflow: scroll; }

.k-scrollbar-vertical {
  top: 0;
  right: 0;
  width: 17px;
  /* scrollbar width */
  height: 100%;
  overflow-x: hidden; }

.k-touch-scrollbar {
  display: none;
  position: absolute;
  z-index: 200000;
  height: 8px;
  width: 8px;
  border: 1px solid #8a8a8a;
  background-color: #858585; }

.k-widget ::selection, .k-block ::selection,
.k-panel ::selection {
  background-color: #607d8b;
  color: #ffffff; }

.k-marquee {
  position: absolute;
  z-index: 100000; }

.k-marquee-color,
.k-marquee-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

.k-marquee-color {
  color: #ffffff;
  background-color: #607d8b;
  border-color: #607d8b;
  opacity: .6; }

.k-marquee-text {
  color: #ffffff; }

.k-ripple-target {
  position: relative; }

.k-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
  pointer-events: none; }

.k-ripple-blob {
  pointer-events: none;
  position: absolute;
  border-radius: 50%;
  padding: 0;
  transform: translate(-50%, -50%) scale(0);
  transition: opacity 100ms linear, transform 500ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: .1;
  background-color: currentColor; }
  .k-primary .k-ripple-blob {
    opacity: .2; }

.k-popup {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin: 0;
  padding: 0 0;
  border-width: 1px;
  border-style: solid;
  font-size: 14px;
  line-height: 1.42857;
  box-sizing: content-box; }
  .k-popup .k-item {
    cursor: pointer;
    outline: none; }
  .k-popup .k-rtl .k-list-optionlabel,
  [dir='rtl'] .k-popup .k-list-optionlabel {
    text-align: right; }

.k-animation-container {
  border-radius: 0 0 4px 4px; }

.k-popup .k-item.k-first {
  position: relative; }

.k-popup > .k-group-header,
.k-popup > .k-virtual-wrap > .k-group-header {
  padding: 4px 8px;
  min-height: 1.42857em;
  white-space: normal;
  transition: background-color 0.2s ease;
  border-bottom-width: 1px;
  border-bottom-style: solid; }

.k-popup > .k-group-header {
  margin-top: 0; }

.k-popup .k-list .k-item > .k-group {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0 .5em;
  font-size: 9.996px;
  line-height: 1.5;
  text-transform: uppercase; }

.k-popup .k-list .k-item > .k-group::before {
  display: block;
  content: " ";
  border-width: 7px;
  border-style: solid;
  position: absolute;
  left: -14px;
  bottom: 0; }

.k-rtl.k-popup .k-list .k-item > .k-group,
[dir="rtl"] .k-popup .k-list .k-item > .k-group {
  right: auto;
  left: 0; }
  .k-rtl.k-popup .k-list .k-item > .k-group::before,
  [dir="rtl"] .k-popup .k-list .k-item > .k-group::before {
    right: -14px;
    left: auto; }

.k-group-header + div > .k-list > .k-item.k-first::before {
  content: " ";
  display: block;
  border-top-width: 1px;
  border-top-style: solid;
  position: absolute;
  top: -1px;
  left: 0;
  right: 0; }

.k-list-scroller {
  position: relative;
  overflow: auto; }

.k-list {
  height: auto; }
  .k-list > .k-item {
    display: flex;
    align-items: center;
    align-content: center; }
    .k-list > .k-item > .k-icon {
      align-self: center;
      margin-right: 8px; }
  .k-list > .k-custom-item {
    font-style: italic; }
    .k-list > .k-custom-item > .k-i-plus, .k-list > .k-custom-item > .k-plus::before, .k-list > .k-custom-item > .k-i-add::before {
      margin-left: auto;
      margin-right: 0; }

.k-list .k-item,
.k-list-optionlabel {
  padding: 4px 8px;
  min-height: 1.42857em;
  line-height: 1.42857em;
  white-space: normal; }

.k-list-optionlabel {
  cursor: pointer; }

.k-list-filter {
  display: block;
  position: relative;
  padding: 8px;
  height: calc( 24px + 1.42857em);
  box-sizing: border-box; }
  .k-list-filter > .k-textbox {
    width: 100% !important;
    box-sizing: border-box;
    padding-left: false;
    padding-right: 24px; }
  .k-list-filter > .k-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%); }
  .k-rtl .k-list-filter > .k-textbox,
  [dir='rtl'] .k-list-filter > .k-textbox {
    padding-right: false;
    padding-left: 24px; }
  .k-rtl .k-list-filter > .k-icon,
  [dir='rtl'] .k-list-filter > .k-icon {
    left: 16px;
    right: auto; }

.k-dropdown-button .k-popup .k-item {
  cursor: pointer; }

.k-split-button .k-list .k-item:focus,
.k-dropdown-button .k-list .k-item:focus {
  outline: none; }

.k-list-container .k-button {
  border-radius: 0;
  padding: 4px 8px;
  border-width: 0;
  color: inherit;
  background-color: transparent;
  background-image: none;
  line-height: inherit;
  display: flex;
  justify-content: flex-start; }
  .k-list-container .k-button .k-icon {
    margin: 0 4px 0 0; }

.k-list-container .k-nodata .k-button {
  display: inline-flex;
  border-width: 1px; }

.k-list-container .k-separator {
  height: 0; }

.k-popup {
  background-clip: padding-box; }
  .k-ie11 .k-popup,
  .k-edge12 .k-popup,
  .k-edge13 .k-popup {
    background-clip: border-box; }

.k-popup > .k-group-header,
.k-popup > .k-virtual-wrap > .k-group-header {
  border-bottom-color: #c2c2c2;
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.06); }

.k-popup .k-list .k-item > .k-group {
  color: #ffffff;
  background: #000000; }

.k-popup .k-list .k-item > .k-group::before {
  border-color: #000000 #000000 transparent transparent; }

.k-rtl.k-popup .k-list .k-item > .k-group::before,
[dir="rtl"] .k-popup .k-list .k-item > .k-group::before {
  border-color: #000000 transparent transparent #000000; }

.k-group-header + div > .k-list > .k-item.k-first::before {
  border-color: #c2c2c2; }

.k-popup-transparent {
  border-width: 0;
  background-color: transparent; }

.k-list .k-item {
  transition: all .2s ease; }

.k-list .k-item:hover.k-state-selected,
.k-list .k-item.k-state-hover.k-state-selected,
.k-list-optionlabel:hover.k-state-selected {
  color: #ffffff;
  background-color: #36505f; }

.k-animation-container {
  border-radius: 0 0 4px 4px; }

.k-animation-container-shown {
  overflow: visible; }

.k-animation-container-shown,
.k-animation-container > .k-popup {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.03), 0 4px 5px 0 rgba(0, 0, 0, 0.04); }

.k-animation-container-fixed > .k-popup {
  box-shadow: none; }

.k-list-container .k-button {
  box-shadow: none; }

.k-list-container .k-button:hover,
.k-list-container .k-button.k-state-hover {
  background-image: none; }

.k-list-container .k-button:active,
.k-list-container .k-button.k-state-active {
  background-image: none; }

.k-list-container .k-button:focus,
.k-list-container .k-button.k-state-focused {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }

.k-list-container .k-nodata {
  color: rgba(0, 0, 0, 0.5); }

.k-list-container .k-button.k-state-disabled {
  background: transparent; }

.k-dropdown .k-select, .k-dropdowntree .k-select {
  border-width: 0; }

.k-dropdown-wrap {
  outline: 0;
  cursor: pointer; }
  .k-dropdown-wrap > .k-input {
    flex: 1; }

.k-edge .k-dropdown-wrap > .k-input {
  min-width: 0; }

.k-dropdown-operator {
  width: auto; }
  .k-dropdown-operator .k-input {
    display: none; }
  .k-dropdown-operator .k-select {
    width: calc( 8px + 1.42857em);
    height: calc( 8px + 1.42857em); }

.k-dropdown .k-dropdown-wrap, .k-dropdowntree .k-dropdown-wrap {
  background-clip: padding-box; }
  .k-ie11 .k-dropdown .k-dropdown-wrap, .k-ie11 .k-dropdowntree .k-dropdown-wrap,
  .k-edge12 .k-dropdown .k-dropdown-wrap,
  .k-edge12 .k-dropdowntree .k-dropdown-wrap,
  .k-edge13 .k-dropdown .k-dropdown-wrap,
  .k-edge13 .k-dropdowntree .k-dropdown-wrap {
    background-clip: border-box; }
  .k-dropdown .k-dropdown-wrap .k-select, .k-dropdowntree .k-dropdown-wrap .k-select {
    padding: 0;
    width: calc( 8px + 1.42857em); }

.k-pager-wrap {
  padding: 8px 8px;
  border-width: 1px;
  line-height: 1.42857;
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: default;
  flex: 0 0 auto; }
  .k-pager-wrap .k-link,
  .k-pager-wrap .k-state-selected {
    padding: 4px;
    width: calc(1.42857em + 2px);
    height: calc(1.42857em + 2px);
    line-height: calc(1.42857em + 2px);
    box-sizing: content-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    text-align: center;
    z-index: 1; }
    .k-pager-wrap .k-link > .k-icon,
    .k-pager-wrap .k-state-selected > .k-icon {
      vertical-align: middle; }
  .k-pager-wrap .k-link:hover,
  .k-pager-wrap .k-state-selected {
    z-index: 2; }
  .k-pager-wrap .k-link:focus,
  .k-pager-wrap .k-state-selected {
    text-decoration: none;
    outline: none; }
  .k-pager-wrap .k-link.k-state-disabled {
    color: inherit; }
  .k-pager-wrap .k-pager-numbers {
    display: inline-flex;
    flex-direction: row; }
    .k-pager-wrap .k-pager-numbers li {
      display: inline-block; }
    .k-pager-wrap .k-pager-numbers .k-current-page {
      display: none; }
    .k-pager-wrap .k-pager-numbers .k-current-page + li {
      margin-left: 0; }
  .k-pager-wrap .k-label {
    margin: 0 1em;
    display: flex;
    align-items: center; }
  .k-pager-wrap .k-pager-input .k-textbox {
    margin: 0 1ex;
    width: 3em; }
  .k-pager-wrap .k-pager-sizes .k-dropdown, .k-pager-wrap .k-pager-sizes .k-dropdowntree,
  .k-pager-wrap .k-pager-sizes > select {
    width: 5.2em;
    margin: 0 8px; }
  .k-pager-wrap .k-pager-refresh {
    order: 10; }
  .k-pager-wrap .k-pager-info {
    flex: 1;
    text-align: right;
    order: 9;
    justify-content: flex-end; }
  [dir="rtl"] .k-pager-wrap .k-i-arrow-e,
  [dir="rtl"] .k-pager-wrap .k-i-arrow-w,
  [dir="rtl"] .k-pager-wrap .k-i-seek-e,
  [dir="rtl"] .k-pager-wrap .k-i-seek-w,
  .k-rtl .k-pager-wrap .k-i-arrow-e,
  .k-rtl .k-pager-wrap .k-i-arrow-w,
  .k-rtl .k-pager-wrap .k-i-seek-e,
  .k-rtl .k-pager-wrap .k-i-seek-w {
    transform: scaleX(-1); }

.k-pager-wrap {
  color: #000000;
  background-color: #f5f5f5; }
  .k-pager-wrap .k-link:focus,
  .k-pager-wrap .k-pager-nav:focus {
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
  .k-pager-wrap .k-link:hover,
  .k-pager-wrap .k-pager-nav:hover {
    background-color: #90a4ae; }
  .k-pager-wrap .k-state-selected:focus {
    box-shadow: none; }
  .k-pager-wrap .k-state-selected:hover {
    color: #ffffff;
    background-color: #607d8b;
    cursor: default; }

.k-pager-numbers .k-link,
.k-pager-numbers .k-link:link {
  color: #607d8b; }
  .k-pager-numbers .k-link.k-state-selected,
  .k-pager-numbers .k-link:link.k-state-selected {
    color: #ffffff; }

.k-button {
  border-radius: 4px;
  padding: 4px 8px;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  font-size: 14px;
  line-height: 1.42857;
  font-family: inherit;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  user-select: none;
  cursor: pointer;
  outline: none;
  -webkit-appearance: none;
  position: relative; }
  .k-button::-moz-focus-inner {
    padding: 0;
    border: 0;
    outline: 0; }
  .k-button:hover, .k-button:focus {
    text-decoration: none;
    outline: 0; }
  .k-button .k-icon,
  .k-button .k-image,
  .k-button .k-sprite {
    color: inherit;
    align-self: center;
    position: relative; }
  .k-button-icontext {
    overflow: visible; }
    .k-button-icontext .k-icon,
    .k-button-icontext .k-image,
    .k-button-icontext .k-sprite {
      margin: 0 4px 0 -4px; }
    .k-rtl .k-button-icontext .k-icon,
    .k-rtl .k-button-icontext .k-image,
    .k-rtl .k-button-icontext .k-sprite, .k-button-icontext[dir='rtl'] .k-icon,
    .k-button-icontext[dir='rtl'] .k-image,
    .k-button-icontext[dir='rtl'] .k-sprite {
      margin: 0 -4px 0 4px; }
  .k-button-icon {
    width: calc( 10px + 1.42857em);
    height: calc( 10px + 1.42857em);
    padding: 4px; }

.k-dropdown-button.k-state-focused, .k-dropdown-button:focus {
  outline: 0; }

.k-dropdown-button.k-widget {
  border-radius: 4px;
  border-width: 0;
  display: inline-block; }

.k-button-group {
  margin: 0;
  padding: 0;
  list-style: none;
  white-space: nowrap;
  display: inline-flex;
  flex-direction: row;
  vertical-align: middle;
  position: relative; }
  .k-button-group .k-button {
    border-radius: 0; }
  .k-button-group .k-button ~ .k-button {
    margin-left: -1px; }
  .k-button-group .k-button:hover,
  .k-button-group .k-button.k-state-hover,
  .k-button-group .k-button:active,
  .k-button-group .k-button.k-state-active {
    z-index: 2; }
  .k-button-group [disabled],
  .k-button-group .k-state-disabled {
    pointer-events: auto; }
  .k-button-group .k-group-start,
  .k-button-group .k-button:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px; }
  .k-button-group .k-group-end,
  .k-button-group .k-button:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px; }
  .k-button-group .k-group-start.k-group-end,
  .k-button-group .k-button:first-child:last-child {
    border-radius: 4px; }
  .k-button-group > input[type="radio"],
  .k-button-group > input[type="checkbox"],
  .k-button-group label input[type="radio"],
  .k-button-group label input[type="checkbox"] {
    margin: 0;
    padding: 0;
    clip: rect(0, 0, 0, 0);
    position: absolute;
    pointer-events: none; }

.k-button-group-stretched {
  display: flex; }
  .k-button-group-stretched .k-button {
    display: inline-block;
    flex: 1 1 0;
    overflow: hidden;
    text-overflow: ellipsis; }
    .k-button-group-stretched .k-button > .k-icon {
      vertical-align: text-bottom; }

.k-split-button:focus,
.k-split-button.k-state-focused {
  outline: none; }

.k-split-button.k-button-group.k-state-focused {
  border-radius: 4px; }

.k-split-button.k-widget {
  border-radius: 4px;
  border-width: 0; }

.k-split-button.k-button-group .k-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px; }

.k-split-button.k-button-group .k-button:nth-child(2) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px; }

.k-button.k-flat, .k-toolbar > .k-button:not(.k-overflow-anchor),
.k-toolbar > .k-button-group > .k-button,
.k-button.k-bare,
.k-calendar.k-calendar-range > .k-button {
  border-color: transparent !important;
  color: inherit;
  background: none !important;
  box-shadow: none !important;
  transition: color .2s ease-in-out; }
  .k-button.k-flat:hover, .k-toolbar > .k-button:hover:not(.k-overflow-anchor),
  .k-toolbar > .k-button-group > .k-button:hover, .k-button.k-flat.k-state-hover, .k-toolbar > .k-state-hover.k-button:not(.k-overflow-anchor),
  .k-toolbar > .k-button-group > .k-state-hover.k-button, .k-button.k-flat:active, .k-toolbar > .k-button:active:not(.k-overflow-anchor),
  .k-toolbar > .k-button-group > .k-button:active, .k-button.k-flat.k-state-active, .k-toolbar > .k-state-active.k-button:not(.k-overflow-anchor),
  .k-toolbar > .k-button-group > .k-state-active.k-button, .k-button.k-flat:hover:active, .k-toolbar > .k-button:hover:active:not(.k-overflow-anchor),
  .k-toolbar > .k-button-group > .k-button:hover:active, .k-button.k-flat:hover.k-state-active, .k-toolbar > .k-button:hover.k-state-active:not(.k-overflow-anchor),
  .k-toolbar > .k-button-group > .k-button:hover.k-state-active,
  .k-button.k-bare:hover,
  .k-calendar.k-calendar-range > .k-button:hover,
  .k-button.k-bare.k-state-hover,
  .k-calendar.k-calendar-range > .k-button.k-state-hover,
  .k-button.k-bare:active,
  .k-calendar.k-calendar-range > .k-button:active,
  .k-button.k-bare.k-state-active,
  .k-calendar.k-calendar-range > .k-button.k-state-active,
  .k-button.k-bare:hover:active,
  .k-calendar.k-calendar-range > .k-button:hover:active,
  .k-button.k-bare:hover.k-state-active,
  .k-calendar.k-calendar-range > .k-button:hover.k-state-active {
    color: inherit; }
  .k-button.k-flat::before, .k-toolbar .k-flat.k-picker-wrap::before,
  .k-toolbar .k-flat.k-dropdown-wrap::before, .k-toolbar > .k-button:not(.k-overflow-anchor)::before, .k-toolbar > .k-picker-wrap:not(.k-overflow-anchor)::before, .k-toolbar > .k-dropdown-wrap:not(.k-overflow-anchor)::before,
  .k-toolbar > .k-button-group > .k-button::before,
  .k-toolbar > .k-button-group > .k-picker-wrap::before,
  .k-toolbar > .k-button-group > .k-dropdown-wrap::before,
  .k-button.k-bare::before,
  .k-toolbar .k-bare.k-picker-wrap::before,
  .k-toolbar .k-bare.k-dropdown-wrap::before,
  .k-calendar.k-calendar-range > .k-button::before,
  .k-toolbar .k-calendar.k-calendar-range > .k-picker-wrap::before,
  .k-toolbar .k-calendar.k-calendar-range > .k-dropdown-wrap::before {
    display: block; }

.k-button.k-outline {
  color: inherit;
  background: none;
  box-shadow: none; }

.k-rtl .k-button-group .k-button ~ .k-button {
  margin-right: -1px;
  margin-left: 0; }

.k-rtl .k-button-group .k-button {
  border-radius: 0; }

.k-rtl .k-button-group .k-group-start,
.k-rtl .k-button-group .k-button:first-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px; }

.k-rtl .k-button-group .k-group-end,
.k-rtl .k-button-group .k-button:last-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px; }

.k-rtl .k-button-group .k-group-start.k-group-end,
.k-rtl .k-button-group .k-button:first-child:last-child {
  border-radius: 4px; }

.k-rtl .k-split-button .k-button {
  border-radius: 0 4px 4px 0; }

.k-rtl .k-split-button .k-split-button-arrow {
  border-radius: 4px 0 0 4px;
  margin-left: 0;
  margin-right: -1px; }

.k-button::before, .k-toolbar .k-picker-wrap::before,
.k-toolbar .k-dropdown-wrap::before {
  border-radius: inherit;
  content: "";
  background: currentColor;
  opacity: 0;
  display: none;
  pointer-events: none;
  position: absolute;
  left: -1px;
  right: -1px;
  top: -1px;
  bottom: -1px;
  z-index: 0;
  transition: opacity .2s ease-in-out; }

.k-button:hover::before, .k-toolbar .k-picker-wrap:hover::before,
.k-toolbar .k-dropdown-wrap:hover::before, .k-button.k-state-hover::before, .k-toolbar .k-state-hover.k-picker-wrap::before,
.k-toolbar .k-state-hover.k-dropdown-wrap::before {
  opacity: 0.08; }

.k-button.k-no-focus:not(:hover)::before, .k-toolbar .k-no-focus.k-picker-wrap:not(:hover)::before,
.k-toolbar .k-no-focus.k-dropdown-wrap:not(:hover)::before, .k-button.k-no-focus:not(.k-state-hover)::before, .k-toolbar .k-no-focus.k-picker-wrap:not(.k-state-hover)::before,
.k-toolbar .k-no-focus.k-dropdown-wrap:not(.k-state-hover)::before {
  opacity: 0; }

.k-button:active::before, .k-toolbar .k-picker-wrap:active::before,
.k-toolbar .k-dropdown-wrap:active::before, .k-button.k-state-active::before, .k-toolbar .k-state-active.k-picker-wrap::before,
.k-toolbar .k-state-active.k-dropdown-wrap::before {
  opacity: 0.16; }

.k-button.k-state-selected::before, .k-toolbar .k-state-selected.k-picker-wrap::before,
.k-toolbar .k-state-selected.k-dropdown-wrap::before {
  opacity: 0.2; }

.k-button::after {
  border-radius: 4px;
  content: "";
  opacity: 0;
  display: none;
  pointer-events: none;
  position: absolute;
  left: -1px;
  right: -1px;
  top: -1px;
  bottom: -1px;
  z-index: 0;
  transition: opacity .2s ease-in-out; }

.k-ie9 .k-button[disabled]:hover,
.k-ie9 .k-button[disabled]:focus,
.k-ie9 .k-button.k-state-disabled:hover,
.k-ie9 .k-button.k-state-disabled:focus,
.k-ie10 .k-button[disabled]:hover,
.k-ie10 .k-button[disabled]:focus,
.k-ie10 .k-button.k-state-disabled:hover,
.k-ie10 .k-button.k-state-disabled:focus {
  outline: none;
  cursor: default;
  opacity: 0.6;
  box-shadow: none; }

.k-button {
  background-clip: padding-box; }
  .k-ie11 .k-button,
  .k-edge12 .k-button,
  .k-edge13 .k-button {
    background-clip: border-box; }
  .k-button:focus, .k-button.k-state-focused {
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.06); }
  .k-button:active, .k-button.k-state-active {
    box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }
  .k-button.k-state-selected {
    box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }

.k-button.k-primary:focus, .k-button.k-primary.k-state-focused {
  box-shadow: 0 3px 4px 0 rgba(96, 125, 139, 0.4); }

.k-button.k-primary:active, .k-button.k-primary.k-state-active {
  box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }

.k-button.k-primary.k-state-selected {
  box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }

.k-dropdown-button:active > .k-button:not(:disabled),
.k-dropdown-button.k-state-active > .k-button:not(:disabled) {
  box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }

.k-dropdown-button:focus > .k-button,
.k-dropdown-button.k-state-focused > .k-button {
  box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.06); }

.k-dropdown-button .k-widget {
  border-width: 0;
  display: inline-block; }

.k-button-group {
  background-clip: padding-box; }
  .k-ie11 .k-button-group,
  .k-edge12 .k-button-group,
  .k-edge13 .k-button-group {
    background-clip: border-box; }
  .k-button-group .k-button:focus,
  .k-button-group .k-button.k-state-focused,
  .k-button-group > input[type="radio"]:focus + .k-button,
  .k-button-group > input[type="checkbox"]:focus + .k-button {
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
  .k-button-group .k-button:active,
  .k-button-group .k-button.k-state-active,
  .k-button-group .k-button.k-state-selected,
  .k-button-group > input[type="radio"]:checked + .k-button,
  .k-button-group > input[type="checkbox"]:checked + .k-button {
    box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }

.k-split-button:focus,
.k-split-button.k-state-focused {
  outline: none;
  box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.06); }

.k-split-button.k-button-group .k-button:active, .k-split-button.k-button-group .k-button.k-state-active {
  box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }

.k-button.k-outline {
  border-color: currentColor;
  color: #000000;
  background: none;
  box-shadow: none; }
  .k-button.k-outline:hover, .k-button.k-outline.k-state-hover {
    border-color: #000000;
    color: #ffffff;
    background-color: #000000; }
  .k-button.k-outline:focus, .k-button.k-outline.k-state-focused {
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.06); }
  .k-button.k-outline:active, .k-button.k-outline.k-state-active {
    border-color: #000000;
    color: #ffffff;
    background-color: #000000; }
  .k-button.k-outline.k-state-selected {
    border-color: #000000;
    color: #ffffff;
    background-color: #000000; }

.k-button.k-primary.k-outline {
  border-color: currentColor;
  color: #607d8b;
  background: none;
  box-shadow: none; }
  .k-button.k-primary.k-outline:hover, .k-button.k-primary.k-outline.k-state-hover {
    border-color: #607d8b;
    color: #ffffff;
    background-color: #607d8b; }
  .k-button.k-primary.k-outline:focus, .k-button.k-primary.k-outline.k-state-focused {
    box-shadow: 0 3px 4px 0 rgba(96, 125, 139, 0.4); }
  .k-button.k-primary.k-outline:active, .k-button.k-primary.k-outline.k-state-active {
    border-color: #607d8b;
    color: #ffffff;
    background-color: #607d8b;
    box-shadow: none; }
  .k-button.k-primary.k-outline.k-state-selected {
    border-color: #607d8b;
    color: #ffffff;
    background-color: #607d8b;
    box-shadow: none; }

.k-button.k-flat, .k-toolbar > .k-button:not(.k-overflow-anchor),
.k-toolbar > .k-button-group > .k-button,
.k-button-group .k-button.k-flat,
.k-button-group .k-toolbar > .k-button:not(.k-overflow-anchor),
.k-toolbar > .k-button-group > .k-button,
.k-button.k-bare,
.k-calendar.k-calendar-range > .k-button,
.k-button-group
.k-button.k-bare,
.k-button-group .k-calendar.k-calendar-range > .k-button {
  color: inherit;
  background: none; }

.k-button.k-flat::after, .k-toolbar > .k-button:not(.k-overflow-anchor)::after,
.k-toolbar > .k-button-group > .k-button::after,
.k-button.k-bare::after,
.k-calendar.k-calendar-range > .k-button::after {
  display: block; }

.k-button.k-flat:focus::after, .k-toolbar > .k-button:not(.k-overflow-anchor):focus::after,
.k-toolbar > .k-button-group > .k-button:focus::after, .k-button.k-flat.k-state-focused::after, .k-toolbar > .k-state-focused.k-button:not(.k-overflow-anchor)::after,
.k-toolbar > .k-button-group > .k-state-focused.k-button::after,
.k-button.k-bare:focus::after,
.k-calendar.k-calendar-range > .k-button:focus::after,
.k-button.k-bare.k-state-focused::after,
.k-calendar.k-calendar-range > .k-button.k-state-focused::after {
  box-shadow: inset 0 0 0 2px currentColor;
  opacity: .12; }

.k-button.k-primary.k-flat, .k-toolbar > .k-primary.k-button:not(.k-overflow-anchor),
.k-toolbar > .k-button-group > .k-primary.k-button,
.k-button-group .k-button.k-primary.k-flat,
.k-button-group .k-toolbar > .k-primary.k-button:not(.k-overflow-anchor),
.k-toolbar > .k-button-group > .k-primary.k-button,
.k-button.k-primary.k-bare,
.k-calendar.k-calendar-range > .k-button.k-primary,
.k-button-group
.k-button.k-primary.k-bare,
.k-button-group .k-calendar.k-calendar-range > .k-button.k-primary {
  color: #607d8b;
  background: none; }

.k-action-buttons {
  border-width: 1px 0 0;
  border-style: solid;
  border-color: inherit;
  display: flex;
  flex-direction: row;
  clear: both; }
  .k-action-buttons .k-button {
    border-radius: 0;
    padding: 12px 16px;
    border-width: 0;
    border-color: inherit;
    color: inherit;
    background: none;
    flex: 1; }
  .k-action-buttons .k-button + .k-button {
    border-left-width: 1px; }
  .k-rtl .k-action-buttons .k-button + .k-button,
  [dir="rtl"] .k-action-buttons .k-button + .k-button {
    border-left-width: 0;
    border-right-width: 1px;
    border-right-style: solid;
    margin-left: 0; }

.k-action-buttons {
  border-color: #c2c2c2; }
  .k-action-buttons .k-button {
    color: inherit; }
    .k-action-buttons .k-button:hover, .k-action-buttons .k-button.k-state-hover {
      border-color: inherit; }
    .k-action-buttons .k-button:focus, .k-action-buttons .k-button.k-state-focused {
      box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
    .k-action-buttons .k-button:active, .k-action-buttons .k-button.k-state-active {
      border-color: inherit; }
  .k-action-buttons .k-primary {
    color: #607d8b; }
    .k-action-buttons .k-primary:hover, .k-action-buttons .k-primary.k-state-hvoer {
      border-color: inherit; }
      .k-action-buttons .k-primary:hover:focus, .k-action-buttons .k-primary:hover.k-state-focused, .k-action-buttons .k-primary.k-state-hvoer:focus, .k-action-buttons .k-primary.k-state-hvoer.k-state-focused {
        box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
    .k-action-buttons .k-primary:focus, .k-action-buttons .k-primary.k-state-focused {
      box-shadow: inset 0 0 0 2px rgba(144, 164, 174, 0.3); }
    .k-action-buttons .k-primary:active, .k-action-buttons .k-primary.k-state-active {
      border-color: inherit; }

.k-edit-form-container {
  width: 400px;
  min-width: 400px;
  border-color: inherit;
  position: relative; }

.k-edit-form-container .k-tabstrip-wrapper {
  margin: -16px -16px; }

.k-edit-form-container .k-tabstrip.k-root-tabs {
  margin-bottom: 16px; }

.k-edit-form-container .k-tabstrip .k-tabstrip-items {
  padding: 16px 16px 0; }

.k-edit-form-container .k-tabstrip .k-content {
  padding: 16px 16px;
  border-width: 0; }

.k-edit-buttons {
  padding: 8px 8px;
  border-width: 1px 0 0;
  border-style: solid;
  border-color: inherit;
  text-align: right;
  clear: both; }

.k-edit-buttons .k-button + .k-button {
  margin-left: 0.5em; }

.k-edit-form-container .k-edit-buttons {
  margin: 16px -16px -16px;
  padding: 16px 16px; }

.k-popup-edit-form > .k-action-buttons,
.k-edit-form-container .k-action-buttons {
  margin: 1em -16px -16px; }

.k-edit-label {
  margin: 0 0 1em 0;
  padding: 5px 0;
  width: 30%;
  line-height: 1.42857;
  text-align: right;
  float: left;
  clear: both; }

.k-edit-field {
  margin: 0 0 1em 0;
  width: 65%;
  float: right;
  clear: right; }

.k-edit-field > .k-widget,
.k-edit-field > .k-textbox,
.k-edit-field > .k-input.k-textbox {
  width: 100%;
  box-sizing: border-box; }

.k-edit-field > .k-colorpicker {
  width: auto; }

.k-edit-field input[type="radio"]:not(.k-radio),
.k-edit-field input[type="checkbox"]:not(.k-checkbox) {
  margin-right: .4ex; }

.k-edit-field .k-radio-label,
.k-edit-field .k-checkbox-label {
  margin-right: 1em; }

.k-edit-field .k-checkbox-label {
  margin-top: 5px; }

.k-edit-field .k-reset > li + li {
  margin-top: 0.5em; }

.k-edit-field .k-reset .k-widget {
  margin: 0 .4ex 0 1ex; }

.k-rtl .k-edit-buttons {
  text-align: left; }
  .k-rtl .k-edit-buttons .k-button + .k-button {
    margin-left: 0;
    margin-right: .5em; }

.k-window {
  padding: 0;
  max-width: 98vw;
  max-height: 98vh;
  border-width: 1px;
  border-style: solid;
  display: inline-flex;
  flex-direction: column;
  position: absolute;
  z-index: 10002; }
  .k-window .k-overlay {
    position: absolute;
    opacity: 0; }

.k-window.k-window-maximized {
  max-width: 100vw;
  max-height: 100vh;
  box-shadow: none; }

.k-window-titlebar {
  padding: 12px 16px;
  border-width: 0 0 1px;
  border-style: solid;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  align-items: center; }

.k-window-title {
  margin: 0;
  font-size: 16px;
  line-height: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  cursor: default;
  flex: 1; }

.k-window-actions {
  margin: -5px;
  line-height: 1;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  align-items: center;
  vertical-align: top; }

.k-window-content,
.k-prompt-container {
  padding: 16px 16px;
  outline: 0;
  overflow: auto;
  position: relative;
  flex: 1 1 auto; }

.k-window-content + .k-prompt-container {
  margin-top: -8px; }

.k-window-iframecontent {
  padding: 0;
  overflow: visible; }
  .k-window-iframecontent .k-content-frame {
    vertical-align: top;
    border: 0;
    width: 100%;
    height: 100%; }

.k-window {
  border-width: 0;
  box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.06); }

.k-window.k-state-focused {
  box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.3); }

.k-window-titlebar {
  border-color: inherit;
  color: #000000;
  background-color: #f5f5f5;
  background-clip: padding-box; }
  .k-ie11 .k-window-titlebar,
  .k-edge12 .k-window-titlebar,
  .k-edge13 .k-window-titlebar {
    background-clip: border-box; }

.k-dialog-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001; }
  .k-dialog-wrapper .k-dialog {
    position: relative; }

.k-dialog {
  padding: 0;
  position: fixed;
  box-sizing: border-box; }
  .k-dialog.k-dialog-centered {
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%; }

.k-dialog-close {
  align-self: flex-end; }

.k-dialog-buttongroup {
  border-width: 1px 0 0;
  border-style: solid;
  border-color: inherit;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  flex: 0 0 auto; }
  .k-dialog-buttongroup .k-button {
    width: auto !important;
    max-width: 100%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis; }

.k-dialog-titlebar {
  border-color: inherit;
  color: #ffffff;
  background-color: #607d8b; }

.k-dialog-buttongroup {
  border-top-width: 1px;
  align-items: stretch; }
  .k-dialog-buttongroup .k-button {
    padding: 12px 16px;
    border-width: 0;
    border-color: inherit;
    flex: 1 0 0; }
  .k-dialog-buttongroup .k-button + .k-button {
    border-left-width: 1px; }
    .k-rtl .k-dialog-buttongroup .k-button + .k-button,
    [dir='rtl'] .k-dialog-buttongroup .k-button + .k-button {
      border-left-width: 0;
      border-right-width: 1px; }
  .k-dialog-buttongroup .k-button:not(:hover):not(.k-state-hover):not(:active):not(.k-state-eactive) {
    color: inherit;
    background: none; }
  .k-dialog-buttongroup .k-primary:not(:hover):not(.k-state-hover):not(:active):not(.k-state-eactive) {
    color: #607d8b; }
  .k-dialog-buttongroup .k-button,
  .k-dialog-buttongroup .k-button:first-child,
  .k-dialog-buttongroup .k-button:last-child,
  .k-dialog-buttongroup .k-group-start,
  .k-dialog-buttongroup .k-group-end {
    border-radius: 0; }

.k-grid {
  display: flex;
  flex-direction: column;
  position: relative; }
  .k-grid.k-display-block {
    display: block; }
  .k-grid .k-grid-container {
    display: flex;
    flex: 1 1 auto;
    overflow: hidden;
    position: relative; }
  .k-grid .k-grid-aria-root {
    border-color: inherit;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    overflow: hidden; }
  .k-grid table {
    margin: 0;
    width: 100%;
    max-width: none;
    border-width: 0;
    border-color: inherit;
    border-collapse: separate;
    border-spacing: 0;
    empty-cells: show;
    outline: 0; }
  .k-grid thead,
  .k-grid tbody,
  .k-grid tfoot {
    text-align: left;
    border-color: inherit; }
  .k-grid tr {
    border-color: inherit; }
  .k-grid th,
  .k-grid td {
    padding: 8px 12px;
    border-style: solid;
    border-color: inherit;
    outline: 0;
    font-weight: inherit;
    text-align: inherit;
    overflow: hidden;
    text-overflow: ellipsis; }
    .k-grid th:first-child,
    .k-grid td:first-child {
      border-left-width: 0; }
    .k-grid th:focus,
    .k-grid td:focus {
      outline: none; }
  .k-grid th {
    padding: 8px 12px;
    border-width: 0 0 1px 1px;
    white-space: nowrap; }
  .k-grid td {
    border-width: 0 0 0 1px;
    vertical-align: middle; }
  .k-grid a {
    color: inherit;
    text-decoration: none; }
  .k-grid a:hover {
    text-decoration: none; }
  .k-grid table,
  .k-grid .k-grid-header-wrap {
    box-sizing: content-box; }
  .k-grid .k-group-col,
  .k-grid .k-hierarchy-col {
    padding: 0;
    width: 32px; }
  .k-grid .k-grouping-row p {
    display: flex;
    align-items: center;
    align-content: center; }
  .k-grid .k-grouping-row td {
    overflow: visible; }
  .k-grid .k-grouping-row + tr td {
    border-top-width: 1px; }
  .k-grid .k-grouping-row .k-group-cell,
  .k-grid .k-grouping-row + tr .k-group-cell {
    border-top-width: 0;
    text-overflow: none; }
  .k-grid .k-grouping-row .k-icon {
    margin-left: -4px;
    margin-right: 8px; }
  .k-grid .k-group-footer td {
    border-style: solid;
    border-width: 1px 0; }
  .k-grid .k-group-footer .k-group-cell + td {
    border-left-width: 1px; }
  .k-grid .k-hierarchy-cell {
    text-align: center;
    padding: 0;
    overflow: visible; }
    .k-grid .k-hierarchy-cell > .k-icon {
      padding: 8px 0;
      width: 100%;
      height: 100%;
      line-height: 1.42857;
      display: inline-block;
      outline: 0; }
  .k-grid .k-hierarchy-cell + td {
    border-left-width: 0; }
  .k-grid[dir="rtl"] thead,
  .k-grid[dir="rtl"] tbody,
  .k-grid[dir="rtl"] tfoot,
  .k-rtl .k-grid thead,
  .k-rtl .k-grid tbody,
  .k-rtl .k-grid tfoot {
    text-align: right; }
  .k-grid[dir="rtl"] th,
  .k-rtl .k-grid th {
    white-space: nowrap; }
  .k-grid[dir="rtl"] .k-grid-header-wrap,
  .k-rtl .k-grid .k-grid-header-wrap {
    border-width: 0 0 0 1px; }
  .k-grid[dir="rtl"] .k-group-indicator .k-button.k-bare, .k-grid[dir="rtl"] .k-group-indicator .k-calendar.k-calendar-range > .k-button,
  .k-grid[dir="rtl"] .k-drag-clue .k-button.k-bare,
  .k-grid[dir="rtl"] .k-drag-clue .k-calendar.k-calendar-range > .k-button,
  .k-rtl .k-grid .k-group-indicator .k-button.k-bare,
  .k-rtl .k-grid .k-group-indicator .k-calendar.k-calendar-range > .k-button,
  .k-rtl .k-grid .k-drag-clue .k-button.k-bare,
  .k-rtl .k-grid .k-drag-clue .k-calendar.k-calendar-range > .k-button {
    margin-left: -2px;
    margin-right: 8px; }
  .k-grid[dir="rtl"] .k-group-indicator .k-link .k-icon,
  .k-grid[dir="rtl"] .k-drag-clue .k-link .k-icon,
  .k-rtl .k-grid .k-group-indicator .k-link .k-icon,
  .k-rtl .k-grid .k-drag-clue .k-link .k-icon {
    margin-left: 4px;
    margin-right: -2px; }
  .k-grid[dir="rtl"] .k-group-indicator,
  .k-rtl .k-grid .k-group-indicator {
    margin-right: 0;
    margin-left: 4px; }
  .k-grid[dir="rtl"] .k-group-indicator + .k-group-indicator,
  .k-rtl .k-grid .k-group-indicator + .k-group-indicator {
    margin-right: 4px; }
  .k-grid[dir="rtl"] .k-grid-content-locked,
  .k-grid[dir="rtl"] .k-grid-footer-locked,
  .k-grid[dir="rtl"] .k-grid-header-locked,
  .k-rtl .k-grid .k-grid-content-locked,
  .k-rtl .k-grid .k-grid-footer-locked,
  .k-rtl .k-grid .k-grid-header-locked {
    border-left-width: 1px;
    border-right-width: 0; }
  .k-grid[dir="rtl"] th:first-child,
  .k-grid[dir="rtl"] td:first-child,
  .k-rtl .k-grid th:first-child,
  .k-rtl .k-grid td:first-child {
    border-left-width: 1px; }
  .k-grid[dir="rtl"] th:last-child,
  .k-grid[dir="rtl"] td:last-child,
  .k-rtl .k-grid th:last-child,
  .k-rtl .k-grid td:last-child {
    border-left-width: 0; }
  .k-grid[dir="rtl"] td.k-hierarchy-cell,
  .k-rtl .k-grid td.k-hierarchy-cell {
    border-left-width: 0; }
  .k-grid[dir="rtl"] .k-hierarchy-cell + td:not(:last-child),
  .k-rtl .k-grid .k-hierarchy-cell + td:not(:last-child) {
    border-left-width: 1px; }
  .k-grid[dir="rtl"] .k-grid-header,
  .k-grid[dir="rtl"] .k-grid-footer,
  .k-rtl .k-grid .k-grid-header,
  .k-rtl .k-grid .k-grid-footer {
    padding-left: 17px;
    padding-right: 0; }
  .k-grid[dir="rtl"] .k-grid-header .k-header:first-child,
  .k-rtl .k-grid .k-grid-header .k-header:first-child {
    border-right-width: 0; }
  .k-grid[dir="rtl"] .k-grid-header .k-filterable > .k-link,
  .k-rtl .k-grid .k-grid-header .k-filterable > .k-link {
    padding-left: calc(calc( 10px + 1.42857em) + 4px);
    padding-right: 12px; }
  .k-grid[dir="rtl"] .k-grid-header .k-grid-filter,
  .k-grid[dir="rtl"] .k-grid-header .k-header-column-menu,
  .k-rtl .k-grid .k-grid-header .k-grid-filter,
  .k-rtl .k-grid .k-grid-header .k-header-column-menu {
    right: auto;
    left: 4px; }
  .k-grid[dir="rtl"] .k-filter-row td:first-child,
  .k-rtl .k-grid .k-filter-row td:first-child {
    border-left-width: 1px; }
  .k-grid[dir="rtl"] .k-filter-row td:last-child,
  .k-rtl .k-grid .k-filter-row td:last-child {
    border-left-width: 0; }
  .k-grid[dir="rtl"] .k-filtercell-operator,
  .k-rtl .k-grid .k-filtercell-operator {
    margin-left: 0;
    margin-right: 4px; }
  .k-grid[dir="rtl"] .k-dirty,
  .k-rtl .k-grid .k-dirty {
    left: auto;
    right: 0; }
  .k-grid[dir="rtl"] .k-edit-cell > .k-textbox,
  .k-grid[dir="rtl"] .k-edit-cell > .k-widget,
  .k-grid[dir="rtl"] .k-edit-cell > .text-box,
  .k-grid[dir="rtl"] .k-grid-edit-row > td > .k-textbox,
  .k-grid[dir="rtl"] .k-grid-edit-row > td > .k-widget,
  .k-grid[dir="rtl"] .k-grid-edit-row > td > .text-box,
  .k-rtl .k-grid .k-edit-cell > .k-textbox,
  .k-rtl .k-grid .k-edit-cell > .k-widget,
  .k-rtl .k-grid .k-edit-cell > .text-box,
  .k-rtl .k-grid .k-grid-edit-row > td > .k-textbox,
  .k-rtl .k-grid .k-grid-edit-row > td > .k-widget,
  .k-rtl .k-grid .k-grid-edit-row > td > .text-box {
    margin-right: calc(-8px - 1px); }
  .k-grid[dir="rtl"] .k-grid-header-wrap.k-auto-scrollable,
  .k-rtl .k-grid .k-grid-header-wrap.k-auto-scrollable {
    margin-left: -1px;
    margin-right: 0; }
  .k-grid[dir="rtl"] .k-grid-header-locked + .k-grid-header-wrap.k-auto-scrollable,
  .k-rtl .k-grid .k-grid-header-locked + .k-grid-header-wrap.k-auto-scrollable {
    margin-left: 0; }
  .k-grid .k-tooltip.k-tooltip-validation {
    display: flex;
    position: absolute;
    width: auto;
    padding: 4px 8px; }
    .k-grid .k-tooltip.k-tooltip-validation .k-callout {
      display: block; }
  .k-grid .k-animation-container-fixed .k-tooltip.k-tooltip-validation {
    position: static; }
  .k-grid .k-dirty-cell {
    position: relative; }
  .k-grid .k-dirty {
    border-width: 5px;
    left: 0;
    right: auto; }
  .k-grid .k-grid-content-locked + .k-grid-content {
    box-sizing: content-box; }

.k-grid-toolbar {
  padding: 8px;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: inherit;
  text-overflow: clip;
  cursor: default;
  display: block; }
  .k-grid-toolbar .k-button {
    vertical-align: middle; }
  .k-grid-toolbar .k-button + .k-button {
    margin-left: 4px; }

.k-grouping-header {
  display: block;
  padding: 8px 8px;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: inherit;
  line-height: calc( 10px + 1.42857em); }

.k-grouping-header-flex {
  display: flex;
  flex-shrink: 0;
  padding: 0; }
  .k-grouping-header-flex > .k-indicator-container {
    display: inline-flex;
    margin: 0;
    padding: 8px 0 8px 8px; }
    .k-grouping-header-flex > .k-indicator-container:last-child {
      flex-grow: 1; }

.k-group-indicator,
.k-drag-clue {
  border-radius: 4px;
  margin: 0;
  padding: 4px 8px;
  border-width: 1px;
  border-style: solid;
  line-height: 1.42857;
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  vertical-align: top;
  box-sizing: content-box; }
  .k-group-indicator .k-link,
  .k-group-indicator .k-button.k-bare,
  .k-group-indicator .k-calendar.k-calendar-range > .k-button,
  .k-drag-clue .k-link,
  .k-drag-clue .k-button.k-bare,
  .k-drag-clue .k-calendar.k-calendar-range > .k-button {
    padding: 0;
    border-width: 0;
    display: inline-flex;
    align-items: center; }
  .k-group-indicator .k-link .k-icon,
  .k-drag-clue .k-link .k-icon {
    margin-left: -2px;
    margin-right: 4px; }
  .k-group-indicator .k-button.k-bare, .k-group-indicator .k-calendar.k-calendar-range > .k-button,
  .k-drag-clue .k-button.k-bare,
  .k-drag-clue .k-calendar.k-calendar-range > .k-button {
    margin-left: 8px;
    margin-right: -2px;
    padding: 0;
    width: auto;
    height: auto;
    opacity: .5; }
    .k-group-indicator .k-button.k-bare::before, .k-group-indicator .k-toolbar .k-bare.k-picker-wrap::before, .k-toolbar .k-group-indicator .k-bare.k-picker-wrap::before, .k-group-indicator
    .k-toolbar .k-bare.k-dropdown-wrap::before,
    .k-toolbar .k-group-indicator .k-bare.k-dropdown-wrap::before, .k-group-indicator .k-calendar.k-calendar-range > .k-button::before, .k-group-indicator .k-toolbar .k-calendar.k-calendar-range > .k-picker-wrap::before, .k-toolbar .k-group-indicator .k-calendar.k-calendar-range > .k-picker-wrap::before, .k-group-indicator
    .k-toolbar .k-calendar.k-calendar-range > .k-dropdown-wrap::before,
    .k-toolbar .k-group-indicator .k-calendar.k-calendar-range > .k-dropdown-wrap::before, .k-group-indicator .k-button.k-bare::after, .k-group-indicator .k-calendar.k-calendar-range > .k-button::after,
    .k-drag-clue .k-button.k-bare::before,
    .k-drag-clue .k-toolbar .k-bare.k-picker-wrap::before, .k-toolbar
    .k-drag-clue .k-bare.k-picker-wrap::before,
    .k-drag-clue
    .k-toolbar .k-bare.k-dropdown-wrap::before,
    .k-toolbar
    .k-drag-clue .k-bare.k-dropdown-wrap::before,
    .k-drag-clue .k-calendar.k-calendar-range > .k-button::before,
    .k-drag-clue .k-toolbar .k-calendar.k-calendar-range > .k-picker-wrap::before, .k-toolbar
    .k-drag-clue .k-calendar.k-calendar-range > .k-picker-wrap::before,
    .k-drag-clue
    .k-toolbar .k-calendar.k-calendar-range > .k-dropdown-wrap::before,
    .k-toolbar
    .k-drag-clue .k-calendar.k-calendar-range > .k-dropdown-wrap::before,
    .k-drag-clue .k-button.k-bare::after,
    .k-drag-clue .k-calendar.k-calendar-range > .k-button::after {
      display: none; }
    .k-group-indicator .k-button.k-bare:hover, .k-group-indicator .k-calendar.k-calendar-range > .k-button:hover,
    .k-drag-clue .k-button.k-bare:hover,
    .k-drag-clue .k-calendar.k-calendar-range > .k-button:hover {
      opacity: 1; }

.k-group-indicator {
  margin-right: 4px; }

.k-group-indicator + .k-group-indicator {
  margin-left: 4px; }

.k-grouping-dropclue {
  width: 12px;
  height: calc( 10px + 1.42857em);
  position: absolute;
  box-sizing: content-box; }
  .k-grouping-dropclue::before, .k-grouping-dropclue::after {
    display: inline-block;
    content: '';
    position: absolute; }
  .k-grouping-dropclue::before {
    border-width: 6px;
    border-style: solid;
    border-left-color: transparent;
    border-right-color: transparent;
    border-bottom-color: transparent;
    top: 0; }
  .k-grouping-dropclue::after {
    width: 2px;
    height: calc(100% - 6px);
    top: 6px;
    left: calc(6px - (2px / 2)); }

.k-grid-header-wrap,
.k-grid-footer-wrap {
  width: 100%;
  border-width: 0 1px 0 0;
  border-style: solid;
  border-color: inherit;
  position: relative;
  overflow: hidden; }

.k-grid-header-wrap.k-auto-scrollable {
  margin-right: -1px; }

.k-grid-header-locked + .k-grid-header-wrap.k-auto-scrollable {
  margin-right: 0; }
  .k-ie .k-grid-header-locked + .k-grid-header-wrap.k-auto-scrollable {
    display: inline-block; }

.k-grid-header,
.k-grid-footer {
  flex: 0 0 auto;
  padding-right: 17px;
  border-width: 0;
  border-style: solid;
  border-color: inherit; }
  .k-grid-header table,
  .k-grid-footer table {
    table-layout: fixed; }

div.k-grid-header,
div.k-grid-footer {
  display: flex;
  flex-direction: row;
  align-items: stretch; }

.k-ie div.k-grid-header {
  display: block; }

.k-grid-header {
  border-bottom-width: 1px; }
  .k-grid-header table {
    margin-bottom: -1px; }
  .k-grid-header tr + tr th {
    border-top-width: 1px; }
  .k-grid-header .k-header {
    position: relative;
    vertical-align: bottom; }
    .k-grid-header .k-header:first-child {
      border-left-width: 0; }
    .k-grid-header .k-header.k-first {
      border-left-width: 1px; }
  .k-grid-header .k-header > .k-link {
    margin: -8px -12px;
    padding: 8px 12px;
    line-height: inherit;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis; }
  .k-grid-header .k-filterable > .k-link {
    padding-right: calc(calc( 10px + 1.42857em) + 4px); }
  .k-grid-header .k-header > .k-link:focus {
    text-decoration: none; }
  .k-grid-header .k-grid-filter,
  .k-grid-header .k-header-column-menu {
    padding: 4px;
    width: calc( 10px + 1.42857em);
    height: calc( 10px + 1.42857em);
    box-sizing: border-box;
    outline: 0;
    line-height: 1.42857;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    position: absolute;
    right: 4px;
    bottom: calc( 8px + 0.71429em + (-1 * calc( 4px + 0.71429em) ));
    z-index: 1; }
  .k-grid-header .k-header-column-menu {
    margin: 0;
    margin-top: calc( (1.42857em + (-1 * calc( 10px + 1.42857em))) / 2);
    margin-bottom: calc( (1.42857em + (-1 * calc( 10px + 1.42857em))) / 2);
    margin-right: -12px; }
  .k-grid-header .k-header > .k-link > .k-icon.k-i-sort-desc-sm, .k-grid-header .k-header > .k-link > .k-icon.k-i-sort-asc-sm {
    vertical-align: text-top;
    margin-left: 7px; }
  .k-grid-header .k-sort-order {
    display: inline-block;
    font-size: 12px;
    height: 16px;
    margin-left: -4px;
    vertical-align: top;
    margin-top: 2px; }

.k-grid-footer {
  border-width: 1px 0 0; }
  .k-grid-footer td {
    height: 1.42857em; }

.k-grid-filter-popup {
  min-width: 200px; }

.k-filter-row {
  line-height: 1.42857; }
  .k-filter-row td,
  .k-filter-row th {
    border-width: 0 0 1px 1px;
    white-space: nowrap;
    padding: 8px 8px; }
  .k-filter-row td:first-child {
    border-left-width: 0; }
  .k-filter-row .k-multiselect {
    height: auto; }
  .k-filter-row .k-filtercell .k-widget.k-sized-input {
    flex: 0 1 auto; }

.k-filtercell {
  width: auto;
  display: flex; }
  .k-filtercell > span,
  .k-filtercell .k-filtercell-wrapper {
    display: flex;
    flex: 1 1 0; }
    .k-filtercell > span > label,
    .k-filtercell .k-filtercell-wrapper > label {
      vertical-align: middle; }
  .k-filtercell .k-filtercell-wrapper > .k-widget {
    width: 100%; }
  .k-filtercell > span .k-button {
    visibility: visible;
    pointer-events: all; }
  .k-filtercell > span .k-button,
  .k-filtercell > span .k-dropdown-operator {
    margin-left: 4px; }
  .k-filtercell > span .k-numerictextbox {
    width: auto; }
  .k-filtercell .k-filtercell-operator > .k-button.k-clear-button-visible {
    visibility: visible;
    height: calc( 10px + 1.42857em); }
  .k-filtercell .k-filtercell-operator > .k-button:not(.k-clear-button-visible) {
    visibility: hidden;
    pointer-events: none; }
  .k-filtercell .k-filtercell-operator {
    margin-left: 4px; }
  .k-filtercell .k-widget:not(.k-dropdown-operator),
  .k-filtercell .k-filtercell-wrapper > .k-textbox {
    display: flex;
    flex: 1 1 auto; }
  .k-filtercell .k-filtercell-wrapper > .k-textbox {
    width: 100%;
    min-width: 0; }
  .k-filtercell .k-autocomplete .k-input,
  .k-filtercell .k-dropdown-wrap .k-input,
  .k-filtercell .k-numeric-wrap .k-input,
  .k-filtercell .k-picker-wrap .k-input,
  .k-filtercell .k-selectbox .k-input,
  .k-filtercell .k-textbox > input {
    padding-left: 0;
    padding-right: 0;
    text-indent: 8px; }

.k-grid-content,
.k-grid-content-locked {
  border-color: inherit; }
  .k-grid-content table,
  .k-grid-content-locked table {
    table-layout: fixed; }
  .k-grid-content tr:last-child td,
  .k-grid-content-locked tr:last-child td {
    border-bottom-width: 0; }

.k-grid-content {
  width: 100%;
  min-height: 0;
  overflow: auto;
  overflow-x: auto;
  overflow-y: scroll;
  position: relative;
  flex: 1; }
  .k-grid-content .k-button {
    vertical-align: middle;
    margin: 0 .16em; }

.k-virtual-scrollable-wrap {
  height: 100%;
  overflow-y: hidden;
  position: relative; }

.k-grid-edit-row td {
  text-overflow: clip; }

.k-grid-edit-row .k-widget,
.k-grid-edit-row .k-button,
.k-grid-edit-row .k-textbox,
.k-grid-edit-row .k-input.k-textbox {
  height: auto; }

.k-grid-edit-row .k-edit-cell {
  padding-top: 0;
  padding-bottom: 0; }

.k-grid-edit-row .k-dirty-cell {
  overflow: visible; }

.k-edit-cell > .k-textbox,
.k-edit-cell > .k-widget,
.k-edit-cell > .text-box,
.k-grid-edit-row > td > .k-textbox,
.k-grid-edit-row > td > .k-widget,
.k-grid-edit-row > td > .text-box {
  margin-left: calc(-8px - 1px); }

.k-grid-edit-row td > .k-textbox,
.k-grid-edit-row td > .k-widget,
.k-edit-cell > .k-textbox,
.k-edit-cell > .k-widget {
  width: calc(100% + ((8px + 1px) * 2)); }

.k-grid-edit-row td > .k-textbox,
.k-grid-edit-row td > .k-widget,
.k-command-cell > .k-button,
.k-edit-cell > .k-textbox,
.k-edit-cell > .k-widget {
  margin-top: calc( (1.42857em + (-1 * calc( 10px + 1.42857em))) / 2);
  margin-bottom: calc( (1.42857em + (-1 * calc( 10px + 1.42857em))) / 2);
  vertical-align: middle; }

.k-grid > .k-resize-handle,
.k-grid-header .k-resize-handle {
  height: 25px;
  cursor: col-resize;
  position: absolute;
  z-index: 2; }

.k-grid-pager {
  padding: 8px;
  border-width: 1px 0 0;
  border-color: inherit; }

.k-grid-virtual .k-grid-content .k-grid-table-wrap {
  float: left;
  width: 100%; }

.k-grid-virtual .k-grid-content .k-grid-table {
  position: relative;
  float: left;
  z-index: 1; }

.k-grid-virtual .k-grid-content > .k-height-container {
  position: relative;
  float: left; }

.k-grid-virtual .k-grid-content::after {
  content: "";
  display: block;
  clear: both; }

.k-grid-add-row td {
  border-bottom-style: solid;
  border-bottom-width: 1px; }

/* Locked columns */
.k-grid-lockedcolumns {
  white-space: nowrap; }

.k-grid-content,
.k-grid-content-locked,
.k-pager-wrap {
  white-space: normal; }

.k-grid-content-locked,
.k-grid-footer-locked,
.k-grid-header-locked {
  flex: 0 0 auto;
  display: inline-block;
  vertical-align: top;
  overflow: hidden;
  position: relative;
  border-style: solid;
  border-width: 0 1px 0 0; }
  .k-grid-content-locked + .k-grid-content.k-auto-scrollable,
  .k-grid-footer-locked + .k-grid-content.k-auto-scrollable,
  .k-grid-header-locked + .k-grid-content.k-auto-scrollable {
    display: inline-block; }

.k-grid-content,
.k-grid-footer-wrap,
.k-grid-header-wrap {
  flex: 1 1 auto;
  display: inline-block;
  vertical-align: top; }
  .k-grid-content.k-auto-scrollable,
  .k-grid-footer-wrap.k-auto-scrollable,
  .k-grid-header-wrap.k-auto-scrollable {
    display: block; }

.k-grid-header-locked > table,
.k-grid-header-wrap > table {
  margin-bottom: -1px; }

.k-header.k-drag-clue {
  overflow: hidden; }

.k-grid.k-grid-no-scrollbar .k-grid-header {
  padding: 0; }

.k-grid.k-grid-no-scrollbar .k-grid-header-wrap {
  border-width: 0; }

.k-grid.k-grid-no-scrollbar .k-grid-content {
  overflow-y: auto; }

.k-loading-pdf-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 100; }
  .k-loading-pdf-mask > .k-i-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 64px; }
  .k-loading-pdf-mask .k-loading-pdf-progress {
    margin: auto;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0; }

.k-grid-pdf-export-element {
  position: absolute;
  left: -10000px;
  top: -10000px; }
  .k-grid-pdf-export-element .k-filter-row {
    display: none; }

.k-filter-menu > div:not(.k-animation-container),
.k-filter-menu .k-filter-menu-container {
  padding: 0.5em 1em; }

.k-filter-menu > div > div > .k-button {
  margin: 0.5em 1% 0;
  width: 48%;
  box-sizing: border-box;
  display: inline-block; }

.k-filter-menu .k-widget,
.k-filter-menu .k-radio-list,
.k-filter-menu .k-textbox {
  margin: 0.5em 0;
  width: 100%;
  display: block; }

.k-filter-menu .k-widget.k-filter-and {
  width: 6em;
  margin: 1em 0; }

.k-filter-menu .k-action-buttons {
  margin: 1em -1em -0.5em; }
  .k-filter-menu .k-action-buttons .k-button {
    margin: 0; }

.k-multicheck-wrap {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
  white-space: nowrap; }
  .k-multicheck-wrap .k-item + .k-item {
    margin-top: 0.5em; }
  .k-multicheck-wrap .k-label {
    margin: 0;
    display: flex;
    align-items: center;
    cursor: pointer; }
    .k-multicheck-wrap .k-label input {
      margin: 0 0.25em; }

.k-filter-selected-items {
  font-weight: bold;
  line-height: normal;
  white-space: nowrap;
  margin: 1em 0 0.5em; }

.k-autofitting {
  width: auto !important;
  table-layout: auto !important; }
  .k-autofitting th.k-header,
  .k-autofitting td {
    white-space: nowrap !important; }
  .k-autofitting .k-detail-row {
    display: none !important; }

.k-column-list {
  padding: 0;
  margin: 0;
  list-style: none;
  max-height: 200px;
  overflow-x: hidden;
  overflow-y: auto; }

.k-column-chooser-title,
.k-column-list-item {
  padding: 4px 8px;
  padding-right: 32px; }

.k-column-list-item {
  display: block;
  margin: 0;
  cursor: pointer; }
  .k-column-list-item .k-checkbox,
  .k-column-list-item .k-checkbox-label {
    vertical-align: middle; }

.k-columnmenu-item {
  padding: 6px 12px;
  cursor: pointer; }
  .k-columnmenu-item > .k-icon {
    margin-right: 4px; }

.k-columnmenu-item-wrapper {
  border-bottom: 1px solid;
  border-bottom-color: inherit; }
  .k-columnmenu-item-wrapper:last-child {
    border-bottom-width: 0; }

.k-columnmenu-item-content {
  overflow: hidden; }
  .k-columnmenu-item-content .k-column-list-item {
    padding-left: calc( 28px + 4px); }
  .k-columnmenu-item-content .k-filter-menu .k-filter-menu-container {
    padding: 1em 1em; }
    .k-columnmenu-item-content .k-filter-menu .k-filter-menu-container .k-columnmenu-actions {
      padding: 1em 0 0; }
  .k-columnmenu-item-content .k-columnmenu-actions {
    display: flex;
    padding: 1em 1em; }
    .k-columnmenu-item-content .k-columnmenu-actions .k-button {
      flex: 1 0 auto;
      width: auto;
      margin: 0 0.5em; }
      .k-columnmenu-item-content .k-columnmenu-actions .k-button:first-child {
        margin-left: 0; }
      .k-columnmenu-item-content .k-columnmenu-actions .k-button:last-child {
        margin-right: 0; }

.k-grid-header,
.k-header,
.k-grid-header-wrap,
.k-grouping-header,
.k-grouping-header .k-group-indicator,
.k-grid td,
.k-grid-footer,
.k-grid-footer-wrap,
.k-grid-content-locked,
.k-grid-footer-locked,
.k-grid-header-locked,
.k-filter-row > td,
.k-filter-row > th {
  border-color: #c2c2c2; }

.k-grid-header,
.k-grid-toolbar,
.k-grouping-header,
.k-grid-add-row,
.k-grid-footer,
.k-drag-clue {
  color: #000000;
  background-color: #f5f5f5; }

.k-grid-content {
  background-color: #ffffff; }

.k-group-footer td,
.k-grouping-row td,
tbody .k-group-cell {
  color: #000000;
  background-color: #ededed; }

.k-grid-footer td,
.k-group-footer td,
.k-grouping-row td {
  font-weight: bold; }

.k-grouping-dropclue::before {
  border-color: #000000 transparent transparent; }

.k-grouping-dropclue::after {
  background-color: #000000; }

.k-grid {
  background-clip: padding-box; }
  .k-ie11 .k-grid,
  .k-edge12 .k-grid,
  .k-edge13 .k-grid {
    background-clip: border-box; }
  .k-grid tr.k-alt {
    background-color: rgba(0, 0, 0, 0.04); }
  .k-grid tr.k-state-selected,
  .k-grid td.k-state-selected {
    color: inherit;
    background-color: rgba(96, 125, 139, 0.25); }
  .k-grid td.k-state-focused,
  .k-grid th.k-state-focused {
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
  .k-grid .k-grid-filter,
  .k-grid .k-header-column-menu,
  .k-grid .k-hierarchy-cell .k-icon {
    border-width: 0;
    color: #000000; }
  .k-grid .k-grouping-row {
    background-color: #ededed; }
    .k-grid .k-grouping-row .k-icon {
      color: #000000;
      text-decoration: none; }
  .k-grid .k-tooltip.k-tooltip-validation {
    color: #ffffff;
    background-color: #607d8b; }
    .k-grid .k-tooltip.k-tooltip-validation .k-callout-n {
      border-bottom-color: #607d8b; }
    .k-grid .k-tooltip.k-tooltip-validation .k-callout-e {
      border-left-color: #607d8b; }
    .k-grid .k-tooltip.k-tooltip-validation .k-callout-s {
      border-top-color: #607d8b; }
    .k-grid .k-tooltip.k-tooltip-validation .k-callout-w {
      border-right-color: #607d8b; }
  .k-grid .k-dirty {
    border-color: currentColor transparent transparent currentColor; }
  .k-grid[dir="rtl"] .k-dirty,
  .k-rtl .k-grid .k-dirty {
    border-color: currentColor currentColor transparent transparent; }

col.k-sorted,
th.k-sorted {
  background-color: rgba(0, 0, 0, 0.02); }

.k-grid-header .k-i-sort-asc-sm,
.k-grid-header .k-i-sort-desc-sm,
.k-grid-header .k-sort-order {
  color: #607d8b; }

.k-grid-header .k-grid-filter:hover {
  color: #ffffff;
  background-color: #90a4ae; }

.k-grid-header .k-grid-filter:focus {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1); }

.k-grid-header .k-grid-filter.k-state-active {
  color: #ffffff;
  background-color: #607d8b; }

.k-tabstrip {
  border-width: 0;
  background-color: transparent;
  display: flex;
  flex-direction: column; }
  .k-tabstrip > .k-tabstrip-items {
    border: 0 solid transparent;
    border-color: inherit;
    display: flex;
    flex-direction: row;
    flex: 0 0 auto;
    position: relative; }
    .k-tabstrip > .k-tabstrip-items .k-item {
      margin: 0;
      padding: 0;
      border: 1px solid transparent;
      position: relative; }
    .k-tabstrip > .k-tabstrip-items .k-item.k-tab-on-top {
      z-index: 1; }
    .k-tabstrip > .k-tabstrip-items .k-item .k-link {
      padding: 6px 12px;
      color: inherit;
      cursor: pointer;
      display: inline-flex;
      vertical-align: middle;
      flex: 1 1 0;
      flex-direction: row;
      align-content: center;
      align-items: center; }
      .k-tabstrip > .k-tabstrip-items .k-item .k-link[data-type="remove"] {
        padding: 6px;
        flex: none; }
  .k-tabstrip > .k-content {
    padding: 4px 8px;
    border-width: 1px;
    border-style: solid;
    border-color: inherit;
    display: none;
    overflow: auto;
    flex: 1 1 auto; }
    .k-tabstrip > .k-content.k-state-active {
      display: block; }
  .k-tabstrip .k-tabstrip-items .k-loading {
    width: 20%;
    height: 0;
    border: 0;
    border-top: 1px solid transparent;
    border-color: inherit;
    background: none;
    position: absolute;
    top: 0;
    left: 0;
    transition: width .2s linear; }
    .k-tabstrip .k-tabstrip-items .k-loading.k-complete {
      width: 100%;
      border-top-width: 0; }
  .k-tabstrip.k-tabstrip-scrollable {
    position: relative; }
    .k-tabstrip.k-tabstrip-scrollable > .k-content {
      border-width: 1px; }
    .k-tabstrip.k-tabstrip-scrollable > .k-tabstrip-items {
      border-width: 0;
      white-space: nowrap;
      overflow: hidden; }
    .k-tabstrip.k-tabstrip-scrollable > .k-tabstrip-prev {
      position: absolute;
      left: 0; }
    .k-tabstrip.k-tabstrip-scrollable > .k-tabstrip-next {
      position: absolute;
      right: 0; }
    .k-ie11 .k-tabstrip.k-tabstrip-scrollable > .k-tabstrip-prev,
    .k-ie11 .k-tabstrip.k-tabstrip-scrollable > .k-tabstrip-next {
      top: 0; }

.k-tabstrip-top > .k-tabstrip-items {
  border-bottom-width: 1px; }
  .k-tabstrip-top > .k-tabstrip-items .k-item {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-bottom: -1px; }
  .k-tabstrip-top > .k-tabstrip-items .k-item + .k-item {
    margin-left: 0; }
  .k-tabstrip-top > .k-tabstrip-items .k-item.k-state-active {
    border-bottom-color: transparent; }

.k-tabstrip-top > .k-content {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  border-top-width: 0; }

.k-tabstrip-bottom > .k-tabstrip-items {
  border-top-width: 1px; }
  .k-tabstrip-bottom > .k-tabstrip-items .k-item {
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    margin-top: -1px; }
  .k-tabstrip-bottom > .k-tabstrip-items .k-item + .k-item {
    margin-left: 0; }
  .k-tabstrip-bottom > .k-tabstrip-items .k-item.k-state-active {
    border-top-color: transparent; }

.k-tabstrip-bottom > .k-content {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-width: 0; }

.k-tabstrip-left {
  flex-direction: row; }
  .k-tabstrip-left > .k-tabstrip-items {
    border-right-width: 1px;
    display: inline-flex;
    flex-direction: column; }
    .k-tabstrip-left > .k-tabstrip-items .k-item {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      margin-right: -1px; }
    .k-tabstrip-left > .k-tabstrip-items .k-item + .k-item {
      margin-top: 0; }
    .k-tabstrip-left > .k-tabstrip-items .k-item.k-state-active {
      border-right-color: transparent; }
  .k-tabstrip-left > .k-content {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    margin: 0 !important;
    border-left-width: 0; }

.k-tabstrip-right {
  flex-direction: row-reverse; }
  .k-tabstrip-right > .k-tabstrip-items {
    border-left-width: 1px;
    display: inline-flex;
    flex-direction: column; }
    .k-tabstrip-right > .k-tabstrip-items .k-item {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      margin-left: -1px; }
    .k-tabstrip-right > .k-tabstrip-items .k-item + .k-item {
      margin-top: 0; }
    .k-tabstrip-right > .k-tabstrip-items .k-item.k-state-active {
      border-left-color: transparent; }
  .k-tabstrip-right > .k-content {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    margin: 0 !important;
    border-right-width: 0; }

.k-tabstrip {
  border-color: #c2c2c2; }
  .k-tabstrip .k-item {
    border-color: transparent;
    color: #607d8b;
    background-color: transparent;
    background-clip: padding-box; }
    .k-ie11 .k-tabstrip .k-item,
    .k-edge12 .k-tabstrip .k-item,
    .k-edge13 .k-tabstrip .k-item {
      background-clip: border-box; }
    .k-tabstrip .k-item:hover, .k-tabstrip .k-item.k-state-hover {
      border-color: transparent;
      color: #90a4ae;
      background-color: transparent; }
    .k-tabstrip .k-item.k-state-active {
      border-color: #c2c2c2;
      color: #000000;
      background-color: #ffffff; }
  .k-tabstrip > .k-content {
    border-color: #ffffff;
    color: #000000;
    background-color: #ffffff;
    background-clip: padding-box; }
    .k-ie11 .k-tabstrip > .k-content,
    .k-edge12 .k-tabstrip > .k-content,
    .k-edge13 .k-tabstrip > .k-content {
      background-clip: border-box; }

.k-tabstrip-top > .k-tabstrip-items .k-item.k-state-active {
  border-bottom-color: #ffffff; }

.k-tabstrip-bottom > .k-tabstrip-items .k-item.k-state-active {
  border-top-color: #ffffff; }

.k-tabstrip-left > .k-tabstrip-items .k-item.k-state-active {
  border-right-color: #ffffff; }

.k-tabstrip-right > .k-tabstrip-items .k-item.k-state-active {
  border-left-color: #ffffff; }

.k-toolbar {
  padding: 8px 8px;
  border-width: 1px;
  line-height: 1.42857;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  box-shadow: none; }
  .k-toolbar.k-toolbar-resizable {
    flex-wrap: nowrap; }
  .k-toolbar > * {
    display: inline-flex;
    align-items: stretch;
    align-content: center;
    vertical-align: middle;
    margin-left: 8px; }
  .k-toolbar .k-overflow-anchor + * {
    margin-left: 0; }
  .k-toolbar .k-button,
  .k-toolbar .k-button-group,
  .k-toolbar .k-separator,
  .k-toolbar .k-split-button,
  .k-toolbar .k-textbox,
  .k-toolbar .k-widget,
  .k-toolbar label {
    vertical-align: middle; }
  .k-toolbar .k-button {
    padding: 4px 8px; }
    .k-toolbar .k-button::before, .k-toolbar .k-picker-wrap::before, .k-toolbar .k-dropdown-wrap::before {
      border-radius: 0; }
  .k-toolbar > .k-button:not(.k-overflow-anchor) + .k-button {
    margin-left: 0; }
  .k-toolbar .k-button-group .k-button {
    border-radius: 0; }
  .k-toolbar .k-button-group .k-group-start,
  .k-toolbar .k-button-group .k-button:first-child {
    border-radius: 0 0 0 0; }
  .k-toolbar .k-button-group .k-group-end,
  .k-toolbar .k-button-group .k-button:last-child {
    border-radius: 0 0 0 0; }
  .k-toolbar .k-split-button .k-button {
    border-radius: 4px 0 0 4px; }
  .k-toolbar .k-split-button .k-button.k-split-button-arrow {
    border-radius: 0 4px 4px 0;
    margin-left: -1px;
    padding: 4px; }
  .k-toolbar .k-overflow-anchor {
    border-radius: 0;
    padding: 8px;
    width: calc( 1.42857em + 8px + 2px + 16px);
    height: 100%;
    margin: 0;
    border-width: 0 0 0 1px;
    border-color: inherit;
    background-clip: padding-box;
    box-sizing: border-box;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0; }
    .k-toolbar .k-overflow-anchor::before {
      display: block; }
  .k-toolbar .k-separator {
    padding: 4px 0;
    margin: 0 8px; }
    .k-toolbar .k-separator + * {
      margin-left: 0; }

.k-overflow-container .k-overflow-tool-group {
  display: block; }

.k-overflow-container .k-button-group {
  display: flex;
  flex-direction: column; }

.k-overflow-container .k-tool.k-state-disabled,
.k-overflow-container .k-overflow-hidden {
  display: none; }

.k-rtl .k-toolbar .k-overflow-anchor {
  right: auto;
  left: 0;
  border-width: 0 1px 0 0; }

.k-toolbar {
  background-clip: padding-box; }
  .k-ie11 .k-toolbar,
  .k-edge12 .k-toolbar,
  .k-edge13 .k-toolbar {
    background-clip: border-box; }
  .k-toolbar .k-split-button:focus,
  .k-toolbar .k-split-button .k-state-focused {
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.06); }
  .k-toolbar .k-split-button .k-button:focus,
  .k-toolbar .k-split-button .k-button.k-state-focused {
    border-color: #c2c2c2;
    box-shadow: inset 0 0 0 1px #c2c2c2; }
  .k-toolbar .k-split-button .k-button:active,
  .k-toolbar .k-split-button .k-button.k-state-active {
    box-shadow: none; }
  .k-toolbar .k-split-button.k-state-disabled,
  .k-toolbar .k-split-button.k-state-disabled .k-button {
    box-shadow: none; }
  .k-toolbar .k-overflow-anchor {
    color: inherit;
    background: transparent; }
  .k-toolbar .k-state-border-up,
  .k-toolbar .k-state-border-down {
    box-shadow: none; }
    .k-toolbar .k-state-border-up::before,
    .k-toolbar .k-state-border-up .k-button::before, .k-toolbar .k-state-border-up .k-picker-wrap::before, .k-toolbar .k-state-border-up .k-dropdown-wrap::before,
    .k-toolbar .k-state-border-down::before,
    .k-toolbar .k-state-border-down .k-button::before, .k-toolbar .k-state-border-down .k-picker-wrap::before, .k-toolbar .k-state-border-down .k-dropdown-wrap::before {
      opacity: 0; }
  .k-toolbar .k-separator {
    border-color: inherit; }
  .k-toolbar .k-button.k-state-disabled::after {
    opacity: 0; }
  .k-toolbar .k-overflow-anchor {
    border-width: 0; }

.k-spreadsheet {
  width: 800px;
  height: 600px;
  display: flex;
  flex-direction: column;
  cursor: default;
  position: relative; }
  .k-spreadsheet .k-vertical-align-center {
    position: relative;
    top: 50%;
    transform: translateY(-50%); }
  .k-spreadsheet .k-vertical-align-bottom {
    position: relative;
    top: 100%;
    transform: translateY(-100%); }

.k-spreadsheet .k-tabstrip-wrapper {
  border-width: 0 0 1px;
  border-style: solid;
  border-color: inherit;
  position: relative; }

.k-spreadsheet-quick-access-toolbar {
  padding: 8px;
  display: inline-flex;
  flex-direction: row;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0; }
  .k-spreadsheet-quick-access-toolbar .k-button:not(:hover) {
    border-color: transparent;
    color: inherit;
    background-color: transparent;
    background-image: none; }

.k-spreadsheet-tabstrip {
  padding-top: 8px; }

.k-spreadsheet-tabstrip .k-loading {
  display: none; }

.k-spreadsheet-tabstrip .k-content {
  border-radius: 0;
  padding: 0;
  border-width: 0;
  overflow: hidden; }

.k-spreadsheet-toolbar {
  border-width: 0; }
  .k-spreadsheet-toolbar [data-tool="backgroundColor"],
  .k-spreadsheet-toolbar [data-tool="textColor"],
  .k-spreadsheet-toolbar [data-tool="borders"],
  .k-spreadsheet-toolbar [data-tool="alignment"],
  .k-spreadsheet-toolbar [data-tool="merge"],
  .k-spreadsheet-toolbar [data-tool="freeze"] {
    width: auto;
    min-width: 1.42857em; }
  .k-spreadsheet-toolbar [data-tool="fontSize"] {
    width: 5em; }
  .k-spreadsheet-toolbar [data-tool="fontFamily"] {
    width: 6em; }
  .k-spreadsheet-toolbar [data-tool="format"] {
    width: 4em; }

.k-spreadsheet-action-bar {
  border-width: 0 0 1px;
  border-style: solid;
  border-color: inherit;
  font-size: 12px;
  font-family: Arial, Verdana, sans-serif;
  position: relative;
  display: flex;
  flex-direction: row; }

.k-spreadsheet-name-editor {
  width: 10em;
  border-width: 0 1px 0 0;
  border-style: solid;
  border-color: inherit; }
  .k-spreadsheet-name-editor .k-combobox {
    width: 100%;
    font-size: inherit; }
  .k-spreadsheet-name-editor .k-combobox .k-dropdown-wrap {
    border-radius: 0;
    border-width: 0; }
  .k-spreadsheet-name-editor .k-combobox .k-select {
    border-radius: 0; }

.k-spreadsheet-formula-bar {
  border-color: inherit;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1; }
  .k-spreadsheet-formula-bar::before {
    font-size: 16px;
    font-family: 'WebComponentsIcons';
    font-style: normal;
    font-variant: normal;
    font-weight: normal;
    line-height: 1;
    padding: 0 4px;
    border-width: 0 1px 0 0;
    border-style: solid;
    border-color: inherit; }
  .k-spreadsheet-formula-bar .k-tooltip,
  .k-spreadsheet-formula-bar .k-group-header,
  .k-spreadsheet-formula-bar .k-list-scroller {
    display: none; }
  .k-spreadsheet-formula-bar .k-spreadsheet-formula-input {
    padding: 4px 8px;
    line-height: 1.42857; }

.k-spreadsheet-formula-input {
  outline: 0;
  white-space: pre;
  flex: 1;
  box-sizing: border-box; }
  .k-spreadsheet-formula-input > .k-syntax-func.k-syntax-at-point,
  .k-spreadsheet-formula-input > .k-syntax-bool.k-syntax-at-point,
  .k-spreadsheet-formula-input > .k-syntax-ref.k-syntax-at-point,
  .k-spreadsheet-formula-input > .k-syntax-str.k-syntax-at-point,
  .k-spreadsheet-formula-input > .k-syntax-num.k-syntax-at-point {
    text-decoration: underline; }
  .k-spreadsheet-formula-input > .k-series-a,
  .k-spreadsheet-formula-input > .k-series-b,
  .k-spreadsheet-formula-input > .k-series-c,
  .k-spreadsheet-formula-input > .k-series-d {
    background-color: transparent; }

.k-spreadsheet-formula-list {
  min-width: 100px; }
  .k-spreadsheet-formula-list .k-item {
    padding: 4px 8px; }

.k-spreadsheet-cell-editor {
  padding: 0 3px;
  line-height: 20px;
  display: none;
  overflow: hidden;
  position: absolute;
  z-index: 2000; }

.k-spreadsheet-sheets-bar {
  padding: 0 4px 4px;
  border-width: 1px 0 0;
  border-style: solid;
  border-color: inherit;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative; }

.k-spreadsheet-sheets-bar-add {
  margin-right: 4px; }
  .k-spreadsheet-sheets-bar-add:not(:hover) {
    border-color: transparent;
    color: inherit;
    background: none; }

.k-spreadsheet-sheets-items {
  margin-top: -1px;
  flex: 1;
  overflow: hidden; }
  .k-spreadsheet-sheets-items .k-tabstrip-items {
    margin: 0 calc( 10px + 1.42857em) !important;
    border-width: 0; }
  .k-spreadsheet-sheets-items .k-tabstrip-prev {
    left: 0 !important; }
  .k-spreadsheet-sheets-items .k-tabstrip-next {
    right: 0 !important; }
  .k-spreadsheet-sheets-items .k-item .k-link {
    display: inline-block; }
  .k-spreadsheet-sheets-items .k-item .k-spreadsheet-sheets-remove {
    margin-right: 0.5em;
    margin-left: -0.5em;
    padding: 0;
    line-height: 1;
    vertical-align: middle; }

.k-spreadsheet-view {
  height: auto !important;
  border-color: inherit;
  font-size: 12px;
  font-family: Arial, Verdana, Sans-serif;
  flex: 1;
  position: relative; }

.k-spreadsheet-fixed-container {
  width: 100%;
  height: 100%;
  border-color: inherit;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: absolute;
  z-index: 2; }

.k-spreadsheet-overflow {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  border-color: inherit; }

.k-spreadsheet-pane {
  padding: 0 1px 0 0;
  border-width: 2px 0 0 2px;
  border-style: solid;
  border-color: inherit;
  box-sizing: border-box;
  position: absolute;
  overflow: hidden; }
  .k-spreadsheet-pane.k-top {
    border-top-width: 0; }
  .k-spreadsheet-pane.k-left {
    border-left-width: 0; }

.k-spreadsheet-top-corner {
  border-width: 0 1px 1px 0;
  border-style: solid;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10000; }
  .k-spreadsheet-top-corner::after {
    content: "";
    display: block;
    width: 0;
    height: 0;
    overflow: hidden;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 6px solid transparent;
    border-right-color: inherit;
    border-bottom-color: inherit; }

.k-spreadsheet-scroller {
  width: 100%;
  height: 100%;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  z-index: 1; }

.k-spreadsheet-haxis,
.k-spreadsheet-vaxis {
  border: 0 solid;
  border-color: inherit;
  position: absolute; }

.k-spreadsheet-haxis {
  border-width: 1px 0 0;
  left: 0; }

.k-spreadsheet-vaxis {
  border-width: 0 0 0 1px;
  top: 0; }

.k-spreadsheet-row-header,
.k-spreadsheet-column-header {
  text-align: center; }

.k-spreadsheet-row-header {
  position: relative; }

.k-spreadsheet-column-header {
  position: absolute; }

.k-spreadsheet-data {
  border-color: inherit;
  cursor: cell;
  position: relative; }

.k-spreadsheet-cell {
  padding: 1px;
  box-sizing: border-box;
  background-clip: padding-box;
  white-space: pre;
  position: absolute;
  overflow: hidden; }

.k-spreadsheet-merged-cell {
  background-color: #ffffff; }

.k-spreadsheet-merged-cells-wrapers {
  position: relative; }

.k-spreadsheet .k-selection-wrapper {
  position: relative;
  cursor: cell; }

.k-spreadsheet .k-spreadsheet-selection {
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box;
  position: absolute; }

.k-spreadsheet .k-spreadsheet-selection-highlight {
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box;
  position: absolute; }

.k-spreadsheet .k-selection-wrapper .k-spreadsheet-editor-button {
  position: absolute;
  padding: 0; }

.k-spreadsheet-active-cell {
  outline-color: transparent !important; }

.k-spreadsheet .k-auto-fill-wrapper {
  position: relative; }

.k-spreadsheet .k-auto-fill,
.k-spreadsheet .k-auto-fill-punch,
.k-spreadsheet .k-auto-fill-br-hint {
  box-sizing: border-box;
  position: absolute; }

.k-spreadsheet .k-auto-fill {
  border-width: 1px;
  border-style: solid;
  cursor: crosshair; }

.k-spreadsheet .k-single-selection::after {
  content: "";
  margin-bottom: -4px;
  margin-right: -4px;
  width: 6px;
  height: 6px;
  border-width: 1px;
  border-style: solid;
  border-radius: 50%;
  display: block;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 100;
  cursor: crosshair; }

.k-spreadsheet .k-autofill-wrapper {
  position: relative;
  cursor: cell; }

.k-spreadsheet-vborder {
  position: absolute;
  border-left-style: solid;
  border-left-width: 1px; }

.k-spreadsheet-hborder {
  position: absolute;
  border-top-style: solid;
  border-top-width: 1px; }

.k-spreadsheet .k-filter-wrapper {
  position: relative; }

.k-spreadsheet .k-filter-range {
  border-width: 1px;
  border-style: solid;
  position: absolute;
  box-sizing: border-box; }

.k-spreadsheet-filter {
  border-radius: 4px;
  line-height: 1;
  position: absolute;
  cursor: pointer; }
  .k-spreadsheet-filter .k-icon {
    vertical-align: middle; }

.k-spreadsheet-filter-menu {
  width: 280px; }
  .k-spreadsheet-filter-menu .k-animation-container {
    position: relative; }
  .k-spreadsheet-filter-menu > .k-menu {
    border-width: 0; }
    .k-spreadsheet-filter-menu > .k-menu .k-item {
      color: inherit; }
    .k-spreadsheet-filter-menu > .k-menu .k-link {
      padding-left: calc( 16px + 8px); }
    .k-spreadsheet-filter-menu > .k-menu .k-icon {
      margin-left: calc(-1 * ( 16px + 4px ));
      margin-right: 4px; }
  .k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {
    height: 200px;
    border-width: 1px;
    border-style: solid;
    overflow-y: scroll;
    overflow-x: auto; }
    .k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper .k-treeview {
      padding: 4px;
      overflow: visible; }
  .k-spreadsheet-filter-menu .k-details {
    padding: 0;
    border-width: 1px 0 0;
    border-style: solid;
    border-color: inherit; }
  .k-spreadsheet-filter-menu .k-details-summary {
    padding: 4px;
    cursor: pointer; }
    .k-spreadsheet-filter-menu .k-details-summary > .k-icon {
      margin-right: 4px; }
  .k-spreadsheet-filter-menu .k-details-content {
    padding: 4px 8px 4px calc( 16px + 8px); }
    .k-spreadsheet-filter-menu .k-details-content > .k-textbox,
    .k-spreadsheet-filter-menu .k-details-content > .k-widget {
      width: 100%;
      margin-bottom: 3px; }
    .k-spreadsheet-filter-menu .k-details-content .k-space-right {
      background-image: none; }
    .k-spreadsheet-filter-menu .k-details-content .k-filter-and {
      width: 75px;
      margin: 8px 0; }
  .k-spreadsheet-filter-menu .k-action-buttons {
    margin: 1em 0 0; }

.k-spreadsheet-popup {
  padding: 0; }
  .k-spreadsheet-popup .k-separator {
    display: block; }
  .k-spreadsheet-popup .k-button {
    border-radius: 0;
    border-width: 0;
    color: inherit;
    background: none; }
  .k-spreadsheet-popup .k-button-icontext {
    display: flex;
    justify-content: flex-start; }
  .k-spreadsheet-popup .k-reset-color,
  .k-spreadsheet-popup .k-custom-color {
    border-radius: 0;
    width: 100%;
    border-width: 0;
    box-sizing: border-box;
    display: flex; }
  .k-spreadsheet-popup .k-reset-color {
    border-bottom-width: 1px; }
  .k-spreadsheet-popup .k-custom-color {
    border-top-width: 1px; }
  .k-spreadsheet-popup .k-spreadsheet-color-palette {
    padding: 0; }
  .k-spreadsheet-popup .k-spreadsheet-border-type-palette {
    width: 12.5rem;
    height: 5rem;
    display: flex;
    flex-flow: row wrap;
    align-content: space-around;
    justify-content: space-around; }

.k-spreadsheet-format-popup .k-item {
  justify-content: space-between; }

.k-spreadsheet-format-popup .k-spreadsheet-sample {
  order: 1; }

.k-spreadsheet-sample {
  opacity: .7; }

.k-spreadsheet-clipboard,
.k-spreadsheet-clipboard-paste {
  margin: 0;
  padding: 0;
  width: 1px;
  height: 1px;
  border: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden; }

.k-spreadsheet-window .k-edit-form-container {
  width: auto;
  min-width: 0; }

.k-spreadsheet-format-cells .k-spreadsheet-preview {
  margin-top: 0.5em;
  text-align: center; }

.k-spreadsheet-format-cells .k-list-scroller {
  margin-top: 0.5em;
  height: 210px;
  border-width: 1px;
  border-style: solid;
  border-color: inherit; }

.k-export-config {
  clear: both;
  position: relative; }
  .k-export-config::after {
    content: "";
    clear: both;
    display: block; }
  .k-export-config .k-edit-field {
    margin-left: 5%;
    width: 45%;
    float: left; }
  .k-export-config .k-page-orientation {
    position: absolute;
    right: 0;
    top: 0.5em; }
    .k-export-config .k-page-orientation .k-icon {
      font-size: 6em; }

.k-spreadsheet {
  background-clip: padding-box; }
  .k-ie11 .k-spreadsheet,
  .k-edge12 .k-spreadsheet,
  .k-edge13 .k-spreadsheet {
    background-clip: border-box; }

.k-syntax-ref {
  color: #ff8822; }

.k-syntax-num {
  color: #0099ff; }

.k-syntax-func {
  font-weight: bold; }

.k-syntax-str {
  color: #38b714; }

.k-syntax-error {
  color: red; }

.k-syntax-bool {
  color: #a9169c; }

.k-syntax-startexp {
  font-weight: bold; }

.k-syntax-paren-match {
  background-color: #caf200; }

.k-spreadsheet-cell-editor {
  color: #000000;
  background-color: #ffffff; }

.k-spreadsheet .k-selection-full,
.k-spreadsheet .k-selection-partial {
  border-color: rgba(96, 125, 139, 0.25);
  background-color: rgba(96, 125, 139, 0.25); }

.k-spreadsheet-selection {
  border-color: #607d8b;
  background-color: rgba(96, 125, 139, 0.25);
  box-shadow: inset 0 0 0 1px #607d8b; }

.k-spreadsheet .k-single-selection::after {
  border-color: #ffffff;
  background-color: #607d8b; }

.k-spreadsheet-active-cell {
  box-shadow: inset 0 0 0 1px #607d8b;
  background-color: #ffffff; }
  .k-spreadsheet-active-cell.k-right {
    box-shadow: inset 0 0 0 1px #607d8b, inset -1px 0 0 1px #607d8b; }
  .k-spreadsheet-active-cell.k-bottom {
    box-shadow: inset 0 0 0 1px #607d8b, inset 0 -1px 0 1px #607d8b; }
  .k-spreadsheet-active-cell.k-bottom.k-right {
    box-shadow: inset 0 0 0 1px #607d8b, inset -1px -1px 0 1px #607d8b; }

.k-spreadsheet .k-auto-fill {
  border-color: #607d8b;
  background-color: rgba(96, 125, 139, 0.25);
  box-shadow: inset 0 0 0 1px #607d8b; }

.k-spreadsheet .k-auto-fill-punch {
  background-color: rgba(255, 255, 255, 0.5); }

.k-spreadsheet .k-filter-range {
  border-color: #607d8b; }

.k-spreadsheet-filter {
  box-shadow: inset 0 0 0 1px #c2c2c2; }

.k-treeview {
  border-width: 0;
  background: none;
  line-height: 1.42857;
  cursor: default;
  overflow: auto;
  white-space: nowrap;
  padding: 0; }
  .k-treeview .k-content,
  .k-treeview > .k-group,
  .k-treeview .k-item > .k-group {
    margin: 0;
    padding: 0;
    background: none;
    list-style: none;
    position: relative; }
    .k-treeview .k-content.ng-animating,
    .k-treeview > .k-group.ng-animating,
    .k-treeview .k-item > .k-group.ng-animating {
      overflow: hidden; }
  .k-treeview .k-top,
  .k-treeview .k-mid,
  .k-treeview .k-bot {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center; }
  .k-treeview .k-item {
    outline-style: none;
    margin: 0;
    padding: 0 0 0 16px;
    border-width: 0;
    display: block; }
  .k-treeview .k-in {
    border-radius: 4px;
    margin: 0;
    padding: 4px 8px;
    border: 1px solid transparent;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    align-content: center;
    vertical-align: middle;
    position: relative; }
  .k-treeview .k-i-expand,
  .k-treeview .k-i-collapse {
    margin-left: -16px;
    cursor: pointer; }
  .k-treeview .k-in .k-icon,
  .k-treeview .k-in .k-image,
  .k-treeview .k-in .k-sprite {
    margin-right: 4px; }

.k-rtl .k-treeview .k-item {
  padding-left: 0;
  padding-right: 16px; }

.k-rtl .k-treeview .k-i-expand,
.k-rtl .k-treeview .k-i-collapse {
  margin-left: 0;
  margin-right: -16px; }

.k-rtl .k-treeview .k-in .k-icon,
.k-rtl .k-treeview .k-in .k-image,
.k-rtl .k-treeview .k-in .k-sprite {
  margin-left: 4px;
  margin-right: 0; }

.k-treeview .k-in {
  border-radius: 0;
  border-width: 0; }

.k-treeview .k-in.k-state-focused {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }

.k-pivot {
  position: relative; }
  .k-pivot .k-grid td {
    white-space: nowrap; }
  .k-pivot .k-grid-content {
    flex: 1 1 auto; }

.k-pivot-toolbar {
  padding: 8px;
  border-bottom-width: 1px;
  border-bottom-style: solid; }

.k-pivot-toolbar .k-button,
.k-fieldselector .k-list li.k-item {
  border-radius: 4px;
  padding: 4px 8px;
  padding-right: 48px;
  font-size: 14px;
  line-height: 1.42857;
  text-align: left;
  position: relative;
  cursor: move;
  white-space: normal; }

.k-settings-measures .k-button {
  margin-bottom: 2px; }

.k-settings-columns .k-button + .k-button {
  margin-left: 8px; }

.k-field-actions {
  position: absolute;
  right: 4px;
  top: 4px;
  line-height: 1;
  cursor: pointer; }

.k-pivot-layout {
  border-spacing: 0;
  table-layout: auto; }

.k-pivot-layout > tbody,
.k-pivot td {
  vertical-align: top; }

.k-pivot-layout > tbody > tr > td {
  padding: 0; }

.k-pivot-rowheaders > .k-grid,
.k-pivot-table > .k-grid {
  border-width: 0; }

.k-pivot-rowheaders > .k-grid td:first-child,
.k-pivot-table .k-grid-header .k-header.k-first {
  border-left-width: 1px; }

.k-pivot-rowheaders > .k-grid td.k-first {
  border-left-width: 0; }

.k-pivot-rowheaders > .k-grid {
  overflow: hidden; }

.k-pivot-table {
  border-left-width: 1px;
  border-left-style: solid; }

.k-pivot-table .k-grid-header-wrap > table {
  height: 100%; }

.k-pivot .k-grid-header .k-header {
  vertical-align: top; }

.k-pivot-layout .k-grid td {
  border-bottom-width: 1px; }

.k-pivot-layout .k-grid-footer > td {
  border-top-width: 0; }

.k-pivot-filter-window .k-treeview {
  max-height: 600px; }

.k-i-kpi-trend-increase,
.k-i-kpi-trend-decrease,
.k-i-kpi-trend-equal {
  color: inherit; }

.k-i-kpi-status-hold {
  color: #ffc107; }

.k-i-kpi-status-deny {
  color: #f44336; }

.k-i-kpi-status-open {
  color: #009688; }

.k-fieldselector .k-columns {
  display: flex;
  align-items: stretch; }
  .k-fieldselector .k-columns > div {
    padding: 8px;
    width: 50%;
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    float: left;
    overflow: auto; }
  .k-fieldselector .k-columns > div + div {
    border-left-width: 1px; }

.k-fieldselector p {
  margin: 0 0 2px;
  text-transform: uppercase; }

.k-fieldselector p .k-icon {
  margin: 0 4px 0 0; }

.k-fieldselector .k-treeview {
  border-width: 0;
  overflow: visible; }

.k-fieldselector .k-edit-label {
  width: 16%; }

.k-fieldselector .k-edit-field {
  width: 77%; }

.k-fieldselector .k-edit-buttons > input,
.k-fieldselector .k-edit-buttons > label {
  float: left;
  margin-top: .4em; }

.k-fieldselector .k-list-container {
  margin-bottom: 1em;
  padding: 2px;
  border-width: 1px;
  border-style: solid; }

.k-fieldselector .k-list {
  padding-bottom: 1em; }

.k-fieldselector .k-list .k-item {
  border-width: 1px;
  border-style: solid; }

.k-fieldselector .k-list .k-item + .k-item {
  margin-top: 0.5em; }

.k-alt,
.k-pivot-toolbar,
.k-pivot-layout > tbody > tr:first-child > td:first-child {
  background-color: #f5f5f5; }

.k-fieldselector .k-list-container {
  background-color: #ffffff; }

.k-pivot-toolbar,
.k-pivot-table,
.k-fieldselector .k-list-container,
.k-fieldselector .k-columns > div,
.k-pivot-rowheaders > .k-grid td:first-child,
.k-pivot-table .k-grid-header .k-header.k-first {
  border-color: #c2c2c2; }

.k-pivot-rowheaders .k-alt .k-alt,
.k-header.k-alt {
  background-color: #ededed; }

.k-pivot-toolbar .k-button,
.k-fieldselector .k-list li.k-item {
  background-clip: padding-box; }
  .k-ie11 .k-pivot-toolbar .k-button,
  .k-edge12 .k-pivot-toolbar .k-button,
  .k-edge13 .k-pivot-toolbar .k-button, .k-ie11
  .k-fieldselector .k-list li.k-item,
  .k-edge12
  .k-fieldselector .k-list li.k-item,
  .k-edge13
  .k-fieldselector .k-list li.k-item {
    background-clip: border-box; }
  .k-pivot-toolbar .k-button:active, .k-pivot-toolbar .k-button.k-state-active,
  .k-fieldselector .k-list li.k-item:active,
  .k-fieldselector .k-list li.k-item.k-state-active {
    border-color: #c2c2c2;
    color: #000000;
    background-color: #f5f5f5;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.06); }
  .k-pivot-toolbar .k-button:focus, .k-pivot-toolbar .k-button.k-state-focused,
  .k-fieldselector .k-list li.k-item:focus,
  .k-fieldselector .k-list li.k-item.k-state-focused {
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.06); }

/* Loading indicator */
.k-fieldselector .k-i-loading {
  border-color: #f5f5f5; }

.k-fieldselector .k-i-loading::before, .k-fieldselector .k-loading-image::before,
.k-fieldselector .k-i-loading::after,
.k-fieldselector .k-loading-image::after {
  background-color: #f5f5f5; }

.k-treelist.k-display-block.k-grid-lockedcolumns {
  display: block; }

.k-treelist .k-status {
  padding: .4em .6em;
  line-height: 1.6em; }
  .k-treelist .k-status .k-loading {
    vertical-align: baseline;
    margin-right: 5px; }

.k-treelist tr.k-hidden {
  display: none; }

.k-treelist .k-treelist-dragging,
.k-treelist .k-treelist-dragging .k-state-hover {
  cursor: default; }

.k-treelist .k-drop-hint {
  position: absolute;
  z-index: 10000;
  visibility: hidden;
  width: 80px;
  height: 5px;
  margin-top: -3px;
  background-color: transparent;
  background-repeat: no-repeat; }

.k-drag-separator {
  display: inline-block;
  border-right: 1px solid;
  height: 1em;
  vertical-align: top;
  margin: 0 .5em; }

.k-scrollview-wrap {
  position: relative;
  overflow: hidden;
  outline: 0;
  display: block; }

.k-scrollview {
  list-style-type: none;
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  cursor: default; }
  .k-scrollview img {
    user-select: none; }
  .k-scrollview > li {
    display: inline-block;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0; }

.k-pages,
.k-scrollview-pageable {
  margin: 0 auto;
  padding: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: center;
  list-style: none;
  position: absolute;
  left: 0;
  bottom: 20px;
  pointer-events: none; }

.k-pages > li,
.k-scrollview-pageable > li.k-button {
  margin: 0 20px;
  padding: 0;
  width: 8px;
  height: 8px;
  box-sizing: content-box;
  vertical-align: middle;
  display: inline-block;
  flex: 0 0 8px;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: all; }

.k-scrollview-next,
.k-scrollview-prev {
  display: table;
  position: absolute;
  padding: 0;
  height: 60%;
  top: 20%;
  text-decoration: none;
  user-select: none;
  cursor: pointer;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent; }

.k-scrollview-prev {
  left: 0; }

.k-scrollview-next {
  right: 0; }

.k-scrollview-next span,
.k-scrollview-prev span {
  display: table-cell;
  margin: 0;
  padding: 0;
  vertical-align: middle;
  font-size: 4.5em;
  font-weight: normal; }

.k-scrollview-elements {
  width: 100%; }

@supports (-webkit-user-select: none) {
  div.k-scrollview-wrap ul.k-scrollview li > * {
    pointer-events: auto; } }

@supports not (-webkit-user-select: none) {
  div.k-scrollview-wrap ul.k-scrollview li > * {
    pointer-events: none; } }

.km-scrollview {
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  width: 100%; }
  .km-scrollview > div > * {
    -webkit-transform: translatez(0); }
  .km-scrollview > div > [data-role="page"] {
    vertical-align: top;
    display: inline-block;
    min-height: 1px; }
  .km-scrollview .km-virtual-page {
    min-height: 1px;
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block; }

.k-ff18 .km-scrollview > div,
.k-ff19 .km-scrollview > div,
.k-ff20 .km-scrollview > div,
.k-ff21 .km-scrollview > div {
  width: 0; }

.km-pages {
  padding: 4px 0 0 0;
  position: static; }

.k-pages > li,
.k-scrollview-pageable > li.k-button {
  background-image: none;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.08); }

.k-pages > li {
  border-color: #c2c2c2;
  color: #000000;
  background-color: #f5f5f5; }

.k-pages > li.k-current-page {
  border-color: #607d8b;
  color: #ffffff;
  background-color: #607d8b; }

.k-scrollview-elements {
  color: #ffffff; }

.k-scrollview-next,
.k-scrollview-prev {
  color: inherit;
  background: transparent;
  text-shadow: rgba(0, 0, 0, 0.3) 0 0 15px;
  opacity: 0.7;
  outline-width: 0; }
  .k-scrollview-next:hover,
  .k-scrollview-prev:hover {
    color: #ffffff;
    opacity: 1; }

.k-scrollview-animation {
  transition-duration: .3s;
  transition-timing-function: ease-out; }

.k-listview > .k-state-focused {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }

.k-listview > .k-state-selected {
  color: inherit;
  background-color: rgba(96, 125, 139, 0.25); }

.k-autocomplete {
  border-radius: 4px; }
  .k-autocomplete .k-input {
    padding: 4px 8px;
    height: 1.42857em;
    box-sizing: content-box;
    border: 0;
    outline: 0;
    color: inherit;
    background: none;
    font: inherit;
    line-height: 1.42857;
    display: inline-block;
    flex: 1; }
  .k-autocomplete .k-i-close, .k-autocomplete .k-i-group-delete::before {
    display: none;
    align-self: center; }
  .k-autocomplete.k-state-hover .k-i-close, .k-autocomplete.k-state-hover .k-i-group-delete::before, .k-autocomplete.k-state-focused .k-i-close, .k-autocomplete.k-state-focused .k-i-group-delete::before {
    display: flex;
    outline: 0; }

.k-autocomplete {
  background-clip: padding-box; }
  .k-ie11 .k-autocomplete,
  .k-edge12 .k-autocomplete,
  .k-edge13 .k-autocomplete {
    background-clip: border-box; }

.k-tooltip {
  border-radius: 4px;
  line-height: 1.42857;
  padding: 4px 8px;
  border-width: 0;
  background-repeat: repeat-x;
  position: absolute;
  z-index: 12000; }
  .k-tooltip:not(.k-tooltip-closable) {
    font-size: 14px; }

.k-tooltip-content {
  overflow: hidden;
  text-overflow: ellipsis; }

.k-tooltip-closable {
  padding: 12px 16px;
  line-height: normal; }
  .k-tooltip-closable .k-tooltip-content {
    padding-right: calc(16px + 4px); }
    .k-rtl .k-tooltip-closable .k-tooltip-content,
    [dir-rtl] .k-tooltip-closable .k-tooltip-content {
      padding-left: calc(16px + 4px);
      padding-right: 0; }

.k-tooltip-button {
  position: absolute;
  top: 12px;
  right: 16px; }
  .k-tooltip-button .k-icon {
    color: inherit; }
  .k-rtl .k-tooltip-button,
  [dir-rtl] .k-tooltip-button {
    left: 16px;
    right: auto; }

.k-callout {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px;
  border-color: transparent;
  pointer-events: none; }

.k-callout-n {
  left: 50%;
  margin-left: -6px;
  top: -12px;
  pointer-events: none; }

.k-callout-w {
  top: 50%;
  margin-top: -6px;
  left: -12px;
  pointer-events: none; }

.k-callout-s {
  left: 50%;
  margin-left: -6px;
  bottom: -12px;
  pointer-events: none; }

.k-callout-e {
  top: 50%;
  margin-top: -6px;
  right: -12px;
  pointer-events: none; }

.k-tooltip {
  color: #ffffff;
  background-color: #607d8b; }

.k-callout-n {
  border-bottom-color: #607d8b; }

.k-callout-e {
  border-left-color: #607d8b; }

.k-callout-s {
  border-top-color: #607d8b; }

.k-callout-w {
  border-right-color: #607d8b; }

.k-slider {
  background-color: transparent;
  border-width: 0;
  position: relative; }
  .k-slider .k-button {
    height: 28px;
    line-height: 28px;
    margin: 0;
    min-width: 0;
    outline: 0;
    padding: 0;
    position: absolute;
    width: 28px;
    box-sizing: content-box; }
    .k-slider .k-button .k-icon {
      vertical-align: baseline;
      line-height: 28px;
      margin-right: 0;
      height: 100%; }
  .k-slider .k-button-increase {
    right: 0;
    top: 0; }
  .k-slider .k-button-decrease {
    left: 0;
    top: 0; }
  .k-slider .k-label {
    font-size: .92em;
    position: absolute;
    white-space: nowrap; }
  .k-slider .k-tick,
  .k-slider .k-slider-track {
    cursor: pointer; }
  .k-slider .k-tick {
    background-color: transparent;
    background-position: center center;
    background-repeat: no-repeat;
    margin: 0;
    padding: 0;
    position: relative; }
  .k-slider.k-state-disabled .k-tick,
  .k-slider.k-state-disabled .k-slider-track,
  .k-slider.k-state-disabled .k-draghandle {
    cursor: default; }
  .k-slider[dir='rtl'] .k-slider-selection {
    left: auto;
    right: 0; }
  .k-slider[dir='rtl'] .k-button-increase {
    left: 0;
    right: auto; }
  .k-slider[dir='rtl'] .k-button-decrease {
    right: 0;
    left: auto; }

.k-slider-vertical {
  height: 200px;
  width: 30px;
  outline: 0; }
  .k-slider-vertical .k-button-decrease {
    bottom: 0;
    top: auto; }
  .k-slider-vertical .k-tick {
    text-align: right;
    margin-left: 2px; }
  .k-slider-vertical .k-slider-topleft .k-tick {
    text-align: left; }
  .k-slider-vertical .k-tick {
    background-position: -92px center; }
  .k-slider-vertical .k-slider-topleft .k-tick {
    background-position: -122px center; }
  .k-slider-vertical .k-slider-bottomright .k-tick {
    background-position: -152px center; }
  .k-slider-vertical .k-tick-large {
    background-position: -2px center; }
  .k-slider-vertical .k-slider-topleft .k-tick-large {
    background-position: -32px center; }
  .k-slider-vertical .k-slider-bottomright .k-tick-large {
    background-position: -62px center; }
  .k-slider-vertical .k-first {
    background-position: -92px 100%; }
  .k-slider-vertical .k-tick-large.k-first {
    background-position: -2px 100%; }
  .k-slider-vertical .k-slider-topleft .k-first {
    background-position: -122px 100%; }
  .k-slider-vertical .k-slider-topleft .k-tick-large.k-first {
    background-position: -32px 100%; }
  .k-slider-vertical .k-slider-bottomright .k-first {
    background-position: -152px 100%; }
  .k-slider-vertical .k-slider-bottomright .k-tick-large.k-first {
    background-position: -62px 100%; }
  .k-slider-vertical .k-last {
    background-position: -92px 0; }
  .k-slider-vertical .k-tick-large.k-last {
    background-position: -2px 0; }
  .k-slider-vertical .k-slider-topleft .k-last {
    background-position: -122px 0; }
  .k-slider-vertical .k-slider-topleft .k-tick-large.k-last {
    background-position: -32px 0; }
  .k-slider-vertical .k-slider-bottomright .k-last {
    background-position: -152px 0; }
  .k-slider-vertical .k-slider-bottomright .k-tick-large.k-last {
    background-position: -62px 0; }
  .k-slider-vertical .k-label {
    display: block;
    left: 120%;
    text-align: left; }
  .k-slider-vertical .k-last .k-label {
    top: -.5em; }
  .k-slider-vertical .k-first .k-label {
    bottom: -.5em; }
  .k-slider-vertical .k-slider-topleft .k-label {
    left: auto;
    right: 120%; }

.k-slider-horizontal {
  display: inline-block;
  height: 30px;
  width: 200px;
  outline: 0; }
  .k-slider-horizontal .k-tick {
    float: left;
    height: 100%;
    text-align: center;
    margin-top: 2px; }
  .k-slider-horizontal .k-tick {
    background-position: center -92px; }
  .k-slider-horizontal .k-slider-topleft .k-tick {
    background-position: center -122px; }
  .k-slider-horizontal .k-slider-bottomright .k-tick {
    background-position: center -152px; }
  .k-slider-horizontal .k-tick-large {
    background-position: center -2px; }
  .k-slider-horizontal .k-slider-topleft .k-tick-large {
    background-position: center -32px; }
  .k-slider-horizontal .k-slider-bottomright .k-tick-large {
    background-position: center -62px; }
  .k-slider-horizontal .k-first {
    background-position: 0 -92px; }
  .k-slider-horizontal .k-tick-large.k-first {
    background-position: 0 -2px; }
  .k-slider-horizontal .k-slider-topleft .k-first {
    background-position: 0 -122px; }
  .k-slider-horizontal .k-slider-topleft .k-tick-large.k-first {
    background-position: 0 -32px; }
  .k-slider-horizontal .k-slider-bottomright .k-first {
    background-position: 0 -152px; }
  .k-slider-horizontal .k-slider-bottomright .k-tick-large.k-first {
    background-position: 0 -62px; }
  .k-slider-horizontal .k-last {
    background-position: 100% -92px; }
  .k-slider-horizontal .k-tick-large.k-last {
    background-position: 100% -2px; }
  .k-slider-horizontal .k-slider-topleft .k-last {
    background-position: 100% -122px; }
  .k-slider-horizontal .k-slider-topleft .k-tick-large.k-last {
    background-position: 100% -32px; }
  .k-slider-horizontal .k-slider-bottomright .k-last {
    background-position: 100% -152px; }
  .k-slider-horizontal .k-slider-bottomright .k-tick-large.k-last {
    background-position: 100% -62px; }
  .k-slider-horizontal .k-label {
    left: 0;
    bottom: -1.2em;
    line-height: 1;
    width: 100%; }
  .k-slider-horizontal .k-first .k-label {
    left: -50%; }
  .k-slider-horizontal .k-last .k-label {
    left: auto;
    right: -50%; }
  .k-slider-horizontal .k-slider-topleft .k-label {
    top: -1.2em; }
  .k-slider-horizontal[dir='rtl'] .k-button-increase .k-icon,
  .k-slider-horizontal[dir='rtl'] .k-button-decrease .k-icon {
    transform: rotate(180deg); }

.k-slider-wrap {
  height: 100%;
  width: 100%; }

.k-slider-track,
.k-slider-selection {
  margin: 0;
  padding: 0;
  position: absolute; }
  .k-slider-horizontal .k-slider-track, .k-slider-horizontal
  .k-slider-selection {
    height: 4px;
    left: 0;
    margin-top: -2px;
    top: 50%; }
  .k-slider-vertical .k-slider-track, .k-slider-vertical
  .k-slider-selection {
    bottom: 0;
    left: 50%;
    margin-left: -2px;
    width: 4px; }

.k-slider-horizontal .k-slider-buttons .k-slider-track {
  left: 38px; }

.k-slider-vertical .k-slider-buttons .k-slider-track {
  bottom: 38px; }

.k-draghandle {
  background-color: transparent;
  background-repeat: no-repeat;
  border-style: solid;
  border-width: 1px;
  outline: 0;
  overflow: hidden;
  position: absolute;
  text-align: center;
  text-decoration: none;
  text-indent: -3333px;
  box-sizing: content-box;
  width: 14px;
  height: 14px; }
  .k-slider-horizontal .k-draghandle {
    top: 50%;
    transform: translateY(-50%); }
    .k-slider-horizontal .k-draghandle:active, .k-slider-horizontal .k-draghandle.k-pressed {
      transform: translateY(-50%) scale(1); }
  .k-slider-vertical .k-draghandle {
    left: 50%;
    transform: translateX(-50%); }
    .k-slider-vertical .k-draghandle:active, .k-slider-vertical .k-draghandle.k-pressed {
      transform: translateX(-50%) scale(1); }
  .k-slider-transitions.k-slider-horizontal .k-draghandle {
    transition: left 0.3s ease-out, background-color 0.3s ease-out, transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); }
  .k-slider-transitions.k-slider-vertical .k-draghandle {
    transition: bottom 0.3s ease-out, background-color 0.3s ease-out, transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); }
  .k-draghandle.k-pressed {
    transition: none; }

.k-slider-transitions.k-slider-horizontal .k-slider-selection {
  transition: width 0.3s ease-out; }

.k-slider-transitions.k-slider-vertical .k-slider-selection {
  transition: height 0.3s ease-out; }

.k-slider-selection.k-pressed {
  transition: none; }

.k-slider-items {
  user-select: none; }
  .k-slider-buttons .k-slider-items {
    margin-left: 38px; }
  .k-slider-horizontal .k-slider-items {
    height: 100%; }
  .k-slider-vertical .k-slider-items {
    padding-top: 1px; }
  .k-slider-horizontal .k-slider-buttons .k-slider-items {
    padding-top: 0; }
  .k-slider-vertical .k-slider-buttons .k-slider-items {
    margin: 0;
    padding-top: 38px; }

.k-slider-tooltip .k-callout-n,
.k-slider-tooltip .k-callout-s {
  margin-left: -6px; }

.k-slider-tooltip .k-callout-w,
.k-slider-tooltip .k-callout-e {
  margin-top: -6px; }

.k-slider .k-slider-track,
.k-slider .k-slider-selection {
  border-radius: 4px; }

.k-slider .k-slider-track {
  background-color: #e6e6e6; }

.k-slider .k-slider-selection {
  background-color: #607d8b; }

.k-slider .k-button {
  border-radius: 50%; }

.k-slider .k-draghandle {
  border-radius: 50%; }
  .k-slider .k-draghandle:active, .k-slider .k-draghandle.k-pressed {
    box-shadow: inset 0 2px 2px 0 rgba(0, 0, 0, 0.06); }
  .k-slider .k-draghandle:focus {
    box-shadow: 0 3px 4px 0 rgba(96, 125, 139, 0.4); }

.k-slider.k-state-focused .k-draghandle {
  box-shadow: 0 3px 4px 0 rgba(96, 125, 139, 0.4); }

.k-slider .k-slider-wrap:focus {
  outline: none; }

.k-slider-horizontal .k-tick {
  background-image: url("data:image/gif;base64,R0lGODlhAQC0AIABALi4uAAAACH5BAEAAAEALAAAAAABALQAAAIWjIGJxqzazlux2ovlzND2rAHgSIZWAQA7"); }

.k-slider-vertical .k-tick {
  background-image: url("data:image/gif;base64,R0lGODlhtAABAIABALi4uAAAACH5BAEAAAEALAAAAAC0AAEAAAIWjIGJxqzazlux2ovlzND2rAHgSIZWAQA7"); }

.k-colorpicker {
  width: auto;
  border-width: 0;
  display: inline-block;
  position: relative;
  overflow: visible; }
  .k-colorpicker .k-selected-color {
    padding: 4px;
    width: calc( 8px + 1.42857em);
    height: calc( 8px + 1.42857em);
    box-sizing: border-box;
    line-height: 0;
    position: relative;
    overflow: hidden; }
    .k-colorpicker .k-selected-color .k-i-line {
      border-top: 1px solid #f44336;
      width: 200%;
      height: 200%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-33%, -33%) rotateZ(45deg);
      transform-origin: 0 0; }
    .k-colorpicker .k-selected-color .k-i-line::before {
      display: none; }
  .k-colorpicker .k-tool-icon {
    padding: 4px;
    width: calc( 8px + 1.42857em);
    height: calc( 8px + 1.42857em);
    border-width: 0 1px 0 0;
    border-style: solid;
    border-color: inherit;
    box-sizing: border-box;
    font-size: inherit;
    text-align: center; }
    .k-colorpicker .k-tool-icon::before {
      font-size: 16px;
      line-height: 1; }
    .k-colorpicker .k-tool-icon .k-selected-color {
      display: inline-block;
      padding: 0;
      margin-bottom: 12px;
      width: 14px;
      height: 3px;
      line-height: 0; }

.k-flatcolorpicker {
  border-radius: 4px;
  padding: 0 0 1em;
  width: 250px;
  display: flex;
  flex-direction: column;
  align-items: stretch; }
  .k-flatcolorpicker .k-draghandle {
    border-radius: 50%;
    width: 1em;
    height: 1em;
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.21);
    box-sizing: border-box; }
  .k-flatcolorpicker .k-selected-color {
    border-width: 0 0 1px;
    border-style: solid;
    border-color: inherit;
    background-position: 50% 50%; }
  .k-flatcolorpicker .k-color-input {
    display: flex;
    flex-direction: row;
    position: relative; }
  .k-flatcolorpicker .k-color-value {
    margin: 0 0 0 calc( 16px + 2.85714em);
    padding: 4px 8px;
    width: 100%;
    border: 0;
    box-sizing: border-box;
    font-size: inherit;
    line-height: 1.42857;
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Roboto Mono", "Ubuntu Mono", "Lucida Console", "Courier New", monospace;
    flex: 1; }
  .k-flatcolorpicker .k-clear-color-container {
    display: flex;
    flex-direction: row; }
  .k-flatcolorpicker .k-clear-color {
    flex: 1; }
  .k-flatcolorpicker .k-color-value + .k-clear-color {
    flex: none;
    position: absolute;
    top: 0;
    right: 0; }
  .k-flatcolorpicker .k-hsv-rectangle {
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: pinch-zoom double-tap-zoom; }
    .k-flatcolorpicker .k-hsv-rectangle .k-draghandle {
      margin: -7px 0 0 -7px;
      cursor: pointer;
      position: absolute;
      z-index: 10;
      left: 50%;
      top: 50%; }
  .k-flatcolorpicker .k-hsv-gradient {
    margin-bottom: 5px;
    height: 180px;
    background: linear-gradient(to bottom, transparent, black), linear-gradient(to right, white, rgba(255, 255, 255, 0)); }
  .k-flatcolorpicker > .k-slider {
    margin: 1em 1em 0; }
  .k-flatcolorpicker .k-hue-slider,
  .k-flatcolorpicker .k-transparency-slider {
    display: block; }
    .k-flatcolorpicker .k-hue-slider .k-draghandle,
    .k-flatcolorpicker .k-transparency-slider .k-draghandle {
      border-width: 3px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.9);
      background-color: transparent; }
    .k-flatcolorpicker .k-hue-slider .k-draghandle:hover,
    .k-flatcolorpicker .k-hue-slider .k-draghandle:focus,
    .k-flatcolorpicker .k-transparency-slider .k-draghandle:hover,
    .k-flatcolorpicker .k-transparency-slider .k-draghandle:focus {
      border-color: white;
      box-shadow: 0 1px 4px black;
      background-color: transparent; }
  .k-flatcolorpicker .k-hue-slider {
    width: 1em;
    height: 1em; }
    .k-flatcolorpicker .k-hue-slider .k-slider-track {
      background: linear-gradient(to right, #ff0000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff, #ff0000); }
    .k-flatcolorpicker .k-hue-slider .k-slider-selection {
      background: none;
      opacity: 0; }
  .k-flatcolorpicker .k-slider-horizontal {
    width: 90%;
    height: 20px; }
  .k-flatcolorpicker .k-controls {
    margin: 1em 1em 0;
    text-align: center; }

.k-colorpalette {
  border-width: 0;
  line-height: 0;
  display: inline-block;
  position: relative; }
  .k-colorpalette .k-palette {
    width: 100%;
    height: 100%;
    border-collapse: collapse;
    position: relative; }
  .k-colorpalette .k-item {
    width: 14px;
    height: 14px;
    overflow: hidden;
    -ms-high-contrast-adjust: none; }
  .k-colorpalette .k-item:hover {
    box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.5), inset 0 0 0 1px rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 101; }
  .k-colorpalette .k-item.k-state-selected,
  .k-colorpalette .k-item.k-state-selected:hover {
    box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.7), inset 0 0 0 1px rgba(255, 255, 255, 0.45);
    position: relative;
    z-index: 100; }

.k-colorpicker .k-picker-wrap {
  background-clip: padding-box; }
  .k-ie11 .k-colorpicker .k-picker-wrap,
  .k-edge12 .k-colorpicker .k-picker-wrap,
  .k-edge13 .k-colorpicker .k-picker-wrap {
    background-clip: border-box; }

.k-colorpicker .k-select {
  padding: 0;
  width: calc( 8px + 1.42857em);
  border-width: 0; }

.k-colorpicker .k-state-focused {
  box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.06); }

.k-flatcolorpicker {
  background-color: #f5f5f5; }

.k-combobox .k-dropdown-wrap > .k-i-close, .k-combobox .k-dropdown-wrap > .k-i-group-delete::before {
  right: calc(8px + 4px * 2 + 20px); }

.k-combobox .k-i-loading {
  position: relative;
  right: auto;
  bottom: auto;
  margin: 0; }

.k-combobox[dir='rtl'] > .k-dropdown-wrap > .k-i-close, .k-combobox[dir='rtl'] > .k-dropdown-wrap > .k-i-group-delete::before,
.k-rtl .k-combobox > .k-dropdown-wrap > .k-i-close,
.k-rtl .k-combobox > .k-dropdown-wrap > .k-i-group-delete::before {
  left: calc(8px + 4px * 2 + 20px);
  right: auto; }

.k-combobox-clearable .k-input {
  padding-right: 24px; }

.k-combobox-clearable[dir='rtl'] .k-input,
.k-rtl .k-combobox-clearable .k-input {
  padding-left: 24px;
  padding-right: 8px; }

.k-combobox .k-dropdown-wrap {
  background-clip: padding-box; }
  .k-ie11 .k-combobox .k-dropdown-wrap,
  .k-edge12 .k-combobox .k-dropdown-wrap,
  .k-edge13 .k-combobox .k-dropdown-wrap {
    background-clip: border-box; }

.k-combobox .k-select {
  padding: 0;
  width: calc( 8px + 1.42857em);
  border-width: 0; }

.k-calendar {
  width: 16.917em;
  position: relative;
  overflow: hidden;
  display: inline-block; }
  .k-calendar .k-link {
    padding: 0.25rem 0.25rem;
    color: inherit;
    line-height: 1.25;
    text-decoration: none;
    display: block;
    outline: 0; }
  .k-calendar table {
    margin: 0;
    width: 100%;
    border-width: 0;
    border-color: inherit;
    border-spacing: 0;
    border-collapse: separate;
    table-layout: fixed;
    outline: 0; }
  .k-calendar td,
  .k-calendar th {
    border-width: 0;
    padding: 0;
    text-align: center;
    border-style: solid;
    border-color: inherit;
    font-weight: normal; }
  .k-calendar th {
    padding: 8px 0;
    font-size: 10px;
    font-weight: bold;
    cursor: default;
    opacity: 0.6; }
  .k-calendar td {
    cursor: pointer; }
  .k-calendar .k-alt {
    cursor: default; }
  .k-calendar .k-header,
  .k-calendar .k-footer {
    padding: 0.25rem 0.25rem;
    text-align: center;
    display: flex;
    flex-direction: row;
    position: relative; }
    .k-calendar .k-header .k-link,
    .k-calendar .k-footer .k-link {
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center; }
  .k-calendar .k-header {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    z-index: 1; }
  .k-calendar .k-footer {
    clear: both; }
  .k-calendar .k-nav-prev,
  .k-calendar .k-nav-next {
    padding: 0.25rem;
    width: 1.25em;
    height: 1.25em;
    box-sizing: content-box; }
  .k-calendar .k-nav-prev {
    left: 0.25rem; }
  .k-calendar .k-nav-next {
    right: 0.25rem; }
  .k-calendar .k-nav-fast {
    margin: 0 0.25rem;
    flex: 1 1 0%; }
  .k-calendar .k-nav-today {
    flex: 1 1 0%; }
  .k-calendar .k-content {
    margin: 0;
    width: 100%;
    height: 14.167em;
    text-align: right;
    float: left; }
    .k-calendar .k-content td {
      border-color: transparent; }
    .k-calendar .k-content .k-link {
      border-radius: 4px;
      line-height: 1.5; }
    .k-calendar .k-content.k-meta-view.k-century .k-link {
      line-height: 1.5;
      text-align: left; }
  .k-calendar .k-animation-container .k-content {
    height: 100%; }
  .k-calendar .k-content .k-link {
    overflow: hidden; }
  .k-calendar .k-alt,
  .k-calendar .k-other-month .k-link {
    opacity: 0.6; }
  .k-calendar .k-alt {
    text-align: center; }
  .k-calendar .k-state-hover,
  .k-calendar .k-state-selected {
    opacity: 1; }
  .k-calendar .k-meta-view .k-link {
    text-align: center;
    line-height: 4; }
  .k-rtl .k-calendar .k-nav-prev .k-icon,
  .k-rtl .k-calendar .k-nav-next .k-icon {
    transform: scaleX(-1); }

.k-calendar-container {
  padding: 0; }
  .k-calendar-container .k-calendar {
    border-width: 0; }

.k-calendar.k-calendar-infinite {
  box-sizing: content-box;
  width: auto;
  display: inline-flex;
  vertical-align: bottom; }
  .k-calendar.k-calendar-infinite .k-content {
    text-align: center; }

.k-calendar .k-content.k-scrollable {
  box-sizing: content-box;
  overflow-x: hidden;
  overflow-y: auto;
  padding-right: 100px;
  padding-left: 100px;
  margin-left: -100px;
  margin-right: -100px;
  margin-right: -117px; }

.k-calendar[dir='rtl'] .k-content.k-scrollable,
.k-rtl .k-calendar .k-content.k-scrollable {
  padding-right: 100px;
  padding-left: 100px;
  margin-left: -100px;
  margin-right: -100px;
  margin-left: -117px; }

.k-calendar[dir='rtl'] .k-calendar-view .k-content th,
.k-rtl .k-calendar .k-calendar-view .k-content th {
  text-align: right; }

.k-calendar .k-scrollable-placeholder {
  position: absolute;
  z-index: -1;
  width: 1px;
  top: 0;
  right: 0; }

.k-calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 8px;
  line-height: 2em; }
  .k-calendar-header .k-title {
    font-weight: bold; }
  .k-calendar-header .k-today {
    cursor: pointer;
    color: #607d8b; }
    .k-calendar-header .k-today:hover, .k-calendar-header .k-today:focus {
      color: #90a4ae; }

.k-calendar-monthview,
.k-calendar-view {
  display: flex;
  flex: 1 0 auto;
  flex-direction: column;
  overflow: hidden;
  box-sizing: content-box;
  padding: 0 1em;
  width: 17em;
  height: calc( 19em + 2.07143em); }
  .k-calendar-monthview .k-calendar-header,
  .k-calendar-view .k-calendar-header {
    flex: 0 0 auto;
    margin-left: -8px;
    padding-left: 8px; }
    .k-calendar-monthview .k-calendar-header .k-title,
    .k-calendar-view .k-calendar-header .k-title {
      margin-left: -8px; }
  .k-calendar-monthview .k-content,
  .k-calendar-view .k-content {
    position: relative;
    flex: 1 0 auto; }
    .k-calendar-monthview .k-content > table,
    .k-calendar-view .k-content > table {
      position: relative;
      z-index: 1;
      width: auto; }
    .k-calendar-monthview .k-content th,
    .k-calendar-view .k-content th {
      border: 0;
      text-align: left;
      font-weight: bold;
      font-size: 14px;
      opacity: 0.6; }
    .k-calendar-monthview .k-content td,
    .k-calendar-view .k-content td {
      cursor: default;
      text-align: center;
      height: 2.42857em;
      width: 2.42857em;
      border-width: 0; }
    .k-calendar-monthview .k-content td,
    .k-calendar-monthview .k-content .k-link,
    .k-calendar-view .k-content td,
    .k-calendar-view .k-content .k-link {
      border-radius: 1.21429em; }
    .k-calendar-monthview .k-content .k-link,
    .k-calendar-view .k-content .k-link {
      cursor: pointer;
      width: 2.42857em;
      height: 2.42857em;
      line-height: 2.42857em;
      padding: 0; }
    .k-calendar-monthview .k-content .k-range-start,
    .k-calendar-view .k-content .k-range-start {
      border-color: inherit;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0; }
    .k-calendar-monthview .k-content .k-range-end,
    .k-calendar-view .k-content .k-range-end {
      border-color: inherit;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0; }
    .k-calendar-monthview .k-content .k-range-mid,
    .k-calendar-view .k-content .k-range-mid {
      border-color: inherit;
      border-radius: 0; }
  .k-calendar-monthview::after,
  .k-calendar-view::after {
    display: block;
    position: absolute;
    bottom: 0;
    content: " ";
    height: 0;
    line-height: 0;
    z-index: 1;
    width: 150%;
    left: -25%;
    box-shadow: 0 0 2.42857em 1.21429em #ffffff; }

.k-calendar-monthview .k-content {
  height: 19em; }

.k-calendar-monthview .k-calendar-weekdays {
  flex: 0 0 auto; }
  .k-calendar-monthview .k-calendar-weekdays thead {
    outline: none;
    cursor: default;
    opacity: 0.6;
    filter: grayscale(0.1);
    pointer-events: none;
    box-shadow: none;
    font-weight: bold; }
  .k-calendar-monthview .k-calendar-weekdays th {
    text-align: center;
    border-width: 0;
    padding: 0;
    line-height: 2.07143em; }

.k-calendar-yearview .k-content table,
.k-calendar-decadeview .k-content table,
.k-calendar-centuryview .k-content table {
  table-layout: auto; }

.k-calendar-yearview .k-content th,
.k-calendar-decadeview .k-content th,
.k-calendar-centuryview .k-content th {
  font-size: inherit;
  height: 2em;
  padding-left: 0;
  padding-right: 0; }

.k-calendar-yearview .k-content td,
.k-calendar-yearview .k-content .k-link,
.k-calendar-decadeview .k-content td,
.k-calendar-decadeview .k-content .k-link,
.k-calendar-centuryview .k-content td,
.k-calendar-centuryview .k-content .k-link {
  border-radius: 1.7em; }

.k-calendar-yearview .k-content .k-link,
.k-calendar-decadeview .k-content .k-link,
.k-calendar-centuryview .k-content .k-link {
  width: 3.4em;
  height: 3.4em;
  line-height: 3.4em; }

.k-calendar-navigation {
  position: relative;
  display: block;
  overflow: hidden;
  width: 5em;
  z-index: 2; }
  .k-calendar-navigation::before, .k-calendar-navigation::after {
    display: block;
    position: absolute;
    content: " ";
    height: 0;
    line-height: 0;
    z-index: 1;
    width: 200%;
    left: -50%;
    box-shadow: 0 0 6em 3em #f5f5f5; }
  .k-calendar-navigation::before {
    top: 0; }
  .k-calendar-navigation::after {
    bottom: 0; }
  .k-calendar-navigation .k-content {
    background: transparent;
    height: auto;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0; }
    .k-calendar-navigation .k-content ul {
      width: 5em; }
    .k-calendar-navigation .k-content li {
      height: 2em;
      line-height: 2em;
      cursor: pointer;
      padding: 0 1em; }
  .k-calendar-navigation .k-calendar-navigation-marker {
    font-weight: bold; }
  .k-calendar-navigation .k-calendar-navigation-highlight {
    position: absolute;
    top: 50%;
    margin-top: -1em;
    right: 0;
    width: 100%;
    height: 2em;
    box-sizing: border-box;
    border-width: 1px 0;
    border-style: solid; }

.k-calendar.k-calendar-range .k-calendar-view {
  width: auto; }

.k-calendar.k-calendar-range table {
  display: flex;
  flex-direction: row; }
  .k-calendar.k-calendar-range table > tbody {
    padding: 0 1em; }
  .k-calendar.k-calendar-range table > tbody + tbody {
    margin-left: 32px; }
  .k-calendar.k-calendar-range table > tbody:only-child {
    margin-left: 0; }

.k-calendar.k-calendar-range > .k-button {
  border-radius: 0;
  width: 44px; }

.k-calendar.k-calendar-range > .k-calendar-view {
  border-width: 0 1px;
  border-style: solid;
  border-color: inherit; }

.k-calendar {
  background-clip: padding-box; }
  .k-ie11 .k-calendar,
  .k-edge12 .k-calendar,
  .k-edge13 .k-calendar {
    background-clip: border-box; }
  .k-calendar .k-footer .k-nav-today {
    color: #607d8b; }
    .k-calendar .k-footer .k-nav-today:hover {
      color: #90a4ae; }
  .k-calendar .k-content thead {
    color: #000000;
    background-color: transparent; }
  .k-calendar .k-header {
    border-bottom-color: #c2c2c2;
    box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.1); }
  .k-calendar .k-content .k-today {
    color: #607d8b; }
    .k-calendar .k-content .k-today.k-state-selected .k-link {
      color: #ffffff; }
  .k-calendar .k-alt {
    background-color: #f2f2f2;
    color: #000000; }
  .k-calendar .k-content .k-alt {
    border-right-color: #c2c2c2; }
  .k-calendar .k-state-selected {
    color: inherit;
    background: transparent;
    border-color: transparent; }
  .k-calendar .k-state-selected .k-link.k-state-hover,
  .k-calendar .k-weekend.k-state-selected .k-link.k-state-hover {
    background-color: #36505f; }
  .k-calendar .k-state-focused .k-link {
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
  .k-calendar .k-state-selected.k-state-focused .k-link {
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
  .k-calendar .k-calendar-navigation {
    box-shadow: inset -1px 0 #c2c2c2;
    background-color: #f5f5f5; }
    .k-calendar .k-calendar-navigation .k-content > ul > li:hover {
      color: #90a4ae; }
  .k-calendar .k-calendar-navigation-highlight {
    border-color: #c2c2c2;
    background-color: #ffffff; }
  .k-calendar .k-calendar-weekdays thead {
    background-color: #ffffff; }
  .k-calendar[dir='rtl'] .k-calendar-navigation,
  .k-rtl .k-calendar .k-calendar-navigation {
    box-shadow: inset 1px 0 #c2c2c2; }
  .k-calendar:not(.k-calendar-infinite) .k-weekend {
    background-color: #f9f9f9; }
  .k-calendar:not(.k-calendar-infinite) .k-state-hover .k-link {
    background-color: #90a4ae; }
  .k-calendar:not(.k-calendar-infinite) .k-content .k-state-selected.k-state-hover .k-link,
  .k-calendar:not(.k-calendar-infinite) .k-content .k-weekend .k-state-selected.k-state-hover .k-link {
    background-color: #36505f; }

.k-calendar.k-calendar-range .k-calendar-view::after {
  display: none; }

.k-calendar.k-calendar-range .k-calendar-view .k-range-start,
.k-calendar.k-calendar-range .k-calendar-view .k-range-end,
.k-calendar.k-calendar-range .k-calendar-view .k-range-mid {
  background-image: linear-gradient(to bottom, #ffffff, #ffffff 2%, #a2b5bf 2%, #a2b5bf 98%, #ffffff 98%, #ffffff 100%); }

.k-calendar.k-calendar-range .k-calendar-view .k-range-start.k-range-end {
  background-image: none;
  background-color: transparent; }

.k-calendar.k-calendar-range .k-calendar-view .k-range-start .k-link,
.k-calendar.k-calendar-range .k-calendar-view .k-range-end .k-link {
  background-color: #607d8b; }

.k-calendar.k-calendar-range .k-calendar-view .k-range-start.k-state-active .k-link,
.k-calendar.k-calendar-range .k-calendar-view .k-range-end.k-state-active .k-link {
  box-shadow: inset 0 0 2px 2px rgba(0, 0, 0, 0.2); }

.k-calendar.k-calendar-range .k-calendar-view .k-range-split-start,
.k-calendar.k-calendar-range .k-calendar-view .k-range-split-end {
  position: relative; }
  .k-calendar.k-calendar-range .k-calendar-view .k-range-split-start::after,
  .k-calendar.k-calendar-range .k-calendar-view .k-range-split-end::after {
    content: "";
    display: block;
    position: absolute;
    top: 2%;
    bottom: 2%;
    width: 5px; }

.k-calendar.k-calendar-range .k-calendar-view .k-range-split-start::after {
  left: -5px;
  right: auto;
  background-image: linear-gradient(to left, #a2b5bf, transparent 100%); }

.k-calendar.k-calendar-range .k-calendar-view .k-range-split-end::after {
  right: -5px;
  left: auto;
  background-image: linear-gradient(to right, #a2b5bf, transparent 100%); }

.k-datepicker .k-i-warning,
.k-datetimepicker .k-i-warning,
.k-timepicker .k-i-warning {
  display: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  overflow: visible; }

.k-datepicker .k-state-invalid .k-i-warning,
.k-datetimepicker .k-state-invalid .k-i-warning,
.k-timepicker .k-state-invalid .k-i-warning {
  display: inline-block; }

.k-datepicker .k-dateinput,
.k-datetimepicker .k-dateinput,
.k-timepicker .k-dateinput {
  width: 100%;
  flex: 1 1 0%;
  margin: 0; }

.k-datepicker .k-dateinput-wrap,
.k-datetimepicker .k-dateinput-wrap,
.k-timepicker .k-dateinput-wrap {
  border: 0;
  border-radius: 4px 0 0 4px; }

.k-rtl .k-datepicker .k-i-warning, .k-datepicker[dir='rtl'] .k-i-warning, .k-rtl
.k-datetimepicker .k-i-warning,
.k-datetimepicker[dir='rtl'] .k-i-warning, .k-rtl
.k-timepicker .k-i-warning,
.k-timepicker[dir='rtl'] .k-i-warning {
  right: auto; }

.k-datepicker .k-i-warning,
.k-timepicker .k-i-warning {
  right: calc( calc( 8px + 1.42857em) + 4px); }

.k-rtl .k-datepicker .k-i-warning, .k-datepicker[dir='rtl'] .k-i-warning, .k-rtl
.k-timepicker .k-i-warning,
.k-timepicker[dir='rtl'] .k-i-warning {
  left: calc( calc( 8px + 1.42857em) + 4px); }

.k-datetimepicker .k-i-warning {
  right: calc( calc( 16px + 2.85714em) + 4px); }

.k-rtl .k-datetimepicker .k-i-warning, .k-datetimepicker[dir='rtl'] .k-i-warning {
  left: calc( calc( 16px + 2.85714em) + 4px); }

.k-datetimepicker .k-select {
  padding: 0;
  border-left-width: 0;
  align-items: stretch; }

.k-datetimepicker .k-link {
  padding: 4px;
  border-width: 0 0 0 1px;
  border-style: solid;
  border-color: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box; }

.k-dateinput {
  position: relative;
  border-width: 0; }
  .k-dateinput .k-i-warning {
    display: none;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    overflow: visible; }
  .k-dateinput.k-state-invalid .k-i-warning {
    display: inline-block; }
  .k-rtl .k-dateinput .k-i-warning, .k-dateinput[dir='rtl'] .k-i-warning {
    left: 8px;
    right: auto; }

.k-time-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  line-height: 2em; }
  .k-time-header .k-time-now {
    border-width: 0;
    background: transparent;
    line-height: inherit; }

.k-time-list-wrapper {
  display: inline-block;
  overflow: hidden;
  box-sizing: content-box;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  padding: 20px 0;
  text-align: center;
  width: 5em;
  height: 200px; }
  .k-time-list-wrapper .k-title {
    display: block;
    text-align: center;
    font-size: 10px;
    position: absolute;
    text-transform: capitalize;
    font-weight: bold;
    min-width: 100%;
    height: 1.5em;
    line-height: 1.5em;
    margin-top: -20px;
    background: transparent; }
  .k-time-list-wrapper.k-state-focused::before, .k-time-list-wrapper.k-state-focused::after {
    display: block;
    content: " ";
    position: absolute;
    width: 100%;
    left: 0;
    pointer-events: none;
    height: calc(50% - 1em);
    box-sizing: border-box;
    border-style: solid; }
  .k-time-list-wrapper.k-state-focused::before {
    top: 0;
    border-width: 2px 2px 0; }
  .k-time-list-wrapper.k-state-focused::after {
    bottom: 0;
    border-width: 0 2px 2px; }

.k-time-container {
  position: absolute;
  display: block;
  overflow-x: hidden;
  overflow-y: scroll;
  line-height: 1.42857;
  left: 0;
  right: 0;
  top: 20px;
  bottom: 20px;
  padding-right: 100px;
  padding-left: 100px;
  margin-left: -100px;
  margin-right: -100px;
  margin-right: -117px; }
  .k-time-container > ul {
    height: auto;
    width: 5em; }

.k-time-list-container {
  display: flex;
  position: relative; }

.k-time-list {
  position: absolute;
  display: flex;
  z-index: 10;
  outline: 0;
  bottom: 0;
  right: 0;
  left: 0;
  top: 0; }
  .k-time-list::before, .k-time-list::after {
    display: block;
    position: absolute;
    content: " ";
    height: 0;
    line-height: 0;
    z-index: 1;
    width: 200%;
    left: -50%; }
  .k-time-list::before {
    top: 0; }
  .k-time-list::after {
    bottom: 0; }

.k-time-list .k-item {
  padding: 4px 8px;
  min-height: calc( 1.42857em + 2px);
  line-height: calc( 1.42857em + 2px); }

.k-time-highlight {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  width: 100%;
  height: calc( 8px + 1.42857em);
  z-index: 1;
  border-width: 1px 0;
  border-style: solid;
  border-radius: .1px; }

.k-time-container .k-scrollable-placeholder {
  position: absolute;
  width: 1px;
  top: 0;
  right: 0; }

.k-time-separator {
  display: inline-flex;
  align-self: center;
  justify-content: center;
  height: 100%;
  z-index: 11; }

.k-datepicker .k-picker-wrap,
.k-timepicker .k-picker-wrap,
.k-datetimepicker .k-picker-wrap {
  background-clip: padding-box; }
  .k-ie11 .k-datepicker .k-picker-wrap,
  .k-edge12 .k-datepicker .k-picker-wrap,
  .k-edge13 .k-datepicker .k-picker-wrap, .k-ie11
  .k-timepicker .k-picker-wrap,
  .k-edge12
  .k-timepicker .k-picker-wrap,
  .k-edge13
  .k-timepicker .k-picker-wrap, .k-ie11
  .k-datetimepicker .k-picker-wrap,
  .k-edge12
  .k-datetimepicker .k-picker-wrap,
  .k-edge13
  .k-datetimepicker .k-picker-wrap {
    background-clip: border-box; }

.k-datepicker .k-select,
.k-timepicker .k-select,
.k-datetimepicker .k-select {
  padding: 0;
  width: calc( 8px + 1.42857em);
  border-width: 0; }

.k-datepicker:hover .k-select,
.k-datepicker .k-state-hover .k-select,
.k-timepicker:hover .k-select,
.k-timepicker .k-state-hover .k-select,
.k-datetimepicker:hover .k-select,
.k-datetimepicker .k-state-hover .k-select {
  border-color: inherit; }

.k-datepicker .k-state-focused .k-select,
.k-datepicker .k-state-active .k-select,
.k-timepicker .k-state-focused .k-select,
.k-timepicker .k-state-active .k-select,
.k-datetimepicker .k-state-focused .k-select,
.k-datetimepicker .k-state-active .k-select {
  border-color: inherit; }

.k-datepicker .k-picker-wrap.k-state-invalid,
.k-datetimepicker .k-picker-wrap.k-state-invalid,
.k-timepicker .k-picker-wrap.k-state-invalid {
  transition: none;
  border-color: #f44336; }
  .k-datepicker .k-picker-wrap.k-state-invalid .k-input,
  .k-datetimepicker .k-picker-wrap.k-state-invalid .k-input,
  .k-timepicker .k-picker-wrap.k-state-invalid .k-input {
    color: #f44336; }

.k-datepicker .k-i-warning,
.k-datetimepicker .k-i-warning,
.k-timepicker .k-i-warning {
  color: #f44336; }

.k-time-header .k-title {
  font-weight: bold; }

.k-time-header .k-time-now {
  color: #607d8b;
  cursor: pointer; }
  .k-time-header .k-time-now:hover, .k-time-header .k-time-now:focus {
    color: #90a4ae; }

.k-time-list-wrapper {
  background-color: #f8f8f8; }
  .k-time-list-wrapper .k-title {
    z-index: 12;
    background: #f8f8f8;
    opacity: 0.6; }
  .k-time-list-wrapper.k-state-focused .k-title {
    color: #000;
    opacity: 1; }
  .k-time-list-wrapper.k-state-focused::before, .k-time-list-wrapper.k-state-focused::after {
    background-color: rgba(0, 0, 0, 0.04);
    border-width: 0; }

.k-time-list::before, .k-time-list::after {
  box-shadow: 0 0 3em 1.5em #f8f8f8; }

.k-time-list .k-item:hover {
  color: #607d8b; }

.k-time-container {
  background: transparent; }

.k-time-highlight {
  background-color: #ffffff;
  border-color: #c2c2c2; }

.k-datetimepicker .k-select {
  width: auto; }

.k-datetimepicker .k-link {
  padding: 0;
  width: calc( 8px + 1.42857em);
  border-width: 0; }

.k-dateinput .k-dateinput-wrap {
  background-clip: padding-box; }
  .k-ie11 .k-dateinput .k-dateinput-wrap,
  .k-edge12 .k-dateinput .k-dateinput-wrap,
  .k-edge13 .k-dateinput .k-dateinput-wrap {
    background-clip: border-box; }

.k-dateinput .k-select {
  padding: 0;
  width: calc( 8px + 1.42857em);
  border-width: 0; }
  .k-dateinput .k-select > .k-state-selected,
  .k-dateinput .k-select > .k-state-active {
    color: black;
    box-shadow: inset 0 3px 4px 0 rgba(0, 0, 0, 0.06); }

.k-dateinput.k-state-invalid .k-textbox {
  color: #f44336;
  border-color: #f44336; }

.k-dateinput.k-state-invalid .k-i-warning {
  color: #f44336; }

.k-textbox,
.k-input.k-textbox,
.k-textarea {
  border-radius: 4px;
  padding: 4px 8px;
  width: 12.4em;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  outline: 0;
  font: inherit;
  font-size: 14px;
  line-height: 1.42857em;
  display: inline-flex;
  vertical-align: middle;
  position: relative;
  -webkit-appearance: none; }

.k-textbox,
.k-input.k-textbox {
  height: calc(1.42857em + (4px * 2) + (1px * 2)); }

.k-textarea {
  width: 18em;
  min-height: calc((1.42857em * 4) + (4px * 2)); }

.k-maskedtextbox {
  display: inline-flex;
  border-width: 0; }
  .k-maskedtextbox .k-textbox {
    flex: 1 0 0;
    min-width: 0; }

.k-input,
.k-textbox > input {
  padding: 0;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  outline: 0;
  color: inherit;
  background: none;
  font: inherit;
  font-size: 14px;
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-appearance: none; }

.k-input::-ms-clear,
.k-textbox > input::-ms-clear,
.k-textbox::-ms-clear {
  display: none; }

.k-textbox:focus {
  box-shadow: 0 2px 2px 1px rgba(0, 0, 0, 0.06); }

.k-textbox:disabled, .k-textbox[disabled], .k-textbox.k-state-disabled {
  outline: none;
  cursor: default;
  opacity: 0.6;
  filter: grayscale(0.1);
  pointer-events: none;
  box-shadow: none; }

.k-textbox-container {
  position: relative;
  padding-top: 1.07143em;
  display: inline-flex;
  width: 12.4em;
  flex-direction: column;
  justify-content: stretch; }
  .k-textbox-container > .k-label {
    pointer-events: none;
    position: absolute;
    line-height: 1.42857em;
    cursor: text;
    top: calc( 1.07143em + 5px);
    left: 9px;
    transition: transform 0.2s ease-out, color 0.2s ease-out; }
  .k-textbox-container > .k-textbox,
  .k-textbox-container > .k-textarea,
  .k-textbox-container > .k-widget {
    flex: 1 1 auto;
    width: auto; }
  .k-textbox-container.k-state-empty > .k-label {
    transform: translate(0, 0) scale(1); }
  .k-textbox-container > .k-label,
  .k-textbox-container.k-state-focused > .k-label {
    transform: translate(-8px, -3px) translate(-1px, -1.07143em) translate(-12.5%, -9.375%) scale(0.75); }
  .k-rtl .k-textbox-container > .k-label, .k-textbox-container[dir='rtl'] > .k-label {
    left: auto;
    right: 9px; }
  .k-rtl .k-textbox-container.k-state-empty > .k-label, .k-textbox-container[dir='rtl'].k-state-empty > .k-label {
    transform: translate(0, 0) scale(1); }
  .k-rtl .k-textbox-container > .k-label,
  .k-rtl .k-textbox-container.k-state-focused > .k-label, .k-textbox-container[dir='rtl'] > .k-label,
  .k-textbox-container[dir='rtl'].k-state-focused > .k-label {
    transform: translate(8px, -3px) translate(1px, -1.07143em) translate(12.5%, -9.375%) scale(0.75); }

.k-checkbox,
.k-radio {
  margin: 0;
  padding: 0;
  clip: rect(0, 0, 0, 0);
  overflow: hidden;
  position: absolute;
  opacity: 0;
  -webkit-appearance: none;
  pointer-events: none; }

.k-radio:disabled + .k-radio-label,
.k-checkbox:disabled + .k-checkbox-label {
  outline: none;
  cursor: default;
  opacity: 0.6;
  filter: grayscale(0.1);
  pointer-events: none;
  box-shadow: none; }

.k-checkbox-label,
.k-radio-label {
  margin: 0;
  padding-left: 20px;
  min-height: 16px;
  line-height: 17px;
  vertical-align: text-top;
  display: inline-flex;
  align-items: flex-start;
  position: relative;
  cursor: pointer; }
  .k-checkbox-label .k-ripple,
  .k-radio-label .k-ripple {
    top: 8px;
    left: 8px;
    right: auto;
    bottom: auto;
    width: 40px;
    height: 40px;
    transform: translate(-50%, -50%);
    border-radius: 50%; }
  .k-checkbox-label .k-ripple-blob,
  .k-radio-label .k-ripple-blob {
    top: 50% !important;
    left: 50% !important;
    width: 200% !important;
    height: 200% !important; }
  .k-checkbox-label.k-no-text,
  .k-radio-label.k-no-text {
    padding: 0;
    width: 16px;
    height: 16px;
    display: inline-block; }

.k-checkbox-label::before,
.k-checkbox-label::after,
.k-radio-label::before,
.k-radio-label::after {
  font-size: 16px;
  font-family: "WebComponentsIcons", monospace;
  box-sizing: border-box;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0; }

.k-checkbox-label::before,
.k-radio-label::before {
  content: "";
  width: 16px;
  height: 16px;
  border-width: 1px;
  border-style: solid; }

.k-checkbox-label::before {
  border-radius: 4px; }

.k-radio-label::before {
  border-radius: 50%; }

.k-checkbox-label::after {
  content: "\e118";
  width: 16px;
  height: 16px;
  transform: scale(0); }

.k-checkbox:checked + .k-checkbox-label::after {
  border-radius: 4px;
  transform: scale(1); }

.k-checkbox:indeterminate + .k-checkbox-label::after {
  content: "";
  transform: scale(1);
  width: 8px;
  height: 8px;
  top: 4px;
  left: 4px; }

.k-radio-label::after {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: absolute;
  top: 4px;
  left: 4px;
  transform: scale(0); }

.k-radio:checked + .k-radio-label::after {
  transform: scale(1); }

.k-radio-list .k-radio-label {
  line-height: 20px; }

.k-fieldset {
  margin: 30px;
  border-width: 1px 0 0;
  border-style: solid;
  padding: 25px 0 0; }
  .k-fieldset > legend {
    margin-left: 0;
    padding: 0 8px 0 0;
    text-transform: uppercase; }

.k-form,
.k-form-inline {
  font-size: 14px;
  line-height: 1.42857;
  padding: 16px; }
  .k-form fieldset,
  .k-form-inline fieldset {
    border-width: 1px 0 0;
    border-style: solid;
    margin: 32px 0;
    padding: 0; }
    .k-form fieldset:first-child:first-of-type,
    .k-form-inline fieldset:first-child:first-of-type {
      margin-top: 0; }
    .k-form fieldset:last-child:last-of-type,
    .k-form-inline fieldset:last-child:last-of-type {
      margin-bottom: 0; }
  .k-form legend,
  .k-form-inline legend {
    font-size: 12px;
    text-align: left;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 12px;
    text-transform: uppercase;
    padding: 0 8px 0 0;
    width: auto; }
  .k-form .k-form-field,
  .k-form-inline .k-form-field {
    display: block;
    text-align: left;
    margin-bottom: 12px; }
    .k-form .k-form-field > span,
    .k-form-inline .k-form-field > span {
      line-height: 1.42857; }
    .k-form .k-form-field:last-child,
    .k-form-inline .k-form-field:last-child {
      margin-bottom: 0; }
  .k-form .k-alert-error,
  .k-form-inline .k-alert-error {
    font-size: 12px;
    margin-top: 8px; }
  .k-form .k-field-info,
  .k-form-inline .k-field-info {
    display: inline-block;
    font-size: 10px;
    line-height: 1;
    margin: 0 8px; }
  .k-form .k-checkbox-label,
  .k-form .k-radio-label,
  .k-form-inline .k-checkbox-label,
  .k-form-inline .k-radio-label {
    margin-right: 16px;
    align-self: center; }

.k-form input,
.k-form label:not(.k-checkbox-label):not(.k-radio-label),
.k-form .k-widget:not(.k-calendar) {
  display: block; }

.k-form .k-form-field > span:not(.k-widget) {
  display: block;
  padding: 4px 0; }

.k-form .k-form-field > input {
  width: 100%; }

.k-form-inline .k-form-field {
  display: flex;
  align-items: flex-start; }
  .k-form-inline .k-form-field > span:not(.k-widget),
  .k-form-inline .k-form-field > label:not(.k-checkbox-label):not(.k-radio-label) {
    width: 25%;
    text-align: right;
    line-height: 1.42857;
    padding: 5px 0;
    padding-right: 12px;
    align-self: center; }
  .k-form-inline .k-form-field > input {
    flex: 1 1 auto; }
  .k-form-inline .k-form-field .k-field-info {
    display: block;
    margin: 0; }

.k-treeview .k-checkbox-label,
.k-grid .k-checkbox-label {
  cursor: default;
  outline: 0; }

.k-textbox,
.k-input.k-textbox,
.k-textarea {
  background-clip: padding-box; }
  .k-ie11 .k-textbox,
  .k-edge12 .k-textbox,
  .k-edge13 .k-textbox, .k-ie11
  .k-input.k-textbox,
  .k-edge12
  .k-input.k-textbox,
  .k-edge13
  .k-input.k-textbox, .k-ie11
  .k-textarea,
  .k-edge12
  .k-textarea,
  .k-edge13
  .k-textarea {
    background-clip: border-box; }
  .k-textbox:hover, .k-textbox.k-state-hover,
  .k-input.k-textbox:hover,
  .k-input.k-textbox.k-state-hover,
  .k-textarea:hover,
  .k-textarea.k-state-hover {
    border-color: #607d8b; }
  .k-textbox:focus, .k-textbox.k-state-focus,
  .k-input.k-textbox:focus,
  .k-input.k-textbox.k-state-focus,
  .k-textarea:focus,
  .k-textarea.k-state-focus {
    color: #000000;
    border-color: #607d8b; }
  .k-textbox.k-state-invalid, .k-textbox.ng-invalid.ng-touched, .k-textbox.ng-invalid.ng-dirty,
  .k-input.k-textbox.k-state-invalid,
  .k-input.k-textbox.ng-invalid.ng-touched,
  .k-input.k-textbox.ng-invalid.ng-dirty,
  .k-textarea.k-state-invalid,
  .k-textarea.ng-invalid.ng-touched,
  .k-textarea.ng-invalid.ng-dirty {
    color: #f44336;
    border-color: #f44336; }
  .k-textbox::-moz-selection,
  .k-input.k-textbox::-moz-selection,
  .k-textarea::-moz-selection {
    background-color: #607d8b;
    color: #ffffff; }

.k-textbox::selection,
.k-input::selection,
.k-textarea::selection {
  background-color: #607d8b;
  color: #ffffff; }

.k-state-disabled .k-textbox::selection, .k-state-disabled
.k-input::selection, .k-state-disabled
.k-textarea::selection {
  color: #000000;
  background-color: transparent; }

.k-textbox::placeholder,
.k-input::placeholder,
.k-input.k-textbox::placeholder,
.k-textarea::placeholder {
  color: rgba(0, 0, 0, 0.5); }

.k-radio-label::before {
  border-color: #c2c2c2;
  background-color: #ffffff; }

.k-radio:focus + .k-radio-label::before {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.06); }

.k-radio:checked + .k-radio-label::before {
  border-color: #607d8b;
  background-color: #607d8b; }

.k-radio:checked + .k-radio-label::after {
  background-color: #ffffff; }

.k-radio:checked:focus + .k-radio-label::before {
  box-shadow: 0 0 0 2px rgba(96, 125, 139, 0.3); }

.k-radio:hover + .k-radio-label::before {
  border-color: #607d8b;
  background-color: #ffffff; }

.k-radio:hover + .k-radio-label::after {
  background-color: #607d8b; }

.k-checkbox-label::before {
  background-color: #ffffff;
  border-color: #c2c2c2; }

.k-checkbox:indeterminate + .k-checkbox-label::after {
  background-color: #607d8b; }

.k-checkbox:focus + .k-checkbox-label::before {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.06); }

.k-checkbox:checked + .k-checkbox-label::before {
  border-color: #607d8b;
  background-color: #607d8b; }

.k-checkbox:checked + .k-checkbox-label::after {
  color: #ffffff; }

.k-checkbox:checked:focus + .k-checkbox-label::before {
  box-shadow: 0 0 0 2px rgba(96, 125, 139, 0.3); }

.k-checkbox:hover + .k-checkbox-label::before {
  border-color: #607d8b;
  background-color: #ffffff; }

.k-checkbox:hover + .k-checkbox-label::after {
  color: #607d8b; }

fieldset {
  border-color: #c2c2c2; }

fieldset legend {
  color: #000000; }

.k-form,
.k-form-inline {
  color: #000000; }
  .k-form fieldset legend,
  .k-form-inline fieldset legend {
    color: black; }
  .k-form .k-field-info,
  .k-form-inline .k-field-info {
    color: #545454; }
  .k-form .k-alert-error,
  .k-form-inline .k-alert-error {
    color: #f44336; }

.k-required,
.k-required.k-field-info {
  color: #607d8b; }

.k-popup .k-check-all {
  margin: 6px 6px 0; }

.k-popup .k-check-all + .k-treeview {
  margin: 0 6px 6px; }

.k-popup .k-check-all {
  padding: 4px 16px; }

.k-popup .k-check-all .k-checkbox-label {
  padding-left: calc( 20px + 8px); }

.k-popup.k-rtl .k-check-all .k-checkbox-label,
[dir='rtl'] .k-popup .k-check-all .k-checkbox-label {
  padding-left: 0;
  padding-right: calc( 20px + 8px); }

html .k-upload {
  position: relative; }

.k-upload .k-upload-button {
  min-width: 7em;
  margin: 8px; }

.k-upload .k-dropzone {
  display: flex;
  align-items: center;
  position: relative;
  border-width: 0;
  background-color: transparent; }
  .k-upload .k-dropzone em,
  .k-upload .k-dropzone .k-dropzone-hint {
    margin-left: 1em;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    opacity: 0;
    white-space: nowrap;
    position: relative;
    vertical-align: middle;
    visibility: hidden;
    font-style: italic;
    display: none; }
  .k-upload .k-dropzone .k-upload-status {
    display: flex;
    align-items: center;
    position: relative;
    padding: 4px 8px;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857; }
    .k-upload .k-dropzone .k-upload-status > .k-icon {
      margin-right: 6px; }
  .k-upload .k-dropzone.k-dropzone-active em,
  .k-upload .k-dropzone.k-dropzone-active .k-dropzone-hint {
    display: block;
    visibility: visible;
    opacity: 1; }
  .k-upload .k-dropzone.k-dropzone-active .k-upload-status {
    display: none; }

.k-upload .k-upload-status-total {
  margin-left: 8px;
  margin-right: 8px; }

.k-upload .k-upload-files {
  padding-bottom: 4px;
  border-width: 1px 0 0;
  border-style: solid;
  border-color: inherit;
  margin: 0; }
  .k-upload .k-upload-files .k-file-multiple,
  .k-upload .k-upload-files .k-file-single {
    display: block;
    width: 100%; }
  .k-upload .k-upload-files .k-file {
    padding: 8px;
    border-width: 0 0 1px;
    border-style: solid;
    border-color: inherit;
    display: flex;
    align-items: center;
    position: relative;
    line-height: 1.42857; }
  .k-upload .k-upload-files .k-progress {
    position: absolute;
    bottom: 0;
    left: 0; }
  .k-upload .k-upload-files .k-filename {
    margin-left: 1em;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative; }
  .k-upload .k-upload-files .k-upload-status {
    position: absolute;
    right: 8px;
    top: 8px; }
    .k-upload .k-upload-files .k-upload-status .k-button {
      padding: 0;
      border-radius: 50%; }
    .k-upload .k-upload-files .k-upload-status .k-icon {
      vertical-align: middle; }
  .k-upload .k-upload-files .k-upload-pct {
    font-weight: normal;
    vertical-align: middle; }
  .k-upload .k-upload-files ~ .k-clear-selected,
  .k-upload .k-upload-files ~ .k-upload-selected {
    margin-top: -4px;
    border-width: 0; }
  .k-upload .k-upload-files ~ .k-upload-selected {
    border-left-width: 1px;
    border-left-style: solid;
    margin-left: -1px; }
  .k-upload .k-upload-files .k-file-name,
  .k-upload .k-upload-files .k-file-size,
  .k-upload .k-upload-files .k-file-validation-message,
  .k-upload .k-upload-files .k-file-information {
    display: block; }
  .k-upload .k-upload-files .k-file-name {
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 4px; }
  .k-upload .k-upload-files .k-file-size,
  .k-upload .k-upload-files .k-file-information,
  .k-upload .k-upload-files .k-file-validation-message {
    font-size: 9.432px; }
  .k-upload .k-upload-files .k-file-information {
    text-indent: 1px; }
  .k-upload .k-upload-files .k-file-extension-wrapper,
  .k-upload .k-upload-files .k-multiple-files-extension-wrapper,
  .k-upload .k-upload-files .k-file-invalid-extension-wrapper,
  .k-upload .k-upload-files .k-multiple-files-invalid-extension-wrapper {
    position: absolute;
    top: 8px;
    width: 24px;
    height: 34px;
    border-width: 2px;
    border-style: solid;
    vertical-align: top;
    font-size: 7.98px;
    text-transform: uppercase;
    margin: 1px 0;
    box-sizing: content-box; }
  .k-upload .k-upload-files .k-file-invalid-extension-wrapper,
  .k-upload .k-upload-files .k-multiple-files-invalid-extension-wrapper {
    font-size: 1.2em; }
  .k-upload .k-upload-files .k-multiple-files-extension-wrapper,
  .k-upload .k-upload-files .k-multiple-files-invalid-extension-wrapper {
    margin-top: 4px; }
  .k-upload .k-upload-files .k-file-state {
    visibility: hidden; }
  .k-upload .k-upload-files .k-file-name-size-wrapper {
    display: block;
    margin-left: calc(24px + 1em);
    margin-right: calc(16px*2 + 3.5em);
    overflow: hidden;
    min-height: 38px; }
  .k-upload .k-upload-files .k-file-extension-wrapper::before,
  .k-upload .k-upload-files .k-multiple-files-extension-wrapper::before,
  .k-upload .k-upload-files .k-multiple-files-extension-wrapper::after,
  .k-upload .k-upload-files .k-file-invalid-extension-wrapper::before,
  .k-upload .k-upload-files .k-multiple-files-invalid-extension-wrapper::before,
  .k-upload .k-upload-files .k-multiple-files-invalid-extension-wrapper::after {
    position: absolute;
    content: '';
    display: inline-block;
    border-style: solid; }
  .k-upload .k-upload-files .k-file-extension-wrapper::before,
  .k-upload .k-upload-files .k-multiple-files-extension-wrapper::before,
  .k-upload .k-upload-files .k-file-invalid-extension-wrapper::before,
  .k-upload .k-upload-files .k-multiple-files-invalid-extension-wrapper::before {
    top: -1px;
    right: -1px;
    width: 0;
    height: 0;
    border-width: 6px;
    margin-top: -1px;
    margin-right: -1px; }
  .k-upload .k-upload-files .k-multiple-files-extension-wrapper::after,
  .k-upload .k-upload-files .k-multiple-files-invalid-extension-wrapper::after {
    top: -6px;
    left: -6px;
    width: 15px;
    height: 35px;
    border-width: 2px 0 0 2px; }
  .k-upload .k-upload-files .k-file-extension,
  .k-upload .k-upload-files .k-file-invalid-icon {
    position: absolute;
    bottom: 0;
    line-height: normal; }
  .k-upload .k-upload-files .k-file-invalid-icon {
    margin-left: 4px; }
  .k-upload .k-upload-files .k-file-extension {
    margin-left: .2em;
    margin-bottom: .3em;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    left: 0; }
  .k-upload .k-upload-files .k-upload-action {
    margin-left: 8px; }

.k-upload .k-action-buttons {
  margin: -4px 0 0;
  padding: 0;
  border-top: 0; }

.k-upload .k-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px; }

.k-upload .k-file-invalid > .k-progress {
  width: 100%; }

.k-upload[dir="rtl"] .k-dropzone .k-upload-status > .k-icon,
.k-rtl .k-upload .k-dropzone .k-upload-status > .k-icon {
  margin-left: 6px;
  margin-right: 0; }

.k-upload[dir="rtl"] .k-upload-files .k-upload-status,
.k-rtl .k-upload .k-upload-files .k-upload-status {
  right: auto;
  left: 8px; }

.k-upload[dir="rtl"] .k-upload-files ~ .k-upload-selected,
.k-rtl .k-upload .k-upload-files ~ .k-upload-selected {
  border-left-width: 0;
  border-right-width: 1px;
  border-right-style: solid;
  margin-left: 0; }

.k-upload[dir="rtl"] .k-upload-files .k-file-invalid-icon,
.k-rtl .k-upload .k-upload-files .k-file-invalid-icon {
  margin-left: 0;
  left: 4px; }

.k-upload[dir="rtl"] .k-file-name-size-wrapper,
.k-rtl .k-upload .k-file-name-size-wrapper {
  margin-right: calc(24px + 1em);
  margin-left: calc(16px + 8px); }

.k-upload[dir="rtl"] .k-file-extension,
.k-rtl .k-upload .k-file-extension {
  right: 0;
  left: auto;
  margin-right: .4em;
  margin-left: 0; }

.k-upload[dir="rtl"] .k-upload-action,
.k-rtl .k-upload .k-upload-action {
  margin-left: 0;
  margin-right: 8px; }

.k-upload-button {
  position: relative;
  overflow: hidden;
  direction: ltr; }
  .k-upload-button input {
    font: 170px monospace !important;
    margin: 0;
    padding: 0;
    filter: alpha(opacity=0);
    opacity: 0;
    cursor: pointer;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1; }

.k-ie9 .k-upload-status-total {
  top: 1.5em; }
  .k-ie9 .k-upload-status-total > .k-icon {
    margin-top: -3px; }

.k-ie9 .k-upload-button {
  margin: 2px 4px; }

.k-upload {
  background-color: #f5f5f5;
  border-color: #c2c2c2;
  background-clip: padding-box; }
  .k-ie11 .k-upload,
  .k-edge12 .k-upload,
  .k-edge13 .k-upload {
    background-clip: border-box; }
  .k-upload .k-upload-files,
  .k-upload .k-upload-selected {
    border-color: #c2c2c2; }
  .k-upload .k-file {
    background-color: #ffffff;
    border-color: #c2c2c2;
    outline: none; }
    .k-upload .k-file.k-state-focused {
      box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
    .k-upload .k-file .k-upload-action {
      opacity: .6;
      color: inherit;
      background: none;
      border-width: 0;
      box-shadow: none; }
      .k-upload .k-file .k-upload-action:hover {
        opacity: 1; }
      .k-upload .k-file .k-upload-action.k-state-focused {
        box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.13); }
    .k-upload .k-file .k-upload-pct {
      opacity: .65; }
  .k-upload .k-file-progress {
    color: #000000; }
  .k-upload .k-file-progress .k-progress {
    background-color: #2196f3; }
  .k-upload .k-file-success .k-file-name {
    color: #009688; }
  .k-upload .k-file-success .k-progress {
    background-color: #009688; }
  .k-upload .k-file-error .k-file-name {
    color: #f44336; }
  .k-upload .k-file-error .k-progress,
  .k-upload .k-file-invalid .k-progress {
    background-color: #f44336; }
  .k-upload .k-file-extension-wrapper,
  .k-upload .k-multiple-files-extension-wrapper {
    color: #bababa;
    border-color: #bababa; }
  .k-upload .k-file-invalid .k-file-name-invalid {
    color: #f44336; }
  .k-upload .k-file-invalid-extension-wrapper,
  .k-upload .k-multiple-files-invalid-extension-wrapper,
  .k-upload .k-file-error .k-file-extension-wrapper {
    color: #f44336;
    border-color: #fcc6c2; }
  .k-upload .k-file-extension-wrapper::before,
  .k-upload .k-multiple-files-extension-wrapper::before {
    background-color: #ffffff;
    border-color: transparent transparent #bababa #bababa; }
  .k-upload .k-file-invalid-extension-wrapper::before,
  .k-upload .k-multiple-files-invalid-extension-wrapper::before,
  .k-upload .k-file-error .k-file-extension-wrapper::before {
    background-color: #ffffff;
    border-color: transparent transparent #fcc6c2 #fcc6c2; }
  .k-upload .k-multiple-files-extension-wrapper::after {
    border-top-color: #bababa;
    border-left-color: #bababa; }
  .k-upload .k-multiple-files-invalid-extension-wrapper::after {
    border-top-color: #fcc6c2;
    border-left-color: #fcc6c2; }
  .k-upload .k-file-size,
  .k-upload .k-file-information,
  .k-upload .k-file-validation-message {
    color: #bababa; }

.k-dropzone .k-i-loading {
  border-color: #f5f5f5; }

.k-dropzone .k-i-loading::before, .k-dropzone .k-loading-image::before,
.k-dropzone .k-i-loading::after,
.k-dropzone .k-loading-image::after {
  background-color: #f5f5f5; }

.k-dropzone-hovered {
  background-color: #90a4ae; }

.k-editor {
  border-collapse: separate;
  border-spacing: 0;
  vertical-align: top;
  position: relative;
  table-layout: fixed; }
  .k-editor .k-content {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    border: 0;
    background: none;
    display: block; }

table.k-editor {
  width: 100%; }

.k-editor-inline {
  border-radius: 4px;
  padding: 2px 4px;
  border: 1px solid transparent;
  word-wrap: break-word;
  overflow: auto;
  background: none;
  transition: border-color .3s; }

.k-window.k-editor-widget {
  padding: 0; }

.editorToolbarWindow {
  padding: 0;
  display: flex;
  align-items: stretch; }

.k-editortoolbar-dragHandle {
  margin: 8px;
  padding: 0;
  cursor: move; }

.k-editor-toolbar-wrap {
  border-color: inherit; }

.k-editor-toolbar {
  margin: 0;
  padding: 8px 8px;
  border-color: inherit;
  list-style-type: none;
  line-height: 1.42857;
  cursor: default;
  word-wrap: break-word;
  /* allow tools to wrap properly in IE */
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: wrap;
  position: relative; }
  .k-editor-toolbar.k-toolbar-resizable {
    flex-wrap: nowrap;
    overflow: hidden;
    flex: 1 1 0%; }
  .k-editor-toolbar li {
    display: inline-flex;
    align-items: center;
    vertical-align: middle; }
  .k-editor-toolbar .k-tool-group {
    padding: 0;
    border-width: 0;
    border-style: solid;
    border-color: inherit; }
    .k-editor-toolbar .k-tool-group .k-widget + .k-widget {
      margin-left: 8px; }
  .k-editor-toolbar .k-tool-group + .k-tool-group {
    margin-left: 8px; }
  .k-editor-toolbar .k-tool-group .k-state-disabled,
  .k-editor-toolbar .k-tool-group.k-state-disabled {
    display: none; }
  .k-editor-toolbar .k-tool {
    padding: 4px;
    width: calc( 10px + 1.42857em);
    height: calc( 10px + 1.42857em);
    border-width: 1px;
    border-style: solid;
    box-sizing: border-box;
    text-decoration: none;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    justify-content: center; }
  .k-editor-toolbar .k-tool-text {
    display: none; }
  .k-editor-toolbar .k-tool + .k-tool {
    margin-left: -1px; }
  .k-editor-toolbar .k-overflow-tools {
    position: absolute;
    top: 0;
    right: 0;
    visibility: hidden; }

.k-editable-area {
  padding: 4px;
  width: 100%;
  height: 100%;
  border-width: 1px 0 0;
  border-style: solid;
  border-color: inherit;
  outline: 0; }
  .k-resizable .k-editable-area {
    padding: 4px 4px 16px; }

.k-edit-form-content {
  flex: 1 1 auto;
  overflow: auto;
  margin: -16px -16px;
  padding: 16px 16px; }

.k-ct-popup {
  box-sizing: border-box;
  width: 190px;
  padding: 5px; }
  .k-ct-popup .k-editor-toolbar {
    text-align: center; }
    .k-ct-popup .k-editor-toolbar .k-tool {
      border-radius: 4px;
      width: 100%;
      height: auto;
      display: flex; }
    .k-ct-popup .k-editor-toolbar .k-tool-text {
      display: inline; }
  .k-ct-popup .k-ct-cell {
    margin: 1px;
    width: 20px;
    height: 20px;
    box-sizing: border-box;
    border: 1px solid;
    border-color: inherit;
    display: inline-block;
    vertical-align: top;
    overflow: hidden;
    opacity: .7; }

.k-editor .k-resize-handle {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0 13px 13px;
  border-color: transparent;
  border-bottom-color: inherit;
  cursor: se-resize; }
  .k-editor .k-resize-handle .k-i-arrow-45-down-right {
    display: none; }

.k-editor .k-overlay {
  position: absolute;
  background-color: #fff;
  opacity: 0; }

.k-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10001;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: .5; }

.k-rtl .k-editor .k-editor-widget .k-dropdown-wrap {
  padding-left: 0; }
  .k-rtl .k-editor .k-editor-widget .k-dropdown-wrap .k-select {
    border-width: 0; }

.k-editor-dialog {
  box-sizing: border-box; }
  .k-editor-dialog .k-edit-form-container {
    width: auto; }
  .k-editor-dialog .k-edit-label {
    width: 30%;
    padding: 5px 0; }
  .k-editor-dialog .k-edit-field {
    width: 66%; }

.k-filebrowser-dialog {
  display: flex; }
  .k-filebrowser-dialog .k-edit-form-container {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto; }
  .k-filebrowser-dialog .k-edit-label {
    width: 18%; }
  .k-filebrowser-dialog .k-edit-field {
    width: 75%; }

.k-filebrowser {
  max-width: 100%; }
  .k-filebrowser .k-floatwrap {
    display: flex; }
    .k-filebrowser .k-floatwrap::after {
      display: none; }
  .k-filebrowser .k-breadcrumbs {
    flex: 1; }
  .k-filebrowser .k-search-wrap {
    margin: 0 0 0 1em;
    width: 150px;
    display: flex;
    align-items: center; }
    .k-filebrowser .k-search-wrap .k-input {
      flex: 1;
      width: 100px; }
    .k-filebrowser .k-search-wrap .k-icon {
      margin: 0;
      position: static; }
  .k-filebrowser .k-filebrowser-toolbar {
    margin: 1em 0 0 0;
    padding: 8px 8px;
    display: flex;
    justify-content: space-between; }
  .k-filebrowser .k-toolbar-wrap {
    display: flex; }
    .k-filebrowser .k-toolbar-wrap > * + * {
      margin-left: 8px; }
    .k-filebrowser .k-toolbar-wrap .k-state-disabled {
      display: none; }
  .k-filebrowser .k-upload {
    padding: 0;
    border-width: 0;
    background: none; }
    .k-filebrowser .k-upload .k-upload-button {
      margin: 0; }
    .k-filebrowser .k-upload .k-upload-status {
      display: none; }
  .k-filebrowser .k-upload-files {
    display: none; }
  .k-filebrowser .k-tiles {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    height: 390px;
    max-height: 50vh;
    margin: 0 0 1em 0;
    padding: 4px 8px;
    border-top-width: 0;
    overflow: auto; }
  .k-filebrowser .k-tile {
    width: 33%;
    height: 90px;
    padding: 4px 8px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    cursor: pointer;
    border-radius: 4px; }
    .k-filebrowser .k-tile .k-i-file,
    .k-filebrowser .k-tile .k-i-folder {
      font-size: 4em; }
    .k-filebrowser .k-tile input {
      width: 100px; }
    .k-filebrowser .k-tile strong {
      display: block;
      font-weight: 400;
      overflow: hidden;
      text-overflow: ellipsis; }
  .k-filebrowser .k-tile-empty {
    display: block;
    margin: auto; }
    .k-filebrowser .k-tile-empty.k-state-selected {
      color: inherit;
      border-width: 0;
      background-image: none;
      background-color: transparent; }
    .k-filebrowser .k-tile-empty strong {
      opacity: .5;
      font-size: 3em;
      font-weight: 400; }
  .k-filebrowser .k-thumb {
    float: left;
    margin-right: 4px; }
  .k-filebrowser .k-breadcrumbs-wrap {
    position: absolute;
    left: 8px;
    top: 4px; }
    .k-filebrowser .k-breadcrumbs-wrap .k-icon {
      position: static;
      margin-top: 0; }

.k-editor-table-wizard-dialog {
  display: flex; }
  .k-editor-table-wizard-dialog .k-edit-form-container {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto; }
  .k-editor-table-wizard-dialog .k-tabstrip-wrapper {
    display: flex;
    flex: 1 1 auto;
    overflow: auto; }
    .k-editor-table-wizard-dialog .k-tabstrip-wrapper .k-tabstrip.k-root-tabs {
      flex: 1 1 auto;
      margin-bottom: 0; }
  .k-editor-table-wizard-dialog .k-edit-field > .k-checkbox {
    position: relative; }
  .k-editor-table-wizard-dialog .k-numerictextbox {
    width: 10em; }
    .k-editor-table-wizard-dialog .k-numerictextbox + .k-dropdown, .k-editor-table-wizard-dialog .k-numerictextbox + .k-dropdowntree {
      width: 4em; }
    .k-editor-table-wizard-dialog .k-numerictextbox + .k-colorpicker,
    .k-editor-table-wizard-dialog .k-numerictextbox + .k-dropdown,
    .k-editor-table-wizard-dialog .k-numerictextbox + .k-dropdowntree {
      margin-left: 4px; }
  .k-editor-table-wizard-dialog .k-colorpicker {
    vertical-align: middle; }
  .k-editor-table-wizard-dialog .k-edit-field > .k-checkbox {
    position: relative; }
  .k-editor-table-wizard-dialog .k-dropdown.k-align, .k-editor-table-wizard-dialog .k-align.k-dropdowntree {
    width: auto; }

.k-popup.k-align .k-list {
  display: flex;
  flex-flow: row wrap; }

.k-popup.k-align .k-list .k-item {
  padding: 4px;
  height: calc( 10px + 1.42857em);
  box-sizing: border-box;
  justify-content: center;
  flex: 1 0 33%; }
  .k-popup.k-align .k-list .k-item .k-icon {
    margin: 0; }

.k-popup.k-align .k-list .k-item:last-child {
  flex: 1 0 100%; }

.k-editor-inline .k-table {
  width: 100%;
  border-spacing: 0;
  margin: 0 0 1em; }
  .k-editor-inline .k-table,
  .k-editor-inline .k-table td {
    outline: 0;
    border: 1px dotted #ccc; }
  .k-editor-inline .k-table td {
    min-width: 1px;
    padding: 2px 4px; }

.k-editor-inline .k-table-resize-handle-wrapper {
  position: absolute;
  width: 5px;
  height: 5px;
  background-color: #fff;
  border: 1px solid #000;
  z-index: 100; }
  .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle {
    width: 100%;
    height: 100%; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-east {
      cursor: e-resize; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-north {
      cursor: n-resize; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-northeast {
      cursor: ne-resize; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-northwest {
      cursor: nw-resize; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-south {
      cursor: s-resize; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-southeast {
      cursor: se-resize; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-southwest {
      cursor: sw-resize; }
    .k-editor-inline .k-table-resize-handle-wrapper .k-table-resize-handle.k-resize-west {
      cursor: w-resize; }

.k-editor-inline .k-column-resize-handle-wrapper {
  position: absolute;
  height: 10px;
  width: 10px;
  cursor: col-resize;
  z-index: 2; }
  .k-editor-inline .k-column-resize-handle-wrapper .k-column-resize-handle {
    width: 100%;
    height: 100%; }
    .k-editor-inline .k-column-resize-handle-wrapper .k-column-resize-handle .k-column-resize-marker {
      width: 2px;
      height: 100%;
      margin: 0 auto;
      background-color: #00b0ff;
      display: none;
      opacity: .8; }

.k-editor-inline .k-row-resize-handle-wrapper {
  position: absolute;
  z-index: 2;
  cursor: row-resize;
  width: 10px;
  height: 10px; }
  .k-editor-inline .k-row-resize-handle-wrapper .k-row-resize-handle {
    display: table;
    width: 100%;
    height: 100%; }
  .k-editor-inline .k-row-resize-handle-wrapper .k-row-resize-marker-wrapper {
    display: table-cell;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    vertical-align: middle; }
  .k-editor-inline .k-row-resize-handle-wrapper .k-row-resize-marker {
    display: none;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 2px;
    background-color: #00b0ff;
    opacity: .8; }

.k-editor {
  background-clip: padding-box; }
  .k-editor .k-editable-area {
    border-color: #c2c2c2; }
  .k-ie11 .k-editor,
  .k-edge12 .k-editor,
  .k-edge13 .k-editor {
    background-clip: border-box; }

.k-editor-inline:hover, .k-editor-inline.k-state-active {
  border-color: #c2c2c2; }

.k-overlay {
  background-color: #000; }

.k-editor-toolbar {
  padding: 0; }
  .k-editor-toolbar .k-tool-group {
    padding: 8px 8px; }
    .k-editor-toolbar .k-tool-group .k-tool.k-group-start,
    .k-editor-toolbar .k-tool-group .k-tool.k-group-end {
      border-radius: 0; }
  .k-editor-toolbar .k-tool-group + .k-tool-group {
    margin: 0;
    border-left-width: 1px; }
  .k-editor-toolbar .k-tool + .k-tool {
    margin: 0; }

.k-editor .k-editor-toolbar-wrap a.k-tool,
.k-ct-popup .k-editor-toolbar a.k-tool,
.editorToolbarWindow.k-header.k-window-content a.k-tool {
  border-width: 0;
  background-image: none;
  background-color: transparent; }
  .k-editor .k-editor-toolbar-wrap a.k-tool.k-state-selected,
  .k-ct-popup .k-editor-toolbar a.k-tool.k-state-selected,
  .editorToolbarWindow.k-header.k-window-content a.k-tool.k-state-selected {
    border-color: #607d8b;
    color: #ffffff;
    background-color: #607d8b; }

.k-editor .k-editor-toolbar-wrap .k-tool-group,
.k-ct-popup .k-editor-toolbar .k-tool-group,
.editorToolbarWindow.k-header.k-window-content .k-tool-group {
  border-color: #c2c2c2; }

.k-editor-toolbar .k-overflow-anchor {
  padding: 8px;
  width: calc( 1.42857em + 8px + 2px + 16px);
  height: calc( 1.42857em + 8px + 2px + 16px); }

.k-multiselect {
  border-radius: 4px;
  width: 100%; }
  .k-multiselect .k-loading-hidden {
    visibility: hidden; }

.k-multiselect-wrap {
  min-height: calc( 1.42857em + 8px);
  cursor: text; }
  .k-multiselect-wrap .k-input {
    float: left; }
  .k-multiselect-wrap ul {
    vertical-align: top; }
  .k-multiselect-wrap li.k-button {
    float: left; }
  .k-multiselect-wrap .k-button {
    min-height: calc( 1.42857em + 4px);
    padding: 1px 4px;
    margin: 2px 0 0 2px;
    cursor: default;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    line-height: inherit;
    white-space: normal; }
    .k-multiselect-wrap .k-button .k-select {
      display: flex;
      cursor: pointer;
      margin-left: 8px; }
    .k-multiselect-wrap .k-button:last-child {
      margin-bottom: 2px; }
  .k-multiselect-wrap .k-searchbar {
    float: left;
    width: auto; }
    .k-multiselect-wrap .k-searchbar .k-input {
      padding-right: 0; }

[dir='rtl'] > .k-multiselect-wrap,
.k-rtl .k-multiselect-wrap {
  padding-left: 24px;
  padding-right: 0; }
  [dir='rtl'] > .k-multiselect-wrap li.k-button,
  [dir='rtl'] > .k-multiselect-wrap .k-input,
  .k-rtl .k-multiselect-wrap li.k-button,
  .k-rtl .k-multiselect-wrap .k-input {
    float: right; }
  [dir='rtl'] > .k-multiselect-wrap .k-searchbar,
  .k-rtl .k-multiselect-wrap .k-searchbar {
    float: right; }
    [dir='rtl'] > .k-multiselect-wrap .k-searchbar .k-input,
    .k-rtl .k-multiselect-wrap .k-searchbar .k-input {
      padding-left: 0;
      padding-right: 8px; }
  [dir='rtl'] > .k-multiselect-wrap .k-button,
  .k-rtl .k-multiselect-wrap .k-button {
    width: auto;
    margin-right: 2px;
    margin-left: 0; }
    [dir='rtl'] > .k-multiselect-wrap .k-button .k-select,
    .k-rtl .k-multiselect-wrap .k-button .k-select {
      margin-left: 0;
      margin-right: 8px; }

.k-multiselect-wrap {
  background-clip: padding-box; }
  .k-ie11 .k-multiselect-wrap,
  .k-edge12 .k-multiselect-wrap,
  .k-edge13 .k-multiselect-wrap {
    background-clip: border-box; }
  .k-multiselect-wrap > .k-readonly {
    opacity: .6; }
  .k-multiselect-wrap li {
    background-clip: padding-box; }
    .k-ie11 .k-multiselect-wrap li,
    .k-edge12 .k-multiselect-wrap li,
    .k-edge13 .k-multiselect-wrap li {
      background-clip: border-box; }
    .k-multiselect-wrap li .k-icon {
      opacity: .6;
      cursor: pointer; }
    .k-multiselect-wrap li .k-icon:hover {
      opacity: 1; }
    .k-multiselect-wrap li:focus .k-icon, .k-multiselect-wrap li.k-state-focused .k-icon {
      opacity: 1; }
  .k-multiselect-wrap .k-i-loading {
    background-color: #ffffff; }

.k-rtl .k-numerictextbox .k-numeric-wrap .k-i-warning, .k-numerictextbox[dir='rtl'] .k-numeric-wrap .k-i-warning {
  align-self: center;
  margin-right: 0;
  margin-left: 0.5em; }

.k-numeric-wrap .k-i-warning {
  align-self: center;
  margin-right: 0.5em; }

.k-numeric-wrap > .k-input {
  flex: 1 1 0; }
  .k-numeric-wrap > .k-input:invalid {
    box-shadow: none; }

.k-numerictextbox .k-numeric-wrap {
  background-clip: padding-box; }
  .k-ie11 .k-numerictextbox .k-numeric-wrap,
  .k-edge12 .k-numerictextbox .k-numeric-wrap,
  .k-edge13 .k-numerictextbox .k-numeric-wrap {
    background-clip: border-box; }

.k-numerictextbox .k-select > .k-state-selected,
.k-numerictextbox .k-select > .k-state-active {
  color: black;
  box-shadow: inset 0 3px 4px 0 rgba(0, 0, 0, 0.06); }

.k-numerictextbox .k-numeric-wrap.k-state-invalid {
  color: #f44336;
  border-color: #f44336; }
  .k-numerictextbox .k-numeric-wrap.k-state-invalid .k-select {
    color: #f44336; }

.k-tooltip-validation {
  margin: 0.5em 0 0;
  padding: 0;
  position: static;
  border-width: 0;
  display: inline-flex;
  align-items: center; }
  .k-tooltip-validation[hidden] {
    display: none; }
  .k-tooltip-validation > .k-icon {
    margin-right: 4px; }
  .k-tooltip-validation .k-callout {
    display: none; }

.k-tooltip-validation {
  border-color: transparent;
  color: #f44336;
  background-color: transparent; }

.k-switch {
  cursor: pointer;
  border: 0;
  display: inline-block;
  font-size: 10px;
  overflow: hidden;
  position: relative;
  text-align: left;
  user-select: none;
  vertical-align: middle;
  width: 6em;
  outline: 0; }
  .k-switch [type='checkbox'] {
    display: none; }

.k-switch,
.k-switch-wrapper,
.k-switch-container,
.k-switch-handle {
  box-sizing: border-box; }

.k-switch-wrapper {
  display: none; }

.k-switch .k-switch-background {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }

.k-switch-container {
  padding: 2px 0;
  display: block;
  width: 100%;
  background: transparent;
  transform: translateZ(0);
  outline: 0; }

.k-switch-handle {
  position: relative;
  width: 2.4em;
  height: 2.4em;
  display: inline-block;
  margin: 0 6px 0 2px;
  border-width: 1px;
  border-style: solid;
  vertical-align: middle; }

.k-switch-label-off {
  left: 3em; }

.k-switch-label-on {
  left: -2.4em; }

.k-switch-label-on,
.k-switch-label-off {
  top: -1px;
  display: block;
  text-align: center;
  position: absolute;
  text-transform: uppercase;
  text-shadow: none;
  line-height: 2.4em;
  vertical-align: middle; }

.k-switch-container,
.k-switch-wrapper {
  border-width: 1px;
  border-style: solid; }

.k-switch[dir='rtl'] .k-switch-label-off {
  left: -2.4em; }

.k-switch[dir='rtl'] .k-switch-label-on {
  left: 3em; }

.k-switch,
.k-switch-container {
  border-radius: 3em;
  outline: 0; }

.k-switch-container {
  border-color: #c2c2c2;
  background-clip: padding-box; }
  .k-ie11 .k-switch-container,
  .k-edge12 .k-switch-container,
  .k-edge13 .k-switch-container {
    background-clip: border-box; }
  .k-switch-container:hover {
    border-color: #607d8b; }

.k-switch-background {
  background-position: 4.3em 0;
  background-repeat: no-repeat; }

.k-switch-handle {
  border-radius: 50%;
  background-clip: padding-box;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06); }
  .k-ie11 .k-switch-handle,
  .k-edge12 .k-switch-handle,
  .k-edge13 .k-switch-handle {
    background-clip: border-box; }

.k-switch-label-on {
  color: #607d8b;
  background-color: transparent; }

.k-switch-label-off {
  color: #000000; }

.k-switch:focus, .k-switch.k-state-focused {
  outline: none;
  box-shadow: inset 0 0 0 3px rgba(0, 0, 0, 0.06); }

.k-switch:active {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.06); }

.k-switch.k-state-disabled {
  cursor: auto; }
  .k-switch.k-state-disabled .k-switch-container {
    border-color: #c2c2c2;
    box-shadow: none; }
  .k-switch.k-state-disabled:focus {
    box-shadow: none; }
  .k-switch.k-state-disabled.k-switch-on .k-switch-handle {
    background: #607d8b;
    border-color: #607d8b;
    opacity: .5; }

.k-switch[aria-readonly="true"] {
  pointer-events: none; }

.k-maskedtextbox .k-i-warning {
  display: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 8px; }

.k-rtl .k-maskedtextbox .k-i-warning, .k-maskedtextbox[dir='rtl'] .k-i-warning {
  right: auto;
  left: 8px; }

.k-maskedtextbox.k-state-invalid .k-i-warning {
  display: inline-block; }

.k-maskedtextbox .k-i-warning {
  color: #f44336; }

.k-maskedtextbox.k-state-invalid .k-textbox {
  color: #f44336;
  border-color: #f44336; }

.k-listbox {
  width: 12.4em;
  height: 200px;
  vertical-align: top;
  background-color: transparent;
  border-width: 0;
  display: inline-flex; }
  .k-listbox .k-listbox-toolbar ul {
    display: flex; }
  .k-listbox.k-listbox-toolbar-left .k-listbox-toolbar ul, .k-listbox.k-listbox-toolbar-right .k-listbox-toolbar ul {
    flex-direction: column; }
  .k-listbox.k-listbox-toolbar-left .k-listbox-toolbar li + li, .k-listbox.k-listbox-toolbar-right .k-listbox-toolbar li + li {
    margin-top: 8px; }
  .k-listbox.k-listbox-toolbar-left {
    flex-direction: row; }
    .k-listbox.k-listbox-toolbar-left .k-listbox-toolbar {
      margin-right: 8px; }
  .k-listbox.k-listbox-toolbar-right {
    flex-direction: row-reverse; }
    .k-listbox.k-listbox-toolbar-right .k-listbox-toolbar {
      margin-left: 8px; }
  .k-listbox.k-listbox-toolbar-top, .k-listbox.k-listbox-toolbar-bottom {
    flex-direction: column; }
    .k-listbox.k-listbox-toolbar-top .k-listbox-toolbar ul, .k-listbox.k-listbox-toolbar-bottom .k-listbox-toolbar ul {
      flex-direction: row; }
    .k-listbox.k-listbox-toolbar-top .k-listbox-toolbar li + li, .k-listbox.k-listbox-toolbar-bottom .k-listbox-toolbar li + li {
      margin-left: 8px; }
  .k-listbox.k-listbox-toolbar-top .k-listbox-toolbar {
    margin-bottom: 8px; }
  .k-listbox.k-listbox-toolbar-bottom .k-listbox-toolbar {
    margin-top: 8px; }
  .k-listbox .k-list-scroller {
    width: 100%;
    border-width: 1px;
    border-style: solid; }
  .k-listbox .k-drop-hint {
    border-top-width: 1px;
    border-top-style: solid; }
  .k-listbox .k-ghost {
    opacity: .5; }

.k-item {
  cursor: default; }

.k-item.k-drag-clue {
  border-radius: 0;
  padding: 4px 8px;
  line-height: 1.42857em;
  border-width: 0;
  font-size: 14px; }

.k-listbox .k-list-scroller {
  background-color: #ffffff;
  border-color: #c2c2c2;
  color: #000000;
  background-clip: padding-box; }
  .k-ie11 .k-listbox .k-list-scroller,
  .k-edge12 .k-listbox .k-list-scroller,
  .k-edge13 .k-listbox .k-list-scroller {
    background-clip: border-box; }

.k-listbox .k-drop-hint {
  border-top-color: #607d8b; }

li.k-item.k-drag-clue {
  background-color: #607d8b;
  color: #ffffff; }

.k-var--chart-font {
  font-size: 14px; }

.k-var--chart-title-font {
  font-size: 1.143em; }

.k-var--chart-label-font {
  font-size: 0.857em; }

.k-chart,
.k-sparkline,
.k-stockchart {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  border-width: 0; }

.k-chart,
.k-stockchart {
  font-size: 14px;
  font-family: "Microsoft YaHei", arial, simsun, sans-serif;
  display: block;
  height: 400px; }

.k-chart-surface {
  height: 100%; }

.k-chart .k-popup {
  border-width: 0; }

.k-chart-tooltip-wrapper .k-animation-container-shown,
.k-chart-tooltip-wrapper.k-animation-container-shown {
  transition: left ease-in 80ms, top ease-in 80ms; }

.k-sparkline-tooltip-wrapper,
.k-chart-tooltip-wrapper {
  z-index: 12000; }
  .k-sparkline-tooltip-wrapper > .k-popup,
  .k-chart-tooltip-wrapper > .k-popup {
    padding: 0;
    border-width: 0; }

.k-chart-tooltip table {
  border-spacing: 0;
  border-collapse: collapse; }

.k-chart-tooltip {
  font-size: 13.006px;
  line-height: 1.42857;
  padding: 4px 8px; }

.k-chart-tooltip th {
  width: auto;
  text-align: center;
  padding: 1px; }

.k-chart-tooltip td {
  width: auto;
  text-align: left;
  padding: 2px 4px;
  line-height: 1.42857;
  vertical-align: middle; }

.k-chart-crosshair-tooltip,
.k-chart-shared-tooltip {
  border-width: 1px;
  border-style: solid; }

.k-chart-shared-tooltip .k-chart-shared-tooltip-marker {
  display: block;
  width: 15px;
  height: 3px;
  vertical-align: middle; }

/* Selection */
.k-selector {
  position: absolute;
  -webkit-transform: translateZ(0); }

.k-selection {
  position: absolute;
  height: 100%;
  border-width: 1px;
  border-style: solid;
  border-bottom: 0; }

.k-selection-bg {
  position: absolute;
  width: 100%;
  height: 100%; }

.k-handle {
  border-radius: 50%;
  width: 22px;
  height: 22px;
  border-width: 1px;
  border-style: solid;
  z-index: 1;
  position: absolute;
  box-sizing: content-box; }

.k-handle div {
  width: 100%;
  height: 100%; }

.k-left-handle {
  left: -11px; }

.k-right-handle {
  right: -11px; }

.k-left-handle div {
  margin: -22px 0 0 -14.66667px;
  padding: 44px 29.33333px 0 0; }

.k-right-handle div {
  margin: -22px 0 0 -14.66667px;
  padding: 44px 0 0 29.33333px; }

.k-left-handle.k-handle-active div {
  margin-left: -44px;
  padding-left: 58.66667px; }

.k-right-handle.k-handle-active div {
  margin-left: -44px;
  padding-right: 58.66667px; }

.k-mask {
  position: absolute;
  height: 100%; }

.k-border {
  width: 1px;
  height: 100%;
  position: absolute; }

.k-marquee {
  position: absolute;
  z-index: 100000; }

.k-marquee-color,
.k-marquee-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

/* Navigator hint */
.k-navigator-hint div {
  position: absolute; }

.k-navigator-hint .k-scroll {
  position: absolute;
  height: 4px; }

.k-navigator-hint .k-tooltip {
  margin-top: 20px;
  min-width: 160px;
  opacity: 1;
  text-align: center; }

/* Sparklines */
.k-sparkline,
.k-sparkline span {
  display: inline-block;
  vertical-align: top; }

.k-sparkline span {
  height: 100%;
  width: 100%; }

.k-chart-dragging {
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none; }

.k-chart-donut-center {
  position: absolute;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  border-radius: 50%;
  text-align: center;
  border: 4px solid transparent;
  box-sizing: border-box; }

.k-pdf-export .k-chart .k-animation-container,
.k-pdf-export .k-sparkline .k-animation-container,
.k-pdf-export .k-stockchart .k-animation-container {
  display: none; }

.k-diagram {
  height: 600px; }

.k-diagram .km-scroll-wrapper {
  width: 100%;
  height: 100%;
  position: relative; }

.k-diagram .km-scroll-wrapper {
  width: 100%;
  height: 100%;
  position: relative; }

.k-canvas-container {
  width: 100%;
  height: 100%; }

/* IE8- */
.k-diagram img {
  box-sizing: content-box; }

.k-treemap {
  height: 400px;
  overflow: hidden; }
  .k-treemap .k-treemap-tile {
    margin: -1px 0 0 -1px;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid;
    border-color: inherit;
    color: inherit;
    background-color: inherit;
    overflow: hidden;
    position: absolute; }
  .k-treemap > .k-treemap-tile {
    position: relative; }
  .k-treemap .k-treemap-title {
    padding: 2px 4px;
    border-width: 0 0 1px;
    border-style: solid;
    background-position: 0 0;
    background-repeat: repeat-x; }
  .k-treemap .k-treemap-title-vertical {
    padding: 4px 2px;
    width: 1.42857em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: absolute;
    top: 0;
    bottom: 0; }
    .k-treemap .k-treemap-title-vertical > div {
      transform-origin: right;
      transform: rotate(-90deg);
      position: absolute;
      top: 0;
      right: 1em; }
  .k-treemap .k-treemap-wrap {
    border-color: inherit;
    color: inherit;
    background-color: inherit;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0; }
  .k-treemap .k-treemap-title + .k-treemap-wrap {
    top: calc( 5.42857px + 1px); }
  .k-treemap .k-treemap-title-vertical + .k-treemap-wrap {
    left: calc( 5.42857px + 1px); }
  .k-treemap .k-leaf {
    padding: 4px; }

.k-arcgauge {
  display: inline-block; }

.k-arcgauge-label {
  position: absolute;
  text-align: center;
  padding: 0;
  margin: 0; }

.k-var--accent {
  background-color: #607d8b; }

.k-var--accent-contrast {
  background-color: #ffffff; }

.k-var--base {
  background-color: #f5f5f5; }

.k-var--background {
  background-color: #ffffff; }

.k-var--border-radius {
  margin-top: 4px; }

.k-var--normal-background {
  background-color: #f5f5f5; }

.k-var--normal-text-color {
  background-color: #000000; }

.k-var--hover-background {
  background-color: #90a4ae; }

.k-var--hover-text-color {
  background-color: #ffffff; }

.k-var--selected-background {
  background-color: #607d8b; }

.k-var--selected-text-color {
  background-color: #ffffff; }

.k-var--success {
  background-color: #009688; }

.k-var--info {
  background-color: #2196f3; }

.k-var--warning {
  background-color: #ffc107; }

.k-var--error {
  background-color: #f44336; }

.k-var--series-a {
  background-color: #607d8b; }

.k-var--series-b {
  background-color: #90a4ae; }

.k-var--series-c {
  background-color: #99aeb8; }

.k-var--series-d {
  background-color: #4b626d; }

.k-var--series-e {
  background-color: #4e839d; }

.k-var--series-f {
  background-color: #727779; }

.k-var--gauge-pointer {
  background-color: #607d8b; }

.k-var--gauge-track {
  background-color: #e6e6e6; }

.k-var--chart-inactive {
  background-color: rgba(0, 0, 0, 0.5); }

.k-var--chart-major-lines {
  background-color: rgba(0, 0, 0, 0.08); }

.k-var--chart-minor-lines {
  background-color: rgba(0, 0, 0, 0.04); }

.k-var--chart-area-opacity {
  opacity: 0.8; }

.k-var--chart-notes-background {
  background-color: rgba(0, 0, 0, 0.5); }

.k-var--chart-notes-border {
  background-color: rgba(0, 0, 0, 0.5); }

.k-var--chart-notes-lines {
  background-color: rgba(0, 0, 0, 0.5); }

.k-var--chart-crosshair-background {
  background-color: rgba(0, 0, 0, 0.5); }

.k-var--chart-error-bars-background {
  background-color: rgba(0, 0, 0, 0.5); }

.k-chart,
.k-sparkline,
.k-stockchart {
  background-color: transparent; }
  .k-chart .k-popup,
  .k-sparkline .k-popup,
  .k-stockchart .k-popup {
    background: transparent; }

.k-chart-tooltip {
  border-radius: 4px;
  color: #ffffff; }

.k-chart-tooltip-inverse {
  color: black; }

.k-chart-crosshair-tooltip,
.k-chart-shared-tooltip {
  color: #000000;
  background-color: #f5f5f5;
  border-color: rgba(0, 0, 0, 0.08); }

.k-selection {
  border-color: rgba(0, 0, 0, 0.08);
  box-shadow: inset 0 1px 7px rgba(0, 0, 0, 0.15); }

.k-selection-bg {
  background-color: transparent; }

.k-handle {
  cursor: e-resize; }

.k-handle div {
  background-color: transparent; }

.k-mask {
  background-color: #ffffff;
  opacity: .8; }

.k-marquee-color {
  background-color: #607d8b; }

.k-marquee-color {
  opacity: .6; }

.k-navigator-hint .k-scroll {
  border-radius: 4px; }

.k-treemap .k-leaf {
  color: #ffffff; }

.k-treemap .k-leaf.k-inverse {
  color: #000000; }

.k-treemap .k-leaf.k-state-hover {
  box-shadow: inset 0 0 0 3px #c2c2c2; }

.k-map {
  height: 600px; }
  .k-map .km-scroll-wrapper {
    width: 100%;
    height: 100%;
    user-select: none;
    position: absolute; }
  .k-map .k-touch-scrollbar {
    display: none; }
  .k-map .k-layer {
    position: absolute;
    left: 0;
    top: 0; }
  .k-map .k-marker {
    margin: -32px 0 0 -16px;
    font-size: 28px;
    cursor: pointer;
    position: absolute; }
  .k-map .k-attribution {
    padding: 2px 4px;
    font-size: 9px;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1000; }
  .k-map .km-scroll-container {
    height: 100%; }

.k-map-controls {
  position: absolute;
  display: flex;
  align-items: center; }

.k-pos-top {
  top: 0; }

.k-pos-bottom {
  bottom: 0; }

.k-pos-left {
  left: 0; }

.k-pos-right {
  right: 0; }

.k-navigator {
  margin: 1em;
  width: 50px;
  height: 50px;
  box-sizing: content-box;
  border-radius: 50%;
  position: relative; }
  .k-pdf-export .k-navigator {
    display: none; }
  .k-navigator .k-button {
    margin: 0;
    padding: 0;
    border-radius: 100%;
    line-height: 1;
    position: absolute; }
    .k-navigator .k-button:not(:hover) {
      border-color: transparent;
      background: none; }
  .k-navigator .k-navigator-up {
    transform: translateX(-50%);
    top: 2px;
    left: 50%; }
  .k-navigator .k-navigator-right {
    transform: translateY(-50%);
    right: 2px;
    top: 50%; }
  .k-navigator .k-navigator-down {
    transform: translateX(-50%);
    bottom: 2px;
    left: 50%; }
  .k-navigator .k-navigator-left {
    transform: translateY(-50%);
    left: 2px;
    top: 50%; }

.k-zoom-control {
  margin: 1em;
  border: 0;
  background: none;
  display: flex; }
  .k-pdf-export .k-zoom-control {
    display: none; }
  .k-zoom-control .k-button {
    padding: 4px; }

.k-buttons-vertical {
  flex-direction: vertical; }

.k-map .k-marker {
  color: #607d8b; }

.k-barcode {
  display: inline-block; }

.k-qrcode {
  display: inline-block; }

.k-splitter {
  height: 300px;
  position: relative; }
  .k-splitter .k-pane {
    overflow: hidden; }
  .k-splitter .k-scrollable {
    overflow: auto; }
  .k-splitter .k-splitter-resizing {
    overflow: hidden; }
  .k-pane > .k-splitter {
    border-width: 0;
    overflow: hidden; }
  .k-splitter .k-pane-loading {
    position: static;
    top: 50%;
    left: 50%; }

.k-ghost-splitbar,
.k-splitbar {
  border-style: solid;
  outline: 0;
  position: absolute;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center; }
  .k-ghost-splitbar .k-icon,
  .k-splitbar .k-icon {
    font-size: 10px;
    display: block;
    cursor: pointer; }

.k-splitbar-draggable-horizontal {
  cursor: col-resize; }

.k-splitbar-draggable-vertical {
  cursor: row-resize; }

.k-ghost-splitbar-horizontal,
.k-splitbar-horizontal {
  width: 8px;
  border-width: 0;
  background-repeat: repeat-y;
  flex-direction: column;
  top: 0; }

.k-ghost-splitbar-vertical,
.k-splitbar-vertical {
  height: 8px;
  border-width: 0;
  background-repeat: repeat-x;
  flex-direction: row;
  left: 0; }

.k-splitbar-static-horizontal {
  width: 1px; }

.k-splitbar-static-vertical {
  height: 1px; }

.k-splitbar-draggable-horizontal .k-resize-handle {
  position: static;
  width: 2px;
  height: 20px; }

.k-splitbar .k-resize-handle {
  display: none;
  background-color: currentColor; }

.k-splitbar-draggable-horizontal .k-resize-handle,
.k-splitbar-draggable-vertical .k-resize-handle {
  display: block; }

.k-splitbar-horizontal .k-i-arrow-60-left, .k-splitbar-horizontal .k-i-arrow-w::before, .k-splitbar-horizontal .k-i-sarrow-w::before, .k-splitbar-horizontal .k-i-expand-w::before {
  margin-bottom: 7px; }

.k-splitbar-horizontal .k-i-arrow-60-right, .k-splitbar-horizontal .k-i-arrow-e::before, .k-splitbar-horizontal .k-i-sarrow-e::before, .k-splitbar-horizontal .k-i-expand::before, .k-splitbar-horizontal .k-i-expand-e::before {
  margin-top: 7px; }

.k-splitbar-vertical .k-i-arrow-60-up, .k-splitbar-vertical .k-i-arrow-n::before, .k-splitbar-vertical .k-i-sarrow-n::before, .k-splitbar-vertical .k-i-expand-n::before {
  margin-right: 7px; }

.k-splitbar-vertical .k-i-arrow-60-down, .k-splitbar-vertical .k-i-arrow-s::before, .k-splitbar-vertical .k-i-sarrow-s::before, .k-splitbar-vertical .k-i-collapse::before, .k-splitbar-vertical .k-i-expand-s::before {
  margin-left: 7px; }

.k-splitbar-draggable-vertical .k-resize-handle {
  position: static;
  width: 20px;
  height: 2px; }

.k-pane > .k-splitter-overlay {
  opacity: 0;
  position: absolute; }

.k-splitter-flex {
  display: flex;
  width: 100%;
  height: auto; }
  .k-splitter-flex .k-pane {
    position: relative;
    flex: 1 1 auto;
    display: block;
    min-width: 0;
    max-width: 100%;
    min-height: 0;
    max-height: 100%;
    height: 100%; }
  .k-splitter-flex .k-pane-static {
    flex-grow: 0;
    flex-shrink: 0; }
  .k-splitter-flex .k-pane-flex {
    display: flex; }
  .k-splitter-flex .k-splitbar {
    position: static;
    flex: 0 0 auto; }
  .k-splitter-flex .k-pane.k-state-hidden, .k-splitter-flex .k-pane[hidden],
  .k-splitter-flex .k-pane-flex.k-state-hidden,
  .k-splitter-flex .k-pane-flex[hidden] {
    flex-basis: 0 !important;
    overflow: hidden !important;
    display: block !important; }
  .k-splitter-flex.k-splitter-horizontal {
    flex-direction: row; }
    .k-splitter-flex.k-splitter-horizontal[dir="rtl"] > .k-splitbar > .k-collapse-next,
    .k-splitter-flex.k-splitter-horizontal[dir="rtl"] > .k-splitbar > .k-collapse-prev {
      transform: scaleX(-1); }
  .k-splitter-flex.k-splitter-vertical {
    flex-direction: column; }

.k-splitter {
  background-clip: padding-box; }
  .k-ie11 .k-splitter,
  .k-edge12 .k-splitter,
  .k-edge13 .k-splitter {
    background-clip: border-box; }

.k-splitbar {
  color: #000000;
  background-color: rgba(235, 235, 235, 0.8); }
  .k-splitbar:active, .k-splitbar.k-state-focused {
    color: #ffffff;
    background: #607d8b; }

.k-splitbar-horizontal-hover,
.k-splitbar-vertical-hover {
  color: #000000;
  background-color: #ebebeb; }

.k-ghost-splitbar {
  background-color: #ebebeb; }

.k-gantt {
  position: relative;
  white-space: nowrap; }
  .k-gantt td {
    overflow: hidden;
    white-space: nowrap;
    vertical-align: top; }
  .k-gantt .k-grid-header tr {
    height: calc( 1.42857em + 17px); }
  .k-gantt .k-grid-content tr {
    height: calc( 1.42857em + 16px); }
  .k-gantt .k-gantt-layout {
    white-space: normal;
    vertical-align: top;
    display: inline-block; }
  .k-gantt .k-splitbar {
    position: relative;
    display: inline-flex; }

.k-gantt-toolbar {
  padding: 8px 8px;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: inherit; }
  .k-gantt-layout + .k-gantt-toolbar {
    border-width: 1px 0 0; }

.k-gantt-actions {
  float: left; }
  .k-gantt-actions .k-button + .k-button {
    margin-left: 8px; }

.k-gantt-views {
  float: right;
  display: inline-flex; }

.k-gantt-toggle {
  margin-right: .5em;
  display: none;
  float: left; }

@media only screen and (max-width: 480px) {
  .k-gantt-toggle {
    display: inline-flex; }
  .k-gantt-pdf,
  .k-gantt-create {
    padding: 4px;
    width: calc( 10px + 1.42857em);
    height: calc( 10px + 1.42857em); }
    .k-gantt-pdf .k-icon,
    .k-gantt-create .k-icon {
      margin: 0; }
    .k-gantt-pdf span + span,
    .k-gantt-create span + span {
      display: none; } }

.k-gantt-views .k-current-view {
  display: none; }

.k-gantt-views .k-current-view .k-link::after {
  content: "";
  margin: 0 0 0 1ex;
  border: .25em solid transparent;
  border-top: .5em solid currentColor;
  border-bottom-width: 0;
  display: inline-block; }

@media (max-width: 1024px) {
  ul.k-gantt-views {
    flex-direction: column;
    align-items: stretch;
    position: absolute;
    right: 8px;
    top: 8px;
    z-index: 1000; }
    ul.k-gantt-views li {
      display: none; }
    ul.k-gantt-views .k-current-view {
      display: block; }
    ul.k-gantt-views.k-state-expanded li {
      display: block; } }

.k-gantt-treelist .k-treelist {
  height: 100%;
  border-width: 0; }

.k-gantt-treelist .k-grid-header {
  padding: 0 !important; }

.k-gantt-treelist .k-grid-header tr {
  height: calc( 2.85714em + 34px);
  vertical-align: bottom; }

.k-gantt-treelist .k-grid-content {
  overflow: hidden;
  overflow-x: scroll; }

.k-gantt-treelist .k-grid-content td {
  vertical-align: middle; }

.k-gantt-timeline .k-timeline {
  height: 100%;
  border-width: 0; }

.k-gantt-timeline .k-grid-content {
  overflow-x: scroll; }

.k-gantt-tables {
  position: relative;
  border-color: inherit; }

.k-gantt-rows,
.k-gantt-columns {
  border-color: inherit;
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0; }

.k-gantt-dependencies {
  opacity: .7;
  position: absolute;
  top: 0;
  left: 0; }

.k-gantt-tasks {
  position: relative; }
  .k-gantt-tasks td {
    padding: 0;
    position: relative;
    vertical-align: middle; }
  .k-gantt-tasks td::after {
    content: "\a0"; }

.k-task-wrap {
  margin: 0 -21px;
  padding: 5px 21px;
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  z-index: 2; }

.k-task-wrap.k-drag-hint {
  position: absolute; }

.k-task {
  position: relative;
  flex: 1 1 auto; }

.k-task-dot {
  width: 16px;
  height: 16px;
  line-height: 1;
  cursor: pointer;
  display: none;
  position: absolute; }

.k-task-wrap:hover .k-task-dot,
.k-task-wrap.k-origin .k-task-dot {
  display: block; }

.k-task-dot::before {
  content: "";
  margin: -4px 0 0 -4px;
  width: 8px;
  height: 8px;
  border-width: 0;
  border-style: solid;
  border-radius: 100%;
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%; }

.k-task-dot:hover::before,
.k-task-dot.k-state-hover::before {
  border-width: 1px; }

.k-task-start {
  left: 0; }

.k-task-end {
  right: 0; }

.k-task-draghandle {
  margin-left: 16px;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-top-width: 0;
  border-bottom-color: inherit;
  position: absolute;
  bottom: 0;
  cursor: e-resize;
  visibility: hidden; }

.k-task-wrap:hover .k-task-draghandle {
  visibility: visible; }

.k-milestone-wrap {
  margin: 0 -2em; }

.k-task-milestone {
  width: 1em;
  height: 1em;
  border-width: 1px;
  border-style: solid;
  transform: rotate(45deg); }

.k-task-summary {
  height: 10px;
  display: inline-block;
  vertical-align: top; }

.k-task-summary,
.k-task-summary-complete {
  background-color: currentColor; }
  .k-task-summary::before, .k-task-summary::after,
  .k-task-summary-complete::before,
  .k-task-summary-complete::after {
    content: "";
    width: 0;
    height: 0;
    border: 8px solid transparent;
    position: absolute;
    top: 0; }
  .k-task-summary::before,
  .k-task-summary-complete::before {
    border-left-color: currentColor;
    left: 0; }
  .k-task-summary::after,
  .k-task-summary-complete::after {
    border-right-color: currentColor;
    right: 0; }

.k-task-summary-complete {
  height: 10px;
  position: relative;
  z-index: 2; }

.k-task-summary-progress {
  height: 15px;
  overflow: hidden; }

.k-task-single {
  border-radius: 4px;
  border-width: 1px;
  border-style: solid;
  cursor: default; }
  .k-task-single .k-resize-handle {
    opacity: .5;
    z-index: 2;
    visibility: hidden; }
  .k-task-single .k-resize-handle::before {
    position: absolute;
    top: 2px;
    bottom: 2px; }
  .k-task-single .k-resize-w {
    left: 0; }
  .k-task-single .k-resize-w::before {
    left: 2px;
    border-left-width: 1px; }
  .k-task-single .k-resize-e {
    right: 0; }
  .k-task-single .k-resize-e::before {
    right: 2px;
    border-left-width: 1px; }
  .k-task-single:hover .k-resize-handle,
  .k-task-single:hover .k-task-actions {
    visibility: visible; }

.k-task-complete {
  border-radius: 4px;
  width: 20%;
  position: absolute;
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0; }

.k-task-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center; }

.k-task-template {
  padding: 2px 4px;
  line-height: normal;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis; }

.k-task-actions {
  padding: 2px;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  align-items: center;
  visibility: hidden; }

.k-task-actions > .k-link {
  display: inline-flex; }

.k-resources-wrap {
  position: absolute;
  display: inline-block;
  z-index: 2;
  margin-left: 20px;
  margin-top: -2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }
  .k-resources-wrap .k-resource {
    margin: 0 5px; }

.k-task-details {
  padding: 4px 8px; }
  .k-task-details strong {
    font-size: 16px;
    font-weight: normal;
    display: block; }
  .k-task-details .k-task-pct {
    font-size: 32px; }
  .k-task-details ul {
    line-height: normal; }

.k-gantt-edit-form .k-gantt-delete {
  float: left; }

.k-rtl .k-gantt-views {
  float: left; }

.k-rtl .k-gantt-actions {
  float: right; }
  .k-rtl .k-gantt-actions .k-button + .k-button {
    margin-left: 0;
    margin-right: 8px; }

.k-rtl .k-gantt-rows,
.k-rtl .k-gantt-columns {
  left: auto;
  right: 0; }

.k-rtl .k-task-wrap:not(.k-milestone-wrap) {
  margin: 0 -26px; }

.k-rtl .k-timeline .k-gantt-tasks tbody {
  text-align: left; }

.k-rtl .k-task-content {
  text-align: right; }

.k-rtl .k-task-complete {
  left: auto;
  right: 0; }

.k-rtl .k-task-draghandle {
  margin-left: 0;
  margin-right: 16px; }

.k-rtl .k-gantt-dependencies {
  left: auto;
  right: 0; }

.k-rtl .k-grid-header .k-header {
  position: static; }

.k-rtl .k-gantt-delete {
  float: right; }

.k-gantt {
  background-clip: padding-box; }
  .k-ie11 .k-gantt,
  .k-edge12 .k-gantt,
  .k-edge13 .k-gantt {
    background-clip: border-box; }

.k-gantt-views li.k-state-selected {
  border-color: #607d8b;
  color: #ffffff;
  background-color: #607d8b; }

.k-gantt-views li:first-child {
  border-radius: 4px; }

.k-gantt-views li:first-child + li {
  border-radius: 4px 0 0 4px; }

.k-gantt-views li:last-child {
  border-radius: 0 4px 4px 0; }

.k-gantt-views.k-state-expanded li {
  border-radius: 0;
  margin: 0; }

.k-gantt-views.k-state-expanded li + li {
  margin-top: -1px; }

.k-gantt-views.k-state-expanded li:first-child {
  border-radius: 4px 4px 0 0; }

.k-gantt-views.k-state-expanded li:first-child + li {
  border-radius: 0; }

.k-gantt-views.k-state-expanded li:last-child {
  border-radius: 0 0 4px 4px; }

.k-gantt-treelist {
  background-color: #fafafa; }
  .k-gantt-treelist .k-treelist {
    background-color: transparent; }
  .k-gantt-treelist tr.k-alt {
    background-color: #f0f0f0; }
  .k-gantt-treelist tr.k-state-selected,
  .k-gantt-treelist td.k-state-selected {
    color: inherit;
    background-color: rgba(96, 125, 139, 0.25); }

.k-gantt-columns .k-nonwork-hour {
  background-color: rgba(0, 0, 0, 0.025); }

.k-line {
  color: black; }

.k-line.k-state-selected {
  color: #607d8b; }

.k-task-dot::before {
  background-color: #000000; }

.k-task-dot:hover::before,
.k-task-dot.k-state-hover::before {
  border-color: #000000;
  background-color: #ffffff; }

.k-task-milestone {
  border-color: #c2c2c2;
  background-color: #000000; }

.k-task-milestone.k-state-selected {
  border-color: #607d8b;
  background-color: #607d8b; }

.k-task-summary {
  color: #0d0d0d; }

.k-task-summary-complete {
  color: black; }

.k-task-summary.k-state-selected {
  color: #8097a2; }
  .k-task-summary.k-state-selected .k-task-summary-complete {
    color: #587380; }

.k-task-summary::before,
.k-task-summary::after,
.k-task-summary-complete::before,
.k-task-summary-complete::after {
  display: none; }

.k-task-single {
  border-width: 0;
  border-color: #c2c2c2;
  color: #ffffff;
  background: #333333; }
  .k-task-single .k-task-complete {
    background: #000000; }
  .k-task-single .k-task-template {
    padding-top: 4px;
    padding-bottom: 4px; }

.k-task-single.k-state-selected {
  border-color: #607d8b;
  color: #ffffff;
  background: #90a4ae; }
  .k-task-single.k-state-selected .k-task-complete {
    background: #607d8b; }

.k-gantt-views li:first-child + li {
  border-radius: 0 4px 4px 0; }

.k-gantt-views li:last-child {
  border-radius: 4px 0 0 4px; }

.k-scheduler {
  display: flex;
  flex-direction: column; }
  .k-scheduler table,
  .k-scheduler thead,
  .k-scheduler tfoot,
  .k-scheduler tbody,
  .k-scheduler tr,
  .k-scheduler th,
  .k-scheduler td {
    border-color: inherit; }

.k-scheduler-table {
  width: 100%;
  max-width: none;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed; }
  .k-scheduler-table td,
  .k-scheduler-table th {
    padding: 8px;
    height: 1.42857em;
    border-style: solid;
    border-width: 0 0 1px 1px;
    vertical-align: top; }
  .k-scheduler-table td:first-child,
  .k-scheduler-table th:first-child {
    border-left-width: 0; }
  .k-scheduler-table .k-middle-row td {
    border-bottom-style: dotted; }

.k-gantt-views, .k-scheduler-navigation, .k-scheduler-views, .k-scheduler-footer {
  display: flex;
  flex-direction: row;
  align-items: center; }
  .k-gantt-views li, .k-scheduler-navigation li, .k-scheduler-views li, .k-scheduler-footer li {
    border-width: 1px;
    border-style: solid;
    position: relative;
    z-index: 1; }
  .k-gantt-views li + li, .k-scheduler-navigation li + li, .k-scheduler-views li + li, .k-scheduler-footer li + li {
    margin-left: -1px; }
  .k-gantt-views .k-state-hover, .k-scheduler-navigation .k-state-hover, .k-scheduler-views .k-state-hover, .k-scheduler-footer .k-state-hover, .k-gantt-views .k-state-selected, .k-scheduler-navigation .k-state-selected, .k-scheduler-views .k-state-selected, .k-scheduler-footer .k-state-selected {
    z-index: 2; }
  .k-gantt-views .k-link, .k-scheduler-navigation .k-link, .k-scheduler-views .k-link, .k-scheduler-footer .k-link {
    padding: 4px 8px;
    box-sizing: border-box;
    color: inherit;
    text-decoration: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: center;
    justify-content: center; }

.k-scheduler-toolbar,
.k-scheduler-footer {
  padding: 8px 8px;
  border-width: 0;
  border-style: solid;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  position: relative;
  white-space: nowrap; }
  .k-scheduler-toolbar li .k-link,
  .k-scheduler-footer li .k-link {
    height: calc( 8px + 1.42857em);
    box-sizing: border-box; }

.k-scheduler-toolbar {
  border-bottom-width: 1px; }

.k-scheduler-footer {
  border-top-width: 1px; }

.k-scheduler-navigation {
  flex: 1; }
  .k-scheduler-navigation .k-nav-prev .k-link,
  .k-scheduler-navigation .k-nav-next .k-link {
    padding: 4px;
    width: calc( 8px + 1.42857em);
    height: calc( 8px + 1.42857em); }
  .k-scheduler-navigation .k-nav-current {
    border: 0;
    margin-left: 0; }
  .k-scheduler-navigation .k-nav-today {
    border-radius: 4px 0 0 4px; }
  .k-scheduler-navigation .k-nav-next {
    border-radius: 0 4px 4px 0; }
  .k-rtl .k-scheduler-navigation .k-i-arrow-60-left, .k-rtl .k-scheduler-navigation .k-i-arrow-w::before, .k-rtl .k-scheduler-navigation .k-i-sarrow-w::before, .k-rtl .k-scheduler-navigation .k-i-expand-w::before,
  .k-rtl .k-scheduler-navigation .k-i-arrow-60-right,
  .k-rtl .k-scheduler-navigation .k-i-arrow-e::before,
  .k-rtl .k-scheduler-navigation .k-i-sarrow-e::before,
  .k-rtl .k-scheduler-navigation .k-i-expand::before,
  .k-rtl .k-scheduler-navigation .k-i-expand-e::before {
    transform: scaleX(-1); }

.k-scheduler-tools {
  margin-right: 1em; }

.k-nav-current .k-icon {
  margin-right: .4ex; }

.k-nav-current .k-sm-date-format,
.k-nav-current .k-lg-date-format {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }

.k-nav-current .k-sm-date-format {
  display: none; }

@media (max-width: 1024px) {
  .k-nav-current .k-sm-date-format {
    display: block; }
  .k-nav-current .k-lg-date-format {
    display: none; } }

.k-scheduler-fullday .k-icon {
  margin-right: 4px; }

.k-scheduler-views li:first-child {
  border-radius: 4px; }

.k-scheduler-views li:first-child + li {
  border-radius: 4px 0 0 4px; }

.k-scheduler-views li:last-child {
  border-radius: 0 4px 4px 0; }

.k-scheduler-views.k-state-expanded li {
  border-radius: 0;
  margin: 0; }

.k-scheduler-views.k-state-expanded li + li {
  margin-top: -1px; }

.k-scheduler-views.k-state-expanded li:first-child {
  border-radius: 4px 4px 0 0; }

.k-scheduler-views.k-state-expanded li:first-child + li {
  border-radius: 0; }

.k-scheduler-views.k-state-expanded li:last-child {
  border-radius: 0 0 4px 4px; }

.k-scheduler-views .k-current-view {
  display: none; }

.k-scheduler-views .k-current-view .k-link::after {
  content: "";
  margin: 0 0 0 1ex;
  border: .25em solid transparent;
  border-top: .5em solid currentColor;
  border-bottom-width: 0;
  display: inline-block; }

@media (max-width: 1024px) {
  .k-scheduler-views {
    flex-direction: column;
    align-items: stretch;
    position: absolute;
    right: 8px;
    top: 8px;
    z-index: 1000; }
    .k-scheduler-views li {
      display: none; }
    .k-scheduler-views .k-current-view {
      display: block; }
    .k-scheduler-views.k-state-expanded li {
      display: block; } }

.k-scheduler-footer li {
  border-radius: 4px; }

.k-scheduler-layout {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  flex: 1 1 auto; }
  .k-scheduler-layout > tbody > tr > td {
    padding: 0;
    vertical-align: top; }

.k-scheduler-header,
.k-scheduler-header-wrap,
.k-scheduler-header-wrap > div {
  border-color: inherit;
  overflow: hidden; }

.k-scheduler-header-wrap {
  border-width: 0 1px 0 0;
  border-style: solid; }

.k-scheduler-times {
  border-color: inherit;
  position: relative;
  overflow: hidden; }
  .k-scheduler-times .k-scheduler-table {
    table-layout: auto; }
  .k-scheduler-times th {
    border-width: 0 1px 1px 0;
    text-align: right;
    white-space: nowrap; }
  .k-scheduler-times tr + tr th {
    border-bottom-color: transparent; }
  .k-scheduler-times .k-slot-cell,
  .k-scheduler-times .k-scheduler-times-all-day {
    border-bottom-color: inherit; }

.k-scheduler-datecolumn {
  width: 12em; }

.k-scheduler-timecolumn {
  width: 11em;
  white-space: nowrap; }

.k-scheduler-content {
  border-color: inherit;
  position: relative;
  overflow: auto; }

.k-event {
  border-radius: 4px;
  min-height: 1.42857em;
  border-width: 1px;
  border-style: solid;
  text-align: left;
  cursor: default;
  position: absolute;
  overflow: hidden; }
  .k-event .k-event-template {
    padding: 2px 4px; }
  .k-event .k-event-time {
    padding-bottom: 0;
    font-size: .875em;
    white-space: nowrap;
    display: none; }
  .k-event .k-event-actions {
    white-space: nowrap;
    position: absolute;
    top: 2px;
    right: 2px;
    opacity: .5;
    visibility: hidden; }
    .k-event .k-event-actions a {
      color: inherit; }
  .k-event .k-event-actions:first-child {
    margin: 2px 0.4ex 0 4px;
    float: left;
    position: static;
    opacity: 1;
    visibility: visible; }
  .k-event .k-resize-handle {
    z-index: 4;
    opacity: .5;
    visibility: hidden; }
  .k-event .k-resize-handle::before {
    border-color: currentColor; }
  .k-event .k-resize-n {
    height: .5em;
    top: 0; }
  .k-event .k-resize-s {
    height: .5em;
    bottom: 0; }
  .k-event .k-resize-n::before,
  .k-event .k-resize-s::before {
    width: 2em;
    border-bottom-width: 1px; }
  .k-event .k-resize-w {
    width: .5em;
    left: 0; }
  .k-event .k-resize-e {
    width: .5em;
    right: 0; }
  .k-event .k-resize-w::before,
  .k-event .k-resize-e::before {
    height: 2em;
    border-left-width: 1px; }
  .k-event:hover .k-event-actions,
  .k-event:hover .k-resize-handle {
    visibility: visible; }

.k-scheduler-mark {
  width: 1em;
  height: 1em;
  display: inline-block; }

.k-more-events {
  padding: 0;
  border-style: solid;
  border-width: 1px;
  font-size: 1.5em;
  line-height: 1;
  text-align: center;
  overflow: hidden;
  position: absolute;
  justify-content: center; }
  .k-more-events > span {
    margin-top: -.5em; }

.k-current-time {
  background: red;
  position: absolute; }

.k-current-time-arrow-down {
  width: 0;
  height: 0;
  background: transparent;
  border-bottom: 4px solid  transparent;
  border-top: 4px solid #f00;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent; }

.k-current-time-arrow-left {
  width: 0;
  height: 0;
  background: transparent;
  border-bottom: 4px solid  transparent;
  border-top: 4px solid transparent;
  border-left: 4px solid transparent;
  border-right: 4px solid #f00; }

.k-current-time-arrow-right {
  width: 0;
  height: 0;
  background: transparent;
  border-bottom: 4px solid  transparent;
  border-top: 4px solid transparent;
  border-left: 4px solid #f00;
  border-right: 4px solid transparent; }

.k-event-drag-hint {
  opacity: .5; }
  .k-event-drag-hint .k-event-actions,
  .k-event-drag-hint .k-event-top-actions,
  .k-event-drag-hint .k-event-bottom-actions,
  .k-event-drag-hint .k-resize-handle {
    display: none; }
  .k-event-drag-hint .k-event-time {
    display: block; }

.k-scheduler-marquee {
  border-width: 0;
  border-style: solid; }
  .k-scheduler-marquee .k-label-top,
  .k-scheduler-marquee .k-label-bottom {
    font-size: .75em;
    position: absolute; }
  .k-scheduler-marquee .k-label-top {
    left: 4px;
    top: 2px; }
  .k-scheduler-marquee .k-label-bottom {
    right: 4px;
    bottom: 2px; }
  .k-scheduler-marquee.k-first::before, .k-scheduler-marquee.k-last::after {
    content: "";
    border-width: 3px;
    border-style: solid;
    position: absolute;
    width: 0;
    height: 0; }
  .k-scheduler-marquee.k-first::before {
    top: 0;
    left: 0;
    border-right-color: transparent;
    border-bottom-color: transparent; }
  .k-scheduler-marquee.k-last::after {
    bottom: 0;
    right: 0;
    border-left-color: transparent;
    border-top-color: transparent; }

.k-pdf-export-shadow .k-scheduler,
.k-scheduler-pdf-export .k-scheduler-content,
.k-scheduler-pdf-export .k-scheduler-times {
  height: auto !important;
  overflow: visible !important; }

.k-scheduler-pdf-export {
  overflow: hidden; }
  .k-scheduler-pdf-export .k-scheduler-header {
    padding: 0 !important; }
  .k-scheduler-pdf-export .k-scheduler-header-wrap {
    border-width: 0 !important; }
  .k-scheduler-pdf-export .k-scheduler-header .k-scheduler-table,
  .k-scheduler-pdf-export .k-scheduler-content .k-scheduler-table {
    width: 100% !important; }

.k-scheduler-monthview .k-scheduler-table {
  height: 100%; }

.k-scheduler-monthview .k-scheduler-table td {
  height: 80px;
  text-align: right; }

.k-scheduler-monthview .k-hidden {
  padding-left: 0 !important;
  padding-right: 0 !important;
  border-right-width: 0 !important; }

.k-scheduler-agendaview .k-scheduler-mark {
  margin-right: .5em;
  width: 1em;
  height: 1em;
  display: inline-block;
  vertical-align: middle; }

.k-scheduler-agendaview .k-scheduler-table td:first-child {
  border-left-width: 1px; }

.k-scheduler-agendaview .k-scheduler-table td.k-first {
  border-left-width: 0; }

.k-scheduler-agendaview .k-task > .k-event-delete {
  color: inherit;
  position: absolute;
  top: 2px;
  right: 2px;
  opacity: .5;
  visibility: hidden; }

.k-scheduler-agendaview .k-state-hover .k-task > .k-event-delete {
  visibility: visible; }

.k-scheduler-agendaday {
  margin: 0 .2em 0 0;
  font-size: 3em;
  line-height: 1;
  font-weight: 400;
  float: left; }

.k-scheduler-agendaweek {
  display: block;
  margin: .4em 0 0;
  line-height: 1;
  font-style: normal; }

.k-scheduler-agendadate {
  font-size: .75em; }

.k-scheduler-edit-form .k-edit-form-container {
  width: 620px; }

.k-scheduler-edit-form .k-edit-label {
  width: 17%; }

.k-scheduler-edit-form .k-edit-field {
  width: 77%; }

.k-scheduler-edit-form .k-scheduler-delete {
  float: left; }

.k-scheduler-edit-form .k-widget.k-recur-interval,
.k-scheduler-edit-form .k-widget.k-recur-count {
  width: 5em; }

.k-scheduler-edit-form .k-widget.k-recur-until {
  width: 9em; }

.k-rtl .k-scheduler-header th,
.k-rtl .k-scheduler-table td {
  border-left-width: 0;
  border-right-width: 1px; }

.k-rtl .k-scheduler .k-scrollbar-v .k-scheduler-header-wrap {
  border-right-width: 0;
  border-left-width: 1px; }

.k-rtl .k-event {
  text-align: right; }
  .k-rtl .k-event .k-resize-w {
    left: auto;
    right: 0; }
  .k-rtl .k-event .k-resize-e {
    right: auto;
    left: 0; }
  .k-rtl .k-event .k-event-actions {
    right: auto;
    left: 2px; }
  .k-rtl .k-event .k-event-actions:first-child {
    float: right; }

.k-rtl .k-scheduler-agendaview .k-task > .k-event-delete {
  left: 2px;
  right: auto; }

.k-rtl .k-scheduler-views li:first-child + li {
  border-radius: 0 4px 4px 0; }

.k-rtl .k-scheduler-views li:last-child {
  border-radius: 4px 0 0 4px; }

.k-rtl .k-scheduler-navigation .k-nav-today {
  border-radius: 0 4px 4px 0; }

.k-rtl .k-scheduler-navigation .k-nav-next {
  border-radius: 4px 0 0 4px; }

.k-rtl .k-scheduler-navigation li + li {
  margin-left: 0;
  margin-right: -1px; }

.k-rtl .k-nav-current .k-icon {
  margin-right: 0;
  margin-left: .4ex; }

.k-rtl .k-scheduler-fullday .k-icon {
  margin-right: 0;
  margin-left: 4px; }

.k-rtl .k-scheduler-marquee .k-label-top {
  left: auto;
  right: 4px; }

.k-rtl .k-scheduler-marquee .k-label-bottom {
  left: 4px;
  right: auto; }

.k-rtl .k-scheduler-edit-form .k-scheduler-delete {
  float: right; }

.k-scheduler {
  background-clip: padding-box; }
  .k-ie11 .k-scheduler,
  .k-edge12 .k-scheduler,
  .k-edge13 .k-scheduler {
    background-clip: border-box; }

.k-scheduler-toolbar,
.k-scheduler-footer {
  background-clip: padding-box; }
  .k-ie11 .k-scheduler-toolbar,
  .k-edge12 .k-scheduler-toolbar,
  .k-edge13 .k-scheduler-toolbar, .k-ie11
  .k-scheduler-footer,
  .k-edge12
  .k-scheduler-footer,
  .k-edge13
  .k-scheduler-footer {
    background-clip: border-box; }

.k-scheduler-navigation li {
  background-clip: padding-box; }
  .k-ie11 .k-scheduler-navigation li,
  .k-edge12 .k-scheduler-navigation li,
  .k-edge13 .k-scheduler-navigation li {
    background-clip: border-box; }

.k-scheduler-views li {
  background-clip: padding-box; }
  .k-ie11 .k-scheduler-views li,
  .k-edge12 .k-scheduler-views li,
  .k-edge13 .k-scheduler-views li {
    background-clip: border-box; }
  .k-scheduler-views li.k-state-selected {
    border-color: #607d8b;
    color: #ffffff;
    background-color: #607d8b; }

.k-scheduler-footer li {
  background-clip: padding-box; }
  .k-ie11 .k-scheduler-footer li,
  .k-edge12 .k-scheduler-footer li,
  .k-edge13 .k-scheduler-footer li {
    background-clip: border-box; }

.k-nonwork-hour {
  background-color: #f7f7f7; }

.k-event-inverse {
  color: #ffffff; }

.k-scheduler-marquee::before,
.k-scheduler-marquee::after {
  border-color: #607d8b; }

.k-mediaplayer {
  position: relative; }
  .k-mediaplayer > iframe {
    width: 100%;
    height: 100%;
    border: 0;
    vertical-align: top; }

.k-mediaplayer-titlebar {
  padding: 8px 8px;
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  right: 0; }

.k-mediaplayer-toolbar-wrap {
  position: absolute;
  z-index: 2;
  bottom: 0;
  left: 0;
  right: 0; }

.k-mediaplayer-toolbar {
  padding: 8px 8px;
  border-width: 0;
  width: 100% !important; }
  .k-mediaplayer-toolbar > * {
    align-items: center;
    align-content: center; }
  .k-mediaplayer-toolbar .k-toolbar-spacer {
    margin-top: 0;
    margin-bottom: 0;
    border-width: 0;
    flex: 1; }
  .k-mediaplayer-toolbar .k-align-right > * + * {
    margin-left: 8px; }

.k-mediaplayer-time-wrap {
  flex: 1; }

.k-mediaplayer-volume-wrap {
  align-items: center; }

.k-slider.k-mediaplayer-seekbar {
  width: 100%;
  position: absolute;
  z-index: 3;
  top: -17px;
  left: 0; }

.k-mediaplayer-seekbar .k-slider-track {
  width: 100% !important;
  border-radius: 0; }

.k-mediaplayer-fullscreen {
  z-index: 10000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important; }

.k-mediaplayer {
  background-clip: padding-box; }
  .k-ie11 .k-mediaplayer,
  .k-edge12 .k-mediaplayer,
  .k-edge13 .k-mediaplayer {
    background-clip: border-box; }

.k-mediaplayer-titlebar {
  color: #ffffff;
  background-image: linear-gradient(rgba(0, 0, 0, 0.7), transparent); }

.k-mediaplayer-toolbar {
  background-color: rgba(245, 245, 245, 0.85); }

.k-notification {
  padding: 0;
  border-radius: 4px; }

.k-notification-wrap {
  font-size: 12px;
  padding: 1em 2em 1em 1em;
  white-space: nowrap;
  cursor: default;
  position: relative;
  line-height: 1.42857em; }
  .k-notification-wrap > .k-icon {
    margin-right: 8px;
    vertical-align: middle; }
  .k-notification-wrap > .k-i-close, .k-notification-wrap > .k-i-group-delete::before {
    margin: 0;
    position: absolute;
    top: 1em;
    right: 0.5em;
    font-size: inherit;
    line-height: inherit; }
    .k-notification-wrap > .k-i-close::before, .k-notification-wrap > .k-i-group-delete::before {
      font-size: 16px; }

.k-rtl .k-notification-wrap {
  padding: 1em 1em 1em 2em; }
  .k-rtl .k-notification-wrap > .k-icon {
    margin-right: 0;
    margin-left: 8px; }
  .k-rtl .k-notification-wrap > .k-i-close, .k-rtl .k-notification-wrap > .k-i-group-delete::before {
    margin: 0;
    right: auto;
    left: 4px; }

.k-notification-info {
  border-color: #bbdefb;
  color: #0d47a1;
  background-color: #e3f2fd; }

.k-notification-success {
  border-color: #b2dfdb;
  color: #004d40;
  background-color: #e0f2f1; }

.k-notification-warning {
  border-color: #ffecb3;
  color: #ff6f00;
  background-color: #fff8e1; }

.k-notification-error {
  border-color: #ffcdd2;
  color: #b71c1c;
  background-color: #ffebee; }

.k-rpanel-left,
.k-rpanel-right {
  position: fixed;
  display: block;
  overflow: auto;
  min-width: 320px;
  height: 100%;
  top: 0; }
  .k-rpanel-left + *,
  .k-rpanel-right + * {
    overflow: auto; }
  .k-rpanel-left.k-rpanel-expanded,
  .k-rpanel-right.k-rpanel-expanded {
    transform: translateX(0) translateZ(0); }

.k-rpanel-left {
  transform: translateX(-100%) translateZ(0);
  left: 0; }

.k-rpanel-right {
  transform: translateX(100%) translateZ(0);
  right: 0; }

.k-rpanel-top {
  position: static;
  max-height: 0; }

.k-rpanel-top.k-rpanel-expanded {
  max-height: 568px;
  overflow: visible !important; }

.k-menu {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  position: relative;
  cursor: default; }
  .k-menu .k-item {
    border-width: 0;
    border-style: solid;
    border-color: inherit;
    display: flex;
    align-items: stretch;
    position: relative;
    user-select: none;
    flex-shrink: 0;
    outline: 0; }
  .k-menu .k-item > .k-link {
    cursor: pointer;
    padding: 6px 12px;
    line-height: 1.42857;
    color: inherit;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    white-space: nowrap; }

.k-menu-link > .k-icon,
.k-menu-link > .k-image,
.k-menu-link > .k-sprite {
  margin-right: 8px; }

.k-menu-expand-arrow.k-i-arrow-60-down, .k-menu-expand-arrow.k-i-arrow-s::before, .k-menu-expand-arrow.k-i-sarrow-s::before, .k-menu-expand-arrow.k-i-collapse::before, .k-menu-expand-arrow.k-i-expand-s::before {
  margin-left: 4px;
  margin-right: -4px; }

.k-menu-expand-arrow.k-i-arrow-60-left, .k-menu-expand-arrow.k-i-arrow-w::before, .k-menu-expand-arrow.k-i-sarrow-w::before, .k-menu-expand-arrow.k-i-expand-w::before, .k-menu-expand-arrow.k-i-arrow-60-right, .k-menu-expand-arrow.k-i-arrow-e::before, .k-menu-expand-arrow.k-i-sarrow-e::before, .k-menu-expand-arrow.k-i-expand::before, .k-menu-expand-arrow.k-i-expand-e::before {
  margin: -8px 0 0;
  position: absolute;
  top: 50%; }

.k-menu-expand-arrow.k-i-arrow-60-right, .k-menu-expand-arrow.k-i-arrow-e::before, .k-menu-expand-arrow.k-i-sarrow-e::before, .k-menu-expand-arrow.k-i-expand::before, .k-menu-expand-arrow.k-i-expand-e::before {
  right: 4px; }

.k-menu-expand-arrow.k-i-arrow-60-left, .k-menu-expand-arrow.k-i-arrow-w::before, .k-menu-expand-arrow.k-i-sarrow-w::before, .k-menu-expand-arrow.k-i-expand-w::before {
  left: 4px; }

.k-menu-group {
  margin: 0;
  padding: 0 0;
  white-space: nowrap;
  list-style: none;
  display: none;
  position: absolute; }
  .k-menu-popup .k-menu-group {
    position: relative;
    display: block; }

.k-menu-horizontal > .k-item + .k-item {
  margin-left: 0; }

.k-menu-horizontal > .k-separator + .k-item {
  margin-left: 0; }

.k-menu-horizontal > .k-item.k-separator {
  margin: 0 2px; }

.k-menu-vertical > .k-item + .k-item {
  margin-top: 0; }

.k-menu-group,
.k-menu-vertical {
  flex-direction: column; }
  .k-menu-group .k-item,
  .k-menu-vertical .k-item {
    display: block;
    border-color: inherit; }
  .k-menu-group .k-item > .k-link,
  .k-menu-vertical .k-item > .k-link {
    line-height: 1.42857;
    padding: 4px 8px;
    padding-right: 32px;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative; }
  .k-menu-group .k-item.k-separator,
  .k-menu-vertical .k-item.k-separator {
    margin: 2px 0; }

.k-popups-wrapper {
  position: relative;
  border: 0;
  margin: 0;
  padding: 0; }

.k-context-menu {
  margin: 0;
  border-width: 1px;
  border-style: solid;
  box-sizing: content-box; }
  .k-context-menu.k-menu-horizontal {
    padding: 0 0; }
  .k-context-menu > .k-item + .k-item:not(.k-separator) {
    margin-left: 0; }

.k-animation-container .k-context-menu.k-menu-horizontal {
  display: flex !important; }

.k-menu-scroll-wrapper {
  margin: 0;
  padding: 0;
  border: 0;
  position: relative; }
  .k-menu-scroll-wrapper .k-menu {
    overflow: hidden; }

.k-menu-scroll-button {
  border-radius: 0;
  padding: 0;
  border-width: 0;
  border-color: inherit;
  color: inherit;
  background: inherit;
  background-clip: border-box;
  position: absolute; }
  .k-menu-scroll-button.k-scroll-left {
    top: 0;
    left: 0;
    height: 100%;
    width: 16px;
    border-right-width: 1px; }
  .k-menu-scroll-button.k-scroll-right {
    top: 0;
    right: 0;
    height: 100%;
    width: 16px;
    border-left-width: 1px; }
  .k-menu-scroll-button.k-scroll-up {
    top: 0;
    left: 0;
    width: 100%;
    height: 16px;
    border-bottom-width: 1px; }
  .k-menu-scroll-button.k-scroll-down {
    bottom: 0;
    left: 0;
    width: 100%;
    height: 16px;
    border-top-width: 1px; }

.k-rtl .k-menu-link > .k-icon,
.k-rtl .k-menu-link > .k-image,
.k-rtl .k-menu-link > .k-sprite,
[dir="rtl"] .k-menu-link > .k-icon,
[dir="rtl"] .k-menu-link > .k-image,
[dir="rtl"] .k-menu-link > .k-sprite {
  margin-left: 8px;
  margin-right: 0; }

.k-rtl .k-menu-link .k-icon.k-menu-expand-arrow,
[dir="rtl"] .k-menu-link .k-icon.k-menu-expand-arrow {
  margin-left: 0; }

.k-rtl .k-menu-expand-arrow.k-i-arrow-60-down, .k-rtl .k-menu-expand-arrow.k-i-arrow-s::before, .k-rtl .k-menu-expand-arrow.k-i-sarrow-s::before, .k-rtl .k-menu-expand-arrow.k-i-collapse::before, .k-rtl .k-menu-expand-arrow.k-i-expand-s::before,
[dir="rtl"] .k-menu-expand-arrow.k-i-arrow-60-down,
[dir="rtl"] .k-menu-expand-arrow.k-i-arrow-s::before,
[dir="rtl"] .k-menu-expand-arrow.k-i-sarrow-s::before,
[dir="rtl"] .k-menu-expand-arrow.k-i-collapse::before,
[dir="rtl"] .k-menu-expand-arrow.k-i-expand-s::before {
  margin-left: -4px;
  margin-right: 4px; }

.k-rtl .k-menu-horizontal > .k-item:first-of-type,
[dir="rtl"] .k-menu-horizontal > .k-item:first-of-type {
  margin-left: 0;
  margin-right: 0; }

.k-rtl .k-menu-group .k-link,
.k-rtl .k-menu-vertical .k-link,
[dir="rtl"] .k-menu-group .k-link,
[dir="rtl"] .k-menu-vertical .k-link {
  padding-right: 8px;
  padding-left: 32px; }

.k-menu:not(.k-context-menu) {
  border-width: 0;
  background: none;
  background-clip: padding-box; }
  .k-ie11 .k-menu:not(.k-context-menu),
  .k-edge12 .k-menu:not(.k-context-menu),
  .k-edge13 .k-menu:not(.k-context-menu) {
    background-clip: border-box; }
  .k-menu:not(.k-context-menu) > .k-item {
    color: #607d8b; }
    .k-menu:not(.k-context-menu) > .k-item:hover, .k-menu:not(.k-context-menu) > .k-item.k-state-hover {
      color: #90a4ae; }
    .k-menu:not(.k-context-menu) > .k-item > .k-state-active {
      color: #000000; }
    .k-menu:not(.k-context-menu) > .k-item.k-state-selected {
      color: #ffffff; }
    .k-menu:not(.k-context-menu) > .k-item:focus, .k-menu:not(.k-context-menu) > .k-item.k-state-focused {
      box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13);
      outline: 0; }

.k-menu-group .k-item > .k-state-active,
.k-menu.k-context-menu .k-item > .k-state-active {
  color: #ffffff;
  background-color: #607d8b; }

.k-menu-group .k-item:focus, .k-menu-group .k-item.k-state-focused,
.k-menu.k-context-menu .k-item:focus,
.k-menu.k-context-menu .k-item.k-state-focused {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13);
  outline: 0; }

.k-menu-scroll-button {
  border-color: #c2c2c2;
  color: #607d8b;
  background: #ffffff; }
  .k-menu-scroll-button:hover {
    border-color: #c2c2c2;
    color: #90a4ae;
    background: #ffffff; }
    .k-menu-scroll-button:hover::before {
      opacity: 0; }

.k-panelbar {
  margin: 0;
  padding: 0;
  list-style: none; }
  .k-panelbar > .k-item {
    border-width: 0;
    border-style: solid;
    border-color: inherit;
    display: block; }
    .k-panelbar > .k-item > .k-link {
      padding: 8px 8px;
      color: inherit;
      background: none;
      text-decoration: none;
      display: flex;
      flex-direction: row;
      align-items: center;
      align-content: center;
      position: relative;
      user-select: none;
      cursor: default;
      transition: background-color 0.2s ease; }
  .k-panelbar > .k-item + .k-item {
    border-top-width: 1px; }
  .k-panelbar .k-group {
    margin: 0;
    padding: 0;
    list-style: none; }
  .k-panelbar .k-group > .k-item {
    display: block; }
    .k-panelbar .k-group > .k-item > .k-link {
      padding: 4px 8px;
      color: inherit;
      text-decoration: none;
      display: flex;
      flex-direction: row;
      align-items: center;
      align-content: center;
      position: relative;
      user-select: none;
      cursor: default;
      transition: background-color 0.2s ease; }
  .k-panelbar .k-panelbar-expand,
  .k-panelbar .k-panelbar-collapse {
    margin-top: -.5em;
    position: absolute;
    top: 50%;
    right: 8px; }
  .k-panelbar .k-link > .k-image,
  .k-panelbar .k-link > .k-sprite {
    vertical-align: middle;
    float: left;
    margin-right: 5px; }
  .k-panelbar[dir='rtl'] .k-panelbar-expand,
  .k-panelbar[dir='rtl'] .k-panelbar-collapse,
  .k-rtl .k-panelbar .k-panelbar-expand,
  .k-rtl .k-panelbar .k-panelbar-collapse {
    right: auto;
    left: 8px; }

.k-panelbar {
  border-color: #c2c2c2;
  color: #000000;
  background-color: #ffffff;
  background-clip: padding-box; }
  .k-ie11 .k-panelbar,
  .k-edge12 .k-panelbar,
  .k-edge13 .k-panelbar {
    background-clip: border-box; }
  .k-panelbar .k-content {
    color: #000000;
    background-color: #f5f5f5; }
  .k-panelbar > .k-item {
    background-clip: padding-box; }
    .k-ie11 .k-panelbar > .k-item,
    .k-edge12 .k-panelbar > .k-item,
    .k-edge13 .k-panelbar > .k-item {
      background-clip: border-box; }
    .k-panelbar > .k-item > .k-link {
      color: #607d8b; }
      .k-panelbar > .k-item > .k-link .k-icon {
        color: #000000; }
    .k-panelbar > .k-item > .k-link:hover,
    .k-panelbar > .k-item > .k-link.k-state-hover {
      color: #90a4ae;
      background-color: #f2f2f2; }
    .k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-expanded) .k-icon,
    .k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-active) .k-icon {
      color: #ffffff; }
    .k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-expanded):hover, .k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-expanded).k-state-hover,
    .k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-active):hover,
    .k-panelbar > .k-item > .k-link.k-state-selected:not(.k-state-active).k-state-hover {
      color: #ffffff;
      background-color: #587380; }
    .k-panelbar > .k-item.k-state-expanded > .k-link,
    .k-panelbar > .k-item.k-state-active > .k-link {
      font-weight: 500; }
      .k-panelbar > .k-item.k-state-expanded > .k-link .k-icon,
      .k-panelbar > .k-item.k-state-active > .k-link .k-icon {
        color: #000000; }
    .k-panelbar > .k-item > .k-link.k-state-focused {
      box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }
  .k-panelbar .k-group {
    color: #000000;
    background-color: #f5f5f5; }
    .k-panelbar .k-group > .k-item > .k-link:hover.k-state-selected,
    .k-panelbar .k-group > .k-item > .k-link.k-state-hover.k-state-selected {
      color: #ffffff;
      background-color: #587380; }
    .k-panelbar .k-group > .k-item > .k-link.k-state-focused {
      box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.13); }

.k-progressbar {
  border-radius: 4px;
  border-width: 0;
  font-size: 12px;
  line-height: 22px;
  display: inline-flex;
  vertical-align: middle;
  position: relative;
  overflow: hidden; }
  .k-progressbar .k-progress-status-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    position: absolute;
    top: 0;
    left: 0; }
  .k-progressbar .k-progress-status {
    padding: 0 0.5em;
    min-width: 10px;
    text-align: center;
    display: inline-block;
    white-space: nowrap; }
  .k-progressbar > .k-state-selected {
    position: absolute;
    overflow: hidden;
    border-style: solid;
    border-width: 0; }
  .k-progressbar > ul {
    border-width: inherit;
    border-style: inherit;
    white-space: nowrap;
    display: flex;
    align-items: stretch;
    flex: 1; }
  .k-progressbar .k-item {
    display: block;
    border-width: 0;
    border-style: solid;
    border-color: transparent; }

.k-progressbar-horizontal {
  width: 27em;
  height: 22px;
  flex-direction: row; }
  .k-progressbar-horizontal .k-progress-status-wrap {
    flex-direction: row;
    justify-content: flex-end;
    top: 0;
    left: 0; }
  .k-progressbar-horizontal > .k-state-selected {
    left: 0;
    right: auto;
    top: 0;
    height: 100%; }
  .k-progressbar-horizontal > ul {
    flex-direction: row; }
  .k-progressbar-horizontal .k-item + .k-item {
    border-left-width: 1px; }
  .k-progressbar-horizontal.k-progressbar-reverse {
    flex-direction: row-reverse; }
    .k-progressbar-horizontal.k-progressbar-reverse .k-progress-status-wrap {
      justify-content: flex-start;
      left: auto;
      right: 0; }
    .k-progressbar-horizontal.k-progressbar-reverse > .k-state-selected {
      left: auto;
      right: 0; }

.k-progressbar-vertical {
  width: 22px;
  height: 27em;
  flex-direction: column;
  justify-content: flex-end; }
  .k-progressbar-vertical .k-progress-status-wrap {
    flex-direction: column;
    justify-content: flex-start;
    left: 0;
    bottom: 0; }
  .k-progressbar-vertical .k-progress-status {
    transform: rotate(-90deg) translateX(-100%);
    transform-origin: 0 0; }
  .k-progressbar-vertical > .k-state-selected {
    left: -1px;
    bottom: -1px;
    width: 100%; }
  .k-progressbar-vertical > ul {
    flex-direction: column; }
  .k-progressbar-vertical .k-item + .k-item {
    border-top-width: 1px; }
  .k-progressbar-vertical.k-progressbar-reverse {
    flex-direction: column-reverse; }
    .k-progressbar-vertical.k-progressbar-reverse .k-progress-status-wrap {
      justify-content: flex-end;
      top: 0;
      bottom: auto; }
    .k-progressbar-vertical.k-progressbar-reverse .k-progress-status {
      transform: rotate(90deg) translateX(-100%);
      transform-origin: 0 100%;
      position: absolute;
      bottom: 0;
      left: 0; }
    .k-progressbar-vertical.k-progressbar-reverse > .k-state-selected {
      left: auto;
      right: 0;
      bottom: auto;
      top: 0; }

.k-progressbar {
  background-color: #f5f5f5;
  border-color: #c2c2c2;
  background-clip: padding-box; }
  .k-ie11 .k-progressbar,
  .k-edge12 .k-progressbar,
  .k-edge13 .k-progressbar {
    background-clip: border-box; }
  .k-progressbar .k-state-selected {
    border-color: #58727f;
    background-color: #607d8b; }
  .k-progressbar .k-item {
    border-color: #ffffff; }

.k-progressbar-indeterminate {
  background: url("data:image/gif;base64,R0lGODlhFgAWAJECAPDw8OTk5AAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFBAACACwAAAAAFgAWAAACL4yPAcsNmZyESDp6bMNGM654DyeOWAmipJiyqweGWuy+Fg23s37zk38BboQf4qIAACH5BAUEAAIALAAAAAAWABYAAAIwBIKpYe231ntRTlfTxZlt03lf+IFdCWUoWbLj2cKvGstXKN6ubk94Tdv8ZkFe0VEAACH5BAUEAAIALAAAAAAWABYAAAIwhBGpG+fH4nuRTVfXhTltnn1G14ikiHToaapt+Far+7E1vZFlbl96LLvhJj8hzFEAACH5BAUEAAIALAAAAAAWABYAAAIwhB+pG+fI2HtRTljTxXmbrHgAqImkOILoaapt91ar67E1vdk5fpHNzaPoJr6Y7FAAACH5BAUEAAIALAAAAAAWABYAAAIvhG+hy4EPmoRINnosw0krHnkOJ45YCaKpB4ZaK8Ls+tKWXJOznp+736OogMGHqgAAIfkEBQQAAgAsAAAAABYAFgAAAi+Ej6HLgQ+ahEkyeuzFUSveeaD3caRjkqOaiqsGhlbMtvN72xO+6w3t8m2EC6CnAAAh+QQFBAACACwAAAAAFgAWAAACMAyOCcHtCp1kME1Z0X15bdp9YCYaodh5X1qy6LnCW6pe9CtPd5xj7vyzBXVDX69RAAAh+QQFBAACACwAAAAAFgAWAAACMIyBqWDtseJ7cU1X1cWZ7dN9YCZCodh5X1qy6LnCmxuTrzyl6qXf9kzjBXNDSjFQAAAh+QQFBAACACwAAAAAFgAWAAACLowNqQvnx+J7kU1X14U5bdQ1XxCOZGeWo/qxm3vBk0yhq93iL83pMw/y1YQYWAEAIfkEBQQAAgAsAAAAABYAFgAAAjCMH6DLgJ9ag0hOaizDWSseeaD3caQziqkGhlZLwqo5Y/H64pLM6hddg5x8P8rQUAAAIfkEBQQAAgAsAAAAABYAFgAAAjCMf6DLgB+ahEg2muzCRm/uKVwUjqEDlqk3klqrrhbMmrU82a8+8zl+8QWBQ8opUAAAOw=="); }
  .k-progressbar-indeterminate .k-progress-status-wrap,
  .k-progressbar-indeterminate .k-state-selected {
    display: none; }

.km-scrollview,
.km-scroll-container {
  user-select: none;
  margin-collapse: separate; }

.km-scroll-wrapper {
  position: relative; }

.km-scroll-header {
  position: absolute;
  z-index: 1001;
  width: 100%;
  top: 0;
  left: 0; }

.km-scroller-pull {
  width: 100%;
  display: block;
  position: absolute;
  line-height: 3em;
  font-size: 1.4em;
  text-align: center;
  transform: translate3d(0, -3em, 0); }

.km-scroller-pull .km-template {
  display: inline-block;
  min-width: 200px;
  text-align: left; }

.km-load-more .km-icon,
.km-widget .km-scroller-pull .km-icon {
  display: inline-block;
  height: 2rem;
  margin-right: 1rem;
  vertical-align: middle;
  width: 2rem;
  font-size: 2rem;
  transform: rotate(0deg);
  transition: transform 300ms linear; }

.km-widget .km-scroller-release .km-icon {
  transform: rotate(180deg); }

.km-widget .km-scroller-refresh .km-icon {
  transition: none; }

.km-touch-scrollbar {
  position: absolute;
  visibility: hidden;
  z-index: 200000;
  height: .4em;
  width: .4em;
  background-color: #333;
  opacity: 0;
  transform-origin: 0 0;
  transition: opacity .3s linear; }

.km-vertical-scrollbar {
  height: 100%;
  right: 2px;
  top: 0; }

.km-horizontal-scrollbar {
  width: 100%;
  left: 0;
  bottom: 2px; }

.k-typography {
  font-size: 14px;
  line-height: 1.42857;
  font-weight: 400; }
  .k-typography p {
    margin: 0 0 14px; }

.k-h1 {
  font-family: inherit;
  font-size: 28px;
  line-height: normal;
  font-weight: 300;
  margin: 0 0 14px; }

.k-h2 {
  font-family: inherit;
  font-size: 16px;
  line-height: normal;
  font-weight: 400;
  margin: 0 0 14px; }

.k-h3 {
  font-family: inherit;
  font-size: 14px;
  line-height: normal;
  font-weight: 700;
  margin: 0 0 14px; }

.k-h4 {
  font-family: inherit;
  font-size: 12px;
  line-height: normal;
  font-weight: 700;
  margin: 0 0 14px; }

.k-h5 {
  font-family: inherit;
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin: 0 0 14px; }

.k-h6 {
  font-family: inherit;
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin: 0 0 14px; }

.k-display-1 {
  font-family: inherit;
  font-size: 84px;
  line-height: 1.2;
  font-weight: 300; }

.k-display-2 {
  font-family: inherit;
  font-size: 70px;
  line-height: 1.2;
  font-weight: 300; }

.k-display-3 {
  font-family: inherit;
  font-size: 56px;
  line-height: 1.2;
  font-weight: 300; }

.k-display-4 {
  font-family: inherit;
  font-size: 42px;
  line-height: 1.2;
  font-weight: 300; }

.k-block,
.k-panel {
  border-radius: 4px;
  padding: 4px 8px;
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box; }
  .k-block > .k-header,
  .k-panel > .k-header {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin: -4px -8px 4px;
    padding: 8px 8px; }

.k-text-primary {
  color: #607d8b !important; }

.k-text-info {
  color: #2196f3 !important; }

.k-text-success {
  color: #009688 !important; }

.k-text-warning {
  color: #ffc107 !important; }

.k-text-error {
  color: #f44336 !important; }

.k-bg-primary {
  background-color: #607d8b !important; }

.k-bg-info {
  background-color: #2196f3 !important; }

.k-bg-success {
  background-color: #009688 !important; }

.k-bg-warning {
  background-color: #ffc107 !important; }

.k-bg-error {
  background-color: #f44336 !important; }

.k-state-primary, .k-card.k-state-primary {
  background-color: #dfe5e8;
  color: #324148;
  border-color: #d2dbdf; }

.k-state-info, .k-card.k-state-info {
  background-color: #d3eafd;
  color: #114e7e;
  border-color: #c1e2fc; }

.k-state-success, .k-card.k-state-success {
  background-color: #cceae7;
  color: #004e47;
  border-color: #b8e2de; }

.k-state-warning, .k-card.k-state-warning {
  background-color: #fff3cd;
  color: #856404;
  border-color: #ffeeba; }

.k-state-error, .k-card.k-state-error {
  background-color: #fdd9d7;
  color: #7f231c;
  border-color: #fccac7; }

.k-info-colored {
  color: #50607f;
  border-color: #d0d9df;
  background-color: #f0f9ff; }

.k-success-colored {
  color: #507f50;
  border-color: #d0dfd0;
  background-color: #f0fff0; }

.k-error-colored {
  color: #7f5050;
  border-color: #dfd0d0;
  background-color: #fff0f0; }

.k-shadow {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.03), 0 4px 5px 0 rgba(0, 0, 0, 0.04); }

.k-inset {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.03), inset 0 4px 5px 0 rgba(0, 0, 0, 0.04); }

.k-badge {
  border-radius: 4px;
  padding: 0 4px;
  box-sizing: border-box;
  font-size: 10px;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis; }

.k-button .k-badge {
  margin-left: 4px;
  position: relative;
  z-index: 5; }

.k-rtl .k-badge {
  right: auto;
  left: -0.75em; }

.k-badge {
  color: #ffffff;
  background-color: #607d8b; }

.k-card {
  border-radius: 4px;
  box-shadow: false;
  border-width: 1px;
  border-style: solid;
  display: flex;
  flex-direction: column;
  overflow: hidden; }

.k-card.k-card-flat {
  box-shadow: none; }

.k-card > .k-card-header:first-child, .k-card > .k-card-body:first-child, .k-card > .k-card-image:first-child, .k-card > .k-card-actions:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px; }

.k-card > .k-card-header:last-child, .k-card > .k-card-body:last-child, .k-card > .k-card-image:last-child, .k-card > .k-card-actions:last-child {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px; }

.k-card-header {
  padding: 12px 16px;
  border-width: 0 0 1px;
  border-style: solid;
  overflow: hidden; }
  .k-card-header > h1,
  .k-card-header > h2,
  .k-card-header > h3,
  .k-card-header > h4,
  .k-card-header > h5,
  .k-card-header > h6 {
    margin: 0; }

.k-card-body {
  padding: 12px 16px;
  flex: 1 1 0; }
  .k-card-body p {
    margin: 0 0 14px; }
  .k-card-body > .k-last,
  .k-card-body > :last-child {
    margin-bottom: 0; }

.k-card-image {
  border: 0;
  max-width: 100%;
  overflow: hidden; }
  .k-card-image > img {
    border: 0;
    max-width: 100%; }

.k-card-title {
  font-family: inherit;
  font-size: 16px;
  line-height: normal;
  font-weight: 400;
  margin: 0 0 14px; }

.k-card-subtitle {
  font-family: inherit;
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin: 0 0 14px; }

.k-card-title + .k-card-subtitle {
  margin-top: -10.5px; }

.k-card > .k-hr {
  margin: 0;
  flex: 0 0 auto;
  border-color: inherit; }

.k-card-actions {
  padding: 8px 16px;
  border-width: 0;
  border-style: solid;
  border-color: inherit;
  overflow: hidden;
  flex-shrink: 0;
  flex-basis: auto; }
  .k-card > .k-card-actions {
    border-top-width: 1px;
    border-color: inherit; }
  .k-card-actions > .k-button.k-flat:first-child, .k-toolbar.k-card-actions > .k-button:first-child:not(.k-overflow-anchor),
  .k-toolbar > .k-button-group.k-card-actions > .k-button:first-child {
    margin-left: -8px; }

.k-card-action {
  border-width: 0;
  border-style: solid;
  border-color: inherit;
  display: inline-flex;
  flex: 1 1 auto; }
  .k-card-action > .k-button {
    border-radius: 0;
    padding: 12px 16px;
    flex: 1 1 auto; }

.k-card-actions-vertical {
  padding: 0;
  display: flex;
  flex-direction: column; }
  .k-card-actions-vertical .k-card-action + .k-card-action {
    border-top-width: 1px; }

.k-card-actions-stretched {
  padding: 0;
  display: flex;
  flex-direction: row; }
  .k-card-actions-stretched .k-card-action + .k-card-action {
    border-left-width: 1px; }

.k-card-list {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: stretch;
  flex: 0 0 auto; }
  .k-card-list .k-card {
    flex: 0 0 auto; }
  .k-card-list .k-card + .k-card {
    margin-top: 16px; }

.k-card-deck {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: stretch;
  flex: 0 0 auto; }
  .k-card-deck .k-card {
    flex: 0 0 auto; }
  .k-card-deck .k-card + .k-card {
    margin-left: 16px; }

.k-card-group {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: stretch;
  flex: 0 0 auto; }
  .k-card-group .k-card {
    border-radius: 0;
    flex: 0 0 auto; }
    .k-card-group .k-card > .k-card-header {
      border-radius: 0; }
  .k-card-group .k-card + .k-card {
    margin-left: -1px; }
  .k-card-group .k-card.k-first {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px; }
    .k-card-group .k-card.k-first > .k-card-header {
      border-top-left-radius: 4px; }
  .k-card-group .k-card.k-last {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px; }
    .k-card-group .k-card.k-last > .k-card-header {
      border-top-right-radius: 4px; }
  .k-card-group .k-card.k-only {
    border-radius: 4px; }
    .k-card-group .k-card.k-only > .k-card-header {
      border-top-left-radius: 4px;
      border-top-right-radius: 4px; }

.k-rtl .k-card-deck .k-card + .k-card,
[dir="rtl"] .k-card-deck .k-card + .k-card {
  margin-left: 0;
  margin-right: 16px; }

.k-ie11 .k-card-body {
  flex: 1 1 auto; }

.k-card {
  border-color: #c2c2c2;
  color: #000000;
  background-color: #ffffff; }

.k-card-header {
  border-color: #c2c2c2;
  color: #000000;
  background-color: #f5f5f5; }

.k-card-actions {
  border-color: #c2c2c2; }

.k-chat {
  height: 600px;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 500px;
  margin: auto; }
  .k-chat .k-message-list {
    padding: 16px 16px;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    overflow-x: hidden;
    overflow-y: auto;
    scroll-behavior: smooth; }
    .k-chat .k-message-list > * + * {
      margin-top: 16px; }
  .k-chat .k-message-group {
    max-width: 80%;
    background: none;
    box-sizing: border-box;
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    position: relative; }
  .k-chat .k-message-group:not(.k-alt) {
    align-items: flex-start;
    text-align: left; }
    .k-chat .k-message-group:not(.k-alt) .k-message-time {
      margin-left: 8px;
      left: 100%; }
    .k-chat .k-message-group:not(.k-alt) .k-message-status {
      left: 0; }
    .k-chat .k-message-group:not(.k-alt) .k-first .k-bubble,
    .k-chat .k-message-group:not(.k-alt) .k-only .k-bubble {
      border-bottom-left-radius: 2px; }
    .k-chat .k-message-group:not(.k-alt) .k-middle .k-bubble,
    .k-chat .k-message-group:not(.k-alt) .k-last .k-bubble {
      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px; }
  .k-chat .k-message-group.k-alt {
    align-self: flex-end;
    align-items: flex-end;
    text-align: right; }
    .k-chat .k-message-group.k-alt .k-message-time {
      margin-right: 8px;
      right: 100%; }
    .k-chat .k-message-group.k-alt .k-message-status {
      right: 0; }
    .k-chat .k-message-group.k-alt .k-first .k-bubble,
    .k-chat .k-message-group.k-alt .k-only .k-bubble {
      border-bottom-right-radius: 2px; }
    .k-chat .k-message-group.k-alt .k-middle .k-bubble,
    .k-chat .k-message-group.k-alt .k-last .k-bubble {
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px; }
  .k-chat .k-message {
    margin: 2px 0 0;
    position: relative;
    transition: margin .2s ease-in-out; }
  .k-chat .k-message-time,
  .k-chat .k-message-status {
    font-size: smaller;
    line-height: normal;
    white-space: nowrap;
    pointer-events: none;
    position: absolute; }
  .k-chat .k-message-time {
    opacity: 0;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity .2s ease-in-out; }
  .k-chat .k-message-status {
    margin-top: 2px;
    height: 0;
    overflow: hidden;
    top: 100%;
    transition: height .2s ease-in-out; }
  .k-chat .k-bubble {
    border-radius: 12px;
    padding: 8px 12px;
    border-width: 1px;
    border-style: solid;
    line-height: 18px; }
  .k-chat .k-message.k-state-selected {
    margin-bottom: 16px;
    border: 0;
    color: inherit;
    background: none; }
    .k-chat .k-message.k-state-selected .k-message-time {
      opacity: 1; }
    .k-chat .k-message.k-state-selected .k-message-status {
      height: 1.2em; }
  .k-chat .k-message-error,
  .k-chat .k-message-sending {
    margin-bottom: 16px; }
    .k-chat .k-message-error .k-message-status,
    .k-chat .k-message-sending .k-message-status {
      height: 1.2em; }

.k-avatar {
  border-radius: 100%;
  width: 32px;
  height: 32px;
  position: absolute; }
  .k-message-group:not(.k-alt) > .k-avatar {
    left: 0;
    bottom: 0; }
  .k-message-group.k-alt > .k-avatar {
    right: 0;
    bottom: 0; }

.k-avatars .k-message-group:not(.k-alt) {
  padding-left: calc( 32px + 8px); }

.k-avatars .k-message-group.k-alt {
  padding-right: calc( 32px + 8px); }

.k-author {
  margin: 0;
  font-size: smaller;
  line-height: normal; }

.k-chat .k-author {
  margin: 0; }

.k-timestamp {
  font-size: smaller;
  line-height: normal;
  text-align: center;
  align-self: stretch; }

.k-quick-replies {
  display: block; }

.k-quick-reply {
  border-radius: 100px;
  margin-right: 8px;
  margin-bottom: 4px;
  padding: 8px 12px;
  border-width: 1px;
  border-style: solid;
  line-height: 18px;
  cursor: pointer;
  user-select: none;
  display: inline-block;
  flex: 0 0 auto;
  transition-property: color, background-color, border-color;
  transition-duration: .2s;
  transition-timing-function: ease-in-out; }

.k-scrollable-quick-replies {
  margin-left: -16px;
  margin-right: -16px;
  padding-left: 16px;
  padding-right: 16px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  flex: 0 0 auto;
  overflow-x: auto;
  overflow-y: hidden; }
  .k-scrollable-quick-replies::-webkit-scrollbar {
    display: none; }
  .k-scrollable-quick-replies .k-quick-reply {
    margin: 0; }
  .k-scrollable-quick-replies .k-quick-reply + .k-quick-reply {
    margin-left: 8px; }

.k-message-box {
  padding: 10px 16px;
  border-width: 1px 0 0;
  border-style: solid;
  flex: 0 0 auto;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap; }
  .k-message-box .k-input {
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    background: none;
    flex: 1 1 auto; }
  .k-message-box .k-button {
    padding: 0; }
    .k-message-box .k-button svg {
      width: 20px;
      height: 20px;
      fill: currentColor;
      display: inline-block; }
    .k-message-box .k-button::before, .k-message-box .k-toolbar .k-picker-wrap::before, .k-toolbar .k-message-box .k-picker-wrap::before, .k-message-box
    .k-toolbar .k-dropdown-wrap::before,
    .k-toolbar .k-message-box .k-dropdown-wrap::before, .k-message-box .k-button::after {
      display: none; }

.k-chat .k-card-list {
  margin: 2px 0 0; }

.k-chat .k-card-deck {
  margin-left: -16px;
  margin-right: -16px;
  padding-left: 16px;
  padding-right: 16px;
  overflow: hidden;
  overflow-x: auto; }
  .k-chat .k-card-deck::-webkit-scrollbar {
    display: none; }
  .k-chat .k-card-deck .k-card-wrap + .k-card-wrap {
    margin-left: 16px; }

.k-chat .k-card-deck .k-card,
.k-chat .k-card-deck .k-card-wrap {
  flex-basis: 200px; }

.k-chat .k-card-deck .k-card-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: stretch;
  flex: 0 0 auto; }
  .k-chat .k-card-deck .k-card-wrap .k-card {
    flex: 0 0 auto; }
  .k-chat .k-card-deck .k-card-wrap .k-card + .k-card {
    margin-left: 16px; }

.k-typing-indicator {
  padding: 0;
  border-radius: 50px;
  display: inline-flex;
  flex-direction: row;
  flex-wrap: nowrap; }
  .k-typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex: 0 0 8px;
    background-color: currentColor;
    opacity: .4; }
    .k-typing-indicator span:nth-of-type(1) {
      animation: 1s k-animation-blink infinite 0.3333s; }
    .k-typing-indicator span:nth-of-type(2) {
      animation: 1s k-animation-blink infinite 0.6666s; }
    .k-typing-indicator span:nth-of-type(3) {
      animation: 1s k-animation-blink infinite 0.9999s; }
  .k-typing-indicator span + span {
    margin-left: 5px; }

@keyframes k-animation-blink {
  50% {
    opacity: 1; } }

.k-rtl .k-message-group:not(.k-alt),
[dir="rtl"] .k-message-group:not(.k-alt) {
  text-align: right; }
  .k-rtl .k-message-group:not(.k-alt) .k-message-time,
  [dir="rtl"] .k-message-group:not(.k-alt) .k-message-time {
    margin-left: 0;
    margin-right: 8px;
    left: auto;
    right: 100%; }
  .k-rtl .k-message-group:not(.k-alt) .k-message-status,
  [dir="rtl"] .k-message-group:not(.k-alt) .k-message-status {
    left: auto;
    right: 0; }

.k-rtl .k-message-group.k-alt,
[dir="rtl"] .k-message-group.k-alt {
  text-align: left; }
  .k-rtl .k-message-group.k-alt .k-message-time,
  [dir="rtl"] .k-message-group.k-alt .k-message-time {
    margin-right: 0;
    margin-left: 8px;
    right: auto;
    left: 100%; }
  .k-rtl .k-message-group.k-alt .k-message-status,
  [dir="rtl"] .k-message-group.k-alt .k-message-status {
    right: auto;
    left: 0; }

.k-rtl .k-message-group:not(.k-alt) > .k-avatar,
[dir="rtl"] .k-message-group:not(.k-alt) > .k-avatar {
  left: auto;
  right: 0; }

.k-rtl .k-message-group.k-alt > .k-avatar,
[dir="rtl"] .k-message-group.k-alt > .k-avatar {
  right: auto;
  left: 0; }

.k-rtl .k-avatars .k-message-group:not(.k-alt),
[dir="rtl"] .k-avatars .k-message-group:not(.k-alt) {
  padding-left: 0;
  padding-right: calc( 32px + 8px); }

.k-rtl .k-avatars .k-message-group.k-alt,
[dir="rtl"] .k-avatars .k-message-group.k-alt {
  padding-right: 0;
  padding-left: calc( 32px + 8px); }

.k-rtl .k-chat .k-card-deck .k-card-wrap + .k-card-wrap,
[dir="rtl"] .k-chat .k-card-deck .k-card-wrap + .k-card-wrap {
  margin-left: 0;
  margin-right: 16px; }

.k-rtl .k-quick-reply,
[dir="rtl"] .k-quick-reply {
  margin-right: 0;
  margin-left: 8px; }

.k-chat {
  border-color: #c2c2c2;
  color: #000000;
  background-color: #f8f8f8; }
  .k-chat .k-timestamp {
    text-transform: uppercase;
    opacity: .7; }
  .k-chat .k-author {
    font-weight: bold; }
  .k-chat .k-bubble {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    border-color: #ffffff;
    color: #000000;
    background-color: #ffffff;
    transition: box-shadow .2s ease-in-out;
    order: -1; }
  .k-chat .k-bubble:hover {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.16); }
  .k-chat .k-state-selected .k-bubble {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.16); }
  .k-chat .k-alt .k-bubble {
    box-shadow: 0 1px 2px rgba(96, 125, 139, 0.2);
    border-color: #607d8b;
    color: #ffffff;
    background-color: #607d8b; }
  .k-chat .k-alt .k-bubble:hover {
    box-shadow: 0 1px 2px rgba(96, 125, 139, 0.2); }
  .k-chat .k-alt .k-state-selected .k-bubble {
    box-shadow: 0 3px 10px rgba(96, 125, 139, 0.4); }
  .k-chat .k-quick-reply {
    border-color: #607d8b;
    color: #607d8b;
    background-color: transparent; }
  .k-chat .k-quick-reply:hover {
    border-color: #607d8b;
    color: #ffffff;
    background-color: #607d8b; }
  .k-chat .k-message-box {
    border-color: inherit;
    color: #000000;
    background-color: #ffffff; }
  .k-chat .k-message-box.k-state-focused {
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.1); }
  .k-chat .k-message-box .k-button:hover {
    color: #607d8b; }

.theme-m {
  color: #607d8b; }

.theme-m-bg {
  color: #ffffff;
  background-color: #607d8b;
  border-color: #607d8b; }

.theme-m-txt {
  color: #607d8b;
  background-color: #ffffff;
  border-color: #ffffff; }

.theme-m-box {
  color: #607d8b;
  background-color: #ffffff;
  border-color: #607d8b; }

.theme-s {
  color: #90a4ae; }

.theme-s-bg {
  color: #ffffff;
  background-color: #90a4ae;
  border-color: #90a4ae; }

.theme-s-txt {
  color: #90a4ae;
  background-color: #ffffff;
  border-color: #ffffff; }

.theme-s-box {
  color: #90a4ae;
  background-color: #ffffff;
  border-color: #90a4ae; }

.theme-c {
  color: #ffffff; }

.theme-l {
  border-color: #607d8b;
  color: #607d8b;
  background-color: #dfe5e8; }
