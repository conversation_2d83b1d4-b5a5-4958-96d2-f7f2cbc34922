/*!
 * Kendo UI Admin v1.0.0 by IKKI & Amikoko - https://ikki2000.github.io/
 * Copyright 2018-2019 IKKI Studio
 * Released under the MIT License - http://localhost:5500/LICENSE
 */

/* JS for Layout | Written by IKKI | 2018-02-03 */

// 配置路径
var path = $('base').attr('href'),
    webType = $('base').attr('type'),
    accentColor,
    minorColor,
    tokenUrl = 'json/logout.json', // Token 验证接口
    navUrl = 'system/config/power/query/one/misMenu/Mutils', // 左侧导航数据接口json/nav.json
    menuUrl = 'json/menu.json', // 顶部菜单数据接口
    searchUrl = 'json/search.json', // 全局搜索自动填充数据接口
    messageUrl = 'json/message.json', // 新消息数量获取接口
    inboxUrl = 'json/message.json', // 收件箱列表获取接口
    outboxUrl = 'json/message.json', // 发件箱列表获取接口
    smsUrl = 'json/message.json', // 短信息列表获取接口
    addressBookUrl = 'json/message.json', // 通讯录列表获取接口
    mailReadUrl = 'json/response.json', // 站内信单条已读标记接口
    mailSendUrl = 'json/response.json', // 站内信发送接口
    smsReadUrl = 'json/response.json', // 短信息已读标记接口
    smsSendUrl = 'json/response.json', // 短信息发送接口
    noticeUrl = 'json/notice.json', // 新提醒数量获取接口
    systemNotificationUrl = 'json/notice.json', // 系统通知列表获取接口
    userUpdatingUrl = 'json/notice.json', // 个人动态列表获取接口
    toDoItemsUrl = 'json/notice.json', // 待办事项列表获取接口
    noticeReadUrl = 'json/response.json', // 提醒单条已读标记接口
    noticeReadAllUrl = 'json/response.json'; // 提醒全部已读标记接c

/* 初始化 ****************************************************************************/
$(function () {
    // 配色
    if (localStorage.hasOwnProperty('colorName')) {
        accentColor = localStorage.getItem('accentColor');
        minorColor = localStorage.getItem('minorColor');
        changeColor(localStorage.getItem('colorName'), accentColor, minorColor);
    } else {
        // accentColor = '#009688';
        // minorColor = '#4db6ac';
        // changeColor('default', accentColor, minorColor);
        changeColor("material_teal");//默认配色
    }
    // 语言
    kendo.culture('zh-CHS');
    localStorage.setItem('culture', 'zh-CHS');

    // 左侧导航数据获取
    $.fn.ajaxPost({
        ajaxType:"POST",
        urlType:"api",
        ajaxUrl: navUrl,
        ajaxData:eval({"MENU_LEVEL":0,"PARENT_MENU_ID":""}),
        succeed: function (res) {
            var data = res.rows.sort(menu_sort);
            var menus = [];            
            ///TODO: 处理子菜单
            var strMenu="" 
            res.rows.forEach(function(firstRow,n){//第一级，第n个
                // firstRow.MENU_ICON="fas fa-map-signs";//class默认是这个
                // firstRow.MENU_ICON="iconfont ico";
                if(firstRow.MENU_ICON==""){
                    firstRow.MENU_ICON="fas fa-map-signs";//class默认是这个
                }
                
                var txt = "<i class='"+firstRow.MENU_ICON+"' style='width:100%;text-align: center;' ></i><abbr>" + firstRow.MENU_NAME + "</abbr>";                
                var url="javascript:showNavPanel("+n+")";//第一级 无url --传入后面的两级的数组
                wholeMenus[n]=firstRow.rows;
                var r =  { 
                    text: txt,
                    encoded: false,
                    cssClass: "links-datasource",
                    url: url
                } ;                
                menus.push(r);
                
                //var themeBackgroupColor=$(".theme-m-bg").css("background-color");

                strMenu +="<div id=\"listM" + n +"\" class=\"nav-box theme-s-bg\" >";
                strMenu +="<div style=\"width:120%;\">";
                // if(firstRow.rows.length>3){
                //     strMenu +="<div class=\"nav-hr1\" ></div>";                
                // }
                // if(firstRow.rows.length>6){
                //     strMenu +="<div class=\"nav-hr2\" ></div>";
                // }
                firstRow.rows.forEach(function(secondRow,m) {
                    var heightNumber=0;
                    strMenu += "<div class=\"tag-box\" tag-box-append-style ><h2><b class=\"theme-s-bg\" >"+ secondRow.MENU_NAME +"</b></h2><ol>"
                    secondRow.rows.forEach(function(thirdRow,len) {
                        var onclickVar=" onclick='javascript:linkTo(\""+thirdRow.MENU_PATH+"\",\""+thirdRow.MENU_NAME+"\",\""+thirdRow.MENU_ICON+"\");'";
                        if(thirdRow.MENU_PATH==""){
                            onclickVar=" onclick='alert(\"此功能尚未配置\");'";
                        }
                        strMenu += "<li><div class=\"tag-box-tex\"><b class=\"theme-s-bg\" title=\""
                        + thirdRow.MENU_NAME 
                        +"\""+onclickVar+">"
                        + thirdRow.MENU_NAME 
                        +"</b></li>";
                        heightNumber++;
                    });
                    //strMenu=strMenu.replace("tag-box-append-style","style=\"height:"+(heightNumber*22+77)+"px;\"");
                    strMenu += "</ol></div>";  
                });

                if(firstRow.rows.length==0){
                    //尚无菜单
                    strMenu +="<p style=\"font-weight:600;\" class=\"theme-s-bg\" >尚未绑定任何菜单功能→_→</p>";
                }

                strMenu +="</div></div>";


                // var txt = "<i class='fas fa-map-signs'></i><abbr>" + row.MENU_NAME + "</abbr>";                
                // var url = "javascript:linkTo(\"/framework/\", \"home\");";
                // if(row.rows.length>0)
                //     url = "javascript:showNavPanel(\"listM" + levelOne + "\");";
                // var r =  { 
                //     text: txt,
                //     encoded: false,
                //     cssClass: "links-datasource",
                //     url: url
                // } ;
                // menus.push(r);
            });




            $('#navPanelBar').kendoPanelBar({                
                dataSource: menus,
                loadOnDemand: false
            });

            $('#navMenu').kendoMenu({                
                orientation: 'vertical',
                dataSource: menus
            });


            // data.forEach(function(MenuLevelOne,levelOne){ 
            //     strMenu +="<div id=\"listM" + levelOne +"\" class=\"nav-box\">";
            //     strMenu +="<div style=\"width:120%;\">";
            //     if(MenuLevelOne.rows.length>1)
            //         strMenu +="<div class=\"nav-hr1\" ></div>";                
            //     if(MenuLevelOne.rows.length>2)
            //         strMenu +="<div class=\"nav-hr2\" ></div>";
            //     MenuLevelOne.rows.forEach(function(MenuLevelTwo,levelTwo) {
            //         strMenu += "<div class=\"tag-box\"><h2><b>"+ MenuLevelTwo.MENU_NAME +"</b></h2><ol>"
            //         MenuLevelTwo.rows.forEach(function(MenuLevelThree,levelThree) {
            //             strMenu += "<li><div class=\"tag-box-tex\"><b title=\""
            //             + MenuLevelThree.MENU_NAME 
            //             +"\" onclick='linkTo(\"/framework/\", \"datasource\");'>"
            //             + MenuLevelThree.MENU_NAME 
            //             +"</b></li>";
            //         });
            //         strMenu += "</ol></div>";
            //     });
            //     strMenu +="</div></div>";
            // });
            $("#navPanel").append(strMenu);

            $.mCustomScrollbar.defaults.scrollButtons.enable=true; //enable scrolling buttons by default
            $.mCustomScrollbar.defaults.axis="yx"; //enable 2 axis scrollbars by default
            $("#nav").mCustomScrollbar();//细线滚动条加载

            var inasideFlag=false;
            $("#aside").mouseenter(function(){
                //鼠标进入
                inasideFlag=true;
            }).mouseleave(function(){
                //鼠标离开
                inasideFlag=false;
                //延迟0.25秒执行
                setTimeout(() => {
                    if(inasideFlag==false){
                        closeNavPanel();
                    }
                }, 350);//延迟0.35s执行-关闭
            });
            $(".nav-box").mouseenter(function(){
                //鼠标进入
                inasideFlag=true;
            }).mouseleave(function(){
                //鼠标离开
                inasideFlag=false;
                //延迟0.25秒执行
                setTimeout(() => {
                    if(inasideFlag==false){
                        closeNavPanel();
                    }
                }, 350);//延迟0.35s执行-关闭
            });

        }
    });

    function menu_sort(x,y){
        return x.MENU_SORT_NUMBER-y.MENU_SORT_NUMBER;
    }



    // 顶部菜单数据获取
    $.fn.ajaxPost({
        ajaxUrl: menuUrl,
        succeed: function (res) {
            for(var i=0;i<res.data.length;i++){
                if(res.data[i].id){
                    if(res.data[i].id=="limsUser"){
                        var limsUser=getLimsUser();
                        var name="管理员";
                        if(limsUser){
                            name=limsUser["name"];
                        }
                        res.data[i].text= "<abbr>百迈客 [ "+name+" ]</abbr><img src='img/IKKI.png' alt='Yikon'>";
                    }
                }
                if(res.data[i].hide){
                    res.data.splice(i,1);//移除一个
                    i--;
                }
            }
            $('#menuV').kendoMenu({
                orientation: 'vertical',
                dataSource: res.data,
                dataBound: function () {
                    // globalSearch();
                    $('#menuV .links-message, #menuV .links-notice').find('span.k-menu-expand-arrow, div.k-content').remove();
                }
            });
            $('#menuH').kendoMenu({
                closeOnClick: false,
                dataSource: res.data,
                open: function (e) {
                    // if ($(e.item).hasClass('links-message')) {
                    //     getMessage();
                    // }
                    // if ($(e.item).hasClass('links-notice')) {
                    //     getNotice();
                    // }
                },
                dataBound: function () {
                    // globalSearch();
                    // initMessage();
                    // initNotice();
                    
                }
            });
            var w = fullw-($("#aside").width()+60)-$("#menuH").width();
            $("#tab ul.k-tabstrip-items").css({
                "width":w+"px",
            });
        }
    });

    // // 新消息数量
    // getMessage();
    // // 新提醒数量
    // getNotice();
    // // 每半分钟获取一次
    // setInterval(function () {
    //     if ($('#menuH div.k-animation-container[aria-hidden=false]').length === 0) {
    //         getMessage();
    //         getNotice();
    //     }
    // }, 30000);
    // 面包屑导航
    // setTimeout(function () {
    //     showPath(location.hash.split('#')[1].split('/')[location.hash.split('#')[1].split('/').length - 1]);
    //     $(window).resize(function () {
    //         showPath(location.hash.split('#')[1].split('/')[location.hash.split('#')[1].split('/').length - 1]);
    //     }).resize();
    // }, 500);
    // // 刷新接管
    // document.onkeydown = function () {
    //     var e = window.event || arguments[0];
    //     // 屏蔽 F5 和 Ctrl + F5
    //     if (e.keyCode === 116 || ((e.ctrlKey) && (e.keyCode === 116))) {
    //         refresh();
    //         return false;
    //     }
    // };
    // // 全屏
    // $('#header').on('click', '.full-screen', fullScreen);
    // // 回车解锁
    // $('body').on('keyup', '#locking input', function (event) {
    //     if(event.keyCode === 13){
    //         unlockScreen();
    //     }
    // });
    // // 工具箱图标
    // $('body').append('<div id="toolBox"><button class="k-button k-state-selected" id="tools"><i class="fas fa-tools"></i></button></div>');
    // $('#toolBox').hover(
    //     function () {
    //         var spacing;
    //         if (window.outerWidth < 768) {
    //             spacing = 40;
    //         } else {
    //             spacing = 60;
    //         }
    //         $(this).height(($(this).find('button:visible').length - 1) * spacing);
    //         $.each($(this).find('button:not(:first):visible'), function (i, doms) {
    //             $(doms).css('bottom', i * spacing + 'px');
    //         });
    //     },
    //     function () {
    //         if (window.outerWidth < 768) {
    //             $(this).height(40);
    //         } else {
    //             $(this).height(60);
    //         }
    //         $(this).find('button:not(:first)').css('bottom', '0');
    //     }
    // );
    // // 聊天机器人图标
    // $('#toolBox').append('<button class="k-button k-state-selected" id="bot"><label for="botCkb"><i class="fas fa-robot"></i></label></button>');
    // $('body').append('<input id="botCkb" type="checkbox"><label for="botCkb"><span id="botMask"></span></label><div id="botChat"></div>');
    // // 聊天机器人提示
    // tipMsg($('#bot'), '聊天机器人', 'left');
    // // 聊天机器人
    // $('#botChat').kendoChat({
    //     user: { // 用户名称及头像
    //         name: sessionStorage.getItem('userName'),
    //         iconUrl: sessionStorage.getItem('avatar')
    //     },
    //     post: function (e) {
    //         $.ajax({
    //             type: 'post',
    //             data: JSON.stringify({
    //                 perception: {
    //                     inputText: {
    //                         text: e.text
    //                     }
    //                 },
    //                 userInfo: {
    //                     apiKey: '73e3544b57fb47e3a2545ea0b47dc474', // 请替换成自己的 Key
    //                     userId: 'demo'
    //                 }
    //             }),
    //             url: 'http://openapi.tuling123.com/openapi/api/v2',
    //             contentType: 'application/json; charset=UTF-8',
    //             dataType: 'json',
    //             success: function (res) {
    //                 $.each(res.results, function (i, items) {
    //                     if (items.resultType === 'text') { // 文本
    //                         $('#botChat').data('kendoChat').renderMessage(
    //                             {
    //                                 type: 'text',
    //                                 text: res.results[i].values.text
    //                             },
    //                             {
    //                                 id: kendo.guid(),
    //                                 name: '🌸小艾',
    //                                 iconUrl: 'img/temp/Esmerarda.png'
    //                             }
    //                         );
    //                     } else if (items.resultType === 'url') { // 链接
    //                         var urlTemp = kendo.template(
    //                             '<div class="card mt-3">' +
    //                                 '<div class="card-body">' +
    //                                     '<a class="card-link" href="#: url #" target="_blank">#: url #</a>' +
    //                                 '</div>' +
    //                             '</div>'
    //                         );
    //                         kendo.chat.registerTemplate('url_card', urlTemp);
    //                         $('#botChat').data('kendoChat').renderAttachments(
    //                             {
    //                                 attachments: [{
    //                                     contentType: 'url_card',
    //                                     content: {
    //                                         url: res.results[i].values.url
    //                                     }
    //                                 }],
    //                                 attachmentLayout: 'list'
    //                             },
    //                             {
    //                                 id: kendo.guid(),
    //                                 name: '🌸小艾',
    //                                 iconUrl: 'img/temp/Esmerarda.png'
    //                             }
    //                         );
    //                     } else if (items.resultType === 'image') { // 图片
    //                         var imageTemp = kendo.template(
    //                             '<div class="text-center mt-3">' +
    //                                 '<img class="img-thumbnail img-fluid" src="#: image #" alt="">' +
    //                             '</div>'
    //                         );
    //                         kendo.chat.registerTemplate('image_card', imageTemp);
    //                         $('#botChat').data('kendoChat').renderAttachments(
    //                             {
    //                                 attachments: [{
    //                                     contentType: 'image_card',
    //                                     content: {
    //                                         image: res.results[i].values.image
    //                                     }
    //                                 }],
    //                                 attachmentLayout: 'list'
    //                             },
    //                             {
    //                                 id: kendo.guid(),
    //                                 name: '🌸小艾',
    //                                 iconUrl: 'img/temp/Esmerarda.png'
    //                             }
    //                         );
    //                     }
    //                 });
    //             }
    //         });
    //     }
    // }).data('kendoChat').renderMessage( // 初始化对话
    //     {
    //         type: 'text',
    //         text: '亲~ 由于GitHub和码云使用的是HTTPS协议~ 而图灵机器人使用的是HTTP协议~ 所以想看对话效果的亲们请部署到本地开启Chrome跨域模式调试~ 谢谢~ ❤️'
    //     },
    //     {
    //         id: kendo.guid(),
    //         name: '🌸小艾',
    //         iconUrl: 'img/temp/Esmerarda.png'
    //     }
    // );
    // // 天气预报图标
    // $('#toolBox').append('<button class="k-button k-state-selected" id="weather" onclick="getWeather();"><i class="wi wi-na"></i></button>');
    // // 天气预报提示
    // tipMsg($('#weather'), '天气预报', 'left');
    // // 天气预报动态图标
    // $.ajax({
    //     type: 'get',
    //     data: {
    //         version: 'v6',
    //         appid: '97987729', // 请替换成自己的 ID
    //         appsecret: 'f2Wfm53j' // 请替换成自己的 Key
    //     },
    //     url: 'https://www.tianqiapi.com/api/',
    //     dataType: 'json',
    //     success: function (res) {
    //         var weatherImg = res.wea_img,
    //             weatherTime = res.update_time.substr(0, 2);
    //         if (weatherTime >= 6 && weatherTime < 18) {
    //             // 白天
    //             if (weatherImg === 'qing') { // 晴
    //                 $('#weather').html('<i class="fas fa-sun"></i>');
    //             } else if (weatherImg === 'yun') { // 多云
    //                 $('#weather').html('<i class="fas fa-cloud-sun"></i>');
    //             } else if (weatherImg === 'yin') { // 阴
    //                 $('#weather').html('<i class="fas fa-cloud"></i>');
    //             } else if (weatherImg === 'yu') { // 雨
    //                 $('#weather').html('<i class="fas fa-cloud-sun-rain"></i>');
    //             } else if (weatherImg === 'lei') { // 雷
    //                 $('#weather').html('<i class="fas fa-bolt"></i>');
    //             } else if (weatherImg === 'bingbao') { // 冰雹
    //                 $('#weather').html('<i class="fas fa-cloud-meatball"></i>');
    //             } else if (weatherImg === 'xue') { // 雪
    //                 $('#weather').html('<i class="fas fa-snowflake"></i>');
    //             } else if (weatherImg === 'wu') { // 雾
    //                 $('#weather').html('<i class="fas fa-smog"></i>');
    //             } else if (weatherImg === 'shachen') { // 沙尘
    //                 $('#weather').html('<i class="fas fa-wind"></i>');
    //             } else {
    //                 $('#weather').html('<i class="wi wi-na"></i>');
    //             }
    //         } else {
    //             // 黑夜
    //             if (weatherImg === 'qing') { // 晴
    //                 $('#weather').html('<i class="fas fa-moon"></i>');
    //             } else if (weatherImg === 'yun') { // 多云
    //                 $('#weather').html('<i class="fas fa-cloud-moon"></i>');
    //             } else if (weatherImg === 'yin') { // 阴
    //                 $('#weather').html('<i class="fas fa-cloud"></i>');
    //             } else if (weatherImg === 'yu') { // 雨
    //                 $('#weather').html('<i class="fas fa-cloud-moon-rain"></i>');
    //             } else if (weatherImg === 'lei') { // 雷
    //                 $('#weather').html('<i class="fas fa-bolt"></i>');
    //             } else if (weatherImg === 'bingbao') { // 冰雹
    //                 $('#weather').html('<i class="fas fa-cloud-meatball"></i>');
    //             } else if (weatherImg === 'xue') { // 雪
    //                 $('#weather').html('<i class="fas fa-snowflake"></i>');
    //             } else if (weatherImg === 'wu') { // 雾
    //                 $('#weather').html('<i class="fas fa-smog"></i>');
    //             } else if (weatherImg === 'shachen') { // 沙尘
    //                 $('#weather').html('<i class="fas fa-wind"></i>');
    //             } else {
    //                 $('#weather').html('<i class="wi wi-na"></i>');
    //             }
    //         }
    //     },
    //     error: function (res) {
    //         alertMsg('获取天气数据出错！', 'error');
    //     }
    // });
    // // 万年历图标
    // $('#toolBox').append('<button class="k-button k-state-selected" id="lunar" onclick="getLunar();"><i class="fas fa-calendar-alt"></i></button>');
    // // 万年历提示
    // tipMsg($('#lunar'), '万年历', 'left');
    // // 便签图标
    // $('#toolBox').append('<button class="k-button k-state-selected" id="note" onclick="getNote();"><i class="fas fa-sticky-note"></i></button>');
    // // 便签提示
    // tipMsg($('#note'), '便签', 'left');
    // // 回到顶部图标
    // $('#section').append('<button class="k-button k-state-selected" id="goTop"><i class="fas fa-arrow-up"></i></button>').scroll(function () {
    //     if ($(this).scrollTop() > 800) {
    //         $('#goTop').fadeIn();
    //     } else {
    //         $('#goTop').fadeOut();
    //     }
    // });
    // // 回到顶部提示
    // tipMsg($('#goTop'), '回到顶部', 'left');
    // // 回到顶部动画
    // $('#goTop').click(function () {
    //     $('#section').animate({ scrollTop: 0 }, 500);
    // });
    // // 功能开关
    // setTimeout(function () {
    //     if (localStorage.hasOwnProperty('globalSearch')) {
    //         switchGlobalSearch(JSON.parse(localStorage.getItem('globalSearch')));
    //     }
    //     if (localStorage.hasOwnProperty('refresh')) {
    //         switchRefresh(JSON.parse(localStorage.getItem('refresh')));
    //     }
    //     if (localStorage.hasOwnProperty('fullScreen')) {
    //         switchFullScreen(JSON.parse(localStorage.getItem('fullScreen')));
    //     }
    //     if (localStorage.hasOwnProperty('lockScreen')) {
    //         switchLockScreen(JSON.parse(localStorage.getItem('lockScreen')));
    //     }
    //     if (localStorage.hasOwnProperty('theme')) {
    //         switchTheme(JSON.parse(localStorage.getItem('theme')));
    //     }
    //     if (localStorage.hasOwnProperty('localization')) {
    //         switchLocalization(JSON.parse(localStorage.getItem('localization')));
    //     }
    //     if (localStorage.hasOwnProperty('message')) {
    //         switchMessage(JSON.parse(localStorage.getItem('message')));
    //     }
    //     if (localStorage.hasOwnProperty('notice')) {
    //         switchNotice(JSON.parse(localStorage.getItem('notice')));
    //     }
    //     switchSeparator();
    //     if (localStorage.hasOwnProperty('bot')) {
    //         switchBot(JSON.parse(localStorage.getItem('bot')));
    //     }
    //     if (localStorage.hasOwnProperty('weather')) {
    //         switchWeather(JSON.parse(localStorage.getItem('weather')));
    //     }
    //     if (localStorage.hasOwnProperty('lunar')) {
    //         switchLunar(JSON.parse(localStorage.getItem('lunar')));
    //     }
    //     if (localStorage.hasOwnProperty('note')) {
    //         switchNote(JSON.parse(localStorage.getItem('note')));
    //     }
    //     switchTools();
        
    //     ///变动位置

        // $(".k-tabstrip-items").appendTo("#header");
        // var w = document.body.clientWidth-($("#aside").width()+60)-$("#menuH").width();
        // console.log(w);
        // $(".k-tabstrip-items").css({
        //     "z-index":"999",
        //     "left":"160px",
        //     "width":w+"px",
        //     "height":40+"px",
        //     "position":"fixed",
        //     "margin-top":"-40px",
        //     "overflow":"hidden",
        // });

    // }, 500);
});

// // 发送 Token 验证
// function tokenAuth() {
//     $.fn.ajaxPost({
//         ajaxAsync: false,
//         ajaxData: {
//             userId: sessionStorage.getItem('userId')
//         },
//         ajaxUrl: tokenUrl,
//         succeed: function (res) {
//             if (sessionStorage.getItem('locked')) {
//                 lockScreen();
//             }
//         },
//         failed: function (res) {
//             logout();
//         }
//     });
// }

// 面包屑导航
function showPath(hash) {
    // $('#path').html('');
    // $.each($('#navPanelBar, #menuH').find('.links-'+ hash).children('.k-link').parents('.k-item'), function (i, doms) {
    //     $('#path').prepend('<span><i class="fas fa-angle-double-right"></i>' + $(doms).children('.k-link').html() + '</span>').find('sup').removeClass();
    // });
    // if (hash === 'search') {
    //     $('#path').prepend('<span><i class="fas fa-angle-double-right"></i><i class="fas fa-search"></i>搜索结果<small>Search Result</small></span>');
    // }
    // if (hash === '404') {
    //     $('#path').prepend('<span><i class="fas fa-angle-double-right"></i><i class="fas fa-info-circle"></i>404<small>Error</small></span>');
    // }
    // var homePath = '';
    // if ($('iframe').length === 1) {
    //     homePath = 'javascript:linkTo(\'/\', \'home\');';
    // } else {
    //     homePath = webType + '/#/home';
    // }
    // $('#path').prepend('<a href="' + homePath + '"><i class="fas fa-home"></i>首页<span><small>Home</small></span></a>');
    // // 展开导航并定位
    // if ($('#navPanelBar').data('kendoPanelBar')) {
    //     $('#navPanelBar .k-link').removeClass('k-state-selected');
    //     $('#navPanelBar').data('kendoPanelBar').expand($('.links-'+ hash).parents('.k-group').parent());
    //     $('.links-'+ hash).find('a.k-link').addClass('k-state-selected');
    // }
    // // 判断面包屑长度
    // if ($(window).width() > 767) {
    //     $('#menuH').show();
    //     $('#header > label[for="menuCkb"], #menuV').hide();
    //     if ($('#header > label[for="navCkb"]').width() + $('#path').width() + $('#menuH').width() > $('#header').width()) {
    //         $('#header > label[for="menuCkb"], #menuV').show();
    //         $('#menuH').hide();
    //     }
    //     if ($('#header > label[for="navCkb"]').width() + $('#path').width() > $('#header').width() - $('#header > label[for="menuCkb"]').width()) {
    //         $('#path').html('<a href="' + homePath + '"><i class="fas fa-home"></i>首页<span><small>Home</small></span></a>');
    //     }
    // }

    $('#header > label[for="navCkb"], #menuV').click(function(){
        $("#navPanel .nav-box").each(function(){
            var $this=$(this);
            if($this.is(":visible")){
                var nav = document.getElementById('nav');  
                var navWidth = nav.style.width || nav.clientWidth || nav.offsetWidth || nav.scrollWidth; 
                var maiheight = document.documentElement.clientHeight;
                if(maiheight<300) maiheight=600;
                maiheight = maiheight-100;//计算div的高度  
                //$this.attr("style","height:"+maiheight+"px;margin-left:"+(navWidth-100)+"px;"); 
                console.log(navWidth);
                if(navWidth==60){
                    $this.animate({
                        height:maiheight+"px",
                        marginLeft:"0px"  
                    },200); 
                }else{
                    $this.animate({
                        height:maiheight+"px",
                        marginLeft:"-40px"
                    },200); 
                }
            }
        });
    });
}

/**
 * 切换-按钮触发
 */
function toggleTabstripItems(obj){
    var marginLeft=parseFloat($(obj).css('margin-left'));
    if(marginLeft>80){
        $(obj).css({'margin-left':'60px'});
    }else{
        $(obj).css({'margin-left':'100px'});
    }
    var itemsMarginLeft=parseFloat($('#tab').children("ul:first").css('margin-left'));
    if(marginLeft>80){
        $('#tab').children("ul:first").css({'margin-left':(itemsMarginLeft-40)+'px'});
    }else{
        $('#tab').children("ul:first").css({'margin-left':(itemsMarginLeft+40)+'px'});
    }
}

// 全局搜索
function globalSearch() {
    $('.global-search input[type=search]').kendoComboBox({
        dataSource: {
            transport: {
                read: function (options) {
                    $.fn.ajaxPost({
                        ajaxUrl: searchUrl,
                        succeed: function (res) {
                            options.success(res);
                        },
                        failed: function (res) {
                            options.error(res);
                        }
                    });
                }
            },
            schema: {
                data: 'data'
            },
            group: {
                field: 'group',
                dir: 'desc'
            }
        },
        dataValueField: 'value',
        dataTextField: 'text',
        height: 300,
        filter: 'contains',
        delay: 300,
        minLength: 2,
        fixedGroupTemplate: '<div class="text-center theme-m-bg">当前：<i class="fas fa-folder-open mr-2"></i>#: data #</div>',
        groupTemplate: '#: data #',
        template: '<i class="#: icon #"></i>#: text #<small>#: small #</small>',
        footerTemplate: '<div class="p-1 text-center theme-s-bg"><small>-= 已找到<strong class="mx-1">#: instance.dataSource.total() #</strong>项 =-</small></div>',
        dataBound: function (e) {
            if ($(e.sender.wrapper).find('.fa-search').length < 1) {
                $(e.sender.wrapper).find('.k-dropdown-wrap').prepend('<i class="fas fa-search theme-m"></i>');
            }
        },
        change: function (e) {
            if (e.sender._old !== '') {
                if (e.sender._oldIndex !== -1) {
                    linkTo(this.value().split('|')[0], this.value().split('|')[1]);
                } else {
                    linkTo('/', 'search');
                    $('#path').html('<a href="' + webType + '/#/home"><i class="fas fa-home"></i>首页<span><small>Home</small></span></a><span><i class="fas fa-angle-double-right"></i><i class="fas fa-search"></i>搜索结果<small>Search Result</small></span>');
                }
            }
        }
    });
}

function fullScreen(){
    var fullscreenEnabled = document.fullscreenEnabled       ||
                            document.webkitFullscreenEnabled ||
                            document.mozFullScreenEnabled    ||
                            document.msFullscreenEnabled;
    if (fullscreenEnabled) {
        var isFullscreen = document.fullscreenElement        ||
                            document.webkitFullscreenElement  ||
                            document.mozFullScreenElement     ||
                            document.msFullscreenElement;
        if (isFullscreen) {
            exitFullscreen();
            $(this).find('.fa-compress').addClass('fa-expand').removeClass('fa-compress');
        } else {
            enterFullscreen();
            $(this).find('.fa-expand').addClass('fa-compress').removeClass('fa-expand');
        }
    } else {
        alertMsg('当前浏览器不支持全屏！', 'error');
    }
}

// 进入全屏
function enterFullscreen(element) {
    var el = element instanceof HTMLElement ? element : document.documentElement;
    var infs = el.requestFullscreen       ||
               el.webkitRequestFullscreen ||
               el.mozRequestFullScreen    ||
               el.msRequestFullscreen;
    if (infs) {
        infs.call(el);
    } else if (window.ActiveXObject) {
        var ws = new ActiveXObject('WScript.Shell');
        ws && ws.SendKeys('{F11}');
    }
}

// 退出全屏
function exitFullscreen() {
    var outfs = document.exitFullscreen       ||
                document.webkitExitFullscreen ||
                document.mozCancelFullScreen  ||
                document.msExitFullscreen;
    if (outfs) {
        outfs.call(document);
    } else if (window.ActiveXObject) {
        var ws = new ActiveXObject('WScript.Shell');
        ws && ws.SendKeys('{F11}');
    }
}

// 锁屏
function lockScreen() {
    document.onkeydown = function () {
        var e = window.event || arguments[0];
        // 屏蔽 F12
        if (e.keyCode === 123) {
            return false;
        // 屏蔽 Ctrl+Shift+I
        } else if ((e.ctrlKey) && (e.shiftKey) && (e.keyCode === 73)) {
            return false;
        // 屏蔽 Shift+F10
        } else if ((e.shiftKey) && (e.keyCode === 121)) {
            return false;
        }
    };
    // 屏蔽右键单击
    document.oncontextmenu = function () {
        return false;
    };
    $('#locking').remove();
    if (sessionStorage.getItem("avatar")) {
        $('body').append('<div id="locking"><figure onclick="lockInput(this);"><img src="' + sessionStorage.getItem("avatar") + '" alt="' + sessionStorage.getItem("userName") + '"></figure><h3>' + sessionStorage.getItem("userName") + '</h3></div>');
    } else {
        $('body').append('<div id="locking"><figure onclick="logout();"><img src="img/avatar.png" alt="IKKI"></figure><h3>你没有正常登录哦~</h3></div>');
    }
    setTimeout(function () {
        $('#locking').addClass('lock-ani');
    }, 200);
    sessionStorage.setItem('locked', true);
}

// 锁屏输入
function lockInput(dom) {
    $(dom).find('img').unwrap();
    $('#locking').append('<div class="input-group"><input class="form-control form-control-lg" type="password" placeholder="请输入登录密码解锁"><div class="input-group-append" onclick="unlockScreen();"><span class="input-group-text"><i class="fas fa-key"></i></span></div></div>');
    setTimeout(function () {
        $('#locking .input-group').addClass('lock-input-ani');
    }, 200);
    $('#locking input').focus();
}

// 解锁
function unlockScreen() {
    if ($('#locking input').val() === sessionStorage.getItem('password')) {
        $('#locking').fadeOut(300, function () {
            $('#locking').remove();
        });
        sessionStorage.removeItem('locked');
        document.onkeydown = function () {
            var e = window.event || arguments[0];
            // 屏蔽 F12
            if (e.keyCode === 123) {
                return true;
                // 屏蔽 Ctrl+Shift+I
            } else if ((e.ctrlKey) && (e.shiftKey) && (e.keyCode === 73)) {
                return true;
                // 屏蔽 Shift+F10
            } else if ((e.shiftKey) && (e.keyCode === 121)) {
                return true;
            }
        };
        // 屏蔽右键单击
        document.oncontextmenu = function () {
            return true;
        };
    } else {
        noticeMsg('密码错误！请重新输入~', 'error', 'top', 3000);
    }
}

// 配色
function changeColor(color, accent, minor) {
    $('#Amikoko').attr('href', 'css/themes/theme_' + color + '.min.css');   
    if ($('#hasChart').length > 0) {
        setTimeout(function () {
            kendo.dataviz.autoTheme(true);
            refresh();
        }, 300);
    }
    localStorage.setItem('colorName', color);
    localStorage.setItem('accentColor', accent);
    accentColor = accent;
    localStorage.setItem('minorColor', minor);
    minorColor = minor;
}

// 语言
function changeLang(lang) {
    $.getScript('js/global/kendo.' + lang + '.js', function () {
        kendo.culture(lang);
        localStorage.setItem('culture', lang);
        refresh();
    });
}

// // 消息初始化
// function initMessage() {
//     var messageHTML =
//         '<div class="card">' +
//             '<div class="card-header">' +
//                 '<button class="k-button" id="messageDrawerBtn" type="button"><i class="fas fa-indent"></i></button>' +
//                 '<strong>站内信及短信息</strong>' +
//                 '<a href="javascript:linkTo(\'/users/\', \'message\');">更多<i class="fas fa-angle-double-right"></i></a>' +
//             '</div>' +
//             '<div class="card-body">' +
//                 '<div id="messageDrawer">' +
//                     '<div id="messageDrawerContent">' +
//                         '<div class="hide">' +
//                             '<div class="row no-gutters">' +
//                                 '<div class="col-12" id="writeMail">' +
//                                     '<form class="mail-content">' +
//                                         '<p>' +
//                                             '<select class="w-100" id="msgReceiver" name="receiver" multiple required data-required-msg="请选择收件人！"></select>' +
//                                             '<span class="k-invalid-msg" data-for="receiver"></span>' +
//                                         '</p>' +
//                                         '<p>' +
//                                             '<select class="w-100" id="msgCC" name="cc" multiple></select>' +
//                                         '</p>' +
//                                         '<p>' +
//                                             '<input class="k-textbox w-100" name="subject" type="text" placeholder="主题（必填）" required data-required-msg="请输入主题！">' +
//                                         '</p>' +
//                                         '<p>' +
//                                             '<textarea class="k-textarea w-100" name="content" placeholder="正文内容"></textarea>' +
//                                         '</p>' +
//                                         '<div class="btns">' +
//                                             '<button class="k-button k-button-icontext k-state-selected" type="button" onclick="sendMail();"><i class="fas fa-paper-plane"></i>发送</button>' +
//                                         '</div>' +
//                                     '</form>' +
//                                 '</div>' +
//                             '</div>' +
//                         '</div>' +
//                         '<div>' +
//                             '<div class="row no-gutters">' +
//                                 '<div class="col-4" id="inbox"></div>' +
//                                 '<div class="col-8">' +
//                                     '<div class="blank"><i class="fas fa-couch"></i>空空如也</div>' +
//                                 '</div>' +
//                             '</div>' +
//                         '</div>' +
//                         '<div class="hide">' +
//                             '<div class="row no-gutters">' +
//                                 '<div class="col-4" id="outbox"></div>' +
//                                 '<div class="col-8">' +
//                                     '<div class="blank"><i class="fas fa-couch"></i>空空如也</div>' +
//                                 '</div>' +
//                             '</div>' +
//                         '</div>' +
//                         '<div class="hide"></div>' +
//                         '<div class="hide">' +
//                             '<div class="row no-gutters">' +
//                                 '<div class="col-4">' +
//                                     '<div id="smsSearch"><span class="k-textbox k-space-left w-100"><input type="text" placeholder="搜索"><a class="k-icon k-i-search k-required ml-1" href="javascript:;"></a></span></div>' +
//                                     '<div id="sms"></div>' +
//                                 '</div>' +
//                                 '<div class="col-8">' +
//                                     '<div class="blank"><i class="fas fa-couch"></i>空空如也</div>' +
//                                 '</div>' +
//                             '</div>' +
//                         '</div>' +
//                         '<div class="hide"></div>' +
//                         '<div class="hide">' +
//                             '<div class="row no-gutters">' +
//                                 '<div class="col-4">' +
//                                     '<div id="addressBookSearch"><span class="k-textbox k-space-left w-100"><input type="text" placeholder="搜索"><a class="k-icon k-i-search k-required ml-1" href="javascript:;"></a></span></div>' +
//                                     '<div id="addressBook"></div>' +
//                                 '</div>' +
//                                 '<div class="col-8">' +
//                                     '<div class="blank"><i class="fas fa-couch"></i>空空如也</div>' +
//                                 '</div>' +
//                             '</div>' +
//                         '</div>' +
//                     '</div>' +
//                 '</div>' +
//             '</div>' +
//         '</div>';
//     $('#messageBox').html(messageHTML);
//     $('#messageDrawer').kendoDrawer({
//         mode: 'push',
//         template:
//             '<ul>' +
//                 '<li data-role="drawer-item"><i class="fas fa-feather" title="写邮件"></i>写邮件</li>' +
//                 '<li class="k-state-selected" id="inboxDrawer" data-role="drawer-item"><i class="fas fa-inbox" title="收件箱"></i>收件箱</li>' +
//                 '<li data-role="drawer-item"><i class="fas fa-envelope" title="发件箱"></i>发件箱</li>' +
//                 '<li data-role="drawer-separator"></li>' +
//                 '<li id="smsDrawer" data-role="drawer-item"><i class="fas fa-comments" title="短信息"></i>短信息</li>' +
//                 '<li data-role="drawer-separator"></li>' +
//                 '<li data-role="drawer-item"><i class="fas fa-address-book" title="通讯录"></i>通讯录</li>' +
//             '</ul>',
//         mini: {
//             width: 40
//         },
//         width: 120,
//         show: function(e) {
//             $('#messageDrawerBtn i').removeClass('fa-indent').addClass('fa-outdent');
//             $('#messageBox .k-drawer-items sup').fadeOut();
//             $('#messageBox .k-drawer-items .badge').fadeIn();
//         },
//         hide: function(e) {
//             $('#messageDrawerBtn i').removeClass('fa-outdent').addClass('fa-indent');
//             $('#messageBox .k-drawer-items .badge').fadeOut();
//             $('#messageBox .k-drawer-items sup').fadeIn();
//         },
//         itemClick: function (e) {
//             if(!e.item.hasClass('k-drawer-separator')){
//                 $('#messageDrawerContent > div').addClass('hide').eq(e.item.index()).removeClass('hide');
//             }
//         }
//     });
//     $('#messageDrawerBtn').click(function () {
//         if ($('#messageDrawer').data('kendoDrawer').drawerContainer.hasClass('k-drawer-expanded')) {
//             $('#messageDrawer').data('kendoDrawer').hide();
//             $('#messageDrawerBtn i').removeClass('fa-outdent').addClass('fa-indent');
//             $('#messageBox .k-drawer-items .badge').fadeOut();
//             $('#messageBox .k-drawer-items sup').fadeIn();
//         } else {
//             $('#messageDrawer').data('kendoDrawer').show();
//             $('#messageDrawerBtn i').removeClass('fa-indent').addClass('fa-outdent');
//             $('#messageBox .k-drawer-items sup').fadeOut();
//             $('#messageBox .k-drawer-items .badge').fadeIn();
//         }
//     });
//     // 通讯录数据源
//     var addressBookDataSource = new kendo.data.DataSource({
//         transport: {
//             read: function (options) {
//                 $.fn.ajaxPost({
//                     ajaxData: {
//                         type: 'addressBook'
//                     },
//                     ajaxUrl: addressBookUrl,
//                     succeed: function (res) {
//                         options.success(res);
//                     },
//                     failed: function (res) {
//                         options.error(res);
//                     }
//                 });
//             }
//         },
//         schema: {
//             total: function(res) {
//                 return res.addressBook.length;
//             },
//             data: 'addressBook',
//             model: {
//                 id: 'id',
//                 fields: {
//                     avatar: { type: 'string' },
//                     realName: { type: 'string' },
//                     nickName: { type: 'string' },
//                     gender: { type: 'string' },
//                     email: { type: 'string' },
//                     group: { type: 'string' }
//                 }
//             }
//         },
//         group: {
//             field: 'group',
//             dir: 'asc'
//         }
//     });
//     // 收件人
//     $('#msgReceiver').kendoMultiSelect({
//         dataSource: addressBookDataSource,
//         placeholder: '收件人（必填）',
//         dataValueField: 'email',
//         dataTextField: 'nickName',
//         height: 400,
//         autoClose: false,
//         filter: 'contains',
//         delay: 300,
//         itemTemplate: '<img src="#: avatar #" alt="#: nickName #">#: realName #<small>&lt;#: email #&gt;</small>',
//         tagTemplate: '<img src="#: avatar #" alt="#: nickName #">#: realName #'
//     });
//     // 抄送
//     $('#msgCC').kendoMultiSelect({
//         dataSource: addressBookDataSource,
//         placeholder: '抄送',
//         dataValueField: 'email',
//         dataTextField: 'nickName',
//         height: 400,
//         autoClose: false,
//         filter: 'contains',
//         delay: 300,
//         itemTemplate: '<img src="#: avatar #" alt="#: nickName #">#: realName #<small>&lt;#: email #&gt;</small>',
//         tagTemplate: '<img src="#: avatar #" alt="#: nickName #">#: realName #'
//     });
//     // 收件箱列表
//     $('#inbox').kendoListView({
//         dataSource: {
//             transport: {
//                 read: function (options) {
//                     $.fn.ajaxPost({
//                         ajaxData: {
//                             type: 'inbox'
//                         },
//                         ajaxUrl: inboxUrl,
//                         succeed: function (res) {
//                             options.success(res);
//                             if (res.inbox.length === 0) {
//                                 $('#inbox').html('<div class="blank">您的收件箱是空的~</div>');
//                             }
//                         },
//                         failed: function (res) {
//                             options.error(res);
//                         }
//                     });
//                 }
//             },
//             schema: {
//                 total: function(res) {
//                     return res.inbox.length;
//                 },
//                 data: 'inbox',
//                 model: {
//                     id: 'id',
//                     fields: {
//                         avatar: { type: 'string' },
//                         nickName: { type: 'string' },
//                         email: { type: 'string' },
//                         to: { type: 'object',
//                             defaultValue: []
//                         },
//                         cc: { type: 'object',
//                             defaultValue: []
//                         },
//                         subject: { type: 'string' },
//                         content: { type: 'string' },
//                         time: { type: 'string' },
//                         unread: { type: 'boolean' }
//                     }
//                 }
//             },
//             pageSize: 10
//         },
//         height: 600,
//         scrollable: 'endless',
//         selectable: true,
//         template:
//             '<div class="mail-list# if (unread) { # unread# } #">' +
//                 '<h5><img src="#= avatar #" alt="#= email #" title="&lt;#= email #&gt;">#= nickName #</h5>' +
//                 '<p>#= subject #</p>' +
//                 '<time>#= time #</time>' +
//             '</div>',
//         change: function (e) {
//             // 收件箱明细
//             var dataItem = e.sender.dataItem(e.sender.select()),
//                 toList = [dataItem.email],
//                 ccList = [],
//                 content =
//                     '<div class="mail-content">' +
//                         '<h6><strong>' + dataItem.subject + '</strong></h6>' +
//                         '<dl class="row no-gutters">' +
//                             '<dt class="col-2">发件人：</dt>' +
//                             '<dd class="col-10"><img src="' + dataItem.avatar + '" alt="' + dataItem.nickName + '">' + dataItem.nickName + '<small>&lt;' + dataItem.email + '&gt;</small></dd>' +
//                             '<dt class="col-2">收件人：</dt>' +
//                             '<dd class="col-10">';
//             for (var i = 0; i < dataItem.to.length; i++) {
//                 content +=      '<img src="' + dataItem.to[i].avatar + '" alt="' + dataItem.to[i].nickName + '">' + dataItem.to[i].nickName + '<small>&lt;' + dataItem.to[i].email + '&gt;;</small><br>';
//                 toList.push(dataItem.to[i].email);
//             }
//                 content +=  '</dd>';
//             if (dataItem.cc.length > 0) {
//                 content +=  '<dt class="col-2">抄送：</dt>' +
//                             '<dd class="col-10">';
//                 for (var k = 0; k < dataItem.cc.length; k++) {
//                     content +=  '<img src="' + dataItem.cc[k].avatar + '" alt="' + dataItem.cc[k].nickName + '">' + dataItem.cc[k].nickName + '<small>&lt;' + dataItem.cc[k].email + '&gt;;</small><br>';
//                     ccList.push(dataItem.cc[k].email);
//                 }
//                 content +=  '</dd>';
//             }
//                 content +=  '<dt class="col-2">时间：</dt>' +
//                             '<dd class="col-10">' + kendo.toString(kendo.parseDate(dataItem.time), 'yyyy-MM-dd（ddd）HH:mm') + '</dd>' +
//                         '</dl>' +
//                         '<div class="content">' + dataItem.content + '</div>' +
//                         '<div class="btns">' +
//                             '<button class="k-button k-button-icontext k-state-selected" type="button" onclick="postMail(\'reply\', \'' + dataItem.email + '\', \'\', \'' + dataItem.subject + '\', \'' + dataItem.content + '\');"><i class="fas fa-reply"></i>回复</button>' +
//                             '<button class="k-button k-button-icontext theme-m-box" type="button" onclick="postMail(\'replyAll\', \'' + toList + '\', \'' + ccList + '\', \'' + dataItem.subject + '\', \'' + dataItem.content + '\');"><i class="fas fa-reply-all"></i>回复全部</button>' +
//                             '<button class="k-button k-button-icontext theme-s-bg" type="button" onclick="postMail(\'forward\', \'\', \'\', \'' + dataItem.subject + '\', \'' + dataItem.content + '\');"><i class="fas fa-share"></i>转发</button>' +
//                         '</div>' +
//                     '</div>';
//             $('#inbox').next().html(content);
//             // 收件箱已读
//             if ($(e.sender.select()).hasClass('unread')) {
//                 $.fn.ajaxPost({
//                     ajaxData: {
//                         id: dataItem.id,
//                         type: 'inbox'
//                     },
//                     ajaxUrl: mailReadUrl,
//                     succeed: function () {
//                         $(e.sender.select()).removeClass('unread');
//                         var badgeDom = $('#inboxDrawer').find('.badge');
//                         if (badgeDom.text() === '1') {
//                             badgeDom.remove();
//                             $('#inboxDrawer').find('sup').remove();
//                         } else {
//                             badgeDom.text(Number(badgeDom.text()) - 1);
//                         }
//                         getMessageNum();
//                     },
//                     failed: function () {
//                         alertMsg('标记已读出错！', 'error');
//                     }
//                 });
//             }
//         }
//     });
//     // 发件箱列表
//     $('#outbox').kendoListView({
//         dataSource: {
//             transport: {
//                 read: function (options) {
//                     $.fn.ajaxPost({
//                         ajaxData: {
//                             type: 'outbox'
//                         },
//                         ajaxUrl: outboxUrl,
//                         succeed: function (res) {
//                             options.success(res);
//                             if (res.outbox.length === 0) {
//                                 $('#outbox').html('<div class="blank">您的发件箱是空的~</div>');
//                             }
//                         },
//                         failed: function (res) {
//                             options.error(res);
//                         }
//                     });
//                 }
//             },
//             schema: {
//                 total: function(res) {
//                     return res.outbox.length;
//                 },
//                 data: 'outbox',
//                 model: {
//                     id: 'id',
//                     fields: {
//                         avatar: { type: 'string' },
//                         nickName: { type: 'string' },
//                         email: { type: 'string' },
//                         to: { type: 'object',
//                             defaultValue: []
//                         },
//                         cc: { type: 'object',
//                             defaultValue: []
//                         },
//                         subject: { type: 'string' },
//                         content: { type: 'string' },
//                         time: { type: 'string' }
//                     }
//                 }
//             },
//             pageSize: 10
//         },
//         height: 600,
//         scrollable: 'endless',
//         selectable: true,
//         template:
//             '<div class="mail-list unread">' +
//                 '<h5>' +
//                     '# for (var i = 0; i < to.length; i++) { #' +
//                         '<img src="#= to[i].avatar #" alt="#= to[i].email #" title="#= to[i].nickName # &lt;#= to[i].email #&gt;">' +
//                     '# } #' +
//                 '</h5>' +
//                 '<p>#= subject #</p>' +
//                 '<time>#= time #</time>' +
//             '</div>',
//         change: function (e) {
//             // 发件箱明细
//             var dataItem = e.sender.dataItem(e.sender.select()),
//                 toList = [],
//                 ccList = [],
//                 content =
//                     '<div class="mail-content">' +
//                         '<h6><strong>' + dataItem.subject + '</strong></h6>' +
//                         '<dl class="row no-gutters">' +
//                             '<dt class="col-2">发件人：</dt>' +
//                             '<dd class="col-10"><img src="' + dataItem.avatar + '" alt="' + dataItem.nickName + '">' + dataItem.nickName + '<small>&lt;' + dataItem.email + '&gt;</small></dd>' +
//                             '<dt class="col-2">收件人：</dt>' +
//                             '<dd class="col-10">';
//             for (var i = 0; i < dataItem.to.length; i++) {
//                 content +=      '<img src="' + dataItem.to[i].avatar + '" alt="' + dataItem.to[i].nickName + '">' + dataItem.to[i].nickName + '<small>&lt;' + dataItem.to[i].email + '&gt;;</small><br>';
//                 toList.push(dataItem.to[i].email);
//             }
//                 content +=  '</dd>';
//             if (dataItem.cc.length > 0) {
//                 content +=  '<dt class="col-2">抄送：</dt>' +
//                             '<dd class="col-10">';
//                 for (var k = 0; k < dataItem.cc.length; k++) {
//                     content +=  '<img src="' + dataItem.cc[k].avatar + '" alt="' + dataItem.cc[k].nickName + '">' + dataItem.cc[k].nickName + '<small>&lt;' + dataItem.cc[k].email + '&gt;;</small><br>';
//                     ccList.push(dataItem.cc[k].email);
//                 }
//                 content +=  '</dd>';
//             }
//                 content +=  '<dt class="col-2">时间：</dt>' +
//                             '<dd class="col-10">' + kendo.toString(kendo.parseDate(dataItem.time), 'yyyy-MM-dd（ddd）HH:mm') + '</dd>' +
//                         '</dl>' +
//                         '<div class="content">' + dataItem.content + '</div>' +
//                         '<div class="btns">' +
//                             '<button class="k-button k-button-icontext k-state-selected" type="button" onclick="postMail(\'reedit\', \'' + toList + '\', \'' + ccList + '\', \'' + dataItem.subject + '\', \'' + dataItem.content + '\');"><i class="fas fa-edit"></i>再次编辑</button>' +
//                         '</div>' +
//                     '</div>';
//             $('#outbox').next().html(content);
//         }
//     });
//     // 短信息列表
//     $('#sms').kendoListView({
//         dataSource: {
//             transport: {
//                 read: function (options) {
//                     $.fn.ajaxPost({
//                         ajaxData: {
//                             type: 'sms'
//                         },
//                         ajaxUrl: smsUrl,
//                         succeed: function (res) {
//                             options.success(res);
//                             if (res.sms.length === 0) {
//                                 $('#sms').html('<div class="blank">您还没有短信息~</div>');
//                             }
//                         },
//                         failed: function (res) {
//                             options.error(res);
//                         }
//                     });
//                 }
//             },
//             schema: {
//                 total: function(res) {
//                     return res.sms.length;
//                 },
//                 data: 'sms',
//                 model: {
//                     id: 'id',
//                     fields: {
//                         avatar: { type: 'string' },
//                         userId: { type: 'string' },
//                         realName: { type: 'string' },
//                         nickName: { type: 'string' },
//                         chat: { type: 'object',
//                             defaultValue: []
//                         },
//                         unread: { type: 'number' }
//                     }
//                 }
//             }
//         },
//         height: 550,
//         scrollable: 'endless',
//         selectable: true,
//         template:
//             '<div class="sms-list" data-id="#= userId #">' +
//                 '<figure>' +
//                     '# if (unread > 0 && unread < 100) { #' +
//                         '<sup class="theme-m-bg">#= unread #</sup>' +
//                     '# } else if (unread >= 100) { #' +
//                         '<sup class="theme-m-bg font-weight-bold">&middot;&middot;&middot;</sup>' +
//                     '# } #' +
//                     '<img src="#= avatar #" alt="#= nickName #">' +
//                 '</figure>' +
//                 '<div>' +
//                     '<h5>' +
//                         '<strong>#= realName #</strong>' +
//                         '# if (chat.length > 0) { #' +
//                             '<time>' +
//                                 '# if (kendo.toString(kendo.parseDate(chat[chat.length - 1].time), "yyyy-MM-dd") === kendo.toString(kendo.parseDate(new Date()), "yyyy-MM-dd")) { #' +
//                                     '#= kendo.toString(kendo.parseDate(chat[chat.length - 1].time), "HH:mm") #' +
//                                 '# } else { #' +
//                                     '#= kendo.toString(kendo.parseDate(chat[chat.length - 1].time), "MM-dd") #' +
//                                 '# } #' +
//                             '</time>' +
//                         '# } #' +
//                     '</h5>' +
//                     '<p># if (chat.length > 0) { ##= chat[chat.length - 1].text ## } #</p>' +
//                 '</div>' +
//             '</div>',
//         change: function (e) {
//             // 短信息明细
//             var dataItem = e.sender.dataItem(e.sender.select());
//             if ($('#smsChat').data('kendoChat')) {
//                 $('#smsChat .k-message-list-content').html('');
//             } else {
//                 $('#sms').parent().next().html('<div id="smsChat"></div>');
//                 $('#smsChat').kendoChat({
//                     user: {
//                         name: sessionStorage.getItem('userName'),
//                         iconUrl: sessionStorage.getItem('avatar')
//                     },
//                     post: function (e) {
//                         $.fn.ajaxPost({
//                             ajaxData: {
//                                 id: dataItem.id,
//                                 text: e.text
//                             },
//                             ajaxUrl: smsSendUrl,
//                             failed: function () {
//                                 alertMsg('短信息发送失败！', 'error');
//                             }
//                         });
//                     }
//                 });
//             }
//             $.each(dataItem.chat, function (i, items) {
//                 var id,
//                     name,
//                     iconUrl,
//                     userInfo;
//                 if (items.belongTo === 'own') {
//                     userInfo = $('#smsChat').data('kendoChat').getUser();
//                     id = userInfo.id;
//                     name = userInfo.name;
//                     iconUrl = userInfo.iconUrl;
//                 } else if (items.belongTo === 'other') {
//                     id = dataItem.userId;
//                     name = dataItem.realName;
//                     iconUrl = dataItem.avatar;
//                 }
//                 $('#smsChat').data('kendoChat').renderMessage(
//                     {
//                         type: 'text',
//                         text: items.text
//                     },
//                     {
//                         id: id,
//                         name: name,
//                         iconUrl: iconUrl
//                     }
//                 );
//                 $('#smsChat .k-message-list-content > div:last p').hide();
//                 $('#smsChat .k-message-list-content > div:last time:last').text(kendo.toString(kendo.parseDate(items.time), "MM-dd HH:mm"));
//             });
//             // 短信息已读
//             $.fn.ajaxPost({
//                 ajaxData: {
//                     id: dataItem.id,
//                     type: 'sms'
//                 },
//                 ajaxUrl: smsReadUrl,
//                 succeed: function () {
//                     $(e.sender.select()).find('sup').remove();
//                     var badgeDom = $('#smsDrawer').find('.badge');
//                     if ($('#sms sup').length === 0) {
//                         badgeDom.remove();
//                         $('#smsDrawer').find('sup').remove();
//                     } else {
//                         badgeDom.text(Number(badgeDom.text()) - dataItem.unread);
//                     }
//                     getMessageNum();
//                 },
//                 failed: function () {
//                     alertMsg('标记已读出错！', 'error');
//                 }
//             });
//         }
//     });
//     // 短信息搜索
//     $('#smsSearch input').keyup(function () {
//         $('#sms').data('kendoListView').dataSource.filter({
//             logic: 'or',
//             filters: [
//                 { field: 'realName', operator: 'contains', value: $(this).val() },
//                 { field: 'nickName', operator: 'contains', value: $(this).val() }
//             ]
//         });
//     });
//     // 通讯录列表
//     $('#addressBook').kendoListView({
//         dataSource: addressBookDataSource,
//         height: 550,
//         scrollable: 'endless',
//         template: function (e) {
//             var group = '<div class="address-book-list">' +
//                             '<h4>' + e.value + '</h4>';
//             for (var i = 0; i < e.items.length; i++) {
//                 group +=    '<h5 data-id="' + e.items[i].id + '" onclick="addressBookInfo(this, \'' + e.items[i].gender + '\', \'' + e.items[i].email + '\');"><img src="' + e.items[i].avatar + '" alt="' + e.items[i].nickName + '">' + e.items[i].realName + '</h5>';
//             }
//                 group += '</div>';
//             return group;
//         }
//     });
//     // 通讯录搜索
//     $('#addressBookSearch input').keyup(function () {
//         $('#addressBook').data('kendoListView').dataSource.filter({
//             logic: 'or',
//             filters: [
//                 { field: 'realName', operator: 'contains', value: $(this).val() },
//                 { field: 'nickName', operator: 'contains', value: $(this).val() },
//                 { field: 'email', operator: 'contains', value: $(this).val() },
//                 { field: 'group', operator: 'contains', value: $(this).val() }
//             ]
//         });
//     });
// }

// // 消息获取
// function getMessage() {
//     $.fn.ajaxPost({
//         ajaxUrl: messageUrl,
//         succeed: function (res) {
//             $('#menuH, #menuV').find('.links-message > .k-link sup').remove();
//             $('#menuH, #menuV').find('.links-message .k-drawer-items sup').remove();
//             $('#menuH, #menuV').find('.links-message .k-drawer-items .badge').remove();
//             var total = res.inboxTotal + res.smsTotal;
//             if (total > 0 && total < 100) {
//                 $('#menuH, #menuV').find('.links-message > .k-link .fa-envelope').after('<sup class="theme-m-bg">' + total + '</sup>');
//             } else if (total >= 100) {
//                 $('#menuH, #menuV').find('.links-message > .k-link .fa-envelope').after('<sup class="theme-m-bg font-weight-bold">&middot;&middot;&middot;</sup>');
//             }
//             if (res.inboxTotal > 0) {
//                 $('#inboxDrawer').append('<span class="badge theme-s-bg">' + res.inboxTotal + '</span><sup></sup>');
//             }
//             if (res.smsTotal > 0) {
//                 $('#smsDrawer').append('<span class="badge theme-s-bg">' + res.smsTotal + '</span><sup></sup>');
//             }
//         }
//     });
// }

// // 新消息数量计算
// function getMessageNum() {
//     var messageNum = 0;
//     $.each($('#messageDrawer').find('.k-drawer-items .badge'), function () {
//         messageNum += Number($(this).text());
//     });
//     if ($('#menuH, #menuV').find('.links-message > .k-link sup').length === 0) {
//         $('#menuH, #menuV').find('.links-message > .k-link .fa-envelope').after('<sup class="theme-m-bg"></sup>');
//     }
//     if (messageNum > 0 && messageNum < 100) {
//         $('#menuH, #menuV').find('.links-message > .k-link sup').removeClass('font-weight-bold').text(messageNum);
//     } else if (messageNum >= 100) {
//         $('#menuH, #menuV').find('.links-message > .k-link sup').addClass('font-weight-bold').text('&middot;&middot;&middot;');
//     } else {
//         $('#menuH, #menuV').find('.links-message > .k-link sup').remove();
//     }
// }

// // 发送站内信
// function sendMail() {
//     if ($('#writeMail form').kendoValidator().data('kendoValidator').validate()) {
//         $.fn.ajaxPost({
//             ajaxData: $('#writeMail form').serializeObject(),
//             ajaxUrl: mailSendUrl,
//             succeed: function (res) {
//                 $('#writeMail form')[0].reset();
//             },
//             isMsg: true
//         });
//     }
// }

// // 发、回复和转发站内信
// function postMail(type, toList, ccList, subject, content) {
//     $('#msgReceiver').data('kendoMultiSelect').value(toList.split(','));
//     $('#msgCC').data('kendoMultiSelect').value(ccList.split(','));
//     if (type === 'reply' || type === 'replyAll') {
//         $('#writeMail input[name=subject]').val('回复：' + subject);
//     } else if (type === 'forward') {
//         $('#writeMail input[name=subject]').val('转发：' + subject);
//     } else {
//         $('#writeMail input[name=subject]').val(subject);
//     }
//     if (type === 'reedit' || type === 'newPost') {
//         $('#writeMail textarea[name=content]').val(content.replace(/<br>/g, '\n'));
//     } else {
//         $('#writeMail textarea[name=content]').val('\n------------------------------------------------------------\n' + content.replace(/<br>/g, '\n'));
//     }
//     $('#messageDrawer .k-drawer-item').removeClass('k-state-selected').eq(0).addClass('k-state-selected');
//     $('#messageDrawerContent > div').addClass('hide').eq(0).removeClass('hide');
// }

// // 发短信息
// function postSms(userId, realName) {
//     $('#messageDrawer .k-drawer-item').removeClass('k-state-selected').eq(4).addClass('k-state-selected');
//     $('#messageDrawerContent > div').addClass('hide').eq(4).removeClass('hide');
//     $('#smsSearch input').val(realName).trigger('keyup');
//     $('#sms').data('kendoListView').select($('#sms .sms-list[data-id=' + userId + ']'));
//     $('#smsChat .k-message-box input').focus();
// }

// // 通讯录明细
// function addressBookInfo(dom, gender, email) {
//     $('#addressBook h5').removeClass('k-state-selected');
//     $(dom).addClass('k-state-selected');
//     var content =
//         '<div class="address-book-content">' +
//             '<figure style="background-image: url(' + $(dom).find('img').attr('src') + ');"></figure>' +
//             '<img src="' + $(dom).find('img').attr('src') + '" alt="' + $(dom).find('img').attr('alt') + '">' +
//             '<h5>' +
//                 $(dom).text();
//     if (gender === '1') {
//         content += '<i class="fas fa-male mars"></i>';
//     } else if (gender === '2') {
//         content += '<i class="fas fa-female venus"></i>';
//     }
//         content += '</h5>' +
//             '<h6>' + $(dom).find('img').attr('alt') + '</h6>' +
//             '<p>' + email + '</p>' +
//             '<div class="btns">' +
//                 '<button class="k-button k-button-icontext k-state-selected" type="button" onclick="postMail(\'newPost\', \'' + email + '\', \'\', \'\', \'\');"><i class="fas fa-envelope"></i>发站内信</button>' +
//                 '<button class="k-button k-button-icontext k-state-selected" type="button" onclick="postSms(\'' + $(dom).attr('data-id') + '\', \'' + $(dom).text() + '\');"><i class="fas fa-comments"></i>发短信息</button>' +
//             '</div>' +
//         '</div>';
//     $('#addressBook').parent().next().html(content);
// }

// // 提醒初始化
// function initNotice() {
//     var noticeHTML =
//         '<div id="noticeTabStrip">' +
//             '<ul>' +
//                 '<li id="notificationTab"><i class="fas fa-volume-up"></i>通知</li>' +
//                 '<li id="updatingTab"><i class="fas fa-user-clock"></i>动态</li>' +
//                 '<li id="toDoTab"><i class="fas fa-calendar-check"></i>待办</li>' +
//             '</ul>' +
//             '<div>' +
//                 '<div id="systemNotification"></div>' +
//                 '<div class="notice-tools">' +
//                     '<a href="javascript:linkTo(\'/users/\', \'notice\');"><i class="fas fa-history"></i>查看历史</a>' +
//                     '<a href="javascript:noticeReadAll(\'systemNotification\', \'notificationTab\');"><i class="fas fa-eye"></i>全部已读</a>' +
//                 '</div>' +
//             '</div>' +
//             '<div>' +
//                 '<div id="userUpdating"></div>' +
//                 '<div class="notice-tools">' +
//                     '<a href="javascript:linkTo(\'/users/\', \'notice\');"><i class="fas fa-history"></i>查看历史</a>' +
//                     '<a href="javascript:noticeReadAll(\'userUpdating\', \'updatingTab\');"><i class="fas fa-eye"></i>全部已读</a>' +
//                 '</div>' +
//             '</div>' +
//             '<div>' +
//                 '<div id="toDoItems"></div>' +
//                 '<div class="notice-tools">' +
//                     '<a href="javascript:linkTo(\'/users/\', \'notice\');"><i class="fas fa-history"></i>查看历史</a>' +
//                     '<a href="javascript:noticeReadAll(\'toDoItems\', \'toDoTab\');"><i class="fas fa-eye"></i>全部已读</a>' +
//                 '</div>' +
//             '</div>' +
//         '</div>';
//     $('#noticeBox').html(noticeHTML);
//     // $('#noticeTabStrip').kendoTabStrip({
//     //     animation: false,
//     //     select: function (e) {
//     //         var noticeType = $(e.contentElement).find('div').first().attr('id');
//     //         if (noticeType === 'systemNotification') {
//     //             getSystemNotification();
//     //         } else if (noticeType === 'userUpdating') {
//     //             getUserUpdating();
//     //         } else if (noticeType === 'toDoItems') {
//     //             getToDoItems();
//     //         }
//     //     }
//     // }).data('kendoTabStrip').select(0);
// }

// // 提醒获取
// function getNotice() {
//     $.fn.ajaxPost({
//         ajaxUrl: noticeUrl,
//         succeed: function (res) {
//             $('#menuH, #menuV').find('.links-notice sup').remove();
//             $('#noticeTabStrip').find('.k-tabstrip-items .badge').remove();
//             var total = res.systemNotificationTotal + res.userUpdatingTotal + res.toDoItemsTotal;
//             if (total > 0 && total < 100) {
//                 $('#menuH, #menuV').find('.links-notice > .k-link .fa-bell').after('<sup class="theme-m-bg">' + total + '</sup>');
//             } else if (total >= 100) {
//                 $('#menuH, #menuV').find('.links-notice > .k-link .fa-bell').after('<sup class="theme-m-bg font-weight-bold">&middot;&middot;&middot;</sup>');
//             }
//             if (res.systemNotificationTotal > 0) {
//                 $('#notificationTab > .k-link').append('<span class="badge theme-s-bg">' + res.systemNotificationTotal + '</span>');
//             }
//             if (res.userUpdatingTotal > 0) {
//                 $('#updatingTab > .k-link').append('<span class="badge theme-s-bg">' + res.userUpdatingTotal + '</span>');
//             }
//             if (res.toDoItemsTotal > 0) {
//                 $('#toDoTab > .k-link').append('<span class="badge theme-s-bg">' + res.toDoItemsTotal + '</span>');
//             }
//         }
//     });
// }

// // 新提醒数量计算
// function getNoticeNum() {
//     var noticeNum = 0;
//     $.each($('#noticeTabStrip').find('.k-tabstrip-items .badge'), function () {
//         noticeNum += Number($(this).text());
//     });
//     if ($('#menuH, #menuV').find('.links-notice sup').length === 0) {
//         $('#menuH, #menuV').find('.links-notice > .k-link .fa-bell').after('<sup class="theme-m-bg"></sup>');
//     }
//     if (noticeNum > 0 && noticeNum < 100) {
//         $('#menuH, #menuV').find('.links-notice sup').removeClass('font-weight-bold').text(noticeNum);
//     } else if (noticeNum >= 100) {
//         $('#menuH, #menuV').find('.links-notice sup').addClass('font-weight-bold').text('&middot;&middot;&middot;');
//     } else {
//         $('#menuH, #menuV').find('.links-notice sup').remove();
//     }
// }

// // 系统通知获取
// function getSystemNotification() {
//     if ($('#systemNotification').data('kendoListView')) {
//         $('#systemNotification').data('kendoListView').destroy();
//     }
//     $('#systemNotification').kendoListView({
//         dataSource: {
//             transport: {
//                 read: function (options) {
//                     $.fn.ajaxPost({
//                         ajaxData: {
//                             type: 'systemNotification'
//                         },
//                         ajaxUrl: systemNotificationUrl,
//                         succeed: function (res) {
//                             $('#notificationTab').find('.badge').remove();
//                             options.success(res);
//                             if (res.systemNotification.length > 0) {
//                                 $('#notificationTab > .k-link').append('<span class="badge theme-s-bg">' + res.systemNotification.length + '</span>');
//                                 getNoticeNum();
//                             } else {
//                                 $('#systemNotification').html('<div class="blank">暂时没有新的系统通知~</div>');
//                             }
//                         },
//                         failed: function (res) {
//                             options.error(res);
//                         }
//                     });
//                 }
//             },
//             schema: {
//                 total: function(res) {
//                     return res.systemNotification.length;
//                 },
//                 data: 'systemNotification',
//                 model: {
//                     id: 'id',
//                     fields: {
//                         avatar: { type: 'string' },
//                         title: { type: 'string' },
//                         content: { type: 'string' },
//                         time: { type: 'string' },
//                         unread: { type: 'boolean' }
//                     }
//                 }
//             },
//             pageSize: 6
//         },
//         height: 500,
//         scrollable: 'endless',
//         selectable: true,
//         template:
//             '<div class="media">' +
//                 '<figure class="theme-m-bg">' +
//                     '<i class="fab fa-#= avatar #"></i>' +
//                 '</figure>' +
//                 '<div class="media-body# if (unread) { # unread# } #">' +
//                     '<h5>#= title #</h5>' +
//                     '<p>#= content #</p>' +
//                     '<time>#= time #</time>' +
//                 '</div>' +
//             '</div>',
//         change: function (e) {
//             // 系统通知已读
//             if ($(e.sender.select()).find('.media-body').hasClass('unread')) {
//                 $.fn.ajaxPost({
//                     ajaxData: {
//                         id: e.sender.dataItem(e.sender.select()).id,
//                         type: 'systemNotification'
//                     },
//                     ajaxUrl: noticeReadUrl,
//                     succeed: function () {
//                         $(e.sender.select()).find('.media-body').removeClass('unread').find('.theme-m').removeClass('theme-m');
//                         var badgeDom = $('#notificationTab').find('.badge');
//                         if (badgeDom.text() === '1') {
//                             badgeDom.remove();
//                         } else {
//                             badgeDom.text(Number(badgeDom.text()) - 1);
//                         }
//                         getNoticeNum();
//                     },
//                     failed: function () {
//                         alertMsg('标记已读出错！', 'error');
//                     }
//                 });
//             }
//         }
//     });
// }

// // 个人动态获取
// function getUserUpdating() {
//     if ($('#userUpdating').data('kendoListView')) {
//         $('#userUpdating').data('kendoListView').destroy();
//     }
//     $('#userUpdating').kendoListView({
//         dataSource: {
//             transport: {
//                 read: function (options) {
//                     $.fn.ajaxPost({
//                         ajaxData: {
//                             type: 'userUpdating'
//                         },
//                         ajaxUrl: userUpdatingUrl,
//                         succeed: function (res) {
//                             $('#updatingTab').find('.badge').remove();
//                             options.success(res);
//                             if (res.userUpdating.length > 0) {
//                                 $('#updatingTab > .k-link').append('<span class="badge theme-s-bg">' + res.userUpdating.length + '</span>');
//                                 getNoticeNum();
//                             } else {
//                                 $('#userUpdating').html('<div class="blank">暂时没有新的个人动态~</div>');
//                             }
//                         },
//                         failed: function (res) {
//                             options.error(res);
//                         }
//                     });
//                 }
//             },
//             schema: {
//                 total: function(res) {
//                     return res.userUpdating.length;
//                 },
//                 data: 'userUpdating',
//                 model: {
//                     id: 'id',
//                     fields: {
//                         avatar: { type: 'string' },
//                         nickName: { type: 'string' },
//                         title: { type: 'string' },
//                         content: { type: 'string' },
//                         time: { type: 'string' },
//                         unread: { type: 'boolean' }
//                     }
//                 }
//             },
//             pageSize: 6
//         },
//         height: 500,
//         scrollable: 'endless',
//         selectable: true,
//         template:
//             '<div class="media">' +
//                 '<img src="#= avatar #" alt="#= nickName #">' +
//                 '<div class="media-body# if (unread) { # unread# } #">' +
//                     '<h5>#= title #</h5>' +
//                     '<p>#= content #</p>' +
//                     '<time>#= time #</time>' +
//                 '</div>' +
//             '</div>',
//         change: function (e) {
//             // 个人动态已读
//             if ($(e.sender.select()).find('.media-body').hasClass('unread')) {
//                 $.fn.ajaxPost({
//                     ajaxData: {
//                         id: e.sender.dataItem(e.sender.select()).id,
//                         type: 'userUpdating'
//                     },
//                     ajaxUrl: noticeReadUrl,
//                     succeed: function () {
//                         $(e.sender.select()).find('.media-body').removeClass('unread').find('.theme-m').removeClass('theme-m');
//                         var badgeDom = $('#updatingTab').find('.badge');
//                         if (badgeDom.text() === '1') {
//                             badgeDom.remove();
//                         } else {
//                             badgeDom.text(Number(badgeDom.text()) - 1);
//                         }
//                         getNoticeNum();
//                     },
//                     failed: function () {
//                         alertMsg('标记已读出错！', 'error');
//                     }
//                 });
//             }
//         }
//     });
// }

// // 待办事项获取
// function getToDoItems() {
//     if ($('#toDoItems').data('kendoListView')) {
//         $('#toDoItems').data('kendoListView').destroy();
//     }
//     $('#toDoItems').kendoListView({
//         dataSource: {
//             transport: {
//                 read: function (options) {
//                     $.fn.ajaxPost({
//                         ajaxData: {
//                             type: 'toDoItems'
//                         },
//                         ajaxUrl: toDoItemsUrl,
//                         succeed: function (res) {
//                             $('#toDoTab').find('.badge').remove();
//                             options.success(res);
//                             if (res.toDoItems.length > 0) {
//                                 $('#toDoTab > .k-link').append('<span class="badge theme-s-bg">' + res.toDoItems.length + '</span>');
//                                 getNoticeNum();
//                             } else {
//                                 $('#toDoItems').html('<div class="blank">暂时没有新的待办事项~</div>');
//                             }
//                         },
//                         failed: function (res) {
//                             options.error(res);
//                         }
//                     });
//                 }
//             },
//             schema: {
//                 total: function(res) {
//                     return res.toDoItems.length;
//                 },
//                 data: 'toDoItems',
//                 model: {
//                     id: 'id',
//                     fields: {
//                         state: { type: 'string' },
//                         stateType: { type: 'string' },
//                         title: { type: 'string' },
//                         content: { type: 'string' },
//                         time: { type: 'string' },
//                         unread: { type: 'boolean' }
//                     }
//                 }
//             },
//             pageSize: 6
//         },
//         height: 500,
//         scrollable: 'endless',
//         selectable: true,
//         template:
//             '<div class="media">' +
//                 '<div class="media-body# if (unread) { # unread# } #">' +
//                     '<h5><em class="k-notification-# if (stateType === \'1\') { #success# } else if (stateType === \'2\') { #info# } else if (stateType === \'3\') { #warning# } else if (stateType === \'4\') { #error# } else { #normal# } #">#= state #</em>#= title #</h5>' +
//                     '<p>#= content #</p>' +
//                     '<time>#= time #</time>' +
//                 '</div>' +
//             '</div>',
//         change: function (e) {
//             // 待办事项已读
//             if ($(e.sender.select()).find('.media-body').hasClass('unread')) {
//                 $.fn.ajaxPost({
//                     ajaxData: {
//                         id: e.sender.dataItem(e.sender.select()).id,
//                         type: 'toDoItems'
//                     },
//                     ajaxUrl: noticeReadUrl,
//                     succeed: function () {
//                         $(e.sender.select()).find('.media-body').removeClass('unread').find('.theme-m').removeClass('theme-m');
//                         var badgeDom = $('#toDoTab').find('.badge');
//                         if (badgeDom.text() === '1') {
//                             badgeDom.remove();
//                         } else {
//                             badgeDom.text(Number(badgeDom.text()) - 1);
//                         }
//                         getNoticeNum();
//                     },
//                     failed: function () {
//                         alertMsg('标记已读出错！', 'error');
//                     }
//                 });
//             }
//         }
//     });
// }

// // 提醒全部已读
// function noticeReadAll(type, tab) {
//     $.fn.ajaxPost({
//         ajaxData: {
//             type: type
//         },
//         ajaxUrl: noticeReadAllUrl,
//         succeed: function () {
//             $('#' + type).find('.media-body').removeClass('unread').find('.theme-m').removeClass('theme-m');
//             $('#' + tab).find('.badge').remove();
//             getNoticeNum();
//         },
//         failed: function () {
//             alertMsg('标记全部已读出错！', 'error');
//         }
//     });
// }

// // 修改密码
// function changePassword() {
//     var changePasswordHtml =
//             '<form>' +
//                 '<input name="userId" type="hidden">' +
//                 '<div class="form-group">' +
//                     '<div class="input-group">' +
//                         '<div class="input-group-prepend">' +
//                             '<span class="input-group-text"><i class="fas fa-key"></i></span>' +
//                         '</div>' +
//                         '<input class="form-control" name="oldPassword" type="password" placeholder="旧密码" required data-required-msg="请输入旧密码！" pattern="[A-Za-z0-9]{6,20}" data-pattern-msg="请输入6-20个大小写字母或数字！">' +
//                         '<span class="k-invalid-msg" data-for="oldPassword"></span>' +
//                     '</div>' +
//                 '</div>' +
//                 '<div class="form-group">' +
//                     '<div class="input-group">' +
//                         '<div class="input-group-prepend">' +
//                             '<span class="input-group-text"><i class="fas fa-key"></i></span>' +
//                         '</div>' +
//                         '<input class="form-control" name="newPassword" type="password" placeholder="新密码" required data-required-msg="请输入新密码！" pattern="[A-Za-z0-9]{6,20}" data-pattern-msg="请输入6-20个大小写字母或数字！">' +
//                         '<div class="input-group-append">' +
//                             '<button class="btn input-group-text" id="showNewPass" type="button"><i class="fas fa-eye-slash"></i></button>' +
//                         '</div>' +
//                         '<span class="k-invalid-msg" data-for="newPassword"></span>' +
//                     '</div>' +
//                     '<div id="newPassStrength"></div>' +
//                 '</div>' +
//                 '<div class="form-group">' +
//                     '<div class="input-group">' +
//                         '<div class="input-group-prepend">' +
//                             '<span class="input-group-text"><i class="fas fa-key"></i></span>' +
//                         '</div>' +
//                         '<input class="form-control" name="confirmPassword" type="password" placeholder="确认密码" required data-required-msg="请输入确认密码！">' +
//                         '<span class="k-invalid-msg" data-for="confirmPassword"></span>' +
//                     '</div>' +
//                 '</div>' +
//             '</form>',
//         confirmDialog = $('<div class="dialog-box" id="changePasswordBox"></div>').kendoDialog({
//             animation: {open: {effects: 'fade:in'}, close: {effects: 'fade:out'}},
//             closable: false,
//             width: '100%',
//             maxWidth: 360,
//             title: '修改密码',
//             content: changePasswordHtml,
//             actions: [
//                 {
//                     text: '确定',
//                     primary: true,
//                     action: function (e) {
//                         var validatorChangePass = $('#changePasswordBox form').kendoValidator({
//                             rules: {
//                                 // 区分密码
//                                 noMatchPassword: function (input) {
//                                     if (!input.is('#changePasswordBox input[name="newPassword"]')) {
//                                         return true;
//                                     }
//                                     return (input.val() !== $('#changePasswordBox input[name="oldPassword"]').val());
//                                 },
//                                 // 匹配密码
//                                 matchPassword: function (input) {
//                                     if (!input.is('#changePasswordBox input[name="confirmPassword"]')) {
//                                         return true;
//                                     }
//                                     return (input.val() === $('#changePasswordBox input[name="newPassword"]').val());
//                                 }
//                             },
//                             messages: {
//                                 noMatchPassword: '新旧密码不能相同！',
//                                 matchPassword: '两次输入的密码不一致！'
//                             }
//                         }).data('kendoValidator');
//                         if (validatorChangePass.validate()) {
//                             $.fn.ajaxPost({
//                                 ajaxData: $('#changePasswordBox form').serializeObject(),
//                                 succeed: function() {
//                                     $('#changePasswordBox').data('kendoDialog').close();
//                                 },
//                                 isMsg: true
//                             });
//                         }
//                         return false;
//                     }
//                 },
//                 {
//                     text: '取消',
//                     action: function (e) {
//                         confirmDialog.close();
//                     }
//                 }
//             ],
//             open: function () {
//                 // 获取用户 ID
//                 $('#changePasswordBox input[name="userId"]').val(sessionStorage.getItem('userId'));
//                 // 显示密码
//                 $('#showNewPass').unbind('click').click(function () {
//                     if ($(this).find('.fa-eye-slash').length === 1) {
//                         $('#changePasswordBox input[name="oldPassword"], #changePasswordBox input[name="newPassword"], #changePasswordBox input[name="confirmPassword"]').attr('type', 'text');
//                         $('#showNewPass i').removeClass('fa-eye-slash').addClass('fa-eye');
//                     } else {
//                         $('#changePasswordBox input[name="oldPassword"], #changePasswordBox input[name="newPassword"], #changePasswordBox input[name="confirmPassword"]').attr('type', 'password');
//                         $('#showNewPass i').removeClass('fa-eye').addClass('fa-eye-slash');
//                     }
//                 });
//                 // 密码强度
//                 var newPassProgress = $('#newPassStrength').kendoProgressBar({
//                     animation: {
//                         duration: 200
//                     },
//                     min: 5,
//                     max: 16,
//                     change: function (e) {
//                         if (e.value < 6) {
//                             this.progressStatus.text('密码强度');
//                         } else if (e.value <= 7) {
//                             this.progressStatus.text('弱');
//                             this.progressWrapper.removeClass('bg-danger bg-warning bg-success bg-info').addClass('bg-danger');
//                         } else if (e.value <= 9) {
//                             this.progressStatus.text('中');
//                             this.progressWrapper.removeClass('bg-danger bg-warning bg-success bg-info').addClass('bg-warning');
//                         } else if (e.value <= 12) {
//                             this.progressStatus.text('强');
//                             this.progressWrapper.removeClass('bg-danger bg-warning bg-success bg-info').addClass('bg-success');
//                         } else {
//                             this.progressStatus.text('超强');
//                             this.progressWrapper.removeClass('bg-danger bg-warning bg-success bg-info').addClass('bg-info');
//                         }
//                     }
//                 }).data('kendoProgressBar');
//                 newPassProgress.progressStatus.text('密码强度');
//                 $('#changePasswordBox input[name="newPassword"]').keyup(function () {
//                     newPassProgress.value(this.value.length);
//                 });
//             },
//             close: function () {
//                 confirmDialog.destroy();
//             }
//         }).data('kendoDialog');
//     confirmDialog.open();
// }

// 退出登录
function logout() {
    sessionStorage.clear();
    if (location.href.indexOf('gitee') !== -1) {
        location.href = path + webType + '/login_gitee.html';
    } else {
        location.href = path +  'login/login.html';
    }
}

// // 天气预报弹窗
// function getWeather() {
//     var divWindow = $('<div class="window-box" id="weatherBox"></div>').kendoWindow({
//         animation: {open: {effects: 'fade:in'}, close: {effects: 'fade:out'}},
//         title: '天气预报',
//         width: 360,
//         modal: true,
//         pinned: true,
//         resizable: false,
//         open: function () {
//             // 获取定位信息
//             if (navigator.geolocation) {
//                 navigator.geolocation.getCurrentPosition(
//                     function (position) {
//                         getWeatherInfo(position.coords.longitude + ',' + position.coords.latitude); // 经纬度定位
//                     },
//                     function (positionError) {
//                         getWeatherInfo('auto_ip'); // 自动 IP 定位
//                     },
//                     {
//                         enableHighAcuracy: true,
//                         maximumAge: 86400000,
//                         timeout: 3000
//                     }
//                 );
//             }
//         },
//         close: function () {
//             divWindow.destroy();
//         }
//     }).data('kendoWindow'),
//         weatherHtml =
//             '<div class="card">' +
//                 '<div class="card-header"><i class="fas fa-map-marker-alt theme-m"></i><span class="loc"><span class="skeleton"></span></div>' +
//                 '<div class="card-body">' +
//                     '<time><span class="skeleton"></span></time>' +
//                     '<div class="d-flex">' +
//                         '<span class="tmp theme-m"><span class="skeleton skeleton-round"></span></span>' +
//                         '<i class="wi wi-celsius theme-m"></i>' +
//                         '<span class="cond-txt"><span class="skeleton"></span></span>' +
//                         '<i class="cond-code wi wi-na theme-s"></i>' +
//                     '</div>' +
//                     '<hr>' +
//                     '<div class="row">' +
//                         '<div class="col-12 air"><span class="skeleton"></span></div>' +
//                     '</div>' +
//                     '<div class="row">' +
//                         '<div class="col-6 tmp-m"><i class="wi wi-thermometer theme-m"></i><span class="skeleton"></span></div>' +
//                         '<div class="col-6 wind"><i class="wi wi-strong-wind theme-m"></i><span class="skeleton"></span></div>' +
//                     '</div>' +
//                     '<div class="row">' +
//                         '<div class="col-6 hum"><i class="wi wi-humidity theme-m"></i><span class="skeleton"></span></div>' +
//                         '<div class="col-6 uv-index"><i class="wi wi-umbrella theme-m"></i><span class="skeleton"></span></div>' +
//                     '</div>' +
//                     '<div class="row">' +
//                         '<div class="col-6 sr"><i class="wi wi-sunrise theme-m"></i><span class="skeleton"></span></div>' +
//                         '<div class="col-6 ss"><i class="wi wi-sunset theme-m"></i><span class="skeleton"></span></div>' +
//                     '</div>' +
//                     '<div class="row">' +
//                         '<div class="col-6 mr"><i class="wi wi-moonrise theme-m"></i><span class="skeleton"></span></div>' +
//                         '<div class="col-6 ms"><i class="wi wi-moonset theme-m"></i><span class="skeleton"></span></div>' +
//                     '</div>' +
//                     '<div id="weatherChart"><div class="px-3"><span class="skeleton"></span></div></div>' +
//                 '</div>' +
//                 '<div class="card-footer">' +
//                     '<div class="row">' +
//                         '<div class="col-4 today"><span>今天</span><span class="skeleton"></span></div>' +
//                         '<div class="col-4 tomorrow"><span>明天</span><span class="skeleton"></span></div>' +
//                         '<div class="col-4 after-tomorrow"><span>后天</span><span class="skeleton"></span></div>' +
//                     '</div>' +
//                 '</div>' +
//             '</div>';
//     divWindow.content(weatherHtml).center().open();
// }

// // 天气预报信息
// function getWeatherInfo(location) {
//     $.ajax({
//         type: 'get',
//         data: {
//             key: 'd2ae781d61744d65a2ef2156eef2cb64', // 请替换成自己的 Key
//             location: location
//         },
//         url: 'https://free-api.heweather.net/s6/weather',
//         dataType: 'json',
//         success: function (res) {
//             var basic = res.HeWeather6[0].basic, // 基础信息
//                 now = res.HeWeather6[0].now, // 实况天气
//                 daily_forecast = res.HeWeather6[0].daily_forecast, // 3-10天天气预报
//                 hourly = res.HeWeather6[0].hourly, // 逐小时预报
//                 lifestyle = res.HeWeather6[0].lifestyle, // 生活指数
//                 update = res.HeWeather6[0].update, // 更新时间
//                 weatherCode = res.HeWeather6[0].now.cond_code; // 天气状况代码
//             // 位置信息
//             $('#weatherBox .loc').html(basic.cnty + ' - ' + basic.admin_area + ' - ' + basic.parent_city + ' - ' + basic.location + '<i class="flag-icon flag-icon-' + basic.cid.substr(0, 2).toLowerCase() + '"></i>');
//             // 更新时间
//             $('#weatherBox time').html('更新于：' + update.loc);
//             // 温度
//             $('#weatherBox .tmp').html(now.tmp);
//             // 天气描述
//             $('#weatherBox .cond-txt').html(now.cond_txt);
//             // 天气图标
//             $('#weatherBox .cond-code').removeClass('wi-na').addClass(getWeatherIcon(kendo.toString(kendo.parseDate(update.loc), 'HH'), weatherCode));
//             // 空气质量
//             if (lifestyle) {
//                 $.ajax({
//                     type: 'get',
//                     data: {
//                         key: 'd2ae781d61744d65a2ef2156eef2cb64', // 请替换成自己的 Key
//                         location: basic.parent_city
//                     },
//                     url: 'https://free-api.heweather.net/s6/air/now',
//                     dataType: 'json',
//                     success: function (res) {
//                         var qlty = res.HeWeather6[0].air_now_city.qlty,
//                             qltyColor;
//                         if (qlty === '优') {
//                             qltyColor = 'success';
//                         } else if (qlty === '良') {
//                             qltyColor = 'info';
//                         } else if (qlty === '轻度污染' || qlty === '中度污染') {
//                             qltyColor = 'warning';
//                         } else if (qlty === '重度污染' || qlty === '严重污染') {
//                             qltyColor = 'error';
//                         }
//                         $('#weatherBox .air').html('空气质量：' + res.HeWeather6[0].air_now_city.aqi + '<span class="badge k-notification-' + qltyColor + '">' + qlty + '</span>');
//                     },
//                     error: function (res) {
//                         alertMsg('获取空气质量数据出错！', 'error');
//                     }
//                 });
//             } else {
//                 $('#weatherBox .air').parent().remove();
//             }
//             // 温度区间
//             $('#weatherBox .tmp-m').html('<i class="wi wi-thermometer theme-m"></i>' + daily_forecast[0].tmp_min + '℃~' + daily_forecast[0].tmp_max + '℃');
//             // 风向风力
//             $('#weatherBox .wind').html('<i class="wi wi-strong-wind theme-m"></i>' + now.wind_dir + now.wind_sc + '级');
//             // 相对湿度
//             $('#weatherBox .hum').html('<i class="wi wi-humidity theme-m"></i>湿度：' + daily_forecast[0].hum + '%');
//             // 紫外线强度
//             if (basic.cnty === '中国') {
//                 // 国内
//                 $('#weatherBox .uv-index').html('<i class="wi wi-umbrella theme-m"></i>紫外线：' + lifestyle[5].brf);
//             } else {
//                 // 国外
//                 $('#weatherBox .uv-index').html('<i class="wi wi-umbrella theme-m"></i>紫外线：' + daily_forecast[0].uv_index + '级');
//             }
//             // 日升时间
//             $('#weatherBox .sr').html('<i class="wi wi-sunrise theme-m"></i>日升：' + daily_forecast[0].sr);
//             // 日落时间
//             $('#weatherBox .ss').html('<i class="wi wi-sunset theme-m"></i>日落：' + daily_forecast[0].ss);
//             // 月升时间
//             $('#weatherBox .mr').html('<i class="wi wi-moonrise theme-m"></i>月升：' + daily_forecast[0].mr);
//             // 月落时间
//             $('#weatherBox .ms').html('<i class="wi wi-moonset theme-m"></i>月落：' + daily_forecast[0].ms);
//             // 未来 24 小时温度曲线
//             $('#weatherChart').kendoChart({
//                 theme: 'sass',
//                 chartArea: {
//                     height: 100
//                 },
//                 dataSource: {
//                     data: hourly,
//                     schema: {
//                         model: {
//                             fields: {
//                                 time: { type: 'string' },
//                                 tmp: { type: 'number' }
//                             }
//                         }
//                     }
//                 },
//                 legend: {
//                     visible: false
//                 },
//                 seriesDefaults: {
//                     type: 'area',
//                     area: {
//                         line: {
//                             width: 1,
//                             style: 'smooth'
//                         }
//                     },
//                     markers: {
//                         visible: true,
//                         size: 5
//                     }
//                 },
//                 series: [
//                     {
//                         field: 'tmp'
//                     }
//                 ],
//                 categoryAxis: {
//                     field: 'time',
//                     majorGridLines: {
//                         visible: false
//                     },
//                     labels: {
//                         template: '#: kendo.toString(kendo.parseDate(value), "HH点") #'
//                     }
//                 },
//                 valueAxis: {
//                     majorGridLines: {
//                         step: 2
//                     },
//                     labels: {
//                         format: '{0}℃',
//                         font: '9px arial, helvetica, sans-serif',
//                         color: '#999',
//                         step: 2
//                     }
//                 },
//                 tooltip: {
//                     visible: true,
//                     template: '#= value #℃'
//                 }
//             });
//             // 今天天气
//             $('#weatherBox .today').html('<span>今天</span><span class="icon"><i class="wi ' + getWeatherIcon(8, daily_forecast[0].cond_code_d) + ' theme-m"></i><i class="wi ' + getWeatherIcon(20, daily_forecast[0].cond_code_n) + ' theme-s"></i></span><span>' + daily_forecast[0].tmp_min + '℃ ~ ' + daily_forecast[0].tmp_max + '℃</span><span>日：' + daily_forecast[0].cond_txt_d + '</span><span>夜：' + daily_forecast[0].cond_txt_n + '</span>');
//             // 明天天气
//             $('#weatherBox .tomorrow').html('<span>明天</span><span class="icon"><i class="wi ' + getWeatherIcon(8, daily_forecast[1].cond_code_d) + ' theme-m"></i><i class="wi ' + getWeatherIcon(20, daily_forecast[1].cond_code_n) + ' theme-s"></i></span><span>' + daily_forecast[1].tmp_min + '℃ ~ ' + daily_forecast[1].tmp_max + '℃</span><span>日：' + daily_forecast[1].cond_txt_d + '</span><span>夜：' + daily_forecast[1].cond_txt_n + '</span>');
//             // 后天天气
//             $('#weatherBox .after-tomorrow').html('<span>后天</span><span class="icon"><i class="wi ' + getWeatherIcon(8, daily_forecast[2].cond_code_d) + ' theme-m"></i><i class="wi ' + getWeatherIcon(20, daily_forecast[2].cond_code_n) + ' theme-s"></i></span><span>' + daily_forecast[2].tmp_min + '℃ ~ ' + daily_forecast[2].tmp_max + '℃</span><span>日：' + daily_forecast[2].cond_txt_d + '</span><span>夜：' + daily_forecast[2].cond_txt_n + '</span>');
//         },
//         error: function (res) {
//             alertMsg('获取天气数据出错！', 'error');
//         }
//     });
// }

// // 天气图标
// function getWeatherIcon(time, weatherCode) {
//     if (time >= 6 && time < 18) {
//         if (weatherCode === '100') { // 日间晴
//             return 'wi-day-sunny';
//         } else if (weatherCode === '101') { // 日间多云
//             return 'wi-cloudy';
//         } else if (weatherCode === '102') { // 日间少云
//             return 'wi-day-sunny-overcast';
//         } else if (weatherCode === '103') { // 日间晴间多云
//             return 'wi-day-cloudy';
//         } else if (weatherCode === '104') { // 日间阴
//             return 'wi-cloud';
//         } else if (weatherCode === '200') { // 日间有风
//             return 'wi-windy';
//         } else if (weatherCode === '201') { // 日间平静
//             return 'wi-day-cloudy-windy';
//         } else if (weatherCode === '202') { // 日间微风
//             return 'wi-cloudy-windy';
//         } else if (weatherCode === '203') { // 日间和风
//             return 'wi-day-cloudy-gusts';
//         } else if (weatherCode === '204') { // 日间清风
//             return 'wi-cloudy-gusts';
//         } else if (weatherCode === '205') { // 日间强风/劲风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '206') { // 日间疾风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '207') { // 日间大风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '208') { // 日间烈风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '209') { // 日间风暴
//             return 'wi-strong-wind';
//         } else if (weatherCode === '210') { // 日间狂爆风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '211') { // 日间飓风
//             return 'wi-hurricane';
//         } else if (weatherCode === '212') { // 日间龙卷风
//             return 'wi-tornado';
//         } else if (weatherCode === '213') { // 日间热带风暴
//             return 'wi-hurricane';
//         } else if (weatherCode === '300') { // 日间阵雨
//             return 'wi-day-sprinkle';
//         } else if (weatherCode === '301') { // 日间强阵雨
//             return 'wi-day-showers';
//         } else if (weatherCode === '302') { // 日间雷阵雨
//             return 'wi-day-storm-showers';
//         } else if (weatherCode === '303') { // 日间强雷阵雨
//             return 'wi-storm-showers';
//         } else if (weatherCode === '304') { // 日间雷阵雨伴有冰雹
//             return 'wi-day-sleet-storm';
//         } else if (weatherCode === '305') { // 日间小雨
//             return 'wi-day-rain';
//         } else if (weatherCode === '306') { // 日间中雨
//             return 'wi-showers';
//         } else if (weatherCode === '307') { // 日间大雨
//             return 'wi-hail';
//         } else if (weatherCode === '308') { // 日间极端降雨
//             return 'wi-rain-wind';
//         } else if (weatherCode === '309') { // 日间毛毛雨/细雨
//             return 'wi-raindrops';
//         } else if (weatherCode === '310') { // 日间暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '311') { // 日间大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '312') { // 日间特大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '313') { // 日间冻雨
//             return 'wi-sleet';
//         } else if (weatherCode === '314') { // 日间小到中雨
//             return 'wi-showers';
//         } else if (weatherCode === '315') { // 日间中到大雨
//             return 'wi-hail';
//         } else if (weatherCode === '316') { // 日间大到暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '317') { // 日间暴雨到大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '318') { // 日间大暴雨到特大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '399') { // 日间雨
//             return 'wi-raindrop';
//         } else if (weatherCode === '400') { // 日间小雪
//             return 'wi-day-snow-wind';
//         } else if (weatherCode === '401') { // 日间中雪
//             return 'wi-snow';
//         } else if (weatherCode === '402') { // 日间大雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '403') { // 日间暴雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '404') { // 日间雨夹雪
//             return 'wi-rain-mix';
//         } else if (weatherCode === '405') { // 日间雨雪天气
//             return 'wi-rain-mix';
//         } else if (weatherCode === '406') { // 日间阵雨夹雪
//             return 'wi-day-snow-thunderstorm';
//         } else if (weatherCode === '407') { // 日间阵雪
//             return 'wi-day-snow';
//         } else if (weatherCode === '408') { // 日间小到中雪
//             return 'wi-snow';
//         } else if (weatherCode === '409') { // 日间中到大雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '410') { // 日间大到暴雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '499') { // 日间雪
//             return 'wi-snowflake-cold';
//         } else if (weatherCode === '500') { // 日间薄雾
//             return 'wi-day-fog';
//         } else if (weatherCode === '501') { // 日间雾
//             return 'wi-day-fog';
//         } else if (weatherCode === '502') { // 日间霾
//             return 'wi-smoke';
//         } else if (weatherCode === '503') { // 日间扬沙
//             return 'wi-dust';
//         } else if (weatherCode === '504') { // 日间浮尘
//             return 'wi-dust';
//         } else if (weatherCode === '507') { // 日间沙尘暴
//             return 'wi-sandstorm';
//         } else if (weatherCode === '508') { // 日间强沙尘暴
//             return 'wi-sandstorm';
//         } else if (weatherCode === '509') { // 日间浓雾
//             return 'wi-fog';
//         } else if (weatherCode === '510') { // 日间强浓雾
//             return 'wi-fog';
//         } else if (weatherCode === '511') { // 日间中度霾
//             return 'wi-smog';
//         } else if (weatherCode === '512') { // 日间重度霾
//             return 'wi-smog';
//         } else if (weatherCode === '513') { // 日间严重霾
//             return 'wi-smog';
//         } else if (weatherCode === '514') { // 日间大雾
//             return 'wi-fog';
//         } else if (weatherCode === '515') { // 日间特强浓雾
//             return 'wi-fog';
//         } else if (weatherCode === '900') { // 日间热
//             return 'wi-hot';
//         } else if (weatherCode === '901') { // 日间冷
//             return 'wi-snowflake-cold';
//         } else if (weatherCode === '999') { // 日间未知
//             return 'wi-na';
//         } else {
//             return 'wi-na';
//         }
//     } else {
//         if (weatherCode === '100') { // 夜间晴
//             return 'wi-night-clear';
//         } else if (weatherCode === '101') { // 夜间多云
//             return 'wi-cloudy';
//         } else if (weatherCode === '102') { // 夜间少云
//             return 'wi-night-alt-partly-cloudy';
//         } else if (weatherCode === '103') { // 夜间晴间多云
//             return 'wi-night-alt-cloudy';
//         } else if (weatherCode === '104') { // 夜间阴
//             return 'wi-cloud';
//         } else if (weatherCode === '200') { // 夜间有风
//             return 'wi-windy';
//         } else if (weatherCode === '201') { // 夜间平静
//             return 'wi-night-alt-cloudy-windy';
//         } else if (weatherCode === '202') { // 夜间微风
//             return 'wi-cloudy-windy';
//         } else if (weatherCode === '203') { // 夜间和风
//             return 'wi-night-alt-cloudy-gusts';
//         } else if (weatherCode === '204') { // 夜间清风
//             return 'wi-cloudy-gusts';
//         } else if (weatherCode === '205') { // 夜间强风/劲风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '206') { // 夜间疾风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '207') { // 夜间大风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '208') { // 夜间烈风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '209') { // 夜间风暴
//             return 'wi-strong-wind';
//         } else if (weatherCode === '210') { // 夜间狂爆风
//             return 'wi-strong-wind';
//         } else if (weatherCode === '211') { // 夜间飓风
//             return 'wi-hurricane';
//         } else if (weatherCode === '212') { // 夜间龙卷风
//             return 'wi-tornado';
//         } else if (weatherCode === '213') { // 夜间热带风暴
//             return 'wi-hurricane';
//         } else if (weatherCode === '300') { // 夜间阵雨
//             return 'wi-night-alt-sprinkle';
//         } else if (weatherCode === '301') { // 夜间强阵雨
//             return 'wi-night-alt-showers';
//         } else if (weatherCode === '302') { // 夜间雷阵雨
//             return 'wi-night-alt-storm-showers';
//         } else if (weatherCode === '303') { // 夜间强雷阵雨
//             return 'wi-storm-showers';
//         } else if (weatherCode === '304') { // 夜间雷阵雨伴有冰雹
//             return 'wi-night-alt-sleet-storm';
//         } else if (weatherCode === '305') { // 夜间小雨
//             return 'wi-night-alt-rain';
//         } else if (weatherCode === '306') { // 夜间中雨
//             return 'wi-showers';
//         } else if (weatherCode === '307') { // 夜间大雨
//             return 'wi-hail';
//         } else if (weatherCode === '308') { // 夜间极端降雨
//             return 'wi-rain-wind';
//         } else if (weatherCode === '309') { // 夜间毛毛雨/细雨
//             return 'wi-raindrops';
//         } else if (weatherCode === '310') { // 夜间暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '311') { // 夜间大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '312') { // 夜间特大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '313') { // 夜间冻雨
//             return 'wi-sleet';
//         } else if (weatherCode === '314') { // 夜间小到中雨
//             return 'wi-showers';
//         } else if (weatherCode === '315') { // 夜间中到大雨
//             return 'wi-hail';
//         } else if (weatherCode === '316') { // 夜间大到暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '317') { // 夜间暴雨到大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '318') { // 夜间大暴雨到特大暴雨
//             return 'wi-rain';
//         } else if (weatherCode === '399') { // 夜间雨
//             return 'wi-raindrop';
//         } else if (weatherCode === '400') { // 夜间小雪
//             return 'wi-night-alt-snow-wind';
//         } else if (weatherCode === '401') { // 夜间中雪
//             return 'wi-snow';
//         } else if (weatherCode === '402') { // 夜间大雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '403') { // 夜间暴雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '404') { // 夜间雨夹雪
//             return 'wi-rain-mix';
//         } else if (weatherCode === '405') { // 夜间雨雪天气
//             return 'wi-rain-mix';
//         } else if (weatherCode === '406') { // 夜间阵雨夹雪
//             return 'wi-night-alt-snow-thunderstorm';
//         } else if (weatherCode === '407') { // 夜间阵雪
//             return 'wi-night-alt-snow';
//         } else if (weatherCode === '408') { // 夜间小到中雪
//             return 'wi-snow';
//         } else if (weatherCode === '409') { // 夜间中到大雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '410') { // 夜间大到暴雪
//             return 'wi-snow-wind';
//         } else if (weatherCode === '499') { // 夜间雪
//             return 'wi-snowflake-cold';
//         } else if (weatherCode === '500') { // 夜间薄雾
//             return 'wi-night-fog';
//         } else if (weatherCode === '501') { // 夜间雾
//             return 'wi-night-fog';
//         } else if (weatherCode === '502') { // 夜间霾
//             return 'wi-smoke';
//         } else if (weatherCode === '503') { // 夜间扬沙
//             return 'wi-dust';
//         } else if (weatherCode === '504') { // 夜间浮尘
//             return 'wi-dust';
//         } else if (weatherCode === '507') { // 夜间沙尘暴
//             return 'wi-sandstorm';
//         } else if (weatherCode === '508') { // 夜间强沙尘暴
//             return 'wi-sandstorm';
//         } else if (weatherCode === '509') { // 夜间浓雾
//             return 'wi-fog';
//         } else if (weatherCode === '510') { // 夜间强浓雾
//             return 'wi-fog';
//         } else if (weatherCode === '511') { // 夜间中度霾
//             return 'wi-smog';
//         } else if (weatherCode === '512') { // 夜间重度霾
//             return 'wi-smog';
//         } else if (weatherCode === '513') { // 夜间严重霾
//             return 'wi-smog';
//         } else if (weatherCode === '514') { // 夜间大雾
//             return 'wi-fog';
//         } else if (weatherCode === '515') { // 夜间特强浓雾
//             return 'wi-fog';
//         } else if (weatherCode === '900') { // 夜间热
//             return 'wi-hot';
//         } else if (weatherCode === '901') { // 夜间冷
//             return 'wi-snowflake-cold';
//         } else if (weatherCode === '999') { // 夜间未知
//             return 'wi-na';
//         } else {
//             return 'wi-na';
//         }
//     }
// }

// // 农历基础数据
// var lunarData = {
//     lunarInfo: [
//         0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
//         0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
//         0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970,
//         0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950,
//         0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557,
//         0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0,
//         0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0,
//         0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6,
//         0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570,
//         0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0,
//         0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5,
//         0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930,
//         0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530,
//         0x05aa0, 0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45,
//         0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0,
//         0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0,
//         0x0a2e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4,
//         0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0,
//         0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160,
//         0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252,
//         0x0d520
//     ],
//     solarMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
//     tianGan: ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'],
//     diZhi: ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'],
//     zodiac: ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'],
//     solarTerm: ['小寒', '大寒', '立春', '雨水', '惊蛰', '春分', '清明', '谷雨', '立夏', '小满', '芒种', '夏至', '小暑', '大暑', '立秋', '处暑', '白露', '秋分', '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'],
//     sTermInfo: [
//         '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f',
//         '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
//         '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f', 'b027097bd097c36b0b6fc9274c91aa',
//         '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd0b06bdb0722c965ce1cfcc920f',
//         'b027097bd097c36b0b6fc9274c91aa', '9778397bd19801ec9210c965cc920e', '97b6b97bd19801ec95f8c965cc920f',
//         '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd197c36c9210c9274c91aa',
//         '97b6b97bd19801ec95f8c965cc920e', '97bd09801d98082c95f8e1cfcc920f', '97bd097bd097c36b0b6fc9210c8dc2',
//         '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec95f8c965cc920e', '97bcf97c3598082c95f8e1cfcc920f',
//         '97bd097bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e',
//         '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
//         '97b6b97bd19801ec9210c965cc920e', '97bcf97c3598082c95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722',
//         '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f',
//         '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
//         '97bcf97c359801ec95f8c965cc920f', '97bd097bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
//         '97b6b97bd19801ec9210c965cc920e', '97bcf97c359801ec95f8c965cc920f', '97bd097bd07f595b0b6fc920fb0722',
//         '9778397bd097c36b0b6fc9210c8dc2', '9778397bd19801ec9210c9274c920e', '97b6b97bd19801ec95f8c965cc920f',
//         '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',
//         '97b6b97bd19801ec95f8c965cc920f', '97bd07f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',
//         '9778397bd097c36c9210c9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bd07f1487f595b0b0bc920fb0722',
//         '7f0e397bd097c36b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
//         '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
//         '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
//         '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e', '97bcf7f1487f531b0b0bb0b6fb0722',
//         '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b97bd19801ec9210c965cc920e',
//         '97bcf7f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
//         '97b6b97bd19801ec9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
//         '9778397bd097c36b0b6fc9210c91aa', '97b6b97bd197c36c9210c9274c920e', '97bcf7f0e47f531b0b0bb0b6fb0722',
//         '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '9778397bd097c36c9210c9274c920e',
//         '97b6b7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c36b0b6fc9210c8dc2',
//         '9778397bd097c36b0b70c9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',
//         '7f0e397bd097c35b0b6fc9210c8dc2', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',
//         '7f0e27f1487f595b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
//         '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
//         '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
//         '7f0e397bd097c35b0b6fc920fb0722', '9778397bd097c36b0b6fc9274c91aa', '97b6b7f0e47f531b0723b0b6fb0721',
//         '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9274c91aa',
//         '97b6b7f0e47f531b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
//         '9778397bd097c36b0b6fc9210c91aa', '97b6b7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',
//         '7f0e397bd07f595b0b0bc920fb0722', '9778397bd097c36b0b6fc9210c8dc2', '977837f0e37f149b0723b0787b0721',
//         '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f5307f595b0b0bc920fb0722', '7f0e397bd097c35b0b6fc9210c8dc2',
//         '977837f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e37f1487f595b0b0bb0b6fb0722',
//         '7f0e397bd097c35b0b6fc9210c8dc2', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
//         '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722', '977837f0e37f14998082b0787b06bd',
//         '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd097c35b0b6fc920fb0722',
//         '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
//         '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
//         '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14998082b0787b06bd',
//         '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0b0bb0b6fb0722', '7f0e397bd07f595b0b0bc920fb0722',
//         '977837f0e37f14998082b0723b06bd', '7f07e7f0e37f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',
//         '7f0e397bd07f595b0b0bc920fb0722', '977837f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b0721',
//         '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f595b0b0bb0b6fb0722', '7f0e37f0e37f14898082b0723b02d5',
//         '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e37f1487f531b0b0bb0b6fb0722',
//         '7f0e37f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
//         '7f0e37f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',
//         '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e37f14898082b072297c35',
//         '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722',
//         '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f149b0723b0787b0721',
//         '7f0e27f1487f531b0b0bb0b6fb0722', '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14998082b0723b06bd',
//         '7f07e7f0e47f149b0723b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722', '7f0e37f0e366aa89801eb072297c35',
//         '7ec967f0e37f14998082b0723b06bd', '7f07e7f0e37f14998083b0787b0721', '7f0e27f0e47f531b0723b0b6fb0722',
//         '7f0e37f0e366aa89801eb072297c35', '7ec967f0e37f14898082b0723b02d5', '7f07e7f0e37f14998082b0787b0721',
//         '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66aa89801e9808297c35', '665f67f0e37f14898082b0723b02d5',
//         '7ec967f0e37f14998082b0787b0721', '7f07e7f0e47f531b0723b0b6fb0722', '7f0e36665b66a449801e9808297c35',
//         '665f67f0e37f14898082b0723b02d5', '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721',
//         '7f0e36665b66a449801e9808297c35', '665f67f0e37f14898082b072297c35', '7ec967f0e37f14998082b0787b06bd',
//         '7f07e7f0e47f531b0723b0b6fb0721', '7f0e26665b66a449801e9808297c35', '665f67f0e37f1489801eb072297c35',
//         '7ec967f0e37f14998082b0787b06bd', '7f07e7f0e47f531b0723b0b6fb0721', '7f0e27f1487f531b0b0bb0b6fb0722'
//     ],
//     lunarFestival: [
//         '0101 春节',
//         '0115 元宵节',
//         '0202 龙头节',
//         '0303 上巳节',
//         '0505 端午节',
//         '0707 七夕节',
//         '0715 中元节',
//         '0815 中秋节',
//         '0909 重阳节',
//         '1001 寒衣节',
//         '1015 下元节',
//         '1208 腊八节',
//         '1223 北小年',
//         '1224 南小年'
//     ],
//     solarFestival: [
//         '0101 元旦',
//         '0214 情人节',
//         '0308 妇女节',
//         '0312 植树节',
//         '0401 愚人节',
//         '0501 劳动节',
//         '0504 青年节',
//         '0601 儿童节',
//         '0701 建党节',
//         '0801 建军节',
//         '0910 教师节',
//         '1001 国庆节',
//         '1101 万圣节',
//         '1213 国家公祭',
//         '1225 圣诞节',
//         '1226 主席诞辰'
//     ],
//     dayChina: ['日', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'],
//     tenDayChina: ['初', '十', '廿', '卅'],
//     monthChina: ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊'],
//     lunarYearDays: function (y) {
//         var i,
//             sum = 348;
//         for (i = 0x8000; i > 0x8; i >>= 1) {
//             sum += (this.lunarInfo[y - 1900] & i) ? 1 : 0;
//         }
//         return (sum + this.leapDays(y));
//     },
//     leapMonth: function (y) {
//         return (this.lunarInfo[y - 1900] & 0xf);
//     },
//     leapDays: function (y) {
//         if (this.leapMonth(y)) {
//             return ((this.lunarInfo[y - 1900] & 0x10000) ? 30 : 29);
//         }
//         return 0;
//     },
//     lunarMonthDays: function (y, m) {
//         if (m > 12 || m < 1) {
//             return -1;
//         } else if (y === 1899) {
//             return 30;
//         }
//         return ((this.lunarInfo[y - 1900] & (0x10000 >> m)) ? 30 : 29 );
//     },
//     solarMonthDays: function (y, m) {
//         if (m > 12 || m < 1) {
//             return -1;
//         }
//         var ms = m - 1;
//         if (ms === 1) {
//             return (((y % 4 === 0) && (y % 100 !== 0) || (y % 400 === 0)) ? 29 : 28);
//         } else {
//             return (this.solarMonth[ms]);
//         }
//     },
//     toGanZhiYear: function (lYear) {
//         var ganKey = (lYear - 3) % 10;
//         var zhiKey = (lYear - 3) % 12;
//         if (ganKey === 0) ganKey = 10;
//         if (zhiKey === 0) zhiKey = 12;
//         return this.tianGan[ganKey - 1] + this.diZhi[zhiKey - 1];
//     },
//     toGanZhi: function (offset) {
//         return this.tianGan[offset % 10] + this.diZhi[offset % 12];
//     },
//     toChinaMonth: function (m) {
//         if (m > 12 || m < 1) {
//             return -1;
//         }
//         var s = this.monthChina[m - 1];
//         s += '月';
//         return s;
//     },
//     toChinaDay: function (d) {
//         var s;
//         switch (d) {
//             case 10:
//                 s = '初十';
//                 break;
//             case 20:
//                 s = '二十';
//                 break;
//             case 30:
//                 s = '三十';
//                 break;
//             default:
//                 s = this.tenDayChina[Math.floor(d / 10)];
//                 s += this.dayChina[d % 10];
//         }
//         return s;
//     },
//     getZodiac: function (y) {
//         return this.zodiac[(y - 4) % 12];
//     },
//     toConstellation: function (cMonth, cDay) {
//         var s = '山羊水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手山羊';
//         var arr = [20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22];
//         return s.substr(cMonth * 2 - (cDay < arr[cMonth - 1] ? 2 : 0), 2) + '座';
//     },
//     getTerm: function (y, n) {
//         if (y < 1900 || y > 2100) {
//             return -1;
//         }
//         if (n < 1 || n > 24) {
//             return -1;
//         }
//         var _table = this.sTermInfo[y - 1900];
//         var _info = [
//             parseInt('0x' + _table.substr(0, 5)).toString(),
//             parseInt('0x' + _table.substr(5, 5)).toString(),
//             parseInt('0x' + _table.substr(10, 5)).toString(),
//             parseInt('0x' + _table.substr(15, 5)).toString(),
//             parseInt('0x' + _table.substr(20, 5)).toString(),
//             parseInt('0x' + _table.substr(25, 5)).toString()
//         ];
//         var _calday = [
//             _info[0].substr(0, 1),
//             _info[0].substr(1, 2),
//             _info[0].substr(3, 1),
//             _info[0].substr(4, 2),
//             _info[1].substr(0, 1),
//             _info[1].substr(1, 2),
//             _info[1].substr(3, 1),
//             _info[1].substr(4, 2),
//             _info[2].substr(0, 1),
//             _info[2].substr(1, 2),
//             _info[2].substr(3, 1),
//             _info[2].substr(4, 2),
//             _info[3].substr(0, 1),
//             _info[3].substr(1, 2),
//             _info[3].substr(3, 1),
//             _info[3].substr(4, 2),
//             _info[4].substr(0, 1),
//             _info[4].substr(1, 2),
//             _info[4].substr(3, 1),
//             _info[4].substr(4, 2),
//             _info[5].substr(0, 1),
//             _info[5].substr(1, 2),
//             _info[5].substr(3, 1),
//             _info[5].substr(4, 2)
//         ];
//         return parseInt(_calday[n - 1]);
//     },
//     getFestival: function (y, m, d, type) {
//         var festival;
//         if (type === 'lunar') {
//             festival = this.lunarFestival;
//             if (m === 12 && d === this.lunarMonthDays(y, m)) {
//                 return '除夕';
//             }
//         } else if (type === 'solar') {
//             festival = this.solarFestival;
//         }
//         for (var i = 0; i < festival.length; i++) {
//             if (m === parseInt(festival[i].substr(0, 2)) && d === parseInt(festival[i].substr(2, 2))) {
//                 return festival[i].substr(5);
//             }
//         }
//         return null;
//     },
//     solar2lunar: function (y, m, d) {
//         if (y < 1900 || y > 2100) {
//             return -1;
//         }
//         if (!y) {
//             var objDate = new Date();
//         } else {
//             var objDate = new Date(y, parseInt(m) - 1, d);
//         }
//         var i,
//             leap = 0,
//             temp = 0;
//         var y = objDate.getFullYear(),
//             m = objDate.getMonth() + 1,
//             d = objDate.getDate();
//         if (y === 1900 && m === 1 && d < 31) {
//             var offset = (Date.UTC(objDate.getFullYear(), objDate.getMonth(), objDate.getDate()) - Date.UTC(1900, 0, 1)) / 86400000;
//         } else {
//             var offset = (Date.UTC(objDate.getFullYear(), objDate.getMonth(), objDate.getDate()) - Date.UTC(1900, 0, 31)) / 86400000;
//         }
//         for (i = 1900; i < 2101 && offset > 0; i++) {
//             temp = this.lunarYearDays(i);
//             offset -= temp;
//         }
//         if (offset < 0) {
//             offset += temp;
//             i--;
//         }
//         var isTodayObj = new Date(),
//             isToday = false;
//         if (isTodayObj.getFullYear() === y && isTodayObj.getMonth() + 1 === m && isTodayObj.getDate() === d) {
//             isToday = true;
//         }
//         var nWeek = objDate.getDay(),
//             cWeek = this.dayChina[nWeek];
//         if (nWeek === 0) {
//             nWeek = 7;
//         }
//         if (y === 1900 && m === 1 && d < 31) {
//             var year = 1899;
//         } else {
//             var year = i;
//         }
//         var leap = this.leapMonth(i);
//         var isLeap = false;
//         for (i = 1; i < 13 && offset > 0; i++) {
//             if (leap > 0 && i === (leap + 1) && isLeap === false) {
//                 --i;
//                 isLeap = true;
//                 temp = this.leapDays(year);
//             } else {
//                 temp = this.lunarMonthDays(year, i);
//             }
//             if (isLeap === true && i === (leap + 1)) {
//                 isLeap = false;
//             }
//             offset -= temp;
//         }
//         if (offset === 0 && leap > 0 && i === leap + 1) {
//             if (isLeap) {
//                 isLeap = false;
//             } else {
//                 isLeap = true;
//                 --i;
//             }
//         }
//         if (offset < 0) {
//             offset += temp;
//             --i;
//         }
//         if (y === 1900 && m === 1 && d < 31) {
//             var month = 12;
//         } else {
//             var month = i;
//         }
//         if (y === 1900 && m === 1 && d === 30) {
//             var day = 30;
//         } else {
//             var day = offset + 1;
//         }
//         var sm = m - 1;
//         var gzY = this.toGanZhiYear(year);
//         var firstNode = this.getTerm(y, (m * 2 - 1));
//         var secondNode = this.getTerm(y, (m * 2));
//         var gzM = this.toGanZhi((y - 1900) * 12 + m + 11);
//         if (d >= firstNode) {
//             gzM = this.toGanZhi((y - 1900) * 12 + m + 12);
//         }
//         var isTerm = false;
//         var Term = null;
//         if (firstNode === d) {
//             isTerm = true;
//             Term = this.solarTerm[m * 2 - 2];
//         }
//         if (secondNode === d) {
//             isTerm = true;
//             Term = this.solarTerm[m * 2 - 1];
//         }
//         var dayCyclical = Date.UTC(y, sm, 1, 0, 0, 0, 0) / 86400000 + 25567 + 10;
//         var gzD = this.toGanZhi(dayCyclical + d - 1);
//         var constellation = this.toConstellation(m, d);
//         var isFestival = false;
//         if (this.getFestival(y, m, d, 'solar') || this.getFestival(year, month, day, 'lunar')) {
//             isFestival = true;
//         }
//         return {
//             'solarYear': y,
//             'solarMonth': m,
//             'solarDay': d,
//             'week': nWeek,
//             'weekCn': '星期' + cWeek,
//             'gzYear': gzY,
//             'gzMonth': gzM,
//             'gzDay': gzD,
//             'lunarYear': year,
//             'lunarMonth': month,
//             'lunarDay': day,
//             'lunarMonthCn': (isLeap ? '闰' : '') + this.toChinaMonth(month),
//             'lunarDayCn': this.toChinaDay(day),
//             'zodiac': this.getZodiac(year),
//             'constellation': constellation,
//             'isToday': isToday,
//             'isLeap': isLeap,
//             'isTerm': isTerm,
//             'term': Term,
//             'isFestival': isFestival,
//             'lunarFestival': this.getFestival(year, month, day, 'lunar'),
//             'solarFestival': this.getFestival(y, m, d, 'solar')
//         };
//     },
//     lunar2solar: function (y, m, d, isLeapMonth) {
//         var isLeapMonth = !!isLeapMonth;
//         var leapOffset = 0;
//         var leapMonth = this.leapMonth(y);
//         var leapDay = this.leapDays(y);
//         if (isLeapMonth && (leapMonth !== m)) {
//             return -1;
//         }
//         if (y === 2100 && m === 12 && d > 1 || y === 1900 && m === 1 && d < 31) {
//             return -1;
//         }
//         var day = this.lunarMonthDays(y, m);
//         var _day = day;
//         if (isLeapMonth) {
//             _day = this.leapDays(y, m);
//         }
//         if (y < 1900 || y > 2100 || d > _day) {
//             return -1;
//         }
//         var offset = 0;
//         for (var i = 1900; i < y; i++) {
//             offset += this.lunarYearDays(i);
//         }
//         var leap = 0,
//             isAdd = false;
//         for(var i = 1; i < m; i++) {
//             leap = this.leapMonth(y);
//             if (!isAdd) {
//                 if (leap <= i && leap > 0) {
//                     offset += this.leapDays(y);
//                     isAdd = true;
//                 }
//             }
//             offset += this.lunarMonthDays(y, i);
//         }
//         if (isLeapMonth) {
//             offset += day;
//         }
//         var stmap = Date.UTC(1900, 1, 30, 0, 0, 0);
//         var calObj = new Date((offset + d - 31) * 86400000 + stmap);
//         var cY = calObj.getUTCFullYear();
//         var cM = calObj.getUTCMonth() + 1;
//         var cD = calObj.getUTCDate();
//         return this.solar2lunar(cY, cM, cD);
//     }
// };

// // 是否节假日
// function isHoliday(date, dates) {
//     for (var i = 0; i < dates.length; i++) {
//         if (date.getMonth() === dates[i].getMonth() && date.getDate() === dates[i].getDate()) {
//             return true;
//         }
//     }
//     return false;
// }

// // 万年历弹窗
// function getLunar() {
//     var divWindow = $('<div class="window-box" id="lunarBox"></div>').kendoWindow({
//             animation: {open: {effects: 'fade:in'}, close: {effects: 'fade:out'}},
//             title: '万年历',
//             width: '90%',
//             maxWidth: 800,
//             modal: true,
//             pinned: true,
//             resizable: false,
//             open: function () {
//                 $('#perpetualCalendar').kendoCalendar({
//                     footer: '今天：#= kendo.toString(data, "yyyy年MM月dd日") #',
//                     month: {
//                         content:
//                             '# var lunar = lunarData.solar2lunar(data.date.getFullYear(), (data.date.getMonth() + 1), data.date.getDate()) #' +
//                             '<div class="d-flex flex-column">' +
//                                 '#= data.value #' +
//                                 '#= getMoonIcon(lunar.lunarDay).substr(0, getMoonIcon(lunar.lunarDay).length - 2) #' +
//                                 '<small class="text-nowrap">' +
//                                 '# if (lunar.lunarFestival) { #' +
//                                     '<span class="festival rounded px-1">#= lunar.lunarFestival #</span>' +
//                                 '# } else if (lunar.solarFestival) { #' +
//                                     '<span class="festival rounded px-1">#= lunar.solarFestival #</span>' +
//                                 '# } else if (lunar.isTerm) { #' +
//                                     '<span class="theme-m-box rounded px-1">#= lunar.term #</span>' +
//                                 '# } else if (lunar.lunarDay === 1) { #' +
//                                     '<span class="theme-s-box rounded px-1">#= lunar.lunarMonthCn #</span>' +
//                                 '# } else { #' +
//                                     '#= lunar.lunarDayCn #' +
//                                 '# } #' +
//                                 '</small>' +
//                             '</div>'
//                     },
//                     value: new Date(),
//                     change: function () {
//                         setLunar(this.value());
//                     }
//                 });
//                 setLunar($('#perpetualCalendar').data('kendoCalendar').value());
//             },
//             close: function () {
//                 divWindow.destroy();
//             }
//         }).data('kendoWindow'),
//         lunarHtml =
//             '<div class="card">' +
//                 '<div class="row no-gutters">' +
//                     '<div class="col-md-4 theme-m-bg" id="lunarShow">' +
//                         '<div class="month"></div>' +
//                         '<div class="day theme-s-bg"></div>' +
//                         '<div class="week"></div>' +
//                         '<div class="lunar-day"></div>' +
//                         '<div class="moon-phase"></div>' +
//                         '<div class="festival"></div>' +
//                         '<div class="lunar-year"></div>' +
//                     '</div>' +
//                     '<div class="col-md-8">' +
//                         '<div id="perpetualCalendar"></div>' +
//                     '</div>' +
//                 '</div>' +
//             '</div>';
//     divWindow.content(lunarHtml).center().open();
// }

// // 万年历显示
// function setLunar(date) {
//     var lunar = lunarData.solar2lunar(date.getFullYear(), (date.getMonth() + 1), date.getDate());
//     // 年月
//     $('#lunarShow .month').html(kendo.toString(date, "yyyy年MM月"));
//     // 日
//     $('#lunarShow .day').html(kendo.toString(date, "dd"));
//     // 时间、星期、节气
//     if (lunar.isTerm) {
//         $('#lunarShow .week').html('<i class="wi wi-time-' + kendo.toString(date, "h") + '"></i>' + kendo.toString(date, "dddd") + '【' + lunar.term + '】');
//     } else {
//         $('#lunarShow .week').html('<i class="wi wi-time-' + kendo.toString(date, "h") + '"></i>' + kendo.toString(date, "dddd"));
//     }
//     // 农历年月日
//     $('#lunarShow .lunar-day').html(lunar.zodiac + '年：' + lunar.lunarMonthCn + lunar.lunarDayCn);
//     // 月相
//     $('#lunarShow .moon-phase').html(getMoonIcon(lunar.lunarDay));
//     // 节假日
//     $('#lunarShow .festival').html('');
//     // 农历节假日
//     if (lunar.lunarFestival) {
//         $('#lunarShow .festival').show().append('<span>' + lunar.lunarFestival + '</span>');
//     }
//     // 公历节假日
//     if (lunar.solarFestival) {
//         $('#lunarShow .festival').show().append('<span>' + lunar.solarFestival + '</span>');
//     }
//     $('#lunarShow .festival').prepend('--=').append('=--');
//     if (!(lunar.lunarFestival) && !(lunar.solarFestival)) {
//         $('#lunarShow .festival').hide();
//     }
//     // 天干地支年月日
//     $('#lunarShow .lunar-year').html('<span>' + lunar.gzYear + '年</span><span>' + lunar.gzMonth + '月</span><span>' + lunar.gzDay + '日</span>');
// }

// // 月相图标及名称
// function getMoonIcon(lunarDay) {
//     if (lunarDay === 1) {
//         return '<i class="wi wi-moon-new"></i>朔月';
//     } else if (lunarDay === 2) {
//         return '<i class="wi wi-moon-waxing-crescent-1"></i>既朔';
//     } else if (lunarDay === 3) {
//         return '<i class="wi wi-moon-waxing-crescent-2"></i>眉月';
//     } else if (lunarDay === 4) {
//         return '<i class="wi wi-moon-waxing-crescent-3"></i>眉月';
//     } else if (lunarDay === 5) {
//         return '<i class="wi wi-moon-waxing-crescent-4"></i>盈眉';
//     } else if (lunarDay === 6) {
//         return '<i class="wi wi-moon-waxing-crescent-5"></i>夕月';
//     } else if (lunarDay === 7) {
//         return '<i class="wi wi-moon-waxing-crescent-6"></i>上弦';
//     } else if (lunarDay === 8) {
//         return '<i class="wi wi-moon-first-quarter"></i>上弦';
//     } else if (lunarDay === 9) {
//         return '<i class="wi wi-moon-waxing-gibbous-1"></i>九夜';
//     } else if (lunarDay === 10) {
//         return '<i class="wi wi-moon-waxing-gibbous-2"></i>宵月';
//     } else if (lunarDay === 11) {
//         return '<i class="wi wi-moon-waxing-gibbous-3"></i>宵月';
//     } else if (lunarDay === 12) {
//         return '<i class="wi wi-moon-waxing-gibbous-4"></i>宵月';
//     } else if (lunarDay === 13) {
//         return '<i class="wi wi-moon-waxing-gibbous-5"></i>盈凸';
//     } else if (lunarDay === 14) {
//         return '<i class="wi wi-moon-waxing-gibbous-6"></i>小望';
//     } else if (lunarDay === 15) {
//         return '<i class="wi wi-moon-full"></i>望月';
//     } else if (lunarDay === 16) {
//         return '<i class="wi wi-moon-full"></i>既望';
//     } else if (lunarDay === 17) {
//         return '<i class="wi wi-moon-waning-gibbous-1"></i>立待';
//     } else if (lunarDay === 18) {
//         return '<i class="wi wi-moon-waning-gibbous-2"></i>居待';
//     } else if (lunarDay === 19) {
//         return '<i class="wi wi-moon-waning-gibbous-3"></i>寝待';
//     } else if (lunarDay === 20) {
//         return '<i class="wi wi-moon-waning-gibbous-4"></i>更待';
//     } else if (lunarDay === 21) {
//         return '<i class="wi wi-moon-waning-gibbous-5"></i>亏凸';
//     } else if (lunarDay === 22) {
//         return '<i class="wi wi-moon-waning-gibbous-6"></i>下弦';
//     } else if (lunarDay === 23) {
//         return '<i class="wi wi-moon-third-quarter"></i>下弦';
//     } else if (lunarDay === 24) {
//         return '<i class="wi wi-moon-waning-crescent-1"></i>有明';
//     } else if (lunarDay === 25) {
//         return '<i class="wi wi-moon-waning-crescent-2"></i>有明';
//     } else if (lunarDay === 26) {
//         return '<i class="wi wi-moon-waning-crescent-3"></i>亏眉';
//     } else if (lunarDay === 27) {
//         return '<i class="wi wi-moon-waning-crescent-4"></i>亏眉';
//     } else if (lunarDay === 28) {
//         return '<i class="wi wi-moon-waning-crescent-5"></i>残月';
//     } else if (lunarDay === 29) {
//         return '<i class="wi wi-moon-waning-crescent-6"></i>晓月';
//     } else if (lunarDay === 30) {
//         return '<i class="wi wi-moon-new"></i>晦月';
//     }
// }

// // 便签弹窗
// function getNote() {
//     if (window.indexedDB) {
//         var req = window.indexedDB.open('noteDB'),
//             db,
//             divWindow = $('<div class="window-box" id="noteBox"></div>').kendoWindow({
//                 animation: {open: {effects: 'fade:in'}, close: {effects: 'fade:out'}},
//                 title: '便签',
//                 width: '90%',
//                 maxWidth: 360,
//                 height: 540,
//                 modal: true,
//                 pinned: true,
//                 resizable: false,
//                 open: function () {
//                     $('#noteListView').kendoListView({
//                         dataSource: {
//                             transport: {
//                                 create: function (options) {
//                                     delete options.data.id;
//                                     var createResult = db.transaction(['list'], 'readwrite').objectStore('list').add(options.data);
//                                     createResult.onsuccess = function (e) {
//                                         options.success(e.target);
//                                         refreshNote();
//                                     };
//                                     createResult.onerror = function () {
//                                         alertMsg('便签新增出错！', 'error');
//                                     };
//                                 },
//                                 destroy: function (options) {
//                                     var destroyResult = db.transaction(['list'], 'readwrite').objectStore('list').delete(options.data.id);
//                                     destroyResult.onsuccess = function (e) {
//                                         options.success(e.target);
//                                         refreshNote();
//                                     };
//                                     destroyResult.onerror = function () {
//                                         alertMsg('便签删除出错！', 'error');
//                                     };
//                                 },
//                                 update: function (options) {
//                                     var updateResult = db.transaction(['list'], 'readwrite').objectStore('list').put(options.data);
//                                     updateResult.onsuccess = function (e) {
//                                         options.success(e.target);
//                                         refreshNote();
//                                     };
//                                     updateResult.onerror = function () {
//                                         alertMsg('便签编辑出错！', 'error');
//                                     };
//                                 },
//                                 read: function (options) {
//                                     var readResult = db.transaction(['list']).objectStore('list').getAll();
//                                     readResult.onsuccess = function (e) {
//                                         options.success(e.target);
//                                         if (e.target.result.length === 0) {
//                                             $('#noteListView').html('<div class="blank"><p class="lead">无便签</p></div>');
//                                         }
//                                     };
//                                     readResult.onerror = function () {
//                                         alertMsg('便签查询出错！', 'error');
//                                     };
//                                 }
//                             },
//                             schema: {
//                                 total: function(res) {
//                                     return res.result.length;
//                                 },
//                                 data: 'result',
//                                 model: {
//                                     id: 'id',
//                                     fields: {
//                                         noteContent: { type: 'string' },
//                                         noteTime: { type: 'string' }
//                                     }
//                                 }
//                             },
//                             pageSize: 5
//                         },
//                         height: 400,
//                         scrollable: 'endless',
//                         template: kendo.template($('#noteListTemplate').html()),
//                         editTemplate: kendo.template($('#noteEditTemplate').html()),
//                         save: function (e) {
//                             // 自动生成时间
//                             e.model.set('noteTime', kendo.toString(new Date(), 'yyyy-MM-dd HH:mm:ss'));
//                         },
//                         dataBound: function () {
//                             // 便签颜色
//                             $('#noteListView .alert:nth-child(6n+1)').addClass('alert-primary');
//                             $('#noteListView .alert:nth-child(6n+2)').addClass('alert-secondary');
//                             $('#noteListView .alert:nth-child(6n+3)').addClass('alert-success');
//                             $('#noteListView .alert:nth-child(6n+4)').addClass('alert-danger');
//                             $('#noteListView .alert:nth-child(6n+5)').addClass('alert-warning');
//                             $('#noteListView .alert:nth-child(6n+6)').addClass('alert-info');
//                         }
//                     });
//                     // 便签新增
//                     $('#noteBox .k-add-button').click(function (e) {
//                         $('#noteListView').data('kendoListView').add();
//                         e.preventDefault();
//                     });
//                     // 便签查找
//                     $('#noteSearch').keyup(function () {
//                         $('#noteListView').data('kendoListView').dataSource.filter({
//                             logic: 'or',
//                             filters: [
//                                 { field: 'noteContent', operator: 'contains', value: $(this).val() },
//                                 { field: 'noteTime', operator: 'contains', value: $(this).val() }
//                             ]
//                         });
//                     });
//                     // 便签清空
//                     $('#noteBox .k-clear-button').click(function (e) {
//                         confirmMsg('清空确认', '你确定要清空便签吗？', 'question', function () {
//                             db.transaction(['list'], 'readwrite').objectStore('list').clear();
//                             refreshNote();
//                         });
//                     });
//                 },
//                 close: function () {
//                     db.close();
//                     divWindow.destroy();
//                 }
//             }).data('kendoWindow'),
//             noteHtml =
//                 '<div class="note-tools">' +
//                     '<span class="k-textbox"><i class="fas fa-search theme-m"></i><input id="noteSearch" type="text"></span>' +
//                     '<a class="k-link k-order-button" href="javascript:;" title="降序" onclick="orderNote(\'desc\');"><i class="fas fa-sort-amount-down"></i></a>' +
//                     '<a class="k-link k-order-button" href="javascript:;" title="升序" onclick="orderNote(\'asc\');"><i class="fas fa-sort-amount-up-alt"></i></a>' +
//                     '<a class="k-link k-clear-button" href="javascript:;" title="清空"><i class="fas fa-trash-alt"></i></a>' +
//                 '</div>' +
//                 '<div id="noteListView"></div>' +
//                 '<div class="note-tools">' +
//                     '<a class="k-button theme-m-box k-add-button" href="javascript:;" title="新增"><span class="k-icon k-i-add"></span></a>' +
//                 '</div>' +
//                 '<script id="noteListTemplate" type="text/x-kendo-template">' +
//                     '<div class="alert">' +
//                         '<p>#= noteContent #</p>' +
//                         '<hr>' +
//                         '<time>' +
//                             '<small>#= noteTime #</small>' +
//                             '<span>' +
//                                 '<a class="k-button theme-m-box k-edit-button" href="javascript:;" title="编辑"><span class="k-icon k-i-edit"></span></a>' +
//                                 '<a class="k-button k-delete-button" href="javascript:;" title="删除"><span class="k-icon k-i-x"></span></a>' +
//                             '</span>' +
//                         '</time>' +
//                     '</div>' +
//                 '</script>' +
//                 '<script id="noteEditTemplate" type="text/x-kendo-template">' +
//                     '<div class="alert">' +
//                         '<textarea class="k-textarea w-100" name="noteContent"></textarea>' +
//                         '<hr>' +
//                         '<time>' +
//                             '<small>#= kendo.toString(new Date(), "yyyy-MM-dd HH:mm:ss") #</small>' +
//                             '<span>' +
//                                 '<a class="k-button theme-m-box k-update-button" href="javascript:;" title="更新"><span class="k-icon k-i-check"></span></a>' +
//                                 '<a class="k-button k-cancel-button" href="javascript:;" title="取消"><span class="k-icon k-i-cancel"></span></a>' +
//                             '</span>' +
//                         '</time>' +
//                     '</div>' +
//                 '</script>';
//         // 本地数据库新建
//         req.onupgradeneeded = function (e) {
//             db = e.target.result;
//             if (!db.objectStoreNames.contains('list')) {
//                 db.createObjectStore('list', { keyPath: 'id', autoIncrement: true });
//             }
//         };
//         // 本地数据库读取
//         req.onsuccess = function (e) {
//             db = req.result;
//             divWindow.content(noteHtml).center().open();
//         };
//         // 本地数据库出错
//         req.onerror = function (e) {
//             alertMsg('获取便签数据出错！', 'error');
//         };
//     } else {
//         alertMsg('您的浏览器不支持便签功能！', 'error');
//     }
// }

// // 刷新便签
// function refreshNote() {
//     $('#noteListView').data('kendoListView').dataSource.read();
// }

// // 排序便签
// function orderNote(dir) {
//     $('#noteListView').data('kendoListView').dataSource.sort({
//         field: 'id',
//         dir: dir
//     });
//     $('#noteBox .k-order-button').toggle();
// }

// // 全局搜索开关
// function switchGlobalSearch(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.global-search').show();
//     } else {
//         $('#menuH, #menuV').find('.global-search').hide();
//     }
// }

// // 刷新开关
// function switchRefresh(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.refresh').show();
//     } else {
//         $('#menuH, #menuV').find('.refresh').hide();
//     }
// }

// // 全屏开关
// function switchFullScreen(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.full-screen').show();
//     } else {
//         $('#menuH, #menuV').find('.full-screen').hide();
//     }
// }

// // 锁屏开关
// function switchLockScreen(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.lock-screen').show();
//     } else {
//         $('#menuH, #menuV').find('.lock-screen').hide();
//     }
// }

// // 配色开关
// function switchTheme(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.theme').show();
//     } else {
//         $('#menuH, #menuV').find('.theme').hide();
//     }
// }

// // 语言开关
// function switchLocalization(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.localization').show();
//     } else {
//         $('#menuH, #menuV').find('.localization').hide();
//     }
// }

// // 消息开关
// function switchMessage(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.links-message').show();
//     } else {
//         $('#menuH, #menuV').find('.links-message').hide();
//     }
// }

// // 提醒开关
// function switchNotice(whether) {
//     if (whether) {
//         $('#menuH, #menuV').find('.links-notice').show();
//     } else {
//         $('#menuH, #menuV').find('.links-notice').hide();
//     }
// }

// // 分隔线
// function switchSeparator() {
//     if (localStorage.hasOwnProperty('globalSearch') && localStorage.hasOwnProperty('refresh')) {
//         if (!JSON.parse(localStorage.getItem('globalSearch')) && !JSON.parse(localStorage.getItem('refresh'))) {
//             $('#menuH, #menuV').find('.separator:eq(0)').hide();
//         } else {
//             $('#menuH, #menuV').find('.separator:eq(0)').show();
//         }
//     }
//     if (localStorage.hasOwnProperty('fullScreen') && localStorage.hasOwnProperty('lockScreen')) {
//         if (!JSON.parse(localStorage.getItem('fullScreen')) && !JSON.parse(localStorage.getItem('lockScreen'))) {
//             $('#menuH, #menuV').find('.separator:eq(1)').hide();
//         } else {
//             $('#menuH, #menuV').find('.separator:eq(1)').show();
//         }
//     }
//     if (localStorage.hasOwnProperty('theme') && localStorage.hasOwnProperty('localization')) {
//         if (!JSON.parse(localStorage.getItem('theme')) && !JSON.parse(localStorage.getItem('localization'))) {
//             $('#menuH, #menuV').find('.separator:eq(2)').hide();
//         } else {
//             $('#menuH, #menuV').find('.separator:eq(2)').show();
//         }
//     }
//     if (localStorage.hasOwnProperty('message') && localStorage.hasOwnProperty('notice')) {
//         if (!JSON.parse(localStorage.getItem('message')) && !JSON.parse(localStorage.getItem('notice'))) {
//             $('#menuH, #menuV').find('.separator:eq(3)').hide();
//         } else {
//             $('#menuH, #menuV').find('.separator:eq(3)').show();
//         }
//     }
// }

// // 聊天机器人开关
// function switchBot(whether) {
//     if (whether) {
//         $('#bot').show();
//     } else {
//         $('#bot').hide();
//     }
// }

// // 天气预报开关
// function switchWeather(whether) {
//     if (whether) {
//         $('#weather').show();
//     } else {
//         $('#weather').hide();
//     }
// }

// // 万年历开关
// function switchLunar(whether) {
//     if (whether) {
//         $('#lunar').show();
//     } else {
//         $('#lunar').hide();
//     }
// }

// // 便签开关
// function switchNote(whether) {
//     if (whether) {
//         $('#note').show();
//     } else {
//         $('#note').hide();
//     }
// }

// // 工具箱
// function switchTools() {
//     if (localStorage.hasOwnProperty('bot') && localStorage.hasOwnProperty('weather') && localStorage.hasOwnProperty('lunar') && localStorage.hasOwnProperty('note')) {
//         if (!JSON.parse(localStorage.getItem('bot')) && !JSON.parse(localStorage.getItem('weather')) && !JSON.parse(localStorage.getItem('lunar')) && !JSON.parse(localStorage.getItem('note'))) {
//             $('#tools').hide();
//         } else {
//             $('#tools').show();
//         }
//     }
// }