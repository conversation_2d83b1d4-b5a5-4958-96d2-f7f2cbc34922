var localhostPath = 'http://127.0.0.1:5500/';
var apiPath = 'http://127.0.0.1:8071/';// 配置接口路径
//var apiPath = 'http://lims.bmk.local:8071/';// 配置接口路径
//var apiPath = 'http://**************:8071//';// 配置接口路径
var developmentModel=true;//开发模式-开启
var fullAjaxType='get';//默认请求方式

var systemUpgrade="";
//var systemUpgrade="index.html";//系统升级页面调整路径-值存在时有效-目录 site
var defaultGridQueryFlag=true;//默认查询开关-ture 开启默认查询 false关闭默认查询


// var localhostPath = 'http://newlims.yikongenomics.cn/yikon-lims-frontMaster/';
// var apiPath = 'http://newlims.yikongenomics.cn:8888/';// 配置接口路径
// var developmentModel=false;//开发模式-关闭
// var fullAjaxType='post';//默认请求方式


//全局变量
var fullh=document.documentElement.clientHeight;
var fullw=document.documentElement.clientWidth;

var tokenExpireNumber=90*60;//90分钟没有操作-设置过期-过期时间
var tokenRefreshNumber=15*60;//15分钟-刷新token时间数 -刷新时间数需要大于过期时间数
var tokenExpireCurrNumber=tokenExpireNumber;//设置过期时间数
var tokenRefreshCurrNumber=tokenRefreshNumber;
var tokenRefreshAllowErrorTimesNumber=3;//刷新错误允许次数
var sysNowTimeFuncParams={};

var currLimsUser;//当前登陆用户
var usersNameAndIdCache={};//定义 userName:userId

var wholeOrg="yikon";//当前-组织
var wholeParamsTabName="";//页签名称
var wholeParamsTabIcon="";//页签图标
var wholeMenus=new Array();//全部菜单内容
var globalTabWinIndexs=new Array();//index页签分配ID情况

var componentDialogCacheArray=new Array();//组件-弹框-缓存
var componentGenerateCacheArray=new Array();//组件-生成编码-缓存

var componentQueryJson={};//组件配置查询参数
var componentQueryData={};//组件请求查询参数

var globalPathValueComponents={};//组件-路径-全局

var gridButtonSelectMappings={};//下拉按钮重组存储变量

//配置参数的表单默认参数
var pageFormDataDefaultParams={};
//表单key对应json默认参数
var pageFormKeyDataDefaultParams={};
//缓存表单参数对应
var pageFormKeyDataParamsCache={};

var globalFunctions=new Array();//全局方法

var queryKendoGridsTimes={};//存储页面表格-表头加载值,已经加载过了为:1

//PE对接参数 正式
//var PE_URL = "http://192.168.225.50/";
//PE对接参数 测试
var PE_URL = "http://192.168.225.50:8001/";
var PE_ClientId = "e1611bdf-cf04-4f3b-a38c-4c2713167c2e";
var PE_ClientName = "lims";
var ClientPwd = "519c4835d5f44a51a4b305eff8e6f5a6";



//U8对接参数
//var U8XML_URL = "http://bj01ufi02.bmk.local";
var U8XML_URL = "http://bj01ufi04.bmk.local";


//JIRRA对接参数 正式
//var JIRRA_URL = "api.bmk.local";
//JIRRA对接参数 测试
var JIRRA_URL = "api-test.biocloud.net";
//百谱云后台启动正式线接口：http://api.bestms.cn/api/capital/pool/huaKai/start
//百谱云后台启动测试线接口：http://***********:8084/api/capital/pool/huaKai/start
var JIRRA_URL_BP="api.bestms.cn";
//var JIRRA_URL_BP="***********:8084";
// 百谱JiraID 测试线
var JiraID_BP = "customfield_19202";
// 百谱JiraID 正式线
//var JiraID_BP = "customfield_19503";



/**
 * 分布式-服务器-单服务请求
 */
var codeSaveToTomcats={"127.0.0.1":apiPath};

var Browser;
