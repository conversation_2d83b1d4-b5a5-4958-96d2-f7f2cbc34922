
function createGridWithModel(gridName, dataSource,columns,editable,command) {
             
            var dataSource = dataSource;
            var columns = columns;

            var gridOptions = {
                toolbar: ["create", "save", "cancel"],
                dataSource: dataSource,
                columns: columns,
                pageable: true,
                // editable: editable,
                height: "100%"};
            command = { command: "destroy", title: " ", width: 170 };
            columns.push(command);
            $("#" + gridName).kendoGrid(gridOptions);        
}


function createGridWithUrl(gridName, baseUrl) {
    $.ajax({
        url: baseUrl,
        type:"POST",
        data:{"query":"query_mis_user","objects":[]},       
        success: function (response) {
            
            var sampleDataItem = response.objects[0];
            console.log(sampleDataItem);
            var model = generateModel(sampleDataItem);
            var dataSource = generateDataSource(baseUrl, model);
            var columns = generateColumns(sampleDataItem);
            var gridOptions = {
                toolbar: ["create", "save", "cancel"],
                dataSource: dataSource,
                columns: columns,
                pageable: true,
                // editable: editable,
                height: 450
            };

            columns.push({ command: "destroy", title: " ", width: 170 });

            $("#" + gridName).kendoGrid(gridOptions);
        }
    });
}

function generateColumns(sampleDataItem) {
    var columnNames = Object.keys(sampleDataItem);
    return columnNames.map(function (name) {
        var isIdField = name.indexOf("ID") !== -1;
        return {
            field: name,
            width: (isIdField ? 40 : 200),
            title: (isIdField ? "Id" : name)
        };
    });
}

var dateFields = [];

function generateModel(sampleDataItem) {
    var model = {};
    var fields = {};
    
    for (var property in sampleDataItem) {
        
        if (property.indexOf("ID") !== -1) {
            model["id"] = property;
        }
        var propType = typeof sampleDataItem[property];
        if (propType === "number") {
            fields[property] = {
                type: "number"
            };
            if (model.id === property) {
                fields[property].editable = false;
            }
        } else if (propType === "boolean") {
            fields[property] = {
                type: "boolean"
            };
        } else if (propType === "string") {
            var parsedDate = kendo.parseDate(sampleDataItem[property]);
            if (parsedDate) {
                fields[property] = {
                    type: "date"
                };
                dateFields[property] = true;
            }
        }
    }
    model.fields = fields;
    
    return model;
}

function generateDataSource(baseURL, model) {
    var dataSource = {
        transport: {
            read: {
                url: "http://127.0.0.1:8080/system/jdbc/query/one/table",
                type: "post",
                dataType: "json",
                data: {"query":"query_mis_user","objects":[]}
        },
        create: {
            url: baseURL + "Create"
        },
        update: {
            url: baseURL + "Update"
        },
        destroy: {
            url: baseURL + "Destroy"
        },
        parameterMap: function (options, operation) {
                if (operation !== "read" && options.models) {
                    return { models: kendo.stringify(options.models) };
                }
            }
        },
        batch: true,
        schema: {
            model: model
        },
        pageSize: 20
    };
    return dataSource;
}
