/*!
 * Kendo UI Admin v1.0.0 by IKKI & Amikoko - https://ikki2000.github.io/
 * Copyright 2018-2019 IKKI Studio
 * Released under the MIT License - http://localhost:5500/LICENSE
 */

/* JS for TabStrip | Written by IKKI | 2018-02-03 */

// 选项卡标题、内容及路由创建
var tabText = '',
    tabContent = '',
    router = new kendo.Router({
        change: function (e) {
            $('#inProgress').css('display', 'flex');
            $('#inProgress .progress-bar').removeClass('w-100').addClass('animated');
            // tokenAuth();            
            // getMessage();            
            // getNotice();            
            //showPath();//e.url.split('/')[e.url.split('/').length - 1]
        },
        routeMissing: function () {            
            linkTo('/admin/views/404','错误404','fa-home');
        }
    });

// 路由路径
router.route('(/:lv1)(/:lv2)(/:lv3)(/:lv4)(/:lv5)', function (lv1, lv2, lv3, lv4, lv5, params){
    var lvs = [lv1, lv2, lv3, lv4, lv5],
        routePath = '',
        routeId = '',
        routeFile = '',
        tabStrip = $('#tab').data('kendoTabStrip');

    $.each(lvs, function (i, lv) {
        if (lv) {
            if(i==0){
                routePath +=  lv;
                routeId += lv;
            }
            else{
                routePath += '/' + lv;
                routeId += '-' + lv;
            }
            routeFile = lv;
        }
    });

    if ($('#tab-' + routeId).length < 1) {
         // 选项卡添加
         var menuTxt ="选项卡";
         var menuClass="fa-map-signs";
         if(wholeParamsTabName&&wholeParamsTabName!=""){
            menuTxt=wholeParamsTabName;
         }else{
            document.location.href="../admin/index_tabstrip.html";
            return;
         }
        //  if(wholeParamsTaIcon&&wholeParamsTabIcon!=""){
        //     menuClass=wholeParamsTabIcon;
        //  }
        $.get(path + routePath + '.html', function (temp) {
            $('#template').html(temp);
            if (routeId === 'admin-views-home') {
                tabText = '<span id="tab-' + routeId + '"><i class="fas fa-home"></i>首页<small>Home</small></span>';
            } else if (routeFile === 'admin-views-404') {
                tabText = '<span id="tab-' + routeId + '"><i class="fas fa-info-circle"></i>404<small>Error</small><i class="fas fa-times-circle"></i></span>';
            } else {
                tabText = '<span id="tab-' + routeId + '"><i class="fas '+menuClass+'"></i><small>'+ menuTxt +'</small></span><i class="fas fa-times-circle"></i></span>';
            }
           
            // tabContent = $(new kendo.View(routeId + 'Temp', { wrap: false }).render()).parent().html();
            tabContent=temp;
            console.log(tabContent);
            tabContent=funcConfig(tabContent,routeId);
            console.log("tabContent-->"+tabContent);
            tabStrip.append({
                text: tabText,
                content: tabContent,
                encoded: false
            });            
            var len = tabStrip.items().length - 1;
            tabStrip.select(len);

            if(routeId.endsWith("index")){//路由定义-指向第四层目录的index.html
                var opts=publicWinHandler({});
                var containment=opts.containment;
                if($(containment)){
                    var uuid;
                    if(globalTabWinIndexs[routeId]){
                        uuid=globalTabWinIndexs[routeId];
                    }else{
                        uuid=getUUID();
                        globalTabWinIndexs[routeId]=uuid;
                    }
                    $(containment).attr("id","index-"+uuid);
                    //loadInitInput($(containment),uuid);//初始页面可以不加
                }
            }

            $.getScript(path + routePath + '.js', function () {
                $('#inProgress .progress-bar').removeClass('animated').addClass('w-100');
                $('#inProgress').fadeOut();

                console.log(path + routePath+".js加载了...");
                if($("#index-"+uuid)){
                    loadInitPages($("#index-"+uuid),routeId);//后台控制加载html表单内容

                    loadInitInput($("#index-"+uuid),routeId);//替换表单ID属性
                    loadInitForm($("#index-"+uuid),routeId);//初始化表单
                }
                funcExce(routeId+"init",params);//初始化方法执行
            });

        }).fail(function () {
            linkTo('/admin/views/404','错误404','fa-home');
        });

    } else {
        // 选项卡激活
        tabStrip.activateTab($('#tab-' + routeId).closest('.k-item'));
        // $.getScript(path + routePath + '.js', function () {
        //     $('#inProgress .progress-bar').removeClass('animated').addClass('w-100');
        //     $('#inProgress').fadeOut();
        // });
    }
});

// 路由根目录
router.route('/', function () {
    linkTo('/admin/views/home','首页','fa-home');
});


// 路由导航链接
function linkTo(links,tabName,tabIcon) {
    wholeParamsTabName=tabName;
    wholeParamsTabIcon=tabIcon;
    console.log(links+tabName+tabIcon);
    //globalTabParameters[]
    // if(links.endsWith("index")){
    //     $('#tab'+replaceUrl(links)).find("small").each(function(){
    //         $(this).parent().click();
    //         return;//存在对应页签-页签未关闭直接跳转-返回
    //     });
    // }
    router.navigate(links);
}

// 初始化
$(function () {
    // 选项卡创建
    $('#tab').kendoTabStrip({
        tabPosition: "top",
        animation: false,
        select: function (e) {
            var selected = $(e.item).find('.k-link').children('span').attr('id').split('-')[1];            
            if (selected === 'admin-views-home') {
                //router.navigate('/admin/views/home');
                linkTo('/admin/views/home','首页','fa-home');
            } else if (selected === 'admin-views-404') {
                linkTo('/admin/views/404','错误404','fa-home');
            } 
            // else {
            //     var url = selected.replace(/\./g,"/");
            //     console.log(url);
            //     // router.navigate(url);
            // }
        },
        // activate: function (e) {
        //     $('#inProgress .progress-bar').removeClass('animated').addClass('w-100');
        //     $('#inProgress').fadeOut();
        // }
    });
    var tabStrip = $('#tab').data('kendoTabStrip');

    // 选项卡删除
    tabStrip.tabGroup.on('click', '.fa-times-circle', function (e) {
        var currli=$(e.target).parent().parent();
        if(currli.hasClass("k-state-active")){
            var prevli=currli.prev();
            if( prevli.length && prevli.length==1 ){//切换到上一个
                prevli.click();//要切换到上一个的路由才行
                var span=prevli.find("small").parent();
                var tabName=span.attr("id");
                if(tabName){
                    if(tabName.length>3){
                        var links=replacePathValue(tabName.substring(3));
                        router.navigate(links);
                    }
                }
            }else{
                //切换首页
                linkTo('/admin/views/home','首页','fa-home');
            }
        }
        tabStrip.remove($(e.target).closest('.k-item'));
    });

    // 选项卡拖放排序
    $('#tab ul.k-tabstrip-items').kendoSortable({
        container: '#tab ul.k-tabstrip-items',
        filter: 'li.k-item',
        axis: 'x',
        cursor: 'move',
        hint: function (e) {
            //return $('<div id="hint" class="k-widget k-header k-tabstrip"><ul class="k-tabstrip-items k-reset"><li class="k-item k-state-active k-tab-on-top">' + e.html() + '</li></ul></div>');
        },
        start: function (e) {
            tabStrip.select(e.item);
        },
        change: function (e) {
            var reference = tabStrip.tabGroup.children().eq(e.newIndex);
            if (e.oldIndex < e.newIndex) {
                tabStrip.insertAfter(e.item, reference);
            } else {
                tabStrip.insertBefore(e.item, reference);
            }
        },
        placeholder: function (e) {
            return e.clone().css({
                'opacity': .3,
                'border': '1px dashed #666'
            });
        },
        end: function (e) {
            tabStrip.activateTab(e.item);
        }
    });
    // 选项卡右键关联菜单
    $('#contextMenu').kendoContextMenu({
        target: '#tab ul.k-tabstrip-items',
        dataSource: [
            // {
            //     text: '<i class="fas fa-times"></i>关闭当前页',
            //     encoded: false,
            //     url: 'javascript:closeThis();'
            // },
            // {
            //     text: '<i class="far fa-times-circle"></i>关闭其他页',
            //     encoded: false,
            //     url: 'javascript:closeOthers();'
            // },
            {
                text: '<i class="fas fa-times-circle"></i>关闭所有页',
                encoded: false,
                url: 'javascript:closeAll();'
            }
        ]
    });
    // 路由启动
    router.start();
    // 选项卡首页
    //linkTo('/admin/views/home','首页','fa-home');
});

// 关闭当前页
function closeThis() {
    console.log("closeThis");
    if ($('#tab li.k-state-active').find('#tab-admin-views-home').length < 1) {     
    console.log("closeThis find");

        $('#tab').data('kendoTabStrip').remove($('#tab li.k-state-active'));
        router.navigate('/admin/views/home');
    }
}

// 关闭其他页
function closeOthers() {
    $('#tab li.k-item').each(function () {
        if ($(this).find('#tab-admin-views-home').length < 1 && !($(this).hasClass('k-state-active'))) {
            $('#tab').data('kendoTabStrip').remove($(this));
        }
    });
}

// 关闭所有页
function closeAll() {
    $('#tab li.k-item span.k-link i.fa-times-circle').each(function () {
        // var home=$(this).find(".k-link #tab-admin-views-home");
        // if(home[0]){
        //     $(this).click();
        // }else{
        //     $('#tab').data('kendoTabStrip').remove($(this).closest('.k-item'));
        // }
        $(this).click();
    });
    //切换首页
    linkTo('/admin/views/home','首页','fa-home');
    //router.navigate('/admin/views/home'); 
}

// 选项卡刷新
function refresh() {
    $('#inProgress').css('display', 'flex');
    $('#inProgress .progress-bar').removeClass('w-100').addClass('animated');
    // tokenAuth();
    var strUrl = location.hash.split('#/')[1];
    var strId =  strUrl.replace(/\//g,"-");    

    $.get('/'+strUrl + '.html', function (temp) {
        $('#template').html(temp);
        $('#tab div.k-state-active').html($(new kendo.View(strId + 'Temp', { wrap: false }).render()).parent().html());
        $.getScript('/' + strUrl + '.js', function () {
            $('#inProgress .progress-bar').removeClass('animated').addClass('w-100');
            $('#inProgress').fadeOut();
        });
        
    });
}

function loadInitPages(obj,uuid){
    var pageFormArray=[];
    var pageFormKeys=[];
    obj.find("div[page-form-key],form[page-form-key]").each(function(){
        var pageFormKey=$(this).attr("page-form-key");
        var pageFormLoad=$(this).attr("page-form-load");
        var formid=$(this).attr("id");
        if(!formid){
            formid="form";//form表单id默认为form
        }
        pageFormArray.push({"this":$(this),"pageFormKey":pageFormKey,"pageFormLoad":pageFormLoad,"formid":formid});
        pageFormKeys.push(pageFormKey);
    });
    if(pageFormKeys.length>0){
        getHtmlByFormKeys(pageFormKeys);
    }
    var formsParamsJson={};
    for(var i=0;i<pageFormArray.length;i++){
        var pageFormJson=pageFormArray[i];
        var pageFormKey=pageFormJson["pageFormKey"];
        var pageFormLoad=pageFormJson["pageFormLoad"];
        var $this=pageFormJson["this"];
        var formid=pageFormJson["formid"];
        var html=getHtmlByFormKey(pageFormKey,uuid,formid);
        html=funcConfig(html,uuid);
        if(pageFormLoad=="before"||pageFormLoad=="start"||pageFormLoad=="first"||pageFormLoad=="prepend"){//加载在元素开头
            $this.prepend(html);
        }else if(pageFormLoad=="html"||pageFormLoad=="replace"){
            $this.html(html);
        }else if(pageFormLoad&&pageFormLoad.startsWith("#")){
            $this.find(pageFormLoad).html(html);
        }else{//加载在元素结尾
            $this.append(html);
        }
        setJsonParam(formsParamsJson,formid,pageFormKeyDataDefaultParams[pageFormKey]);
    }
    setJsonParam(pageFormDataDefaultParams,obj,formsParamsJson);

}

/**
 * 获取html代码-通过请求后端
 * @param {*} pageFormKey 页面对应的 formKey
 * @param {*} uuid -- pathValue
 * @param {*} formid  -- form
 */
function getHtmlByFormKeys(pageFormKeys){
    $.fn.ajaxPost({
        ajaxUrl:"system/config/pagefrom/getPagefromConfig",
        ajaxAsync: false,//同步
        ajaxType: 'post',
        ajaxData: {"pagefrom_id":pageFormKeys},
        succeed:function(res){
            var formsMap=res["formsMap"];
            if(formsMap){
                for(var index in formsMap){
                    setJsonParam(pageFormKeyDataParamsCache,index,formsMap[index]);
                }
            }
        },
        failed:function(res){
            alertMsg("加载失败!页签标识","error");
        }
    });
}

/**
 * 获取html代码-通过请求后端
 * @param {*} pageFormKey 页面对应的 formKey
 * @param {*} uuid -- pathValue
 * @param {*} formid  -- form
 */
function getHtmlByFormKey(pageFormKey,uuid,formid){
    var html="";
    if(pageFormKey){
        var result;
        if(pageFormKeyDataParamsCache[pageFormKey]){
            result=pageFormKeyDataParamsCache[pageFormKey];
        }else{
            $.fn.ajaxPost({
                ajaxUrl:"system/config/pagefrom/getPagefromConfig",
                ajaxAsync: false,//同步
                ajaxType: 'post',
                ajaxData: {"pagefrom_id":pageFormKey},
                succeed:function(res){
                    var formsMap=res["formsMap"];
                    if(formsMap){
                        for(var index in formsMap){
                            setJsonParam(pageFormKeyDataParamsCache,index,formsMap[index]);
                            result=formsMap[index];
                        }
                    }
                },
                failed:function(res){
                    alertMsg("加载失败!页签标识","error");
                }
            });
        } 
        if(result){
            var pagefromModuleList=result;
            var pageFormValues={};
            if(pagefromModuleList){
                for(var pagefrom=0;pagefrom<pagefromModuleList.length;pagefrom++){
                    console.log(pagefromModuleList[pagefrom]);
                    console.log(pagefromModuleList[pagefrom]["TYPE"]);
                    if(pagefromModuleList[pagefrom]["TYPE"]=="form"||pagefromModuleList[pagefrom]["TYPE"]=="search"){
                        setJsonParam(pageFormValues,pagefromModuleList[pagefrom]["ID"],pagefromModuleList[pagefrom]["fieldList"]);
                    }
                }
            }
            var dataDefaultParams={};
            var templateHtml="";
            for(var key in pageFormValues){
                if(pageFormValues[key]){
                    for(var k in pageFormValues[key]){
                        var value=pageFormValues[key][k];
                        if(value){
                            if(value["IS_NEWLINE"]=="Y"){//新的一行
                                html+=getHtmlByReplaceTemplate("formGroupRow",{content:templateHtml});
                                templateHtml="";
                            }
                            var templateId;
                            if(value["IS_SHOW"]=="N"){//隐藏的
                                templateId="hidden-0";
                            }else{
                                templateId=value["TEMPLATE_ID"];
                            }
                            var label="";
                            if(value["LABEL"]){
                                label=value["LABEL"];
                            }
                            var id="";
                            if(value["DB_TABLE_FIELD"]){
                                id=value["DB_TABLE_FIELD"];
                            }
                            var rules="";
                            if(value["DATA_CHECK"]){
                                rules=value["DATA_CHECK"];
                            }
                            var placeholder="";
                            if(value["PLACEHOLDER"]){
                                placeholder=value["PLACEHOLDER"];
                            }
    
                            var star="";
                            if(value["IS_REQUIRE"]=="Y"){
                                star=getHtmlByReplaceTemplate("starFontHtml");
                            }
    
                            if(templateId.startsWith("lineHr")){
                                label="";
                                if(placeholder!==""){
                                    placeholder="<span class=\"span-placeholder\" >"+placeholder+"</span>";
                                }
                            }
                            var templateJson={
                                label:label,
                                id:id,
                                name:id,
                                rules:rules,
                                placeholder:placeholder,
                                star:star
                            };
                            templateHtml+=getHtmlByReplaceTemplate(templateId,templateJson);
                            var dataType="";
                            if(value["DATA_SOURCE_TYPE"]){
                                dataType=value["DATA_SOURCE_TYPE"];
                            }
                            var dataParams="";
                            if(value["DATA_SOURCE_PARAMS"]){
                                dataParams=value["DATA_SOURCE_PARAMS"];
                            }
                            var defaultType="";
                            if(value["DEFAULT_TYPE"]){
                                defaultType=value["DEFAULT_TYPE"];
                            }
                            var defaultParams="";
                            if(value["DEFAULT_PARAMS"]){
                                defaultParams=value["DEFAULT_PARAMS"];
                            }
                            var configParams="";
                            if(value["CONFIG_PARAMS"]){
                                configParams=value["CONFIG_PARAMS"];
                            }
                            var val=getDataDefaultObjectParams(uuid,formid,templateId,dataType,dataParams,configParams,defaultType,defaultParams,placeholder,id);
                            if(val){//有所定义
                                setJsonParam(dataDefaultParams,id,val);
                            }
                        }
                    }
                }
            }
            setJsonParam(pageFormKeyDataDefaultParams,pageFormKey,dataDefaultParams);
            if(templateHtml!=""){
                html+=getHtmlByReplaceTemplate("formGroupRow",{content:templateHtml});
                templateHtml="";
            }
        }
    }
    return html;
}

/**
 * 通过 templateId 和替换的json获取已替换的html
 * @param {*} templateId 
 * @param {*} templateJson 
 */
function getHtmlByReplaceTemplate(templateId,templateJson){
    var defaults=getDefaultJsonByElement(templateId);
    templateJson=$.extend({}, defaults, templateJson);//继承
    var templateElement = $("#"+templateId).html(); 
    for(var key in templateJson){
        if(!templateJson[key]){
            templateJson[key]="";
        }
        templateElement=templateElement.replace(RegExp("{{"+key+"}}", "g"),templateJson[key]);
    }
    if(templateElement){
        return templateElement;
    }else{
        return "";
    }
}


/**
 * templateId--标签元素ID
 * 返回默认需要继承的参数内容
 * @param {*} templateId 
 */
function getDefaultJsonByElement(templateId){
    if(templateId){
        if(templateId.startsWith("input-")||templateId.startsWith("textarea-")||
        templateId.startsWith("date-")||templateId.startsWith("datetime-")||
        templateId.startsWith("multiSelect-")||templateId.startsWith("upload-")||
        templateId.startsWith("download-")||templateId.startsWith("dialogOpen-")||
        templateId.startsWith("lineHr-")||templateId.startsWith("autoComplete-")){
            return {
                label:"",
                id:"",
                name:"",
                rules:"",
                star:"",
                placeholder:"",
            };
        }else if(templateId.startsWith("select-")){
            return {
                label:"",
                id:"",
                name:"",
                star:"",
                rules:"",
            };
        }else if(templateId.startsWith("hidden-")){
            return {
                id:"",
                name:"",
                star:"",
            }
        }else if(templateId=="formGroupRow"){
            return {
                content:"",
            }
        }
    }
    return {};
}


/**
 * 解析数据源-默认值参数
 *  下拉-单选
    {
        id:"dropDownList",//下拉
        dataSource: [
            { text: '---请选择---', value: '' },
            { text: '固定文本默认值', value: '固定文本默认值' },
            { text: '当前操作人默认值', value: '当前操作人默认值' },
            { text: '当前日期时间默认值', value: '当前日期时间默认值' },
            { text: '其他默认值', value: '其他默认值' },
        ],
        value:"固定文本默认值"
    }
    //多选-文本
    { id:"multiSelect",//多选下拉
        dataSource: [
            { text: "北京", value: "北京" },
            { text: "上海", value: "上海" },
            { text: "深圳", value: "深圳" },
            { text: "厦门", value: "厦门" }
        ],
        value:["北京","厦门"]
    }
    //自动补充
    {
        id:"autoComplete",
        dataSource: ["","",""],
    }
    tableName:"sysMetadataMain"
 * 
 * 
 */
function getDataDefaultObjectParams(uuid,formid,templateId,dataType,dataParams,configParams,defaultType,defaultParams,placeholder,id){
    if(templateId.startsWith("select-")){
        var dataSource=[];
        var datasArray=[];
        if(dataType=="简单的下拉数据源"){
            if(dataParams&&dataParams!=""){
                var dataParamsArray=dataParams.split(";");
                if(placeholder&&placeholder!=""){
                    dataSource.push({ text: placeholder, value: '' });
                }else if(placeholder==""){//为空值。默认不需要此参数

                }else{
                    dataSource.push({ text: '---请选择---', value: '' });
                }
                for(var i=0;i<dataParamsArray.length;i++){
                    if(dataParamsArray[i]==""){
                        continue;
                    }
                    dataSource.push({ text: dataParamsArray[i], value: dataParamsArray[i] });
                    datasArray.push({ text: dataParamsArray[i], value: dataParamsArray[i] });
                }
            }
            if(configParams&&configParams!=""){
                try{
                    var array=[];
                    array=$.parseJSON(configParams);
                    for(var i=0;i<array.length;i++){
                        var json=array[i];
                        if(json && isObj(json)){
                            dataSource.push({ text: array[i]["text"], value: array[i]["value"] });
                            datasArray.push($.extend({}, json,{ text: array[i]["text"], value: array[i]["value"] }));
                        }
                    }
                }catch(e){
                    return ;
                }
            }
            if(defaultType=="固定文本默认值"){
                return {
                    id:"dropDownList",//下拉
                    dataSource: dataSource,
                    value:defaultParams,
                    datasArray:datasArray,
                }
            }else{
                return {
                    id:"dropDownList",//下拉
                    dataSource: dataSource,
                    datasArray:datasArray,
                }
            }
        }else if(dataType=="复杂的下拉数据源"){
            var json={};
            if(configParams&&configParams!=""){
                try{
                    json=$.parseJSON(configParams);
                }catch(e){
                    return ;
                }
            }
            var defaults={
                text:"",
                value:"",
            };
            var json=$.extend({}, defaults, json);//继承
            var arr=getDataSourceByConfig(dataParams,json["objects"],json["search"]);
            for(var i=0;i<arr.length;i++){
                var obj=arr[i];
                var text=obj[json["text"]];
                var value;
                if(obj[json["value"]]){
                    value=obj[json["value"]];
                }else{
                    value=text;
                }
                var oJson={};
                for(var k in json){
                    setJsonParam(oJson,k,obj[json[k]]);
                }
                dataSource.push({text:text,value:value});
                datasArray.push($.extend({}, oJson, {text:text,value:value}));
            }
            if(defaultType=="固定文本默认值"){
                return {
                    id:"dropDownList",//下拉
                    dataSource: dataSource,
                    value:defaultParams,
                    datasArray:datasArray,
                }
            }else{
                return {
                    id:"dropDownList",//下拉
                    dataSource: dataSource,
                    datasArray:datasArray,
                }
            }
        }
    }
    if(templateId.startsWith("autoComplete-")){
        var dataSource=[];
        var datasArray=[];
        if(dataType=="简单的下拉数据源"){
            if(dataParams&&dataParams!=""){
                var dataParamsArray=dataParams.split(";");
                for(var i=0;i<dataParamsArray.length;i++){
                    if(dataParamsArray[i]==""){
                        continue;
                    }
                    dataSource.push(dataParamsArray[i]);
                    datasArray.push({"text":dataParamsArray[i]});
                }
            }
            if(configParams&&configParams!=""){
                try{
                    var array=[];
                    array=$.parseJSON(configParams);
                    for(var i=0;i<array.length;i++){
                        var json=array[i];
                        if(json && isObj(json)){
                            dataSource.push(array[i]["text"]);
                            datasArray.push({"text":array[i]["text"]});
                        }
                    }
                }catch(e){
                    return ;
                }
            }
            if(defaultType=="固定文本默认值"){
                return {
                    id:"autoComplete",//自动补充
                    dataSource: dataSource,
                    value:defaultParams,
                    datasArray:datasArray,
                }
            }else{
                return {
                    id:"autoComplete",//自动补充
                    dataSource: dataSource,
                    datasArray:datasArray,
                }
            }
        }else if(dataType=="复杂的下拉数据源"){
            var json={};
            if(configParams&&configParams!=""){
                try{
                    json=$.parseJSON(configParams);
                }catch(e){
                    return ;
                }
            }
            var defaults={
                text:"",
                value:"",
            };
            var json=$.extend({}, defaults, json);//继承
            var arr=getDataSourceByConfig(dataParams,json["objects"],json["search"]);
            var dataSource=[];
            for(var i=0;i<arr.length;i++){
                var obj=arr[i];
                var text=obj[json["text"]];
                var oJson={};
                for(var k in json){
                    setJsonParam(oJson,k,obj[json[k]]);
                }
                dataSource.push(text);
                datasArray.push($.extend({}, oJson, {text:text}));
            }
            if(defaultType=="固定文本默认值"){
                return {
                    id:"autoComplete",//自动补充
                    dataSource: dataSource,
                    value:defaultParams,
                    datasArray:datasArray,
                }
            }else{
                return {
                    id:"autoComplete",//自动补充
                    dataSource: dataSource,
                    datasArray:datasArray,
                }
            }
        }
    }
    if(templateId.startsWith("multiSelect-")){
        var dataSource=[];
        var datasArray=[];
        if(dataType=="简单的下拉数据源"){
            if(dataParams&&dataParams!=""){
                var dataParamsArray=dataParams.split(";");
                if(placeholder&&placeholder!=""){
                    dataSource.push({ text: placeholder, value: '' });
                }else if(placeholder==""){//为空值。默认不需要此参数

                }else{
                    dataSource.push({ text: '---请选择---', value: '' });
                }
                for(var i=0;i<dataParamsArray.length;i++){
                    if(dataParamsArray[i]==""){
                        continue;
                    }
                    dataSource.push({ text: dataParamsArray[i], value: dataParamsArray[i] });
                    datasArray.push({ text: dataParamsArray[i], value: dataParamsArray[i] });
                }
            }
            if(configParams&&configParams!=""){
                try{
                    var array=[];
                    array=$.parseJSON(configParams);
                    for(var i=0;i<array.length;i++){
                        var json=array[i];
                        if(json && isObj(json)){
                            dataSource.push({ text: array[i]["text"], value: array[i]["value"] });
                            datasArray.push($.extend({}, json,{ text: array[i]["text"], value: array[i]["value"] }));
                        }
                    }
                }catch(e){
                    return ;
                }
            }
            if(defaultType=="固定文本默认值"){
                return {
                    id:"multiSelect",//下拉
                    dataSource: dataSource,
                    value:defaultParams.split(";"),
                    datasArray:datasArray,
                }
            }else{
                return {
                    id:"multiSelect",//下拉
                    dataSource: dataSource,
                    datasArray:datasArray,
                }
            }
        }else if(dataType=="复杂的下拉数据源"){
            var json={};
            if(configParams&&configParams!=""){
                try{
                    json=$.parseJSON(configParams);
                }catch(e){
                    return ;
                }
            }
            var defaults={
                text:"",
                value:"",
            };
            var json=$.extend({}, defaults, json);//继承
            var arr=getDataSourceByConfig(dataParams,json["objects"],json["search"]);
            var dataSource=[];
            for(var i=0;i<arr.length;i++){
                var obj=arr[i];
                var text=obj[json["text"]];
                var value;
                if(obj[json["value"]]){
                    value=obj[json["value"]];
                }else{
                    value=text;
                }
                var oJson={};
                for(var k in json){
                    setJsonParam(oJson,k,obj[json[k]]);
                }
                dataSource.push({text:text,value:value});
                datasArray.push($.extend({}, oJson, {text:text,value:value}));
            }
            if(defaultType=="固定文本默认值"){
                return {
                    id:"multiSelect",//自动补充
                    dataSource: dataSource,
                    value:defaultParams.split(";"),
                    datasArray:datasArray,
                }
            }else{
                return {
                    id:"multiSelect",//自动补充
                    dataSource: dataSource,
                    datasArray:datasArray,
                }
            }
        }
    }
    if(templateId.startsWith("dialogOpen-")){
        if(dataType=="弹框组件数据源"){
            //配置一个名称---
            var dailogJson;
            console.log("组件名称");
            console.log(dataParams);
            console.log("组件参数configParams:");

            var json={};
            if(configParams&&configParams!=""){
                try{
                    json=$.parseJSON(configParams);
                }catch(e){
                    return ;
                }
            }
            var defaults={
                title:dataParams,
                query:componentQueryJson[dataParams],
            };
            var json=$.extend({}, defaults, json);//继承
            var mapping=json["mapping"];//键值映射
            if(isObj(mapping)){                              
                dailogJson={
                    name:"数据源",//组件名称
                    componentId:id+uuid,
                    title:json["title"],
                    params:json,
                    settings:function(obj,value){
                        console.log(obj);
                        if(isArray(obj)){
                            var mappingParams={};
                            for(var key in mapping){
                                var objv="";
                                for(var i=0;i<obj.length;i++){
                                    var v=obj[i][mapping[key]];
                                    if(v!=""){
                                        objv+=";"+v;
                                    }
                                }
                                if(objv.startsWith(";")){
                                    objv=objv.substring(1);
                                }
                                setJsonParam(mappingParams,key,objv);
                            }
                            getInfo(formid,uuid,mappingParams,null,function(viewModel,params){
                                var callBackJson={
                                    "id":id,
                                    "formid":formid,
                                    "viewModel":viewModel,
                                    "params":params,
                                }
                                funcExce(uuid+"dailogOpenCallBack",callBackJson);
                            });
                        }else{
                            var mappingParams={};
                            for(var key in mapping){
                                setJsonParam(mappingParams,key,obj[mapping[key]]);
                            }
                            getInfo(formid,uuid,mappingParams,null,function(viewModel,params){
                                var callBackJson={
                                    "id":id,
                                    "formid":formid,
                                    "viewModel":viewModel,
                                    "params":params,
                                }
                                funcExce(uuid+"dailogOpenCallBack",callBackJson);
                            });
                        }
                    }
                }
            }else{
                dailogJson={
                    name:"数据源",//组件名称
                    componentId:id+uuid,
                    title:json["title"],
                    params:json,
                };
            }
            var dailogJsons=$.extend({}, componentDialogCacheArray[id+uuid]);
            setJsonParam(dailogJsons,formid,dailogJson);
            componentDialogCacheArray[id+uuid+formid]=dailogJsons;

            if(globalFunctions[uuid+"dailogOpen"]){

            }else{//不存在此方法-即注册默认方法
                funcPush(uuid+"dailogOpen",function(componentId,componentName,obj){
                    var formidTemp;
                    formidTemp=formid;
                    if(formid=="form"){
                       
                    }else{
                        var a=parentNodeInclude(obj,"form-horizontal","class");//对象
                        if(a){
                            if(a.id){
                                formidTemp=a.id.replace(uuid,"");
                            }
                        }
                    }
                    var data=componentDialogCacheArray[componentId+formidTemp];
                    // var cloneData = $.extend({},data);
                    var cloneData = $.extend(true,{}, data);//深度复制
                    var formJson=getJsonByForm(formidTemp,uuid);//获取到表单的JSON
                    var searchJsonTemp={};
                    if(formJson){
                        if(isObj(cloneData[formidTemp]["params"])){
                            var ajaxData=cloneData[formidTemp]["params"];
                            if(isObj(ajaxData["search"])){//如果查询条件有值，可能是从表单上获取的参数
                                var searchJson=ajaxData["search"];
                                searchJsonTemp = Object.assign({}, searchJson);//浅克隆
                                for(var index in searchJson){
                                    var value=searchJson[index];
                                    if(typeof(value)=="string"){
                                        if(value.startsWith("$")){//获取表单的参数-映射参数-不作处理
                                            var mappingValue=formJson[value.replace("$","").replace("{","").replace("}","")];
                                            if(mappingValue){
                                                setJsonParam(searchJson,index,mappingValue);
                                            }else{
                                                setJsonParam(searchJson,index,"");
                                            }
                                        }
                                    }
                                }
                                setJsonParam(ajaxData,"search",searchJson);
                            }
                            setJsonParam(cloneData[formidTemp],"params",ajaxData);
                        }
                    }
                    openComponent(cloneData[formidTemp]);
                });
            }
        }
    }
    //编号生成器控件
    if(templateId.startsWith("numberGenerate-")){
        if(dataType=="触发组件数据源"){
            console.log("组件名称");
            console.log(dataParams);
            console.log("组件参数configParams:");
            var defaults={
                limit:"年",
                type:"编号",
            };
            var json={};
            if(configParams&&configParams!=""){
                try{
                    json=$.parseJSON(configParams);
                }catch(e){
                    return ;
                }
            }
            var json=$.extend({}, defaults, json);//继承

            componentGenerateCacheArray[id+uuid+formid]=json;
            if(globalFunctions[uuid+"numberGenerate"]){

            }else{//不存在此方法-即注册默认方法
                
                funcPush(uuid+"numberGenerate",function(componentId,componentName,obj){
                    var formidTemp;
                    formidTemp=formid;
                    if(formid=="form"){
                       
                    }else{
                        var a=parentNodeInclude(obj,"form-horizontal","class");//对象
                        if(a){
                            if(a.id){
                                formidTemp=a.id.replace(uuid,"");
                            }
                        }
                    }
                    var ajaxData=componentGenerateCacheArray[componentId+formidTemp];
                    //var cloneData = $.extend({}, ajaxData);
                    var cloneData = $.extend(true,{}, ajaxData);//深度复制
                    var formJson=getJsonByForm(formidTemp,uuid);//获取到表单的JSON
                    if(formJson){
                        for(var index in cloneData){
                            var value=cloneData[index];
                            if(typeof(value)=="string"){
                                if(value.startsWith("$")){//获取表单的参数-映射参数-不作处理
                                    var mappingValue=formJson[value.replace("$","").replace("{","").replace("}","")];
                                    if(mappingValue){
                                        setJsonParam(cloneData,index,mappingValue);
                                    }else{
                                        setJsonParam(cloneData,index,"");
                                    }
                                }
                            }
                        }
                    }
                    //请求后台-参数
                    $.fn.ajaxPost({
                        ajaxType:"post",
                        ajaxUrl:"system/settings/code/auto",
                        ajaxData:cloneData,
                        succeed:function(result){
                            if(result["code"]>0){
                                var data=result["info"];
                                var numberCode=data["numberCode"];
                                var infoJson={};
                                setJsonParam(infoJson,id,numberCode);
                                if(numberCode){
                                    getInfo(formidTemp,uuid,infoJson,null,function(viewModel,params){
                                        var callBackJson={
                                            "id":id,
                                            "formid":formidTemp,
                                            "viewModel":viewModel,
                                            "params":params,
                                        }
                                        funcExce(uuid+"dailogOpenCallBack",callBackJson);
                                    });
                                }
                            }else{
                                alertMsg("操作失败","error");
                            }
                        }
                    });
                    //openComponent(componentDialogCacheArray[componentId]);
                });
            }
        }
    }
    
    if(defaultType=="固定文本默认值"){
        if(defaultParams){
            return defaultParams;
        }
    }else if(defaultType=="当前操作人默认值"){
        var limsUser=getLimsUser();
        if(limsUser){
            return limsUser["name"];
        }
    }else if(defaultType=="当前操作人ID默认值"){
        var limsUser=getLimsUser();
        if(limsUser){
            return limsUser["id"];
        }
    }else if(defaultType=="当前账套默认值"){
        var limsUser=getLimsUser();
        if(limsUser){
            return limsUser["zt"];
        }
    }else if(defaultType=="当前日期时间默认值"){
        return sysNowTimeFuncParams["sysNowTime"];
    }else if(defaultType=="其他默认值"){

    }
}