{"result": "y", "msg": "数据获取错误，请重试！", "data": [{"TaskID": 1, "OwnerID": "Goddess", "Avatar": "img/temp/Goddess.png", "Title": "拯救雅典娜", "Description": "纱织中箭~ 必须在十二小时之内突破十二宫到达教皇殿请教皇拔箭！", "Start": "2019-08-15 11:00", "End": "2019-08-15 23:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 2, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "等待修复圣衣", "Description": "天马座青铜圣衣修复中！", "Start": "2019-08-15 11:00", "End": "2019-08-15 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 3, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "等待修复圣衣", "Description": "天龙座青铜圣衣修复中！", "Start": "2019-08-15 11:00", "End": "2019-08-15 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 4, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "等待修复圣衣", "Description": "天鹅座青铜圣衣修复中！", "Start": "2019-08-15 11:00", "End": "2019-08-15 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 5, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "等待修复圣衣", "Description": "仙女座青铜圣衣修复中！", "Start": "2019-08-15 11:00", "End": "2019-08-15 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 6, "OwnerID": "<PERSON><PERSON>", "Avatar": "img/temp/Aries.png", "Title": "修复四件青铜圣衣", "Description": "修复天马座、天龙座、天鹅座、仙女座四件青铜圣衣！", "Start": "2019-08-15 11:00", "End": "2019-08-15 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 7, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "突破金牛宫", "Description": "跟金牛座黄金圣斗士阿鲁迪巴战斗！", "Start": "2019-08-15 12:00", "End": "2019-08-15 13:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 8, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "被击晕", "Description": "被金牛座黄金圣斗士阿鲁迪巴击晕！", "Start": "2019-08-15 12:00", "End": "2019-08-15 13:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 9, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "被击晕", "Description": "被金牛座黄金圣斗士阿鲁迪巴击晕！", "Start": "2019-08-15 12:00", "End": "2019-08-15 13:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 10, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "被击晕", "Description": "被金牛座黄金圣斗士阿鲁迪巴击晕！", "Start": "2019-08-15 12:00", "End": "2019-08-15 13:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 11, "OwnerID": "<PERSON><PERSON>", "Avatar": "img/temp/Taurus.png", "Title": "守卫金牛宫", "Description": "跟天马座青铜圣斗士星矢战斗！", "Start": "2019-08-15 12:00", "End": "2019-08-15 13:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 12, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过双子宫", "Description": "直接通过双子宫！", "Start": "2019-08-15 13:00", "End": "2019-08-15 13:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 13, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "通过双子宫", "Description": "直接通过双子宫！", "Start": "2019-08-15 13:00", "End": "2019-08-15 13:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 14, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "闯双子宫", "Description": "跟双子座黄金圣斗士撒加战斗！", "Start": "2019-08-15 13:00", "End": "2019-08-15 14:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 15, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "突破双子宫", "Description": "跟双子座黄金圣斗士撒加战斗！", "Start": "2019-08-15 13:00", "End": "2019-08-15 14:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 16, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "被冰封", "Description": "被水瓶座黄金圣斗士卡妙冰封！", "Start": "2019-08-15 14:00", "End": "2019-08-15 18:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 17, "OwnerID": "Gemini", "Avatar": "img/temp/Gemini.png", "Title": "遥控双子宫", "Description": "跟仙女座青铜圣斗士瞬战斗！", "Start": "2019-08-15 13:00", "End": "2019-08-15 14:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 18, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过巨蟹宫", "Description": "直接通过巨蟹宫！", "Start": "2019-08-15 13:30", "End": "2019-08-15 14:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 19, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "突破巨蟹宫", "Description": "跟巨蟹座黄金圣斗士迪斯马斯克战斗！", "Start": "2019-08-15 13:30", "End": "2019-08-15 15:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 20, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "通过巨蟹宫", "Description": "直接通过巨蟹宫！", "Start": "2019-08-15 14:00", "End": "2019-08-15 15:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 21, "OwnerID": "Cancer", "Avatar": "img/temp/Cancer.png", "Title": "守卫巨蟹宫", "Description": "跟天龙座青铜圣斗士紫龙战斗！", "Start": "2019-08-15 14:00", "End": "2019-08-15 15:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 22, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "突破狮子宫", "Description": "跟狮子座黄金圣斗士艾欧里亚战斗！", "Start": "2019-08-15 14:30", "End": "2019-08-15 16:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 23, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "通过狮子宫", "Description": "直接通过狮子宫！", "Start": "2019-08-15 15:00", "End": "2019-08-15 16:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 24, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "通过狮子宫", "Description": "直接通过狮子宫！", "Start": "2019-08-15 15:00", "End": "2019-08-15 16:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 25, "OwnerID": "<PERSON>", "Avatar": "img/temp/Leo.png", "Title": "守卫狮子宫", "Description": "跟天马座青铜圣斗士星矢战斗！", "Start": "2019-08-15 15:00", "End": "2019-08-15 16:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 26, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "被击晕", "Description": "被处女座黄金圣斗士沙加击晕！", "Start": "2019-08-15 16:00", "End": "2019-08-15 17:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 27, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "被击晕", "Description": "被处女座黄金圣斗士沙加击晕！", "Start": "2019-08-15 16:00", "End": "2019-08-15 17:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 28, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "被击晕", "Description": "被处女座黄金圣斗士沙加击晕！", "Start": "2019-08-15 16:00", "End": "2019-08-15 17:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 29, "OwnerID": "Phoenix", "Avatar": "img/temp/Phoenix.png", "Title": "突破处女宫", "Description": "跟处女座黄金圣斗士沙加战斗！", "Start": "2019-08-15 16:00", "End": "2019-08-15 17:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 30, "OwnerID": "Virgo", "Avatar": "img/temp/Virgo.png", "Title": "守卫处女宫", "Description": "跟凤凰座青铜圣斗士一辉战斗！", "Start": "2019-08-15 16:00", "End": "2019-08-15 17:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 31, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过天秤宫", "Description": "劈开冰棺后直接通过天秤宫！", "Start": "2019-08-15 17:00", "End": "2019-08-15 18:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 32, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "通过天秤宫", "Description": "劈开冰棺后直接通过天秤宫！", "Start": "2019-08-15 17:00", "End": "2019-08-15 18:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 33, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "通过天秤宫", "Description": "劈开冰棺后直接通过天秤宫！", "Start": "2019-08-15 17:00", "End": "2019-08-15 18:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 34, "OwnerID": "Phoenix", "Avatar": "img/temp/Phoenix.png", "Title": "时空缝隙", "Description": "昏迷在时空缝隙！", "Start": "2019-08-15 17:00", "End": "2019-08-15 22:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 35, "OwnerID": "Libra", "Avatar": "img/temp/Libra.png", "Title": "出借黄金圣衣", "Description": "天秤座黄金圣斗士童虎将黄金圣衣借给青铜圣斗士们救活冰河！", "Start": "2019-08-15 17:00", "End": "2019-08-15 18:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 36, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过天蝎宫", "Description": "直接通过天蝎宫！", "Start": "2019-08-15 18:00", "End": "2019-08-15 19:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 37, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "通过天蝎宫", "Description": "直接通过天蝎宫！", "Start": "2019-08-15 18:00", "End": "2019-08-15 19:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 38, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "突破天蝎宫", "Description": "跟天蝎座黄金圣斗士米罗战斗！", "Start": "2019-08-15 18:00", "End": "2019-08-15 19:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 39, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "通过天蝎宫", "Description": "直接通过天蝎宫！", "Start": "2019-08-15 18:00", "End": "2019-08-15 19:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 40, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Scorpion.png", "Title": "守卫天蝎宫", "Description": "跟天鹅座青铜圣斗士冰河战斗！", "Start": "2019-08-15 18:00", "End": "2019-08-15 19:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 41, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过射手宫", "Description": "直接通过射手宫！", "Start": "2019-08-15 19:00", "End": "2019-08-15 20:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 42, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "通过射手宫", "Description": "直接通过射手宫！", "Start": "2019-08-15 19:00", "End": "2019-08-15 20:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 43, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "通过射手宫", "Description": "直接通过射手宫！", "Start": "2019-08-15 19:00", "End": "2019-08-15 20:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 44, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "通过射手宫", "Description": "直接通过射手宫！", "Start": "2019-08-15 19:00", "End": "2019-08-15 20:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 45, "OwnerID": "<PERSON><PERSON><PERSON><PERSON>", "Avatar": "img/temp/Sagittarius.png", "Title": "守护射手宫", "Description": "将雅典娜托付给青铜圣斗士们！", "Start": "2019-08-15 19:00", "End": "2019-08-15 20:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 46, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过山羊宫", "Description": "直接通过山羊宫！", "Start": "2019-08-15 20:00", "End": "2019-08-15 21:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 47, "OwnerID": "Dragon", "Avatar": "img/temp/Dragon.png", "Title": "突破山羊宫", "Description": "跟山羊座黄金圣斗士修罗战斗！", "Start": "2019-08-15 20:00", "End": "2019-08-15 21:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 48, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "通过山羊宫", "Description": "直接通过山羊宫！", "Start": "2019-08-15 20:00", "End": "2019-08-15 21:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 49, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "通过山羊宫", "Description": "直接通过山羊宫！", "Start": "2019-08-15 20:00", "End": "2019-08-15 21:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 50, "OwnerID": "Capricorn", "Avatar": "img/temp/Capricorn.png", "Title": "守卫山羊宫", "Description": "跟天龙座青铜圣斗士紫龙战斗！", "Start": "2019-08-15 20:00", "End": "2019-08-15 21:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 51, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过水瓶宫", "Description": "直接通过水瓶宫！", "Start": "2019-08-15 21:00", "End": "2019-08-15 22:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 52, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Cygnus.png", "Title": "突破水瓶宫", "Description": "跟水瓶座黄金圣斗士卡妙战斗！", "Start": "2019-08-15 21:00", "End": "2019-08-15 22:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 53, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "通过水瓶宫", "Description": "直接通过水瓶宫！", "Start": "2019-08-15 21:00", "End": "2019-08-15 22:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 54, "OwnerID": "A<PERSON><PERSON>", "Avatar": "img/temp/Aquarius.png", "Title": "守卫水瓶宫", "Description": "跟天鹅座青铜圣斗士冰河战斗！", "Start": "2019-08-15 21:00", "End": "2019-08-15 22:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 55, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "通过双鱼宫", "Description": "直接通过双鱼宫！", "Start": "2019-08-15 22:00", "End": "2019-08-15 22:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 56, "OwnerID": "Andromeda", "Avatar": "img/temp/Andromeda.png", "Title": "突破双鱼宫", "Description": "跟双鱼座黄金圣斗士阿布罗狄战斗！", "Start": "2019-08-15 22:00", "End": "2019-08-15 22:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 54, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Picses.png", "Title": "守卫双鱼宫", "Description": "跟仙女座青铜圣斗士瞬战斗！", "Start": "2019-08-15 22:00", "End": "2019-08-15 22:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 55, "OwnerID": "Pegasus", "Avatar": "img/temp/Pegasus.png", "Title": "突破教皇殿", "Description": "跟双子座黄金圣斗士撒加战斗！", "Start": "2019-08-15 22:30", "End": "2019-08-15 23:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 56, "OwnerID": "Phoenix", "Avatar": "img/temp/Phoenix.png", "Title": "闯教皇殿", "Description": "跟双子座黄金圣斗士撒加战斗！", "Start": "2019-08-15 22:30", "End": "2019-08-15 23:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 57, "OwnerID": "Gemini", "Avatar": "img/temp/Gemini.png", "Title": "大决战", "Description": "跟天马座青铜圣斗士星矢、凤凰座青铜圣斗士一辉战斗！", "Start": "2019-08-15 22:30", "End": "2019-08-15 23:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 58, "OwnerID": "Goddess", "Avatar": "img/temp/Goddess.png", "Title": "雅典娜获救", "Description": "经过青铜圣斗士们的努力~ 纱织终于获救~ 并获得残存黄金圣斗士们的认可~ 开始作为雅典娜执掌圣域！", "Start": "2019-08-15 23:00", "End": "2019-08-15 23:30", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": null, "RecurrenceException": null, "IsAllDay": false}, {"TaskID": 59, "OwnerID": "<PERSON><PERSON>", "Avatar": "img/temp/Aries.png", "Title": "镇守白羊宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 60, "OwnerID": "<PERSON><PERSON>", "Avatar": "img/temp/Taurus.png", "Title": "镇守金牛宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 61, "OwnerID": "Gemini", "Avatar": "img/temp/Gemini.png", "Title": "镇守双子宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 62, "OwnerID": "Cancer", "Avatar": "img/temp/Cancer.png", "Title": "镇守巨蟹宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 63, "OwnerID": "<PERSON>", "Avatar": "img/temp/Leo.png", "Title": "镇守狮子宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 64, "OwnerID": "Virgo", "Avatar": "img/temp/Virgo.png", "Title": "镇守处女宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 65, "OwnerID": "Libra", "Avatar": "img/temp/Libra.png", "Title": "镇守天秤宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 66, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Scorpion.png", "Title": "镇守天蝎宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 67, "OwnerID": "<PERSON><PERSON><PERSON><PERSON>", "Avatar": "img/temp/Sagittarius.png", "Title": "镇守射手宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 68, "OwnerID": "Capricorn", "Avatar": "img/temp/Capricorn.png", "Title": "镇守山羊宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 69, "OwnerID": "A<PERSON><PERSON>", "Avatar": "img/temp/Aquarius.png", "Title": "镇守水瓶宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}, {"TaskID": 70, "OwnerID": "<PERSON><PERSON><PERSON>", "Avatar": "img/temp/Picses.png", "Title": "镇守双鱼宫", "Description": "日常巡视~", "Start": "2019-01-01 12:00", "End": "2019-01-01 12:00", "StartTimezone": null, "EndTimezone": null, "RecurrenceID": null, "RecurrenceRule": "FREQ=DAILY", "RecurrenceException": null, "IsAllDay": true}]}