{"result": "y", "data": [{"text": "<hr>", "encoded": false, "id": "hr"}, {"text": "<i class='fas fa-paint-brush'></i>主题", "encoded": false, "items": [{"text": "<i class='fas fa-genderless'></i>Material Design", "encoded": false, "items": [{"text": "<i class='color' style='background-color: #018786;'></i><i class='color' style='background-color: #79bcbc;'></i>Default", "encoded": false, "url": "javascript:changeColor(\"material_default\");"}, {"text": "<i class='color' style='background-color: #f44336;'></i><i class='color' style='background-color: #e57373;'></i>Red", "encoded": false, "url": "javascript:changeColor(\"material_red\");"}, {"text": "<i class='color' style='background-color: #e91e63;'></i><i class='color' style='background-color: #f06292;'></i>Pink", "encoded": false, "url": "javascript:changeColor(\"material_pink\");"}, {"text": "<i class='color' style='background-color: #9c27b0;'></i><i class='color' style='background-color: #ba68c8;'></i>Purple", "encoded": false, "url": "javascript:changeColor(\"material_purple\");"}, {"text": "<i class='color' style='background-color: #673ab7;'></i><i class='color' style='background-color: #9575cd;'></i>Deep Purple", "encoded": false, "url": "javascript:changeColor(\"material_deep_purple\");"}, {"text": "<i class='color' style='background-color: #3f51b5;'></i><i class='color' style='background-color: #7986cb;'></i>Indigo", "encoded": false, "url": "javascript:changeColor(\"material_indigo\");"}, {"text": "<i class='color' style='background-color: #2196f3;'></i><i class='color' style='background-color: #64b5f6;'></i>Blue", "encoded": false, "url": "javascript:changeColor(\"material_blue\");"}, {"text": "<i class='color' style='background-color: #03a9f4;'></i><i class='color' style='background-color: #4fc3f7;'></i>Light Blue", "encoded": false, "url": "javascript:changeColor(\"material_light_blue\");"}, {"text": "<i class='color' style='background-color: #00bcd4;'></i><i class='color' style='background-color: #4dd0e1;'></i><PERSON>an", "encoded": false, "url": "javascript:changeColor(\"material_cyan\");"}, {"text": "<i class='color' style='background-color: #009688;'></i><i class='color' style='background-color: #4db6ac;'></i>Teal", "encoded": false, "url": "javascript:changeColor(\"material_teal\");"}, {"text": "<i class='color' style='background-color: #4caf50;'></i><i class='color' style='background-color: #81c784;'></i>Green", "encoded": false, "url": "javascript:changeColor(\"material_green\");"}, {"text": "<i class='color' style='background-color: #8bc34a;'></i><i class='color' style='background-color: #aed581;'></i>Light Green", "encoded": false, "url": "javascript:changeColor(\"material_light_green\");"}, {"text": "<i class='color' style='background-color: #cddc39;'></i><i class='color' style='background-color: #dce775;'></i>Lime", "encoded": false, "url": "javascript:changeColor(\"material_lime\");"}, {"text": "<i class='color' style='background-color: #ffeb3b;'></i><i class='color' style='background-color: #fff176;'></i>Yellow", "encoded": false, "url": "javascript:changeColor(\"material_yellow\");"}, {"text": "<i class='color' style='background-color: #ffc107;'></i><i class='color' style='background-color: #ffd54f;'></i>Amber", "encoded": false, "url": "javascript:changeColor(\"material_amber\");"}, {"text": "<i class='color' style='background-color: #ff9800;'></i><i class='color' style='background-color: #ffb74d;'></i>Orange", "encoded": false, "url": "javascript:changeColor(\"material_orange\");"}, {"text": "<i class='color' style='background-color: #ff5722;'></i><i class='color' style='background-color: #ff8a65;'></i>Deep Orange", "encoded": false, "url": "javascript:changeColor(\"material_deep_orange\");"}, {"text": "<i class='color' style='background-color: #795548;'></i><i class='color' style='background-color: #a1887f;'></i>Brown", "encoded": false, "url": "javascript:changeColor(\"material_brown\");"}, {"text": "<i class='color' style='background-color: #9e9e9e;'></i><i class='color' style='background-color: #e0e0e0;'></i>Grey", "encoded": false, "url": "javascript:changeColor(\"material_grey\");"}, {"text": "<i class='color' style='background-color: #607d8b;'></i><i class='color' style='background-color: #90a4ae;'></i>Blue Grey", "encoded": false, "url": "javascript:changeColor(\"material_blue_grey\");"}]}], "id": "theme"}, {"text": "<i class='fas fa-envelope'></i>消息", "encoded": false, "cssClass": "links-message", "url": "javascript:linkTo(\"/function/system/msg/msg/index\",\"消息中心\");", "id": "message"}, {"text": "<hr>", "encoded": false, "id": "hr"}, {"text": "<abbr>百迈客 [ 管理员 ]</abbr><img src='img/IKKI.png' alt='Berry'>", "encoded": false, "items": [{"text": "<i class='fas fa-user'></i>用户中心", "encoded": false, "cssClass": "links-account", "url": "javascript:linkTo(\"/function/config/system/account/index\",\"用户中心\");"}, {"text": "<i class='fas fa-key'></i>修改密码", "encoded": false, "cssClass": "links-password", "url": "javascript:linkTo(\"/function/config/system/password/index\", \"修改密码\");"}, {"text": "<i class='fas fa-cog'></i>系统设置", "encoded": false, "cssClass": "links-setting", "url": "javascript:linkTo(\"/function/config/system/settings/index\", \"系统设置\");"}, {"text": "<i class='fas fa-sign-out-alt'></i>退出登录", "encoded": false, "url": "javascript:confirmMsg(\"退出登录\", \"你确定要退出登录吗？\", \"question\", logout, \"\", \"\", \"\", noFunc, noFunc, false);"}], "id": "limsUser"}]}