{"result": "y", "msg": "", "data": [{"id": "01", "text": "<i class='fas fa-object-group'></i><abbr>采购/库管</abbr>", "encoded": false, "cssClass": "links-datasource", "url": "javascript:showNavPanel(\"listM0\");", "subItems": [{"text": "物料仓库管理", "url": "linkTo('/dashboard/grids/', 'grid_custom');", "subItems": [{"text": "入库管理01", "url": "linkTo(\"framework\", \"datasource\");"}, {"text": "出库管理02", "url": "linkTo(\"framework\", \"drawing\");"}]}, {"text": "采购管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理11", "url": "linkTo(\"framework\", \"globalization\");"}, {"text": "出库管理12", "url": "linkTo(\"framework\", \"integration\");"}]}, {"text": "采购管理2", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理21", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}, {"text": "出库管理22", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}]}]}, {"id": "02", "text": "<i class='fas fa-cubes'></i><abbr>营销中心</abbr>", "encoded": false, "cssClass": "links-datasource", "url": "javascript:showNavPanel(\"listM1\");", "subItems": [{"text": "物料仓库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}, {"text": "出库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}]}, {"text": "采购管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}, {"text": "出库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}]}]}, {"id": "03", "text": "<i class='fas fa-columns'></i><abbr>项目管理</abbr>", "encoded": false, "cssClass": "links-datasource", "url": "javascript:showNavPanel(\"listM3\");", "subItems": [{"text": "物料仓库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}, {"text": "出库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}]}, {"text": "采购管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}, {"text": "出库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}]}]}, {"id": "04", "text": "<i class='fas fa-map-signs'></i><abbr>运营/生产</abbr>", "encoded": false, "cssClass": "links-datasource", "url": "javascript:showNavPanel(\"listM4\");", "subItems": [{"text": "物料仓库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}, {"text": "出库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}]}, {"text": "采购管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");", "subItems": [{"text": "入库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}, {"text": "出库管理", "url": "linkTo(\"/dashboard/grids/\", \"grid_custom\");"}]}]}]}