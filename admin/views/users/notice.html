<script id="noticeTemp" type="text/x-kendo-template">
    <div id="noticeTabStripView"></div>
    <style scoped>
        #noticeTabStripView .k-tabstrip-items li {
            padding: 0 15px;
        }
        #noticeTabStripView .k-tabstrip-items .k-state-active {
            background: #f6f6f6;
        }
        #noticeTabStripView .k-tabstrip-items .k-sprite {
            overflow: visible;
            margin-right: 8px;
            font-size: 16px;
            line-height: 16px;
        }
        #noticeTabStripView .k-tabstrip-items .badge {
            margin-left: 6px;
            font-weight: 100;
        }
        #noticeTabStripView .k-content {
            padding: 0;
        }
        #noticeTabStripView .k-content .k-toolbar {
            border-top: 0;
            border-left: 0;
            border-right: 0;
        }
        #noticeTabStripView .k-content .k-toolbar .k-checkbox-label {
            margin-left: 8px;
        }
        #noticeTabStripView .k-content .k-listview {
            border: 0;
        }
        #noticeTabStripView .k-content .k-listview .media {
            align-items: center;
            padding: 16px;
        }
        #noticeTabStripView .k-content .k-listview .media label {
            margin-right: 8px;
        }
        #noticeTabStripView .k-content .k-listview .media figure {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 15px 0 0;
            border-radius: 50%;
            width: 32px;
            height: 32px;
        }
        #noticeTabStripView .k-content .k-listview .media figure i {
            margin: 0;
        }
        #noticeTabStripView .k-content .k-listview .media img {
            margin: 0 15px 0 0;
            border: 1px solid #ccc;
            border-radius: 50%;
            width: 32px;
            height: 32px;
        }
        @media only screen and (max-width: 575px) {
            #noticeTabStripView .k-tabstrip-items {
                justify-content: space-between;
            }
            #noticeTabStripView .k-tabstrip-items li {
                padding: 0;
                min-width: 30%;
            }
            #noticeTabStripView .k-tabstrip-items li .k-link {
                justify-content: center;
                white-space: nowrap;
            }
            #noticeTabStripView .k-toolbar .k-textbox {
                width: 120px;
            }
            #noticeTabStripView .k-toolbar .k-overflow-anchor {
                width: 30px;
            }
            #noticeTabStripView .k-content .k-listview .media {
                align-items: flex-start;
            }
            #noticeTabStripView .k-content .k-listview .media figure {
                border-radius: 4px;
            }
            #noticeTabStripView .k-content .k-listview .media img {
                border-radius: 4px;
            }
        }
        @media only screen and (min-width: 576px) {
            #noticeTabStripView .k-content .k-listview .media figure {
                width: 64px;
                height: 64px;
            }
            #noticeTabStripView .k-content .k-listview .media figure i {
                font-size: 28px;
            }
            #noticeTabStripView .k-content .k-listview .media img {
                width: 64px;
                height: 64px;
            }
        }
        @media only screen and (max-width: 400px) {
            #noticeTabStripView .k-toolbar .k-textbox {
                width: 90px;
            }
        }
        #noticeTabStripView .k-content .k-listview .media h5 {
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: bold;
            color: #ccc;
            white-space: normal;
        }
        #noticeTabStripView .k-content .k-listview .media h5 em {
            float: right;
            margin-left: 10px;
            border-width: 1px;
            border-style: solid;
            border-radius: 4px;
            padding: 3px 8px;
            font-size: 12px;
            font-style: normal;
        }
        #noticeTabStripView .k-content .k-listview .media h5 .k-notification-normal {
            border-color: #ccc;
            color: #656565;
            background: #f6f6f6;
        }
        #noticeTabStripView .k-content .k-listview .media p {
            margin-bottom: 3px;
            font-size: 12px;
            color: #ccc;
            white-space: normal;
        }
        #noticeTabStripView .k-content .k-listview .media time {
            font-size: 12px;
            color: #ccc;
        }
        #noticeTabStripView .k-content .k-listview .media .unread h5 {
            color: #000;
        }
        #noticeTabStripView .k-content .k-listview .media .unread p {
            color: #666;
        }
        #noticeTabStripView .k-content .k-listview .media .unread time {
            color: #999;
        }
        #noticeTabStripView .k-content .k-listview .k-state-selected h5,
        #noticeTabStripView .k-content .k-listview .k-state-selected p,
        #noticeTabStripView .k-content .k-listview .k-state-selected time {
            color: #000;
        }
        #noticeTabStripView .k-content .blank {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
        .ids {
            z-index: 1;
            width: 16px;
            height: 16px;
            clip: auto;
            pointer-events: auto;
        }
    </style>
</script>