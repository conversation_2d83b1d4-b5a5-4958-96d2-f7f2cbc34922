<script id="buttonTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-lg-6 col-xl-4">
            <div class="card mb-3">
                <h5 class="card-header">BioMarker &amp; Huakai 按钮</h5>
                <div class="card-body">
                    <button class="k-button theme-m-bg" type="button">主色底</button>
                    <button class="k-button theme-s-bg" type="button">次色底</button>
                    <button class="k-button theme-m-box" type="button">主色框</button>
                    <button class="k-button theme-s-box" type="button">次色框</button>
                    <button class="k-button theme-l" type="button">主色框次色底</button>
                    <button class="k-button k-button-lg theme-m-bg" type="button">大按钮</button>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-8">
            <div class="card mb-3">
                <h5 class="card-header">Kendo UI 按钮</h5>
                <div class="card-body">
                    <button class="k-button" type="button">普通按钮</button>
                    <button class="k-button k-state-selected" type="button">选中按钮</button>
                    <button class="k-button k-state-hover" type="button">悬停按钮</button>
                    <button class="k-button k-state-disabled" type="button">禁用按钮</button>
                    <button class="k-button k-button-icontext k-notification-success" type="button"><i class="k-icon k-i-check-outline"></i>成功按钮</button>
                    <button class="k-button k-button-icontext k-notification-info" type="button"><i class="k-icon k-i-information"></i>信息按钮</button>
                    <button class="k-button k-button-icontext k-notification-warning" type="button"><i class="k-icon k-i-warning"></i>警告按钮</button>
                    <button class="k-button k-button-icontext k-notification-error" type="button"><i class="k-icon k-i-close-outline"></i>错误按钮</button>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card mb-3">
                <h5 class="card-header">HTML &amp; CSS 按钮</h5>
                <div class="card-body">
                    <button class="k-button" type="button"><i class="fas fa-yin-yang mr-1"></i>样式按钮</button>
                    <button class="k-button k-button-icontext" type="button"><i class="k-icon k-i-star"></i>图标按钮</button>
                    <button class="k-button" type="button"><img src="img/IKKI.png" alt="">图片按钮</button>
                    <button class="k-button" type="button" disabled>禁用按钮</button>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card mb-3">
                <h5 class="card-header">Javascript 按钮</h5>
                <div class="card-body">
                    <button id="cssButton" type="button">样式按钮</button>
                    <button id="iconButton" type="button">图标按钮</button>
                    <button id="imageButton" type="button">图片按钮</button>
                    <button id="disabledButton" type="button">禁用按钮</button>
                </div>
            </div>
        </div>
    </div>
    <style scoped>
        .k-button img {
            margin-right: 4px;
            width: 16px;
            height: 16px;
        }
    </style>
</script>