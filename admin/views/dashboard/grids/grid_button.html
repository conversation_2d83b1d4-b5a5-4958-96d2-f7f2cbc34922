<script id="grid_buttonTemp" type="text/x-kendo-template">
    <div class="card mb-3">
        <h5 class="card-header">条件搜索</h5>
        <div class="card-body">
            <form class="condition">
                <div class="form-group row justify-content-lg-center mb-lg-4">
                    <label class="col-form-label text-lg-right col-lg-2 k-label-lg">搜索：</label>
                    <div class="col-lg-4">
                        <span class="k-textbox k-textbox-lg k-space-left w-100"><input id="searchKeywords" name="keywords" type="text" placeholder="请输入关键字并回车……"><a class="k-icon k-i-search k-required" href="javascript:;"></a></span>
                    </div>
                    <div class="col-lg-2 text-center text-lg-left">
                        <button class="k-button k-button-lg k-state-selected mt-3 mt-lg-0" id="searchBtn" type="button" onclick="conditionSearch();">模糊搜索</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="card">
        <h5 class="card-header">表格展示</h5>
        <div class="card-body">
            <div id="grid"></div>
        </div>
    </div>
    <style scoped>
        #permissionTreeView .k-checkbox-label {
            margin-left: 3px;
        }
        #permissionTreeView .k-checkbox-label i {
            margin-left: 5px;
            margin-right: 6px;
            width: 18px;
            height: 18px;
            font-size: 18px;
            line-height: 18px;
        }
        #permissionTreeView .k-checkbox-label i.fa-genderless {
            margin-right: 0;
        }
        #permissionTreeView .k-checkbox-label small {
            margin-left: 5px;
            font-size: 12px;
            opacity: .5;
            font-family: tahoma, sans-serif;
        }
        #permissionTreeView .k-checkbox-label sub {
            bottom: 0;
            margin-left: 5px;
            border-radius: 4px;
            padding: 0 5px;
            height: 18px;
            line-height: 18px;
        }
    </style>
</script>