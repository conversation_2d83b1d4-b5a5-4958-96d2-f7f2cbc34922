<script id="grid_inlineTemp" type="text/x-kendo-template">
    <div class="card">
        <h5 class="card-header">条件搜索</h5>
        <div class="card-body">
            <form class="condition">
                <div class="form-group row justify-content-lg-center mb-lg-4">
                    <label class="col-form-label text-lg-right col-lg-2 k-label-lg">搜索：</label>
                    <div class="col-lg-4">
                        <span class="k-textbox k-textbox-lg k-space-left w-100"><input id="searchKeywords" name="keywords" type="text" placeholder="请输入关键字并回车……"><a class="k-icon k-i-search k-required" href="javascript:;"></a></span>
                    </div>
                    <div class="col-lg-2 text-center text-lg-left">
                        <button class="k-button k-button-lg k-state-selected mt-3 mt-lg-0" id="searchBtn" type="button" onclick="conditionSearch();">模糊搜索</button>
                    </div>
                </div>
                <div class="adv-search-area">
                    <div class="form-row">
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">姓名：</label>
                            <input class="k-textbox w-100" name="realName" type="text" placeholder="文本框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">性别：</label>
                            <input class="k-radio" id="gender1" name="gender" type="radio" value="1"><label class="k-radio-label" for="gender1">男</label>
                            <input class="k-radio" id="gender2" name="gender" type="radio" value="2"><label class="k-radio-label" for="gender2">女</label>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">年龄：</label>
                            <input class="w-100" id="ageStart" name="ageStart" type="number" placeholder="数字框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="ageEnd" name="ageEnd" type="number" placeholder="数字框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">身高：</label>
                            <input class="w-100" id="heightStart" name="heightStart" type="number" placeholder="小数单位数字框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="heightEnd" name="heightEnd" type="number" placeholder="小数单位数字框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">血型：</label>
                            <select class="w-100" id="bloodType" name="bloodType">
                                <option value="">单选下拉框</option>
                                <option value="1">A 型</option>
                                <option value="2">B 型</option>
                                <option value="3">O 型</option>
                                <option value="4">AB 型</option>
                                <option value="5">其他</option>
                            </select>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">生日：</label>
                            <input class="w-100" id="birthdayStart" name="birthdayStart" type="date" placeholder="日期框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="birthdayEnd" name="birthdayEnd" type="date" placeholder="日期框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">配偶生日：</label>
                            <input class="w-100" id="mateBirthdayStart" name="mateBirthdayStart" type="date" placeholder="日期时间掩码框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="mateBirthdayEnd" name="mateBirthdayEnd" type="date" placeholder="日期时间掩码框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">银行卡：</label>
                            <input class="w-100" id="creditCard" name="creditCard" type="text" placeholder="掩码框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">资产：</label>
                            <input class="w-100" id="assetStart" name="assetStart" type="number" placeholder="金融数字框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="assetEnd" name="assetEnd" type="number" placeholder="金融数字框结束">
                        </div>
                        <div class="form-group col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">籍贯：</label>
                            <select class="w-100" id="province" name="province"></select>
                        </div>
                        <div class="form-group col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">&nbsp;</label>
                            <select class="w-100" id="city" name="city"></select>
                        </div>
                        <div class="form-group col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">&nbsp;</label>
                            <select class="w-100" id="area" name="area"></select>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">居住地：</label>
                            <input class="w-100" id="domicile" name="domicile" type="text">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">民族：</label>
                            <input class="w-100" id="nation" name="nation" type="text" placeholder="输入下拉框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">生肖：</label>
                            <input class="w-100" id="zodiac" name="zodiac" type="text" placeholder="表格下拉框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">语言：</label>
                            <input class="w-100" id="language" name="language" type="text" placeholder="自动完成框">
                        </div>
                        <div class="form-group col-lg-9 col-xl-6">
                            <label class="d-block">教育程度：</label>
                            <input class="k-checkbox" id="education1" name="education" type="checkbox" value="1"><label class="k-checkbox-label" for="education1">小学</label>
                            <input class="k-checkbox" id="education2" name="education" type="checkbox" value="2"><label class="k-checkbox-label" for="education2">初中</label>
                            <input class="k-checkbox" id="education3" name="education" type="checkbox" value="3"><label class="k-checkbox-label" for="education3">高中</label>
                            <input class="k-checkbox" id="education4" name="education" type="checkbox" value="4"><label class="k-checkbox-label" for="education4">中专</label>
                            <input class="k-checkbox" id="education5" name="education" type="checkbox" value="5"><label class="k-checkbox-label" for="education5">大专</label>
                            <input class="k-checkbox" id="education6" name="education" type="checkbox" value="6"><label class="k-checkbox-label" for="education6">本科</label>
                            <input class="k-checkbox" id="education7" name="education" type="checkbox" value="7"><label class="k-checkbox-label" for="education7">硕士</label>
                            <input class="k-checkbox" id="education8" name="education" type="checkbox" value="8"><label class="k-checkbox-label" for="education8">博士</label>
                            <input class="k-checkbox" id="education9" name="education" type="checkbox" value="9"><label class="k-checkbox-label" for="education9">其他</label>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">毕业年份：</label>
                            <input class="w-100" id="graduationStart" name="graduationStart" placeholder="年份框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="graduationEnd" name="graduationEnd" placeholder="年份框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">参加工作年月：</label>
                            <input class="w-100" id="firstJobStart" name="firstJobStart" type="month" placeholder="月份框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="firstJobEnd" name="firstJobEnd" type="month" placeholder="月份框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">手机：</label>
                            <input class="k-textbox w-100" name="mobile" type="tel" placeholder="手机框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">电子邮件：</label>
                            <input class="k-textbox w-100" name="email" type="email" placeholder="电子邮件框">
                        </div>
                        <div class="form-group col-lg-6 col-xl-4">
                            <label class="d-block">个人主页：</label>
                            <input class="k-textbox w-100" name="homepage" type="url" placeholder="网址框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">起床时间：</label>
                            <input class="w-100" id="getUpStart" name="getUpStart" type="time" placeholder="时间框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="getUpEnd" name="getUpEnd" type="time" placeholder="时间框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">最有意义的时刻：</label>
                            <input class="w-100" id="importantMomentStart" name="importantMomentStart" type="datetime" placeholder="日期时间框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="importantMomentEnd" name="importantMomentEnd" type="datetime" placeholder="日期时间框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">性格：</label>
                            <input id="character" name="character" type="range" value="0">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">颜色喜好：</label>
                            <input id="color" name="color">
                        </div>
                        <div class="form-group col-lg-6 col-xl-4">
                            <label class="d-block">相配的星座：</label>
                            <select class="w-100" id="constellation" name="constellation" multiple>
                                <option value="1">白羊座</option>
                                <option value="2">金牛座</option>
                                <option value="3">双子座</option>
                                <option value="4">巨蟹座</option>
                                <option value="5">狮子座</option>
                                <option value="6">处女座</option>
                                <option value="7">天秤座</option>
                                <option value="8">天蝎座</option>
                                <option value="9">射手座</option>
                                <option value="10">山羊座</option>
                                <option value="11">水瓶座</option>
                                <option value="12">双鱼座</option>
                            </select>
                        </div>
                        <div class="form-group col-lg-6 col-xl-4">
                            <label class="d-block">旅游足迹：</label>
                            <select class="w-100" id="tourism" name="tourism" multiple></select>
                        </div>
                        <div class="form-group col-lg-6 col-xl-2">
                            <label class="d-block">自我评价：</label>
                            <input class="w-100" id="evaluation" name="evaluation">
                        </div>
                        <div class="form-group col-lg-6 col-xl-6">
                            <label class="d-block">自我介绍：</label>
                            <textarea class="k-textarea w-100" name="summary" placeholder="文本域框"></textarea>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">是否在线：</label>
                            <input id="online" name="online" type="checkbox">
                        </div>
                    </div>
                    <div class="form-group row justify-content-center mb-3">
                        <button class="k-button k-button-lg k-state-selected mx-3" id="advSearchBtn" type="button" onclick="conditionSearch();">精确搜索</button>
                        <button class="k-button k-button-lg mx-3" type="reset">重 置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="adv-search-btn">
        <button class="k-button" type="button" onclick="advSearch(this);"><i class='fas fa-angle-double-down'></i>高级搜索</button>
    </div>
    <div class="card">
        <h5 class="card-header">表格展示</h5>
        <div class="card-body">
            <div id="grid"></div>
        </div>
    </div>
    <script id="detailsTemplate" type="text/x-kendo-template">
        <div class="k-edit-form-container">
            <div class="k-edit-label py-0">ID：</div>
            <div class="k-edit-field">#= id #</div>
            <div class="k-edit-label py-0">用户名：</div>
            <div class="k-edit-field">#= userName #</div>
            <div class="k-edit-label py-0">姓名：</div>
            <div class="k-edit-field">#= realName #</div>
            <div class="k-edit-label py-0">昵称：</div>
            <div class="k-edit-field">#= nickName #</div>
            <div class="k-edit-label py-0">状态：</div>
            <div class="k-edit-field">
                # if (online) { #
                    <span class="dot-color k-notification-success"></span><span class="k-notification-success bg-transparent ml-2">在线</span>
                # } else { #
                    <span class="dot-color k-notification-error"></span><span class="k-notification-error bg-transparent ml-2">离线</span>
                # } #
            </div>
            <div class="k-edit-label py-0">性别：</div>
            <div class="k-edit-field">
                # if (gender === '1') { #
                    男 <i class="fas fa-mars mars"></i>
                # } else if (gender === '2') { #
                    女 <i class="fas fa-venus venus"></i>
                # } #
            </div>
            <div class="k-edit-label py-0">年龄：</div>
            <div class="k-edit-field">#= age # 岁</div>
            <div class="k-edit-label py-0">身高：</div>
            <div class="k-edit-field">#= kendo.toString(height, '0.00') # m</div>
            <div class="k-edit-label py-0">血型：</div>
            <div class="k-edit-field">
                # if (bloodType === '1') { #
                    A 型
                # } else if (bloodType === '2') { #
                    B 型
                # } else if (bloodType === '3') { #
                    O 型
                # } else if (bloodType === '4') { #
                    AB 型
                # } else if (bloodType === '5') { #
                    其他
                # } #
            </div>
            <div class="k-edit-label py-0">生日：</div>
            <div class="k-edit-field">#= birthday #</div>
            <div class="k-edit-label py-0">配偶生日：</div>
            <div class="k-edit-field">#= mateBirthday #</div>
            <div class="k-edit-label py-0">银行卡：</div>
            <div class="k-edit-field">#= creditCard.replace(creditCard.substr(2, 12), '** **** **** **') #</div>
            <div class="k-edit-label py-0">资产：</div>
            <div class="k-edit-field">#= kendo.toString(asset, 'c') #</div>
            <div class="k-edit-label py-0">籍贯：</div>
            <div class="k-edit-field">#= nativePlace.provinceName # - #= nativePlace.cityName # - #= nativePlace.areaName #</div>
            <div class="k-edit-label py-0">居住地：</div>
            <div class="k-edit-field">#= domicile.name #</div>
            <div class="k-edit-label py-0">民族：</div>
            <div class="k-edit-field">#= nation.nationName #</div>
            <div class="k-edit-label py-0">生肖：</div>
            <div class="k-edit-field">#= zodiac.zodiacName #</div>
            <div class="k-edit-label py-0">语言：</div>
            <div class="k-edit-field">#= language #</div>
            <div class="k-edit-label py-0">教育程度：</div>
            <div class="k-edit-field">
                # for (var i = 0; i < education.length; i++) { #
                    # if (education[i] === '1') { #
                        小学&nbsp;
                    # } else if (education[i] === '2') { #
                        初中&nbsp;
                    # } else if (education[i] === '3') { #
                        高中&nbsp;
                    # } else if (education[i] === '4') { #
                        中专&nbsp;
                    # } else if (education[i] === '5') { #
                        大专&nbsp;
                    # } else if (education[i] === '6') { #
                        本科&nbsp;
                    # } else if (education[i] === '7') { #
                        硕士&nbsp;
                    # } else if (education[i] === '8') { #
                        博士&nbsp;
                    # } else if (education[i] === '9') { #
                        其他&nbsp;
                    # } #
                # } #
            </div>
            <div class="k-edit-label py-0">毕业年份：</div>
            <div class="k-edit-field">#= graduation #</div>
            <div class="k-edit-label py-0">参加工作年月：</div>
            <div class="k-edit-field">#= firstJob #</div>
            <div class="k-edit-label py-0">手机：</div>
            <div class="k-edit-field">#= mobile #</div>
            <div class="k-edit-label py-0">电子邮件：</div>
            <div class="k-edit-field">#= email #</div>
            <div class="k-edit-label py-0">个人主页：</div>
            <div class="k-edit-field">#= homepage #</div>
            <div class="k-edit-label py-0">起床时间：</div>
            <div class="k-edit-field">#= getUp #</div>
            <div class="k-edit-label py-0">最有意义的时刻：</div>
            <div class="k-edit-field">#= importantMoment #</div>
            <div class="k-edit-label py-0">性格：</div>
            <div class="k-edit-field">
                # if (character === 10) { #
                    超级开朗
                # } else if (character === 8) { #
                    非常开朗
                # } else if (character === 6) { #
                    很开朗
                # } else if (character === 4) { #
                    比较开朗
                # } else if (character === 2) { #
                    有点开朗
                # } else if (character === 0) { #
                    普通
                # } else if (character === -2) { #
                    有点内向
                # } else if (character === -4) { #
                    比较内向
                # } else if (character === -6) { #
                    很内向
                # } else if (character === -8) { #
                    非常内向
                # } else if (character === -10) { #
                    超级内向
                # } #
            </div>
            <div class="k-edit-label py-0">颜色喜好：</div>
            <div class="k-edit-field">
                <span style="display: inline-block; width: 10%; height: 24px; background: #= color #; border: 1px solid \#c5c5c5; border-radius: 4px; vertical-align: middle;"></span>
            </div>
            <div class="k-edit-label py-0">相配的星座：</div>
            <div class="k-edit-field">
                # for (var i = 0; i < constellation.length; i++) { #
                    # if (constellation[i] === "1") { #
                        白羊座&nbsp;
                    # } else if (constellation[i] === "2") { #
                        金牛座&nbsp;
                    # } else if (constellation[i] === "3") { #
                        双子座&nbsp;
                    # } else if (constellation[i] === "4") { #
                        巨蟹座&nbsp;
                    # } else if (constellation[i] === "5") { #
                        狮子座&nbsp;
                    # } else if (constellation[i] === "6") { #
                        处女座&nbsp;
                    # } else if (constellation[i] === "7") { #
                        天秤座&nbsp;
                    # } else if (constellation[i] === "8") { #
                        天蝎座&nbsp;
                    # } else if (constellation[i] === "9") { #
                        射手座&nbsp;
                    # } else if (constellation[i] === "10") { #
                        山羊座&nbsp;
                    # } else if (constellation[i] === "11") { #
                        水瓶座&nbsp;
                    # } else if (constellation[i] === "12") { #
                        双鱼座&nbsp;
                    # } #
                # } #
            </div>
            <div class="k-edit-label py-0">旅游足迹：</div>
            <div class="k-edit-field">
                # for (var i = 0; i < tourism.length; i++) { #
                    #= tourism[i].name #&nbsp;
                # } #
            </div>
            <div class="k-edit-label py-0">自我评价：</div>
            <div class="k-edit-field">
                # if (evaluation === 1) { #
                    不合格
                # } else if (evaluation === 2) { #
                    待提升
                # } else if (evaluation === 3) { #
                    合格
                # } else if (evaluation === 4) { #
                    良好
                # } else if (evaluation === 5) { #
                    优秀
                # } else if (evaluation === 6) { #
                    完美
                # } #
            </div>
            <div class="k-edit-label py-0">自我介绍：</div>
            <div class="k-edit-field">#= summary #</div>
            <div class="k-edit-label py-0">头像：</div>
            <div class="k-edit-field">
                <img class="img-thumbnail w-25" src="#= photo.url #" alt="[#= kendo.toString(photo.size / 1024, '0.00') # KB]" title="#= photo.name ##= photo.extension #">
            </div>
            <div class="k-edit-label py-0">签名：</div>
            <div class="k-edit-field">#= sign #</div>
        </div>
    </script>
</script>