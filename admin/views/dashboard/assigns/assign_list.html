<script id="assign_listTemp" type="text/x-kendo-template">
    <div class="card position-absolute w-100 h-100">
        <h5 class="card-header">分配展示</h5>
        <div class="row no-gutters h-100 overflow-hidden">
            <div class="col-lg-5 h-100 overflow-auto">
                <div class="card-body h-100">
                    <div id="toolbarFrom"></div>
                    <div class="border-top-0 mb-lg-3" id="listFrom"></div>
                </div>
            </div>
            <div class="col-lg-2 h-100 d-flex flex-row flex-lg-column justify-content-center">
                <button class="k-button k-button-icontext m-3" id="addItems"><span class="k-icon k-i-add"></span>添加</button>
                <button class="k-button k-button-icontext m-3" id="delItems"><span class="k-icon k-i-x"></span>删除</button>
            </div>
            <div class="col-lg-5 h-100 overflow-auto">
                <div class="card-body h-100">
                    <div id="toolbarTo"></div>
                    <div class="border-top-0 mb-lg-3" id="listTo"></div>
                </div>
            </div>
        </div>
        <div class="card-footer text-center">
            <button class="k-button k-button-icontext mx-2 k-state-selected" id="submitIdAssign"><span class="k-icon k-i-check"></span>提交 ID</button>
            <button class="k-button k-button-icontext mx-2 theme-m-box" id="submitDataAssign"><span class="k-icon k-i-validation-data"></span>提交数据</button>
            <button class="k-button k-button-icontext mx-2" id="refreshAssign"><span class="k-icon k-i-reset"></span>重置</button>
        </div>
    </div>
    <script id="listTemplate" type="text/x-kendo-template">
        <div class="listItem media align-items-center p-3">
            <span class="mr-2">
                <input class="k-checkbox ids" id="#= id #Ids" type="checkbox" value="#= id #"><label class="k-checkbox-label" for="#= id #Ids"></label>
            </span>
            <img class="img-thumbnail w-20" src="#= photo.url #" alt="#= nickName #" title="#= realName #">
            <div class="media-body ml-3">
                <h5 class="mt-2 mb-3">
                    <strong class="h4 font-weight-bold text-dark mr-2">#= realName #</strong>
                    <small class="text-black-50">[#= nickName #]</small>
                    # if (gender === '1') { #
                        <i class="fas fa-mars mars ml-2"></i>
                    # } else if (gender === '2') { #
                        <i class="fas fa-venus venus ml-2"></i>
                    # } #
                </h5>
                <p class="mb-2">
                    # if (online) { #
                        <span class="dot-color k-notification-success"></span><span class="k-notification-success bg-transparent ml-2">在线</span>
                    # } else { #
                        <span class="dot-color k-notification-error"></span><span class="k-notification-error bg-transparent ml-2">离线</span>
                    # } #
                </p>
                <dl class="d-flex mb-0">
                    <dt class="font-weight-bold text-nowrap">生日：</dt>
                    <dd class="text-black-50">#= birthday #</dd>
                </dl>
                <dl class="d-flex mb-0">
                    <dt class="font-weight-bold text-nowrap">生肖：</dt>
                    <dd class="text-black-50">#= zodiac.zodiacName #</dd>
                </dl>
            </div>
        </div>
    </script>
    <!-- 临时修复 Kendo UI 自定义列表复选框无法选中的 BUG -->
    <style scoped>
        #listFrom,
        #listTo {
            min-height: calc(100% - 48px);
        }
        @media only screen and (max-width: 991px) {
            .position-absolute {
                position: relative !important;
            }
        }
        .ids {
            z-index: 1;
            width: 16px;
            height: 16px;
            clip: auto;
            pointer-events: auto;
        }
    </style>
</script>