<script id="form_cascadeTemp" type="text/x-kendo-template">
    <div class="card">
        <div class="card-body">
            <form>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">自动完成框分组：</label>
                    <div class="col-lg-6">
                        <input class="w-100" id="autoCompleteGroup" name="autoCompleteGroup">
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">单选下拉框分组：</label>
                    <div class="col-lg-6">
                        <select class="w-100" id="dropDownListGroup" name="dropDownListGroup"></select>
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">输入下拉框分组：</label>
                    <div class="col-lg-6">
                        <input class="w-100" id="comboBoxGroup" name="comboBoxGroup">
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">表格下拉框分组：</label>
                    <div class="col-lg-6">
                        <input class="w-100" id="multiColumnComboBoxGroup" name="multiColumnComboBoxGroup">
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">多选下拉框分组：</label>
                    <div class="col-lg-6">
                        <select class="w-100" id="multiSelectGroup" name="multiSelectGroup" multiple></select>
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">树形下拉框多选：</label>
                    <div class="col-lg-6">
                        <input class="w-100" id="dropDownTreeMulti" name="dropDownTreeMulti">
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">省市县乡村五级联动：</label>
                    <div class="col-lg-6">
                        <select class="w-100" id="province" name="province"></select>
                        <select class="w-100 mt-2" id="city" name="city"></select>
                        <select class="w-100 mt-2" id="area" name="area"></select>
                        <!--<select class="w-100 mt-2" id="street" name="street"></select>-->
                        <!--<select class="w-100 mt-2" id="village" name="village"></select>-->
                        <small class="form-text text-danger">乡镇和村庄的数据太大，故予以注释处理~</small>
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">省市区三级联动：</label>
                    <div class="col-lg-6">
                        <span class="d-block"><input class="w-100" id="province2" name="province2"></span>
                        <span class="d-block mt-2"><input class="w-100" id="city2" name="city2"></span>
                        <span class="d-block mt-2"><input class="w-100" id="area2" name="area2"></span>
                    </div>
                </div>
                <div class="form-group row justify-content-lg-center">
                    <label class="col-form-label text-lg-right col-lg-2">省市二级联动：</label>
                    <div class="col-lg-6">
                        <span class="d-block"><input class="w-100" id="province3" name="province3"></span>
                        <span class="d-block mt-2"><input class="w-100" id="city3" name="city3"></span>
                    </div>
                </div>
            </form>
        </div>
    </div>
</script>