<script id="list_customTemp" type="text/x-kendo-template">
    <div class="card">
        <h5 class="card-header">条件搜索</h5>
        <div class="card-body">
            <form class="condition">
                <div class="form-group row justify-content-lg-center mb-lg-4">
                    <label class="col-form-label text-lg-right col-lg-2 k-label-lg">搜索：</label>
                    <div class="col-lg-4">
                        <span class="k-textbox k-textbox-lg k-space-left w-100"><input id="searchKeywords" name="keywords" type="text" placeholder="请输入关键字并回车……"><a class="k-icon k-i-search k-required" href="javascript:;"></a></span>
                    </div>
                    <div class="col-lg-2 text-center text-lg-left">
                        <button class="k-button k-button-lg k-state-selected mt-3 mt-lg-0" id="searchBtn" type="button" onclick="conditionSearch();">模糊搜索</button>
                    </div>
                </div>
                <div class="adv-search-area">
                    <div class="form-row">
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">姓名：</label>
                            <input class="k-textbox w-100" name="realName" type="text" placeholder="文本框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">性别：</label>
                            <input class="k-radio" id="gender1" name="gender" type="radio" value="1"><label class="k-radio-label" for="gender1">男</label>
                            <input class="k-radio" id="gender2" name="gender" type="radio" value="2"><label class="k-radio-label" for="gender2">女</label>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">年龄：</label>
                            <input class="w-100" id="ageStart" name="ageStart" type="number" placeholder="数字框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="ageEnd" name="ageEnd" type="number" placeholder="数字框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">身高：</label>
                            <input class="w-100" id="heightStart" name="heightStart" type="number" placeholder="小数单位数字框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="heightEnd" name="heightEnd" type="number" placeholder="小数单位数字框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">血型：</label>
                            <select class="w-100" id="bloodType" name="bloodType">
                                <option value="">单选下拉框</option>
                                <option value="1">A 型</option>
                                <option value="2">B 型</option>
                                <option value="3">O 型</option>
                                <option value="4">AB 型</option>
                                <option value="5">其他</option>
                            </select>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">生日：</label>
                            <input class="w-100" id="birthdayStart" name="birthdayStart" type="date" placeholder="日期框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="birthdayEnd" name="birthdayEnd" type="date" placeholder="日期框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">配偶生日：</label>
                            <input class="w-100" id="mateBirthdayStart" name="mateBirthdayStart" type="date" placeholder="日期时间掩码框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="mateBirthdayEnd" name="mateBirthdayEnd" type="date" placeholder="日期时间掩码框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">银行卡：</label>
                            <input class="w-100" id="creditCard" name="creditCard" type="text" placeholder="掩码框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">资产：</label>
                            <input class="w-100" id="assetStart" name="assetStart" type="number" placeholder="金融数字框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="assetEnd" name="assetEnd" type="number" placeholder="金融数字框结束">
                        </div>
                        <div class="form-group col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">籍贯：</label>
                            <select class="w-100" id="province" name="province"></select>
                        </div>
                        <div class="form-group col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">&nbsp;</label>
                            <select class="w-100" id="city" name="city"></select>
                        </div>
                        <div class="form-group col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">&nbsp;</label>
                            <select class="w-100" id="area" name="area"></select>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">居住地：</label>
                            <input class="w-100" id="domicile" name="domicile" type="text">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">民族：</label>
                            <input class="w-100" id="nation" name="nation" type="text" placeholder="输入下拉框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">生肖：</label>
                            <input class="w-100" id="zodiac" name="zodiac" type="text" placeholder="表格下拉框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">语言：</label>
                            <input class="w-100" id="language" name="language" type="text" placeholder="自动完成框">
                        </div>
                        <div class="form-group col-lg-9 col-xl-6">
                            <label class="d-block">教育程度：</label>
                            <input class="k-checkbox" id="education1" name="education" type="checkbox" value="1"><label class="k-checkbox-label" for="education1">小学</label>
                            <input class="k-checkbox" id="education2" name="education" type="checkbox" value="2"><label class="k-checkbox-label" for="education2">初中</label>
                            <input class="k-checkbox" id="education3" name="education" type="checkbox" value="3"><label class="k-checkbox-label" for="education3">高中</label>
                            <input class="k-checkbox" id="education4" name="education" type="checkbox" value="4"><label class="k-checkbox-label" for="education4">中专</label>
                            <input class="k-checkbox" id="education5" name="education" type="checkbox" value="5"><label class="k-checkbox-label" for="education5">大专</label>
                            <input class="k-checkbox" id="education6" name="education" type="checkbox" value="6"><label class="k-checkbox-label" for="education6">本科</label>
                            <input class="k-checkbox" id="education7" name="education" type="checkbox" value="7"><label class="k-checkbox-label" for="education7">硕士</label>
                            <input class="k-checkbox" id="education8" name="education" type="checkbox" value="8"><label class="k-checkbox-label" for="education8">博士</label>
                            <input class="k-checkbox" id="education9" name="education" type="checkbox" value="9"><label class="k-checkbox-label" for="education9">其他</label>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">毕业年份：</label>
                            <input class="w-100" id="graduationStart" name="graduationStart" placeholder="年份框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="graduationEnd" name="graduationEnd" placeholder="年份框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">参加工作年月：</label>
                            <input class="w-100" id="firstJobStart" name="firstJobStart" type="month" placeholder="月份框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="firstJobEnd" name="firstJobEnd" type="month" placeholder="月份框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">手机：</label>
                            <input class="k-textbox w-100" name="mobile" type="tel" placeholder="手机框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">电子邮件：</label>
                            <input class="k-textbox w-100" name="email" type="email" placeholder="电子邮件框">
                        </div>
                        <div class="form-group col-lg-6 col-xl-4">
                            <label class="d-block">个人主页：</label>
                            <input class="k-textbox w-100" name="homepage" type="url" placeholder="网址框">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">起床时间：</label>
                            <input class="w-100" id="getUpStart" name="getUpStart" type="time" placeholder="时间框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="getUpEnd" name="getUpEnd" type="time" placeholder="时间框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">最有意义的时刻：</label>
                            <input class="w-100" id="importantMomentStart" name="importantMomentStart" type="datetime" placeholder="日期时间框起始">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-none d-md-block">至</label>
                            <input class="w-100" id="importantMomentEnd" name="importantMomentEnd" type="datetime" placeholder="日期时间框结束">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">性格：</label>
                            <input id="character" name="character" type="range" value="0">
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">颜色喜好：</label>
                            <input id="color" name="color">
                        </div>
                        <div class="form-group col-lg-6 col-xl-4">
                            <label class="d-block">相配的星座：</label>
                            <select class="w-100" id="constellation" name="constellation" multiple>
                                <option value="1">白羊座</option>
                                <option value="2">金牛座</option>
                                <option value="3">双子座</option>
                                <option value="4">巨蟹座</option>
                                <option value="5">狮子座</option>
                                <option value="6">处女座</option>
                                <option value="7">天秤座</option>
                                <option value="8">天蝎座</option>
                                <option value="9">射手座</option>
                                <option value="10">山羊座</option>
                                <option value="11">水瓶座</option>
                                <option value="12">双鱼座</option>
                            </select>
                        </div>
                        <div class="form-group col-lg-6 col-xl-4">
                            <label class="d-block">旅游足迹：</label>
                            <select class="w-100" id="tourism" name="tourism" multiple></select>
                        </div>
                        <div class="form-group col-sm-6 col-md-4 col-lg-3 col-xl-2">
                            <label class="d-block">是否在线：</label>
                            <input id="online" name="online" type="checkbox">
                        </div>
                    </div>
                    <div class="form-group row justify-content-center mb-3">
                        <button class="k-button k-button-lg k-state-selected mx-3" id="advSearchBtn" type="button" onclick="conditionSearch();">精确搜索</button>
                        <button class="k-button k-button-lg mx-3" type="reset">重 置</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="adv-search-btn">
        <button class="k-button" type="button" onclick="advSearch(this);"><i class='fas fa-angle-double-down'></i>高级搜索</button>
    </div>
    <div class="card">
        <h5 class="card-header">列表展示</h5>
        <div class="card-body">
            <div id="toolbar"></div>
            <div class="border-top-0 border-bottom-0" id="listView"></div>
            <div id="pager"></div>
        </div>
    </div>
    <script id="listTemplate" type="text/x-kendo-template">
        <div class="listItem card rounded-0">
            <div class="row no-gutters">
                <div class="col-md-2 col-xl-1 border-right d-none d-md-block">
                    <div class="card-body text-center p-2">
                        <img class="img-thumbnail mb-1" src="#= photo.url #" alt="[#= kendo.toString(photo.size / 1024, '0.00') # KB]" title="#= id #：#= photo.name ##= photo.extension #">
                        <p class="mb-2">
                            <strong>#= userName #</strong>
                            <br>
                            # if (online) { #
                                <span class="dot-color k-notification-success"></span><span class="k-notification-success bg-transparent ml-2">在线</span>
                            # } else { #
                                <span class="dot-color k-notification-error"></span><span class="k-notification-error bg-transparent ml-2">离线</span>
                            # } #
                        </p>
                        <p class="text-muted mb-3"><small>#= summary #</small></p>
                        <small class="text-left">
                            <dl class="d-flex mb-0">
                                <dt class="font-weight-bold text-nowrap">年龄：</dt>
                                <dd class="text-black-50">#= age # 岁</dd>
                            </dl>
                            <dl class="d-flex mb-0">
                                <dt class="font-weight-bold text-nowrap">身高：</dt>
                                <dd class="text-black-50">#= kendo.toString(height, '0.00') # m</dd>
                            </dl>
                            <dl class="d-flex mb-0">
                                <dt class="font-weight-bold text-nowrap">血型：</dt>
                                <dd class="text-black-50">
                                    # if (bloodType === '1') { #
                                        A 型
                                    # } else if (bloodType === '2') { #
                                        B 型
                                    # } else if (bloodType === '3') { #
                                        O 型
                                    # } else if (bloodType === '4') { #
                                        AB 型
                                    # } else if (bloodType === '5') { #
                                        其他
                                    # } #
                                </dd>
                            </dl>
                            <dl class="d-flex mb-0">
                                <dt class="font-weight-bold text-nowrap">生日：</dt>
                                <dd class="text-black-50">#= birthday #</dd>
                            </dl>
                            <dl class="d-flex mb-0">
                                <dt class="font-weight-bold text-nowrap">生肖：</dt>
                                <dd class="text-black-50">#= zodiac.zodiacName #</dd>
                            </dl>
                        </small>
                    </div>
                </div>
                <div class="w-100 border-bottom d-block d-md-none">
                    <div class="card-body text-center p-2">
                        <img class="img-thumbnail mb-1" src="#= photo.url #" alt="[#= kendo.toString(photo.size / 1024, '0.00') # KB]" title="#= id #：#= photo.name ##= photo.extension #">
                        <p class="mb-2">
                            <strong class="mr-3">#= userName #</strong>
                            # if (online) { #
                                <span class="dot-color k-notification-success"></span><span class="k-notification-success bg-transparent ml-2">在线</span>
                            # } else { #
                                <span class="dot-color k-notification-error"></span><span class="k-notification-error bg-transparent ml-2">离线</span>
                            # } #
                        </p>
                        <p class="text-muted mb-3"><small>#= summary #</small></p>
                        <small>
                            <dl class="d-flex justify-content-center mb-0">
                                <dt class="font-weight-bold text-nowrap">年龄：</dt>
                                <dd class="text-black-50">#= age # 岁</dd>
                                <dt class="font-weight-bold text-nowrap ml-3">身高：</dt>
                                <dd class="text-black-50 mr-3">#= kendo.toString(height, '0.00') # m</dd>
                                <dt class="font-weight-bold text-nowrap">血型：</dt>
                                <dd class="text-black-50">
                                    # if (bloodType === '1') { #
                                        A 型
                                    # } else if (bloodType === '2') { #
                                        B 型
                                    # } else if (bloodType === '3') { #
                                        O 型
                                    # } else if (bloodType === '4') { #
                                        AB 型
                                    # } else if (bloodType === '5') { #
                                        其他
                                    # } #
                                </dd>
                            </dl>
                            <dl class="d-flex justify-content-center mb-0">
                                <dt class="font-weight-bold text-nowrap">生日：</dt>
                                <dd class="text-black-50">#= birthday #</dd>
                                <dt class="font-weight-bold text-nowrap ml-3">生肖：</dt>
                                <dd class="text-black-50">#= zodiac.zodiacName #</dd>
                            </dl>
                        </small>
                    </div>
                </div>
                <div class="col-md-10 col-xl-11">
                    <div class="card h-100 border-0 rounded-0 bg-transparent">
                        <div class="card-header d-flex align-items-center p-2">
                            <input class="k-checkbox ids" id="#= id #Ids" type="checkbox" value="#= id #"><label class="k-checkbox-label" for="#= id #Ids"></label>
                            <h5 class="mx-1 mb-0">
                                # if (gender === '1') { #
                                    <i class="fas fa-mars mars"></i>
                                # } else if (gender === '2') { #
                                    <i class="fas fa-venus venus"></i>
                                # } #
                            </h5>
                            <h4 class="mx-1 mb-0 font-weight-bold text-dark">#= realName #</h4>
                            <small class="mx-1 text-black-50">[#= nickName #]</small>
                            <span class="ml-auto">
                                <span class="d-none d-md-inline-block"><a class="k-button k-button-icontext theme-m-box k-edit-button ml-1" href="javascript:;"><span class="k-icon k-i-edit"></span>编辑</a></span>
                                <span class="d-inline-block d-md-none"><a class="k-button theme-m-box k-edit-button ml-1" href="javascript:;"><span class="k-icon k-i-edit"></span></a></span>
                                <span class="d-none d-md-inline-block"><a class="k-button k-button-icontext k-del-button ml-1" href="javascript:;" onclick="btnDestroy(event);"><span class="k-icon k-i-x"></span>删除</a></span>
                                <span class="d-inline-block d-md-none"><a class="k-button k-del-button ml-1" href="javascript:;" onclick="btnDestroy(event);"><span class="k-icon k-i-x"></span></a></span>
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <p class="col-md-6 col-xl-3">
                                    <strong>配偶生日：</strong>
                                    #= mateBirthday #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>银行卡：</strong>
                                    #= creditCard.replace(creditCard.substr(2, 12), '** **** **** **') #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>资产：</strong>
                                    #= kendo.toString(asset, 'c') #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>民族：</strong>
                                    #= nation.nationName #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>籍贯：</strong>
                                    #= nativePlace.provinceName # - #= nativePlace.cityName # - #= nativePlace.areaName #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>居住地：</strong>
                                    #= domicile.name #
                                </p>
                            </div>
                            <div class="row">
                                <p class="col-md-6 col-xl-3">
                                    <strong>语言：</strong>
                                    #= language #
                                </p>
                                <p class="col-md-6 col-xl-9">
                                    <strong>教育程度：</strong>
                                    # for (var i = 0; i < education.length; i++) { #
                                        # if (education[i] === '1') { #
                                            小学&nbsp;
                                        # } else if (education[i] === '2') { #
                                            初中&nbsp;
                                        # } else if (education[i] === '3') { #
                                            高中&nbsp;
                                        # } else if (education[i] === '4') { #
                                            中专&nbsp;
                                        # } else if (education[i] === '5') { #
                                            大专&nbsp;
                                        # } else if (education[i] === '6') { #
                                            本科&nbsp;
                                        # } else if (education[i] === '7') { #
                                            硕士&nbsp;
                                        # } else if (education[i] === '8') { #
                                            博士&nbsp;
                                        # } else if (education[i] === '9') { #
                                            其他&nbsp;
                                        # } #
                                    # } #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>毕业年份：</strong>
                                    #= graduation #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>参加工作年月：</strong>
                                    #= firstJob #
                                </p>
                            </div>
                            <div class="row">
                                <p class="col-md-6 col-xl-3">
                                    <strong>手机：</strong>
                                    #= mobile #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>电子邮件：</strong>
                                    #= email #
                                </p>
                                <p class="col-md-12 col-xl-6">
                                    <strong>个人主页：</strong>
                                    #= homepage #
                                </p>
                            </div>
                            <div class="row">
                                <p class="col-md-6 col-xl-3">
                                    <strong>起床时间：</strong>
                                    #= getUp #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>最有意义的时刻：</strong>
                                    #= importantMoment #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>性格：</strong>
                                    # if (character === 10) { #
                                        超级开朗
                                    # } else if (character === 8) { #
                                        非常开朗
                                    # } else if (character === 6) { #
                                        很开朗
                                    # } else if (character === 4) { #
                                        比较开朗
                                    # } else if (character === 2) { #
                                        有点开朗
                                    # } else if (character === 0) { #
                                        普通
                                    # } else if (character === -2) { #
                                        有点内向
                                    # } else if (character === -4) { #
                                        比较内向
                                    # } else if (character === -6) { #
                                        很内向
                                    # } else if (character === -8) { #
                                        非常内向
                                    # } else if (character === -10) { #
                                        超级内向
                                    # } #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>颜色喜好：</strong>
                                    <span style="display: inline-block; width: 60%; height: 24px; background: #= color #; border: 1px solid \#c5c5c5; border-radius: 4px; vertical-align: middle;"></span>
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>相配的星座：</strong>
                                    # for (var i = 0; i < constellation.length; i++) { #
                                        # if (constellation[i] === "1") { #
                                            白羊座&nbsp;
                                        # } else if (constellation[i] === "2") { #
                                            金牛座&nbsp;
                                        # } else if (constellation[i] === "3") { #
                                            双子座&nbsp;
                                        # } else if (constellation[i] === "4") { #
                                            巨蟹座&nbsp;
                                        # } else if (constellation[i] === "5") { #
                                            狮子座&nbsp;
                                        # } else if (constellation[i] === "6") { #
                                            处女座&nbsp;
                                        # } else if (constellation[i] === "7") { #
                                            天秤座&nbsp;
                                        # } else if (constellation[i] === "8") { #
                                            天蝎座&nbsp;
                                        # } else if (constellation[i] === "9") { #
                                            射手座&nbsp;
                                        # } else if (constellation[i] === "10") { #
                                            山羊座&nbsp;
                                        # } else if (constellation[i] === "11") { #
                                            水瓶座&nbsp;
                                        # } else if (constellation[i] === "12") { #
                                            双鱼座&nbsp;
                                        # } #
                                    # } #
                                </p>
                                <p class="col-md-6 col-xl-3">
                                    <strong>旅游足迹：</strong>
                                    # for (var i = 0; i < tourism.length; i++) { #
                                        #= tourism[i].name #&nbsp;
                                    # } #
                                </p>
                            </div>
                        </div>
                        <div class="card-footer">#= sign #</div>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script id="editTemplate" type="text/x-kendo-template">
        <div class="card rounded-0">
            <form id="editForm">
                <input name="id" type="hidden">
                <div class="row no-gutters">
                    <div class="col-md-2 col-xl-1 border-right infoArea">
                        <div class="card-body p-2">
                            <p class="text-center">
                                <img class="img-thumbnail mb-1" id="photoShow" src="#= photo.url #" alt="#= photo.name ##= photo.extension #" title="#= kendo.toString(photo.size / 1024, '0.00') # KB">
                                <input class="w-100" id="photoEdit" name="photo" type="file">
                                <span class="k-invalid-msg" data-for="photo"></span>
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>用户名：</label>
                                <input class="k-textbox w-100" name="userName" type="text" placeholder="文本框" required data-required-msg="请输入用户名！" pattern="[A-Za-z0-9]{4,16}" data-pattern-msg="请输入4-16个大小写字母或数字！">
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>密码：</label>
                                <input class="k-textbox w-100" name="password" type="password" placeholder="密码框" required data-required-msg="请输入密码！" pattern="[A-Za-z0-9]{6,20}" data-pattern-msg="请输入6-20个大小写字母或数字！">
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>确认密码：</label>
                                <input class="k-textbox w-100" name="confirmPassword" type="password" placeholder="密码框" required data-required-msg="请输入确认密码！">
                            </p>
                            <p>
                                <label class="d-block">是否在线：</label>
                                <input id="onlineEdit" name="online" type="checkbox">
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>自我介绍：</label>
                                <textarea class="k-textarea w-100" name="summary" placeholder="文本域框" required data-required-msg="请输入自我介绍！"></textarea>
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>年龄：</label>
                                <input class="w-100" id="ageEdit" name="age" type="number" placeholder="数字框" required data-required-msg="请输入年龄！">
                                <span class="k-invalid-msg" data-for="age"></span>
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>身高：</label>
                                <input class="w-100" id="heightEdit" name="height" type="number" placeholder="小数单位数字框" required data-required-msg="请输入身高！">
                                <span class="k-invalid-msg" data-for="height"></span>
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>血型：</label>
                                <select class="w-100" id="bloodTypeEdit" name="bloodType" required data-required-msg="请选择血型！">
                                    <option value="">单选下拉框</option>
                                    <option value="1">A 型</option>
                                    <option value="2">B 型</option>
                                    <option value="3">O 型</option>
                                    <option value="4">AB 型</option>
                                    <option value="5">其他</option>
                                </select>
                                <span class="k-invalid-msg" data-for="bloodType"></span>
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>生日：</label>
                                <input class="w-100" id="birthdayEdit" name="birthday" type="date" placeholder="日期框" required data-required-msg="请输入生日！">
                                <span class="k-invalid-msg" data-for="birthday"></span>
                            </p>
                            <p>
                                <label class="d-block"><strong class="k-required">*</strong>生肖：</label>
                                <input class="w-100" id="zodiacEdit" name="zodiac" type="text" placeholder="表格下拉框" required data-required-msg="请选择生肖！">
                                <span class="k-invalid-msg" data-for="zodiac"></span><span class="k-invalid-msg" data-for="zodiac_input"></span>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-10 col-xl-11">
                        <div class="card h-100 border-0 rounded-0 bg-transparent">
                            <div class="card-header d-md-flex align-items-start p-2">
                                <p class="mx-md-3 mb-md-0">
                                    <label class="d-block"><strong class="k-required">*</strong>性别：</label>
                                    <input class="k-radio" id="genderEdit1" name="gender" type="radio" value="1"><label class="k-radio-label" for="genderEdit1">男</label>
                                    <input class="k-radio" id="genderEdit2" name="gender" type="radio" value="2"><label class="k-radio-label" for="genderEdit2">女</label>
                                    <span class="k-invalid-msg" data-for="gender"></span>
                                </p>
                                <p class="mx-md-3 mb-md-0">
                                    <label class="d-block"><strong class="k-required">*</strong>姓名：</label>
                                    <input class="k-textbox w-100" name="realName" type="text" placeholder="文本框" required data-required-msg="请输入姓名！" pattern="[\\\u4E00-\\\u9FA5]{1,10}" data-pattern-msg="请输入1-10个汉字！">
                                </p>
                                <p class="mx-md-3 mb-md-0">
                                    <label class="d-block"><strong class="k-required">*</strong>昵称：</label>
                                    <input class="k-textbox w-100" name="nickName" type="text" placeholder="文本框" required data-required-msg="请输入昵称！" pattern="[A-Za-z0-9\\\s_\\\-\\\u4E00-\\\u9FA5]{2,20}" data-pattern-msg="请输入2-20个大小写字母、数字、空格、下划线、中划线或汉字！">
                                    <span class="theme-m ajax-loading"><span class="k-icon k-i-loading"></span>验证中……</span><span class="k-invalid-msg" data-for="nickName"></span>
                                </p>
                                <span class="ml-md-auto">
                                    <a class="k-button k-button-icontext theme-m-box k-update-button ml-1" href="javascript:;"><span class="k-icon k-i-check"></span>更新</a>
                                    <a class="k-button k-button-icontext k-cancel-button ml-1" href="javascript:;"><span class="k-icon k-i-cancel"></span>取消</a>
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>配偶生日：</label>
                                        <input class="w-100" id="mateBirthdayEdit" name="mateBirthday" type="date" placeholder="日期时间掩码框" pattern="^((?!年-月-日).)*$" data-pattern-msg="请输入配偶生日！">
                                        <span class="k-invalid-msg" data-for="mateBirthday"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>银行卡：</label>
                                        <input class="w-100" id="creditCardEdit" name="creditCard" type="text" placeholder="掩码框" required data-required-msg="请输入银行卡！" pattern="[\\\d\\\s]{19}" data-pattern-msg="请补足位数！">
                                        <span class="k-invalid-msg" data-for="creditCard"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>资产：</label>
                                        <input class="w-100" id="assetEdit" name="asset" type="number" placeholder="金融数字框" required data-required-msg="请输入资产！">
                                        <span class="k-invalid-msg" data-for="asset"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>民族：</label>
                                        <input class="w-100" id="nationEdit" name="nation" type="text" placeholder="输入下拉框" required data-required-msg="请选择民族！">
                                        <span class="k-invalid-msg" data-for="nation"></span><span class="k-invalid-msg" data-for="nation_input"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>籍贯：</label>
                                        <select class="w-100" id="provinceEdit" name="nativePlace" required data-required-msg="请选择省份！"></select>
                                        <span class="k-invalid-msg" data-for="nativePlace"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-none d-md-block">&nbsp;</label>
                                        <select class="w-100" id="cityEdit" name="nativePlace" required data-required-msg="请选择城市！"></select>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-none d-md-block">&nbsp;</label>
                                        <select class="w-100" id="areaEdit" name="nativePlace" required data-required-msg="请选择区县！"></select>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>居住地：</label>
                                        <input class="w-100" id="domicileEdit" name="domicile" type="text" required data-required-msg="请选择居住地！">
                                        <span class="k-invalid-msg" data-for="domicile"></span>
                                    </p>
                                </div>
                                <div class="row">
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>语言：</label>
                                        <input class="w-100" id="languageEdit" name="language" type="text" placeholder="自动完成框" required data-required-msg="请输入语言！">
                                        <span class="k-invalid-msg" data-for="language"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-9">
                                        <label class="d-block"><strong class="k-required">*</strong>教育程度：</label>
                                        <input class="k-checkbox" id="educationEdit1" name="education" type="checkbox" value="1"><label class="k-checkbox-label" for="educationEdit1">小学</label>
                                        <input class="k-checkbox" id="educationEdit2" name="education" type="checkbox" value="2"><label class="k-checkbox-label" for="educationEdit2">初中</label>
                                        <input class="k-checkbox" id="educationEdit3" name="education" type="checkbox" value="3"><label class="k-checkbox-label" for="educationEdit3">高中</label>
                                        <input class="k-checkbox" id="educationEdit4" name="education" type="checkbox" value="4"><label class="k-checkbox-label" for="educationEdit4">中专</label>
                                        <input class="k-checkbox" id="educationEdit5" name="education" type="checkbox" value="5"><label class="k-checkbox-label" for="educationEdit5">大专</label>
                                        <input class="k-checkbox" id="educationEdit6" name="education" type="checkbox" value="6"><label class="k-checkbox-label" for="educationEdit6">本科</label>
                                        <input class="k-checkbox" id="educationEdit7" name="education" type="checkbox" value="7"><label class="k-checkbox-label" for="educationEdit7">硕士</label>
                                        <input class="k-checkbox" id="educationEdit8" name="education" type="checkbox" value="8"><label class="k-checkbox-label" for="educationEdit8">博士</label>
                                        <input class="k-checkbox" id="educationEdit9" name="education" type="checkbox" value="9"><label class="k-checkbox-label" for="educationEdit9">其他</label>
                                        <span class="k-invalid-msg" data-for="education"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>毕业年份：</label>
                                        <input class="w-100" id="graduationEdit" name="graduation" placeholder="年份框" required data-required-msg="请输入毕业年份！">
                                        <span class="k-invalid-msg" data-for="graduation"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>参加工作年月：</label>
                                        <input class="w-100" id="firstJobEdit" name="firstJob" type="month" placeholder="月份框" required data-required-msg="请输入参加工作年月！">
                                        <span class="k-invalid-msg" data-for="firstJob"></span>
                                    </p>
                                </div>
                                <div class="row">
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>手机：</label>
                                        <input class="k-textbox w-100" name="mobile" type="tel" placeholder="手机框" required data-required-msg="请输入手机！" pattern="^1(3[0-9]|4[579]|5[0-35-9]|6[6]|7[0135-8]|8[0-9]|9[89])\\\d{8}$" data-pattern-msg="手机格式不正确！">
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>电子邮件：</label>
                                        <input class="k-textbox w-100" name="email" type="email" placeholder="电子邮件框" required data-required-msg="请输入电子邮件！" data-email-msg="电子邮件格式不正确！">
                                    </p>
                                    <p class="col-md-12 col-xl-6">
                                        <label class="d-block"><strong class="k-required">*</strong>个人主页：</label>
                                        <input class="k-textbox w-100" name="homepage" type="url" placeholder="网址框" required data-required-msg="请输入个人主页！" data-url-msg="网址格式不正确！">
                                    </p>
                                </div>
                                <div class="row">
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>起床时间：</label>
                                        <input class="w-100" id="getUpEdit" name="getUp" type="time" placeholder="时间框" required data-required-msg="请输入起床时间！">
                                        <span class="k-invalid-msg" data-for="getUp"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>最有意义的时刻：</label>
                                        <input class="w-100" id="importantMomentEdit" name="importantMoment" type="datetime" placeholder="日期时间框" required data-required-msg="请输入最有意义的时刻！">
                                        <span class="k-invalid-msg" data-for="importantMoment"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>性格：</label>
                                        <input class="w-100" id="characterEdit" name="character" type="range" required data-required-msg="请选择性格！" value="0">
                                        <span class="k-invalid-msg" data-for="character"></span>
                                    </p>
                                    <p class="col-md-6 col-xl-3">
                                        <label class="d-block"><strong class="k-required">*</strong>颜色喜好：</label>
                                        <input class="w-100" id="colorEdit" name="color" required data-required-msg="请选择颜色喜好！">
                                        <span class="k-invalid-msg" data-for="color"></span>
                                    </p>
                                    <p class="col-md-6">
                                        <label class="d-block"><strong class="k-required">*</strong>相配的星座：</label>
                                        <select class="w-100" id="constellationEdit" name="constellation" multiple required data-required-msg="请选择相配的星座！">
                                            <option value="1">白羊座</option>
                                            <option value="2">金牛座</option>
                                            <option value="3">双子座</option>
                                            <option value="4">巨蟹座</option>
                                            <option value="5">狮子座</option>
                                            <option value="6">处女座</option>
                                            <option value="7">天秤座</option>
                                            <option value="8">天蝎座</option>
                                            <option value="9">射手座</option>
                                            <option value="10">山羊座</option>
                                            <option value="11">水瓶座</option>
                                            <option value="12">双鱼座</option>
                                        </select>
                                        <span class="k-invalid-msg" data-for="constellation"></span>
                                    </p>
                                    <p class="col-md-6">
                                        <label class="d-block"><strong class="k-required">*</strong>旅游足迹：</label>
                                        <select class="w-100" id="tourismEdit" name="tourism" multiple required data-required-msg="请选择旅游足迹！"></select>
                                        <span class="k-invalid-msg" data-for="tourism"></span>
                                    </p>
                                </div>
                            </div>
                            <div class="card-footer">
                                <label class="d-block"><strong class="k-required">*</strong>签名：</label>
                                <textarea class="w-100" id="signEdit" name="sign" required data-required-msg="请输入签名！"></textarea>
                                <span class="k-invalid-msg" data-for="sign"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </script>
    <!-- 临时修复 Kendo UI 自定义列表复选框无法选中的 BUG -->
    <style scoped>
        @media only screen and (max-width: 767px) {
            #editForm .infoArea {
                border-right: 0 !important;
                border-bottom: 1px solid #dee2e6;
            }
        }
        .k-upload .k-dropzone .k-upload-status {
            display: none;
        }
        .ids {
            z-index: 1;
            width: 16px;
            height: 16px;
            clip: auto;
            pointer-events: auto;
        }
    </style>
</script>