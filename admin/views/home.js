$(function () {
    var pathValue="admin-views-home";
    if(developmentModel){

    }else{
        var token=sessionStorage.getItem('token');
        if(!token||token==""){
            outLoginLims();
            return;
        }
    }

    if(systemUpgrade==""){
        var w = fullw-($("#aside").width()+60)-$("#menuH").width();
        $(".k-tabstrip-items").css({
            "z-index":"999",
            "left":"160px",
            "width":w+"px",
            "height":40+"px",
            "position":"fixed",
            "margin-top":"-40px",
            "overflow":"hidden",
        });
        systemUpgrade="index.html";
    }

    var initData=function(){
        return {};
    }
    var init=function(){
        
        //$("#hasChart").css({"height":(fullh-65)+"px"});
        // 首页统计数据获取
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl: 'modular/home/<USER>/countNumbers',
            ajaxData:{},
            succeed: function (res) {
                if(res["info"]){
                    var info=res["info"];
                    var sampleNumbers=info["sampleNumbers"];
                    var projectNumbers=info["projectNumbers"];
                    var onlineNumbers=info["onlineNumbers"];
                    var loginNumbers=info["loginNumbers"];
                    var zlSampleNumbers=info["zlSampleNumbers"];
                    setTimeout(function () {
                        // 浏览量
                        new CountUp('sampleNumbers', 0, sampleNumbers).start();
                        // 访问次数
                        new CountUp('projectNumbers', 0, projectNumbers).start();
                        // 访客数
                        new CountUp('onlineNumbers', 0, onlineNumbers).start();
                        // IP 数
                        new CountUp('loginNumbers', 0, loginNumbers).start();
                        // 质量数量
                        new CountUp('zlSampleNumbers',0,zlSampleNumbers).start();
                    }, 500);

                    var onlinePersonNames=info["onlinePersonNames"];
                    $("#onlineNumbers").attr("title",onlinePersonNames);
                }
            }
        });
    }

    funcPushs(pathValue,{
        "initData":initData,
        "init":init,
    });
});