<script id="treelistTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">普通树形列表</h5>
                <div class="card-body">
                    <div id="generalTreeList"></div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">弹出框编辑树形列表</h5>
                <div class="card-body">
                    <div id="popupTreeList"></div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">行内编辑树形列表</h5>
                <div class="card-body">
                    <div id="inlineTreeList"></div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">单元格编辑树形列表</h5>
                <div class="card-body">
                    <div id="incellTreeList"></div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">拖放树形列表</h5>
                <div class="card-body">
                    <div id="dragTreeList"></div>
                </div>
            </div>
        </div>
    </div>
    <script id="detailsTemplate" type="text/x-kendo-template">
        <div class="k-edit-form-container">
            <div class="k-edit-label py-0">ID：</div>
            <div class="k-edit-field">#= id #</div>
            <div class="k-edit-label py-0">用户名：</div>
            <div class="k-edit-field">#= userName #</div>
            <div class="k-edit-label py-0">姓名：</div>
            <div class="k-edit-field">#= realName #</div>
            <div class="k-edit-label py-0">昵称：</div>
            <div class="k-edit-field">#= nickName #</div>
            <div class="k-edit-label py-0">状态：</div>
            <div class="k-edit-field">
                # if (online) { #
                    <span class="dot-color k-notification-success"></span><span class="k-notification-success bg-transparent ml-2">在线</span>
                # } else { #
                    <span class="dot-color k-notification-error"></span><span class="k-notification-error bg-transparent ml-2">离线</span>
                # } #
            </div>
            <div class="k-edit-label py-0">性别：</div>
            <div class="k-edit-field">
                # if (gender === '1') { #
                    男 <i class="fas fa-mars mars"></i>
                # } else if (gender === '2') { #
                    女 <i class="fas fa-venus venus"></i>
                # } #
            </div>
            <div class="k-edit-label py-0">年龄：</div>
            <div class="k-edit-field">#= age # 岁</div>
            <div class="k-edit-label py-0">身高：</div>
            <div class="k-edit-field">#= kendo.toString(height, '0.00') # m</div>
            <div class="k-edit-label py-0">血型：</div>
            <div class="k-edit-field">
                # if (bloodType === '1') { #
                    A 型
                # } else if (bloodType === '2') { #
                    B 型
                # } else if (bloodType === '3') { #
                    O 型
                # } else if (bloodType === '4') { #
                    AB 型
                # } else if (bloodType === '5') { #
                    其他
                # } #
            </div>
            <div class="k-edit-label py-0">生日：</div>
            <div class="k-edit-field">#= birthday #</div>
            <div class="k-edit-label py-0">配偶生日：</div>
            <div class="k-edit-field">#= mateBirthday #</div>
            <div class="k-edit-label py-0">银行卡：</div>
            <div class="k-edit-field">#= creditCard.replace(creditCard.substr(2, 12), '** **** **** **') #</div>
            <div class="k-edit-label py-0">资产：</div>
            <div class="k-edit-field">#= kendo.toString(asset, 'c') #</div>
            <div class="k-edit-label py-0">籍贯：</div>
            <div class="k-edit-field">#= nativePlace.provinceName # - #= nativePlace.cityName # - #= nativePlace.areaName #</div>
            <div class="k-edit-label py-0">居住地：</div>
            <div class="k-edit-field">#= domicile.name #</div>
            <div class="k-edit-label py-0">民族：</div>
            <div class="k-edit-field">#= nation.nationName #</div>
            <div class="k-edit-label py-0">生肖：</div>
            <div class="k-edit-field">#= zodiac.zodiacName #</div>
            <div class="k-edit-label py-0">语言：</div>
            <div class="k-edit-field">#= language #</div>
            <div class="k-edit-label py-0">教育程度：</div>
            <div class="k-edit-field">
                # for (var i = 0; i < education.length; i++) { #
                    # if (education[i] === '1') { #
                        小学&nbsp;
                    # } else if (education[i] === '2') { #
                        初中&nbsp;
                    # } else if (education[i] === '3') { #
                        高中&nbsp;
                    # } else if (education[i] === '4') { #
                        中专&nbsp;
                    # } else if (education[i] === '5') { #
                        大专&nbsp;
                    # } else if (education[i] === '6') { #
                        本科&nbsp;
                    # } else if (education[i] === '7') { #
                        硕士&nbsp;
                    # } else if (education[i] === '8') { #
                        博士&nbsp;
                    # } else if (education[i] === '9') { #
                        其他&nbsp;
                    # } #
                # } #
            </div>
            <div class="k-edit-label py-0">毕业年份：</div>
            <div class="k-edit-field">#= graduation #</div>
            <div class="k-edit-label py-0">参加工作年月：</div>
            <div class="k-edit-field">#= firstJob #</div>
            <div class="k-edit-label py-0">手机：</div>
            <div class="k-edit-field">#= mobile #</div>
            <div class="k-edit-label py-0">电子邮件：</div>
            <div class="k-edit-field">#= email #</div>
            <div class="k-edit-label py-0">个人主页：</div>
            <div class="k-edit-field">#= homepage #</div>
            <div class="k-edit-label py-0">起床时间：</div>
            <div class="k-edit-field">#= getUp #</div>
            <div class="k-edit-label py-0">最有意义的时刻：</div>
            <div class="k-edit-field">#= importantMoment #</div>
            <div class="k-edit-label py-0">性格：</div>
            <div class="k-edit-field">
                # if (character === 10) { #
                    超级开朗
                # } else if (character === 8) { #
                    非常开朗
                # } else if (character === 6) { #
                    很开朗
                # } else if (character === 4) { #
                    比较开朗
                # } else if (character === 2) { #
                    有点开朗
                # } else if (character === 0) { #
                    普通
                # } else if (character === -2) { #
                    有点内向
                # } else if (character === -4) { #
                    比较内向
                # } else if (character === -6) { #
                    很内向
                # } else if (character === -8) { #
                    非常内向
                # } else if (character === -10) { #
                    超级内向
                # } #
            </div>
            <div class="k-edit-label py-0">颜色喜好：</div>
            <div class="k-edit-field">
                <span style="display: inline-block; width: 10%; height: 24px; background: #= color #; border: 1px solid \#c5c5c5; border-radius: 4px; vertical-align: middle;"></span>
            </div>
            <div class="k-edit-label py-0">相配的星座：</div>
            <div class="k-edit-field">
                # for (var i = 0; i < constellation.length; i++) { #
                    # if (constellation[i] === "1") { #
                        白羊座&nbsp;
                    # } else if (constellation[i] === "2") { #
                        金牛座&nbsp;
                    # } else if (constellation[i] === "3") { #
                        双子座&nbsp;
                    # } else if (constellation[i] === "4") { #
                        巨蟹座&nbsp;
                    # } else if (constellation[i] === "5") { #
                        狮子座&nbsp;
                    # } else if (constellation[i] === "6") { #
                        处女座&nbsp;
                    # } else if (constellation[i] === "7") { #
                        天秤座&nbsp;
                    # } else if (constellation[i] === "8") { #
                        天蝎座&nbsp;
                    # } else if (constellation[i] === "9") { #
                        射手座&nbsp;
                    # } else if (constellation[i] === "10") { #
                        山羊座&nbsp;
                    # } else if (constellation[i] === "11") { #
                        水瓶座&nbsp;
                    # } else if (constellation[i] === "12") { #
                        双鱼座&nbsp;
                    # } #
                # } #
            </div>
            <div class="k-edit-label py-0">旅游足迹：</div>
            <div class="k-edit-field">
                # for (var i = 0; i < tourism.length; i++) { #
                    #= tourism[i].name #&nbsp;
                # } #
            </div>
            <div class="k-edit-label py-0">自我介绍：</div>
            <div class="k-edit-field">#= summary #</div>
            <div class="k-edit-label py-0">头像：</div>
            <div class="k-edit-field">
                <img class="img-thumbnail w-25" src="#= photo.url #" alt="[#= kendo.toString(photo.size / 1024, '0.00') # KB]" title="#= photo.name ##= photo.extension #">
            </div>
            <div class="k-edit-label py-0">签名：</div>
            <div class="k-edit-field">#= sign #</div>
        </div>
    </script>
</script>