<script id="tooltipTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">提示框</h5>
                <div class="card-body text-center">
                    <button class="k-button theme-m-box" id="tipMsgCenter" type="button">中提示</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">提示框</h5>
                <div class="card-body text-center">
                    <button class="k-button theme-m-box" id="tipMsgTop" type="button">上提示</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">提示框</h5>
                <div class="card-body text-center">
                    <button class="k-button theme-m-box" id="tipMsgBottom" type="button">下提示</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">提示框</h5>
                <div class="card-body text-center">
                    <button class="k-button theme-m-box" id="tipMsgLeft" type="button">左提示</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">提示框</h5>
                <div class="card-body text-center">
                    <button class="k-button theme-m-box" id="tipMsgRight" type="button">右提示</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义提示框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-state-selected" id="customTooltip" type="button">点击弹出提示</button>
                </div>
            </div>
        </div>
    </div>
</script>