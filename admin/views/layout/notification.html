<script id="notificationTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">成功通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-success" id="noticeMsgSuccessLeftTop" type="button">左上</button>
                    <button class="k-button k-notification-success" id="noticeMsgSuccessTop" type="button">上</button>
                    <button class="k-button k-notification-success" id="noticeMsgSuccessRightTop" type="button">右上</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">信息通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-info" id="noticeMsgInfoLeftTop" type="button">左上</button>
                    <button class="k-button k-notification-info" id="noticeMsgInfoTop" type="button">上</button>
                    <button class="k-button k-notification-info" id="noticeMsgInfoRightTop" type="button">右上</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">警告通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-warning" id="noticeMsgWarningLeftTop" type="button">左上</button>
                    <button class="k-button k-notification-warning" id="noticeMsgWarningTop" type="button">上</button>
                    <button class="k-button k-notification-warning" id="noticeMsgWarningRightTop" type="button">右上</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">错误通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-error" id="noticeMsgErrorLeftTop" type="button">左上</button>
                    <button class="k-button k-notification-error" id="noticeMsgErrorTop" type="button">上</button>
                    <button class="k-button k-notification-error" id="noticeMsgErrorRightTop" type="button">右上</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">成功通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-success" id="noticeMsgSuccessLeft" type="button">左</button>
                    <button class="k-button k-notification-success" id="noticeMsgSuccessCenter" type="button">中</button>
                    <button class="k-button k-notification-success" id="noticeMsgSuccessRight" type="button">右</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">信息通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-info" id="noticeMsgInfoLeft" type="button">左</button>
                    <button class="k-button k-notification-info" id="noticeMsgInfoCenter" type="button">中</button>
                    <button class="k-button k-notification-info" id="noticeMsgInfoRight" type="button">右</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">警告通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-warning" id="noticeMsgWarningLeft" type="button">左</button>
                    <button class="k-button k-notification-warning" id="noticeMsgWarningCenter" type="button">中</button>
                    <button class="k-button k-notification-warning" id="noticeMsgWarningRight" type="button">右</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">错误通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-error" id="noticeMsgErrorLeft" type="button">左</button>
                    <button class="k-button k-notification-error" id="noticeMsgErrorCenter" type="button">中</button>
                    <button class="k-button k-notification-error" id="noticeMsgErrorRight" type="button">右</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">成功通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-success" id="noticeMsgSuccessLeftBottom" type="button">左下</button>
                    <button class="k-button k-notification-success" id="noticeMsgSuccessBottom" type="button">下</button>
                    <button class="k-button k-notification-success" id="noticeMsgSuccessRightBottom" type="button">右下</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">信息通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-info" id="noticeMsgInfoLeftBottom" type="button">左下</button>
                    <button class="k-button k-notification-info" id="noticeMsgInfoBottom" type="button">下</button>
                    <button class="k-button k-notification-info" id="noticeMsgInfoRightBottom" type="button">右下</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">警告通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-warning" id="noticeMsgWarningLeftBottom" type="button">左下</button>
                    <button class="k-button k-notification-warning" id="noticeMsgWarningBottom" type="button">下</button>
                    <button class="k-button k-notification-warning" id="noticeMsgWarningRightBottom" type="button">右下</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">错误通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-error" id="noticeMsgErrorLeftBottom" type="button">左下</button>
                    <button class="k-button k-notification-error" id="noticeMsgErrorBottom" type="button">下</button>
                    <button class="k-button k-notification-error" id="noticeMsgErrorRightBottom" type="button">右下</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-success" id="customNotificationSuccess" type="button">成功通知</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-info" id="customNotificationInfo" type="button">信息通知</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-warning" id="customNotificationWarning" type="button">警告通知</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-notification-error" id="customNotificationError" type="button">错误通知</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-state-selected" id="customNotificationQuestion" type="button">提问通知</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button k-state-selected" id="customNotificationEmail" type="button">邮件通知</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">隐藏通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button theme-m-box" id="hideAllNotification" type="button">隐藏所有自定义通知</button>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">指定位置通知框</h5>
                <div class="card-body text-center">
                    <button class="k-button theme-m-box" id="staticNotification" type="button">指定位置通知</button>
                    <div id="notificationHere"></div>
                </div>
            </div>
        </div>
    </div>
</script>