<script id="daterangepickerTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">DOM 日期范围框</h5>
                <div class="card-body">
                    <div id="domDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">普通日期范围框</h5>
                <div class="card-body">
                    <div id="generalDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">只读日期范围框</h5>
                <div class="card-body">
                    <div id="readonlyDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">禁用日期范围框</h5>
                <div class="card-body">
                    <div id="disabledDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">默认值日期范围框</h5>
                <div class="card-body">
                    <div id="defaultValueDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">星期数日期范围框</h5>
                <div class="card-body">
                    <div id="weekDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">无标签日期范围框</h5>
                <div class="card-body">
                    <div id="noLabelDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">屏蔽日期范围框</h5>
                <div class="card-body">
                    <div id="shieldDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">节假日日期范围框</h5>
                <div class="card-body">
                    <div id="holidayDateRangePicker"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">农历日期范围框</h5>
                <div class="card-body">
                    <div id="lunarDateRangePicker"></div>
                </div>
            </div>
        </div>
    </div>
    <style scoped>
        .k-calendar .fa-star {
            color: #f35800;
        }
        .festival {
            border-color: #f35800;
            background-color: #fff;
            color: #f35800;
        }
        #lunarDateRangePicker_dateview .k-calendar-view {
            width: 48em;
            height: 24em;
        }
        #lunarDateRangePicker_dateview .k-calendar-view > table,
        #lunarDateRangePicker_dateview .k-calendar-view .k-link {
            width: 100%;
            height: 100%;
        }
        #lunarDateRangePicker_dateview .k-calendar-view .k-century .k-link {
            text-align: center;
        }
    </style>
</script>