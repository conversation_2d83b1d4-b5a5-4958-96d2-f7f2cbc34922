<script id="autocompleteTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">DOM 自动完成框</h5>
                <div class="card-body">
                    <input id="domAutoComplete" type="text" placeholder="自动完成框" value="北京市,上海市">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">普通自动完成框</h5>
                <div class="card-body">
                    <input id="generalAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">只读自动完成框</h5>
                <div class="card-body">
                    <input id="readonlyAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">禁用自动完成框</h5>
                <div class="card-body">
                    <input id="disabledAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">默认值自动完成框</h5>
                <div class="card-body">
                    <input id="defaultValueAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">筛选自动完成框</h5>
                <div class="card-body">
                    <input id="filterAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义首尾自动完成框</h5>
                <div class="card-body">
                    <input id="headerFooterAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义分组自动完成框</h5>
                <div class="card-body">
                    <input class="w-100" id="groupAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义列表项自动完成框</h5>
                <div class="card-body">
                    <input id="customAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">新增项自动完成框</h5>
                <div class="card-body">
                    <input id="addItemAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义分隔符自动完成框</h5>
                <div class="card-body">
                    <input id="separatorAutoComplete" type="text">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">等宽自动完成框</h5>
                <div class="card-body">
                    <input class="w-100" id="widthAutoComplete" type="text">
                </div>
            </div>
        </div>
    </div>
</script>