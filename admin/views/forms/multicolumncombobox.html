<script id="multicolumncomboboxTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">DOM 表格下拉框</h5>
                <div class="card-body">
                    <input id="domMultiColumnComboBox" placeholder="表格下拉框" value="31">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">普通表格下拉框</h5>
                <div class="card-body">
                    <input id="generalMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">只读表格下拉框</h5>
                <div class="card-body">
                    <input id="readonlyMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">禁用表格下拉框</h5>
                <div class="card-body">
                    <input id="disabledMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">默认值表格下拉框</h5>
                <div class="card-body">
                    <input id="defaultValueMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">筛选表格下拉框</h5>
                <div class="card-body">
                    <input id="filterMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义首尾表格下拉框</h5>
                <div class="card-body">
                    <input id="headerFooterMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义分组表格下拉框</h5>
                <div class="card-body">
                    <input class="w-100" id="groupMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义列表项表格下拉框</h5>
                <div class="card-body">
                    <input id="customMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">新增项表格下拉框</h5>
                <div class="card-body">
                    <input id="addItemMultiColumnComboBox">
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">等宽表格下拉框</h5>
                <div class="card-body">
                    <input class="w-100" id="widthMultiColumnComboBox">
                </div>
            </div>
        </div>
    </div>
</script>