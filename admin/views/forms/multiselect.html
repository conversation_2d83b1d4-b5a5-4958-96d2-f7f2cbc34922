<script id="multiselectTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">DOM 多选下拉框</h5>
                <div class="card-body">
                    <select id="domMultiSelect" multiple>
                        <option value="">多选下拉框</option>
                        <option value="11" selected></option>
                        <option value="31" selected></option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">普通多选下拉框</h5>
                <div class="card-body">
                    <select id="generalMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">只读多选下拉框</h5>
                <div class="card-body">
                    <select id="readonlyMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">禁用多选下拉框</h5>
                <div class="card-body">
                    <select id="disabledMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">默认值多选下拉框</h5>
                <div class="card-body">
                    <select id="defaultValueMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">筛选多选下拉框</h5>
                <div class="card-body">
                    <select id="filterMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义首尾多选下拉框</h5>
                <div class="card-body">
                    <select id="headerFooterMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义分组多选下拉框</h5>
                <div class="card-body">
                    <select class="w-100" id="groupMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">自定义列表项多选下拉框</h5>
                <div class="card-body">
                    <select id="customMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">新增项多选下拉框</h5>
                <div class="card-body">
                    <select id="addItemMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">统计模式多选下拉框</h5>
                <div class="card-body">
                    <select id="tagMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">数量限制多选下拉框</h5>
                <div class="card-body">
                    <select id="maxMultiSelect" multiple></select>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">等宽多选下拉框</h5>
                <div class="card-body">
                    <select class="w-100" id="widthMultiSelect" multiple></select>
                </div>
            </div>
        </div>
    </div>
    <style scoped>
        #customMultiSelect_taglist img {
            width: 20px;
            height: 20px;
        }
    </style>
</script>