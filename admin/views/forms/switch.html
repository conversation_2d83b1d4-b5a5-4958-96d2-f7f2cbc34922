<script id="switchTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">DOM 转换框</h5>
                <div class="card-body">
                    <input id="domSwitch" type="checkbox" checked>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">普通转换框</h5>
                <div class="card-body">
                    <input id="generalSwitch" type="checkbox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">只读转换框</h5>
                <div class="card-body">
                    <input id="readonlySwitch" type="checkbox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">禁用转换框</h5>
                <div class="card-body">
                    <input id="disabledSwitch" type="checkbox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">默认值转换框</h5>
                <div class="card-body">
                    <input id="defaultValueSwitch" type="checkbox">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">文字转换框</h5>
                <div class="card-body">
                    <input id="textSwitch" type="checkbox">
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">等宽转换框</h5>
                <div class="card-body">
                    <input id="widthSwitch" type="checkbox">
                </div>
            </div>
        </div>
    </div>
</script>