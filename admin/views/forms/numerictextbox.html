<script id="numerictextboxTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">DOM 数字框</h5>
                <div class="card-body">
                    <input id="domNumericTextBox" type="number" min="1" max="10" step="1" placeholder="数字框" value="6">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">普通数字框</h5>
                <div class="card-body">
                    <input id="generalNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">只读数字框</h5>
                <div class="card-body">
                    <input id="readonlyNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">禁用数字框</h5>
                <div class="card-body">
                    <input id="disabledNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">默认值数字框</h5>
                <div class="card-body">
                    <input id="defaultValueNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">四舍五入小数数字框</h5>
                <div class="card-body">
                    <input id="decimalNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">单位数字框</h5>
                <div class="card-body">
                    <input id="formatNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">百分比数字框</h5>
                <div class="card-body">
                    <input id="percentageNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">金融数字框</h5>
                <div class="card-body">
                    <input id="financeNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">科学计数数字框</h5>
                <div class="card-body">
                    <input id="scientificNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">因子倍数数字框</h5>
                <div class="card-body">
                    <input id="factorNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">直接截取数字框</h5>
                <div class="card-body">
                    <input id="truncateNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">小数位数输入限制数字框</h5>
                <div class="card-body">
                    <input id="restrictNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-3">
            <div class="card mb-3">
                <h5 class="card-header">无箭头数字框</h5>
                <div class="card-body">
                    <input id="spinnerNumericTextBox" type="number">
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">等宽数字框</h5>
                <div class="card-body">
                    <input class="w-100" id="widthNumericTextBox" type="number">
                </div>
            </div>
        </div>
    </div>
</script>