<script id="editorTemp" type="text/x-kendo-template">
    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">DOM 富文本框</h5>
                <div class="card-body">
                    <textarea id="domEditor">
                        &lt;div align="center"&gt;
                            &lt;p&gt;
                                &lt;a href="https://www.telerik.com/kendo-ui"&gt;
                                    &lt;img src="https://raw.githubusercontent.com/IKKI2000/KendoUI-Admin-Site/master/img/logo.png" alt="LOGO"&gt;
                                &lt;/a&gt;
                            &lt;/p&gt;
                            &lt;h1&gt;🌸&nbsp;Kendo UI Admin &amp; Site&nbsp;🌌&lt;/h1&gt;
                            &lt;p&gt;Kendo UI Admin &amp;amp; Site base on Kendo UI for jQuery and Bootstrap 4.&lt;/p&gt;
                            &lt;p&gt;GitHub Demo: &lt;a href="http://127.0.0.1:8072/"&gt;http://127.0.0.1:8072/&lt;/a&gt;&lt;/p&gt;
                            &lt;p&gt;码云演示：&lt;a href="https://ikki2000.gitee.io/kendoui-admin-site/index_gitee.html"&gt;https://ikki2000.gitee.io/kendoui-admin-site/index_gitee.html&lt;/a&gt;&lt;/p&gt;
                        &lt;/div&gt;
                    </textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">普通富文本框</h5>
                <div class="card-body">
                    <textarea id="generalEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">默认值富文本框</h5>
                <div class="card-body">
                    <textarea id="defaultValueEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">行内编辑富文本框</h5>
                <div class="card-body">
                    <div class="text-center mt-5" id="inlineEditor">
                        <p>
                            <a href="https://www.telerik.com/kendo-ui">
                                <img src="https://raw.githubusercontent.com/IKKI2000/KendoUI-Admin-Site/master/img/logo.png" alt="LOGO">
                            </a>
                        </p>
                        <h1>🌸 Kendo UI Admin & Site 🌌</h1>
                        <p>Kendo UI Admin &amp; Site base on Kendo UI for jQuery and Bootstrap 4.</p>
                        <p>GitHub Demo: <a href="http://127.0.0.1:8072/">http://127.0.0.1:8072/</a></p>
                        <p>码云演示：<a href="https://ikki2000.gitee.io/kendoui-admin-site/index_gitee.html">https://ikki2000.gitee.io/kendoui-admin-site/index_gitee.html</a></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">清除粘贴富文本框</h5>
                <div class="card-body">
                    <textarea id="pasteCleanupEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">自定义代码块富文本框</h5>
                <div class="card-body">
                    <textarea id="snippetEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">自定义工具栏富文本框</h5>
                <div class="card-body">
                    <textarea id="customEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">自定义 PDF 导出富文本框</h5>
                <div class="card-body">
                    <textarea id="pdfEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">自定义图片上传富文本框</h5>
                <div class="card-body">
                    <textarea id="imageEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">自定义文件上传富文本框</h5>
                <div class="card-body">
                    <textarea id="fileEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">全功能富文本框</h5>
                <div class="card-body">
                    <textarea id="fullEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">不编码富文本框</h5>
                <div class="card-body">
                    <textarea id="noEncodedEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-3">
                <h5 class="card-header">序列化富文本框</h5>
                <div class="card-body">
                    <textarea id="serializationEditor"></textarea>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="card mb-3">
                <h5 class="card-header">等宽富文本框</h5>
                <div class="card-body">
                    <textarea class="w-100" id="widthEditor"></textarea>
                </div>
            </div>
        </div>
    </div>
    <style scoped>
        .fa-github {
            font-size: 16px;
        }
        .k-imagebrowser .k-thumb img {
            width: 80px;
        }
    </style>
</script>