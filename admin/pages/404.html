<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge, chrome=1">
        <meta name="renderer" content="webkit">
        <meta name="force-rendering" content="webkit">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="keywords" content="BioMarker,LIMS">
        <meta name="description" content="BioMarkerLIMS">
        <meta name="author" content="BioMarker &amp; Huakai">
        <meta name="copyright" content="">
        <title>BioMarker LIMS by Huakai</title>
        <base href="http://127.0.0.1:8072/" type="admin">
        <link href="img/favicon.png" rel="icon" type="image/png">
        <link href="css/bootstrap.min.css" rel="stylesheet">
        <link href="css/themes/theme_default.min.css" rel="stylesheet">
        <link href="css/amikoko.admin.css" rel="stylesheet">
        <script src="js/jquery.min.js"></script>
    </head>
    <body>
        <div class="position-absolute w-100 h-100 d-flex flex-column justify-content-center align-items-center">
            <p><img src="img/error.png" alt="404"><span class="display-1 align-middle">404</span></p>
            <p><time class="h5 theme-m mr-2" id="countdown"></time>秒后</p>
            <p><a class="k-button k-button-lg k-state-selected" href="admin/pages/home.html">返回首页</a></p>
        </div>
        <script>
            $(function () {
                // 返回首页
                var sec = 3,
                    intervalID = setInterval(function () {
                        if (sec > 1) {
                            sec = sec - 1;
                            $('#countdown').text(sec);
                        } else {
                            clearInterval(intervalID);
                            location.href = 'admin/pages/home.html';
                        }
                    }, 1000);
                $('#countdown').text(sec);
            });
        </script>
    </body>
</html>