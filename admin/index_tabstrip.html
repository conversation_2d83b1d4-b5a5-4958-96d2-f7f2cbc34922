<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge, chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="force-rendering" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="copyright" content="">
    <title>百迈客LIMS by Huakai</title>
    <base href="http://127.0.0.1:5500/" type="admin">
    <link href="img/favicon.png" rel="icon" type="image/png">
    <link href="css/fontawesome-all.min.css" rel="stylesheet">
    <link href="css/flag-icon.min.css" rel="stylesheet">
    <link href="css/weather-icons.min.css" rel="stylesheet">
    <link href="css/kendo.ui.widgets.icon.css" rel="stylesheet">
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link id="Amikoko" href="css/themes/theme_material_blue_grey.min.css" rel="stylesheet">
    <link href="css/amikoko.admin.css" rel="stylesheet">
    <link href="css/amikoko.common.css" rel="stylesheet">
    <!-- 细滚动条样式 -->
    <link rel="stylesheet" href="css/jquery.mCustomScrollbar.css">
    
    <script type="text/javascript" src="js/commonParams.js"></script>
    <script src="js/jquery.min.js"></script>
    <script src="js/jszip.min.js"></script>
    <script src="js/kendo.all.min.js"></script>
    <script id="IKKI" src="js/global/kendo.zh-CHS.js"></script>

    <!-- 细滚动条 -->
    <script src="js/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="js/ikki.layout.js"></script>
    <script src="js/ikki.tabstrip.js"></script>
    <script src="js/ikki.js"></script>
    <script src="js/huakai.grid.js"></script>
    <script src="js/countUp.min.js"></script>
    
    <link rel="stylesheet" href="plug/viewerjs/css/viewer.min.css">
    <script src="plug/viewerjs/js/viewer.min.js"></script>    

    <!-- 板操作相关js -->
    <script src="js/board/event.js"></script>
    <script src="js/board/drag_oop.js"></script>
    <script src="js/board/tween.js"></script>

    <!-- 图表相关js -->
    <script src="js/echarts.min.js"></script>
</head>
<body id="mainBody">
    <!-- 控件 -->
    <input id="navCkb" type="checkbox">
    <input id="menuCkb" type="checkbox">
    <label for="navCkb"><span id="mask"></span></label>
    <!-- 侧栏 -->
    <aside class="theme-m-bg" id="aside" >
        <h1 onclick="javascript:$('#tab-admin-views-home').click();" >亿康LIMS </h1>
        <nav id="nav">
            <ul id="navPanelBar"></ul>
            <ul id="navMenu"></ul>
            <div id="navPanel">
                
            </div>
        </nav>
    </aside>
    <!-- 主体 -->
    <main id="main">
        <!-- 头部 -->
        <header class="theme-m" id="header">
            <label for="navCkb" onclick="toggleTabstripItems(this);" style="margin-left: 100px;" ><i class="fas fa-bars"></i></label>
            <label for="menuCkb" ><i class="fas fa-ellipsis-h"></i></label>
            <h1>Yinkon LIMS by Huakai</h1>
            <nav id="path"></nav>
            <menu id="menuH"></menu>
            <menu id="menuV"></menu>
        </header>
        <!-- 内容 -->
        <section id="section">
            <div id="container">
                <div id="contextMenu"></div>
                <div id="tab"></div>
            </div>
            <!-- 脚部 -->
            <footer id="footer">Powered by Yinkon &amp; Huakai &copy; 2018-2019</footer>
            <!-- <div class="progress" id="inProgress">
                <div class="progress-bar progress-bar-striped theme-m-bg"></div>
            </div>  -->
        </section>
    </main>
    <template id="template"></template>
</body>

<script id="hidden-0" type="text/x-kendo-template">
    <input id="{{id}}" name="{{name}}" type="hidden" />
</script>
<script id="input-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="input-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="k-textbox" id="{{id}}" name="{{name}}" {{rules}}
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
    </div>
</script>
<script id="select-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="select-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <select class="w-100" id="{{id}}" name="{{name}}" {{rules}} ></select>
    </div>
</script>
<script id="datetime-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="datetime-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="dates" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="date-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <input class="date" id="{{id}}" name="{{name}}" {{rules}} />
    </div>
</script>
<script id="textarea-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="textarea-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <textarea class="k-textarea" id="{{id}}" name="{{name}}" {{rules}}
        placeholder="{{placeholder}}"  ></textarea>
    </div>
</script>
<script id="multiSelect-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="multiSelect-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} multiSelect
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="autoComplete-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <input id="{{id}}" name="{{name}}" {{rules}} autoComplete
        type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />                                         
    </div>
</script>
<script id="upload-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="upload-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 清空 -->
            <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
            <!-- 上传 -->
            <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
            <div style="display:none;" ><input class="w-100" onchange="∑upload|this,$(this).parent().parent().children(':first').attr('id'));" multiple="files" type="file"> </div>
         </span>
    </div>  
</script>
<script id="download-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="download-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox hk-file">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <!-- 下载 -->
            <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val(),$(this).parent().find('input[type=text]')[0]);"></a>
         </span>
    </div>  
</script>
<script id="dialogOpen-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="dialogOpen-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-globe k-required" style="cursor :pointer;" onclick="∑dailogOpen|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="button-1" type="text/x-kendo-template">
    <div class="col-sm-1 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-2" type="text/x-kendo-template">
    <div class="col-sm-2 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-3" type="text/x-kendo-template">
    <div class="col-sm-3 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-4" type="text/x-kendo-template">
    <div class="col-sm-4 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-5" type="text/x-kendo-template">
    <div class="col-sm-5 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-6" type="text/x-kendo-template">
    <div class="col-sm-6 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-7" type="text/x-kendo-template">
    <div class="col-sm-7 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-8" type="text/x-kendo-template">
    <div class="col-sm-8 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-9" type="text/x-kendo-template">
    <div class="col-sm-9 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-10" type="text/x-kendo-template">
    <div class="col-sm-10 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-11" type="text/x-kendo-template">
    <div class="col-sm-11 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="button-12" type="text/x-kendo-template">
    <div class="col-sm-12 text-lg-right">
        <button class="k-button k-button-lg k-state-selected mt-2 mt-lg-0" id="{{id}}" name="{{name}}" 
            onclick="∑clickButton|$(this).parent().find('button[type=button]').html(),$(this).parent().find('button[type=button]').attr('id'),this);"
            {{rules}} type="button" >{{label}}</button>
    </div>
</script>
<script id="label-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="label-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block" {{placeholder}} >{{label}}：{{star}}</label>
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="div-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <div id="{{id}}" name="{{name}}" {{rules}} ></div>
    </div>
</script>
<script id="lineHr-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>
<script id="lineHr-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <div id="{{id}}" name="{{name}}" class="line-hr theme-m" {{rules}} >
            {{label}}{{placeholder}}
        </div>
    </div>
</script>

<script id="numberGenerate-1" type="text/x-kendo-template">
    <div class="col-sm-1">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-2" type="text/x-kendo-template">
    <div class="col-sm-2">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-3" type="text/x-kendo-template">
    <div class="col-sm-3">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-4" type="text/x-kendo-template">
    <div class="col-sm-4">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-5" type="text/x-kendo-template">
    <div class="col-sm-5">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-6" type="text/x-kendo-template">
    <div class="col-sm-6">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-7" type="text/x-kendo-template">
    <div class="col-sm-7">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-8" type="text/x-kendo-template">
    <div class="col-sm-8">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-9" type="text/x-kendo-template">
    <div class="col-sm-9">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-10" type="text/x-kendo-template">
    <div class="col-sm-10">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-11" type="text/x-kendo-template">
    <div class="col-sm-11">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>
<script id="numberGenerate-12" type="text/x-kendo-template">
    <div class="col-sm-12">
        <label class="d-block">{{label}}：{{star}}</label>
        <span class="k-textbox k-space-right w-100">
            <input id="{{id}}" name="{{name}}" {{rules}}
            type="text" placeholder="{{placeholder}}" onmousemove="$(this).attr('title',this.value);" />
            <a class="k-icon k-i-hand k-required" style="cursor :pointer;" onclick="∑numberGenerate|$(this).parent().find('input[type=text]').attr('id'),$(this).parent().find('input[type=text]').val(),this);"></a>
        </span>
    </div>  
</script>


<script id="formGroupRow" type="text/x-kendo-template">
    <div class="form-group row col-sm-12">{{content}}</div>
</script>
<script id="starFontHtml" type="text/x-kendo-template">
    <font style="font-size:12px;color:red;font-weight:bold;" >✻</font>
</script>


<script id="settings-panel" type="text/x-kendo-template">
    <div class="k-grid-settings theme-m-txt k-icon k-i-gears"  
        style="background-color:transparent !important;height: 29px; width: 26px;margin-right:-26px;
        font-size:20px;padding-top:5px;padding-left:3px;" 
        onclick="$('{{tableId}} .k-grid-settings').find('.settings-panel').stop(true,true).slideDown('fast');">
        <div class="settings-panel" >
            <div class="k-window-titlebar theme-m-bg" style="margin-top: 0px;padding:4px 20px;" >
                <span class="k-icon k-i-select-box" style="font-size:22px;-ms-flex:1;flex:1;-ms-flex-wrap:nowrap;flex-wrap:nowrap;" 
                onclick="$('{{tableId}} .settings-panel .k-window-titlebar .k-i-select-box').find('.xl').stop(true,true).slideDown('fast');" ></span>
                <div class="k-window-actions" onclick="$('{{tableId}} .k-grid-settings').find('.settings-panel').slideUp('fast');event.stopPropagation();"> 
                <a class="k-button k-bare k-button-icon k-window-action" >
                <span class="k-icon k-i-close"></span></a></div>
            </div>
            {{settingsFormHtml}}
            <div style="padding:4px 20px;position: absolute;bottom: 0;height:45px;width:100%;" >
            {{resetQueryButton}}{{queryButton}}{{saveQueryButton}}{{searchButton}}{{colsButton}}{{helpButton}}
            </div>
        </div>
    </div>
</script>

</html>