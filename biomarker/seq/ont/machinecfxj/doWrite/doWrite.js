$(document).ready(function () {
    var pathValue = "biomarker-seq-ont-machinecfxj-doWrite-doWrite";
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "BIO_ONT_RUN_INFO"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
debugger;
    var paramsValue;
    var init = function (params) {
        paramsValue = params;
    }


    var submit = function () {
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    //提交成功
                    ranmx(result["ID"]);
                    alertMsg("提交成功", "success", function () {
                        funcExce(pathValue + "pageCallBack");//执行回调
                        funcExce(pathValue + "close");//关闭页面
                    });
                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });
    }

    var ranmx = function (RANID) {

        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "queryTaskLibExMx-Check","search": {"ID":paramsValue["ID"]}} ,
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        var objectRanMx = [];
        var objectONT = [];
        var objectLIB = [];
        var objectADD = [];

        for (var i = 0; i < rows.length; i++) {

            var ONT_ID = getRandomId();   // ONT_ID  
            var POOL_LIB_ID = getRandomId();   // POOL_LIB_ID
            objectRanMx.push({
                "RUN_ID": RANID,
                "ONT_ID": ONT_ID,
            })
            objectONT.push({
                "ID": ONT_ID,
                "LIB_ID": POOL_LIB_ID,
            })
            objectLIB.push({
                "ID": rows[i]["LIBID"],
                "POOL_ID": POOL_LIB_ID,
            })
            objectADD.push({
                "TASK_ID": rows[i]["TASKLIBID"],
                "ONT_ID": ONT_ID,
                "TASK_MX_ID": rows[i]["ID"],
            })



        }

        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd = { "tableName": "BIO_ONT_RUN_INFOMX", "objects": objectRanMx };
        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

      var newUrl="system/jdbc/save/one/table/objects";
        var paramsadd = { "tableName": "BIO_ONT_INFO", "objects": objectONT };
        putAddOrUpdata(newUrl, paramsadd, "否", "推入下一步实验任务");

        var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectLIB };
        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");
        var paramsadd = { "tableName": "BIO_ADD_DATA_ONT", "objects": objectADD };
        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");
    }


    function getRandomId() {
        return 'FDSX-' + new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});