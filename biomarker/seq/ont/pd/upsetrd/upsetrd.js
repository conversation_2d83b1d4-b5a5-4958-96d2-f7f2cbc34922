$(document).ready(function() {
    var pathValue="biomarker-seq-ont-pd-upsetrd-upsetrd";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_RD_ONT"
        };
    }
    var init=function(params){
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
    }
   var subUpData=function(){
debugger;
    	var ids=paramsValue["ids"];
    	var libids=paramsValue["libids"];
    	var libcodes=paramsValue["libcodes"];
    	var ispools=paramsValue["ispools"];
    	var ONT_SM_ND=paramsValue["ONT_SM_ND"];
    	var ONT_SM_TJ=paramsValue["ONT_SM_TJ"];
    	var ONT_SM_ZL=paramsValue["ONT_SM_ZL"];
    	var ONT_SM_DATA=paramsValue["ONT_SM_DATA"];
    	var jsonData = getJsonByForm("form",pathValue);//获取表单json
    	 var object=[];
         for(var i=0;i < libids.length;i++){
        	 object.push($.extend({},jsonData,{
                        //  "ID":ids[i],
        		 "SEQ_ID":ids[i],
        		 "LIB_ID":libids[i],
        		 "ONT_STATUS":"草稿",
        		 "SEQ_PLAT":"-1",
        		 "LIBRARY_CODE":libcodes[i],
        		 "ONT_SM_ND":ONT_SM_ND[i],
        		 "ONT_SM_TJ":ONT_SM_TJ[i],
        		 "ONT_SM_ZL":ONT_SM_ZL[i],
        		 "ONT_SM_DATA":ONT_SM_DATA[i],
        		 "ISPOOL":ispools[i]
        	 }));//表单值继承
         }
         var params={"tableName":"BIO_RD_ONT","objects":object};
         var url="system/jdbc/save/batch/table";
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:url,
             ajaxData:params,
             succeed:function(result){
                 if(result["code"]>0){
                 	funcExce(pathValue+"pageCallBack");                 	
                    alertMsg("提交成功!");
                    funcExce(pathValue+"close");
                 }else{
                 	console.log(result);
                 }
             }
         });    	
    }
    funcPushs(pathValue,{
        "init":init,
        "subUpData":subUpData
    });
 });