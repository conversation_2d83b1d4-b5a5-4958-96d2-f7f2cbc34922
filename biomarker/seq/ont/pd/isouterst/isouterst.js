$(document).ready(function () {
    var pathValue = "biomarker-seq-ont-pd-isouterst-isouterst";
    var paramsValue;
    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {
            tableName: "BIO_RD_TASK"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        paramsValue = params;
        //getInfo("form1", pathValue, params);
        getInfo("form", pathValue, params);
        // 传入数组ids
        var url = "system/jdbc/query/info/" + initData().tableName; //后端请求路径
        //getInfo("form1", pathValue, params, url); //传入id 
        getInfo("form", pathValue, params, url); //传入id 
    }
    var submit = function () {
debugger;
        var jiraUpdateFlag= $("#JIRAUPDATEFLAG"+pathValue).val();
        var serverFrom= $("#SERVERFROM"+pathValue).val();
        var whetherDeliver= $("#WHETHERDELIVER"+pathValue).val();
        var fast5Handle= $("#FAST5HANDLE"+pathValue).val();
        var splitBarcode= $("#SPLITBARCODE"+pathValue).val();
        var dataplan= $("#DATA_PLAN"+pathValue).val();
        var ju;
        var wd;
        var f5;
        var sb;
        var pushsts = paramsValue["wCode"];

        if(jiraUpdateFlag == "是"){
            ju = 1;
        }else{
            ju = 0;
        }
        if(whetherDeliver == "是"){
            if(serverFrom==null || serverFrom== ""){
                alertMsg("数据来源集群为空，请填写后再提交！");
                return;
             }
            wd = 1;
        }else{
            if(dataplan==null || dataplan == ""){
                alertMsg("数据路径为空，请填写后再提交！");
                return;
             }
            wd = 0;
             serverFrom = "";
        }
        if(fast5Handle == "是"){
            f5 = 1;
        }else{
            f5 = 0;
        }
        if(splitBarcode == "是"){
            sb = 1;
        }else{
            sb = 0;
        }

        
        var object = [];
            //更新状态
            object.push({
                "jiraUpdateFlag":ju,
                "serverFrom":serverFrom,
                "whetherDeliver":wd,
                "fast5Handle":0,
                "splitBarcode":sb,
                "dataplan":dataplan,
            });
        funcExce(paramsValue["pPathValue"] + pushsts,{"object": object,"wCode":paramsValue["wCode"]});
        funcExce(pathValue + "close"); //关闭页面
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit
    });
});