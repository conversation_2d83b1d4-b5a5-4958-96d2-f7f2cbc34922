$(document).ready(function () {
    var pathValue = "biomarker-seq-ont-pd-index";
    var paramsValue;
    var initData = function () {
        return {};
    }
    var flag = 0;
    var gridNameDGrid;
    var gridNameS1 = [];
    var subGrid_N;
    var gridNameS2 = [];
    var gridNameS3 = [];
    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var flag2 = 0;
    //任务池
    var init = function (params) {
        paramsValue = params;
        var toolbar = getButtonTemplates(pathValue, [
            { name: "add", target: "addlane", title: "派单.." },
          //  { name: "exel", target: "importData1", title: "测序轮数导入" },
            { name: "exel", target: "rdtaskz", title: "研发导入(一级)/模板" },
            { name: "exel", target: "rdtask", title: "研发导入(二级)/模板" },
            { name: "delete", target: "deleteyfone", title: "研发删除(一级)" },
            { name: "delete", target: "deleteyftwo", title: "研发删除(二级)" },
        ]);

        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "QueryOntSeqTask", "objects": [["ONT"], ["正常"], ["正常"], ["1"]] },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var LIB_ID = e.data.LIB_ID;
                var readsql = "lib_sample_pool_all_list";
                if (e.data.ISPOOL == "否") {
                    readsql = "queryTaskLibExMxResult-ONT-Seq";
                }
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[LIB_ID], [ROW_ID]] },
                };
                subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS3.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        init1();
        init2();
        init3();
    }
    //单号-草稿
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "submit", target: "doGo", title: "确认提交" },
            { name: "exel", target: "importData3", title: "数据导入" },
            { name: "delete", target: "pushsdateAge", title: "删除任务" },
            { name: "delete", target: "deleteinfoyf", title: "删除研发任务" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_ont_sj_info-pd", "objects": [["草稿", "接收退回"], ["草稿", "接收退回"]] },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID= e.data.ID;
                var readsql = "lib_sample_pool_all_list-hk2";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
    }
    //已处理
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
            { name: "exel", target: "importData2", title: "测序轮数导入" },
            { name: "submit", target: "pushs", title: "发起文件检查任务" },
            { name: "submit", target: "pushsdate", title: "发起数据处理任务" },
            { name: "edit", target: "editA", title: "jira更新run编号" }
            //          { name: "submit", target: "doGodataInfoAge", title: "重新发起数据处理任务" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "seq_ont_sj_info-pd", "objects": [[
                    "上机待接收", "上机已接收", "上机退回","下机待确认",
                    "上机信息待确认", "上机信息已确认",
                    "数据下机待处理", "数据下机已处理", "数据下机已审核",
                    "数据审核退回"
                ],
                [
                    "上机待接收", "上机已接收", "上机退回","下机待确认",
                    "上机信息待确认", "上机信息已确认",
                    "数据下机待处理", "数据下机已处理", "数据下机已审核",
                    "数据审核退回"
                ]]
            },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID= e.data.ID;
                var readsql = "lib_sample_pool_all_list-hk2";
      
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);
    }
    //已完成
    var init3 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "移至待排" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "QueryOntSeqTask", "objects": [["ONT"], ["结束", "暂停", "未上机", "终止"], ["结束", "暂停", "未上机", "终止"], ["1"]] },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "lib_sample_pool_all_list";
                if (e.data.ISPOOL == "否") {
                    readsql = "queryTaskLibExMxResult-ONT-Seq";
                }
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);
    }
    var addlane = function () { debugger
        var arrIds = getGridSelectData(gridNameDGrid);
        var LIB_IDS = getSelectData(gridNameDGrid,"LIB_ID");
        
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        }
        var ROW_ID = arrIds[0]["ID"];
        var LIB_ID = arrIds[0]["LIB_ID"];
        var readsql = "lib_sample_pool_all_list";
        if (arrIds[0]["ISPOOL"] == "否") {
            readsql = "queryTaskLibExMxResult-ONT-Seq";
        }
        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": readsql, "objects": [[LIB_ID], [ROW_ID]] },
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        for (var i = 0; i < rows.length; i++) {
            var a = rows[i]["DATA_PROCESSING_TYPE"]; //数据处理类型
            var b = rows[i]["INDEX_NAME"]; //index
            var c = rows[i]["SEQ_ROUNTDS"]; //测序轮数

            // if (a == null || b == null || c == null) {
            //     alertMsg("数据处理类型或测序轮数或index为空!");
            //     return;
            // }
        }
        var url = "biomarker/seq/ont/pd/upset/upset";
        // if (arrIds[0]["YN"] == "Y") {
        //     url = "biomarker/seq/ont/pd/upsetrd/upsetrd";
        // }
        var ids = [];
        var libids = [];
        var libcodes = [];
        var ispools = [];
        var ONT_SM_ND = [];
        var ONT_SM_TJ = [];
        var ONT_SM_ZL = [];
        var ONT_SM_DATA = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            libids.push(arrIds[i]["LIB_ID"]);
            ispools.push(arrIds[i]["ISPOOL"]);
            libcodes.push(arrIds[i]["LIBRARY_CODE"]);
            ONT_SM_ND.push(arrIds[i]["ONT_SM_ND"]);
            ONT_SM_TJ.push(arrIds[i]["ONT_SM_TJ"]);
            ONT_SM_ZL.push(arrIds[i]["ONT_SM_ZL"]);
            ONT_SM_DATA.push(arrIds[i]["ONT_SM_DATA"]);
        }
        var winOpts = {
            url: url,
            title: "ONT上机排单.."
        };
        openWindow(winOpts, {"LIB_IDS":LIB_IDS,"arrIds":arrIds, "ids": ids, "libids": libids, "libcodes": libcodes, "ispools": ispools, "ONT_SM_ND": ONT_SM_ND, "ONT_SM_TJ": ONT_SM_TJ, "ONT_SM_ZL": ONT_SM_ZL, "ONT_SM_DATA": ONT_SM_DATA });
    }
    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
          gridNameS1 = [];
          gridNameS2 = [];
          gridNameS3 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }
        if (gridNameD3Grid) {
            gridNameD3Grid.dataSource.read();
        }
    }

    //确认提交
    var doGo = function () {
        debugger;
        var g = getGridSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
 
        var objectup = [];
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        for (var i = 0; i < g.length; i++) {
            var sample;
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "lib_sample_pool_all_list-hk2", "objects": [[g[i]["ID"]]] },
                succeed: function (rs) {
                    sample = rs.rows;
                }
            });
            var LIBRARY_JHDATA_SUM=0;
            for (var j = 0; j < sample.length; j++) {
                LIBRARY_JHDATA_SUM=LIBRARY_JHDATA_SUM+sample[i]["LIBRARY_JHDATA"] ;
                if (sample[j]["LIBRARY_JHDATA"] == null || sample[j]["LIBRARY_JHDATA"] == "") {
                    alertMsg("子文库编号【" + sample[j]["LIBRARY_CODE"] + "】安排数据量为空!");
                    return;
                }
            }

            var ps = "上机待接收";
            var pt = "";
            if (g[i]["ONT_TEST_MAN"] == "实验-委外") {
                ps = "已提交委外";
            }
            //更新记录
            objectup.push({
                "ID": g[i]["ID"],//联联任务ID
                "ONT_STATUS": ps,
                "SAMPLE_SUM":sample.length,
                "LIBRARY_JHDATA_SUM":LIBRARY_JHDATA_SUM,
                "SYS_INSERTTIME": time,
                "SYS_MAN": username
            });
        }
        var paramsup = { "tableName": "BIO_ONT_INFO", "objects": objectup };
        var url = "system/jdbc/save/batch/table";
        putAddOrUpdata(url, paramsup, "是", "提交");
    }

    //文件检查推送
    // var pushs = function () {
    //     openWindow({
    //         url: "biomarker/seq/ont/pd/isouterst/isouterst",
    //         title: "添加信息",
    //         currUrl: "biomarker/seq/ont/pd/index",
    //     },
    //         { pPathValue: pathValue, wCode: "fileInspect" }
    //     );
    // }

    //数据处理推送
    var pushsdate = function () {

        openWindow({
            url: "biomarker/seq/ont/pd/isouterst/isouterst",
            title: "添加信息",
            currUrl: "biomarker/seq/ont/pd/index",
        },
            { pPathValue: pathValue, wCode: "doGodataInfo" }
        );
    }

    //重新发起数据处理
    var pushsdateAge = function () {

        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验操作!");
            return;
        }
        confirmMsg("确认", "确定要删除记录吗?", "warn", function () {
            var params = { "tableName": "BIO_ONT_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }





    //文件检查
    var pushs = function () {
        if (flag2 == 1) {
            alertMsg("请勿重复点击!");
            return;

        }
        flag2 = 1;
        var arrIds = getGridSelectData(gridNameD2Grid);
        var arrIdsMx = [];
        var rows2;
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条lane记录进行操作!");
            flag2 = 0;
            return;
        }
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrDatas = getGridSelectData(gridNameS2[i]);
            arrIdsMx = arrIdsMx.concat(arrDatas);

        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_BIO_DATA_PROCESSING_TASK_ont_view", "objects": [["ONT"], ["未开始"], ["文件检查"]], "search": { "LANE_NO": [arrIds[0]["LIBRARY_CODE"]] } },
            succeed: function (rs) {
                //console.log(rs);				
                rows2 = rs["rows"];
            }
        });
        if (rows2.length > 0) {
            var gnl = confirm("此数据已推送文件检查，确认重复推送吗？");
            if (gnl == false) {
                flag2 = 0;
                return;
            }

        }

        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];
        var object = [];
        var objects = [];
        var objectjh = [];
        var objectjhyf = [];
        var rows1;
        var flag = 0;
        var readsql = "lib_sample_pool_all_list-hk";
        if (arrIds[0]["ISPOOL"] == "否") {
            readsql = "queryTaskLibExMxResult-ONT-Seq";
        }
        debugger;
        if (arrIdsMx.length == 0) {
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": readsql, "objects": [[arrIds[0]["LIB_ID"]], [arrIds[0]["SEQ_ID"]]] },
                succeed: function (rs) {
                    //console.log(rs);				
                    rows1 = rs["rows"];
                }
            });
            flag = 1;

        } else {

            //            confirmMsg("确认", "是否全部文库都发起文件检查任务?", "warn", function () {
            rows1 = arrIdsMx;
            //            })

        }

        var LIBRARY_NUM = rows1.length;
        var mid = getRandomId();
        var taskno = getTaskNo("文件检查-ONT", "DATACHECK");
        var obj = "是否全部文库都发起文件检查任务?";
        if (flag != 1) {
            obj = "是否只对选择的文库发起文件检查任务?";
        }
        confirmMsg("确认", obj, "warn", function () {
            object.push({
                "ID": mid,
                "TASK_SJ_TYPE": "文件检查",
                "TASK_NO": taskno,
                "LANE_NO": arrIds[0]["LIBRARY_CODE"],
                "LIBRARY_NUM": LIBRARY_NUM,
                "TASK_SJ_DATE": time,
                "TASK_INITIATOR": name,
                "TASK_SJ_START_DATE": time,
                "PUSH_STATUS": "待推送",
                "EXECUTION_STATUS": "未开始",
                "DATA_SJ_SOURCES": "ONT",
                "JIRAUPDATEFLAG": null,
                "SERVERFROM": null,
                "WHETHERDELIVER": null,
                "FAST5HANDLE": null,
                "SPLITBARCODE": null
            });

            var indexnames = [];
            var indextypes = [];
            for (var i = 0; i < rows1.length; i++) {
                indexnames.push(rows1[i]["INDEX_NAME"]);
                indextypes.push(rows1[i]["LIBRARY_TYPE"]);
            }
            var index;
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "QueryIndexList-PBMCD", "objects": [], "search": { "INDEX_NAME": indexnames, "INDEX_TYPE": indextypes } },
                succeed: function (rs) {
                    //console.log(rs);				
                    index = rs["rows"];
                }
            });

            for (var i = 0; i < rows1.length; i++) {

                var F_INDEX = "-";
                var R_INDEX = "-";
                for (var x = 0; x < index.length; x++) {
                    if (index[x]["INDEX_TYPE"] == rows1[x]["LIBRARY_TYPE"]) {
                        F_INDEX = index[x]["SEQ_I5_1"];
                        R_INDEX = index[x]["SEQ_I7_1"];
                    }

                }

                var mxid = getRandomId();
                objects.push({
                    "ID": mxid,
                    "BDPT_ID": mid,
                    "SPECIESTYPE": rows1[i]["TASK_LS_BIOTYPE"],
                    "SPECIESZH": rows1[i]["SPECIES_CH_NAME"],
                    "F_INDEX": F_INDEX,
                    "R_INDEX": R_INDEX,
                    "PROJECT_NAME": rows1[i]["PROJECT_NAME"],
                    "PROJECT_NO": rows1[i]["PROJECT_NO"],
                    "PROJECT_SUBNO": rows1[i]["PROJECT_SUBNO"],
                    "DATA_SUM": rows1[i]["LIBRARY_DATA"],
                    "LIBRARY_TYPE": rows1[i]["LIBRARY_TYPE_EN"],
                    "LIBRARY_CODE_Z": rows1[i]["LIBRARY_CODE"],
                    //                    "LIBRARY_CODE": rows1[i]["LIBRARY_CODE"],
                    "PROJECT_MANAGER": arrIds[0]["SYS_MAN"],
                    "PROJECT_MAN": arrIds[0]["SYS_MAN"],
                    "SAMPLE_NAME": rows1[i]["SAMPLE_NAME"],
                    "INDEX_NAME": rows1[i]["INDEX_NAME"],
                    "DATA_PATH": null,
                    "FAST5": null,
                    "ONT": null,
                    "LIBRARY_JHDATA": rows1[i]["LIBRARY_JHDATA"],
                    "BIO_CODE": rows1[i]["BIO_CODE"],
                    "TASK_LSMX_REMARKS": rows1[i]["JK_TASKMX_REMARK"],
                    "RUN_NO": null,
                    "EXECUTION_STATUS": "未开始",
                    "SPECIES": rows1[i]["SPECIES"],
                    "SPECIESTYPE": "",
                    "DATAPROCESSTYPE": rows1[i]["DATA_PROCESSING_TYPE"],
                    "LANE_NO": rows1[i]["LIBRARY_CODE"],
                    "POOL_ID": rows1[i]["POOL_ID"],
                    "TASK_LIB_ID": rows1[i]["TASKLIBID"],
                    "TASK_LIB_MXID": rows1[i]["TASKMXID"],
                    "LIB_INFO_ID": rows1[i]["ID"],
                });
                if (rows1[i]["YN"] == "Y") {
                    objectjhyf.push({
                        "ID": rows1[i]["ID"],
                        "CHECK_TASK_NO": taskno
                    });
                } else {
                    objectjh.push({
                        "ID": rows1[i]["ID"],
                        "HANDLE_TASK_NO": taskno
                    });
                }
            }
            var urlsends = "system/jdbc/save/one/table/objects";
            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK", "objects": object };
            putAddOrUpdata(urlsends, paramsup, "是", "文件检查");
            var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK_MX", "objects": objects };
            putAddOrUpdata(urlsends, paramsup, "否", "文件检查");
            if (objectjh.length > 0) {
                var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectjh };
                putAddOrUpdata(urlsend, paramsup, "", "文件检查");
            }
            if (objectjhyf.length > 0) {
                var paramsup = { "tableName": "BIO_RD_ONT_INFO", "objects": objectjhyf };
                putAddOrUpdata(urlsend, paramsup, "", "文件检查");
            }
            pushst(object, objects, "dataCheck", "文件检查");
        })
        flag2 = 0;

    }
    //重新发起数据处理
    var doGodataInfoAge = function (objst) {
        if (flag2 == 1) {
            alertMsg("请勿重复点击!");
            return;
        }
        flag2 = 1;
        var rows2;
        var arrIds = getGridSelectData(gridNameD2Grid);
        var arrIdsMx = [];
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条lane记录进行操作!");
            flag2 = 0;
            return;
        }
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrDatas = getGridSelectData(gridNameS2[i]);
            arrIdsMx = arrIdsMx.concat(arrDatas);

        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_BIO_DATA_PROCESSING_TASK_ont_view", "objects": [["ONT"], ["未开始", "结束"], ["数据处理"]], "search": { "LANE_NO": [arrIds[0]["LIBRARY_CODE"]] } },
            succeed: function (rs) {
                //console.log(rs);				
                rows2 = rs["rows"];
            }
        });
        if (rows2.length > 0) {
            var gnl = confirm("此数据已推送数据处理，确认重复推送吗？");
            if (gnl == false) {
                flag2 = 0;
                return;
            }

        }
        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];
        var object = [];
        var objects = [];
        var objectjh = [];
        var objectjhyf = [];
        var rows1;
        var flag = 0;
        var readsql = "lib_sample_pool_all_list-hk";
        if (arrIds[0]["ISPOOL"] == "否") {
            readsql = "queryTaskLibExMxResult-ONT-Seq";
        }
        debugger;
        if (arrIdsMx.length == 0) {
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": readsql, "objects": [[arrIds[0]["LIB_ID"]], [arrIds[0]["SEQ_ID"]]] },
                succeed: function (rs) {
                    //console.log(rs);				
                    rows1 = rs["rows"];
                }
            });
            flag = 1;
        } else {

            //            confirmMsg("确认", "是否全部文库都发起数据处理任务?", "warn", function () {
            rows1 = arrIdsMx;
            //            })

        }

        var LIBRARY_NUM = rows1.length;
        var mid = getRandomId();
        var taskno = getTaskNo("数据处理-ONT", "DATAHANDLE");
        var obj = "是否全部文库都发起数据处理任务?";
        if (flag != 1) {
            obj = "是否只对选择的文库发起数据处理任务?";

        }

        confirmMsg("确认", obj, "warn", function () {
            object.push({
                "ID": mid,
                "TASK_SJ_TYPE": "数据处理",
                "TASK_NO": taskno,
                "LANE_NO": arrIds[0]["LIBRARY_CODE"],
                "LIBRARY_NUM": LIBRARY_NUM,
                "TASK_SJ_DATE": time,
                "TASK_INITIATOR": name,
                "TASK_SJ_START_DATE": time,
                "PUSH_STATUS": "待推送",
                "EXECUTION_STATUS": "未开始",
                "DATA_SJ_SOURCES": "ONT",
                "JIRAUPDATEFLAG": objst["object"][0]["jiraUpdateFlag"],
                "SERVERFROM": objst["object"][0]["serverFrom"],
                "WHETHERDELIVER": objst["object"][0]["whetherDeliver"],
                "FAST5HANDLE": objst["object"][0]["fast5Handle"],
                "SPLITBARCODE": objst["object"][0]["splitBarcode"]
            });
            for (var i = 0; i < rows1.length; i++) {
                objects.push({
                    "BDPT_ID": mid,
                    "PROJECT_NAME": rows1[i]["PROJECT_NAME"],
                    "PROJECT_NO": rows1[i]["PROJECT_NO"],
                    "PROJECT_SUBNO": rows1[i]["PROJECT_SUBNO"],
                    "DATA_SUM": rows1[i]["LIBRARY_DATA"],
                    "LIBRARY_TYPE": rows1[i]["LIBRARY_TYPE_EN"],
                    "LIBRARY_CODE_Z": rows1[i]["LIBRARY_CODE"],
                    "PROJECT_MANAGER": arrIds[0]["SYS_MAN"],
                    "PROJECT_MAN": arrIds[0]["SYS_MAN"],
                    "SAMPLE_NAME": rows1[i]["SAMPLE_NAME"],
                    "INDEX_NAME": rows1[i]["INDEX_NAME"],
                    "DATA_PATH": null,
                    "FAST5": null,
                    "ONT": null,
                    "LIBRARY_JHDATA": rows1[i]["LIBRARY_JHDATA"],
                    "BIO_CODE": rows1[i]["BIO_CODE"],
                    "TASK_LSMX_REMARKS": rows1[i]["JK_TASKMX_REMARK"],
                    "RUN_NO": null,
                    "EXECUTION_STATUS": "未开始",
                    "SPECIES": rows1[i]["SPECIES"],
                    "SPECIESTYPE": "",
                    "DATAPROCESSTYPE": rows1[i]["DATA_PROCESSING_TYPE"],
                    "LANE_NO": rows1[i]["LIBRARY_CODE"],
                    "POOL_ID": rows1[i]["POOL_ID"],
                    "TASK_LIB_ID": rows1[i]["TASKLIBID"],
                    "TASK_LIB_MXID": rows1[i]["TASKMXID"],
                    "LIB_INFO_ID": rows1[i]["ID"]
                });
                if (rows1[i]["YN"] == "Y") {
                    objectjhyf.push({
                        "ID": rows1[i]["ID"],
                        "CHECK_TASK_NO": taskno
                    });
                } else {
                    objectjh.push({
                        "ID": rows1[i]["ID"],
                        "HANDLE_TASK_NO": taskno
                    });
                }
            }

            pushst(object, objects, "submit", "数据处理");

            var urlsends = "system/jdbc/save/one/table/objects";
            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK", "objects": object };
            putAddOrUpdata(urlsends, paramsup, "是", "数据处理");
            var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK_MX", "objects": objects };
            putAddOrUpdata(urlsend, paramsup, "否", "数据处理");
            if (objectjh.length > 0) {
                var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectjh };
                putAddOrUpdata(urlsend, paramsup, "是", "文件检查");
            }
            if (objectjhyf.length > 0) {
                var paramsup = { "tableName": "BIO_RD_ONT_INFO", "objects": objectjhyf };
                putAddOrUpdata(urlsend, paramsup, "是", "文件检查");
            }
        })
        flag2 = 0;


    }

    //数据处理
    var doGodataInfo = function (objst) {
        if (flag2 == 1) {
            alertMsg("请勿重复点击!");
            return;
        }
        flag2 = 1;
        var rows2;
        var arrIds = getGridSelectData(gridNameD2Grid);
        var arrIdsMx = [];
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条lane记录进行操作!");
            flag2 = 0;
            return;
        }
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrDatas = getGridSelectData(gridNameS2[i]);
            arrIdsMx = arrIdsMx.concat(arrDatas);

        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_BIO_DATA_PROCESSING_TASK_ont_view", "objects": [["ONT"], ["未开始", "结束"], ["数据处理"]], "search": { "LANE_NO": [arrIds[0]["LIBRARY_CODE"]] } },
            succeed: function (rs) {
                //console.log(rs);				
                rows2 = rs["rows"];
            }
        });
        if (rows2.length > 0) {
            var gnl = confirm("此数据已推送数据处理，确认重复推送吗？");
            if (gnl == false) {
                flag2 = 0;
                return;
            }

        }
        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];
        var object = [];
        var objects = [];
        var objectjh = [];
        var objectjhyf = [];
        var rows1;
        var flag = 0;
        var readsql = "lib_sample_pool_all_list-hk";
        if (arrIds[0]["ISPOOL"] == "否") {
            readsql = "queryTaskLibExMxResult-ONT-Seq";
        }
        debugger;
        if (arrIdsMx.length == 0) {
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": readsql, "objects": [[arrIds[0]["LIB_ID"]], [arrIds[0]["SEQ_ID"]]] },
                succeed: function (rs) {
                    //console.log(rs);				
                    rows1 = rs["rows"];
                }
            });
            flag = 1;

        } else {

            //            confirmMsg("确认", "是否全部文库都发起数据处理任务?", "warn", function () {
            rows1 = arrIdsMx;
            //            })

        }

        var LIBRARY_NUM = rows1.length;
        var mid = getRandomId();
        var taskno = getTaskNo("数据处理-ONT", "DATAHANDLE");
        var obj = "是否全部文库都发起数据处理任务?";
        if (flag != 1) {
            obj = "是否只对选择的文库发起数据处理任务?";

        }

        confirmMsg("确认", obj, "warn", function () {
            object.push({
                "ID": mid,
                "TASK_SJ_TYPE": "数据处理",
                "TASK_NO": taskno,
                "LANE_NO": arrIds[0]["LIBRARY_CODE"],
                "LIBRARY_NUM": LIBRARY_NUM,
                "TASK_SJ_DATE": time,
                "TASK_INITIATOR": name,
                "TASK_SJ_START_DATE": time,
                "PUSH_STATUS": "待推送",
                "EXECUTION_STATUS": "未开始",
                "DATA_SJ_SOURCES": "ONT",
                "JIRAUPDATEFLAG": objst["object"][0]["jiraUpdateFlag"],
                "SERVERFROM": objst["object"][0]["serverFrom"],
                "WHETHERDELIVER": objst["object"][0]["whetherDeliver"],
                "SEQUENCINGDATAPATH": objst["object"][0]["dataplan"],
                "FAST5HANDLE": objst["object"][0]["fast5Handle"],
                "SPLITBARCODE": objst["object"][0]["splitBarcode"]
            });
            var indexnames = [];
            var indextypes = [];
            for (var i = 0; i < rows1.length; i++) {
                indexnames.push(rows1[i]["INDEX_NAME"]);
                indextypes.push(rows1[i]["LIBRARY_TYPE"]);
            }
            var index;
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "QueryIndexList-PBMCD", "objects": [], "search": { "INDEX_NAME": indexnames, "INDEX_TYPE": indextypes } },
                succeed: function (rs) {
                    //console.log(rs);				
                    index = rs["rows"];
                }
            });

            for (var i = 0; i < rows1.length; i++) {
                var F_INDEX = "-";
                var R_INDEX = "-";
                for (var x = 0; x < index.length; x++) {
                    if (index[x]["INDEX_TYPE"] == rows1[x]["LIBRARY_TYPE"]) {
                        F_INDEX = index[x]["SEQ_I5_1"];
                        R_INDEX = index[x]["SEQ_I7_1"];
                    }

                }

                var mxid = getRandomId();
                objects.push({
                    "ID": mxid,
                    "BDPT_ID": mid,
                    "F_INDEX": F_INDEX,
                    "R_INDEX": R_INDEX,
                    "SPECIESTYPE": rows1[i]["TASK_LS_BIOTYPE"],
                    "SPECIESZH": rows1[i]["SPECIES_CH_NAME"],
                    "PROJECT_NAME": rows1[i]["PROJECT_NAME"],
                    "PROJECT_NO": rows1[i]["PROJECT_NO"],
                    "PROJECT_SUBNO": rows1[i]["PROJECT_SUBNO"],
                    "DATA_SUM": rows1[i]["LIBRARY_DATA"],
                    "LIBRARY_TYPE": rows1[i]["LIBRARY_TYPE_EN"],
                    "LIBRARY_CODE_Z": rows1[i]["LIBRARY_CODE"],
                    "PROJECT_MANAGER": arrIds[0]["SYS_MAN"],
                    "PROJECT_MAN": arrIds[0]["SYS_MAN"],
                    "SAMPLE_NAME": rows1[i]["SAMPLE_NAME"],
                    "INDEX_NAME": rows1[i]["INDEX_NAME"],
                    "DATA_PATH": "",
                    "FAST5": null,
                    "ONT": null,
                    "LIBRARY_JHDATA": rows1[i]["LIBRARY_JHDATA"],
                    "BIO_CODE": rows1[i]["BIO_CODE"],
                    "TASK_LSMX_REMARKS": rows1[i]["JK_TASKMX_REMARK"],
                    "RUN_NO": null,
                    "EXECUTION_STATUS": "未开始",
                    "SPECIES": rows1[i]["SPECIES"],
                    "SPECIESZH": rows1[i]["SPECIES_CH_NAME"],
                    "SPECIESTYPE": "",
                    "DATAPROCESSTYPE": rows1[i]["DATA_PROCESSING_TYPE"],
                    "LANE_NO": rows1[i]["LIBRARY_CODE"],
                    "POOL_ID": rows1[i]["POOL_ID"],
                    "TASK_LIB_ID": rows1[i]["TASKLIBID"],
                    "TASK_LIB_MXID": rows1[i]["TASKMXID"],
                    "LIB_INFO_ID": rows1[i]["ID"]
                });
                if (rows1[i]["YN"] == "Y") {
                    objectjhyf.push({
                        "ID": rows1[i]["ID"],
                        "CHECK_TASK_NO": taskno
                    });
                } else {
                    objectjh.push({
                        "ID": rows1[i]["ID"],
                        "HANDLE_TASK_NO": taskno
                    });
                }
            }

            var urlsends = "system/jdbc/save/one/table/objects";
            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK", "objects": object };
            putAddOrUpdata(urlsends, paramsup, "是", "数据处理");
            var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK_MX", "objects": objects };
            putAddOrUpdata(urlsends, paramsup, "否", "数据处理");
            if (objectjh.length > 0) {
                var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectjh };
                putAddOrUpdata(urlsend, paramsup, "", "数据处理");
            }
            if (objectjhyf.length > 0) {
                var paramsup = { "tableName": "BIO_RD_ONT_INFO", "objects": objectjhyf };
                putAddOrUpdata(urlsend, paramsup, "", "数据处理");
            }
            pushst(object, objects, "submit", "数据处理");
        })
        flag2 = 0;

    }

    var pushst = function (arrIds, obj, taskType, typest) {
        debugger;
        if (flag == 1) {
            alertMsg("正在推送中，请勿重复点击!");
            return;
        }
        flag = 1;
        var rows1 = [];
        var object;
        var objectmxid = [];
        var sampleList = [];
        var copyTaskRequest;
        // $.fn.ajaxPost({
        // 	ajaxUrl: "system/jdbc/query/one/table",
        // 	ajaxType: "post",
        // 	ajaxAsync: false,
        // 	ajaxData: { "query": "query_BIO_DATA_PRO_TMXont_view", "objects": [[arrIds[0]["ID"]]] },
        // 	succeed: function (rs) {
        // 		//console.log(rs);				
        // 		rows1 = rs["rows"];
        // 	}
        // });
        for (var i = 0; i < obj.length; i++) {
            sampleList.push({
                "speciesType": obj[i]["SPECIESTYPE"],
                "speciesZh": obj[i]["SPECIESZH"],
                "fIndex": obj[i]["F_INDEX"],
                "rIndex": obj[i]["R_INDEX"],
                "projectManager": obj[i]["PROJECT_MAN"],
                "nucleicAcidNumber": obj[i]["BIO_CODE"],
                "specialRequirementsDatabaseConstructionSequencing": obj[i]["TASK_LSMX_REMARKS"],
                "index": obj[i]["INDEX_NAME"],
                "projectIssue": obj[i]["PROJECT_SUBNO"],
                "dataPath": obj[i]["DATA_PATH"],
                "sampleName": obj[i]["SAMPLE_NAME"],
                "schedulingDataVolumeG": Number(obj[i]["LIBRARY_JHDATA"]),
                "subLibraryNumber": obj[i]["LIBRARY_CODE_Z"],
                "keepFast5": obj[i]["FAST5"],
                "customerServiceManager": Number(obj[i]["PROJECT_MANAGER"]),
                "runNumber": arrIds[0]["LANE_NO"],
                "contractDataVolume": obj[i]["DATA_SUM"],
                "libraryType": obj[i]["LIBRARY_TYPE"],
                "projectName": obj[i]["PROJECT_NAME"],
                "dataProcessType": obj[i]["DATAPROCESSTYPE"],
                "taskId": arrIds[0]["ID"],
                "taskNo": arrIds[0]["TASK_NO"],
                "taskMxId": obj[i]["ID"],

            });

            objectmxid.push(obj[i]["ID"]);
        }
        var sequencingDataPath = "";
        if (taskType == "dataCheck") {
            copyTaskRequest = null;

        } else {
            if (arrIds[0]["WHETHERDELIVER"] == 1) {
                copyTaskRequest = {
                    "splitBarcode": arrIds[0]["SPLITBARCODE"], //
                    "sequencingPlatform": "ONT", //
                    "serverFrom": arrIds[0]["SERVERFROM"],//数据来源
                    "runNumber": arrIds[0]["LANE_NO"],
                    "whetherDeliver": arrIds[0]["WHETHERDELIVER"],
                    "fast5Handle": arrIds[0]["FAST5HANDLE"]
                };
            } else {
                sequencingDataPath = arrIds[0]["SEQUENCINGDATAPATH"];
                copyTaskRequest = null;
            }
        }
        object = {
            "taskType": taskType,//任务类型
            "copyTaskRequest": copyTaskRequest, //拷贝
            "handlerType": "3",//类型
            "sequencingDataPath": sequencingDataPath,//只有二代的数据处理任务提交才传该参数
            "sampleList": sampleList,//样品信息
            "jiraUpdateFlag": arrIds[0]["JIRAUPDATEFLAG"], //是否更新jira，1：更新， 2： 不更新
            "taskId": arrIds[0]["ID"], //hk任务单ID
            "taskNo": arrIds[0]["TASK_NO"]//hk任务单编号
        };
        putToPust(object, arrIds[0]["ID"], objectmxid);
        flag = 0;

    }
    var putToPust = function (params, id, mid) {
        //var inobjjson = { "url": "http://"+JIRRA_URL+"/api/v1/dataProcess/huakai/createDataProcess", "bodyParams": params };//测试
        var inobjjson = { "url": "http://"+JIRRA_URL+"/data_process//api/v1/dataProcess/huakai/createDataProcess", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (result.apiData.flag == "true" || result.apiData.flag) {
                        var objects = [];
                        var objectmxid = [];
                        objects.push({
                            "ID": id,
                            "PUSH_STATUS": "已推送"
                        });
                        /*  for (var i = 0; i < mid.length; i++) {
                              objectmxid.push({
                                  "ID": mid[i],
                                  "LIB_ID": result.apiData.taskId,
                              });
  
                          }*/
                        var urlsend = "system/jdbc/save/batch/table";
                        var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK", "objects": objects };
                        putAddOrUpdata(urlsend, paramsup, "否", "数据处理");
                        var paramsup = { "tableName": "BIO_DATA_PROCESSING_TASK_MX", "objects": objectmxid };
                        putAddOrUpdata(urlsend, paramsup, "否", "数据处理");
                        alertMsg(result.apiData.message);
                    } else {
                        alertMsg("提示:推送失败(<font color=#ff0000>" + result.apiData.message + "</font>)!");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }


    //任务单状态修改
    var doTaskStatus = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/seq/pb/pd/uptaskstatus/uptaskstatus",
            title: "修改单状态.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    }
    //撤回
    var doReturn = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["ONT_STATUS"] != "上机待接收") {
                alertMsg("操作失败,所选记录存在已“已接收”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "ONT_STATUS": "草稿",
                });
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_ONT_INFO", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //移至待审核
    var doReturn2 = function () {
        var g = getGridSelectData(gridNameD3Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["ISOK"] == "暂停" || g[i]["ISOK"] == "未上机") {
                objectup.push({
                    "ID": g[i]["ID"],
                    "ISOK": "正常"
                });
            } else {
                alertMsg("操作失败,只有“<font color=#ff0000>暂停、未上机</font>”状态方允许操作!");
                return;
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_SEQ_MOD", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //删除
    var deleteinfo = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验操作!");
            return;
        }
        confirmMsg("确认", "确定要删除记录吗?", "warn", function () {
            var params = { "tableName": "BIO_ONT_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }
    //删除
    var deleteinfoyf = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验操作!");
            return;
        }
        confirmMsg("确认", "确定要删除记录吗?", "warn", function () {
            var params = { "tableName": "BIO_RD_ONT", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }
    //研发删除（一级）
    var deleteyfone = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验操作!");
            return;
        }
        confirmMsg("确认", "确定要删除记录吗?", "warn", function () {
            var params = { "tableName": "BIO_RD_ONT", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }
    //研发删除（二级）
    var deleteyftwo = function () {
        var arrIds = getSelectData(subGrid_N);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验操作!");
            return;
        }
        confirmMsg("确认", "确定要删除记录吗?", "warn", function () {
            var params = { "tableName": "BIO_RD_ONT_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }
    //批量执行
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    //研发导入
    var rdtask = function (componentId) {
        var grid = subGrid_N;
        var gridmRows = getGridSelectData(gridNameDGrid);
        if (gridmRows.length == 0) {
            alertMsg("至少选择一行主单数据");
            return;
        } else if (gridmRows.length > 1) {
            alertMsg("只能选择一行主单数据");
            return;
        }
        debugger;
        var tableName = "BIO_SEQ_MOD";
        if (gridmRows[0]["YN"] == "Y") {
            tableName = "BIO_RD_ONT";
        }
        var p = {
            "tableName": "BIO_RD_ONT_INFO",
            "topMap": {//主表信息
                "tableName": tableName,
                "ID": gridmRows[0].ID,
                //"LIBRARY_CODE":gridmRows[0].LIBRARY_CODE,
            },
            "mapping": {
                "POOL_ID": "${ID}",
                //"LIBRARY_CODE":"${LIBRARY_CODE}",
            }
        };
        debugger;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                "template": function () {
                    saveGridDataToExcel({ grid: subGrid_N, select: 1, expKey: "C" });
                },
                "import": function (info, pv) {
                    $.fn.ajaxPost({
                        ajaxType: "post",
                        ajaxUrl: "system/config/meta/importData2",
                        ajaxData: $.extend(p, { "info": info }, { "template": grid.getOptions().columns, expKey: "C" }),
                        succeed: function (result) {
                            alertMsg("导入成功!", "success", function () {
                                debugger;
                                funcExce(pv + "close");//关闭页面
                                refreshGrid();
                            });
                        },
                        failed: function (result) {
                            if (result["msg"]) {
                                alertMsg(result["msg"], "error");
                            } else {
                                alertMsg("导入失败!", "error");
                            }
                        }
                    });
                }
            }
        });
    }

    //研发导入（主）
    var rdtaskz = function (componentId) {
        var grid = gridNameDGrid;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                "template": function (p, n) {
                    if (n == "import") {
                        return {
                            template: grid.getOptions().columns,
                            expKey: "B",
                            tableName: "BIO_RD_ONT"
                        };
                    } else {
                        saveGridDataToExcel({ grid: gridNameDGrid, select: 1, expKey: "B" });
                    }
                },
                "succeed": function () {
                    refreshGrid();
                    return false;
                }
            },
        });
    }


    //生成uuid
    function getRandomId() {
        return new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };

    //生成任务单号
    function getTaskNo(numstr, nost) {
        //        var numstr = "数据处理-ONT";
        var no;

        $.fn.ajaxPost({
            ajaxUrl: "system/settings/code/auto",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "limit": "日", "type": numstr, "format": "Šτ${NO_CHAR}|-ONT-|#yyMMdd#|-①", "length": "3", "NO_CHAR": nost },
            succeed: function (rs) {
                no = rs.info.numberCode;       //执行单号
            }
        });
        return no;

    }


    //表格导入
    var importData1 = function (componentId) {
        var arrIds2 = getGridSelectData(gridNameDGrid);
        var arrIds = [];
        var url = "lib_sample_pool_all_list";
        var tableNa = "BIO_LIB_INFO";
        for (var i = 0; i < gridNameS3.length; i++) {
            var arrSubID = getSelectData(gridNameS3[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        if (arrIds2.length != 1) {
            alertMsg("请选择一条主单记录进行操作!");
            return;
        }
        if (arrIds2[0]["ISPOOL"] == "否") {
            url = "queryTaskLibExMxResult-ONT-Seq";
        }
        if (arrIds2[0]["YN"] == "Y") {
            tableNa = "BIO_RD_ONT_INFO";

        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "D",
                        tableName: tableNa,
                        requestData: {
                            ajaxData: { "query": url, "size": 5000, "objects": [[arrIds2[0]["LIB_ID"]], [arrIds2[0]["SEQ_ID"]]] },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }


    //表格导入
    var importData2 = function (componentId) {
        var arrIds2 = getGridSelectData(gridNameD2Grid);
        var arrIds = [];
        var url = "lib_sample_pool_all_list-hk";
        var tableNa = "BIO_LIB_INFO";
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        if (arrIds2.length != 1) {
            alertMsg("请选择一条主单记录进行操作!");
            return;
        }
        if (arrIds2[0]["ISPOOL"] == "否") {
            url = "queryTaskLibExMxResult-ONT-Seq";
        }
        if (arrIds2[0]["YN"] == "Y") {
            tableNa = "BIO_RD_ONT_INFO";

        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: tableNa,
                        requestData: {
                            ajaxData: { "query": url, "size": 5000, "objects": [[arrIds2[0]["LIB_ID"]], [arrIds2[0]["SEQ_ID"]]] },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }



    var editA = function () {
        debugger;
        var arrobjs = getGridSelectData(gridNameD2Grid);
        if (arrobjs.length != 1) {
            alertMsg("请只选择一条数据进行操作!");
            return;
        }

        var RUN_ID = arrobjs[0]["ID"];
        var RUN_NO = arrobjs[0]["ONT_LIBID"];
        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_JIRA_KEYWORD_list_ONT", "objects": [[RUN_ID]] },
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        if (rows.length == 0) {
            alertMsg("每条RUN只能更新一次!");
            return;
        }
        var updatejira = [];
        for (var j = 0; j < rows.length; j++) {
            var LSM_KEY_P = rows[j]["LSM_KEY_P"];    //jira 项目关键字为空
            var ADD_NUMBER = rows[j]["ADD_NUMBER"];   // 测序轮数
            if (LSM_KEY_P == null) {
                alertMsg("任务单【" + rows[j]["TASK_NO"] + "】jira 项目关键字为空，请联系运营补齐");
                return;
            }
            var url = "http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
            var parmars = { "jiraKey": LSM_KEY_P, "fields": ["customfield_10111", "status"] };
            var inobjjson = { "url": url, "bodyParams": parmars };
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: "system/api/post/bodyParams",
                ajaxData: inobjjson,
                ajaxAsync: false,
                succeed: function (result) {
                    var JIRARUN = "";
                    if (result["code"] > 0) {
                        var customfield_10111 = result.apiData[0].fields.customfield_10111;
                        if (customfield_10111 == null) {
                            //if (ADD_NUMBER == 0) {
                            JIRARUN = "首次(" + RUN_NO + ")"
                            // }
                        } else {
                            // if (ADD_NUMBER == 0) {
                            //     var str = "首次"
                            // }
                            // if (ADD_NUMBER == 1) {
                            //     var str = "一次加测"
                            // }
                            // if (ADD_NUMBER == 2) {
                            //     var str = "二次加测"
                            // }
                            // if (ADD_NUMBER == 3) {
                            //     var str = "三次加测"
                            // }
                            // if (ADD_NUMBER == 4) {
                            //     var str = "四次加测"
                            // }
                            // if (ADD_NUMBER == 5) {
                            //     var str = "五次加测"
                            // }
                            var str = ADD_NUMBER;
                            var a = customfield_10111.indexOf(str);
                            if (a > -1) {
                                var b = customfield_10111.indexOf(")", a);
                                var JIRARUN = customfield_10111.slice(0, b) + "+" + RUN_NO + customfield_10111.slice(b);
                            } else {
                                JIRARUN = customfield_10111 + "+" + str + "(" + RUN_NO + ")"
                            }
                        }
                        updatejira.push({ key: LSM_KEY_P, value: JIRARUN });
                    } else {
                        alertMsg("jira 项目关键字" + rows[j]["LSM_KEY_P"] + "加载获取jira信息失败!");
                        return;
                    }
                }
            });
        }

        var a = 0;
        var m = mask(pathValue, "正在推送到jira,请稍等...");
        for (var j = 0; j < updatejira.length; j++) {
            var params = {
                "jiraKey": updatejira[0]["key"],
                "updateField": {
                    "customfield_10111": updatejira[0]["value"]
                }
            };
            var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };

            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: "system/api/post/bodyParams",
                ajaxData: inobjjson,
                ajaxAsync: false,
                succeed: function (result) {
                    //  unmask(m);
                    if (result["code"] > 0) {
                        //alertMsg("提示:LSM推送成功!");
                        a = 0;

                    } else {
                        // alertMsg(errMsg + "操作失败!");
                        a = 1;
                    }
                },
                failed: function (res) {
                    //   unmask(m);
                    // alertMsg("提示:提交保存失败", "error");
                    a = 2;
                }
            });
            if (a == 1 || a == 2) {
                break;
            }
        }
        unmask(m);
        if (a == 1 || a == 2) {
            alertMsg(errMsg + "操作失败!");
        } else {

            var objectuse = [];
            objectuse.push({ "ID": RUN_ID, "IS_JIRA_RUN": "是" });

            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd = { "tableName": "BIO_ONT_INFO", "objects": objectuse };
            putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

            alertMsg("提示:LSM推送成功!");
        }

    }

    
    //表格导入
    var importData3 = function (componentId) { 
        var arrIds =getSelectData(gridNameD1Grid); 
       
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条执行单记录进行操作!");
            return;
        }
 

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_ONTJH_MX",
                        requestData: {
                            ajaxData: { "query": "lib_sample_pool_all_list-hk2", "size": 5000, "objects": [arrIds] },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }
    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "addlane": addlane,
        "doTaskStatus": doTaskStatus,
        "doGo": doGo,
        "deleteinfoyf": deleteinfoyf,
        "deleteyfone": deleteyfone,
        "deleteyftwo": deleteyftwo,
        "deleteinfo": deleteinfo,
        "doReturn": doReturn,
        "doReturn2": doReturn2,
        "refreshGrid": refreshGrid,
        "rdtask": rdtask,
        "rdtaskz": rdtaskz,
        "importData1": importData1,
        "importData3":importData3,
        "callBack": callBack,
        //        "fileInspect": fileInspect,
        "doGodataInfoAge": doGodataInfoAge,
        "editA": editA,
        "pushs": pushs,
        "pushsdate": pushsdate,
        "pushsdateAge": pushsdateAge,
        "importData2": importData2,
        "doGodataInfo": doGodataInfo
    });
});