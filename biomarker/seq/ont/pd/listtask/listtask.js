$(document).ready(function() {
   var pathValue="biomarker-seq-ont-pd-listtask-listtask";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }
   var paramsValue;
   var gridNameGrid;
   var gridName1Grid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
	   paramsValue=params;
	   
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"add",title:"确认选择"},
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"seq_task_sele_mod","objects":[["否"],["否"]]},
            headerFilter:function(cols,i){
                if(i){
                    
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        
        init1();
   }

   var init1=function(params){
	   	   
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"add2",title:"确认选择"}

        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"seq_task_sele_mod","objects":[["是"],["否"]]},
            headerFilter:function(cols,i){
                if(i){
                    
                }
            }
        };
        gridName1Grid = initKendoGrid("#gridName1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
   }

   var add=function(){
	   
	   add_(1);
   }
   var add2=function(){
	   
	   add_(2);
   }
   
    var add_=function(flag){
      
    	var grid_;
    	
    	if(flag==1){
    		grid_=gridNameGrid;
    	}else{
    		grid_=gridName1Grid;
    	}
    	var g=getGridSelectData(grid_);   
        if(g.length==0){
            alertMsg("请至少选择一条数据进行操作!");
            return ;
        }
        
        var username=getLimsUser()["name"];
        var time=sysNowTimeFuncParams["sysNowTime"];
        var object=[];
        var objectup=[];
        for(var i=0;i < g.length;i++){
 
        	object.push({
			"LIBRARY_CODE":g[i]["LIBRARY_CODE"],//文库编号
			"LIB_ID":g[i]["LIB_ID"],//关联文库ID
        		"LIB_QC_ID":g[i]["LIB_QC_ID"],//关联文库质检ID
        		"LIB_TASK_MX_ID":g[i]["LIB_TASK_MX_ID"],//关联建库任务单明细ID
	        	"SYS_MAN":username,//操作人
	    		"SYS_INSERTTIME":time//操作时间
        	});
        	
        	objectup.push({
        		"ID":g[i]["ID"],
        		"ISOK":"是"
        	});
       
        }
          
       var params={"tableName":"BIO_ONT_INFO","objects":object};
       var paramsup={"tableName":"BIO_SEQ_MOD","objects":objectup};

       //插入任务明细记录
       var url="system/jdbc/save/batch/table";
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:url,
           ajaxData:params,
           succeed:function(result){
               if(result["code"]>0){
                               	
               }else{
               	console.log(result);
               }
           }
       });
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:url,
           ajaxData:paramsup,
           succeed:function(result){
               if(result["code"]>0){//成功保存后执行流程提交
               	refreshGrid();
               	funcExce(pathValue+"pageCallBack","1");//父执行回调
               	console.log(result);
                alertMsg("提交成功!");
               }else{
               	console.log(result);
               }
           }
       });
    	
    }

   
  
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
        if(gridName1Grid){
            gridName1Grid.dataSource.read();//重新读取--刷新
        }
     }

    
  
     
     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "add":add,//确认选择
         "add2":add2,//确认选择
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});