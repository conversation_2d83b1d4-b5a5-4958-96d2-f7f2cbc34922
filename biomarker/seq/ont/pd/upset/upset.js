$(document).ready(function () {
    var pathValue = "biomarker-seq-ont-pd-upset-upset";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "BIO_ONT_INFO"
        };
    }
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
    }
    var subUpData = function () {
        debugger;
        // var ids = paramsValue["ids"];
        // var libids = paramsValue["libids"];
        // var libcodes = paramsValue["libcodes"];
        // var ispools = paramsValue["ispools"];
        // var jsonData = getJsonByForm("form", pathValue);//获取表单json
        // var object = [];
        // for (var i = 0; i < libids.length; i++) {
        // 	object.push($.extend({}, jsonData, {
        // 		"SEQ_ID": ids[i],
        // 		"LIB_ID": libids[i],
        // 		"LIBRARY_CODE": libcodes[i],
        // 		"ISPOOL": ispools[i]
        // 	}));//表单值继承
        // }
        // var params = { "tableName": "BIO_ONT_INFO", "objects": object };
        // var url = "system/jdbc/save/batch/table";
        // $.fn.ajaxPost({
        // 	ajaxType: "post",
        // 	ajaxUrl: url,
        // 	ajaxData: params,
        // 	succeed: function (result) {
        // 		if (result["code"] > 0) {
        // 			funcExce(pathValue + "pageCallBack");
        // 			alertMsg("提交成功!");
        // 			funcExce(pathValue + "close");
        // 		} else {
        // 			console.log(result);
        // 		}
        // 	}
        // });
 
        var LIB_IDS = paramsValue["LIB_IDS"];

        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var exID = result["ID"];//关联执行单

                    var sample;
                    $.fn.ajaxPost({
                        ajaxUrl: "system/jdbc/query/one/table",
                        ajaxType: "post",
                        ajaxAsync: false,
                        ajaxData: { "query": "lib_sample_pool_ONT_all_list", "size": 2000, "objects": [LIB_IDS, LIB_IDS, LIB_IDS] },
                        succeed: function (rs) {
                            sample = rs.rows;             //样品
                        }
                    });

                    var object = [];
                    var LIBRARY_JHDATA_SUM=0;
                    for (var i = 0; i < sample.length; i++) {
                        LIBRARY_JHDATA_SUM=LIBRARY_JHDATA_SUM+sample[i]["LIBRARY_JHDATA"];
                        object.push({
                            "ONTJH_ID": exID,
                            "LIB_ID": sample[i]["ID"],
                            "LIBRARY_CODE": sample[i]["LIBRARY_CODEZ"],
                            "LIBRARY_JHDATA": sample[i]["LIBRARY_JHDATA"],//安排数据量（G）
                            "SEQ_ROUNTDS": sample[i]["SEQ_ROUNTDS"],//数据处理类型
                            "DATA_PROCESSING_TYPE": sample[i]["DATA_PROCESSING_TYPE"],//测序轮数
                            "INDEX_NAME": sample[i]["INDEX_NAME"],// Index名称
                        });//表单值继承
                    } 
                    var objONTZ=[{
                        "ID":exID,
                        "SAMPLE_SUM":sample.length,
                        "LIBRARY_JHDATA_SUM":LIBRARY_JHDATA_SUM,
                    }]
                    if(paramsValue["arrIds"][0]["ONT_SM_ND"] !=null) objONTZ[0]["ONT_SM_ND"]=paramsValue["arrIds"][0]["ONT_SM_ND"];
                    if(paramsValue["arrIds"][0]["ONT_SM_TJ"] !=null) objONTZ[0]["ONT_SM_TJ"]=paramsValue["arrIds"][0]["ONT_SM_TJ"];
                    if(paramsValue["arrIds"][0]["ONT_SM_ZL"] !=null) objONTZ[0]["ONT_SM_ZL"]=paramsValue["arrIds"][0]["ONT_SM_ZL"];
                    if(paramsValue["arrIds"][0]["ONT_SM_DATA"] !=null) objONTZ[0]["ONT_SM_DATA"]=paramsValue["arrIds"][0]["ONT_SM_DATA"];
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsadd = { "tableName": "BIO_ONT_INFO", "objects": objONTZ };
                    putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

                    var paramsadd = { "tableName": "BIO_ONTJH_MX", "objects": object };
                    putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");
                }
            }
        })
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        funcExce(pathValue + "pageCallBack");
                        alertMsg("提交成功!");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue, {
        "init": init,
        "subUpData": subUpData
    });
});