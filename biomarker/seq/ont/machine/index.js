$(document).ready(function () {
    var pathValue = "biomarker-seq-ont-machine-index";
    var paramsValue;
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    var gridNameD1Grid;
    var init = function (params) {
        paramsValue = params;
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit", title: "填写上机信息.." },
            { name: "excel", target: "importData1", title: "实验导入/模板" },
            { name: "excel", target: "importDatayf", title: "研发导入/模板" },
            { name: "edit", target: "doSop", title: "设置SOP" },
            { name: "edit", target: "doGo", title: "提交" },
            { name: "return", target: "doReturn", title: "退回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_ont_sj_info", "objects": [["上机已接收", "下机确认退回"], ["上机已接收", "下机确认退回"]] },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "lib_sample_pool_all_list-hk2";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);//初始化表格的方法
        init1();
    }
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "seq_ont_sj_info", "objects": [[
                    "下机待确认", "下机已确认", "数据下机待处理", "数据下机已处理",
                    "数据审核退回", "数据下机已审核"], [
                    "下机待确认", "下机已确认", "数据下机待处理", "数据下机已处理",
                    "数据审核退回", "数据下机已审核"]]
            },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "lib_sample_pool_all_list-hk2";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    //表格导入
    var importData1 = function (componentId) {
        debugger;
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var g = getGridSelectData(gridNameDGrid);
        for (var i = 0; i < g.length; i++) {
            if (g[i]["YN"] == "Y") {
                alertMsg("研发数据无法通过实验导入进行操作!");
                return;
            }
        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_ONT_INFO",
                        requestData: {
                            ajaxData: { "query": "seq_ont_sj_info", "size": 5000, "objects": [["上机已接收", "上机退回"], ["上机已接收", "上机退回"]], "search": { "ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }
    var callBack = function () {
        refreshGrid();
    };

    //研发表格导入
    var importDatayf = function (componentId) {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        var g = getGridSelectData(gridNameDGrid);
        for (var i = 0; i < g.length; i++) {
            if (g[i]["YN"] == "N") {
                alertMsg("实验数据无法通过研发导入进行操作!");
                return;
            }

        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_RD_ONT",
                        requestData: {
                            ajaxData: { "query": "seq_ont_sj_info", "size": 5000, "objects": [["上机已接收", "上机退回"], ["上机已接收", "上机退回"]], "search": { "ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }
    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
    }

    var edit = function () {
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        }
        debugger;
        var url = "biomarker/seq/ont/machine/upset/upset";
        if (arrIds[0]["YN"] == "Y") {
            url = "biomarker/seq/ont/machine/upsetrd/upsetrd";
        }
        var ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
        }
        var winOpts = {
            url: url,
            title: "ONT上机信息.."
        };
        openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
    }
    //提交
    var doGo = function () {
        var g = getGridSelectData(gridNameDGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var tableName = "BIO_ONT_INFO";
        if (g[0]["YN"] == "Y") {
            tableName = "BIO_RD_ONT";
        }
        var objectup = [];
        var ids = [];
        var objectsop = [];
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["SOP_NAME"] == null) {
                alertMsg("请先设置sop在提交");
                return;
            }
            //更新记录
            objectup.push({
                "ID": g[i]["ID"],//联联任务ID
                "ONT_STATUS": "下机待确认"
            });
            ids.push(g[i]["ID"]);
        }
        debugger;
        var rows1 = [];
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_BIO_BZ_MATERIEL_SOP_cx_form", "objects": [ids] },
            succeed: function (rs) {
                //console.log(rs);				
                rows1 = rs["rows"];
            }
        });
        for (var i = 0; i < rows1.length; i++) {
            objectsop.push({
                "ID": rows1[i]["ID"],
                "SOP_REVIEW_FLAG": "待审核"
            });
        }
        var paramsup = { "tableName": tableName, "objects": objectup };
        var url = "system/jdbc/save/batch/table";
        putAddOrUpdata(url, paramsup, "是", "提交");
        //更新SOP状态
        var paramsusop = { "tableName": "BIO_BZ_MATERIEL_SOP_JL", "objects": objectsop };
        putAddOrUpdata(url, paramsusop, "否", "更新SOP状态");
    }
    //退回
    var doReturn = function () {
        var g = getGridSelectData(gridNameDGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            objectup.push({
                "ID": g[i]["ID"],
                "ONT_STATUS": "上机退回"
            });
        }
        var paramsup = { "tableName": "BIO_ONT_INFO", "objects": objectup };
        var url = "system/jdbc/save/batch/table";
        putAddOrUpdata(url, paramsup, "是", "提交");
    }
    //撤回
    var doReturn2 = function () {
        var g = getGridSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["ONT_STATUS"] != "下机待确认退回") {
                alertMsg("操作失败,所选记录存在已“上机信息已确认”状态!");
                //return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "ONT_STATUS": "上机已接收",
                });
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_ONT_INFO", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //批量执行
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    var doSop = function () {
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条主单记录进行操作!");
            return;
        }
        if (arrIds.length > 1) {
            alertMsg("请只选择一条主单记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/seq/ont/machine/doSop/doSop",
            title: "设置SOP..",
        };
        openWindow(winOpts, { "EXE_TQQC_ID": arrIds[0]["ID"], "EX_DH_NO": arrIds[0]["EX_DH_NO"], "YN": arrIds[0]["YN"] });
    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "edit": edit,
        "doGo": doGo,
        "doReturn": doReturn,
        "importDatayf": importDatayf,
        "doReturn2": doReturn2,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "importData1": importData1,
        "doSop": doSop,
    });
});