$(document).ready(function () {
    var pathValue = "biomarker-seq-ont-machinejcsh-index";
    var paramsValue;
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameS = [];
    var gridNameS1 = [];

    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doUpset", title: "修改本次加测量.." },
            { name: "submit", target: "doGoLib", title: "提交重建库" },
            { name: "submit", target: "doGo", title: "提交已处理" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_TASK_LIB_list_ONT", "objects": [] },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql2 = "seq_ont_pd_return_info_jc";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 270,
                    read: { "query": readsql2, "objects": [[ROW_ID], ['否']] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        init1();
    }
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_TASK_LIB_list_ONT_", "objects": [] },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql2 = "seq_ont_pd_return_info_jc";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 270,
                    read: { "query": readsql2, "objects": [[ROW_ID], ['是', '接收']] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
    }
    //提交
    var doGo = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {
            var object = [];
            for (var i = 0; i < arrIds.length; i++) {
                if (arrIds[i]["ARRANGE_DATA"] > 0) {
                    object.push({ "ID": arrIds[i]["ADDDATAID"], "ADD_STATUS": "待处理" });
                } else {
                    object.push({ "ID": arrIds[i]["ADDDATAID"], "ADD_STATUS": "接收" });
                }

            }
            var params = { "tableName": "BIO_ADD_DATA", "objects": object };
            var url = "system/jdbc/update/batch/table";
            putAddOrUpdata(url, params, "是", "");
        })
    }
    //提交
    var doReturn = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {
            var object = [];
            for (var i = 0; i < arrIds.length; i++) {
                object.push({ "ID": arrIds[i]["ADDDATAID"], "ADD_STATUS": "否" });
            }
            var params = { "tableName": "BIO_ADD_DATA", "objects": object };
            var url = "system/jdbc/update/batch/table";
            putAddOrUpdata(url, params, "是", "");
        })
    }
    //实验填写
    var doUpset = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var ADDDATAID = [];
        var winOpts = {
            url: "biomarker/seq/ont/machinejcsh/upset/upset",
            title: "加填写.."
        };
        for (var i = 0; i < arrIds.length; i++) {
            ADDDATAID.push(arrIds[i]["ADDDATAID"]);
        }
        openWindow(winOpts, { "IDS": ADDDATAID, "ID": ADDDATAID[0] });
    }
    var doGoLib = function () {
        var arrIds = [];
        var rows1 = [];
        var rows2 = [];
        var btlmId = [];
        var mid = getRandomId();
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_PBJC_view", "objects": [[arrIds[0]["TASKLIBID"]]] },
            succeed: function (rs) {
                //console.log(rs);				
                rows1 = rs["rows"][0];
            }
        });
        rows1["ID"] = mid;
        rows1["TASK_LS_STATUS"] = "加测";
        rows1["JC_STATUS"] = "加测!";
        var paramsUpTask = [];
        var paramsUpTaskMx = [];
        var flag1 = 0;
        debugger;
        paramsUpTask.push(rows1);
        for (var i = 0; i < arrIds.length; i++) {
            if (arrIds[i]["TASKLIBID"] == null) {
                alertMsg("提示:操作失败,参数缺失请联系管理员!");
                return;
            } else {
                if (arrIds[i]["TASKLIBID"]) {
                    flag1 = 1;
                    var mxid = getRandomId();
                    $.fn.ajaxPost({
                        ajaxUrl: "system/jdbc/query/one/table",
                        ajaxType: "post",
                        ajaxAsync: false,
                        ajaxData: { "query": "query_PBJC_MX_view", "objects": [[arrIds[0]["TASKMXID"]]] },
                        succeed: function (rs) {
                            //console.log(rs);				
                            rows2 = rs["rows"][0];
                        }
                    });
                    rows2["ID"] = mxid;
                    rows2["TASK_LS_ID"] = mid;
                    rows2["ADD_DATA"] = arrIds[i]["ADD_DATA"];
                    rows2["TASK_LSMX_STATUS"] = "加测";
                    paramsUpTaskMx.push(rows2);
                    // paramsUpTask.push({//BIO_TASK_LIB
                    //     "ID": arrIds[i]["TASKLIBID"],
                    //     "JC_STATUS": "加测!"
                    // });
                    // paramsUpTaskMx.push({
                    //     "ID": arrIds[i]["TASKMXID"],
                    //     "TASK_LSMX_STATUS": "加测"
                    // });
                }
            }
        }
        var urlsend = "system/jdbc/save/one/table/objects";
        if (flag1 == 1) {
            var paramsup1 = { "tableName": "BIO_TASK_LIB", "objects": paramsUpTask };
            putAddOrUpdata(urlsend, paramsup1, "否", "提交");
            var paramsup2 = { "tableName": "BIO_TASK_LIBMX", "objects": paramsUpTaskMx };
            putAddOrUpdata(urlsend, paramsup2, "是", "提交");
        }

    }
    //批量执行
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS = [];
        gridNameS1 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
    }
    //生成uuid
    function getRandomId() {
        return "PBJC-" + new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "doGo": doGo,
        "doUpset": doUpset,
        "doReturn": doReturn,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "doGoLib": doGoLib,
    });
});