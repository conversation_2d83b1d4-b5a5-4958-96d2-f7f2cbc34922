$(document).ready(function () {

    var pathValue = "biomarker-seq-mcdpooling2-re-elane-elane";
    var initData = function () {
        return {};
    }
    var gridNameGrid;
    var paramsValue;
    var gridNameS1 = [];
    var init = function (params) {
        paramsValue = params;
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doPool", title: "修改Pool信息" },
            { name: "edit", target: "doLaneMx", title: "上机安排量调整.." },
            { name: "ok", target: "doPoolMxMove", title: "移除Pool明细" },
            { name: "ok", target: "doControlJs", title: "control数据量计算" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "queryDoMCD2LanePoolMain",
                "objects": [[paramsValue["LANE_ID"]], ["草稿", "退回", "待接收", "已接收", "已排单", "已完成"]]
            },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "seq_NGS_lane_Pool_Lib_mx", "objects": [], "search": { "POOL_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }

        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    var doPool = function () {
        var g = getGridSelectData(gridNameGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }

        var ids = [];
        var laneid = "";

        for (var i = 0; i < g.length; i++) {
            ids.push(g[i]["ID"]);
        }
        var winOpts = {
            url: "biomarker/seq/mcdpooling2/re/dopool/dopool",
            title: "Pool..",
            currUrl: replacePathValue(pathValue)
        };
        openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
    }
    //上机按排量调整
    var doLaneMx = function () {
        var g = getGridSelectData(gridNameGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var ids = [];
        var laneid = "";

        for (var i = 0; i < g.length; i++) {
            ids.push(g[i]["LANE_MX_ID"]);
        }
        var winOpts = {
            url: "biomarker/seq/mcdpooling2/re/doLanemx/doLanemx",
            title: "Pool..",
            currUrl: replacePathValue(pathValue)
        };
        openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
    }

    //control数据量计算
    var doControlJs = function () {

        debugger;
        var g = getGridSelectData(gridNameGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        for (var i = 0; i < g.length; i++) {
            if (g[i]["APXS"] == null || g[i]["APXS"] == "" || g[i]["APXS"] == 0) {
                alertMsg(g[i]["POOL_CODE"] + "混样比例系数不能为空!");
                return;
            }
            if (g[i]["LANE_MX_DATA_SUM"] == null || g[i]["LANE_MX_DATA_SUM"] == "" || g[i]["APXS"] == 0) {
                alertMsg(g[i]["POOL_CODE"] + "上机安排数据量(M)不能为空!");
                return;
            }
            if (g[i]["TASKZJ_QPCR_PM"] == null || g[i]["TASKZJ_QPCR_PM"] == "" || g[i]["APXS"] == 0) {
                alertMsg(g[i]["POOL_CODE"] + "QPCR浓度（pM）不能为空!");
                return;
            }
            if (g[i]["TASKZJ_L2100_SIZE"] == null || g[i]["TASKZJ_L2100_SIZE"] == "" || g[i]["APXS"] == 0) {
                alertMsg(g[i]["POOL_CODE"] + "片段大小不能为空!");
                return;
            }
        }
        var object = [];
        for (var j = 0; j < g.length; j++) {
            var ap = g[j]["APXS"];
            var lmd = g[j]["LANE_MX_DATA_SUM"];
            var tqp = g[j]["TASKZJ_QPCR_PM"];
            var tls = g[j]["TASKZJ_L2100_SIZE"];
            var LANE_MX_SAMPLE_VOLBL = ap * lmd;
            var TASKZJ_ZJ_PCRNM = tqp * 452 / tls * 10;
            var LANE_MX_SAMPLE_VOL = parseInt((LANE_MX_SAMPLE_VOLBL / TASKZJ_ZJ_PCRNM)*100)/100;
            object.push({
                "ID": g[j]["LANE_MX_ID"],
                "LANE_MX_SAMPLE_VOLBL": LANE_MX_SAMPLE_VOLBL,
                "TASKZJ_ZJ_PCRNM": TASKZJ_ZJ_PCRNM,
               // "LANE_MX_SAMPLE_VOL": 9.99999999,
                "LANE_MX_SAMPLE_VOL": LANE_MX_SAMPLE_VOL,
            });
            /* objectpcr.push({
                 "ID":g[i]["ID"],
                 "TASKZJ_ZJ_PCRNM":TASKZJ_ZJ_PCRNM,
             });*/
        }
        var params = { tableName: "BIO_LANE_MX", objects: object }; //插入任务明细记录
        var url = "system/jdbc/save/batch/table";
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
                    alertMsg("提交成功!", "success"); // funcExce(pathValue+"close");//关闭页面
                } else {
                    alertMsg("提交失败!", "error");
                }
            },
        });

    }

    //记录移除
    var doPoolMxMove = function () {
        var arrg = [];
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            arrg = arrg.concat(arrSubID);

        }
        if (arrg.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        for (var i = 0; i < arrg.length; i++) {
            arrIds.push(arrg[i]["LIBID"]);
        }
        confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
            var params = { "tableName": "BIO_LIB_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        });
    }
    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS1 = [];
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
    }

    funcPushs(pathValue, {
        "initData": initData,
        "doPool": doPool,
        "doLaneMx": doLaneMx,
        "doControlJs": doControlJs,
        "init": init,
        "doPoolMxMove": doPoolMxMove,
        "callBack": callBack,
        "refreshGrid": refreshGrid
    });
});