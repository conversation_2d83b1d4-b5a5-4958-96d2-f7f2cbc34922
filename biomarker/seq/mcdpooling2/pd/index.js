$(document).ready(function() {
   var pathValue="biomarker-seq-mcdpooling2-pd-index";
   var initData=function(){
       return {};
   }
   var gridNameDGrid;
   var gridNameD1Grid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"edit",title:"指派实验员"}
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"seq_NGS2_lane_info","objects":[["草稿"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 420,
                    read:{"query":"queryDoMCD2LanePoolMain","objects":[[ROW_ID],['已排单']],"search":{"LANE_ID":[ROW_ID]}},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        init1();
   }
   
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[

       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET_list","objects":[["MCD2Lane排单"],["待接收","已接收","待审核","已审核","已完成"]]},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"seq_NGS_lane_SHEET_list_2","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           } 
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
  }

    var edit=function(){

       var arrIds=getSelectData(gridNameDGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行操作!");
            return ;
        }

        var winOpts={
            url:"biomarker/seq/mcdpooling2/pd/pdup/pdup",
            title:"MCD排Lane.."
         };
        openWindow(winOpts,{"IDS":arrIds,"EX_TYPE":"MCD2Lane排单"});
    }
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "edit":edit,
         "refreshGrid":refreshGrid,
         "callBack":callBack,
     });
});