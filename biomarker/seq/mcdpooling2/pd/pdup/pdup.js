$(document).ready(function() {
    var pathValue="biomarker-seq-mcdpooling2-pd-pdup-pdup";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"EXE_TQQC_SHEET"
        };
    }
    var init=function(params){
    	
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
    }
    var submit=function(){
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                     alertMsg("提交成功","success",function(){
                         var laneids=paramsValue["IDS"]; 
                         var objectup=[];
                         for(var i=0;i < laneids.length;i++){
             	       		objectup.push({
             	       			"ID":laneids[i],
             	       			"EXE_TQQC2_ID":result["ID"],
             	       			"LANE_STATUS2":"待接收"
             		    	});
             	        }
                         var urlsend="system/jdbc/save/batch/table";
                       	 var paramsadd={"tableName":"BIO_LANE_INFO","objects":objectup};
                       	 putAddOrUpdata(urlsend,paramsadd,"是","更新lane");
                       	 
                          
                     });
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });
    }
    //批量执行插入
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
               	 if(isDoCallBack=="是"){
               		funcExce(pathValue+"pageCallBack");
               		funcExce(pathValue+"close");
               	 }
                }else{
               	 alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 })