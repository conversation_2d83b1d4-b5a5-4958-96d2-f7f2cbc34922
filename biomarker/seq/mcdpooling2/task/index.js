$(document).ready(function() {
   var pathValue="biomarker-seq-mcdpooling2-task-index";
   var initData=function(){
       return {};
   }

   var gridNameDGrid;
   var gridNameD1Grid;
   var gridNameS=[];
   var gridNameS1=[];
   //Pool待调整
   var init=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	   {name:"edit",target:"doChangLane",title:"Lane信息填写.."},
    	   {name:"edit",target:"doChangMyTask",title:"Pool调整.."},
    	   {name:"return",target:"doReturn2",title:"退回"},
    	   {name:"edit",target:"doComfirm",title:"提交"},
           
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET_list","objects":[["MCD2Lane排单"],["待审核"]]},
           headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list_2","objects":[ROW_ID]},
                    headerFilter:function(cols,i){
                        if(i){
                            if(cols[i]["field"]&&cols[i]["field"]=="LANE_NO"){
                                setJsonParam(cols[i],"template",getTemplate("#= LANE_NO #","funcExce(\'"+pathValue+"elane\',\'#= ID #\');","txt"));
                            }
                        }
                    }
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
       };
       gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
      init1();
  }
   //已完成
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	   {name:"return",target:"doReturn",title:"撤回"},
           {name:"printer",target:"putData1",title:"打印"}
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET_list","objects":[["MCD2Lane排单"],["已审核","已完成"]]},
           headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list_2","objects":[ROW_ID]},
                    headerFilter:function(cols,i){
                        if(i){
                            if(cols[i]["field"]&&cols[i]["field"]=="LANE_NO"){
                                setJsonParam(cols[i],"template",getTemplate("#= LANE_NO #","funcExce(\'"+pathValue+"vlane\',\'#= ID #\');","txt"));
                            }
                        }
                    }
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
  }
   
 function getRandomId() {
    return (('FDSX' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
 };
  
 
//提交
 var doComfirm=function(){
	 var g=getGridSelectData(gridNameDGrid);  
     if(g.length==0){
         alertMsg("请至少选择一条数据进行接收操作!");
         return ;
     }
     var objectupmain=[];
     for(var i=0;i < g.length;i++){
  	   var mainid=g[i]["ID"];
  	   //主单状态
  	   objectupmain.push({
	       		"ID":mainid,
	       		"EX_RE_STATUS":"已审核"
	       	});
  	 }
     var urlsend="system/jdbc/save/batch/table";
     var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
	  putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
 }

 //撤回
 var doReturn=function(){
 	var g=getGridSelectData(gridNameD1Grid);
     if(g.length==0){
        	alertMsg("请至少选择一条记录进行操作!");
        	return;
      }
     var objectup=[];
     for(var i=0;i<g.length;i++){
	   	  	if(g[i]["EX_RE_STATUS"]!="已审核"){  
	   	  		alertMsg("操作失败,所选记录“已完成”状态!");
	   	  		return;
	   	  	}else{
		   	  	objectup.push({
	   	       		"ID":g[i]["ID"],
	   		       	 "EX_RE_STATUS":"待审核"
	   		    });
	   	  	}
     }
    var urlsend="system/jdbc/save/batch/table";
    var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
 	putAddOrUpdata(urlsend,paramsup,"是","提交");
 }

var doReturn2=function(){
	 var g=getGridSelectData(gridNameDGrid);  
     if(g.length==0){
         alertMsg("请至少选择一条数据进行接收操作!");
         return ;
     }
     var objectupmain=[];
     for(var i=0;i < g.length;i++){
  	   var mainid=g[i]["ID"];
  	   objectupmain.push({
	       		"ID":mainid,
	       		"EX_RE_STATUS":"审核退回"
	       	});
  	 }
     var urlsend="system/jdbc/save/batch/table";
     var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
	  putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
 }
 //调整
 var doChangMyTask=function(){
	 var arrIds=[];
     for(var i=0;i<gridNameS.length;i++){
     	var arrSubID=getSelectData(gridNameS[i]);
     	if(arrSubID.length!=0){
     		arrIds=arrIds.concat(arrSubID);
     	}
     }
     if(arrIds.length==0){
     	alertMsg("请选择一条Lane记录进行操作!");
     	return;
     }else if(arrIds.length>1){
    	 alertMsg("一次只允许对一条lane记录进行操作!");
      	return; 
     }
     
     elane(arrIds[0]);
 }

//打印
 var putData1=function(componentId){
 	
 	var arrDatas=[];
     var columns=[];
     var exno="";
     var extype="";
     
     for(var i=0;i<gridNameS1.length;i++){
           if(i==0){
                  columns=gridNameS1[i].getOptions().columns;
             }
     	var arrSubDatas=getGridSelectData(gridNameS1[i]);
        	if(arrSubDatas.length!=0){
        		arrDatas=arrDatas.concat(arrSubDatas);
        	}
     }
     if(arrDatas.length==0){
     	alertMsg("请至少选择一条样本记录进行操作!");
     	return;
     }
 	
       var htmlContent=printGridDataToHtml(
     	  {columns:columns,
         	  data:arrDatas,
         	  "expKey":"P",
         	  align:"center",
         	  title:"混lane任务调整审核-MCD2",
         	  printHeader:[{"打印时间：":toDateFormatByZone(sysNowTimeFuncParams["sysNowTime"]),"制表人：":getLimsUser()["name"]}],
     		});
     $.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/settings/session/cache",
         ajaxData:{"html":htmlContent},
         succeed:function(result){
             if(result["code"]>0){
                 var data=result["info"];
                 var html=data["html"];
                 openComponent({
                     name:"打印控件",//组件名称s
                     componentId:componentId,
                     params:{"html":html,"style":"style-01"}
                 });
             }else{
                 alertMsg(result["info"],"warning");
             }
         }
     });
 }


//lane填写
var doChangLane=function(){
	 var arrIds=[];
     for(var i=0;i<gridNameS.length;i++){
     	var arrSubID=getSelectData(gridNameS[i]);
     	if(arrSubID.length!=0){
     		arrIds=arrIds.concat(arrSubID);
     	}
     }
     if(arrIds.length==0){
     	alertMsg("请选择Lane记录进行操作!");
     	return;
     }
      var winOpts={
		 url:"biomarker/seq/mcdpooling2/task/editlane/editlane",
		 title:"lane填写.."
	 };
       openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
 }

 var vlane=function(LandId){
	 var winOpts={
		 url:"biomarker/seq/mcdpooling2/task/vlane/vlane",
		 title:"lane详细.."
	 };
   openWindow(winOpts,{"LANE_ID":LandId});
 }
 
 var elane=function(LandId){
	 var winOpts={
		 url:"biomarker/seq/mcdpooling2/task/elane/elane",
		 title:"lane调整.."
	 };
   openWindow(winOpts,{"LANE_ID":LandId});
 }
 //批量执行插入
 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
     $.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:urls,
         ajaxData:inobjjson,
         succeed:function(result){
             if(result["code"]>0){
            	 if(isDoCallBack=="是"){
            		 alertMsg("提示:操作成功!");
            		 refreshGrid();
            	 }
             }else{
            	 alertMsg(errMsg+"操作失败!");
             }
         }
     });
 }

     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
    	 gridNameS=[];
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();
        }
       
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "refreshGrid":refreshGrid,
         "doComfirm":doComfirm,
         "doReturn":doReturn,
         "doReturn2":doReturn2,
         "vlane":vlane,
         "elane":elane,
         "doChangMyTask":doChangMyTask,
         "doChangLane":doChangLane,
         "callBack":callBack,
		 "putData1": putData1,
     });
});