$(document).ready(function() {
    var pathValue="biomarker-seq-mcdpooling2-task-editlane-editlane";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_LANE_INFO"
        };
    }
    var init=function(params){
       paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
    }
    var submit=function(){
    	//取出IDS
    	var ids=paramsValue["IDS"];
    	var jsonData = getJsonByForm("form",pathValue);
    	 var object=[];
         for(var i=0;i < ids.length;i++){
        	 object.push($.extend({},jsonData,{"ID":ids[i]}));
         }
         var params={"tableName":"BIO_LANE_INFO","objects":object};
         var url="system/jdbc/save/batch/table";
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:url,
             ajaxData:params,
             succeed:function(result){
                 if(result["code"]>0){
                 	funcExce(pathValue+"pageCallBack");
                  alertMsg("提交成功!");
                  funcExce(pathValue+"close");
                 }else{
                 	console.log(result);
                 }
             }
         });
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });