$(document).ready(function() {
    var pathValue="biomarker-seq-mcdpooling2-result_-index";
    var initData=function(){
        return {};
    }
 
    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameS=[];
  
    //Pool待调整
    var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
           //{name:"edit",target:"doChangMyTask",title:"Pool调整.."},
             {name:"edit",target:"doedit",title:"实验填写"},
            {name:"comfirm",target:"doComfirm",title:"提交"}
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"pd_lane_SHEET_list","objects":[["MCD2Lane排单"],["已审核"]]},
            headerFilter:function(cols,i){},
             detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
             detailInit: function (e) {
                 var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                     url: "system/jdbc/query/one/table",
                     sort: "",
                     toolbar: null,
                     height: 320,
                     read:{"query":"seq_NGS_lane_SHEET_list_2","objects":[ROW_ID]},
                     headerFilter:function(cols,i){
                         if(i){
                             if(cols[i]["field"]&&cols[i]["field"]=="LANE_NO"){
                                 setJsonParam(cols[i],"template",getTemplate("#= LANE_NO #","funcExce(\'"+pathValue+"elane\',\'#= ID #\');","txt"));
                             }
                         }
                     }
                 };
                 var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                 gridNameS.push(subGrid_N);
             }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
       init1();
   }
    //已完成
    var init1=function(params){
        var toolbar=getButtonTemplates(pathValue,[
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"pd_lane_SHEET_list","objects":[["MCD2Lane排单"],["已完成"]]},
            headerFilter:function(cols,i){},
             detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
             detailInit: function (e) {
                 var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                     url: "system/jdbc/query/one/table",
                     sort: "",
                     toolbar: null,
                     height: 320,
                     read:{"query":"seq_NGS_lane_SHEET_list_2","objects":[ROW_ID]},
                     headerFilter:function(cols,i){
                         if(i){
                             if(cols[i]["field"]&&cols[i]["field"]=="LANE_NO"){
                                 setJsonParam(cols[i],"template",getTemplate("#= LANE_NO #","funcExce(\'"+pathValue+"vlane\',\'#= ID #\');","txt"));
                             }
                         }
                     }
                 };
                 var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
             }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
   }
    
  
 //提交完成
  var doComfirm=function(){
      var g=getGridSelectData(gridNameDGrid);  
      if(g.length==0){
          alertMsg("请至少选择一条数据进行接收操作!");
          return ;
      }
      var objectupmain=[];
      var ids=[];
      for(var i=0;i < g.length;i++){
              var mainid=g[i]["ID"];
              ids.push(mainid);
              objectupmain.push({
                "ID":mainid,
                "EX_RE_STATUS":"已完成"
            });
        }
      doGo(ids,objectupmain);
   
  }
 
 //确认提交结果
  var doGo=function(ids,objectupmain){
      var params={"query":"seq_NGS_lane_SHEET_list_2","objects":[ids]};
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:"system/jdbc/query/one/table",
          ajaxData:params,
          succeed:function(result){
              if(result["code"]>0){
                  var rows=result["rows"];
                  var flag=0;
                  var objectup=[];
                  var objecaddlib=[];
                  var objectaddqc=[];
                  
                  var time=sysNowTimeFuncParams["sysNowTime"];
                  var username=getLimsUser()["name"];
                 
                  for(var i=0;i<rows.length;i++){
                        var row=rows[i];
                        //  var is2100=row["JK_TASKMX_L2100"];
                        //  var isqpcr=row["JK_TASKMX_QPCR"];
                        //  var isqubit=row["JK_TASKMX_QUBIT_IS"];
                         var is2100="否";
                         var isqpcr="否";
                         var isqubit="否";
                           var qc_flag="否";//否/是/完
                           if(is2100=="是"||
                                   isqpcr=="是"||
                                   isqubit=="是"){
                               
                                      qc_flag="是";
                                      flag=1;
                                      var libid=getRandomId();
                                      objecaddlib.push({//BIO_LIB_INFO
                                        "ID":libid,
                                        "LIBRARY_CODE":row["LANE_NO"],
                                        "FROM_ID":row["ID"],
                                        "FROM_TYPE":"混LANE",
                                        "JK_TASKMX_L2100":is2100,//是否片段检测
                                          "JK_TASKMX_QPCR":isqpcr,//是否QPCR检测
                                          "JK_TASKMX_QUBIT_IS":isqubit//是否QUBIT检测
                                    });
                                      objectaddqc.push({//BIO_LIB_QC_INFO
                                        "TASK_JK_ID":libid	       	
                                   });
                               }
                               objectup.push({//BIO_LANE_INFO
                                          "ID":row["ID"],
                                          "LANE_STATUS":"已提交结果",
                                          "QC_FLAG":qc_flag
                               });
                      }
                           
                      //实验结果
                      var url="system/jdbc/save/batch/table";
                     var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
                      putAddOrUpdata(url,paramsup,"是","更新lane"); 
                      
                      var paramsup={"tableName":"BIO_LANE_INFO","objects":objectup};
                      putAddOrUpdata(url,paramsup,"否","更新lane"); 
                      
                      if(flag==1){
                          var paramsup={"tableName":"BIO_LIB_QC_INFO","objects":objectaddqc};
                          putAddOrUpdata(url,paramsup,"否","生成QC记录");  
                          
                          var newUrl="system/jdbc/save/one/table/objects";
                          var paramsnainadd={"tableName":"BIO_LIB_INFO","objects":objecaddlib};
                          putAddOrUpdata(newUrl,paramsnainadd,"否","生成文库记录");
                      }    
                  
              }else{
                  alertMsg(errMsg+"操作失败!");
              }
          }
      });
             
  }
  
  function getRandomId() {
         return (('FDSX' || '-LANE-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
  };
  
  //调整
  var doChangMyTask=function(){
      var arrIds=[];
      for(var i=0;i<gridNameS.length;i++){
          var arrSubID=getSelectData(gridNameS[i]);
          if(arrSubID.length!=0){
              arrIds=arrIds.concat(arrSubID);
          }
      }
      if(arrIds.length==0){
          alertMsg("请选择一条Lane记录进行操作!");
          return;
      }else if(arrIds.length>1){
          alertMsg("一次只允许对一条lane记录进行操作!");
           return; 
      }
      
      elane(arrIds[0]);
  }
  var doedit=function(){
          var arrIds=[];
          for(var i=0;i<gridNameS.length;i++){
              var arrSubID=getSelectData(gridNameS[i]);
              if(arrSubID.length!=0){
                  arrIds=arrIds.concat(arrSubID);
              }
          }
          if(arrIds.length==0){
               alertMsg("请选择一条Lane记录进行操作!");
               return;
           }
         var winOpts={
             url:"biomarker/seq/mcdpooling2/result_/upset/upset",
             title:"MCDLane填写.."
         };
         openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
      }
  var vlane=function(LandId){
      var winOpts={
          url:"biomarker/seq/mcdpooling2/result_/vlane/vlane",
          title:"lane详细.."
      };
    openWindow(winOpts,{"LANE_ID":LandId});
  }
  
  var elane=function(LandId){
      var winOpts={
          url:"biomarker/seq/mcdpooling2/result_/elane/elane",
          title:"lane调整.."
      };
    openWindow(winOpts,{"LANE_ID":LandId});
  }
  //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
                  if(isDoCallBack=="是"){
                      alertMsg("提示:操作成功!");
                      refreshGrid();
                  }
              }else{
                  alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 
      var callBack=function(){
         refreshGrid();
      };
 
      var refreshGrid=function(){
          gridNameS=[];
         if(gridNameDGrid){
             gridNameDGrid.dataSource.read();
         }
         if(gridNameD1Grid){
             gridNameD1Grid.dataSource.read();
         }
        
      }
 
      funcPushs(pathValue,{
          "initData":initData,
          "init":init,
          "refreshGrid":refreshGrid,
          "doComfirm":doComfirm,
          "doedit":doedit,
          "vlane":vlane,
          "elane":elane,
          "doChangMyTask":doChangMyTask,
          "callBack":callBack,
      });
 });