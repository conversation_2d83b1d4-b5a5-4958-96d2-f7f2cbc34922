$(document).ready(function() {
   var pathValue="biomarker-seq-mcdpooling2-result_-elane-elane";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var paramsValue;
   var gridNameS1=[];
   var init=function(params){
	    paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
             {name:"ok",target:"doPool",title:"修改Pool信息"},
             {name:"ok",target:"doPoolMxMove",title:"移除Pool明细"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDLanePoolMain",
            	"objects":[[paramsValue["LANE_ID"]],["草稿","退回","待接收","已接收","已排单","已完成"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                         url: "system/jdbc/query/one/table",
                         sort: "",
                         toolbar: null,
                         height: 320,
                         read:{"query":"queryTaskPoolLibExMx","objects":[],"search":{"LANE_ID":[ROW_ID]}},
                     };
                     var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                    gridNameS1.push(subGrid_N);
                 }
         	 
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
   }
   var doPool=function(){
	   var g=getGridSelectData(gridNameGrid);
       if(g.length==0){
       	alertMsg("请至少选择一条记录进行操作!");
       	return;
       }
       
       var ids=[];
       var laneid="";
       
       for(var i=0;i<g.length;i++){
       	ids.push(g[i]["ID"]);
       }
      var winOpts={
            url:"biomarker/seq/mcdpooling2/result_/dopool/dopool",
            title:"Pool..",
            currUrl:replacePathValue(pathValue)
       };
       openWindow(winOpts,{"IDS":ids,"ID":ids[0]});
   }

 //记录移除
   var doPoolMxMove=function(){
  	 var arrg=[];
  	 var arrIds=[];
       for(var i=0;i<gridNameS1.length;i++){
       	var arrSubID=getGridSelectData(gridNameS1[i]);
       	arrg=arrg.concat(arrSubID);
       	
       }
       if(arrg.length==0){
           alertMsg("请至少选择一条数据进行操作!");
           return ;
       }
       for(var i=0;i < arrg.length;i++){
       	arrIds.push(arrg[i]["LIBID"]);
       }
       confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function() {
          var params={"tableName":"BIO_LIB_INFO","ids":arrIds};
          var url="system/jdbc/delete/batch/table";
          deleteGridDataByIds(url,params,refreshGrid);
       });
    }
     var callBack=function(){
        refreshGrid();
     };

    var refreshGrid=function(){
        gridNameS1=[];
        if(gridNameGrid){
        	gridNameGrid.dataSource.read();
        }     
     }
   
     funcPushs(pathValue,{
         "initData":initData,
         "doPool":doPool,
         "init":init,
         "doPoolMxMove":doPoolMxMove,
         "callBack":callBack,
         "refreshGrid":refreshGrid
      });
});