$(document).ready(function() {
   var pathValue="biomarker-seq-mcdpooling-re-index";
   var initData=function(){
       return {};
   }

   var gridNameDGrid;
   var gridNameD1Grid;
   var gridNameD2Grid;
   var gridNameS1=[];
   var gridNameS2=[];
   var subGrid_N2;
   //待接收
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"doReMyTask",title:"确认接收"},
            {name:"edit",target:"doTaskToExcel2",title:"研发导入(一级)/模板"},
            {name:"edit",target:"doTaskToExcel",title:"研发导入(二级)/模板"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"pd_lane_SHEET22_list","objects":[["MCDLane排单"],["MCD待接收"],["MCD待接收"]]},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var LANE_NO = e.data.LANE_NO;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list","objects":[[ROW_ID],[LANE_NO]]},
                    headerFilter:function(cols,i){
                        if(i){
                            if(cols[i]["field"]&&cols[i]["field"]=="LANE_NO"){
                                setJsonParam(cols[i],"template",getTemplate("#= LANE_NO #","funcExce(\'"+pathValue+"vlane\',\'#= ID #\');","txt"));
                            }
                        }
                    }
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
               subGrid_N2 = subGrid_N;
               gridNameS1.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
        init1();
        init2();
        init3();
   }
   //待Pool
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	   {name:"edit",target:"doReMyTaskFF",title:"Lane Pool操作.."},
    	   {name:"edit",target:"doOK",title:"提交"},
           {name:"printer",target:"putData1",title:"打印"}
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET22_list","objects":[["MCDLane排单"],["MCD已接收"],["MCD已接收"]]},
           headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var LANE_NO = e.data.LANE_NO;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list","objects":[[ROW_ID],[LANE_NO]]},
                    headerFilter:function(cols,i){
                        if(i){
                            if(cols[i]["field"]&&cols[i]["field"]=="LANE_NO"){
                                setJsonParam(cols[i],"template",getTemplate("#= LANE_NO #","funcExce(\'"+pathValue+"vlane\',\'#= ID #\');","txt"));
                            }
                        }
                    }
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
  }
   //Pool待调整
   var init2=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	  // {name:"edit",target:"doChangLane",title:"Lane信息填写.."},
    	  // {name:"edit",target:"doChangMyTask",title:"Pool混库调整.."},
    	  // {name:"comfirm",target:"doComfirm",title:"提交完成"}
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET22_list","objects":[["MCDLane排单"],["MCD待审核","MCD已审核","MCD已完成"],["MCD待审核","MCD已审核","MCD已完成"]]},
           headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var LANE_NO = e.data.LANE_NO;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list","objects":[[ROW_ID],[LANE_NO]]},
                    headerFilter:function(cols,i){
                        if(i){
                            if(cols[i]["field"]&&cols[i]["field"]=="LANE_NO"){
                                setJsonParam(cols[i],"template",getTemplate("#= LANE_NO #","funcExce(\'"+pathValue+"elane\',\'#= ID #\');","txt"));
                            }
                        }
                    }
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
       };
       gridNameD2Grid = initKendoGrid("#gridNameD2Grid"+pathValue,gridNameGridJson);
  }

 function getRandomId() {
    return (('FDSX' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
 };
  
 //根据执行单ID获取明细信息
 var doReMyTask=function(){
		   var g=getGridSelectData(gridNameDGrid);  
	       if(g.length==0){
	           alertMsg("请至少选择一条数据进行接收操作!");
	           return ;
	       }
	       var objectupmain=[];
	       for(var i=0;i < g.length;i++){
	    	   var mainid=g[i]["ID"];
	    	   //主单状态
	    	   objectupmain.push({
    	       		"ID":mainid,
    	       		"EX_RE_STATUS":"MCD已接收"
    	       	});
	    	 }
	       var urlsend="system/jdbc/save/batch/table";
           var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
    	   putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");     
 }
 
 //调整
 var doReMyTaskFF=function(){
	 var arrIds=[];
     for(var i=0;i<gridNameS1.length;i++){
     	var arrSubID=getSelectData(gridNameS1[i]);
     	if(arrSubID.length!=0){
     		arrIds=arrIds.concat(arrSubID);
     	}
     }
     if(arrIds.length==0){
     	alertMsg("请选择一条Lane记录进行操作!");
     	return;
     }else if(arrIds.length>1){
    	 alertMsg("一次只允许对一条lane记录进行操作!");
      	return; 
     }
	 var winOpts={
	     url:"biomarker/seq/mcdpooling/re/chageLane/chageLane",
	     title:"MCD混Lane调整.."
	  };
	  openWindow(winOpts,{"LANE_ID":arrIds});
	 
 }
 //提交
 var doOK=function(){
	 var g=getGridSelectData(gridNameD1Grid);  
     if(g.length==0){
         alertMsg("请至少选择一条数据进行接收操作!");
         return ;
     }
     var objectupmain=[];
     for(var i=0;i < g.length;i++){
  	   var mainid=g[i]["ID"];
  	   //主单状态
  	   objectupmain.push({
	       		"ID":mainid,
	       		"EX_RE_STATUS":"MCD待审核"
	       	});
  	 }
     var urlsend="system/jdbc/save/batch/table";
     var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
	   putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
 }
 
 //打印
	var putData1=function(componentId){
		
	    var arrDatas=[];
	    var columns=[];
	    var exno="";
	    var extype="";
	    
	    for(var i=0;i<gridNameS1.length;i++){
	          if(i==0){
	                 columns=gridNameS1[i].getOptions().columns;
	            }
	    	var arrSubDatas=getGridSelectData(gridNameS1[i]);
	       	if(arrSubDatas.length!=0){
	       		arrDatas=arrDatas.concat(arrSubDatas);
	       	}
	    }
	    if(arrDatas.length==0){
	    	alertMsg("请至少选择一条样本记录进行操作!");
	    	return;
	    }

	     var htmlContent=printGridDataToHtml(
	    	  {columns:columns,
	        	  data:arrDatas,
	        	  "expKey":"P",
	        	  align:"center",
	        	  title:"混lane任务调整-MCD1",
	        	 printHeader:[{"打印时间：":toDateFormatByZone(sysNowTimeFuncParams["sysNowTime"]),"制表人：":getLimsUser()["name"]}],
      		});
	    $.fn.ajaxPost({
	        ajaxType:"post",
	        ajaxUrl:"system/settings/session/cache",
	        ajaxData:{"html":htmlContent},
	        succeed:function(result){
	            if(result["code"]>0){
	                var data=result["info"];
	                var html=data["html"];
	                openComponent({
	                    name:"打印控件",//组件名称s
	                    componentId:componentId,
	                    params:{"html":html,"style":"style-01"}
	                });
	            }else{
	                alertMsg(result["info"],"warning");
	            }
	        }
	    });
	}
	    
 
 
//提交
 var doComfirm=function(){
	 var g=getGridSelectData(gridNameD2Grid);  
     if(g.length==0){
         alertMsg("请至少选择一条数据进行接收操作!");
         return ;
     }
     var objectupmain=[];
     for(var i=0;i < g.length;i++){
  	   var mainid=g[i]["ID"];
  	   //主单状态
  	   objectupmain.push({
	       		"ID":mainid,
	       		"EX_RE_STATUS":"MCD已完成"
	       	});
  	 }
     var urlsend="system/jdbc/save/batch/table";
     var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
	  putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
 }
 
 var doChangMyTask=function(){
	 var arrIds=[];
     for(var i=0;i<gridNameS2.length;i++){
     	var arrSubID=getSelectData(gridNameS2[i]);
     	if(arrSubID.length!=0){
     		arrIds=arrIds.concat(arrSubID);
     	}
     }
     if(arrIds.length==0){
     	alertMsg("请选择一条Lane记录进行操作!");
     	return;
     }else if(arrIds.length>1){
    	 alertMsg("一次只允许对一条lane记录进行操作!");
      	return; 
     }
     
     elane(arrIds[0]);
 }
//lane填写
var doChangLane=function(){
	 var arrIds=[];
     for(var i=0;i<gridNameS2.length;i++){
     	var arrSubID=getSelectData(gridNameS2[i]);
     	if(arrSubID.length!=0){
     		arrIds=arrIds.concat(arrSubID);
     	}
     }
     if(arrIds.length==0){
     	alertMsg("请选择Lane记录进行操作!");
     	return;
     }
      var winOpts={
		 url:"biomarker/seq/mcdpooling/re/editlane/editlane",
		 title:"lane填写.."
	 };
       openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
 }

 var vlane=function(LandId){
	 var winOpts={
		 url:"biomarker/seq/mcdpooling/re/vlane/vlane",
		 title:"lane详细.."
	 };
       openWindow(winOpts,{"LANE_ID":LandId});
 }
 
 var elane=function(LandId){
	 var winOpts={
		 url:"biomarker/seq/mcdpooling/re/elane/elane",
		 title:"lane详细.."
	 };
   openWindow(winOpts,{"LANE_ID":LandId});
 }
 //批量执行插入
 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
     $.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:urls,
         ajaxData:inobjjson,
         succeed:function(result){
             if(result["code"]>0){
            	 if(isDoCallBack=="是"){
            		 alertMsg("提示:操作成功!");
            		 refreshGrid();
            	 }
             }else{
            	 alertMsg(errMsg+"操作失败!");
             }
         }
     });
 }

     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
    	 gridNameS1=[];
    	 gridNameS2=[];
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();
        }
        if(gridNameD2Grid){
        	gridNameD2Grid.dataSource.read();
        }
 
     }
//研发导入(二级)/模板
var doTaskToExcel=function(componentId){
        var grid=subGrid_N2;
        var gridmRows=getGridSelectData(gridNameDGrid);
        if(gridmRows.length==0){
            alertMsg("至少选择一行主单数据");
            return ;
        }else if(gridmRows.length>1){
            alertMsg("只能选择一行主单数据");
            return ;
        }
        //debugger;
        var p={
            "tableName":"BIO_RD_TASK_POOL_LANE_MCD1",
            "topMap":{//主表信息
                "tableName":"EXE_TQQC_SHEET",
                "ID":gridmRows[0].ID,// -- 混库ID
            },
             "mapping":{
             	"LANE_SEQ_DOMIX_ID":"${ID}",
             	"LANE_NO":gridmRows[0].LANE_NO,
             }
        };
         //debugger;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":function(){
                    saveGridDataToExcel({grid:subGrid_N2,select:1,expKey:"B"});
                },
                "import":function(info,pv){
                    $.fn.ajaxPost({
                        ajaxType:"post",
                        ajaxUrl:"system/config/meta/importData2",
                        ajaxData:$.extend(p,{"info":info},{"template":grid.getOptions().columns,expKey:"B"}),
                        succeed:function(result){
                            alertMsg("导入成功!","success",function() {
                                 //debugger;
                                funcExce(pv+"close");//关闭页面
                                refreshGrid();
                            });
                        },
                        failed:function(result){
                            if(result["msg"]){
                                alertMsg(result["msg"],"error");
                            }else{
                                alertMsg("导入失败!","error");
                            }
                        }
                    });      
                }
            }
        });
    }


//研发导入（一级）
 var doTaskToExcel2=function(componentId){
   var grid=gridNameDGrid;
         openComponent({ 
             name:"导入数据",//组件名称
             componentId:componentId,
             params:{
                 "template":function(p,n){
                     if(n=="import"){
                         return {template:grid.getOptions().columns,
                             expKey:"A",
                             tableName:"BIO_RD_TASK_POOL_LANE_MCD1ZD"
                         };
                     }else{
                         saveGridDataToExcel({grid:gridNameDGrid,select:1,expKey:"A"});
                     }
                 },
                 "succeed":function(){
                     refreshGrid();
                     return false;
                 }
             },
         });
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "refreshGrid":refreshGrid,
         "doReMyTask":doReMyTask,
         "doReMyTaskFF":doReMyTaskFF,
         "doOK":doOK,
          "doTaskToExcel":doTaskToExcel,
         "doTaskToExcel2":doTaskToExcel2,
         "doComfirm":doComfirm,
         "vlane":vlane,
         "elane":elane,
         "doChangMyTask":doChangMyTask,
         "doChangLane":doChangLane,
         "callBack":callBack,
	"putData1": putData1,
     });
});