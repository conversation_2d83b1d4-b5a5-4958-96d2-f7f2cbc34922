$(document).ready(function() {
   var pathValue="biomarker-seq-mcdpooling-re-addtoex-addtoex";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var paramsValue;
   var init=function(params){
	    paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
        	{name:"edit",target:"add",title:"确认选择"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDLanePoolMain",
            	"objects":[paramsValue["LANE_ID"],["草稿","退回"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                         url: "system/jdbc/query/one/table",
                         sort: "",
                         toolbar: null,
                         height: 320,
                   read:{"query":"seq_NGSMCD_lane_Pool_Lib_mx","objects":[],"search":{"ID":[ROW_ID]}},
                     };
                     var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                 }
         	 
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
   }

    var add=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
           	alertMsg("请选择一条记录进行操作!");
           	return;
         }else if(arrIds.length>1){
        	 alertMsg("仅限选择一条记录进行操作!");
             return;
         }
	   var time=sysNowTimeFuncParams["sysNowTime"];
	   var username=getLimsUser()["name"];

        var objectadd=[];
        for(var i=0;i<arrIds.length;i++){
     	   var ids=paramsValue["IDS"];                     	
 	  	   var time=sysNowTimeFuncParams["sysNowTime"];
 	  	   var username=getLimsUser()["name"];
 	  	   for(var j=0;j < ids.length;j++){
 	       		objectadd.push({
              		        "ID":ids[j],
    	    	                "POOL_ID":arrIds[i],
                 	    	"SYS_MAN":username,
                	    	"SYS_INSERTTIME":time
 		    	});
 	        }
        	}
         var urlsend="system/jdbc/save/batch/table";
    	 var paramsadd={"tableName":"BIO_LANE_MX","objects":objectadd};
    	 putAddOrUpdata(urlsend,paramsadd,"是","推入下一步实验任务");
       
     
    }
    //批量执行插入
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
               	 if(isDoCallBack=="是"){
               		 alertMsg("提示:操作成功!");
               		 funcExce(pathValue+"pageCallBack");
               		 funcExce(pathValue+"close");
               	 }
                }else{
               	 alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "open":open,
         "add":add,
         "callBack":callBack,
     });
});