$(document).ready(function() {
    var pathValue="biomarker-seq-mcdpooling-re-chageLane-chageLane";
    var initData=function(){
        return {};
    }
    var paramsValue;
    var gridNameGrid;
    var gridName1Grid;
    var gridName2Grid;
    var gridNameS1=[];
    var gridNameS2=[];
    var gridData;
    //待Pool
    var init=function(params){
        paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
            {name:"excel",target:"importData1",title:"实验导入/模板"},
            {name:"submit",target:"doPool",title:"生成Pool"},
            {name:"edit",target:"addToEx",title:"追加任务.."},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            size:5000,
            read:{
                "query":"queryDoMCDLanePoollist",
                "objects":[],
                "search":{"LANE_ID":paramsValue["LANE_ID"]}},
            fetch:function(data){
                gridData=data;
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
        init1();
       // init2();
    }
    //Pool草稿
    var init1=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"submit",target:"edit",title:"修改Pool.."},
            {name:"delete",target:"remove",title:"移除Pool明细"},
            {name:"delete",target:"deleteInfo",title:"删除Pool"},
            {name:"ok",target:"doOK",title:"提交"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDLanePoolMain",
                "objects":[paramsValue["LANE_ID"],["草稿","待接收","退回"]]},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    size:5000,
                    read:{"query":"seq_NGSMCD_lane_Pool_Lib_mx","objects":[],"search":{"POOL_ID":[ROW_ID]}},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
        };
        gridName1Grid = initKendoGrid("#gridName1Grid"+pathValue,gridNameGridJson);
    }
    //已处理
    var init2=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"ok",target:"doReturn",title:"撤回"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDLanePoolMain",
                "objects":[paramsValue["LANE_ID"],["待接收","已接收"]]},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGSMCD_lane_Pool_Lib_mx","objects":[],"search":{"POOL_ID":[ROW_ID]}},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
            }
        };
        gridName2Grid = initKendoGrid("#gridName2Grid"+pathValue,gridNameGridJson);
    }

    //排单生成
    var edit=function(){

        var g=getGridSelectData(gridName1Grid);
        if(g.length!=1){
            alertMsg("请选择一条记录进行操作!");
            return;
        }

        var ids=[];
        var libids=[];
        var laneid="";

        for(var i=0;i<g.length;i++){
            ids.push(g[i]["ID"]);
            libids.push(g[i]["LIB_ID"]);
            laneid=g[i]["LANE_ID"];
        }
        var winOpts={
            url:"biomarker/seq/mcdpooling/re/dopool/dopool",
            title:"Pool..",
            currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"ID":ids[0]});
    }


    var doPool=function(){
        debugger;
        if(gridData.length>0){
            var pools=[];
            var poolIds=[];
            for(var i=0;i<gridData.length;i++){
                var row=gridData[i];
                if(row["POOL_CODE"]){
                    if(pools.includes(row["POOL_CODE"])==false){
                        poolIds.push(getRandomId());
                        pools.push(row["POOL_CODE"]);
                    }
                }else{
                    alertMsg("提示:Pool编号列存在空值!");
                    return;
                }
            }
            var time=sysNowTimeFuncParams["sysNowTime"];
            var username=getLimsUser()["name"];
            var objectupmx=[];
            var objectaddlane=[];
            var objaddpool=[];
            for(var i=0;i<poolIds.length;i++){
                var LANE_ID="";
                var n=0;
                var sumtj=0;
                for(var j=0;j<gridData.length;j++){
                    var row=gridData[j];
                    if(pools[i]==row["POOL_CODE"]){//BIO_LANE_MX
                        LANE_ID=row["LANE_ID"];
                        n++;
                        sumtj+=row["LANE_MX_SAMPLE_VOL"];
                        objectupmx.push({
                            "ID":row["ID"],
                            "POOL_ID":poolIds[i],
                            "SYS_MAN":username,
                            "SYS_INSERTTIME":time
                        });
                    }

                }
                objectaddlane.push({//BIO_LANE_MX
                    "LIB_ID":poolIds[i],
                    "LANE_ID":LANE_ID,//关联ID
                    "POOL_ID":"",
                    "LIBRARY_CODE":pools[i],//文库编号
                    "ISMCD":"是",//为MCD库
                    "ISPOOL":"是",
                    "ISSEL":"-1",
                    "SYS_MAN":username,
                    "SYS_INNERTTIME":time
                });

                objaddpool.push({//BIO_LIB_POOLING
                    "ID":poolIds[i],
                    "POOL_CODE":pools[i],
                    "POOL_TYPE":"MCD混Lane建库",
                    "POOL_STATUS":"待接收",
                    "POOL_LIB_NUM":n,
                    "POOL_VOL":sumtj,
                    "SEQ_PLAT":"NGS",
                    "POOL_MAN":username,
                    "POOL_B_TIME":time,
                    "SYS_MAN":username,
                    "SYS_INNERTTIME":time
                });
            }

            //生成
            var urlsend="system/jdbc/save/batch/table";
            var paramsaddlane={"tableName":"BIO_LANE_MX","objects":objectaddlane};
            putAddOrUpdata(urlsend,paramsaddlane,"否","添加lane明细");

            var paramsUP={"tableName":"BIO_LANE_MX","objects":objectupmx};
            putAddOrUpdata(urlsend,paramsUP,"否","更新原有明细");

            var addurl="system/jdbc/save/one/table/objects";
            var paramsaddpool={"tableName":"BIO_LIB_POOLING","objects":objaddpool};
            putAddOrUpdata(addurl,paramsaddpool,"是","添加pool");

        }
    }





    //审核提交
    var doOK=function(){
        var arrIds=getSelectData(gridName1Grid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup=[];
        for(var i=0;i<arrIds.length;i++){
            objectup.push({
                "ID":arrIds[i],//联联任务ID
                "POOL_STATUS":"待接收"
            });
        }
        var urlsend="system/jdbc/save/batch/table";
        var paramsup={"tableName":"BIO_LIB_POOLING","objects":objectup};
        putAddOrUpdata(urlsend,paramsup,"是","提交");
    }
    //撤回
    var doReturn=function(){
        var g=getGridSelectData(gridName2Grid);
        if(g.length==0){
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup=[];
        for(var i=0;i<g.length;i++){
            if(g[i]["POOL_STATUS"]!="待接收"){
                alertMsg("操作失败,所选记录存在已“已接收”状态!");
                return;
            }else{
                objectup.push({
                    "ID":g[i]["ID"],
                    "POOL_STATUS":"草稿"
                });
            }
        }
        var urlsend="system/jdbc/save/batch/table";
        var paramsup={"tableName":"BIO_LIB_POOLING","objects":objectup};
        putAddOrUpdata(urlsend,paramsup,"是","提交");
    }
    //追加任务
    var addToEx=function(){
        var g=getGridSelectData(gridNameGrid);
        if(g.length==0){
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var ids=[];
        for(var i=0;i<g.length;i++){
            ids.push(g[i]["ID"]);
        }

        var winOpts={
            url:"biomarker/seq/mcdpooling/re/addtoex/addtoex",
            title:"追加任务到Pool..",
            currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"IDS":ids,"LANE_ID":paramsValue["LANE_ID"]});
    }


    //记录移除
    var remove=function(){
        var arrg=[];
        var arrIds=[];
        for(var i=0;i<gridNameS1.length;i++){
            var arrSubID=getGridSelectData(gridNameS1[i]);
            arrg=arrg.concat(arrSubID);
        }
        if(arrg.length==0){
            alertMsg("请至少选择一条数据进行操作!");
            return ;
        }
        var objectup=[];
        for(var i=0;i < arrg.length;i++){
            objectup.push({
                "ID":arrg[i]["ID"],
                "POOL_ID":null
            });
        }
        var urlsend="system/jdbc/save/batch/table";
        var paramsup={"tableName":"BIO_LANE_MX","objects":objectup};
        putAddOrUpdata(urlsend,paramsup,"是","提交");

    }
    //deleteInfo
    var deleteInfo=function(){
        var g=getSelectData(gridName1Grid);
        if(g.length==0){
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }

        var delurl="system/jdbc/delete/one/table/where";
        var delparms={"tableName":"BIO_LIB_POOLING","where":{"ID":g}}
        putAddOrUpdata(delurl,delparms,"是","提交");

        var upurl="system/jdbc/update/one/table/where";
        var paramsup={"tableName":"BIO_LANE_MX","POOL_ID":null,"where":{"POOL_ID":g}};
        putAddOrUpdata(upurl,paramsup,"否","提交");

    }
    //表格导入
    var importData1=function(componentId){
        var arrIds=getGridSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var ids=[];
        for(var i=0;i<arrIds.length;i++){
            ids.push(arrIds[i]["ID"]);
        }

        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                template:function(p,n){
                    return exportAndImportData({
                        expKey:"A",
                        tableName:"BIO_LANE_MX",
                        requestData:{
                            ajaxData:{"query":"queryDoMCDLanePoollist","size":5000,"objects":[],"search":{"LANE_ID":paramsValue["LANE_ID"]}},
                        },
                        params:p,
                        name:n,
                    });
                }
            },
            callBack:refreshGrid
        });
    }
    //批量执行插入
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
                    if(isDoCallBack=="是"){
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                }else{
                    alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }

    function getRandomId() {
        return 'FDSX-MCDPool-'+ new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };

    var callBack=function(){
        refreshGrid();
    };

    var refreshGrid=function(){
        gridNameS1=[];
        gridNameS2=[];
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
        if(gridName1Grid){
            gridName1Grid.dataSource.read();
        }
        if(gridName2Grid){
            gridName2Grid.dataSource.read();
        }

    }

    funcPushs(pathValue,{
        "initData":initData,
        "init":init,
        "edit":edit,
        "doPool":doPool,
        "addToEx":addToEx,
        "doOK":doOK,
        "doReturn":doReturn,
        "remove":remove,
        "deleteInfo":deleteInfo,
        "refreshGrid":refreshGrid,
        "callBack":callBack,
        "importData1":importData1,
    });
});