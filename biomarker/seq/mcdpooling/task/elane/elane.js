$(document).ready(function() {
   var pathValue="biomarker-seq-mcdpooling-task-elane-elane";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var paramsValue;
   var gridNameS=[];
   var init=function(params){
	    paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
             {name:"ok",target:"doPool",title:"修改Pool信息"},
             {name:"excel",target:"importData1",title:"实验导入/模板"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDLanePoolMain",
            	"objects":[[paramsValue["LANE_ID"]],["草稿","退回","待接收","已接收","已排单","已完成"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                         url: "system/jdbc/query/one/table",
                         sort: "",
                         toolbar: null,
                         height: 320,
                        read:{"query":"seq_NGSMCD_lane_Pool_Lib_mx","objects":[],"search":{"POOL_ID":[ROW_ID]}},
                     };
                     var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                    gridNameS.push(subGrid_N);
                 }
         	 
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
   }
   var doPool=function(){
	   var g=getGridSelectData(gridNameGrid);
       if(g.length==0){
       	alertMsg("请至少选择一条记录进行操作!");
       	return;
       }
       
       var ids=[];
       var laneid="";
       
       for(var i=0;i<g.length;i++){
       	ids.push(g[i]["ID"]);
       }
      var winOpts={
            url:"biomarker/seq/mcdpooling/re/dopool/dopool",
            title:"Pool..",
            currUrl:replacePathValue(pathValue)
       };
       openWindow(winOpts,{"IDS":ids,"ID":ids[0]});
   }
     //表格导入
	var importData1=function(componentId){
	 	var arrIds=[];
	        for(var i=0;i<gridNameS.length;i++){
	        		var arrSubID=getGridSelectData(gridNameS[i]);
	 	       	if(arrSubID.length!=0){
	 	       		arrIds=arrIds.concat(arrSubID);
	 	       	}
	        }
               //console.log(arrIds[0]);
	        if(arrIds.length==0){
	        	alertMsg("请至少选择一条样本记录进行操作!");
	        	return;
	        }
                var ids=[];
	        for(var i=0;i<arrIds.length;i++){
                  ids.push(arrIds[i]["ID"]);
                }


	 openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                template:function(p,n){
                    return exportAndImportData({
                        expKey:"A",
                        tableName:"BIO_LANE_MX",
                        requestData:{
                         ajaxData:{"query":"queryTaskPoolLibExMx","size":5000,"objects":[],"search":{"ID":ids}},
                        },
                        params:p,
                        name:n,
                    });
                }
            },
            callBack:refreshGrid
        });
      }
	    
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
    	 gridNameS=[];
        if(gridNameGrid){
        	gridNameGrid.dataSource.read();
        }
     }
     
   
     funcPushs(pathValue,{
         "init":init,
         "initData":initData,
         "doPool":doPool,
         "importData1":importData1,
        "callBack":callBack,
"        refreshGrid":refreshGrid,

     });
});