$(document).ready(function() {
    var pathValue="biomarker-seq-mcdpooling-task-lanev-lanev";

        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"BIO_LANE_INFO"
        };
    }
	
	var paramsValue;
	var gridNameGrid;
	

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
		paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        $("#LANE_TYPE"+pathValue).val(params["LANE_TYPE"]);
        
        var toolbar=getButtonTemplates(pathValue,[
           
       ]);//工具条
       //请求参数
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           height: fullh-300,
           toolbar: toolbar,
           read:{"query":"seq_NGS_lane_info_mx_MCD","objects":[params["ID"]]},
		   headerFilter:function(cols,i){
                if(i){
                   
                }
            }
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
    }
	

     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read(
            		{
                     	"objects":[paramsValue["ID"]]
                     });//重新读取--刷新
        }
     }

  

	funcPushs(pathValue,{
		"initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
		"init":init,//初始化方法-在加载完初始化数据之后执行
		"refreshGrid":refreshGrid,
		"callBack":callBack,//回调方法
	});
 
 });