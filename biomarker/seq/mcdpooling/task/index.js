$(document).ready(function () {
    var pathValue = "biomarker-seq-mcdpooling-task-index";
    var initData = function () {
        return {};
    }

    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameS = [];
    var gridNameS2 = [];

    //Pool待调整
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doChangLane", title: "Lane信息填写.." },
            { name: "edit", target: "doChangMyTask", title: "Pool调整.." },
            { name: "edit", target: "doComfirm", title: "提交" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "pd_lane_SHEET_list", "objects": [["MCDLane排单"], ["MCD待审核"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "seq_NGS_lane_SHEET_list", "objects": [ROW_ID] },
                    headerFilter: function (cols, i) {
                        if (i) {
                            if (cols[i]["field"] && cols[i]["field"] == "LANE_NO") {
                                setJsonParam(cols[i], "template", getTemplate("#= LANE_NO #", "funcExce(\'" + pathValue + "elane\',\'#= ID #\');", "txt"));
                            }
                        }
                    }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        init1();
    }
    //已完成
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
            { name: "printer", target: "putData1", title: "打印" },
            { name: "edit", target: "doComfirmPE", title: "提交PE" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "pd_lane_SHEET_list", "objects": [["MCDLane排单"], ["MCD已审核", "MCD已完成"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "seq_NGS_lane_SHEET_list", "objects": [ROW_ID] },
                    headerFilter: function (cols, i) {
                        if (i) {
                            if (cols[i]["field"] && cols[i]["field"] == "LANE_NO") {
                                setJsonParam(cols[i], "template", getTemplate("#= LANE_NO #", "funcExce(\'" + pathValue + "vlane\',\'#= ID #\');", "txt"));
                            }
                        }
                    }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
    }

    function getRandomId() {
        return (('FDSX' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
    };


    //提交
    var doComfirm = function () {
        var g = getGridSelectData(gridNameDGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var objectupmain = [];
        for (var i = 0; i < g.length; i++) {
            var mainid = g[i]["ID"];
            //主单状态
            objectupmain.push({
                "ID": mainid,
                "EX_RE_STATUS": "MCD已审核"
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var parammain = { "tableName": "EXE_TQQC_SHEET", "objects": objectupmain };
        putAddOrUpdata(urlsend, parammain, "是", "执行单状态更新:");
    }
    //撤回
    var doReturn = function () {
        var g = getGridSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["EX_RE_STATUS"] != "MCD已审核") {
                alertMsg("操作失败,所选记录“已完成”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "EX_RE_STATUS": "MCD待审核"
                });
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //调整
    var doChangMyTask = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请选择一条Lane记录进行操作!");
            return;
        } else if (arrIds.length > 1) {
            alertMsg("一次只允许对一条lane记录进行操作!");
            return;
        }

        elane(arrIds[0]);
    }
    //打印
    var putData1 = function (componentId) {
        var arrDatas = [];
        var columns = [];
        var exno = "";
        var extype = "";

        for (var i = 0; i < gridNameS2.length; i++) {
            if (i == 0) {
                columns = gridNameS2[i].getOptions().columns;
            }
            var arrSubDatas = getGridSelectData(gridNameS2[i]);
            if (arrSubDatas.length != 0) {
                arrDatas = arrDatas.concat(arrSubDatas);
            }
        }
        if (arrDatas.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        var htmlContent = printGridDataToHtml(
            {
                columns: columns,
                data: arrDatas,
                "expKey": "P",
                align: "center",
                title: "混lane任务调整审核-MCD2",
                printHeader: [{ "打印时间：": toDateFormatByZone(sysNowTimeFuncParams["sysNowTime"]), "制表人：": getLimsUser()["name"] }],
            });
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/settings/session/cache",
            ajaxData: { "html": htmlContent },
            succeed: function (result) {
                if (result["code"] > 0) {
                    var data = result["info"];
                    var html = data["html"];
                    openComponent({
                        name: "打印控件",//组件名称s
                        componentId: componentId,
                        params: { "html": html, "style": "style-01" }
                    });
                } else {
                    alertMsg(result["info"], "warning");
                }
            }
        });
    }


    //lane填写
    var doChangLane = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请选择Lane记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/seq/mcdpooling/task/editlane/editlane",
            title: "lane填写.."
        };
        openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
    }
    var vlane = function (LandId) {
        var winOpts = {
            url: "biomarker/seq/mcdpooling/task/vlane/vlane",
            title: "lane详细.."
        };
        openWindow(winOpts, { "LANE_ID": LandId });
    }

    var elane = function (LandId) {
        var winOpts = {
            url: "biomarker/seq/mcdpooling/task/elane/elane",
            title: "lane调整.."
        };
        openWindow(winOpts, { "LANE_ID": LandId });
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }

    }
    var doComfirmPE = function () {
        debugger;
        var g = getGridSelectData(gridNameD1Grid);
        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData:  { "query": "seq_NGS_lane_SHEET_list", "objects": [[g[0]["ID"]]] },
            succeed: function (rs) {
                rows = rs.rows;           
            }
        });
        var IDS=[];
        for(var i = 0;i<rows.length;i++){
            IDS.push(rows[i]["ID"]);
        }
     var    rowsMX;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData:  { "query": "queryDoMCDLanePoolMain", "objects": [IDS,["草稿","退回","待接收","已接收","已排单","已完成"]]},
            succeed: function (rs) {
                rowsMX = rs.rows;           
            }
        });
        var IDS2=[];
        for(var i = 0;i<rowsMX.length;i++){
            IDS2.push(rowsMX [i]["ID"]);
        }
      var   rowsMX2;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,

            ajaxData:  {"query":"seq_NGSMCD_lane_Pool_Lib_mx","objects":[],"search":{"POOL_ID":IDS2},"size":5000},
            succeed: function (rs) {
                rowsMX2= rs.rows;           
            }
        });
        var PEtoken;
        var PEVariable = { "ClientName": PE_ClientName, ClientPwd: ClientPwd };
        var inobjjson = { "url": "http://192.168.225.50/api/clientInfo/login", "PEVariable": PEVariable }
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                PEtoken = rs.apiData.result.token;
            }
        });

        var PEsamples = [];
        for (var j = 0; j < rowsMX2.length; j++) {
            PEsamples.push({
                "SourceBC":rowsMX2[j]["PCR_BOARD_CODE"],
                "SourceWell":rowsMX2[j]["PCR_PLAT_CELL"],
                "DestNo":"1",
                "DestBC":"1",
                "Vol1":rowsMX2[j]["LANE_MX_SAMPLE_VOL"]*1,


            });
        }
        time = sysNowTimeFuncParams["sysNowTime"];

        var PEVariable = {
            "TimeStamp": time,
            "Token": PEtoken,
            "Cmd":"Pooling",  
            "RQData": {
                "TaskNo": g[0]["EX_DH_NO"],
                "WorkList": PEsamples
            }
        };
        var inobjjson = { "url": "http://192.168.225.50/api/order/create", "PEVariable": PEVariable }
        var RValue;
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                RValue = rs;
            }
        });
        if (!RValue.apiData.success) {
            alertMsg(RValue.apiData.msg);
            return;
        }else{
            alertMsg("推送成功");
        }


    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "refreshGrid": refreshGrid,
        "doComfirmPE": doComfirmPE,
        "doComfirm": doComfirm,
        "doReturn": doReturn,
        "vlane": vlane,
        "elane": elane,
        "doChangMyTask": doChangMyTask,
        "doChangLane": doChangLane,
        "callBack": callBack,
        "putData1": putData1,
    });
});