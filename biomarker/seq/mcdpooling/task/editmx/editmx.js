$(document).ready(function() {
	
    var pathValue="biomarker-seq-mcdpooling-task-editmx-editmx";

    var paramsValue;
        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"BIO_LANE_MX"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
 
    var updataSet=function(){
    	//取出IDS
    	var ids=paramsValue["IDS"];
    	var jsonData = getJsonByForm("form",pathValue);//获取表单json
    	
    	 var object=[];
         for(var i=0;i < ids.length;i++){
        	 object.push($.extend({},jsonData,{"ID":ids[i]}));//表单值继承
         }
         
         
         //执行更新
         var params={"tableName":"BIO_LANE_MX","objects":object};

         //插入任务明细记录
         var url="system/jdbc/save/batch/table";
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:url,
             ajaxData:params,
             succeed:function(result){
                 
                 if(result["code"]>0){//成功保存后执行流程提交
                 	
                 	funcExce(pathValue+"pageCallBack");//父执行回调
                 
                 	console.log(result);
                 	
                  alertMsg("提交成功!");
                  
                  funcExce(pathValue+"close");//关闭页面
                 	
                 }else{
                 	console.log(result);
                 }
             }
         });
         
    	
    }
    
    
    funcPushs(pathValue,{
    	"initData":initData,
        "init":init,
        "updataSet":updataSet
    });
 
 });