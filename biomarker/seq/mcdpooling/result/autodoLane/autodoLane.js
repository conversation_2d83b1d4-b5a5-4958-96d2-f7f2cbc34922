$(document).ready(function () {
    var pathValue = "biomarker-seq-mcdpooling-result-autodoLane-autodoLane";
    var paramsValue;
    var gridNameDGrid;
    var ispass = 0;
    var initData = function () {
        return {
        };
    }
    var init = function (params) {
        paramsValue = params;
        gridNameDGrid = paramsValue["gridNameDGrid"];
    }
    var submit = function () {
        if (ispass == 0) {
            submit1();
        } else {
            alertMsg("提示:正在排lane耐心等待返回结果提示!", "wran");
            return;
        }
    }
    var submit1 = function () {
        debugger;
        var validator = $("#form" + pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) { } else {
            alertMsg("验证未通过", "wran");
            ispass = 0;
            return;
        }
        var p = getJsonByForm("form", pathValue);
        var arrIds = getSelectData(gridNameDGrid);

        if (arrIds.length == 0) {
            arrIds = getGridItemsData(gridNameDGrid);
            arrIds = getGridDataByName(arrIds);
        }
 
        var username = getLimsUser()["name"];
        var a = { "arrIds": arrIds, "configure": p , "username":username}

        var result;
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/autodoLane",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData:a,
            succeed: function (rs) {
                result = rs.result;
            }
        });
        

    }


    funcPushs(pathValue, {
        "init": init,
        "initData": initData,
        "submit": submit,
    });

});