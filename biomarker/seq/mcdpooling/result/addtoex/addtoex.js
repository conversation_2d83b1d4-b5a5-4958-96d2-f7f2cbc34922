$(document).ready(function() {
   var pathValue="biomarker-seq-mcdpooling-result-addtoex-addtoex";
   var paramsValue;
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
	   paramsValue=params;
	   var toolbar=getButtonTemplates(pathValue,[
           {name:"edit",target:"add",title:"确认选择"},
       ]);
        var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"queryDoMCDYPLane",
      	        "objects":[["MCD混样建库预排Lane"],
      	        		["草稿","调度退回"]]},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"queryDoMCDYPLaneMx","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }
        }
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
  }
  
   var add=function(){
	   
       var arrIds=getSelectData(gridNameGrid);
       if(arrIds.length==0){
          	alertMsg("请至少选择一条记录进行操作!");
          	return;
        }
       var objectadd=[];
        var objectMx=[];
        var objectupmx=[];
       for(var i=0;i<arrIds.length;i++){
    	   var ids=paramsValue["IDS"];     
    	   var indexS=paramsValue["indexS"];  
	  	   var time=sysNowTimeFuncParams["sysNowTime"];
	  	   var username=getLimsUser()["name"];
	  	   
	  	   for(var j=0;j < ids.length;j++){
	  		 var libid=getRandomId();
	       		objectadd.push({
	       			"ID":libid,
                        "TASK_LIB_MX_ID":ids[j],//联联任务ID
                         "LIBRARY_CODE": indexS[j]["LIB_LIBRARY_CODE"],//文库编号
               	 	"LANE_ID":arrIds[i],//关联执行单
	    	       	"INDEX_NAME":indexS[j]["INDEX_NAME"],//index
            		"SEQ_I7_NAME":indexS[j]["SEQ_I7_NAME"],//i7编号
            		"SEQ_I5_NAME":indexS[j]["SEQ_I5_NAME"],//i5编号
            		"SEQ_I7_1":indexS[j]["SEQ_I7_1"],//I7端
            		"SEQ_I5_1":indexS[j]["SEQ_I5_1"],//I5序列-V1.0
            		"SEQ_I5_2":indexS[j]["SEQ_I5_2"],//I5序列-V1.5
            		"SEQ_ODBY":indexS[j]["SEQ_ODBY"],//分组
            		"SEQ_CELL":indexS[j]["SEQ_CELL"],//对应孔位
               	 	"SYS_MAN":username,//实验员
               	 	"SYS_INSERTTIME":time//开始日期
		    	});
         		objectMx.push({//BIO_LANE_MX
					   "TASK_ID":indexS[j]["TASK_ID"],
					   "LANE_ID":arrIds[i],
					   "LANE_MX_CUSTOMER_TYPE":indexS[j]["LANE_MX_CUSTOMER_TYPE"],//项目等级
					   "LIBRARY_TYPE":indexS[j]["LIBRARY_TYPE"],//文库类型
					   "INDEX_NAME":indexS[j]["INDEX_NAME"],//index名称
					   "LIBRARY_CODE":indexS[j]["LIBRARY_CODE"],
					   "ISMCD":"是",
					   "SEQ_ID":ids[j],
					   "LIB_ID":libid,
					   "SEQ_LIBID":libid//排单池的文库ID,有可能是混库ID
			     });
                  objectupmx.push({
                    "ID": ids[j],
                    "MCD_PASS": "是"
                });
	        }
       	}
     var newUrl="system/jdbc/save/one/table/objects"; 
   	 var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
   	 putAddOrUpdata(newUrl,paramsadd,"是","推入下一步实验任务");
   	 
     var urlsend="system/jdbc/save/batch/table";
   	 var paramsMx={"tableName":"BIO_LANE_MX","objects":objectMx};
  	 putAddOrUpdata(urlsend,paramsMx,"否","推入下一步实验任务");
        var paramsupmx = {"tableName": "BIO_TASK_LIBMX", "objects": objectupmx};
        putAddOrUpdata(urlsend, paramsupmx, "否", "已排lane");
      
    } 
   function getRandomId() {
 	   return (('FDSX' || '-LANE-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
  };
   //批量执行插入
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		 refreshGrid();
              		funcExce(pathValue+"pageCallBack");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
  
   
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "add":add,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});