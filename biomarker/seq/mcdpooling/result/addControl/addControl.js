$(document).ready(function() {
    var pathValue="biomarker-seq-mcdpooling-result-addControl-addControl";
    var paramsValue;
    var initData=function(){
        return {};
    }
    var gridNameGrid;
    var init=function(params){
        paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"add",title:"确认选择"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDControlList","objects":[]},
        }
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }

    var add=function(){
        debugger;
        var arrIds=getGridSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var time=sysNowTimeFuncParams["sysNowTime"];
        var username=getLimsUser()["name"];
        
        
        var ids=paramsValue["IDS"];
        var objectadd=[];
        var objectMx=[];
        for(var i=0;i<ids.length;i++){

            for(var j=0;j < arrIds.length;j++){
                var libid=getRandomId();
                objectadd.push({
                    "ID":libid,
                    "TASK_LIB_MX_ID":arrIds[j]["LIBMXID"],//联联任务ID
                    "LANE_ID":ids[i],//关联执行单
                    "INDEX_NAME":arrIds[j]["INDEX_NAME_"],//index
                    "SEQ_I7_NAME":arrIds[j]["SEQ_I7_NAME_"],//i7编号
                    "SEQ_I5_NAME":arrIds[j]["SEQ_I5_NAME_"],//i5编号
                    "SEQ_I5_1":arrIds[j]["SEQ_I5_1_"],//I5序列-V1.0
                    "SYS_MAN":username,//实验员
                    "SYS_INSERTTIME":time//开始日期
                });
                objectMx.push({//BIO_LANE_MX
                    "TASK_ID":arrIds[j]["TASK_ID"],
                    "LANE_ID":ids[i],
                    "LANE_MX_CUSTOMER_TYPE":"6",//项目等级
                    "LIBRARY_TYPE":"MCD",//文库类型
                    "INDEX_NAME":arrIds[j]["INDEX_NAME_"],//index名称
                    "LIBRARY_CODE":"",
                    "ISMCD":"是",
                    "SEQ_ID":arrIds[j]["LIBMXID"],
                    "LIB_ID":libid,
                    "SEQ_LIBID":libid//排单池的文库ID,有可能是混库ID
                });
            }
            
        }
        var newUrl="system/jdbc/save/one/table/objects";
        var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
        putAddOrUpdata(newUrl,paramsadd,"是","推入下一步实验任务");

        var urlsend="system/jdbc/save/batch/table";
        var paramsMx={"tableName":"BIO_LANE_MX","objects":objectMx};
        putAddOrUpdata(urlsend,paramsMx,"否","推入下一步实验任务");

    }
    function getRandomId() {
        return (('FDSX' || '-LANE-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
    };
    //批量执行插入
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
                    if(isDoCallBack=="是"){
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                        funcExce(pathValue+"pageCallBack");
                    }
                }else{
                    alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }


    var callBack=function(){
        refreshGrid();
    };

    var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
    }

    funcPushs(pathValue,{
        "initData":initData,
        "init":init,
        "add":add,
        "refreshGrid":refreshGrid,
        "callBack":callBack,//回调方法
    });
});