$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-laneyp-index";
   var initData=function(){
       return {};
   }

   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryPoolingYpList","objects":[]},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
                var PLAT=e.data.TASK_LS_PLAT;
                var LENGTH=e.data.SEQ_LENGTH;
                var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: "",
                   height: 520,
                   read:{"query":"queryPoolingYpList_MX","objects":[]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
             }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }

  function getRandomId() {
      return  new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
   };

     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "refreshGrid":refreshGrid,
         "callBack":callBack,
     });
});