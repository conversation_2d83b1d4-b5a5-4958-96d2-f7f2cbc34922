$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-receive-doxx-doxx";
    var paramsValue;
    var gName;
    var dataApOf=[];
    var dataApObj=[];
    var isdo=0;
    var initData=function(){
        return {
        };
    }
    var init=function(params){
         paramsValue=params; 
         gName=paramsValue["gName"];
         $("#POOL_CODE"+pathValue).val(paramsValue["LANE_NO"][0]+"-");
    }
    
    var submit=function(){
    	var p=getJsonByForm("form",pathValue);
    	if(p["XX"]==""){
            alertMsg("请输入混样倍数!");
         	return;
        }
    	 var m=mask(pathValue,"正在计算,请稍等...");
        var arrIds=getGridSelectData(gName);
        var poolIds=[];
        var xx=p["XX"];
        for(var i=0;i<arrIds.length;i++){
        	//未设置pool的
     	   if(arrIds[i]["POOL_CODE"]==null || arrIds[i]["POOL_CODE"]==""){
     		   if(isdo==0){
	     		  poolIds.push({
	     			  "ID":arrIds[i]["ID"],
	     			  "LANE_MX_SAMPLE_RATIO":xx,
	     			  "LANE_MX_SAMPLE_VOL":xx*arrIds[i]["LANE_MX_SAMPLE_VOL"],
	     			 "LANE_MX_SAMPLE_VOL2":xx*arrIds[i]["LANE_MX_SAMPLE_VOL"]
	     		  });
     		   }else{
 	     		  poolIds.push({
	     			  "ID":arrIds[i]["ID"],
	     			  "LANE_MX_SAMPLE_RATIO":xx,
	     			  "LANE_MX_SAMPLE_VOL2":xx*arrIds[i]["LANE_MX_SAMPLE_VOL"]
	     		  });
     		   }
     	   }
        }

        if(poolIds.length>0){
        	var urlsend="system/jdbc/save/batch/table";
            var paramsup={"tableName":"BIO_LANE_MX","objects":poolIds};
      	    putAddOrUpdata(urlsend,paramsup,"是",m);
        }else{
        	alertMsg("未发现可形成pool的记录!");
         	return;
        }
    }

 var submit2=function(){

    	var p=getJsonByForm("form",pathValue);
    	 if(p["POOL_CODE"]==""){
                alertMsg("请输入Pool编号!");
         	return;
        }
      var m=mask(pathValue,"正在更新pooling,请稍等...");
     var arrIds=getGridSelectData(gName);
        var poolIds=[];
        var poolcode=p["POOL_CODE"];
        for(var i=0;i<arrIds.length;i++){
        	//换逄后体积大于1且未设置pool的
     	   if(arrIds[i]["LANE_MX_SAMPLE_VOL2"]>=1 && (arrIds[i]["POOL_CODE"]==null || arrIds[i]["POOL_CODE"]=="")){
     		  poolIds.push({
     			  "ID":arrIds[i]["ID"],
     			  "POOL_CODE":poolcode
     		  });
     	   }
        }

        if(poolIds.length>0){
        	isdo=1;
        	var urlsend="system/jdbc/save/batch/table";
            var paramsup={"tableName":"BIO_LANE_MX","objects":poolIds};
      	    putAddOrUpdata(urlsend,paramsup,"是",m);
        }else{
        	alertMsg("未发现可形成pool的记录!");
         	return;
        }
    }
 //获取安排数据量系统和上机系数
 var submit3=function(){
	 var m=mask(pathValue,"正在重置,请稍等...");
    var libtype=[];
    var arrTypes=getGridSelectData(gName);
    for(var i=0;i<arrTypes.length;i++){//获取涉及的文库类型组合
 	   if(arrTypes[i]["LIBRARY_TYPE"] && libtype.includes(arrTypes[i]["LIBRARY_TYPE"])==false){
 		   libtype.push(arrTypes[i]["LIBRARY_TYPE"]);
 	   }
    }
 	//所有的任务安排数据量系数和上机系数
    params={"query":"queryXsInfoAPSJ","objects":[libtype]};
	   $.fn.ajaxPost({
	        ajaxUrl:"system/jdbc/query/one/table",
	        ajaxType: "post",
	        ajaxData: params,
	        succeed:function(result){
	        	if(result["code"]>0){
	        		isdo=0;
	        		var rows=result["rows"];
	        		for(var i=0;i<rows.length;i++){
	        			row=rows[i];
		        		var n=dataApOf.indexOf(row["LIBRARY_TYPE"]);
			        	if(n>-1){
			        			dataApObj[n].push({
			        				"down":row["DATA_DOWN"],//下限
			        				"up":row["DATA_UP"],//上限
			        				"x1":row["SJ_COEFFIENT"],//安排系数值
			        			     "x2":row["SJ_SJCOEFFIENT"]//上机系数值
			        			});
			        	}else{//不存在
			        			dataApOf.push(row["LIBRARY_TYPE"]);
			        			var tempobj=[];
			        			tempobj.push({
				        				"down":row["DATA_DOWN"],//下限
				        				"up":row["DATA_UP"],//上限
				        				"x1":row["SJ_COEFFIENT"],//系数值
				        			    "x2":row["SJ_SJCOEFFIENT"]//上机系数值
				        			});
			        			dataApObj.push(tempobj);
			        	}
		        		
	        		}
	        		//执行刷新到列表
	        		doSetLaneMx(m);
	        	}
	        }
	   });
	   
 }
 /**
 1.取样体积计算：
 取样体积=混样比例/Qubit理论浓度
 混样倍数
 上机安排数据量2(M）=上机安排数据量1(M）*上机系数
 上机安排数据量1(M）=任务单本次-上机安排数据量(M）*安排系数
 Qubit理论浓度：=【Qubit*1515】/【2100或GX结果】
 **/
 var doSetLaneMx=function(m){

	    	if(dataApOf.length<0){
	    		alertMsg("提示:未发现上机系数维护信息!");
	         	return;
	    	}
	    	var upmx=[];
	        var arrTypes=getGridSelectData(gName);
	        for(var i=0;i<arrTypes.length;i++){//获取涉及的文库类型组合
	     	   if(arrTypes[i]["LIBRARY_TYPE"]){
	     		   var dataNumber=arrTypes[i]["LANE_MX_DATA_SUMTHE"];//本次安排量(M)
	     		   var dataNumber2=arrTypes[i]["LANE_MX_DATA_SUM2"];//上机安排数据量2(M)
	     		   //找到标准系数
	     		   var n=dataApOf.indexOf(arrTypes[i]["LIBRARY_TYPE"]);
	     		   if(n>=0){
	     			   var tempobj=dataApObj[n];
	     			   for(var k=0;k<tempobj.length;k++){//在数据量范围内
	     				   var obj=tempobj[k];
	     				   var a1=0;
	     				   var a2=0;
	     				   if((arrTypes[i]["QPCR_NM"]==0||arrTypes[i]["QPCR_NM"]==null)
	     						   ||arrTypes[i]["TASKZJ_L2100_SIZE"]==0||arrTypes[i]["TASKZJ_L2100_SIZE"]==null){
	     					   	alertMsg("提示:文库片段大小或qpcr(nM)浓度不能为空值!");
	   			         		return;
	     				   }else{
	     					  var x=arrTypes[i]["TASKZJ_QUBIT_ND"];//qubit 浓度
	     					  var nm=arrTypes[i]["QPCR_NM"];// QPCR(nM)
	     					  var y=arrTypes[i]["TASKZJ_L2100_SIZE"];//片段长度
	    					 
	     					  a2=x/y*1515;
	     					  a1=dataNumber*obj["x1"]*1/a2;
	     				   }
	     				   
	     				   if(obj["down"]<=dataNumber&&obj["up"]>=dataNumber){
								upmx.push({
									"ID":arrTypes[i]["ID"],
									"APXS":obj["x1"],//安排系数
									"POOL_CODE":"",
									"LANE_MX_DATA_SUM":dataNumber*obj["x1"],//上机安排数据量1(M）
									"SJXS":obj["x2"],//上机系数
									"LANE_MX_DATA_SUM2":dataNumber*obj["x1"]*obj["x2"],  //上机安排数据量2(M）
									"LANE_MX_SAMPLE_VOL":dataNumber2/nm,    //取样体积
									"LANE_MX_SAMPLE_RATIO":1,   // 混样倍数
									"LANE_MX_SAMPLE_VOL2":dataNumber2/nm,   //换算后取样体积
									"LIB_QUBIT_LLND":a2       //Qubit理论浓度
								});
								break;
							}//if

	     			   }//k
	     		   }//if 2
	     	  }//if1
	        }//i
	        
	       // k.TASKZJ_L2100_SIZE, -- {"title":"片段大小","width":150,"type":"number","exp":["A"]}
	       // k.TASKZJ_QUBIT_ND, -- {"title":"文库Qubit(ng/μl)","width":150,"type":"number","exp":["A"]}
	        debugger;
	        if(upmx.length>0){
	        	var urlsend="system/jdbc/save/batch/table";
	            var paramsup={"tableName":"BIO_LANE_MX","objects":upmx};
	      	    putAddOrUpdata(urlsend,paramsup,"是",m);
	        }else{
	        	alertMsg("未能成功更新安排系数值!");
	         	return;
	        }
}
	    

 
 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,m){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
               	 if(isDoCallBack=="是"){
               		alertMsg("提示:操作成功!");
               		gName.dataSource.read();
               	 }
                }else{
               	 alertMsg(errMsg+"操作失败!");
                }
                unmask(m);
            }
        
        });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "submit2":submit2,
        "submit3":submit3
    });
})