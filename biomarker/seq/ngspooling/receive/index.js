$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-receive-index";
   var initData=function(){
       return {};
   }

   var gridNameDGrid;
   var gridNameD1Grid;
   var gridNameD2Grid;
   var gridNameD3Grid;
   var gridNameS1=[];
   var gridNameS3=[];
   var gridNameS3_3=[];
   var gridNameS=[];
   //待接收
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"doReMyTask",title:"确认接收"},
            {name:"excel",target:"importData1",title:"实验导入/模板"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"pd_lane_SHEET_list","objects":[["NGSLane排单"],["待接收"]]},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list","objects":[ROW_ID]},
                    detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
                   detailInit: function (e) {
                   	var ROW_ID2 = e.data.ID;
                       var subGrid_N_JSON={
                           url: "system/jdbc/query/one/table",
                           sort: "",
                           toolbar: null,
                           height: 320,
                           read:{"query":"seq_NGS_lane_info_mx-3","objects":[],"search":{"LANE_ID":[ROW_ID2]}},
                       };
                       var subGrid_N2=initKendoGrid("#subGrid_"+ROW_ID2+"_"+pathValue,subGrid_N_JSON);
                       gridNameS1.push(subGrid_N2);
                   }//2
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
        init1();
        init3();
   }

   //lane调整
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	   {name:"edit",target:"doReMyTaskFF",title:"混Lane调整.."},
    	   {name:"comfirm",target:"doOK",title:"提交"}
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET_list","objects":[["NGSLane排单"],["已接收","待审核"]]},
           headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list","objects":[ROW_ID]},
                     detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
                   detailInit: function (e) {
                   	var ROW_ID2 = e.data.ID;
                       var subGrid_N_JSON={
                           url: "system/jdbc/query/one/table",
                           sort: "",
                           toolbar: null,
                           height: 320,
                           read:{"query":"seq_NGS_lane_info_mx-3","objects":[],"search":{"LANE_ID":[ROW_ID2]}},
                       };
                       var subGrid_N2=initKendoGrid("#subGrid_"+ROW_ID2+"_"+pathValue,subGrid_N_JSON);
                       gridNameS1.push(subGrid_N2);
                   }//2
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
  }
     //已提交审核
   var init3=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	  // {name:"edit",target:"doReMyTaskFF",title:"混Lane调整.."},
    	  // {name:"comfirm",target:"doOK",title:"提交"}
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET_list","objects":[["NGSLane排单"],["调整已审核"]]},
           headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"seq_NGS_lane_SHEET_list","objects":[ROW_ID]},
                     detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
                   detailInit: function (e) {
                   	var ROW_ID2 = e.data.ID;
                       var subGrid_N_JSON={
                           url: "system/jdbc/query/one/table",
                           sort: "",
                           toolbar: null,
                           height: 320,
                           read:{"query":"seq_NGS_lane_info_mx-3","objects":[],"search":{"LANE_ID":[ROW_ID2]}},
                       };
                       var subGrid_N3=initKendoGrid("#subGrid_"+ROW_ID2+"_"+pathValue,subGrid_N_JSON);
                       gridNameS3_3.push(subGrid_N3);
                   }//2
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS3.push(subGrid_N);
            }
       };
       gridNameD3Grid = initKendoGrid("#gridNameD3Grid"+pathValue,gridNameGridJson);
  }
   
 function getRandomId() {
    return (('FDSX' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
 };
  
//表格导入
var importData1 = function(componentId) {
	var arrIds = [];
	for (var i = 0; i < gridNameS.length; i++) {
		var arrSubID = getSelectData(gridNameS[i]);
		if (arrSubID.length != 0) {
			arrIds = arrIds.concat(arrSubID);
		}
	}
	if (arrIds.length == 0) {
		alertMsg("请至少选择一条样本记录进行操作!");
		return;
	}

	openComponent({
		name: "导入数据", //组件名称
		componentId: componentId,
		params: {
			template: function(p, n) {
				return exportAndImportData({
					expKey: "A",
					tableName: "",
					requestData: {
						ajaxData: {
							"query": "seq_NGS_lane_SHEET_list",
							"size": 5000,
							"objects": [],
							"search": {
								"ID": arrIds
							}
						},
					},
					params: p,
					name: n,
				});
			}
		},
		callBack: refreshGrid
	});
}
 //根据执行单ID获取明细信息
 var doReMyTask=function(){
		   var g=getGridSelectData(gridNameDGrid);  
	       if(g.length==0){
	           alertMsg("请至少选择一条数据进行接收操作!");
	           return ;
	       }
	       var objectupmain=[];
	       for(var i=0;i < g.length;i++){
	    	   var mainid=g[i]["ID"];
	    	   //主单状态
	    	   objectupmain.push({
    	       		"ID":mainid,
    	       		"EX_RE_STATUS":"已接收"
    	       	});
	    	 }
	       var urlsend="system/jdbc/save/batch/table";
           var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
    	   putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");     
 }
 
 //调整
 var doReMyTaskFF=function(){
     var arrIds=[];
     for(var i=0;i<gridNameS1.length;i++){
     	var arrSubID=getGridSelectData(gridNameS1[i]);
     	if(arrSubID.length!=0){
     		arrIds=arrIds.concat(arrSubID);
     	}
     }
     if(arrIds.length==0){
     	alertMsg("请选择一条Lane记录进行操作!");
     	return;
     }else if(arrIds.length>1){
    	 alertMsg("一次只允许对一条lane记录进行操作!");
      	return; 
     }
var laneid=[];
var laneno=[];
laneid.push(arrIds[0]["ID"]);
laneno.push(arrIds[0]["LANE_NO"]);
     
	 var winOpts={
	     url:"biomarker/seq/ngspooling/receive/chageLane/chageLane",
	     title:"混Lane调整.."
	  };
	  openWindow(winOpts,{"LANE_ID":laneid,"LANE_NO":laneno});
	 
 }
 //提交
 var doOK=function(){
	 var g=getGridSelectData(gridNameD1Grid);  
     if(g.length==0){
         alertMsg("请至少选择一条数据进行接收操作!");
         return ;
     }
     var objectupmain=[];
     for(var i=0;i < g.length;i++){
  	   var mainid=g[i]["ID"];
  	   //主单状态
  	   objectupmain.push({
	       		"ID":mainid,
	       		"EX_RE_STATUS":"待审核"
	       	});
  	 }
     var urlsend="system/jdbc/save/batch/table";
     var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
	   putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
 }
 
//提交
 var doComfirm=function(){
	 var g=getGridSelectData(gridNameD2Grid);  
     if(g.length==0){
         alertMsg("请至少选择一条数据进行接收操作!");
         return ;
     }
     var objectupmain=[];
     for(var i=0;i < g.length;i++){
  	   var mainid=g[i]["ID"];
  	   //主单状态
  	   objectupmain.push({
	       		"ID":mainid,
	       		"EX_RE_STATUS":"已完成"
	       	});
  	 }
     var urlsend="system/jdbc/save/batch/table";
     var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
	  putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
 }
 
 var vlane=function(LandId){
	 var winOpts={
		 url:"biomarker/seq/ngspooling/receive/vlane/vlane",
		 title:"lane详细.."
	 };
   openWindow(winOpts,{"LANE_ID":LandId});
 }
 
 var elane=function(LandId){
	 var winOpts={
		 url:"biomarker/seq/ngspooling/receive/elane/elane",
		 title:"lane详细.."
	 };
   openWindow(winOpts,{"LANE_ID":LandId});
 }
 //批量执行插入
 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
     $.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:urls,
         ajaxData:inobjjson,
         succeed:function(result){
             if(result["code"]>0){
            	 if(isDoCallBack=="是"){
            		 alertMsg("提示:操作成功!");
            		 refreshGrid();
            	 }
             }else{
            	 alertMsg(errMsg+"操作失败!");
             }
         }
     });
 }

     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
    	 gridNameS1=[];
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();
        }
        if(gridNameD2Grid){
        	gridNameD2Grid.dataSource.read();
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "refreshGrid":refreshGrid,
         "doReMyTask":doReMyTask,
         "doReMyTaskFF":doReMyTaskFF,
         "doOK":doOK,
         "doComfirm":doComfirm,
         "vlane":vlane,
         "elane":elane,
         "callBack":callBack,
         "importData1":importData1 
     });
});