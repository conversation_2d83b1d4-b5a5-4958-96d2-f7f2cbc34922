$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-receive-dxxx-dxxx";
    var paramsValue;
    var gName;
    var dataApOf=[];
    var dataApObj=[];
    var isdo=0;
    var initData=function(){
        return {
        };
    }
    var init=function(params){
         paramsValue=params; 
         gName=paramsValue["gName"];
         //$("#POOL_CODE"+pathValue).val(paramsValue["LANE_NO"][0]+"-");
    }
    var submit=function(){
    	var p=getJsonByForm("form",pathValue);
    	if(p["XX"]==""){
                alertMsg("请输入混样倍数!");
         	return;
        }
        var arrIds=getGridSelectData(gName);
        var poolIds=[];
        var xx=p["XX"];
        for(var i=0;i<arrIds.length;i++){
        	//未设置pool的
     	   if(arrIds[i]["POOL_CODE"]==null || arrIds[i]["POOL_CODE"]==""){
     		   if(isdo==0){
	     		  poolIds.push({
	     			  "ID":arrIds[i]["ID"],
	     			  "LANE_MX_SAMPLE_RATIO":xx,
	     			  "LANE_MX_SAMPLE_VOL":xx*arrIds[i]["LANE_MX_SAMPLE_VOL"],
	     			 "LANE_MX_SAMPLE_VOL2":xx*arrIds[i]["LANE_MX_SAMPLE_VOL"]
	     		  });
     		   }else{
 	     		  poolIds.push({
	     			  "ID":arrIds[i]["ID"],
	     			  "LANE_MX_SAMPLE_RATIO":xx,
	     			  "LANE_MX_SAMPLE_VOL2":xx*arrIds[i]["LANE_MX_SAMPLE_VOL"]
	     		  });
     		   }
     	   }
        }

        if(poolIds.length>0){
        	var urlsend="system/jdbc/save/batch/table";
            var paramsup={"tableName":"BIO_LANE_MX","objects":poolIds};
      	    putAddOrUpdata(urlsend,paramsup,"是","更新");
        }else{
        	alertMsg("未发现可形成pool的记录!");
         	return;
        }
    }
 
 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
               	 if(isDoCallBack=="是"){
               		alertMsg("提示:操作成功!");
               		gName.dataSource.read();
               	 }
                }else{
               	 alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
})