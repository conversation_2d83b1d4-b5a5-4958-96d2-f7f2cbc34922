$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-receive-doeditsj-doeditsj";
        /**
     * 初始化数据-无参
     */
debugger;
   var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_LANE_MX"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
     paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
 
 
    var submit=function(){

         var p=getJsonByForm("form",pathValue);
        var a=p["LANE_MX_DATA_SUM"];
        var b=p["SJXS"];
        var c=p["LANE_MX_SAMPLE_RATIO"];
        var Qubit=paramsValue["Qubit"];
       
        getInfo("form",pathValue,{"LANE_MX_DATA_SUM2":a*b});
        getInfo("form",pathValue,{"LANE_MX_SAMPLE_VOL":(a*b)/Qubit});
        getInfo("form",pathValue,{"LANE_MX_SAMPLE_VOL2":((a*b)/Qubit)*c});


        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });