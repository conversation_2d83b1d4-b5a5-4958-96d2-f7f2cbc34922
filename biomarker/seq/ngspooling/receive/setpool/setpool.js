$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-receive-setpool-setpool";
    var paramsValue;
    var gName;
    var initData=function(){
        return {
        };
    }
    var init=function(params){
         paramsValue=params; 
         gName=paramsValue["gName"];
    }
    var submit=function(){
        var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) {} else {
            //表单验证未通过
            alertMsg("验证未通过","wran");
            ispass=0;
            return ;
        }
    	var p=getJsonByForm("form",pathValue);
    	
     var arrIds=getGridSelectData(gName);
        var poolIds=[];
        var poolcode=p["POOL_CODE"];
        for(var i=0;i<arrIds.length;i++){
        	//换逄后体积大于1且未设置pool的
     	   if(arrIds[i]["LANE_MX_SAMPLE_VOL2"]>=1 && (arrIds[i]["POOL_CODE"]==null || arrIds[i]["POOL_CODE"]=="")){
     		  poolIds.push({
     			  "ID":arrIds[i]["ID"],
     			  "POOL_CODE":poolcode
     		  });
     	   }
        }

        if(poolIds.length>0){
        	var urlsend="system/jdbc/save/batch/table";
            var paramsup={"tableName":"BIO_LANE_MX","objects":poolIds};
      	    putAddOrUpdata(urlsend,paramsup,"是","更新");
        }else{
        	alertMsg("未发现可形成pool的记录!");
         	return;
        }
    }
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
               	 if(isDoCallBack=="是"){
               		alertMsg("提示:操作成功!");
               		gName.dataSource.read();
               	 }
                }else{
               	 alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
})