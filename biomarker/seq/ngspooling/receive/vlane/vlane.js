$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-receive-vlane-vlane";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var paramsValue;
   var init=function(params){
	    paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDLanePoolMain",
				 "objects":[[paramsValue["LANE_ID"]],["草稿","退回","待接收","已接收","已排单","已完成"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                         url: "system/jdbc/query/one/table",
                         sort: "",
                         toolbar: null,
                         height: 320,
                   read:{"query":"seq_NGS_lane_Pool_Lib_mx","objects":[],"search":{"POOL_ID":[ROW_ID]}},
                     };
                     var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                 }
         	 
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
   }
     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
     });
});