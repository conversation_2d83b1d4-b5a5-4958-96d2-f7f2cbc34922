$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-wspd-index";
   var initData=function(){
       return {};
   }
   var gridNameDGrid;
   var gridNameD1Grid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"edit",title:"指派实验员.."}
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"seq_NGS_lane_whsong_info","objects":[["待排单"],["否"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
            	var readsql="seq_NGS_lane_info_mx";
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read:{"query":readsql,"objects":[[ROW_ID]]},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
        init1();
   }
   
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"pd_lane_SHEET_list","objects":[["NGSWSLane排单"],["待接收","已接收"]]},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 500,
                   read:{"query":"seq_NGS_lane_SHEET_list","objects":[ROW_ID]},
                   detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
                   detailInit: function (e) {
                   	var ROW_ID = e.data.ID;
                   	var readsql="seq_NGS_lane_info_mx";
                       var subGrid_N_JSON={
                           url: "system/jdbc/query/one/table",
                           sort: "",
                           toolbar: null,
                           height: 300,
                           read:{"query":readsql,"objects":[[ROW_ID]]},
                       };
                       var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                   }
                   
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }           
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
  }

    var edit=function(){
       var arrIds=getSelectData(gridNameDGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行操作!");
            return ;
        }
        var winOpts={
            url:"biomarker/seq/ngspooling/wspd/pdup/pdup",
            title:"NGS排Lane.."
         };
        openWindow(winOpts,{"IDS":arrIds,"EX_TYPE":"NGSWSLane排单"});
    }
   
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "edit":edit,
         "refreshGrid":refreshGrid,
         "callBack":callBack,
     });
});