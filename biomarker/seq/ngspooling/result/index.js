$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-result-index";
   var initData=function(){
       return {};
   }
   var gridNameDGrid;
   var gridNameD1Grid;
    var gridNameS=[];
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"edit",title:"lane实验填写.."},
            //{name:"return",target:"doReturn",title:"退回"},
            {name:"submit",target:"doGo",title:"提交"},
           {name:"excel",target:"importData1",title:"实验导入/模板"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"seq_NGS_lane_Result_info","objects":[["NGSLane排单"],["调整已审核","已审核"],["已提交","已排单","撤回"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"queryDoLanePoolMain","objects":[[ROW_ID]]},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
            
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
        init1();
   }

   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
           // {name:"return",target:"doReturn",title:"撤回"}
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"seq_NGS_lane_Result_info","objects":[["NGSLane排单"],["调整已审核","已审核"],["已排RUN","已提交结果","上机已接收"]]},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"queryDoLanePoolMain","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
  }

    var edit=function(){
        var arrIds=getSelectData(gridNameDGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
        var winOpts={
                url:"biomarker/seq/ngspooling/result/upset/upset",
            title:"Lane结果.."
        };
        openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
     }
 
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();
        }
     }
//表格导入
var importData1 = function(componentId) {
	var arrIds = [];
	for (var i = 0; i < gridNameS.length; i++) {
		var arrSubID = getSelectData(gridNameS[i]);
		if (arrSubID.length != 0) {
			arrIds = arrIds.concat(arrSubID);
		}
	}
	if (arrIds.length == 0) {
		alertMsg("请至少选择一条样本记录进行操作!");
		return;
	}

	openComponent({
		name: "导入数据", //组件名称
		componentId: componentId,
		params: {
			template: function(p, n) {
				return exportAndImportData({
					expKey: "A",
					tableName: "BIO_LIB_POOLING",
					requestData: {
						ajaxData: {
							"query": "queryDoLanePoolMain",
							"size": 5000,
							"objects": [],
							"search": {
								"ID": arrIds
							}
						},
					},
					params: p,
					name: n,
				});
			}
		},
		callBack: refreshGrid
	});
}

     //确认提交结果
     var doGo=function(){
         var g=getGridSelectData(gridNameDGrid);
         if(g.length==0){
             alertMsg("请至少选择一条数据进行接收操作!");
             return ;
         }
         var objectup=[];
         var objecaddlib=[];
         var objectaddqc=[];

         var time=sysNowTimeFuncParams["sysNowTime"];
         var username=getLimsUser()["name"];
         var flag=0;
         for(var i=0;i < g.length;i++){
        	var is2100=g[i]["JK_TASKMX_L2100"];
        	var isqpcr=g[i]["JK_TASKMX_QPCR"];
        	var isqubit=g[i]["JK_TASKMX_QUBIT_IS"];
	  	    var qc_flag="否";//否/是/完
	  	    if(is2100=="是"||
	  	    		isqpcr=="是"||
	  	    		isqubit=="是"){
	  	    	
	  	       		qc_flag="是";
	  	       		flag=1;
	  	       		var libid=getRandomId();
	  	       		objecaddlib.push({//BIO_LIB_INFO
			       		"ID":libid,
			       		"LIBRARY_CODE":g[i]["LANE_NO"],
			       		"FROM_ID":g[i]["ID"],
			       		"FROM_TYPE":"混LANE",
			       		"JK_TASKMX_L2100":is2100,//是否片段检测
		  	       		"JK_TASKMX_QPCR":isqpcr,//是否QPCR检测
		  	       		"JK_TASKMX_QUBIT_IS":isqubit//是否QUBIT检测
			       	});
	  	       		objectaddqc.push({//BIO_LIB_QC_INFO
			       		"TASK_JK_ID":libid	       	
			      	});
	  	    	}
		  	    objectup.push({//BIO_LANE_INFO
		  	       		"ID":g[i]["ID"],
		  	       		"LANE_STATUS":"已提交结果",
		  	       		"QC_FLAG":qc_flag
		  	    });
         }
        //实验结果
        var paramsup={"tableName":"BIO_LANE_INFO","objects":objectup};
        var url="system/jdbc/save/batch/table";
     	putAddOrUpdata(url,paramsup,"是","更新lane");  
     	
     	if(flag==1){
     		var paramsup={"tableName":"BIO_LIB_QC_INFO","objects":objectaddqc};
         	putAddOrUpdata(url,paramsup,"是","生成QC记录");  
         	
         	var newUrl="system/jdbc/save/one/table/objects";
         	var paramsnainadd={"tableName":"BIO_LIB_INFO","objects":objecaddlib};
         	putAddOrUpdata(newUrl,paramsnainadd,"否","生成文库记录");
     	}     	
     }
     
  var doReturn=function(){
	 var g=getGridSelectData(gridNameDGrid);  
     if(g.length==0){
         alertMsg("请至少选择一条数据进行接收操作!");
         return ;
     }
     var objectupmain=[];
     for(var i=0;i < g.length;i++){
  	   var mainid=g[i]["ID"];
  	   //主单状态
  	   objectupmain.push({
	       		"ID":mainid,
	       		"EX_RE_STATUS":"实验退回"
	       	});
  	 }
     var urlsend="system/jdbc/save/batch/table";
     var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
	 putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
 }
 	function getRandomId() {
 		return (('FDSX' || '-LANE-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
     };
     //批量执行
     var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:urls,
             ajaxData:inobjjson,
             succeed:function(result){
                 if(result["code"]>0){
                	 if(isDoCallBack=="是"){
                		 alertMsg("提示:操作成功!");
                		 refreshGrid();
                	 }
                 }else{
                	 alertMsg(errMsg+"操作失败!");
                 }
             }
         });
     }
     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "edit":edit,
         "doGo":doGo,
         "doReturn":doReturn,
         "refreshGrid":refreshGrid,
         "callBack":callBack,
         "importData1":importData1 
     });
});