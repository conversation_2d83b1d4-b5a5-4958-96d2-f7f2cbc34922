$(document).ready(function () {
	var pathValue="biomarker-seq-ngspooling-lane-popup-popup"; 
	var paramsValue;
    var gridNameGrid;

	var init = function (params) {
		paramsValue = params;
		var toolbar = getButtonTemplates(pathValue, [
		]);
		var gridNameGridJson = {
			url: "berry/automation/sop/base",
			sort: "",
			height: fullh - 130,
			toolbar: toolbar,
			read: { "LANEID" : params["LANEID"] },
                                  headerFilter: function (cols, i) {
                if (i) {
                    if ( cols[i]["field"] != "LANE_NO" && cols[i]["field"] != "ID" &&cols[i]["field"] != "BASE" ) {
                        // setJsonParam(cols[i],"template",getTemplate("#= RELEASE_STATUS #","funcExce(\'"+pathValue+"updateStatus\',\'#= ID #\',\'#= RELEASE_STATUS #\');","txt"));
                        setJsonParam(cols[i], "template", function (dataItem) {
                            var ONE = dataItem["ONE"]; 
                            var A = ONE.replace('%','')
                            var templateInfo = '';
                            if (A > 60) {
                                templateInfo = '&nbsp;<font class="theme-m-txt" style="cursor:pointer;" > <span style="color:blue;">' + ONE + '</font>';
                            } else if (A < 10) {
                                templateInfo = '&nbsp;<font class="theme-m-txt" style="cursor:pointer;" ><span style="color:green;">' + ONE + '</font>';
                            } else {
                                templateInfo = '&nbsp;<font class="theme-m-txt" style="cursor:pointer;" ><span style="color:black;">' + ONE + '</font>';
                            }
                            return templateInfo;
                        });
                    }

                }
            }
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
	};

	var edit = function () {
		var gridData = getGridSelectData(gridNameGrid);
		var parmasup = [];
		parmasup.push({
			ID: paramsValue["ID"],
                        W_MATERIAL_DATE: gridData[0]["DVDATE"],
			W_MATERIAL_BATCH: gridData[0]["CBATCH"],
		});

		var url = "system/jdbc/save/batch/table";
		var paramsadd = {
			tableName: "BIO_BZ_MATERIEL_SOP_JL_MX",
			objects: parmasup,
		};
		putAddOrUpdata(url, paramsadd, "是", "");
	};

	//批量执行插入
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						alertMsg("提示:操作成功!",'success',close1());
					}
				} else {
					alertMsg(errMsg + "操作失败!",'error');
				}
			},
		});
	};

	var close1 = function () {
	//	funcExce(pathValue + "pageCallBack"); //执行回调
		funcExce(pathValue + "close"); //关闭页面
	};

    funcPushs(pathValue,{
        "init":init,
        "edit":edit,
        "close1":close1,
    });
});