$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-lane-dolane-dolane";
    var paramsValue;
    var gridNameDGrid;
    var ispass=0;
    var initData=function(){
        return {
        };
    }
    var init=function(params){
         paramsValue=params; 
         gridNameDGrid=paramsValue["gridNameDGrid"];
    }
    var submit=function(){
    	if(ispass==0){
    		submit1();
    	}else{
    		alertMsg("提示:正在排lane耐心等待返回结果提示!","wran");
            return ;
    	}
    }
    var submit1=function(){
        var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) {} else {
            //表单验证未通过
            alertMsg("验证未通过","wran");
            ispass=0;
            return ;
        }
    	var p=getJsonByForm("form",pathValue);
     	var arrIds=getGridSelectData(gridNameDGrid);
        if(arrIds.length==0){
           alertMsg("请至少选择一条数据进行修改!");
           ispass=0;
           return ;
        }
        //检测文库类型
       var type=["sRNA","circRNA","SLAF","Reseq","Trans","lnc","ATAC","CUT&tag","Hi-C","MCD","Denovo"];
       for(var i=0;i<arrIds.length;i++){
    	   if(type.indexOf(arrIds[i]["LIBRARY_TYPE"])<0){
    		   alertMsg("提示:操作失败,“"+arrIds[i]["LIBRARY_CODE"]+"”文库类型错误!");
               return ;
    	   }
    	   if(arrIds[i]["READSD"]<0){
    		   alertMsg("提示:操作失败,“"+arrIds[i]["LIBRARY_CODE"]+"”合同要求测序Reads数（M）值必须大于0!");
               return ;
    	   }
       }
        
        if(p["datatype"]=="Nova"){//Nova;Xten
        	ispass=1;
        	expInputfileNova();
        }else{
        	ispass=1;
        	expInputfileXten();
        }
    }
    var novaFileHeaderJson={
        "CUSTOMER_VIP":"客户等级",
        "DATENUM":"时间",
        "PROJECT_SUBNO":"项目期号",
        "LIBRARY_TYPE":"文库类型",
        "POOL_CODE":"文库编号",
        "LIBRARY_CODE":"子文库名称",
        "READSD":"合同要求测序Reads数（M）",
        "INDEX_NAME":"Index名称",
        "SEQ_I7_1":"I7端",
        "SEQ_I5_NAVA":"I5端",
        "TASK_LSMX_BIOSAM":"生物学重复编号",
        "LANE":"lane号",
        "ID":"唯一标识",
        "REASON":"原因"
    }
    var xtenFileHeaderJson={
        "CUSTOMER_VIP":"客户等级",
        "DATENUM":"时间",
        "PROJECT_SUBNO":"项目期号",
        "LIBRARY_TYPE":"文库类型",
        "POOL_CODE":"文库编号",
        "LIBRARY_CODE":"子文库名称",
        "READSD":"合同要求测序Reads数（M）",
        "INDEX_NAME":"Index名称",
        "SEQ_I7_1":"I7端",
        "SEQ_I5_XTEN":"I5端",
        "TASK_LSMX_BIOSAM":"生物学重复编号",
        "LANE":"lane号",
        "ID":"唯一标识",
        "REASON":"原因"
    }

     var fileTypeName="Nova";
     var dataScope ="";
     var inputfile;
     var outputfile;
     var outputfileName;
     var inputindex;
     
     var expInputfileNova=function(){
     	var arrDatas=getGridSelectData(gridNameDGrid);
         if(arrDatas.length==0){
        	 ispass=0;
             alertMsg("请至少选择一条数据进行文件生成操作!");
             return ;
         }
         console.log("arrDatas",arrDatas);
         var objects=[];
         var objectsHeader=[];
         for(var index in novaFileHeaderJson){
              objectsHeader.push(novaFileHeaderJson[index]);
         }
         objects.push(objectsHeader);

         var READSDsMax=0;
         var READSDsMin=0;
         for(var i=0;i<arrDatas.length;i++){
             var json=arrDatas[i];
             
             var objectsBody=[];
             for(var index in novaFileHeaderJson){
                  objectsBody.push(json[index]?json[index]:"");
             }
             objects.push(objectsBody);
             READSDsMax+=Math.ceil(json["READSD"]);
             READSDsMin+=Math.floor(json["READSD"]);
         }
         READSDsMax=READSDsMax+1
         READSDsMin=READSDsMin-1;
         dataScope =" --Novamax "+READSDsMin+"-"+READSDsMax;
         console.log("objects",objects);
           $.fn.ajaxPost({
              ajaxType:"post",
              ajaxUrl:"system/config/tools/write",
              ajaxData:{"fileName":"nova.txt","objects":objects},
              succeed:function(result){

                  if(result["code"]>0){
                          inputfile= result["filePath"];
                          outputfile=inputfile.replace(result["fileName"],'')+"out-"+result["fileName"];
                          outputfileName="out-"+result["fileName"];
                          fileTypeName="Nova";
                          execute();
                  }else{
                	  ispass=0;
                 	 alertMsg("提示:生成(Nova)文件操作失败!");
                  }
              }
          });
     }

     var expInputfileXten=function(){
     	var arrDatas=getGridSelectData(gridNameDGrid);
         if(arrDatas.length==0){
        	 ispass=0;
             alertMsg("请至少选择一条数据进行文件生成操作!");
             return ;
         }
         console.log("arrDatas",arrDatas);
         var objects=[];
         var objectsHeader=[];
         for(var index in xtenFileHeaderJson){
              objectsHeader.push(novaFileHeaderJson[index]);
         }
         objects.push(objectsHeader);

         var READSDsMax=0;
         var READSDsMin=0;
         for(var i=0;i<arrDatas.length;i++){
             var json=arrDatas[i];
             
             var objectsBody=[];
             for(var index in xtenFileHeaderJson){
                  objectsBody.push(json[index]?json[index]:"");
             }
             objects.push(objectsBody);
             READSDsMax+=Math.ceil(json["READSD"]);
             READSDsMin+=Math.floor(json["READSD"]);
         }
         READSDsMax=READSDsMax+1
         READSDsMin=READSDsMin-1;
         dataScope =" --Xtenmax "+READSDsMin+"-"+READSDsMax;
         console.log("objects",objects);
           $.fn.ajaxPost({
              ajaxType:"post",
              ajaxUrl:"system/config/tools/write",
              ajaxData:{"fileName":"nova.txt","objects":objects},
              succeed:function(result){
                  if(result["code"]>0){
                          inputfile= result["filePath"];
                          outputfile=inputfile.replace(result["fileName"],'')+"out-"+result["fileName"];
                          outputfileName="out-"+result["fileName"];
                          fileTypeName="Xten";
                          execute();
                  }else{
                	  ispass=0;
                 	 alertMsg("提示:成功(Xten)文件操作失败!");
                  }
              }
          });
     }
 //
 //"out-nova20210220222917165.txt"
 //outputfile
 //outputfileName
    var readFile=function(){
    	ispass=1;
           $.fn.ajaxPost({
              ajaxType:"post",
              ajaxUrl:"system/config/tools/read",
              ajaxData:{"fileName":outputfileName},//"out-nova20210220222917165.txt"
              succeed:function(result){
                  console.log(result);
                  if(result["code"]>0){
                 	var content=result["content"];
                         if(content){
                             if(content.length>1){//有内容-第一行是表头
                             	doToLane(content);  
                             }else{
                            	 ispass=0;
                                 alertMsg("读取文件无内容!极有可能是因为index重复导致!");
                             }
                         }
                  }else{
                	  ispass=0;
                 	 alertMsg("读取文件失败!");
                  }
              }
          });
    }
    
    var execute=function(){
          var formJson=getJsonByForm("form",pathValue);
          console.log("formJson=>",formJson);


          var datatype=formJson["datatype"];
          var READSDsMax=formJson["READSDsMax"];
          var READSDsMin=formJson["READSDsMin"];
          var indexmismatch=formJson["indexmismatch"];
          var srnamax=formJson["srnamax"];
          var slafmax=formJson["slafmax"];
          var circmax=formJson["circmax"];
          if(READSDsMax&&READSDsMin&&READSDsMax>READSDsMin){
               dataScope =" --"+datatype+"max "+READSDsMin+"-"+READSDsMax;
          }
          if(indexmismatch){
               dataScope +=" --indexmismatch "+indexmismatch;
          }
          if(srnamax){
               dataScope +=" --srnamax "+srnamax;
          }
          if(slafmax){
               dataScope +=" --slafmax "+slafmax;
          }
          if(circmax){
               dataScope +=" --circmax "+circmax;
          }
          $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:"system/config/tools/cmd",
             // ajaxData:{"command":"/data/app/biomarker/process_lane_main.py","param":" --inputfile xxx  --outputfile xxx  --datatype xxx --indexmismatch xxx --Novamax xxx  --Xtenmax xxx --srnamax xxx --circmax xxx --slafmax xxx"},
             ajaxData:{"command":"/data/app/biomarker/process_lane_main.py","param":" --inputfile "+inputfile+"  --outputfile "+outputfile+"  --datatype "+fileTypeName+dataScope },
             succeed:function(result){
                 if(result["code"]>0){
                	// alertMsg("提示:操作成功!");
                	readFile();
                 }else{
                	 ispass=0;
                	 alertMsg(errMsg+"操作失败!");
                 }
             }
         });
    }

    
  var doToLane=function(content){
	   ispass=0;
 	   var time=sysNowTimeFuncParams["sysNowTime"];
 	   var username=getLimsUser()["name"];
 	   var laneS=[];
 	   var laneNumS=[];
 	   var seqIds=[];
 	   var laneIds=[];

 	   var tempSeqId=[];
 	   //检测形成的lane号
 	   for(var i=1;i<content.length;i++){
           if(content[i][11]!=""&&content[i][11]!=null){
         	  if(laneS.includes(content[i][11])==false) {//分组后
         		  laneIds.push(getRandomId());
         		  laneS.push(content[i][11]);
         	  }
         	  tempSeqId.push(content[i][12]);
           }
        }
 	   //如果全部都没有lane的情况,需要刷原因
 	   if(laneIds.length==0){
 		   var isDoSeqs0=[];
 		   var objectUpSeq0=[];
 		    var flag0=0;
 		  for(var j=1;j<content.length;j++){ 
	 		  if(isDoSeqs0.indexOf(content[j][12])<0){//防止重复更新
	 			 flag0=1;
						objectUpSeq0.push({//BIO_SEQ_MOD
						 "ID":content[j][12],
						 "IS_LANE":"否",
						 "REASON":content[j][14]
					   });
						isDoSeqs0.push(content[j][12]); 
			 }
 		  }
 		  if(flag0==1){
	 		   var urlsend="system/jdbc/save/batch/table";
	     	   var paramsupSeq={"tableName":"BIO_SEQ_MOD","objects":objectUpSeq0};
	   	       putAddOrUpdata(urlsend,paramsupSeq,"是","全部记录均未排上lane,请调整参数后再执行");
 		  }else{
 			 alertMsg("操作成功,全部记录均未排上lane,请调整参数后再执!");
 		  }
 		   return;
 	   }
 	   
 		 var params={"query":"QueryNGSSeqTask","objects":[["1"]],"search":{"ID":tempSeqId}};
 		   $.fn.ajaxPost({
 		        ajaxUrl:"system/jdbc/query/one/table",
 		        ajaxType: "post",
 		        ajaxData: params,
 		        succeed:function(result){
 		             if(result["code"]>0){

 		          	   var rows=result["rows"];
 		          	   var libId=[];
 		          	   var seqlibIds=[];
 		          	   var keys=[];
 		          	   var taskids=[];
 		          	   var lanenums=[];
 		          	   var readsnums=[];
 		          	   var l2100Sizes=[];
 		          	   var qubitNds=[];
 		          	   for(var i=0;i<rows.length;i++){
 			        		var row=rows[i];
 			        		keys.push(row["ID"]+row["LIBRARY_CODE"]+row["INDEX_NAME"]);
 			        		taskids.push(row["TASKID"]);
 			        		libId.push(row["LIBID"]);
 		  	        		seqlibIds.push(row["SEQLIBID"]);
 		  	        		l2100Sizes.push(row["TASKZJ_L2100_SIZE"]);
 		  	        		qubitNds.push(row["TASKZJ_QUBIT_ND"]);
 		  	        		lanenums.push(0);
 		  	        		readsnums.push(0);
 			        	}
 		          	   var objectLane=[];
 			      	   var objectMx=[];
 			      	   var objectUpSeq=[];
 			      	   var isDoSeqs=[];
 			      	   var flag=0;
 			      	   var flag2=0;
 			      	   for(var i=0;i<laneIds.length;i++){
 			      		   for(var j=1;j<content.length;j++){
 			      	          if(content[j][11]!=""&&content[j][11]!=null&&laneS[i]==content[j][11]){//lane号相同
 			      	        	        var n=keys.indexOf(content[j][12]+content[j][5]+content[j][7]);
 			      	        	        if(n>-1){
 			      	        	        	flag=1;
 			      	        	        	lanenums[n]=lanenums[n]+1;
 			      	        	        	readsnums[n]=readsnums[n]+content[j][6];
 			      	        	        	
 			      	        	        	var llnd=0;//Qubit理论浓度：=【Qubit*1515】/【2100或GX结果】
 				        					if(l2100Sizes[n]==0||l2100Sizes[n]==null||l2100Sizes[n]==""){
 				        						 alertMsg("排lane失败!不能存在文库片段长度值为0或空值!");
 				        						 return;
 				        					}
 				        					llnd=qubitNds[n]*1515/l2100Sizes[n];
 				        					
 			      	        	        	objectMx.push({//BIO_LANE_MX
 				      						   "TASK_ID":taskids[n],
 				      						   "LANE_ID":laneIds[i],
 				      						   "LANE_MX_CUSTOMER_TYPE":content[j][0],//客户等级
 				      						   "LIBRARY_TYPE":content[j][3],//文库类型
 				      						   "INDEX_NAME":content[j][7],//index名称
 				      						   "POOL_CODE":content[j][4],
 				      						   "LIBRARY_CODE":content[j][5],
 				      						   "SEQ_ID":content[j][12],
 				      						   "LIB_ID":libId[n],
 				      						   "SEQ_LIBID":seqlibIds[n],//排单池的文库ID,有可能是混库ID
 				      						   "TASKZJ_L2100_SIZE":l2100Sizes[n],
 				      						   "TASKZJ_QUBIT_ND":qubitNds[n],
 				      						   "LIB_QUBIT_LLND":llnd//Qubit理论浓度
 				      					   });
 				        					if(isDoSeqs.indexOf(content[j][12])<0){//防止重复更新
	 				      	  					objectUpSeq.push({//BIO_SEQ_MOD
	 				      							 "ID":content[j][12],
	 				      							 "IS_LANE":"是",
	 				      							 "REASON":content[j][13]
	 				      						   });
	 				      	  					isDoSeqs.push(content[j][12]); 
 				        					}
 			      	        	        }
 			      	          }else{
 			      	        	  if(content[j][11]==""||content[j][11]==null){
 			      	        	     if(isDoSeqs.indexOf(content[j][12])<0){//防止重复更新
 			      	        		    flag2=1;
			      	  					objectUpSeq.push({//BIO_SEQ_MOD
			      							 "ID":content[j][12],
			      							 "IS_LANE":"否",
			      							 "REASON":content[j][13]
			      						   });
			      	  					isDoSeqs.push(content[j][12]); 
		        					}
 			      	        	  }
 			      	          }
 			      	       }
 			      		   //添加lane
 			      	   		  objectLane.push({//BIO_LANE_INFO
 			       	   			  "ID":laneIds[i],
 			       	   			  "LANE_NO":laneS[i],
 			       	   			  "LANE_SM_NUM":lanenums[i],
 			       	   			  "LANE_SEQ_READS":readsnums[i],
 			       	   			  "SYS_MAN":username,
 			       	   			  "SYS_INSERTTIME":time
 			       	   		  }); 
 			      	   }
 			      	if(flag==1){
 			      	     var newUrl="system/jdbc/save/one/table/objects";
 			      	     var paramsnainadd={"tableName":"BIO_LANE_INFO","objects":objectLane};
 			      	     putAddOrUpdata(newUrl,paramsnainadd,"否","添加LANE");
 			      	   	 
 			      	     var urlsend="system/jdbc/save/batch/table";
 			             var paramsup={"tableName":"BIO_LANE_MX","objects":objectMx};
 			      	     putAddOrUpdata(urlsend,paramsup,"否","添加lane明细");
 			      	     
 			             var paramsupSeq={"tableName":"BIO_SEQ_MOD","objects":objectUpSeq};
 			      	     putAddOrUpdata(urlsend,paramsupSeq,"是","lane成功!");
 		              }else if(flag2==1){
  			      	     var urlsend="system/jdbc/save/batch/table";
 		            	 var paramsupSeq={"tableName":"BIO_SEQ_MOD","objects":objectUpSeq};
 			      	     putAddOrUpdata(urlsend,paramsupSeq,"是","操作成功!");
 		              }else{
 		            	 alertMsg("提交失败!请联系管理员!");
 		              }
 		          	   
 		             }else{
 		            	 alertMsg("查询文库信息失败!");
 		             }
 		        }
 		   });
    }
    
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提示:"+errMsg+"!");
             		funcExce(pathValue+"pageCallBack");    
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
  function getRandomId() {
  	   return (('FDSX' || '-LANE-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
   };
    funcPushs(pathValue,{
        "init":init,
        "initData":initData,
        "submit":submit,
    });
 
 });