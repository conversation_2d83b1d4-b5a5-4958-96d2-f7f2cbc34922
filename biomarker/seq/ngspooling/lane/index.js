$(document).ready(function () {
    var pathValue = "biomarker-seq-ngspooling-lane-index";
    var paramsValue;
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    var mydata;
    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var gridNameD4Grid;
    var gridNameD5Grid;
    var gridNameS1 = [];
    //任务池
    var init = function (params) {
        paramsValue = params;
        var toolbar = getButtonTemplates(pathValue, [
            //{name:"add",target:"addlane",title:"生成pooling任务.."},
            { name: "add", target: "doLane", title: "自动排lane操作.." },
            { name: "add", target: "domyLane", title: "一键生成lane" },
            { name: "add", target: "domySplit", title: "拆分数据" },
            { name: "", target: "doPpIndex", title: "匹配index" },
            { name: "edit", target: "doEditIndex", title: "修改index" },
            { name: "edit", target: "addToEx", title: "追加任务.." },
            { name: "edit", target: "doTaskStatus", title: "状态修改.." },
            { name: "edit", target: "doSeqStop", title: "终止" },
            { name: "excel", target: "doLibToExcel", title: "实验导入/模板" },
            { name: "excel", target: "doLibToExcelYF", title: "研发修改" },
            { name: "excel", target: "doTaskToExcel", title: "研发导入模板" },
            { name: "edit", target: "unpdate", title: "修改数据量" },
            { name: "edit", target: "SLAFunpdate", title: "SLAF修改数据量" },
            //            {name:"edit",target:"doReturn3",title:"撤回"},
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            size: 1000,
            read: { "query": "QueryNGSSeqTask", "objects": [["1"]], "search": { "SLAF_BS": "1" } },
            fetch: function (data) {
                mydata = data;
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        init1();
        init2();
        init3();
        init4();
        init5();
    }
    //单号-草稿
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            //{name:"edit",target:"doUpset",title:"实验填写.."},
            { name: "excel", target: "importData1", title: "实验导入/模板" },
            //{name:"delete",target:"deleteLane",title:"删除lane"},
            { name: "submit", target: "dolaneno", title: "生成lane编号" },
            { name: "delete", target: "remove", title: "移除文库" },
            { name: "edit", target: "editA", title: "碱基平衡" },
            { name: "excel", target: "importData2", title: "数据量导入" },
            { name: "submit", target: "doGo", title: "提交" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_NGS_lane_info", "objects": [["草稿", "已排单", "排单退回"], ["否"], ["是", "否"]] },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "seq_NGS_lane_info_mx-3";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [], "search": { "LANE_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
    }
    //已处理
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_NGS_lane_info", "objects": [["待排单", "已排RUN", "待审核", "实验退回", "调整已审核", "已完成", "上机已接收"], ["否"], ["是", "否"]] },

            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "seq_NGS_lane_info_mx-2";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [], "search": { "LANE_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);
    }
    //已完成
    var init3 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "移至待排" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            pageable: { pageSizes: [10, 100, 1000, 2000, 3000, 5000, 100000] },
            read: { "query": "QueryNGSSeqTaskSTOP", "objects": [], "search": {} },
        };
        gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);
    }
    var init4 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [

        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            pageable: { pageSizes: [10, 100, 1000, 2000, 3000, 5000, 100000] },
            read: { "query": "QueryNGSSeqTaskList", "objects": [], "search": {} },
        };
        gridNameD4Grid = initKendoGrid("#gridNameD4Grid" + pathValue, gridNameGridJson);
    }

    //slaf检测index
    var init5 = function (params) {
        paramsValue = params;
        var toolbar = getButtonTemplates(pathValue, [
            { name: "add", target: "doToSlaf", title: "提交" },
            { name: "edit", target: "SLAFIndex", title: "SLAFindex冲突判断" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            size: 500,
            read: { "query": "QueryNGSSeqTask-SLAF", "objects": [["2"]], "search": { "LIBRARY_TYPE": "SLAF", "SLAF_BS": "2" } },
            fetch: function (data) {
                mydata = data;
            }
        };
        gridNameD5Grid = initKendoGrid("#gridNameD5Grid" + pathValue, gridNameGridJson);
    }
    var addlane = function () {
        alertMsg("功能暂停不开放!");
        return;
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        }
        var libids = [];
        var libcodes = [];
        var ispools = [];
        for (var i = 0; i < arrIds.length; i++) {
            libids.push(arrIds[i]["LIB_ID"]);
            ispools.push(arrIds[i]["ISPOOL"]);
            libcodes.push(arrIds[i]["LIBRARY_CODE"]);
        }
        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/addlane/addlane",
            title: "二代上机排单.."
        };
        openWindow(winOpts, { "libids": libids, "libcodes": libcodes, "ispools": ispools });
    }
    var doLane = function () {

        var arrIds = getSelectData(gridNameDGrid);

        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/dolane/dolane",
            title: "排单操作..",
            // width:1800,
            // height:1460,
            // position:{"top":150,"left":200}
        };
        openWindow(winOpts, { "gridNameDGrid": gridNameDGrid, "IDS": arrIds });
    }

    //追加任务
    var addToEx = function () {
        debugger;


        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var laneS = [];
        var laneIds = [];

        var lanenums = [];//lane文库数
        var bqsnums = [];//本期安排数据量
        var apsnums = [];//本次安排数据量

        var objectLane = [];
        var objectMx = [];
        var objectUpSeq = [];
        var objectUpSeq2 = [];//研发的
        var isDoSeqs = [];
        var flag = 0;
        var flag2 = 0;
        var flag3 = 0;
        var doflag = 0;
        var errMsg = "";
        var m = mask(pathValue, "正在追加lane,请稍等...");
        var lanedata = getGridItemsData(gridNameD1Grid);


        for (var i = 0; i < mydata.length; i++) {
            for (var j = 0; j < lanedata.length; j++) {
                if (mydata[i]["LANENO"] && lanedata[j]["LANE_NO"] == mydata[i]["LANENO"] && laneS.includes(mydata[i]["LANENO"]) == false) {//分组后
                    laneIds.push(lanedata[j]["ID"]);
                    laneS.push(mydata[i]["LANENO"]);
                    lanenums.push(lanedata[j]["LANE_SM_NUM"]);
                    bqsnums.push(lanedata[j]["THE_DATA_SUM"]);
                    apsnums.push(lanedata[j]["LANE_MX_DATA_SUMTHE"]);
                }
            }
        }

        if (laneS.length == 0) {
            alertMsg("未发现可追加数据!");
            unmask(m);
            return;
        }


        for (var i = 0; i < laneS.length; i++) {//lane
            var laneindex = [];
            //生成lane明细
            for (var j = 0; j < mydata.length; j++) {//遍历列表
                if (mydata[j]["LANENO"] && laneS[i] == mydata[j]["LANENO"]) {
                    var row = mydata[j];

                    flag = 1;
                    lanenums[i] = lanenums[i] + 1;
                    bqsnums[i] = bqsnums[i] + parseFloat(row["THE_DATA_SUM"]);//累计
                    apsnums[i] = apsnums[i] + parseFloat(row["READSD"]);//累计

                    var llnd = 0;//Qubit理论浓度：=【Qubit*1515】/【2100或GX结果】
                    if (parseFloat(row["TASKZJ_L2100_SIZE"]) <= 0 || parseFloat(row["TASKZJ_QUBIT_ND"]) <= 0) {
                        alertMsg("排lane失败!不能存在文库片段长度值为0或空值!");
                        unmask(m);
                        return;
                    }
                    llnd = parseFloat(row["TASKZJ_QUBIT_ND"]) * 1515 / parseFloat(row["TASKZJ_L2100_SIZE"]);


                    objectMx.push({//BIO_LANE_MX
                        "TASK_ID": row["TASK_ID"],
                        "LANE_ID": laneIds[i],
                        "LANE_MX_CUSTOMER_TYPE": row["CUSTOMER_VIP"],//客户等级
                        "LIBRARY_TYPE": row["LIBRARY_TYPE"],//文库类型
                        "INDEX_NAME": row["INDEX_NAME"],//index名称
                        "POOL_CODE": row["POOL_CODE"],
                        "LIBRARY_CODE": row["LIBRARY_CODE"],
                        "SEQ_ID": row["ID"],//排单池文库ID
                        "LIB_ID": row["LIBID"],//文库ID
                        "SEQ_LIBID": row["SEQLIBID"],//排单池的文库ID,有可能是混库ID
                        "TASKZJ_L2100_SIZE": row["TASKZJ_L2100_SIZE"],
                        "TASKZJ_QUBIT_ND": row["TASKZJ_QUBIT_ND"],
                        "LANE_MX_DATA_SUMTHE": row["READSD"],//本次安排数据量
                        "THE_DATA_SUM": row["THE_DATA_SUM"],//本期安排数据量
                        "LANE_MX_DATA_SUM": row["THD_SJ_DATA1"],//上机安排数据量1
                        "LIB_QUBIT_LLND": row["TASKZJ_QUBIT_LLND"]//Qubit理论浓度
                    });
                    //2)
                    if (row["YFA"] == "是") {//研发的
                        if (objectUpSeq2.indexOf(row["ID"]) < 0) {
                            flag3 = 1;
                            objectUpSeq2.push(row["ID"]);
                        }
                    } else {
                        if (isDoSeqs.indexOf(row["ID"]) < 0) {//防止重复更新
                            flag2 = 1;
                            objectUpSeq.push({//BIO_SEQ_MOD
                                "ID": row["ID"],
                                "IS_LANE": "是",
                                "ISOK": null//把加测的也置空
                            });
                            isDoSeqs.push(row["ID"]);
                        }
                    }


                    if (laneindex.includes(row["INDEX_NAME"]) == false) {
                        laneindex.push(row["INDEX_NAME"]);
                    } else {

                        doflag = 1;
                        if (errMsg == "") {
                            errMsg = "lane号“" + laneS[i] + "存在编号“" + row["LIBRARY_CODE"] + "”index冲突(“<font color=#ff0000>" + row["INDEX_NAME"] + "</font>”)”!</br>";
                        } else {
                            errMsg += "lane号“" + laneS[i] + "存在编号“" + row["LIBRARY_CODE"] + "”index冲突(“<font color=#ff0000>" + row["INDEX_NAME"] + "</font>”)”!</br>";
                        }
                    }


                }//end if
            }//end for j

        }
        if (flag == 1) {

            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectMx };
            putAddOrUpdata(urlsend, paramsup, "是", "添加lane明细");
            if (flag2 == 1) {
                var paramsupSeq = { "tableName": "BIO_SEQ_MOD", "objects": objectUpSeq };
                putAddOrUpdata(urlsend, paramsupSeq, "否", m);
            }
            //更新研发
            if (flag3 == 1) {
                var urlsend2 = "system/jdbc/update/one/table/where";
                var paramsupSeq2 = { "tableName": "BIO_RD_TASK_POOL", "IS_LANE": "是", "where": { "ID": objectUpSeq2 } }
                putAddOrUpdata(urlsend2, paramsupSeq2, "否", m);
            }
        } else if (flag2 == 1) {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsupSeq = { "tableName": "BIO_SEQ_MOD", "objects": objectUpSeq };
            putAddOrUpdata(urlsend, paramsupSeq, "是", m);
            //更新研发
            if (flag3 == 1) {
                var urlsend2 = "system/jdbc/update/one/table/where";
                var paramsupSeq2 = { "tableName": "BIO_RD_TASK_POOL", "IS_LANE": "是", "where": { "ID": objectUpSeq2 } }
                putAddOrUpdata(urlsend2, paramsupSeq2, "否", m);
            }
        } else {
            alertMsg("提交失败!请联系管理员!");
        }



    }
    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS1 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }
        if (gridNameD3Grid) {
            gridNameD3Grid.dataSource.read();
        }
    }
    var refreshGrid2 = function () {
        gridNameS1 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
    }

    //确认提交
    var doGo = function () {
        debugger

        var g = getGridSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }

        var alertDialog = $('<div class="dialog-box"></div>').kendoDialog({
            animation: { open: { effects: 'fade:in' }, close: { effects: 'fade:out' } },
            maxWidth: '30%',
            maxHeight: '30%',
            minWidth: 380,
            minHeight: 196,
            title: '全流程提交确认',
            content: '提示是否全流程？',
            actions: [
                {
                    text: '全流程提交',
                    primary: true,
                    action: function () {

                        alertMsgNoBtn('全流程提交！', 'info');
                        var RUN_IDS = getSelectData(gridNameD1Grid);
                        // 如果用户点击了“确定” 
                        $.fn.ajaxPost({
                            ajaxUrl: "function/system/settlement/nextNgs/runLCJH",
                            ajaxType: "post",
                            ajaxAsync: false,
                            ajaxData: { "RUN_IDS": RUN_IDS },
                            succeed: function (rs) {
                                if (rs["code"] > 0) {
                                    refreshGrid();
                                    alertMsg("提示:操作成功!");
                                } else {
                                    alertMsg(errMsg + "操作失败!");
                                }
                            }
                        });
                    }
                },
                {
                    text: '非全流程提交',
                    action: function () {
                        
                        alertMsgNoBtn('非全流程提交！', 'info');
                        var objectup = [];
                        var time = sysNowTimeFuncParams["sysNowTime"];
                        var username = getLimsUser()["name"];
                        for (var i = 0; i < g.length; i++) {
                            //更新记录
                            objectup.push({
                                "ID": g[i]["ID"],//联联任务ID
                                "LANE_STATUS": "待排单",
                                "SYS_INSERTTIME": time,
                                "SYS_MAN": username
                            });

                        }
                        var paramsup = { "tableName": "BIO_LANE_INFO", "objects": objectup };
                        var url = "system/jdbc/save/batch/table";
                        putAddOrUpdata(url, paramsup, "是", "提交");
                    }
                },
                {
                    text: '取消提交',
                    action: function () {
                        alertMsgNoBtn('取消提交！', 'info');
                    }
                }
            ],
            close: function () {
                alertDialog.destroy();
            }
        }).data('kendoDialog');
        alertDialog.open();

    }
    //确认提交
    var doToSlaf = function () {
        var g = getGridSelectData(gridNameD5Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var objectup = [];
        var objectyfup = [];
        for (var i = 0; i < g.length; i++) {
            var TASK_LSSQ_GODATA = g[i]["TASK_LSSQ_GODATA"] * 1;//合同数据量
            var TASK_LSSQ_SAMPLENUM = g[i]["TASK_LSSQ_SAMPLENUM"] - 1;//任务单样品数
            var POOL_LIB_NUM = g[i]["POOL_LIB_NUM"] - 1;//混库样品数
            var THE_DATA_SUM = (TASK_LSSQ_GODATA / TASK_LSSQ_SAMPLENUM) * POOL_LIB_NUM;         //本期数据量（M）=合同数据量（M）/（该酶切组合下总样本数-1）*（该混库下的样本数-1）
            //更新记录
            if (g[i]["YFA"] == "否") {
                objectup.push({
                    "ID": g[i]["TASK_MX_ID"],//联联任务ID
                    "THE_DATA_SUM": THE_DATA_SUM,
                    "SLAF_BS": "1",
                });
            } else {
                objectyfup.push({
                    "ID": g[i]["ID"],//联联任务ID
                    "SLAF_BS": "1",
                });
            }
        }
        if (objectup.length > 0) {
            var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objectup };
            var url = "system/jdbc/save/batch/table";
            putAddOrUpdata(url, paramsup, "是", "提交");
        }
        if (objectyfup.length > 0) {
            var paramsup = { "tableName": "BIO_RD_TASK_POOL", "objects": objectyfup };
            var url = "system/jdbc/save/batch/table";
            putAddOrUpdata(url, paramsup, "是", "提交");
        }
    }
    //任务单状态修改
    var doTaskStatus = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/uptaskstatus/uptaskstatus",
            title: "修改单状态.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    }
    //撤回
    var doReturn = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["LANE_STATUS"] != "待排单") {
                alertMsg("操作失败,所选记录存在已“已接收”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "LANE_STATUS": "草稿",
                });
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_LANE_INFO", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //删除
    var deleteLane = function () {

        var username = getLimsUser()["name"];
        if (username != "何进侯") {
            alertMsg("权限不足!");
            return;
        }

        var g = getSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }

        var params = { "tableName": "BIO_LANE_INFO", "ids": g };
        var url = "system/jdbc/delete/batch/table";
        deleteGridDataByIds(url, params, refreshGrid);


    }
    //记录移除
    var remove = function () {
        debugger;
        var arrg = [];
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            arrg = arrg.concat(arrSubID);
        }
        if (arrg.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        var ids = [];
        var seqIds = [];
        var seqIdsYF = [];
        for (var i = 0; i < arrg.length; i++) {
            ids.push(arrg[i]["ID"]);
            if (arrg[i]["YFA"] == "否") {
                seqIds.push({
                    "ID": arrg[i]["SEQ_ID"],
                    "MCD_PASS": "否",
                    "IS_LANE": "否",
                });
            } else {
                seqIdsYF.push({
                    "ID": arrg[i]["LIBID"],
                    "MCD_PASS": "否",
                    "IS_LANE": "否",
                });
            }
        }

        var params = { "tableName": "BIO_LANE_MX", "ids": ids };
        var url = "system/jdbc/delete/batch/table";
        deleteGridDataByIds(url, params, refreshGrid);



        var url = "system/jdbc/save/batch/table";
        if (seqIds.length > 0) {
            var params = { "tableName": "BIO_SEQ_MOD", "objects": seqIds };
            putAddOrUpdata(url, params, "否", "更新");
        }
        if (seqIdsYF.length > 0) {
            var params = { "tableName": "BIO_RD_TASK_POOL", "objects": seqIdsYF };
            putAddOrUpdata(url, params, "否", "更新");
        }


    }

    //实验填写
    var doUpset = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/upset/upset",
            title: "实验填写.."
        };
        openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
    }
    //修改index
    var doEditIndex = function (componentId) {


        var arrIds = getSelectData(gridNameDGrid, "LIBID");
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "E",
                        tableName: "BIO_LIB_INFO",
                        requestData: {
                            ajaxData: { "query": "QueryNGSSeqTask", "size": 10000, "objects": [["1"]], "search": { "LIBID": arrIds } },
                            // ajaxData:{"query":"QueryNGSSeqTask","size":10000,"objects":[]},
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid2
        });

    }

    //匹配index
    var doPpIndex = function () {
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请选择至少一条记录进行操作!");
            return;
        }
        var index5 = [];
        var index7 = [];
        for (var i = 0; i < arrIds.length; i++) {
            var rows1 = [];
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "QueryIndexList", "objects": [], "search": { "INDEX_NAME": [arrIds[i]["INDEX_NAME"]], "INDEX_TYPE": [arrIds[i]["LIBRARY_TYPE"]] } },
                succeed: function (rs) {
                    //console.log(rs);				
                    rows1 = rs["rows"];
                }
            });
            var row1 = [];
            if (rows1.length > 0) {
                var SEQ_I5_NAVA = rows1[0]["SEQ_I5_NAVA"];
                var SEQ_I7_1 = rows1[0]["SEQ_I7_1"];
                if (arrIds[i]["SEQ_PLAT_TEST"] == "illumina-1" || arrIds[i]["SEQ_PLAT_TEST"] == "BGI") {
                    SEQ_I5_NAVA = rows1[0]["SEQ_I5_BGI"];
                    SEQ_I7_1 = rows1[0]["SEQ_I7_BGI"];
                }
                index5.push({
                    "ID": arrIds[i]["LIBID"],
                    "SEQ_I5_NAVA": SEQ_I5_NAVA,
                    "SEQ_I7_1": SEQ_I7_1,
                    "SEQ_I5_XTEN": rows1[0]["SEQ_I5_XTEN"]
                });
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_LIB_INFO", "objects": index5 };
        putAddOrUpdata(urlsend, paramsup, "是", "匹配");

    }



    //表格导入
    var importData1 = function (componentId) {
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_LANE_MX",
                        requestData: {
                            ajaxData: { "query": "seq_NGS_lane_info_mx-3", "size": 5000, "objects": [], "search": { "ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }

    var doLibToExcel = function (componentId) {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_SEQ_MOD",
                        requestData: {
                            ajaxData: { "query": "QueryNGSSeqTask", "size": 10000, "objects": [["1"]], "search": { "ID": arrIds } },
                            // ajaxData:{"query":"QueryNGSSeqTask","size":10000,"objects":[]},
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid2
        });
    }
    //研发修改
    var doLibToExcelYF = function (componentId) {

        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "C",
                        tableName: "BIO_RD_TASK_POOL",
                        requestData: {
                            ajaxData: { "query": "QueryNGSSeqTask", "size": 10000, "objects": [["1"]], "search": { "ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid2
        });
    }
    //研发导入
    var doTaskToExcel = function (componentId) {
        var grid = gridNameDGrid;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                "template": function (p, n) {
                    if (n == "import") {
                        return {
                            template: grid.getOptions().columns,
                            expKey: "D",
                            tableName: "BIO_RD_TASK_POOL"
                        };
                    } else {
                        saveGridDataToExcel({ grid: gridNameDGrid, select: 1, expKey: "D" });
                    }
                },
                "succeed": function () {
                    refreshGrid();
                }
            },
        });
    }

    //lane号生成
    var dolaneno = function () {
        var yymmdd = toDateFormatByZone(new Date(), "yyyyMMdd")
        yymmdd = yymmdd.substring(2, yymmdd.length);
        //取出当天最大数
        var params = { "query": "QueryNGSSeqTaskLaneNo", "objects": [yymmdd] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    n = 1;
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        n = row["MAXNUMBER"] + 1;
                    }
                    var g = getSelectData(gridNameD1Grid);
                    if (g.length == 0) {
                        alertMsg("请至少选择一条记录进行操作!");
                        return;
                    }
                    var pasmsno = [];
                    for (var i = 0; i < g.length; i++) {
                        var laneno = "";
                        if (n < 10) {
                            laneno = yymmdd + "-0" + n;
                        } else {
                            laneno = yymmdd + "-" + n;
                        }
                        n++;

                        pasmsno.push({
                            "ID": g[i],
                            "LANE_NO": laneno
                        });
                    }
                    var params = { "tableName": "BIO_LANE_INFO", "objects": pasmsno };
                    var urlsend = "system/jdbc/save/batch/table";
                    putAddOrUpdata(urlsend, params, "是", "更新lane");
                }
            }
        });

    }
    //一键生成lane
    var domyLane = function () {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var laneS = [];
        var laneIds = [];

        var lanenums = [];//lane文库数
        var bqsnums = [];//本期安排数据量
        var apsnums = [];//本次安排数据量

        var objectLane = [];
        var objectMx = [];
        var objectUpSeq = [];
        var objectUpSeq2 = [];//研发的
        var isDoSeqs = [];
        var flag = 0;
        var flag2 = 0;
        var flag3 = 0;
        var doflag = 0;
        var errMsg = "";
        var m = mask(pathValue, "正在生成lane,请稍等...");
        for (var i = 0; i < mydata.length; i++) {
            if (mydata[i]["LANENO"] && laneS.includes(mydata[i]["LANENO"]) == false) {//分组后
                laneIds.push(getRandomId());
                laneS.push(mydata[i]["LANENO"]);
                lanenums.push(0);
                bqsnums.push(0);
                apsnums.push(0);
            }
        }
        for (var i = 0; i < laneS.length; i++) {//lane
            var laneindex = [];
            //生成lane明细
            for (var j = 0; j < mydata.length; j++) {//遍历列表
                if (mydata[j]["LANENO"] && laneS[i] == mydata[j]["LANENO"]) {
                    var row = mydata[j];

                    flag = 1;
                    lanenums[i] = lanenums[i] + 1;
                    bqsnums[i] = parseFloat(row["THE_DATA_SUM"]);//累计
                    apsnums[i] = parseFloat(row["READSD"]);//累计

                    var llnd = 0;//Qubit理论浓度：=【Qubit*1515】/【2100或GX结果】
                    if (parseFloat(row["TASKZJ_L2100_SIZE"]) <= 0 || parseFloat(row["TASKZJ_QUBIT_ND"]) <= 0) {
                        alertMsg("排lane失败!不能存在文库片段长度值为0或空值!");
                        unmask(m);
                        return;
                    }
                    llnd = parseFloat(row["TASKZJ_QUBIT_ND"]) * 1515 / parseFloat(row["TASKZJ_L2100_SIZE"]);


                    objectMx.push({//BIO_LANE_MX
                        "TASK_ID": row["TASK_ID"],
                        "LANE_ID": laneIds[i],
                        "LANE_MX_CUSTOMER_TYPE": row["CUSTOMER_VIP"],//客户等级
                        "LIBRARY_TYPE": row["LIBRARY_TYPE"],//文库类型
                        "INDEX_NAME": row["INDEX_NAME"],//index名称
                        "POOL_CODE": row["POOL_CODE"],
                        "LIBRARY_CODE": row["LIBRARY_CODE"],
                        "SEQ_ID": row["ID"],//排单池文库ID
                        "LIB_ID": row["LIBID"],//文库ID
                        "SEQ_LIBID": row["SEQLIBID"],//排单池的文库ID,有可能是混库ID
                        "TASKZJ_L2100_SIZE": row["TASKZJ_L2100_SIZE"],
                        "TASKZJ_QUBIT_ND": row["TASKZJ_QUBIT_ND"],
                        "LANE_MX_DATA_SUMTHE": row["READSD"],//本次安排数据量
                        "THE_DATA_SUM": row["THE_DATA_SUM"],//本期安排数据量
                        "LANE_MX_DATA_SUM": row["THD_SJ_DATA1"],//上机安排数据量1
                        "LIB_QUBIT_LLND": row["TASKZJ_QUBIT_LLND"]//Qubit理论浓度
                    });
                    //2)
                    if (row["YFA"] == "是") {//研发的
                        if (objectUpSeq2.indexOf(row["ID"]) < 0) {
                            flag3 = 1;
                            objectUpSeq2.push(row["ID"]);
                        }
                    } else {
                        if (isDoSeqs.indexOf(row["ID"]) < 0) {//防止重复更新
                            flag2 = 1;
                            objectUpSeq.push({//BIO_SEQ_MOD
                                "ID": row["ID"],
                                "IS_LANE": "是",
                                "ISOK": null//把加测的也置空
                            });
                            isDoSeqs.push(row["ID"]);
                        }
                    }


                    if (laneindex.includes(row["INDEX_NAME"]) == false) {
                        laneindex.push(row["INDEX_NAME"]);
                    } else {

                        doflag = 1;
                        if (errMsg == "") {
                            errMsg = "lane号“" + laneS[i] + "存在编号“" + row["LIBRARY_CODE"] + "”index冲突(“<font color=#ff0000>" + row["INDEX_NAME"] + "</font>”)”!</br>";
                        } else {
                            errMsg += "lane号“" + laneS[i] + "存在编号“" + row["LIBRARY_CODE"] + "”index冲突(“<font color=#ff0000>" + row["INDEX_NAME"] + "</font>”)”!</br>";
                        }
                    }


                }//end if
            }//end for j

            //添加lane
            objectLane.push({//BIO_LANE_INFO
                "ID": laneIds[i],
                "LANE_NO": laneS[i],
                "LANE_SM_NUM": lanenums[i],
                "LANE_SEQ_READS": bqsnums[i],
                "THE_DATA_SUM": apsnums[i],
                "SYS_MAN": username,
                "SYS_INSERTTIME": time
            });


        }//end for i
        if (doflag == 1) {
            //unmask(m);
            // alertMsg("提示:排lane失败!</br>"+errMsg);
            //return;
        }
        if (flag == 1) {
            var newUrl = "system/jdbc/save/one/table/objects";
            var paramsnainadd = { "tableName": "BIO_LANE_INFO", "objects": objectLane };
            putAddOrUpdata(newUrl, paramsnainadd, "否", "添加LANE");

            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectMx };
            putAddOrUpdata(urlsend, paramsup, "是", "添加lane明细");
            if (flag2 == 1) {
                var paramsupSeq = { "tableName": "BIO_SEQ_MOD", "objects": objectUpSeq };
                putAddOrUpdata(urlsend, paramsupSeq, "否", m);
            }
            //更新研发
            if (flag3 == 1) {
                var urlsend2 = "system/jdbc/update/one/table/where";
                var paramsupSeq2 = { "tableName": "BIO_RD_TASK_POOL", "IS_LANE": "是", "where": { "ID": objectUpSeq2 } }
                putAddOrUpdata(urlsend2, paramsupSeq2, "否", m);
            }
        } else if (flag2 == 1) {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsupSeq = { "tableName": "BIO_SEQ_MOD", "objects": objectUpSeq };
            putAddOrUpdata(urlsend, paramsupSeq, "是", m);
            //更新研发
            if (flag3 == 1) {
                var urlsend2 = "system/jdbc/update/one/table/where";
                var paramsupSeq2 = { "tableName": "BIO_RD_TASK_POOL", "IS_LANE": "是", "where": { "ID": objectUpSeq2 } }
                putAddOrUpdata(urlsend2, paramsupSeq2, "否", m);
            }
        } else {
            alertMsg("提交失败!请联系管理员!");
        }
    }
    var domySplit = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length != 1) {
            alertMsg("请选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/dosplit/dosplit",
            title: "数据量拆分填写.."
        };
        openWindow(winOpts, { "ID": arrIds[0] });
    }
    //终止
    var doSeqStop = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请选择记录进行操作!");
            return;
        }
        var object = [];

        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        for (var i = 0; i < arrIds.length; i++) {
            object.push({
                "ID": arrIds[i],
                "ISSTOP": "是",
                "SYS_MAN": username,
                "SYS_INSERTTIME": time
            })
        }

        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd = { "tableName": "BIO_SEQ_MOD", "objects": object };
        putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");

    }
    //撤回到待审核
    var doReturn3 = function () {
        var arrIds = getSelectData(gridNameDGrid, "BLQIID");
        if (arrIds.length == 0) {
            alertMsg("请选择记录进行操作!");
            return;
        }
        var BLIid = arrIds;
        var arrIdsID = getSelectData(gridNameDGrid, "ID");
        var object = [];
        var objectyf = [];
        var flag1 = 0;
        var flag2 = 0;
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var flagstr = "草稿";
        var flagstr2 = "已处理";
        for (var i = 0; i < BLIid.length; i++) {
            if (BLIid[i] == "" || BLIid[i] == null) {
                flag1 = 1;
                objectyf.push(arrIdsID[i]);
            } else {
                flag2 = 1;
                object.push({
                    "ID": BLIid[i],
                    "TASKZJ_ZJ_STATUS": flagstr,
                    "TASKZJ_L2100_BOOL": flagstr2,
                    "TASKZJ_QUBIT_BOOL": flagstr2,
                    "TASKZJ_QPCR_BOOL": flagstr2,
                    "TASKZJ_ZJ_MAN": username,
                    "TASKZJ_ZJ_DATE": time
                });
            }
        }
        //执行更新
        var url = "system/jdbc/delete/batch/table";
        if (flag1 == 1) {//研发的
            var paramsyf = { "tableName": "BIO_RD_TASK_POOL", "ids": objectyf };
            if (flag2 == 1) {
                deleteGridDataByIds(url, paramsyf, refreshGrid);
            } else {
                deleteGridDataByIds(url, paramsyf);
            }
        }
        if (flag2 == 1) {
            var params = { "tableName": "BIO_LIB_QC_INFO", "objects": object };
            //插入任务明细记录
            var url = "system/jdbc/save/batch/table";
            putAddOrUpdata(url, params, "否", "更新");
            var bsmID = getSelectData(gridNameDGrid, "ID");
            var params = { "tableName": "BIO_SEQ_MOD", "ids": bsmID };
            deleteGridDataByIds(url, params, refreshGrid);
        }

    }


    //移至待排
    var doReturn2 = function () {
        var arrIds = getSelectData(gridNameD3Grid);
        if (arrIds.length == 0) {
            alertMsg("请选择记录进行操作!");
            return;
        }
        var newUrl = "system/jdbc/update/one/table/where";
        var newrecode = { "tableName": "BIO_SEQ_MOD", "ISSTOP": "否", "where": { "ID": arrIds } };
        putAddOrUpdata(newUrl, newrecode, "刷新", "生成");
    }
    function getRandomId() {
        return 'FDSX-LANE-' + new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };
    //批量执行
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, m) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                unmask(m);
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        refreshGrid();

                        alertMsg("提示:操作成功!");
                    }
                    if (isDoCallBack == "刷新") {
                        refreshGrid();
                        alertMsg("提示:操作成功!");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    //修改数据量
    var unpdate = function () {
        var g = getGridSelectData(gridNameDGrid);
        if (g.length != 1) {
            alertMsg("请至选择一条记录进行操作!");
            return;
        }
        var THE_DATA_SUM = g[0]["THE_DATA_SUM"];
        var READSD = g[0]["READSD"];
        var THD_SJ_DATA1 = g[0]["THD_SJ_DATA1"];
        var bsmid = g[0]["ID"];
        var bttmid = g[0]["TASK_MX_ID"];
        var tqorjk = g[0]["TQORJK"];
        var yf = g[0]["YFA"];

        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/unpdate/unpdate",
            title: "修改数据量..",
            currUrl: replacePathValue(pathValue)
        };
        openWindow(winOpts, { "THE_DATA_SUM": THE_DATA_SUM, "READSD": READSD, "THD_SJ_DATA1": THD_SJ_DATA1, "bsmid": bsmid, "bttmid": bttmid, "tqorjk": tqorjk, "yf": yf });
    }

    //修改数据量
    var SLAFunpdate = function () {
        var g = getGridSelectData(gridNameDGrid);
        if (g.length != 1) {
            alertMsg("请至只选择一条记录进行操作!");
            return;
        }

        var LIBID = g[0]["LIBID"];
        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/SLAFunpdate/SLAFunpdate",
            title: "修改数据量SLAF..",
            currUrl: replacePathValue(pathValue)
        };

        openWindow(winOpts, { "LIBID": LIBID });
    }



    var SLAFIndex = function () {
        debugger;
        var g = getGridSelectData(gridNameD5Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }

        var params = { "SLAFIndex": [] };
        for (var i = 0; i < g.length; i++) {
            var LIBRARY_CODE = g[i]["LIBRARY_CODE"];
            var INDEX_NAME = g[i]["INDEX_NAME"];
            if (params.keys.indexOf(INDEX_NAME) < 0) {
                params.keys.push(INDEX_NAME);
                params[INDEX_NAME] = {
                    LIBRARY_CODES: []
                };
            }
            params[INDEX_NAME].LIBRARY_CODES.push(LIBRARY_CODE);
        }
        var str = "";
        for (var key in params) {
            if (params[key].length > 1) {
                str = str + params[key];

            }
        }
        if (str.size == 0) {

            alertMsg("所选文库INDEX无冲突!");
        } else {
            alertMsg("所选文库INDEX冲突!文库编号为【" + str + "】");
        }
    }



    //碱基平衡
    var editA = function () {
        debugger;
        var LANEID = getSelectData(gridNameD1Grid);
        if (LANEID.length < 1) {
            alertMsg("请至选择一条记录进行操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/seq/ngspooling/lane/popup/popup",
            title: "碱基平衡.."
        };
        openWindow(winOpts, { "LANEID": LANEID }); //传递

    }
    var importData2 = function (componentId) {

        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至选择一条记录进行操作!");
            return;
        }


        pass = 0;
        debugger;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_LANE_INFO",
                        requestData: {
                            ajaxData: { "query": "seq_NGS_lane_info", "size": 7000, "objects": [["草稿", "已排单", "排单退回"], ["否"], ["是", "否"]], "search": { "ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                },
                "import": function (info, importPathValue) {
                    var m = mask(importPathValue, "正在提交...");
                    unmask(m);
                    $.fn.ajaxPost({
                        ajaxUrl: "/berry/mcd/yf/importData2",//导入数据
                        ajaxType: "post",
                        ajaxData: { "info": info, "template": gridNameDGrid["columns"] },
                        succeed: function (results) {
                            if (results["code"] > 0) {
                                refreshGrid();
                                alertMsg("导入成功!", "success", funcExce(importPathValue + "close"));

                            } else {
                                alertMsg("导入失败!");
                            }

                        },
                        failed: function (result) {
                            unmask(m);
                            alertMsg(result["msg"], "error");
                        }
                    });

                }
            },
            callBack: refreshGrid
        });




    }


    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "editA": editA,
        "addlane": addlane,
        "addToEx": addToEx,
        "doUpset": doUpset,
        "doLane": doLane,
        "deleteLane": deleteLane,
        "remove": remove,
        "doTaskStatus": doTaskStatus,
        "doLibToExcel": doLibToExcel,
        "doTaskToExcel": doTaskToExcel,
        "doLibToExcelYF": doLibToExcelYF,
        "doGo": doGo,
        "doReturn": doReturn,
        "doReturn2": doReturn2,
        "doToSlaf": doToSlaf,
        "doReturn3": doReturn3,
        "refreshGrid": refreshGrid,
        "refreshGrid2": refreshGrid2,
        "callBack": callBack,
        "importData1": importData1,
        "dolaneno": dolaneno,
        "domyLane": domyLane,
        "domySplit": domySplit,
        "doSeqStop": doSeqStop,
        "SLAFunpdate": SLAFunpdate,
        "unpdate": unpdate,
        "doPpIndex": doPpIndex,
        "SLAFIndex": SLAFIndex,
        "doEditIndex": doEditIndex,
        "importData2": importData2,
    });
});