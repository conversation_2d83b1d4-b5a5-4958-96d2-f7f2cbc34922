$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-lane-addtoex-addtoex";
   var paramsValue;
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
	   paramsValue=params;
	   var toolbar=getButtonTemplates(pathValue,[
           {name:"edit",target:"add",title:"确认选择"},
       ]);
	   var gridNameGridJson={
	    		url: "system/jdbc/query/one/table",
              sort: "",
              toolbar: toolbar,
              read:{"query":"seq_NGS_lane_info","objects":[["草稿","排单退回"],["否"],["是","否"]]},
              detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
              detailInit: function (e) {
              	var ROW_ID = e.data.ID;
              	var readsql="seq_NGS_lane_info_mx";
                  var subGrid_N_JSON={
                      url: "system/jdbc/query/one/table",
                      sort: "",
                      toolbar: "",
                      height: 320,
                      read:{"query":readsql,"objects":[[ROW_ID]]},
                  };
                  var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
             }
	      };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
  }
  
   var add=function(){
	   
       var arrIds=getSelectData(gridNameGrid);
       if(arrIds.length==0){
          	alertMsg("请至少选择一条记录进行操作!");
          	return;
        }
       var objectadd=[];
       var libids=paramsValue["libids"];
   	   var libcodes=paramsValue["libcodes"];
   	   var ispools=paramsValue["ispools"];
   	   var time=sysNowTimeFuncParams["sysNowTime"];
	   var username=getLimsUser()["name"];
	   
       for(var i=0;i<arrIds.length;i++){
	  	   for(var j=0;j < libids.length;j++){
	       		objectadd.push({
	       			"LANE_ID":arrIds[i],
	        		"LIB_ID":libids[j],
	        		"LIBRARY_CODE":libcodes[j],
	        		"ISPOOL":ispools[j]
		    	});
	        }
       	}
        var urlsend="system/jdbc/save/batch/table";
   	 var paramsadd={"tableName":"BIO_LANE_MX","objects":objectadd};
   	 putAddOrUpdata(urlsend,paramsadd,"是","推入下一步实验任务");
      
    } 
    
   //批量执行插入
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		 refreshGrid();
              		funcExce(pathValue+"pageCallBack");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
  
   
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "add":add,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});