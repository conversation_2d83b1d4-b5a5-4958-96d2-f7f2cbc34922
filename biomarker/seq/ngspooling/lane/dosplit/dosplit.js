$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-lane-dosplit-dosplit";
	var totalSum=0;
	var paramsValue;

    var initData=function(){
        return {
            tableName:"BIO_SEQ_MOD"
        };
    }
    var init=function(params){
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        debugger;
        getInfo("form",pathValue,params,url,function(p,v){
        	debugger;
        	totalSum=Number($("#THE_DATA_APSUM"+pathValue).val());
        	$("#ISSPLIT"+pathValue).val("是");
        });
        
        
    }
 
 
    var submit=function(){
    	debugger;
        var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) {} else {
            //表单验证未通过
            alertMsg("验证未通过","wran");
            return ;
        }
        
    	var a1=Number($("#THE_DATA_APSUM"+pathValue).val());
    	var a2=Number($("#THE_DATA_APSUM2"+pathValue).val());
    	
    	if((a1+a2)!=totalSum){
    		alertMsg("提示:验证未通过,(原+新)数据量不等于拆分前值“<font color=#ff0000>"+totalSum+"</font>”","wran");
            return ;
    	}
    	
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    doRequset(paramsValue["ID"],a2);
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
    var doRequset=function(seqid,a2){
		 var time=sysNowTimeFuncParams["sysNowTime"];
	     var username=getLimsUser()["name"];
		 var params={"query":"queryBioSeqModInfo","objects":[[seqid]]};
		   $.fn.ajaxPost({
		        ajaxUrl:"system/jdbc/query/one/table",
		        ajaxType: "post",
		        ajaxData: params,
		        succeed:function(result){
		        if(result["code"]>0){
	  	        	var rows=result["rows"];
	  	        	var newrecode=[];
	  	        	for(var i=0;i<rows.length;i++){
	  	        		var row=rows[i];
	  	        		newrecode.push($.extend({}, row,{"ID":getRandomId(),
	  	        			"THE_DATA_APSUM":a2,
	  	        			"ISSPLIT":"是"
	  	        			}));
	  	        	}
	  	 	     var newUrl="system/jdbc/save/one/table/objects";
	  		     var paramsnainadd={"tableName":"BIO_SEQ_MOD","objects":newrecode};
	  		     putAddOrUpdata(newUrl,paramsnainadd,"是","生成");
		        }
		        }
		   });
   	 }
    
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
                	funcExce(pathValue+"pageCallBack");//执行回调
                	alertMsg("提示:拆分数据成功!");
                    funcExce(pathValue+"close");//关闭页面
                }else{
               	 alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }

    
    function getRandomId() {
   	   return 'SEQ-COPY-'+new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });