$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-wspdtask-pdup-pdup";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_WW_TASK"
        };
    }
    var init=function(params){
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
    }
     var submit=function(){       
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                     //提交成功
                     alertMsg("提交成功","success",function(){                    	 
                    	 var object=[];
                    	 var ids=paramsValue["IDS"];
                         for(var i=0;i < ids.length;i++){
                        	 object.push({"ID":ids[i],"WW_ID":result["ID"]});
                         }
                         var params={"tableName":"BIO_LANE_INFO","objects":object};
                         //插入任务明细记录
                         var url="system/jdbc/save/batch/table";
                         $.fn.ajaxPost({
                             ajaxType:"post",
                             ajaxUrl:url,
                             ajaxData:params,
                             succeed:function(result){
                                 if(result["code"]>0){
                                 	funcExce(pathValue+"pageCallBack");                                  
                                    funcExce(pathValue+"close");
                                 }else{
                                 	console.log(result);
                                 }
                             }
                         });
                     });
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });