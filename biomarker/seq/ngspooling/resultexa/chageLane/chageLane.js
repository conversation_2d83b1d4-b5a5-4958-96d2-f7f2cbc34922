$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-resultexa-chageLane-chageLane";
   var initData=function(){
       return {};
   }
   var paramsValue;
   var gridNameGrid;
   var gridName1Grid;
   var gridName2Grid;
   var gridNameS1=[];
   var gridNameS2=[];
   //待Pool
   var init=function(params){
	   paramsValue=params;
      var toolbar=getButtonTemplates(pathValue,[
           {name:"edit",target:"edit",title:"Pool.."},
           {name:"edit",target:"addToEx",title:"追加任务.."},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"seq_NGS_lane_info_mx",
        	   "objects":[paramsValue["LANE_ID"]]},
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
       init1();
       init2();
   }
 //Pool草稿
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	   {name:"ok",target:"doOK",title:"Pool提交"},
    	   {name:"delete",target:"remove",title:"明细移除"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"queryDoMCDLanePoolMain",
        	   "objects":[paramsValue["LANE_ID"],["草稿","退回"]]},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"seq_NGS_lane_Pool_Lib_mx","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
               gridNameS1.push(subGrid_N);
           }
       };
       gridName1Grid = initKendoGrid("#gridName1Grid"+pathValue,gridNameGridJson);
  }
   //已处理
   var init2=function(params){
	   var toolbar=getButtonTemplates(pathValue,[
		   {name:"ok",target:"doReturn",title:"撤回"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"queryDoMCDLanePoolMain",
        	   "objects":[paramsValue["LANE_ID"],["待接收","已接收"]]},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"seq_NGS_lane_Pool_Lib_mx","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }
       };
       gridName2Grid = initKendoGrid("#gridName2Grid"+pathValue,gridNameGridJson);
   }
  
    //排单生成
    var edit=function(){
 
    	var g=getGridSelectData(gridNameGrid);
        if(g.length==0){
        	alertMsg("请至少选择一条记录进行操作!");
        	return;
        }
        
        var ids=[];
        var laneid="";
        var libids=[];
        
        for(var i=0;i<g.length;i++){
        	ids.push(g[i]["ID"]);
        	libids.push(g[i]["LIB_ID"]);
        	laneid=g[i]["LANE_ID"];
        }
       var winOpts={
             url:"biomarker/seq/ngspooling/resultexa/pooling/pooling",
             title:"Pool..",
             currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"IDS":ids,"LIBIDS":libids,"LANE_ID":laneid});
     }
     
    
    //审核提交
    var doOK=function(){
    	  var arrIds=getSelectData(gridName1Grid);
          if(arrIds.length==0){
             	alertMsg("请至少选择一条记录进行操作!");
             	return;
           }
          var objectup=[];
          for(var i=0;i<arrIds.length;i++){
	   	  	   objectup.push({
	   	       		"ID":arrIds[i],//联联任务ID
	   		       	 "POOL_STATUS":"待接收"
	   		    });
          	}
         var urlsend="system/jdbc/save/batch/table";
      	 var paramsup={"tableName":"BIO_LIB_POOLING","objects":objectup};
      	 putAddOrUpdata(urlsend,paramsup,"是","提交");
    }
    //撤回
    var doReturn=function(){
    	var g=getGridSelectData(gridName2Grid);
        if(g.length==0){
           	alertMsg("请至少选择一条记录进行操作!");
           	return;
         }
        var objectup=[];
        for(var i=0;i<g.length;i++){
	   	  	if(g[i]["POOL_STATUS"]!="待接收"){  
	   	  		alertMsg("操作失败,所选记录存在已“已接收”状态!");
	   	  		return;
	   	  	}else{
		   	  	objectup.push({
	   	       		"ID":g[i]["ID"],
	   		       	 "POOL_STATUS":"草稿"
	   		    });
	   	  	}
        }
       var urlsend="system/jdbc/save/batch/table";
       var paramsup={"tableName":"BIO_LIB_POOLING","objects":objectup};
    	putAddOrUpdata(urlsend,paramsup,"是","提交");
    }
    //追加任务
    var addToEx=function(){
	   	var g=getGridSelectData(gridNameGrid);
        if(g.length==0){
        	alertMsg("请至少选择一条记录进行操作!");
        	return;
        }
        var ids=[];        
        for(var i=0;i<g.length;i++){
        	ids.push(g[i]["ID"]);
        }
       
       var winOpts={
    	   url:"biomarker/seq/ngspooling/resultexa/addtoex/addtoex",
	       title:"追加任务到Pool..",
	       currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"IDS":ids,"LANE_ID":paramsValue["LANE_ID"]});
	}
   
  //记录移除
   var remove=function(){
	   var arrg=[];
	  	 var arrIds=[];
	     for(var i=0;i<gridNameS1.length;i++){
	       	var arrSubID=getGridSelectData(gridNameS1[i]);
	       	arrg=arrg.concat(arrSubID);
	     }
	     if(arrg.length==0){
	        alertMsg("请至少选择一条数据进行操作!");
	        return ;
	      }
	       var objectup=[];
	       for(var i=0;i < arrg.length;i++){
	    	   objectup.push({
	   	       		"ID":arrg[i]["ID"],
	   		       	 "POOL_ID":null
	   		    });        
	       }
	       var urlsend="system/jdbc/save/batch/table";
	       var paramsup={"tableName":"BIO_LANE_MX","objects":objectup};
	       putAddOrUpdata(urlsend,paramsup,"是","提交");
	       
   }
   //批量执行插入
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		 refreshGrid();
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
   
     var callBack=function(){
        refreshGrid();
     };

    var refreshGrid=function(){
    	 gridNameS1=[];
    	 gridNameS2=[];
        if(gridNameGrid){
        	gridNameGrid.dataSource.read();
        }
        if(gridName1Grid){
        	gridName1Grid.dataSource.read();
        }
        if(gridName2Grid){
        	gridName2Grid.dataSource.read();
        }
     
     }
   
     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "edit":edit,
         "addToEx":addToEx,
         "doOK":doOK,
         "doReturn":doReturn,
         "remove":remove,
         "refreshGrid":refreshGrid,
         "callBack":callBack,
     });
});