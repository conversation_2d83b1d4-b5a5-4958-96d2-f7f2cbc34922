$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-resultexa-pooling-pooling";    
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_LIB_POOLING"
        };
    }
    var init=function(params){
    	
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        $("#POOL_TYPE"+pathValue).val("NGS混LANE建库");
        $("#POOL_LIB_NUM"+pathValue).val(paramsValue["IDS"].length);
        
        
    }
 
  var subUpData=function(){
	   var time=sysNowTimeFuncParams["sysNowTime"];
	   var username=getLimsUser()["name"];
	   var poollibcode= $("#POOL_CODE"+pathValue).val();
         //插入执行主单
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                     alertMsg("提交成功,生成的草稿,请前往审核提交!","success",function(){
                    	var ids=paramsValue["IDS"];
                      	var laneid=paramsValue["LANE_ID"];
                      	var libids=paramsValue["LIBIDS"];
                     	var objectaddlane=[];
                     	var objectupmx=[];
                     	
                     	//添加到lane 明细
                     	objectaddlane.push({//BIO_LANE_MX
                     		"LIB_ID":result["ID"],
                     		"LANE_ID":laneid,//关联ID
                     		"POOL_ID":"",
                         	"LIBRARY_CODE":poollibcode,//文库编号                     	
                         	"ISMCD":"否",//为MCD库
                         	"ISPOOL":"是",
                                 "ISSEL":"-1"
                     	});
                     	
                     	  //更新文库
                          for(var i=0;i < ids.length;i++){//BIO_LIB_INFO
                        	  objectupmx.push({
                         			"ID":ids[i],
               	    	       		"POOL_ID":result["ID"]
               	    	       	});
                          }
                         //执行更新到文库
                         var urlsend="system/jdbc/save/batch/table";
          	        	 //添加到lane 明细
          	        	 var paramsaddlane={"tableName":"BIO_LANE_MX","objects":objectaddlane};
          	        	 putAddOrUpdata(urlsend,paramsaddlane,"是","添加Pool到明细");
          	        	 var paramsUP={"tableName":"BIO_LANE_MX","objects":objectupmx};
          	        	 putAddOrUpdata(urlsend,paramsUP,"是","更新原有明细");
                     });
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });

    }
  
  function getRandomId() {
	    return (('FDSX-pool-' || 'NGS-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
  };
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		funcExce(pathValue+"pageCallBack");
                    funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 
    var submit=function(){
    	subUpData();
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "subUpData":subUpData
    });
 
 });