$(document).ready(function () {
    var pathValue = "biomarker-seq-ngspooling-resultexa-index";
    var initData = function () {
        return {};
    }

    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameS1 = [];
    var gridNameS2 = [];

    //待Pool
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doReMyTaskFF", title: "实验填写.." },
            { name: "comfirm", target: "doOK", title: "提交" },
            { name: "comfirm", target: "pushPE", title: "提交PE" },
            // {name:"excel",target:"importData1",title:"实验导入/模板"},
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "pd_lane_SHEET_list", "objects": [["NGSLane排单"], ["待审核", "实验退回"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "seq_NGS_lane_SHEET_list", "objects": [ROW_ID] },
                    headerFilter: function (cols, i) {
                        if (i) {
                            if (cols[i]["field"] && cols[i]["field"] == "LANE_NO") {
                                setJsonParam(cols[i], "template", getTemplate("#= LANE_NO #", "funcExce(\'" + pathValue + "elane\',\'#= ID #\');", "txt"));
                            }
                        }
                    }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
        init2();
    }
    //已审核
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "excel", target: "PEExpData", title: "PE表实验导出" },
            { name: "excel", target: "QYExpData", title: "取样表实验导出" },
            { name: "excel", target: "SGExpData", title: "手工实验导出" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "pd_lane_SHEET_list", "objects": [["NGSLane排单"], ["调整已审核", "已审核"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "seq_NGS_lane_SHEET_list", "objects": [ROW_ID] },
                    headerFilter: function (cols, i) {
                        if (i) {
                            if (cols[i]["field"] && cols[i]["field"] == "LANE_NO") {
                                setJsonParam(cols[i], "template", getTemplate("#= LANE_NO #", "funcExce(\'" + pathValue + "vlane\',\'#= ID #\');", "txt"));
                            }
                        }
                    }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);
    }


    function getRandomId() {
        return (('FDSX' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
    };

    //调整
    var doReMyTaskFF = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请选择一条Lane记录进行操作!");
            return;
        } else if (arrIds.length > 1) {
            alertMsg("一次只允许对一条lane记录进行操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/seq/ngspooling/resultexa/elane/elane",
            title: "混Lane调整.."
        };
        openWindow(winOpts, { "LANE_ID": arrIds[0] });

    }
    //表格导入
    var importData1 = function (componentId) {
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        openComponent({
            name: "导入数据", //组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "",
                        requestData: {
                            ajaxData: {
                                "query": "seq_NGS_lane_SHEET_list",
                                "size": 5000,
                                "objects": [],
                                "search": {
                                    "ID": arrIds
                                }
                            },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }
    //提交
    var doOK = function () {
        var g = getGridSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var objectupmain = [];
        for (var i = 0; i < g.length; i++) {
            var mainid = g[i]["ID"];
            //主单状态
            objectupmain.push({
                "ID": mainid,
                "EX_RE_STATUS": "调整已审核"
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var parammain = { "tableName": "EXE_TQQC_SHEET", "objects": objectupmain };
        putAddOrUpdata(urlsend, parammain, "是", "执行单状态更新:");
    }

    //提交
    var doComfirm = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var objectupmain = [];
        for (var i = 0; i < g.length; i++) {
            var mainid = g[i]["ID"];
            //主单状态
            objectupmain.push({
                "ID": mainid,
                "EX_RE_STATUS": "已完成"
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var parammain = { "tableName": "EXE_TQQC_SHEET", "objects": objectupmain };
        putAddOrUpdata(urlsend, parammain, "是", "执行单状态更新:");
    }

    var vlane = function (LandId) {
        var winOpts = {
            url: "biomarker/seq/ngspooling/resultexa/vlane/vlane",
            title: "lane详细.."
        };
        openWindow(winOpts, { "LANE_ID": LandId });
    }

    var elane = function (LandId) {
        var winOpts = {
            url: "biomarker/seq/ngspooling/resultexa/elane/elane",
            title: "实验填写.."
        };
        openWindow(winOpts, { "LANE_ID": LandId });
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS1 = [];

        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }

    }



    var pushPE = function () {
        debugger;
        var arrIds = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条Lane记录进行操作!");
            return;
        }

        var g = getGridSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条执行单进行操作!");
            return;
        }


        var sample = [];
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "seq_NGS_lane_Pool_Lib_mx", size: 1000, "search": { "LANE_ID": arrIds, "PE": 1 } },
            succeed: function (rs) {
                sample = rs.rows;             //样品
            }
        });

        var PEtoken;
        var PEVariable = { "account": "lims", password: "123456" };
        var inobjjson = { "url": "http://192.168.221.79:8001/api/ClientInfo/login", "PEVariable": PEVariable }
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                PEtoken = rs.apiData.result.data.token;
            }
        });



        var PEsamples = [];
        for (var j = 0; j < sample.length; j++) {
            PEsamples.push({
                "SourceBarcode": sample[j]["SOURCE_PLATE"],
                "SourceWell": sample[j]["SOURCE_WELL"],
                "DestinationBarcode": sample[j]["TARGET_CODE"],
                "DestinationWell": sample[j]["TARGET_WELL"],
                "Volume": sample[j]["LLTJ_ECHO_LIBRARY"]
            });

        }

        time = sysNowTimeFuncParams["sysNowTime"];
        var PEVariable = {
            "Token": PEtoken,
            "OrderType": "PostqPCR",
            "TaskNo": g[0]["EX_DH_NO"],
            "data": {
                "WorkList": PEsamples
            }
        };
        var inobjjson = { "url": "http://192.168.221.79:8001/api/orderInfo/create", "PEVariable": PEVariable }
        var RValue;
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                RValue = rs;
            }
        });

        if (!RValue.apiData.success) {
            alertMsg(RValue.apiData.msg);
        } else {
            alertMsg("推送成功！");
        }
    }


     
    //PE表实验导出
    var PEExpData = function () { debugger
        var objects = getSelectData(gridNameD2Grid);
        var arrIds = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }

        if (objects.length == 0 && arrIds.length == 0  ) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var search={};
        if(objects.length != 0){
            search={
                "EXE_TQQC_ID": objects,"EXECUTION_TYPE":"自动"
            }
        }
        if(arrIds!=0){
            search={
                "LANE_ID": arrIds,"EXECUTION_TYPE":"自动"
            }
        }


 
        saveResultDataToExcel({
            requestData: {
                ajaxData: {
                    "query": "seq_NGS_lane_info_mx_SGPEExpData" ,  search 
                },
            }
        },
            { "expKey": "B" }
        );

    }
 
    //取样表实验导出
    var QYExpData = function () {
        var objects = getSelectData(gridNameD2Grid);
        var arrIds = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }

        if (objects.length == 0 && arrIds.length == 0  ) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var search={};
        if(objects.length != 0){
            search={
                "EXE_TQQC_ID": objects
            }
        }
        if(arrIds!=0){
            search={
                "LANE_ID": arrIds
            }
        }

 
        saveResultDataToExcel({
            requestData: {
                ajaxData: {
                    "query": "seq_NGS_lane_info_mx_QYExpData", search
                },
            }
        },
            { "expKey": "C" }
        );

    }

    //手工实验导出
    var SGExpData = function () {
        var objects = getSelectData(gridNameD2Grid);
        var arrIds = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }

        if (objects.length == 0 && arrIds.length == 0  ) {
            alertMsg("请至少选择一条数据进行接收操作!");
            return;
        }
        var search={};
        if(objects.length != 0){
            search={
                "EXE_TQQC_ID": objects,"EXECUTION_TYPE":"手动"
            }
        }
        if(arrIds!=0){
            search={
                "LANE_ID": arrIds,"EXECUTION_TYPE":"手动"
            }
        }

 
        saveResultDataToExcel({
            requestData: {
                ajaxData: {
                    "query": "seq_NGS_lane_info_mx_SGPEExpData", search
                },
            }
        },
            { "expKey": "A" }
        );

    }



    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "refreshGrid": refreshGrid,
        "pushPE": pushPE,
        "doReMyTaskFF": doReMyTaskFF,
        "SGExpData":SGExpData,
        "QYExpData":QYExpData,
        "PEExpData":PEExpData,
        "doOK": doOK,
        "doComfirm": doComfirm,
        "vlane": vlane,
        "elane": elane,
        "callBack": callBack,
        "importData1": importData1
    });
});