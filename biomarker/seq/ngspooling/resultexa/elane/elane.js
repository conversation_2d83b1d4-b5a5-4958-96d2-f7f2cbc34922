$(document).ready(function() {
   var pathValue="biomarker-seq-ngspooling-resultexa-elane-elane";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var paramsValue;
   var init=function(params){
	    paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
             {name:"ok",target:"doPool",title:"修改Pool信息"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryDoMCDLanePoolMain",
            	"objects":[[paramsValue["LANE_ID"]],["草稿","退回","待接收","已接收","已排单","已完成"]]},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                         url: "system/jdbc/query/one/table",
                         sort: "",
                         toolbar: null,
                         height: 320,
                         read:{"query":"seq_NGS_lane_Pool_Lib_mx","objects":[],"search":{"POOL_ID":[ROW_ID]}},
                     };
                     var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                 }
         	 
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
   }
   var doPool=function(){
	   var g=getGridSelectData(gridNameGrid);
       if(g.length==0){
       	alertMsg("请至少选择一条记录进行操作!");
       	return;
       }else if(g.length!=1){
       	alertMsg("仅限一次操作一条记录!");
       	return;
       }
       
       var ids=[];
       var laneid="";
       
       for(var i=0;i<g.length;i++){
       	ids.push(g[i]["ID"]);
       }
      var winOpts={
            url:"biomarker/seq/ngspooling/resultexa/dopool/dopool",
            title:"Pool..",
            currUrl:replacePathValue(pathValue)
       };
       openWindow(winOpts,{"IDS":ids,"ID":ids[0]});
   }
    var callBack=function(){
        refreshGrid();
     };

    var refreshGrid=function(){

        if(gridNameGrid){
        	gridNameGrid.dataSource.read();
        }
     
     }
   
   
     funcPushs(pathValue,{
         "initData":initData,
         "doPool":doPool,
         "init":init,
         "callBack":callBack,
         "refreshGrid":refreshGrid
     });
});