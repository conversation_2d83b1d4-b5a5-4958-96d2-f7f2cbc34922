$(document).ready(function() {
    var pathValue="biomarker-seq-ngspooling-resultexa-lanev-lanev";
        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"BIO_LANE_INFO"
        };
    }
	
	var paramsValue;
	var gridNameGrid;
	

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
		paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        $("#LANE_TYPE"+pathValue).val(params["LANE_TYPE"]);
        
        var toolbar=getButtonTemplates(pathValue,[
           
       ]);//工具条
       //请求参数
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           height: fullh-300,
           toolbar: toolbar,
           read:{"query":"seq_NGS_lane_info_mx","objects":[params["ID"]]},
		   headerFilter:function(cols,i){
                if(i){
                   
                }
            }
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
    }
	

    var addlane=function(){
        if(paramsValue["ID"]==""){
            alert("请先保存主单信息！");
            return ;
        }
        var winOpts={
            url:"biomarker/seq/ngspooling/lane/listtask/listtask",
            title:"新增:lane信息..",
			currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"ID":"","LANE_ID":paramsValue["ID"]});
    }

    var open=function(IDS){
        var winOpts={
            url:"biomarker/seq/ngspooling/lane/editmx/editmx",
            title:"修改:lane信息..",
			currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"IDS":IDS});//传递id
    }

    var edit=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
		open(arrIds);
     }
     
     var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
						getInfo("form",pathValue,{ID:result["ID"]});//传入id
                        paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read(
            		{
                     	"objects":[paramsValue["ID"]]
                     });//重新读取--刷新
        }
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_LANE_MX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }

    //表格导入
    var importData=function(componentId){
        var grid=gridNameGrid;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":grid,//单表导入
                "tableName":"BIO_LANE_MX",
            }
        });
    }
	

	funcPushs(pathValue,{
		"initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
		"init":init,//初始化方法-在加载完初始化数据之后执行
		"open":open,
		"addlane":addlane,//打开添加表单
		"edit":edit,
		"refreshGrid":refreshGrid,
		"deleteInfo":deleteInfo,
		"submit":submit,//提交方法
		"callBack":callBack,//回调方法
		"importData":importData,
	});
 
 });