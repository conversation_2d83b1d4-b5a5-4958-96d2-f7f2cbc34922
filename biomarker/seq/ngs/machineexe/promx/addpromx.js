$(document).ready(function() {
    var pathValue="biomarker-seq-ngs-machineexe-promx-addpromx";
        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"BIO_WW_PURCHASE_RECEIPT_MX"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
 
 
    var submit=function(){
   $("#ITEMCLASSCODE"+pathValue).val("00");
   $("#ITEMCLASSNAME"+pathValue).val("T3项目管理");
   $("#INVENTORYCODE"+pathValue).val("900000");
   $("#INVNAME"+pathValue).val("委托加工");
   $("#QUANTITY"+pathValue).val("4");
   $("#CMASSUNITNAME"+pathValue).val("管数");
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });