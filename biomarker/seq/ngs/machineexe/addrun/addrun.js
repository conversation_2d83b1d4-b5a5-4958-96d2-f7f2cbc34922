$(document).ready(function() {
    var pathValue="biomarker-seq-ngs-machineexe-addrun-addrun";
    var initData=function(){
        return {
            tableName:"BIO_RUN_INFO"
        };
    }
	
	var paramsValue;
	var gridNameGrid;
    var init=function(params){
		paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        var toolbar=getButtonTemplates(pathValue,[
             {name:"edit",target:"doEdit",title:"实验填写.."},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           height: fullh-300,
           toolbar: toolbar,
           read:{"query":"seq_run_info_mx_down","objects":[params["ID"]]},
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }

    var doEdit=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
      var winOpts={
            url:"biomarker/seq/ngs/machineexe/editlane/editlane",
            title:"修改:lane信息..",
	    currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
     }
     
     var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
						getInfo("form",pathValue,{ID:result["ID"]});//传入id
                        paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read(
            		{
                     	"objects":[paramsValue["ID"]]
                     });//重新读取--刷新
        }
     }
	funcPushs(pathValue,{
		"initData":initData,
		"init":init,
		"open":open,
		"doEdit":doEdit,
		"refreshGrid":refreshGrid,
		"submit":submit,
		"callBack":callBack,
	});
 
 });