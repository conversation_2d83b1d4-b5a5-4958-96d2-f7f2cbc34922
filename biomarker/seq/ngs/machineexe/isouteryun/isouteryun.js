$(document).ready(function () {
    var pathValue = "biomarker-seq-ngs-machineexe-isouteryun-isouteryun";
    var paramsValue;
    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {
            tableName: "BIO_RD_TASK"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        paramsValue = params;
        //getInfo("form1", pathValue, params);
        getInfo("form", pathValue, params);
        // 传入数组ids
        var url = "system/jdbc/query/info/" + initData().tableName; //后端请求路径
        //getInfo("form1", pathValue, params, url); //传入id 
        getInfo("form", pathValue, params, url); //传入id 
    }
    var submit = function () {
debugger;
        var jiraUpdateFlag= $("#JIRAUPDATEFLAG"+pathValue).val();
        var sequencingDataPath= $("#SEQUENCINGDATAPATH"+pathValue).val();
        var seqplat = $("#SEQ_PLAT"+pathValue).val();
        var ju;
        var wd;
        var f5;
        var sb;
        if(jiraUpdateFlag == "是"){
            ju = 1;
        }else{
            ju = 0;
        }        
        var object = [];
            //更新状态
            object.push({
                "jiraUpdateFlag":ju,
                "sequencingDataPath":sequencingDataPath,
                "SEQ_PLAT":seqplat,
            });
        funcExce(paramsValue["pPathValue"] + "pushsts",{"object": object,"wCode":paramsValue["wCode"]});
        funcExce(pathValue + "close"); //关闭页面
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit
    });
});