$(document).ready(function() {
     var pathValue="biomarker-seq-ngs-machineexe-wclane-wclane";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_LANE_INFO"
        };
    }
    var init=function(params){
    	
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
    }
 
 
  var subUpData=function(){
    	
    	//取出IDS
    	var ids=paramsValue["IDS"];
    	var jsonData = getJsonByForm("form",pathValue);
    	time=sysNowTimeFuncParams["sysNowTime"];
         var username=getLimsUser()["name"];
    	 var object=[];
         for(var i=0;i < ids.length;i++){
            object.push($.extend({},jsonData,{"ID":ids[i]}));
         }
         
         
         //执行更新
         var params={"tableName":"BIO_LANE_INFO","objects":object};

         //插入任务明细记录
         var url="system/jdbc/save/batch/table";
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:url,
             ajaxData:params,
             succeed:function(result){
                 
                 if(result["code"]>0){//成功保存后执行流程提交
                 	
                 	funcExce(pathValue+"pageCallBack");//父执行回调
                 
                 	console.log(result);
                 	
                  alertMsg("提交成功!");
                  
                  funcExce(pathValue+"close");//关闭页面
                 	
                 }else{
                 	console.log(result);
                 }
             }
         });
         
    	
    }
 
    var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                      funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "subUpData":subUpData
    });
 
 });