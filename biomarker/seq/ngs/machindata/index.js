$(document).ready(function () {
    var pathValue = "biomarker-seq-ngs-machindata-index";
    var initData = function () {
        return {};
    }
    debugger;
    var gridNameGrid;
    var gridName1Grid;
    var gridName2Grid;
    var gridName3Grid;
    var gridName4Grid;
    var gridName5Grid;
    var gridName6Grid;
    var gridName7Grid;
    var gridName8Grid;
    var gridName9Grid;
    var gridNameLane = [];
    var gridNameLaneMx = [];
    var gridNameLane2 = [];
    var gridNameLaneMx2 = [];
    var gridName8Lane = [];
    var gridName8LaneMx = [];
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "excel", target: "importData1", title: "lane实验导入/模板" },
            { name: "excel", target: "importData2", title: "样本实验导入/模板" },
            { name: "edit", target: "doUpWW", title: "委外实验填写.." },
            { name: "edit", target: "doBf2", title: "提交加测任务" },
            { name: "submit", target: "inputCheck1", title: "提交" },
            { name: "end", target: "doGoEnd", title: "结单" },
        ]);
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_run_info", "objects": [["数据待处理"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 460,
                    read: { "query": "seq_run_info_mx_down2", "objects": [], "search": { "RUN_ID": [ROW_ID] } },
                    detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
                    detailInit: function (e) {
                        var ROW_ID = e.data.ID;
                        var LANEID = e.data.LANEID;
                        var LANETYPE = e.data.LANE_TYPE;
                        var sqlcode = "seq_NGS_lane_info_mx_data2";
                        if (LANETYPE == "MCD混样建库预排Lane") sqlcode = "seq_NGSMCD_lane_info_mx_data2";
                        var subGrid_N_JSON = {
                            url: "system/jdbc/query/one/table",
                            sort: "",
                            toolbar: "",
                            height: 320,
                            size: 2000,
                            read: { "query": sqlcode, "objects": [], "search": { "LANE_ID": [LANEID] } },
                        };
                        var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                        gridNameLaneMx.push(subGrid_N);
                    }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameLane.push(subGrid_N);

            }
        };

        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
        init_1();
        init_2();
        init_3();
        init_4();
        init_5();
        init_6();
        init_7();
        init_8();
        init_9();
    }
    var init_1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doUpset3", title: "修改本次加测量.." },
            { name: "submit", target: "doGoPooling3", title: "提交pooling池" },
            //  { name: "submit", target: "doGoLib3", title: "提交重建库" },
            { name: "submit", target: "doGoYc", title: "提交异常池" },
            { name: "submit", target: "doGoJCJK", title: "提交加测建库" },
            { name: "edit", target: "cancelJc", title: "取消加测" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_NGS_lane_info_mx-jc", "objects": [["加测待处理"], ["加测待处理"]] },
        };
        gridName1Grid = initKendoGrid("#gridName1Grid" + pathValue, gridNameGridJson);

    }

    var init_2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            // {name:"edit",target:"doUpset3",title:"修改本次加测量.."},
            //{name:"submit",target:"doGoPooling3",title:"提交pooling池"},
            //{name:"submit",target:"doGoLib3",title:"提交重建库"},
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_NGS_lane_info_mx-jc-slaf", "objects": [["草稿", "已提交加测"]] },
        };
        gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson);

    }
    var init_3 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doUpset2", title: "修改本次加测量.." },
            { name: "submit", target: "doGoPooling2", title: "提交pooling池" },
            { name: "submit", target: "doGoLib2", title: "提交重建库" },
            { name: "edit", target: "cancelJc3", title: "取消加测" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_NGS_lane_info_mx-jc-mcd", "objects": [["草稿", "已提交加测", "数据待处理"]] },
        };
        gridName3Grid = initKendoGrid("#gridName3Grid" + pathValue, gridNameGridJson);
    }

    var init_4 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "submit", target: "againJc", title: "重新加测" },

        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_run_info", "objects": [["数据已处理"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 460,
                    read: { "query": "seq_run_info_mx_down2", "objects": [], "search": { "RUN_ID": [ROW_ID] } },
                    detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
                    detailInit: function (e) {
                        var ROW_ID = e.data.ID;
                        var LANEID = e.data.LANEID;
                        var LANETYPE = e.data.LANE_TYPE;
                        var sqlcode = "seq_NGS_lane_info_mx_data2";
                        if (LANETYPE == "MCD混样建库预排Lane") sqlcode = "seq_NGSMCD_lane_info_mx_data2";
                        var subGrid_N_JSON = {
                            url: "system/jdbc/query/one/table",
                            sort: "",
                            toolbar: "",
                            height: 320,
                            read: { "query": sqlcode, "objects": [], "search": { "LANE_ID": [LANEID] } },
                        };
                        var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                        gridNameLaneMx2.push(subGrid_N);
                    }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameLane2.push(subGrid_N);

            }
        };

        gridName4Grid = initKendoGrid("#gridName4Grid" + pathValue, gridNameGridJson);

    }

    var init_5 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "modify", title: "修改" },
            { name: "edit", target: "submit5", title: "提交" },
            { name: "edit", target: "sjwwdr", title: "数据导入" },

        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_BIO_WW_PURCHASE_RECEIPT_view", "objects": [["委外数据待处理"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "ID") {
                        setJsonParam(cols[i], "template", getTemplate("#= ID #", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ID = e.data.ID;
                var BILL_ID = e.data.BILL_ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": "query_BIO_WW_PURCHASE_RECEIPT_MX_list", "objects": [[BILL_ID]] }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ID + "_" + pathValue, subGrid_N_JSON);
                //gridNameS1.push(subGrid_N);
            }
        };
        gridName5Grid = initKendoGrid("#gridName5Grid" + pathValue, gridNameGridJson);//初始化表格的方法
        init_6();
    }

    var init_6 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doBack", title: "撤回" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_WW_PURCHASE_RECEIPT_view", "objects": [["待推送", "已推送", "推送失败"]], "search": { "WW_STATES_CL": ["委外数据已处理"] } },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ID = e.data.ID;
                var BILL_ID = e.data.BILL_ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": "query_BIO_WW_PURCHASE_RECEIPT_MX_list", "objects": [[BILL_ID]] }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ID + "_" + pathValue, subGrid_N_JSON);
                //gridNameS1.push(subGrid_N);

            }
        };
        gridName6Grid = initKendoGrid("#gridName6Grid" + pathValue, gridNameGridJson);

    }

    var init_7 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "dotqjc", title: "提交提取异常池" },
            { name: "submit", target: "doGojk", title: "提交建库异常池" },
            { name: "submit", target: "doOK", title: "合格" },
            { name: "edit", target: "doqxyc", title: "提交常规加测池" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_NGS_lane_info_mx-jc", "objects": [["异常待提交"], ["异常待提交"]] },
        };
        gridName7Grid = initKendoGrid("#gridName7Grid" + pathValue, gridNameGridJson);

    }

    var init_8 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "excel", target: "exceptionDataDiscard", title: "丢弃异常处理结果" },
            { name: "excel", target: "exceptionDataAccept", title: "接受异常处理结果" },
            { name: "edit", target: "exceptionDataApplication", title: "应用最终结果" },
            // { name: "edit", target: "doBf2", title: "提交加测任务" },
            // { name: "submit", target: "inputCheck1", title: "提交" },
            // { name: "end", target: "doGoEnd", title: "结单" },
        ]);
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_run_info_exception", "objects": [] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 460,
                    read: { "query": "seq_run_info_mx_down2", "objects": [], "search": { "RUN_ID": [ROW_ID] } },
                    detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
                    detailInit: function (e) {
                        var ROW_ID = e.data.ID;
                        var LANEID = e.data.LANEID;
                        var LANETYPE = e.data.LANE_TYPE;
                        var sqlcode = "seq_NGS_lane_info_mx_exception";
                        if (LANETYPE == "MCD混样建库预排Lane") sqlcode = "seq_NGSMCD_lane_info_mx_data2_exception";
                        var subGrid_N_JSON = {
                            url: "system/jdbc/query/one/table",
                            sort: "",
                            toolbar: "",
                            height: 320,
                            size: 2000,
                            read: { "query": sqlcode, "objects": [], "search": { "LANE_ID": [LANEID] } },
                        };
                        var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                        gridName8LaneMx.push(subGrid_N);
                    }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridName8Lane.push(subGrid_N);

            }
        };

        gridName8Grid = initKendoGrid("#gridName8Grid" + pathValue, gridNameGridJson);
    }

    var init_9 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "excel", target: "importData3", title: "样本实验导入/模板" },
            { name: "edit", target: "doBf3", title: "提交加测任务" },
        ]);
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "seq_NGS_lane_info_mx_data_JC", "objects": [], "search": { "RUN_STATUS": ["无意义"] } },

        };
        gridName9Grid = initKendoGrid("#gridName9Grid" + pathValue, gridNameGridJson);
    }

    var inputCheck1 = function () {

        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }


        confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {

            var object = [];
            var time = sysNowTimeFuncParams["sysNowTime"];

            for (var i = 0; i < arrIds.length; i++) {
                object.push({ "ID": arrIds[i], "RUN_STATUS": "数据已处理" });
            }
            var params = { "tableName": "BIO_RUN_INFO", "objects": object };
            var urlsend = "system/jdbc/save/batch/table";
            putAddOrUpdata(urlsend, params, "是", "提交");

        })
    }

    //取消加测
    var cancelJc = function () {

        var arrIds = getGridSelectData(gridName1Grid);
        var object = [];
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行取消加测吗?", "warn", function () {

            debugger;
            var object = [];
            var objectlanemx = [];
            //            var name = getLimsUser()["name"];
            //           var time = sysNowTimeFuncParams["sysNowTime"];
            for (var i = 0; i < arrIds.length; i++) {
                object.push({
                    "ID": arrIds[i]["SEQ_DATA_ID"],
                    "DATA_STATUS": "数据待处理",
                    "IS_JC": "否"
                });
                objectlanemx.push({
                    "ID": arrIds[i]["LANE_MX_ID"],
                    "LANEMX_STATUS": "数据待处理",
                });
            }

            var urlsend = "system/jdbc/save/batch/table";

            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
            putAddOrUpdata(urlsend, paramsup, "否", "");

            var params = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
            putAddOrUpdata(urlsend, params, "是", "撤回");

        })
    }
    //取消加测
    var cancelJc3 = function () {

        var arrIds = getGridSelectData(gridName3Grid);
        var object = [];
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行取消加测吗?", "warn", function () {

            debugger;
            var object = [];
            //            var name = getLimsUser()["name"];
            //           var time = sysNowTimeFuncParams["sysNowTime"];
            for (var i = 0; i < arrIds.length; i++) {
                object.push({
                    "ID": arrIds[i]["SEQ_DATA_ID"],
                    "IS_JC": "否"
                });
            }
            var params = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
            var urlsend = "system/jdbc/save/batch/table";
            putAddOrUpdata(urlsend, params, "是", "撤回");

        })
    }
    //提交异常任务
    var doGoYc = function () {

        var arrIds = getGridSelectData(gridName1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行取消加测吗?", "warn", function () {

            debugger;

            var objectlanemx = [];
            var object = [];
            //           var name = getLimsUser()["name"];
            //            var time = sysNowTimeFuncParams["sysNowTime"];
            for (var i = 0; i < arrIds.length; i++) {
                object.push({
                    "ID": arrIds[i]["SEQ_DATA_ID"],
                    "DATA_STATUS": "异常待提交"
                });
                objectlanemx.push({
                    "ID": arrIds[i]["LANE_MX_ID"],
                    "LANEMX_STATUS": "异常待提交"
                });
            }
            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
            putAddOrUpdata(urlsend, paramsup, "否", "");
            var params = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
            putAddOrUpdata(urlsend, params, "是", "提交");

        })
    }

    //取消异常任务
    var doqxyc = function () {

        var arrIds = getGridSelectData(gridName7Grid);
        //        var g = getGridSelectData(gridName1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行取消异常吗?", "warn", function () {

            debugger;
            var objectlanemx = [];
            var object = [];
            //           var name = getLimsUser()["name"];
            //           var time = sysNowTimeFuncParams["sysNowTime"];
            for (var i = 0; i < arrIds.length; i++) {
                object.push({
                    "ID": arrIds[i]["SEQ_DATA_ID"],
                    "DATA_STATUS": "加测待处理"
                });
                objectlanemx.push({
                    "ID": arrIds[i]["LANE_MX_ID"],
                    "LANEMX_STATUS": "加测待处理"
                });
            }
            var params = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
            var urlsend = "system/jdbc/save/batch/table";
            putAddOrUpdata(urlsend, params, "是", "提交");

            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
            putAddOrUpdata(urlsend, paramsup, "否", "");

        })
    }

    //提交建库异常池
    var doGojk = function () {

        var arrIds = getGridSelectData(gridName7Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行提交建库异常池吗?", "warn", function () {

            debugger;
            var objqc = [];
            var objectlanemx = [];
            var objtmx = [];
            var objbrl = [];


            var name = getLimsUser()["name"];
            var time = sysNowTimeFuncParams["sysNowTime"];
            for (var i = 0; i < arrIds.length; i++) {
                objqc.push({
                    "ID": arrIds[i]["LIBQCID"],
                    "IS_REGO": "否",
                    "TASKZJ_ZJ_STATUS": "重建库加测"
                });
                objtmx.push({
                    "ID": arrIds[i]["TASK_MX_ID"],
                    "SYS_MAN": name,
                    "SYS_INSERTTIME": time,
                    "REBUILD_ADD": "是", //重建加测标识
                    "TASKZJ_ZJ_UNREST": "ADD_RESON",
                    "ADD_NUMBER": parseInt(arrIds[i]["ADD_NUMBER"]) + 1
                });
                objbrl.push({
                    "ID": arrIds[i]["SEQ_DATA_ID"],
                    "DATA_STATUS": "重建库加测",
                    "SYS_MAN": name,
                    "SYS_INSERTTIME": time
                });
                objectlanemx.push({
                    "ID": arrIds[i]["LANE_MX_ID"],
                    "LANEMX_STATUS": "重建库加测",
                });
            }
            var params = { "tableName": "BIO_LIB_QC_INFO", "objects": objqc };
            var urlsend = "system/jdbc/save/batch/table";
            putAddOrUpdata(urlsend, params, "是", "提交");
            var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objtmx };
            putAddOrUpdata(urlsend, paramsup, "否", "");
            var params1up = { "tableName": "BIO_RUN_LIBRARY", "objects": objbrl };
            putAddOrUpdata(urlsend, params1up, "否", "");
            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
            putAddOrUpdata(urlsend, paramsup, "否", "");

        })
    }

    //提交提取检测异常池
    var dotqjc = function () {


        var arrIds = getGridSelectData(gridName7Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行提交提取异常池吗?", "warn", function () {
            debugger;
            var objqc = [];
            var objtmx = [];
            var objbrl = [];
            var objcode = [];
            var objectlanemx = [];
            var name = getLimsUser()["name"];
            var time = sysNowTimeFuncParams["sysNowTime"];

            for (var i = 0; i < arrIds.length; i++) {
                objcode.push(arrIds[i]["BIO_CODE"]);
            }
            var rows;
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "query_bdrq_bttmx_list", "objects": [], "search": { "SAMPLE_GENNO": objcode } },
                succeed: function (rs) {
                    rows = rs.rows;
                }
            });

            for (var i = 0; i < arrIds.length; i++) {
                var BIO_CODE = arrIds[i]["BIO_CODE"];
                for (var j = 0; j < rows.length; j++) {
                    var SAMPLE_GENNO = rows[j]["SAMPLE_GENNO"];
                    if (SAMPLE_GENNO == BIO_CODE) {
                        objqc.push({
                            "ID": rows[j]["BDRQID"],
                            "RCJC_SAMPLE_STATUES": "重提重建"
                        });
                        objtmx.push({
                            "ID": arrIds[j]["BTTMXID"],
                            "SYS_MAN": name,
                            "SYS_INSERTTIME": time,
                            "REBUILD_ADD": "是", //重提重建标识
                            "TASKZJ_ZJ_UNREST": "ADD_RESON",
                            "ADD_NUMBER": parseInt(arrIds[i]["ADD_NUMBER"]) + 1
                        });
                        objbrl.push({
                            "ID": arrIds[i]["SEQ_DATA_ID"],
                            "DATA_STATUS": "重提重建",
                            "SYS_MAN": name,
                            "SYS_INSERTTIME": time
                        });
                        objectlanemx.push({
                            "ID": arrIds[i]["LANE_MX_ID"],
                            "LANEMX_STATUS": "重提重建",
                        });
                    }
                }
            }
            var params = { "tableName": "BIO_DNA_RNA_QC", "objects": objqc };
            var urlsend = "system/jdbc/save/batch/table";
            putAddOrUpdata(urlsend, params, "是", "提交");
            var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objtmx };
            putAddOrUpdata(urlsend, paramsup, "否", "");
            var params1up = { "tableName": "BIO_RUN_LIBRARY", "objects": objbrl };
            putAddOrUpdata(urlsend, params1up, "否", "");
            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
            putAddOrUpdata(urlsend, paramsup, "否", "");


        })
    }

    var doBf2 = function () {
        debugger;
        var g = [];
        for (var i = 0; i < gridNameLaneMx.length; i++) {
            var arrSubID = getGridSelectData(gridNameLaneMx[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        debugger;

        confirmMsg("确认", "确定要对选中的记录进行提交加测吗?", "warn", function () {
            debugger;
            var object = [];
            var objectlanemx = [];
            var objectmen = [];
            var name = getLimsUser()["name"];
            var time = sysNowTimeFuncParams["sysNowTime"];

            var objects = [];
            for (var i = 0; i < g.length; i++) {
                if (g[i]["IS_JC"] == "是" && g[i]["ADD_DATA"] != null && g[i]["ADD_DATA"] != 0) {



                    if (g[i]["ADD_RESON"] != "合格") {
                        object.push({
                            "ID": g[i]["SEQ_DATA_ID"],
                            "DATA_STATUS": "加测待处理",
                            "IS_JC": "是"
                        });
                        objectlanemx.push({
                            "ID": g[i]["LANE_MX_ID"],
                            "LANEMX_STATUS": "加测待处理"
                        });
                        objectmen.push({
                            "ID": g[i]["TASK_MX_ID"],
                            "SYS_MAN": name,
                            "SYS_INSERTTIME": time
                        });
                    }
                }

            }
            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objects };
            //  putAddOrUpdata(urlsend, paramsup, "否", "");
            // var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objectmen };
            putAddOrUpdata(urlsend, paramsup, "否", "");
            var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
            putAddOrUpdata(urlsend, paramsup, "否", "");
            var paramsup = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
            putAddOrUpdata(urlsend, paramsup, "是", "提交");
        });
    }

    //重新加测
    var againJc = function () {
        debugger;
        var g = [];
        for (var i = 0; i < gridNameLaneMx2.length; i++) {
            var arrSubID = getGridSelectData(gridNameLaneMx2[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var objects = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["IS_PRETEST"] == "是") {
                if (g[i]["THE_DATA_SUMG"] != 0) {
                    var THE_DATA_SUMG = g[i]["THE_DATA_SUMG"] - g[i]["USED_DATA"];
                    objects.push({
                        "ID": g[i]["MTID"],
                        "THE_DATA_SUMG": THE_DATA_SUMG
                    });

                }
            }

        }
        var ids = [];
        for (var i = 0; i < g.length; i++) {
            ids.push(g[i]["SEQ_DATA_ID"]);
            taskid.push(g[i]["TASK_MX_ID"]);
        }

        var winOpts = {
            url: "biomarker/seq/ngs/machindata/againjc/againjc",
            title: "加测填写.."
        };
        openWindow(winOpts, { "ID": ids, "TASK_MX_ID": taskid });
        /*    var object = [];
            var name = getLimsUser()["name"];
            var time = sysNowTimeFuncParams["sysNowTime"];
            for (var i = 0; i < g.length; i++) {
                object.push({
                    "ID": g[i]["SEQ_DATA_ID"],
                    "IS_JC": "是",
                    "DATA_STATUS":"数据待处理"
                });
            }
            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objects };
            putAddOrUpdata(urlsend, paramsup, "否", "");
            var paramsup = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
            putAddOrUpdata(urlsend, paramsup, "是", "提交");
            */
    }

    //lane表格导入
    var importData1 = function (componentId) {
        var arrIds = [];
        for (var i = 0; i < gridNameLane.length; i++) {
            var arrSubID = getSelectData(gridNameLane[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_LANE_INFO",
                        requestData: {
                            ajaxData: { "query": "seq_run_info_mx_down2", "size": 5000, "objects": [], "search": { "ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }

    //lane明细表格导入
    var importData2 = function (componentId) {
        var arrIds = [];
        debugger;
        for (var i = 0; i < gridNameLane.length; i++) {
            var arrSubID = getGridSelectData(gridNameLane[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["LANEID"]);
        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_RUN_LIBRARY",
                        requestData: {
                            ajaxData: { "query": "seq_NGS_lane_info_mx_data2", "size": 5000, "objects": [], "search": { "LANE_ID": ids } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }

    //本次加测量
    var doUpset3 = function () {
        var arrIds = getGridSelectData(gridName1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var ids = [];
        var winOpts = {
            url: "biomarker/seq/ngs/machindata/upset/upset",
            title: "加测填写.."
        };
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["SEQ_DATA_ID"]);
        }
        openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
    }
    //本次加测量
    var doUpset2 = function () {
        var arrIds = getGridSelectData(gridName3Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var ids = [];
        var winOpts = {
            url: "biomarker/seq/ngs/machindata/upset/upset",
            title: "加测填写.."
        };
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["SEQ_DATA_ID"]);
        }
        openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
    }
    //提交至pooling
    var doGoPooling3 = function () {

        debugger;
        var g = getGridSelectData(gridName1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        for (var i = 0; i < g.length; i++) {
            if (g[i]["ADD_DATA"] == 0) {
                alertMsg("加测数据量不可为0!");
                return;
            }
        }



        var POOL_CODES = getSelectData(gridName1Grid, "LIBRARY_CODE");

        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "QueryNGSSeqTask", "objects": [["1"]], "search": { "SLAF_BS": "1", "LIBRARY_CODEP": POOL_CODES } },
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        if (rows.length > 0) {
            confirmMsg2("重复提示", "待提交文库里，文库编号有重复文库，请确认是否继续提交？如不提交，重新选择文库。", "warn", function () {
                gy2();
            });
        } else {
            gy2();
        }





    }

    var gy2 = function () {

        var g = getGridSelectData(gridName1Grid);

        var object = [];
        var objectmod = [];
        var objectlanemx = [];
        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];
        for (var i = 0; i < g.length; i++) {
            objectlanemx.push({
                "ID": g[i]["LANE_MX_ID"],
                "LANEMX_STATUS": "已提交加测"
            });
            object.push({
                "ID": g[i]["SEQ_DATA_ID"],
                "DATA_STATUS": "已提交加测"
            });
            //公式：上机安排数据量1（M)=有效数据量（G）/【加测任务单处的上机安排数据量1（M)*0.3】*加测数据量（G）*1.2/0.3
            var USED_DATA = g[i]["USED_DATA"];
            var THE_DATA_APSUM = g[i]["THE_DATA_APSUM"];
            var ADD_DATA = g[i]["ADD_DATA"];
            var THD_SJ_DATA1 = (USED_DATA / (THE_DATA_APSUM * 0.3)) * ADD_DATA * 1.2 / 0.3;
            var THE_DATA_APSUM = ADD_DATA / 0.3;
            var addnumber = parseInt(g[i]["ADD_NUMBER"]) + 1;
            var rows1 = [];
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "query_BIO_SEQ_MOD_list", "objects": [[g[i]["BSMID"]]] },
                succeed: function (rs) {
                    //console.log(rs);				
                    rows1 = rs["rows"][0];
                }
            });
            var mid = getRandomId();
            rows1["ID"] = mid;
            rows1["ISOK"] = "加测";
            rows1["ADD_DATA"] = ADD_DATA;
            rows1["THD_SJ_DATA1"] = THD_SJ_DATA1;
            rows1["THE_DATA_APSUM"] = THE_DATA_APSUM;
            rows1["ADD_NUMBER"] = addnumber;
            rows1["LANENO"] = null;

            objectmod = objectmod.concat(rows1);

            //				objectmod.push(rows1);

        }
        var urlsend = "system/jdbc/save/one/table/objects";
        var parammod = { "tableName": "BIO_SEQ_MOD", "objects": objectmod };
        putAddOrUpdata(urlsend, parammod, "否", "提交");

        var urlsends = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
        putAddOrUpdata(urlsends, paramsup, "否", "");

        var paramsup = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
        putAddOrUpdata(urlsends, paramsup, "是", "提交");
    }





    //提交至pooling
    var doGoPooling2 = function () {
        debugger;
        var g = getGridSelectData(gridName3Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }

        confirmMsg("确认", "确定要对选中的记录进行提前加测吗?", "warn", function () {
            var object = [];
            var objectmod = [];
            var paramsUpTaskMx = [];
            var name = getLimsUser()["name"];
            var time = sysNowTimeFuncParams["sysNowTime"];
            for (var i = 0; i < g.length; i++) {
                paramsUpTaskMx.push({//BIO_TASK_LIBMX
                    "ID": g[i]["TASK_MX_ID"],
                    "LIBRARY_CODE": g[i]["LIBRARY_CODE"],
                    "MCD_PASS": "否",
                    "SYS_INSERTTIME": time,//实验结果时间
                    "SYS_MAN": name //实验操作人
                });
                object.push({
                    "ID": g[i]["SEQ_DATA_ID"],
                    "DATA_STATUS": "已提交Pooling池"
                });
            }
            var urlsend = "system/jdbc/save/batch/table";
            var parammod = { "tableName": "BIO_TASK_LIBMX", "objects": paramsUpTaskMx };
            putAddOrUpdata(urlsend, parammod, "否", "提交");

            var paramsup = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
            putAddOrUpdata(urlsend, paramsup, "是", "提交");
        });
    }
    //提交至重建库
    var doGoLib3 = function () {
        var arrIds = getGridSelectData(gridName1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        for (var i = 0; i < arrIds.length; i++) {
            if (arrIds[i]["ADD_DATA"] == 0) {
                alertMsg("加测数据量不可为0!");
                return;
            }
        }

        var paramsUpTask = [];
        var paramsUpExe = [];
        var paramsUpTaskMx = [];
        var paramsUpExeMx = [];
        var pramsJc = [];
        var flag1 = 0;
        var flag2 = 0;
        for (var i = 0; i < arrIds.length; i++) {
            if (arrIds[i]["EXE_TQQC_ID"] == null) {
                alertMsg("所选记录为混库,只能提交到“Pooling池”重排上机单!");
                return;
            } else {
                if (arrIds[i]["TASK_ID"]) {//DNA/RNA建库情况
                    flag1 = 1;
                    paramsUpTask.push({//BIO_TASK_LIB
                        "ID": arrIds[i]["TASK_ID"],
                        "JC_STATUS": "加测!"
                    });
                    paramsUpTaskMx.push({
                        "ID": arrIds[i]["TASK_LIBMX_ID"],
                        "TASK_LS_STATUS": "加测"
                    });
                } else if (arrIds[i]["EXE_TQQC_ID"]) {//HIC/ATAC建库情况
                    flag2 = 1;
                    paramsUpExe.push({//EXE_TQQC_SHEET
                        "ID": arrIds[i]["EXE_TQQC_ID"],
                        "JC_STATUS": "加测!"
                    });
                    paramsUpExeMx.push({//BIO_TQ_TASK_MX
                        "ID": arrIds[i]["TASK_LIBMX_ID"],
                        "TASK_SM_STATUS": "加测"
                    });
                }
            }
            pramsJc.push({
                "ID": arrIds[i]["SEQ_DATA_ID"],
                "DATA_STATUS": "已提交"
            });
        }

        var urlsend = "system/jdbc/save/batch/table";
        if (flag1 == 1) {
            var paramsup1 = { "tableName": "BIO_TASK_LIB", "objects": paramsUpTask };
            putAddOrUpdata(urlsend, paramsup1, "否", "提交");
            var paramsup2 = { "tableName": "BIO_TASK_LIBMX", "objects": paramsUpTaskMx };
            putAddOrUpdata(urlsend, paramsup2, "否", "提交");
        }
        if (flag2 == 1) {
            var paramsup2 = { "tableName": "EXE_TQQC_SHEET", "objects": paramsUpExe };
            putAddOrUpdata(urlsend, paramsup2, "否", "提交");
            var paramsup3 = { "tableName": "BIO_TQ_TASK_MX", "objects": paramsUpExeMx };
            putAddOrUpdata(urlsend, paramsup3, "否", "提交");
        }
        if (flag1 == 1 || flag2 == 1) {
            var paramsup4 = { "tableName": "BIO_RUN_LIBRARY", "objects": pramsJc };
            putAddOrUpdata(urlsend, paramsup4, "是", "提交");
        }
    }
    //提交至重建库
    var doGoLib2 = function () {
        debugger;
        var arrIds = getGridSelectData(gridName3Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }

        var paramsUpTask = [];
        var pramsJc = [];
        var flag1 = 0;
        var flag2 = 0;
        for (var i = 0; i < arrIds.length; i++) {
            if (arrIds[i]["TASK_MX_ID"]) {//DNA/RNA建库情况
                flag1 = 1;
                paramsUpTask.push({//BIO_TASK_LIB
                    "ID": arrIds[i]["ID"],
                    "REBUILD_LIB": "是",
                    "JC_STATUS": "重建库",
                    "TASK_LS_STATUS": "重建库"
                });
            }
            pramsJc.push({
                "ID": arrIds[i]["SEQ_DATA_ID"],
                "DATA_STATUS": "已提交重建库"
            });
        }

        var urlsend = "system/jdbc/save/batch/table";
        if (flag1 == 1) {
            var paramsup1 = { "tableName": "BIO_TASK_LIB", "objects": paramsUpTask };
            putAddOrUpdata(urlsend, paramsup1, "否", "提交");
        }
        if (flag1 == 1 || flag2 == 1) {
            var paramsup4 = { "tableName": "BIO_RUN_LIBRARY", "objects": pramsJc };
            putAddOrUpdata(urlsend, paramsup4, "是", "提交");
        }
    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameLane = [];
        gridNameLaneMx = [];
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
        if (gridName1Grid) {
            gridName1Grid.dataSource.read();
        }
        if (gridName2Grid) {
            gridName2Grid.dataSource.read();
        }
        if (gridName3Grid) {
            gridName3Grid.dataSource.read();
        }
        if (gridName4Grid) {
            gridName4Grid.dataSource.read();
        }
        if (gridName5Grid) {
            gridName5Grid.dataSource.read();
        }
        if (gridName6Grid) {
            gridName6Grid.dataSource.read();
        }
        if (gridName7Grid) {
            gridName7Grid.dataSource.read();
        }
        if (gridName8Grid) {
            gridName8Grid.dataSource.read();
        }
        if (gridName9Grid) {
            gridName9Grid.dataSource.read();
        }
    }

    var open = function (ID) {
        debugger;
        var arrIds = getGridSelectData(gridName5Grid);
        var winOpts = {
            url: "biomarker/seq/ngs/machindata/modify/modify",
            title: "委外单修改...",
        };
        openWindow(winOpts, {
            "ID": ID,
            "BILL_ID": arrIds[0]["BILL_ID"],
        }); //传递id
    };

    var modify = function () {
        var arrIds = getSelectData(gridName5Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    };

    // 提交
    var submit5 = function () {
        var arrIds = getGridSelectData(gridName5Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行提交!");
            return;
        }
        debugger;
        if (arrIds[0]["DEPARTMENTCODE"] == null || arrIds[0]["DEPARTMENTCODE"] == "") {
            alertMsg("请填写部门编码后再进行提交!");
            return;

        }
        var object = [];
        confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {
            for (var i = 0; i < arrIds.length; i++) {

                object.push({
                    ID: arrIds[i]["ID"], //样本ID
                    PUSH_STATE: "待推送", //更新状态
                    WW_STATES_CL: "委外数据已处理",
                });
            }
            var tableName = "BIO_WW_PURCHASE_RECEIPT";
            var params = { tableName: tableName, objects: object };
            //插入任务明细记录
            var url = "system/jdbc/save/batch/table";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url,
                ajaxData: params,
                succeed: function (result) {
                    if (result["code"] > 0) {
                        refreshGrid();
                        // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
                        alertMsg("提交成功!", "success");
                        // funcExce(pathValue+"close");//关闭页面
                    } else {
                        alertMsg("提交失败!", "error");
                    }
                },
            });
        });
    };
    var doUpWW = function () {
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/seq/ngs/machindata/doUpWW/doUpWW",
            title: "委外实验填写.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递

    }

    function getRandomId() {
        return new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };


    var doGoEnd = function () {
    }

    //丢弃异常处理结果
    var exceptionDataDiscard = function () {
        var arrIds;
        var object = [];
        for (var i = 0; i < gridName8LaneMx.length; i++) {
            var arrSubID = getSelectData(gridName8LaneMx[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条明细进行修改!");
            return;
        }
        for (var i = 0; i < arrIds.length; i++) {
            object.push({
                "ID": arrIds[i]["ID"],
                "EXCEPTIONSTATE": "已丢弃",
            });

        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_RUN_LIBRARY_EXCEPTION", "objects": object };
        putAddOrUpdata(urlsend, paramsup, "否", "提交");

    }

    //接受异常处理结果
    var exceptionDataAccept = function () {
        var arrIds;
        var object = [];
        for (var i = 0; i < gridName8LaneMx.length; i++) {
            var arrSubID = getSelectData(gridName8LaneMx[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条明细进行修改!");
            return;
        }
        for (var i = 0; i < arrIds.length; i++) {
            object.push({
                "ID": arrIds[i]["ID"],
                "EXCEPTIONSTATE": "已接受",
            });

        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_RUN_LIBRARY_EXCEPTION", "objects": object };
        putAddOrUpdata(urlsend, paramsup, "否", "提交");

    }



    //应用最终结果
    var exceptionDataApplication = function () {
        debugger;
        var arrIds = [];
        var object = [];
        var deleteArrIds = [];
        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];
        for (var i = 0; i < gridName8LaneMx.length; i++) {
            var arrSubID = getGridSelectData(gridName8LaneMx[i]);
            arrIds.push(arrSubID[0]);
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条明细进行修改!");
            return;
        }

        confirmMsg("确认", "确定要对选中的记录应用到最终结果吗?", "warn", function () {
            for (var i = 0; i < arrIds.length; i++) {
                deleteArrIds.push({
                    "LANE_ID": arrIds[i]["LANE_ID"],
                    "LANE_MX_ID": arrIds[i]["LANE_MX_ID"],
                    "TASK_MX_ID": arrIds[i]["TASK_MX_ID"],
                    "RUN_ID": arrIds[i]["RUN_ID"],
                });

            }
            for (var i = 0; i < arrIds.length; i++) {
                object.push({
                    "ID": arrIds[i]["ID"],
                    "EXCEPTIONSTATE": "已应用",
                });

            }
            var arrIdsRz = arrIds;
            for (var j = 0; j < arrIdsRz.length; j++) {
                var idsrz = getRandomId;
                arrIdsRz[i]["ID"] = idsrz;
                arrIdsRz[i]["SYS_MAN"] = name;
                arrIdsRz[i]["SYS_INSERTTIME"] = time;
                arrIds[i]["SYS_MAN"] = name;
                arrIds[i]["SYS_INSERTTIME"] = time;
            }
            //删除原有数据
            var params = { "tableName": "BIO_RUN_LIBRARY", "ids": deleteArrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
            //删除日志数据
            // var params = { "tableName": "BIO_RUN_LIBRARY_JOURNAL", "ids": deleteArrIds };
            // var url = "system/jdbc/delete/batch/table";
            // deleteGridDataByIds(url, params, refreshGrid);
            //更改异常表数据状态
            var urlsend = "system/jdbc/save/batch/table";
            var paramsups = { "tableName": "BIO_RUN_LIBRARY_EXCEPTION", "objects": object };
            putAddOrUpdata(urlsend, paramsups, "否", "提交");
            //覆盖原有数据
            var paramsup = { "tableName": "BIO_RUN_LIBRARY", "objects": arrIds };
            putAddOrUpdata(urlsend, paramsup, "是", "提交");
            //更新日志
            var paramsup = { "tableName": "BIO_RUN_LIBRARY_JOURNAL", "objects": arrIdsRz };
            putAddOrUpdata(urlsend, paramsup, "是", "提交");
        });

    }

    var doBack = function () {
        var arrobj = getGridSelectData(gridName6Grid);
        if (arrobj.length != 1) {
            alertMsg("请只选择一条记录进行操作!");
            return;
        }
        var PUSH_STATE = arrobj[0]["PUSH_STATE"];
        if (PUSH_STATE == "已推送") {
            confirmMsg("确认", "记录已推送U8,请先联系U8删除后再删除", "warn", function () {
                doBack1(arrobj[0]);
            });
        } else {
            doBack1(arrobj[0]);
        }

    }

    var doBack1 = function (arrobj) {
        var object = [];
        object.push({
            "ID": arrobj["ID"],
            "PUSH_STATE": "委外数据待处理",
        });
        var urlsend = "system/jdbc/save/batch/table";
        var paramsups = { "tableName": "BIO_WW_PURCHASE_RECEIPT", "objects": object };
        putAddOrUpdata(urlsend, paramsups, "是", "提交");
    }

    //生成uuid
    function getRandomId() {
        return "NGSRZ-" + new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };


    //提交至加测同时建库
    var doGoJCJK = function () {

        debugger;
        var g = getGridSelectData(gridName1Grid);

        if (g.length != 1) {
            alertMsg("请只选择一条记录进行操作!");
            return;
        }
        for (var i = 0; i < g.length; i++) {
            if (g[i]["ADD_DATA"] == 0) {
                alertMsg("加测数据量不可为0!");
                return;
            }
        }

        var winOpts = {
            url: "biomarker/seq/ngs/machindata/doGoJCJK/doGoJCJK",
            title: "提交至加测同时建库..",
            currUrl: replacePathValue(pathValue)
        };

        openWindow(winOpts, { "object": g });


    }

    //lane明细表格导入
    var importData3 = function (componentId) {
        debugger;

        //var arrIds = getSelectData(gridName9Grid,"SEQ_DATA_ID");


        var LANE_MX_ID = getSelectData(gridName9Grid, "LANE_MX_ID");


        var RUN_ID = getSelectData(gridName9Grid, "RUN_ID");

        if (LANE_MX_ID.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }


        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_RUN_LIBRARY",
                        requestData: {
                            ajaxData: { "query": "seq_NGS_lane_info_mx_data_JC", "size": 5000, "objects": [], "search": { "LANE_MX_ID": LANE_MX_ID, "RUN_ID": RUN_ID } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }


    var doBf3 = function () {
        debugger;
        var g = getGridSelectData(gridName9Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }


        var POOL_CODES = getSelectData(gridName9Grid, "POOL_CODE");

        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "seq_NGS_lane_info_mx-jc", "objects": [["加测待处理"], ["加测待处理"]], "search": { "LIBRARY_CODES": POOL_CODES } },
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        if (rows.length > 0) {
            confirmMsg2("重复提示", "待提交文库里，文库编号有重复文库，请确认是否继续提交？如不提交，重新选择文库。", "warn", function () {
                gy();
            });
        } else {
            gy();
        }

    }

    var gy = function () {

        var g = getGridSelectData(gridName9Grid);
        var object = [];
        var objectlanemx = [];
        var objectmen = [];
        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];

        var objects = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["IS_JC"] == "是" && g[i]["ADD_DATA"] != null && g[i]["ADD_DATA"] != 0) {



                if (g[i]["ADD_RESON"] != "合格") {
                    object.push({
                        "ID": g[i]["SEQ_DATA_ID"],
                        "DATA_STATUS": "加测待处理",
                        "IS_JC": "是"
                    });
                    objectlanemx.push({
                        "ID": g[i]["LANE_MX_ID"],
                        "LANEMX_STATUS": "加测待处理"
                    });
                    objectmen.push({
                        "ID": g[i]["TASK_MX_ID"],
                        "SYS_MAN": name,
                        "SYS_INSERTTIME": time
                    });
                }
            }

        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objects };
        //  putAddOrUpdata(urlsend, paramsup, "否", "");
        // var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objectmen };
        putAddOrUpdata(urlsend, paramsup, "否", "");
        var paramsup = { "tableName": "BIO_LANE_MX", "objects": objectlanemx };
        putAddOrUpdata(urlsend, paramsup, "否", "");
        var paramsup = { "tableName": "BIO_RUN_LIBRARY", "objects": object };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");

    }


    // 确认框
    function confirmMsg2(title, msg, type, confirmed) {
        var confirmDialog = $('<div class="dialog-box"></div>').kendoDialog({
            animation: { open: { effects: 'fade:in' }, close: { effects: 'fade:out' } },
            closable: false,
            maxWidth: '30%',
            maxHeight: '30%',
            minWidth: 320,
            minHeight: 196,
            title: title,
            content: '<dl class="d-flex align-items-center m-0"><dt>' + checkInfoType(type) + '</dt><dd class="m-0">' + msg + '</dd></dl>',
            actions: [
                {
                    text: '继续提交？',
                    primary: true,
                    action: function (e) {
                        confirmed();
                    }
                },
                {
                    text: '不提交？',
                    action: function (e) {
                        confirmDialog.close();
                    }
                }
            ],
            close: function () {
                confirmDialog.destroy();
            }
        }).data('kendoDialog');
        confirmDialog.open();
    }

    var sjwwdr = function (componentId) {
        // qwwdswfd(["lvw21wq0dp6hgmtlxzh","lvw2199ndmlu1hf2vc"])

        // return;
        
        debugger
        var IDS = getSelectData(gridName5Grid);
        if (IDS.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_WW_PURCHASE_RECEIPT",
                        requestData: {
                            ajaxData: { "query": "query_BIO_WW_PURCHASE_RECEIPT_view", "objects": [["委外数据待处理"]], "search": { "IDS": IDS } },
                            // ajaxData:{"query":"QueryNGSSeqTask","size":10000,"objects":[]},
                        },
                        params: p,
                        name: n,
                    });
                },
                "succeed": function () {
                    qwwdswfd(IDS);
                }
            }
        });

    }
    var qwwdswfd = function (IDS) {debugger
        $.fn.ajaxPost({
            ajaxUrl: "function/system/settlement/wlwkImageUpload/sjwwdr",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: {
                "IDS": IDS
            },
            succeed: function (rs) {
                if (rs["code"] > 0) {
                    alertMsg(rs["apiData"] );
                    refreshGrid();
                }
            }
        });

    }




    funcPushs(pathValue, {
        "initData": initData,
        "sjwwdr": sjwwdr,
        "doGoJCJK": doGoJCJK,
        "doBack": doBack,
        "init": init,
        "submit5": submit5,
        "modify": modify,
        "doUpWW": doUpWW,
        "inputCheck1": inputCheck1,
        "importData1": importData1,
        "importData2": importData2,
        "importData3": importData3,
        "cancelJc": cancelJc,
        "cancelJc3": cancelJc3,
        "doBf2": doBf2,
        "doBf3": doBf3,
        "doUpset3": doUpset3,
        "doUpset2": doUpset2,
        "doGoPooling3": doGoPooling3,
        "doGoPooling2": doGoPooling2,
        "doGoLib3": doGoLib3,
        "doGoLib2": doGoLib2,
        "doGoEnd": doGoEnd,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "doGoYc": doGoYc,
        "doqxyc": doqxyc,
        "doGojk": doGojk,
        "dotqjc": dotqjc,
        "againJc": againJc,
        "exceptionDataDiscard": exceptionDataDiscard,
        "exceptionDataAccept": exceptionDataAccept,
        "exceptionDataApplication": exceptionDataApplication,
    });
});