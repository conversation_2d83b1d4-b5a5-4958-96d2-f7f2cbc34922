$(document).ready(function () {
    var pathValue = "biomarker-system-qd-po-index";
    var initData = function () {
        return {};
    }
    var gridNameGrid;
    var gridName2Grid;
    var flag = 0;
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            //  {name:"add",target:"add"},
            //{name:"edit",target:"edit",title:"提交"},
            { name: "pushs", target: "pushs", title: "推送" },
            { name: "pusherror", target: "pusherror", title: "查看U8返回信息" },
            //  {name:"delete",target:"deleteInfo"}
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_QD_PRODUCTION_ORDER", "objects": [["待推送", "推送失败"]] },
            headerFilter: function (cols, i) { }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
        init2();
    }

    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "pusherror", target: "pusherrorcg", title: "查看U8返回信息" },
            { name: "edit", target: "withdraw", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_QD_PRODUCTION_ORDER", "objects": [["已推送"]] },
            headerFilter: function (cols, i) { }
        };
        gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson);

    }
    var add = function () {
        var winOpts = {
            url: "biomarker/interface/projectinfo/rd/add/add",
            title: "新增:青岛U8生产订单.."
        };
        openWindow(winOpts);
    }
    var pusherror = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }
    var pusherrorcg = function () {
        var arrIds = getSelectData(gridName2Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }
    var open = function (ID) {
        var winOpts = {
            url: "biomarker/system/qd/po/add/addsee",
            title: "查看:U8返回信息.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }

    var openviews = function (ID) {
        var winOpts = {
            url: "biomarker/interface/projectinfo/rd/addviews/addviews",
            title: "查看:常规则项立项信息.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }
    var edit = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var sumbit = function () {
        formSubmit({
            formId: "form",
            pathValue: pathValue
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
        if (gridName2Grid) {
            gridName2Grid.dataSource.read();
        }
    }

    var deleteInfo = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
            var params = { "tableName": "BIO_PROJECT_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }

    //表格导入
    var importData = function (componentId) {
        var grid = gridNameGrid;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                "template": grid,//单表导入
                "tableName": "BIO_PROJECT_INFO",
            }
        });
    }
    //推送
    var pushs = function () {
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        if (flag == 1) {
            alertMsg("正在推送中，请勿重复点击!");
            return;
        }
        if (flag == 0) {
            flag = 1;
        }
        var objects = [];
        var Ids = [];
        //confirmMsg("确认", "确定要对选中的记录进行推送吗?", "warn", function () {
        for (var i = 0; i < arrIds.length; i++) {
            if (Ids.indexOf(arrIds[i]["MOID"]) < 0) {
                Ids.push(arrIds[i]["MOID"]);//订单主表ID
            }
        }
        var params = { "Ids": Ids, "ts": "手动模式", "urlhtml": U8XML_URL }; //插入任务明细记录
        var url = "system/jdbc/qd/pop";
        var jsondate;
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                jsondate = result
            },
        });
        if (jsondate["code"] > 0) {
            for (var i = 0; i < Ids.length; i++) {
                //var keys2;
                //
                var myDate = new Date();
                var date2 = myDate.toLocaleString();
                var apidate = jsondate["apiData"][i]["apiData"];
                var substr = apidate.match(/key=\"(\S*)\" succeed/);
                var reds = "";
                var reds2 = "";
                var rows4 = "";
                var apidates = "";
                /*               var EX_DH_NO = Ids[i]["EX_DH_NO"];
                               $.fn.ajaxPost({
                                   ajaxUrl: "system/jdbc/query/one/table",
                                   ajaxType: "post",
                                   ajaxAsync: false,
                                   ajaxData: { "query": "query_qd_fhz_list", "objects": [[EX_DH_NO]] },
                                   succeed: function (rs) {
                                       //console.log(rs);				
                                       rows4 = rs;
                                   }
                               });*/
                debugger;
                apidates = "   |    推送时间  :  " + date2 + "  返回值  :  " + apidate + "     |   ";
                //var a =apidate.indexOf("u8key")
                if (apidate.indexOf("u8key") >= 0) {

                    // alertMsg("推送成功!", "success"); 
                    reds = "推送成功!";
                    reds2 = "success";
                    objects.push({
                        //                        ID: arrIds[i]["ID"], //样本ID
                        MOID: Ids[i],//主单ID 
                        RETURN_VALUE: apidates,//返回值 
                        LIMS_TO_U8ID: substr[1], //u8单号
                        PUSH_STATE: "已推送", //更新状态
                    });
                } else {
                    reds = "推送失败!";
                    reds2 = "error";
                    objects.push({
                        //                        ID: arrIds[i]["ID"], //样本ID
                        MOID: Ids[i],//主单ID
                        RETURN_VALUE: apidates,//返回值 
                        PUSH_STATE: "推送失败", //更新状态
                    });
                }

                var pushobj = [];
                pushobj.push({
                    "API_TYPE": "青岛U8生产订单",
                    "PUSH_DATE": sysNowTimeFuncParams["sysNowTime"],
                    "MOID": Ids[i],
                    "API_RS": apidate,
                    "PUSH_MAN": getLimsUser()["name"],
                    "PUSH_MODE": "手动推送",
                });
            }
            alertMsg(reds, reds2);
            debugger;
            //更新状态
            var tableName = "BIO_QD_PRODUCTION_ORDER";
            var params = { "tableName": tableName, "objects": objects, "where": { "MOID": "${MOID}" } }; //插入任务明细记录
            var url = "/system/jdbc/save/batch/table/where";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url,
                ajaxData: params,
                ajaxAsync: false,
                succeed: function (result) {
                    if (result["code"] > 0) {
                        refreshGrid();
                        //alertMsg("提交成功!", "success"); 
                        flag = 0;
                    } else {
                        //alertMsg("提交失败!", "error");
                        flag = 0;
                    }
                },
            });

            //日志记录
            var tableName1 = "BIO_U8API_LOG";
            var params1 = { "tableName": tableName1, "objects": pushobj }; //插入任务明细记录
            var url1 = "/system/jdbc/save/batch/table";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url1,
                ajaxData: params1,
                ajaxAsync: false,
                succeed: function (result) {

                },
            });
            refreshGrid();
        } else {
            alertMsg("提交失败", "error");
        }
        //});
    };
    var withdraw = function () {
        var arrIds = getGridSelectData(gridName2Grid);
        if (arrIds.length < 1) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        var object = [];
        var Ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            if (Ids.indexOf(arrIds[i]["MOID"]) < 0) {
                Ids.push(arrIds[i]["MOID"]);//订单主表ID
            }
        }
        for (var i = 0; i < Ids.length; i++) {
            object.push({
                MOID: Ids[i], //主表ID
U8_DATA_CHECK: "N",
                PUSH_STATE: "待推送", //更新状态
            });
        }
        //更新状态
        var tableName = "BIO_QD_PRODUCTION_ORDER";
        var params = { "tableName": tableName, "objects": object, "where": { "MOID": "${MOID}" } }; //插入任务明细记录
        var url = "/system/jdbc/save/batch/table/where";
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    refreshGrid();

                    alertMsg("操作成功!", "success");
                } else {
                    alertMsg(" 操作失败!", "error");

                }
            },
        });
    }
    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "pusherror": pusherror,
        "pusherrorcg": pusherrorcg,
        "open": open,
        "add": add,
        "edit": edit,
        "pushs": pushs,
        "withdraw": withdraw,
        "openviews": openviews,
        "refreshGrid": refreshGrid,
        "deleteInfo": deleteInfo,
        "sumbit": sumbit,//提交方法
        "callBack": callBack,//回调方法
        "importData": importData,
    });
});