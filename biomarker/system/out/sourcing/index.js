$(document).ready(function () {
    var pathValue = "biomarker-system-out-sourcing-index";
    var initData = function () {
        return {};
    }
    var gridNameGrid;
    var gridName2Grid;
    var flag = 0;
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            // {name:"add",target:"add"},
            //{name:"edit",target:"edit",title:"提交"},
            { name: "edit", target: "pushs", title: "推送" },
            { name: "pusherror", target: "pusherror", title: "查看U8返回信息" },
            //{name:"delete",target:"deleteInfo"}
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_OUT_SOURCING_view", "objects": [], "search": { "PUSH_STATE": ["待推送", "推送失败"] } },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "TRADE_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= TRADE_NO #", "funcExce(\'" + pathValue + "openviews\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
        init1();
    }

    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "pusherror", target: "pusherrorcg", title: "查看U8返回信息" },
            { name: "edit", target: "withdraw", title: "撤回" },
            // {name:"add",target:"add"},
            //{name:"edit",target:"edit",title:"提交"},
            // {name:"edit",target:"edit2",title:"提交"},
            //{name:"delete",target:"deleteInfo"}
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_OUT_SOURCING_view", "objects": [], "search": { "PUSH_STATE": ["已推送"] } },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "TRADE_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= TRADE_NO #", "funcExce(\'" + pathValue + "openviews\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson);
    }

    var add = function () {
        var winOpts = {
            url: "biomarker/interface/projectinfo/rd/add/add",
            title: "新增:材料出库单-生产.."
        };
        openWindow(winOpts);
    }

    var open = function (ID) {
        var winOpts = {
            url: "biomarker/interface/projectinfo/rd/add/add",
            title: "修改:材料出库单-生产.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }
    var openviews = function (ID) {
        var winOpts = {
            url: "biomarker/system/out/sourcing/xq/xq",
            title: "查看:材料出库单-生产信息.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }
    var edit = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }

    var edit2 = function () {
        var g = getSelectData(gridNameGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }

        var objectupmain = [];
        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];
        for (var i = 0; i < g.length; i++) {
            //主单状态
            objectupmain.push({
                "ID": g[i],
                "EXAMINE_MAN": name,//审核人
                "EXAMINE_TIME": time,//审核时间
                "EXAMINE_FLAG": "通过"//审核状态
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var parammain = { "tableName": "BIO_PROJECT_INFO", "objects": objectupmain };
        putAddOrUpdata(urlsend, parammain, "是", "执行单状态更新:");
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var sumbit = function () {
        formSubmit({
            formId: "form",
            pathValue: pathValue
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
        if (gridName2Grid) {
            gridName2Grid.dataSource.read();
        }
    }

    var deleteInfo = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
            var params = { "tableName": "BIO_PROJECT_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }

    //表格导入
    var importData = function (componentId) {
        var grid = gridNameGrid;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                "template": grid,//单表导入
                "tableName": "BIO_PROJECT_INFO",
            }
        });
    }


    //推送
    var pushs = function () {
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        if (flag == 1) {

        }
        if (flag == 0) {
            flag = 1;
        }
        var object = [];
        var Ids = [];
        //confirmMsg("确认", "确定要对选中的记录进行推送吗?", "warn", function () {
        for (var i = 0; i < arrIds.length; i++) {
            Ids.push(arrIds[i]["ID"]);//样本ID
        }
        var params = { "Ids": Ids, "ts": "手动模式" }; //插入任务明细记录
        var url = "system/jdbc/oss/push";
        var jsondate;
        debugger;
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                jsondate = result

            },
        });
        debugger;
        var reds = "";
        var reds2 = "";
        if (jsondate["code"] > 0) {
            for (var i = 0; i < arrIds.length; i++) {
                //var keys2;
                //
                var myDate = new Date();
                var date2 = myDate.toLocaleString();
                var apidate = jsondate["apiData"][i];
                var substr = apidate.match(/"code":"(\S*)"}/);
                var substr2 = apidate.match(/"state":"(\S*)","data/);
                var substr3 = apidate.match(/委外华开单号重复：(\S*)","code"/);

                var reds = "";
                var reds2 = "";
                var rows4 = "";
                var apidates = "";
                var EX_DH_NO = arrIds[i]["EX_DH_NO"];

                debugger;
                apidates = "   |    推送时间  :  " + date2 + "  返回值  :  " + apidate + "     |   ";
                //var a =apidate.indexOf("u8key")
                // if(apidate.indexOf("code") >  0){
                if (substr2[1] == "success") {
                    reds = "推送成功!";
                    reds2 = "success";
                    object.push({
                        ID: arrIds[i]["ID"], //样本ID
                        RETURN_VALUE: apidates,//返回值 
                        LIMS_TO_U8ID: substr[1], //u8单号
                        PUSH_STATE: "已推送", //更新状态
                    });
                } else {
                                       if (apidate.indexOf("生成材料出库单成功")>-1) {  
                         var substr3 = apidate.slice(apidate.indexOf("：")+1);
                         reds = "推送成功!";
                         reds2 = "success";
                         object.push({
                             ID: arrIds[i]["ID"], //样本ID
                             RETURN_VALUE: apidates,//返回值 
                             LIMS_TO_U8ID: substr3[1], //u8单号
                             PUSH_STATE: "已推送", //更新状态
                         });
                    }else if (apidate.indexOf("委外华开单号重复")>-1) { 
                        reds = "推送成功!";
                        reds2 = "success";
                        object.push({
                            ID: arrIds[i]["ID"], //样本ID
                            RETURN_VALUE: apidates,//返回值 
                            LIMS_TO_U8ID: substr3[1], //u8单号
                            PUSH_STATE: "已推送", //更新状态
                        });
                    }
                    else {
                        reds = "推送失败!";
                        reds2 = "error";
                        object.push({
                            ID: arrIds[i]["ID"], //样本ID
                            RETURN_VALUE: apidates,//返回值 
                            PUSH_STATE: "推送失败", //更新状态
                        });
                    }
                }
            }
   //         alertMsg(reds, reds2);
            debugger;
            var tableName = "BIO_OUT_SOURCING";
            var params = { tableName: tableName, objects: object }; //插入任务明细记录
            var url = "system/jdbc/save/batch/table";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url,
                ajaxData: params,
                ajaxAsync: false,
                succeed: function (result) {
                    if (result["code"] > 0) {
                        refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
                        // alertMsg("提交成功!", "success"); // funcExce(pathValue+"close");//关闭页面
                        flag = 0;
                    } else {
                        // alertMsg("提交失败!", "error");
                        flag = 0;
                    }
                },
            });
            refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
            //alertMsg("提交成功", "success"); // funcExce(pathValue+"close");//关闭页面
        } else {
            alertMsg("推送失败", "error");
            flag = 0;
        }
        //});
    };

    var opencx = function (ID) {
        var winOpts = {
            url: "biomarker/system/out/sourcing/add/addsee",
            title: "查看:U8返回信息.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }

    var pusherror = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        opencx(arrIds[0]);
    }
    var pusherrorcg = function () {
        var arrIds = getSelectData(gridName2Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        opencx(arrIds[0]);
    }

    var withdraw = function () {
        var arrIds = getSelectData(gridName2Grid);
        if (arrIds.length < 1) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        var object = [];
        for (var i = 0; i < arrIds.length; i++) {
            object.push({
                ID: arrIds[i], //主表ID
U8_DATA_CHECK: "N",
                PUSH_STATE: "待推送", //更新状态
            });
        }
 
        //更新状态
        var tableName = "BIO_OUT_SOURCING";
        var params = { "tableName": tableName, "objects": object, "where": { "ID": "${ID}" } }; //插入任务明细记录
        var url = "/system/jdbc/save/batch/table/where";
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    refreshGrid();

                    alertMsg("操作成功!", "success");
                } else {
                    alertMsg(" 操作失败!", "error");

                }
            },
        });
    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "open": open,
        "add": add,
        "edit": edit,
        "withdraw": withdraw,
        "pusherror": pusherror,
        "pusherrorcg": pusherrorcg,
        "edit2": edit2,
        "pushs": pushs,
        "openviews": openviews,
        "refreshGrid": refreshGrid,
        "deleteInfo": deleteInfo,
        "sumbit": sumbit,//提交方法
        "callBack": callBack,//回调方法
        "importData": importData,
    });
});