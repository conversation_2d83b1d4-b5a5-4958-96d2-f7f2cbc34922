$(document).ready(function () {
	var pathValue = "biomarker-system-out-sourcing-add-addsee";
	var paramsValue;
	var gridNameGrid;
      debugger;
	// 主单数据库表
	var initData = function () {
		return {tableName:"BIO_OUT_SOURCING"};
	};

	// 页面初始化
var init = function (params) {
		paramsValue = params;
		getInfo("form", pathValue, params);
                 var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
                getInfo("form",pathValue,params,url);//传入id	
	 	init2();
	};	
	var refreshGrid = function () {
		if (gridNameGrid) {
			gridNameGrid.dataSource.read(); //重新读取--刷新
		}
	};

	var callBack = function () {
		refreshGrid();
	};
	// 保存
	var submit = function () {
		debugger;
		var g = getGridItemsData(gridNameGrid);
		if (g.length == 0) {
			alertMsg("至少要有一条材料明细");
			return;
		}

		formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					if ($("#ID" + pathValue).val() == "") {
						$("#ID" + pathValue).val(result["ID"]);
						isAdd = true;
						updateListMx();
					}
					alertMsg("保存主单成功", "success");
					funcExce(pathValue+"pageCallBack");//执行回调
                    funcExce(pathValue+"close");//关闭页面
					
				} else {
					alertMsg("保存主单失败", "error");
				}
			},
		});
	};

	// 回填添加到列表上
	var addSub = function (wlDataList) {
		for (var i = 0; i < wlDataList.length; i++) {
			gridNameGrid.dataSource.add(wlDataList[i]);
		}
	}
	funcPushs(pathValue, {
		"init": init,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"submit": submit,
		"addSub": addSub,
	});
});