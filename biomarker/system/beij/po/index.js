$(document).ready(function () {
    var pathValue = "biomarker-system-beij-po-index";

    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {};
    }

    var gridNameGrid;
    var flag = 0;
    debugger;
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            //{name:"add",target:"add"},
            { name: "pushs", target: "pushs", title: "推送" },
            { name: "pusherror", target: "pusherror", title: "查看U8返回信息" },
            //  {name:"delete",target:"deleteInfo"},
            //	{name:"import",target:"importData",title:"导入"},
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_BIO_BJ_PRODUCTION_ORDER", "objects": [["待推送", "推送失败"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ID = e.data.ID;
                var MODID = e.data.MODID;
                debugger;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": "query_BIO_BJ_PRODUCTION_ALLOCATEMX", "objects": [[MODID]] }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ID + "_" + pathValue, subGrid_N_JSON);
                //gridNameS1.push(subGrid_N);
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);//初始化表格的方法
        init2();
    }
    var init2 = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "pusherror", target: "pusherrorcg", title: "查看U8返回信息" },
            { name: "edit", target: "withdraw", title: "撤回" },
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_BIO_BJ_PRODUCTION_ORDER", "objects": [["已推送"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ID = e.data.ID;
                var MODID = e.data.MODID;
                debugger;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": "query_BIO_BJ_PRODUCTION_ALLOCATEMX", "objects": [[MODID]] }
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ID + "_" + pathValue, subGrid_N_JSON);
                //gridNameS1.push(subGrid_N);
            }
        };
        gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    var add = function () {
        var winOpts = {
            url: "${openPath}",
            title: "新增:北京U8生产订单.."
        };
        openWindow(winOpts);
    }

    var open = function (ID) {
        var winOpts = {
            url: "biomarker/system/beij/po/add/addsee",
            title: "修改:北京U8生产订单.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }

    var edit = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }

    var pusherror = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }

    var pusherrorcg = function () {
        var arrIds = getSelectData(gridName2Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }

    var sumbit = function () {
        formSubmit({
            formId: "form",
            pathValue: pathValue
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
    }

    var deleteInfo = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        var params = { "tableName": "table", "ids": arrIds };
        var url = "system/jdbc/delete/batch/table";
        deleteGridDataByIds(url, params, refreshGrid);
    }

    //表格导入
    var importData = function (componentId) {
        var grid = gridNameGrid;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                "template": grid,//单表导入
                "tableName": "table",
            }
        });
    }

    var pushs = function () {
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        if (flag == 1) {
            alertMsg("正在推送中，请勿重复点击!");
            return;
        }
        if (flag == 0) {
            flag = 1;
        }
        var object = [];
        var Ids = [];
        //confirmMsg("确认", "确定要对选中的记录进行推送吗?", "warn", function () {
        for (var i = 0; i < arrIds.length; i++) {
            if (Ids.indexOf(arrIds[i]["MOID"]) < 0) {
                Ids.push(arrIds[i]["MOID"]);//样本ID U8XML_URL
            }
        }
        var params = { "Ids": Ids, "ts": "手动模式", "urlhtml": U8XML_URL }; //插入任务明细记录
        var url = "system/jdbc/bj/pop";
        var jsondate;
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                jsondate = result

            },
        });

        var reds = "";
        var reds2 = "";
        if (jsondate["code"] > 0) {
            for (var i = 0; i < Ids.length; i++) {
                //var keys2;
                //
                var myDate = new Date();
                var date2 = myDate.toLocaleString();
                var apidate = jsondate["apiData"][i]["apiData"];
                var substr = apidate.match(/key=\"(\S*)\" succeed/);
                var reds = "";
                var reds2 = "";
                //                var rows4 = "";
                var apidates = "";
                /*                var EX_DH_NO = arrIds[i]["EX_DH_NO"];
                                $.fn.ajaxPost({
                                    ajaxUrl: "system/jdbc/query/one/table",
                                    ajaxType: "post",
                                    ajaxAsync: false,
                                    ajaxData: { "query": "query_bj_fhz_list", "objects": [[EX_DH_NO]] },
                                    succeed: function (rs) {
                                        //console.log(rs);				
                                        rows4 = rs;
                                    }
                                });
                                debugger;
                                */
                apidates = "   |    推送时间  :  " + date2 + "  返回值  :  " + apidate + "     |   ";
                var apidate = jsondate["apiData"][i]["apiData"];
                var substr = apidate.match(/key=\"(\S*)\" succeed/);
                //var a =apidate.indexOf("u8key")
                if (apidate.indexOf("u8key") >= 0) {
                    reds = "推送成功!";
                    reds2 = "success";
                    object.push({
                        //                        ID: arrIds[i]["ID"], //样本ID
                        MOID: Ids[i], //主表ID
                        RETURN_VALUE: apidates,//返回值 
                        LIMS_TO_U8ID: substr[1], //u8单号
                        PUSH_STATE: "已推送", //更新状态
                    });
                } else {
                    if (apidate.indexOf("单号重复") > -1) {
                        reds = "推送成功!";
                        reds2 = "success";
                        object.push({
                            //                            ID: arrIds[i]["ID"], //样本ID
                            MOID: Ids[i], //主表ID
                            RETURN_VALUE: apidates,//返回值 
                            LIMS_TO_U8ID: substr[1], //u8单号
                            PUSH_STATE: "已推送", //更新状态
                        });
                    } else {
                        reds = "推送失败!";
                        reds2 = "error";
                        object.push({
                            //                            ID: arrIds[i]["ID"], //样本ID
                            MOID: Ids[i], //主表ID
                            RETURN_VALUE: apidates,//返回值 
                            PUSH_STATE: "推送失败", //更新状态
                        });
                    }
                }
                var pushobj = [];
                pushobj.push({
                    "API_TYPE": "北京U8生产订单",
                    "PUSH_DATE": sysNowTimeFuncParams["sysNowTime"],
                    "MOID": Ids[i],
                    "API_RS": apidate,
                    "PUSH_MAN": getLimsUser()["name"],
                    "PUSH_MODE": "手动推送",
                });
            }
            alertMsg(reds, reds2);
            debugger;
            //更新状态
            var tableName = "BIO_BJ_PRODUCTION_ORDER";
            var params = { "tableName": tableName, "objects": object, "where": { "MOID": "${MOID}" } }; //插入任务明细记录
            var url = "/system/jdbc/save/batch/table/where";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url,
                ajaxData: params,
                ajaxAsync: false,
                succeed: function (result) {
                    if (result["code"] > 0) {
                        refreshGrid();
                        flag = 0;
                        // alertMsg("提交成功!", "success"); 
                    } else {
                        // alertMsg("提交失败!", "error");
                        flag = 0;
                    }
                },
            });
            //日志记录
            var tableName1 = "BIO_U8API_LOG";
            var params1 = { "tableName": tableName1, "objects": pushobj }; //插入任务明细记录
            var url1 = "/system/jdbc/save/batch/table";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url1,
                ajaxData: params1,
                ajaxAsync: false,
                succeed: function (result) {

                },
            });

            refreshGrid();

        } else {
            alertMsg("推送失败", "error");
        }
        //});
    };
    var withdraw = function () {
        var arrIds = getGridSelectData(gridName2Grid);
        if (arrIds.length < 1) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        var object = [];
        var Ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            if (Ids.indexOf(arrIds[i]["MOID"]) < 0) {
                Ids.push(arrIds[i]["MOID"]);//样本ID U8XML_URL
            }
        }
        for (var i = 0; i < Ids.length; i++) {
            object.push({
                MOID: Ids[i], //主表ID
U8_DATA_CHECK: "N",
                PUSH_STATE: "待推送", //更新状态
            });
        }
        //更新状态
        var tableName = "BIO_BJ_PRODUCTION_ORDER";
        var params = { "tableName": tableName, "objects": object, "where": { "MOID": "${MOID}" } }; //插入任务明细记录
        var url = "/system/jdbc/save/batch/table/where";
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    refreshGrid();

                    alertMsg("操作成功!", "success");
                } else {
                    alertMsg(" 操作失败!", "error");

                }
            },
        });





    }
    funcPushs(pathValue, {
        "initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
        "init": init,//初始化方法-在加载完初始化数据之后执行
        "open": open,
        "add": add,//打开添加表单
        "edit": edit,
        "refreshGrid": refreshGrid,
        "deleteInfo": deleteInfo,
        "sumbit": sumbit,//提交方法
        "callBack": callBack,//回调方法
        "pushs": pushs,
        "withdraw": withdraw,
        "pusherror": pusherror,
        "pusherrorcg": pusherrorcg,
        "importData": importData,
    });
});