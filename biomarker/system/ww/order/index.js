$(document).ready(function () {
    var pathValue = "biomarker-system-ww-order-index";
    var initData = function () {
        return {};
    }
    var gridNameGrid;
    var gridName2Grid;
    var flag = 0;
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            // {name:"add",target:"add"},
            //{name:"edit",target:"edit",title:"提交"},
            { name: "pushs", target: "pushs", title: "推送" },
            { name: "edit", target: "modifyTime", title: "修改时间" },
            { name: "pusherror", target: "pusherror", title: "查看U8返回信息" },
            { name: "edit", target: "bbExport", title: "报表导出" },
            //{name:"delete",target:"deleteInfo"}
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_WW_ORDER_view", "objects": [["待推送", "推送失败"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "CODE") {
                        setJsonParam(cols[i], "template", getTemplate("#= CODE #", "funcExce(\'" + pathValue + "openviews\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
        init2();
    }
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "pusherror", target: "pusherrorcg", title: "查看U8返回信息" },
            { name: "edit", target: "withdraw", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_WW_ORDER_view", "objects": [["已推送"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "CODE") {
                        setJsonParam(cols[i], "template", getTemplate("#= CODE #", "funcExce(\'" + pathValue + "openviews\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson);
    }
    var add = function () {
        var winOpts = {
            url: "biomarker/interface/projectinfo/rd/add/add",
            title: "新增:常规则项立项信息.."
        };
        openWindow(winOpts);
    }

    var open = function (ID) {
        var winOpts = {
            url: "biomarker/interface/projectinfo/rd/add/add",
            title: "修改:委外订单子表信息.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }
    var openviews = function (ID) {
        var winOpts = {
            url: "biomarker/system/ww/order/ckindex/ckindex",
            title: "查看:委外订单子表信息.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }
    var edit = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }

    var edit2 = function () {
        var g = getSelectData(gridNameGrid);
        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }

        var objectupmain = [];
        var name = getLimsUser()["name"];
        var time = sysNowTimeFuncParams["sysNowTime"];
        for (var i = 0; i < g.length; i++) {
            //主单状态
            objectupmain.push({
                "ID": g[i],
                "EXAMINE_MAN": name,//审核人
                "EXAMINE_TIME": time,//审核时间
                "EXAMINE_FLAG": "通过"//审核状态
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var parammain = { "tableName": "BIO_PROJECT_INFO", "objects": objectupmain };
        putAddOrUpdata(urlsend, parammain, "是", "执行单状态更新:");
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var sumbit = function () {
        formSubmit({
            formId: "form",
            pathValue: pathValue
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
        if (gridName2Grid) {
            gridName2Grid.dataSource.read();
        }
    }

    var deleteInfo = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
            var params = { "tableName": "BIO_PROJECT_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        })
    }

    //表格导入
    var importData = function (componentId) {
        var grid = gridNameGrid;
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                "template": grid,//单表导入
                "tableName": "BIO_PROJECT_INFO",
            }
        });
    }
    var myDate = new Date();
    var pushs = function () {
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        if (flag == 1) {
            alertMsg("正在推送中，请勿重复点击!");
            return;
        }
        if (flag == 0) {
            flag = 1;
        }
        var object = [];
        var Ids = [];
        //confirmMsg("确认", "确定要对选中的记录进行推送吗?", "warn", function () {
        for (var i = 0; i < arrIds.length; i++) {
            Ids.push(arrIds[i]["ID"]);//样本ID
        }
        var params = { "Ids": Ids, "ts": "手动模式", "urlhtml": U8XML_URL }; //插入任务明细记录
        var url = "system/jdbc/ww/orderpush";
        var jsondate;
        debugger;
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                jsondate = result

            },
        });
        debugger;
        var reds = "";
        var reds2 = "";
        if (jsondate["code"] > 0) {
            for (var i = 0; i < arrIds.length; i++) {
                //var keys2;
                //
                var myDate = new Date();
                var date2 = myDate.toLocaleString();
                var apidate = jsondate["apiData"][i]["apiData"];
                var substr = apidate.match(/key=\"(\S*)\" succeed/);
                var reds = "";
                var reds2 = "";
                var rows4 = "";
                var apidates = "";
                var EX_DH_NO = arrIds[i]["EX_DH_NO"];
                $.fn.ajaxPost({
                    ajaxUrl: "system/jdbc/query/one/table",
                    ajaxType: "post",
                    ajaxAsync: false,
                    ajaxData: { "query": "query_ww_dd_fhz_list", "objects": [[EX_DH_NO]] },
                    succeed: function (rs) {
                        //console.log(rs);				
                        rows4 = rs;
                    }
                });
                debugger;
                apidates = rows4["rows"][0]["RETURN_VALUE"] + "   |    推送时间  :  " + date2 + "  返回值  :  " + apidate + "     |   ";
                var apidate = jsondate["apiData"][i]["apiData"];
                var substr = apidate.match(/key=\"(\S*)\" succeed/);
                if (apidate.indexOf("u8key") >= 0 || apidate.indexOf( "单据号重复") >= 0) {
                    reds = "推送成功!";
                    reds2 = "success";
                    object.push({
                        ID: arrIds[i]["ID"], //样本ID
                        //GENERATE_DATE: myDate.toLocaleString(), //时间
                        RETURN_VALUE: apidate,//返回值 
                        LIMS_TO_U8ID: substr[1], //u8单号
                        PUSH_STATE: "已推送", //更新状态
                    });
                } else {
                    reds = "推送失败!";
                    reds2 = "error";

                    object.push({
                        ID: arrIds[i]["ID"], //样本ID
                        RETURN_VALUE: apidate,//返回值
                        PUSH_STATE: "推送失败", //更新状态
                    });
                }

            }
            alertMsg(reds, reds2);
            debugger;
            var tableName = "BIO_WW_ORDER";
            var params = { tableName: tableName, objects: object }; //插入任务明细记录
            var url = "system/jdbc/save/batch/table";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url,
                ajaxData: params,
                ajaxAsync: false,
                succeed: function (result) {
                    if (result["code"] > 0) {
                        refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
                        // alertMsg("提交成功!", "success"); // funcExce(pathValue+"close");//关闭页面
                    } else {
                        //alertMsg("提交失败!", "error");
                    }
                },
            });
            refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
            flag = 0;
            //alertMsg("提交成功", "success"); // funcExce(pathValue+"close");//关闭页面
        } else {
            alertMsg("推送失败", "error");
            flag = 0;
        }
        //});
    };
    var openck = function (ID) {
        var winOpts = {
            url: "biomarker/system/ww/order/add/addsee",
            title: "查看:U8返回信息.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }
    var pusherror = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        openck(arrIds[0]);
    }
    var pusherrorcg = function () {
        var arrIds = getSelectData(gridName2Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        openck(arrIds[0]);
    }
    var modifyTime = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/system/ww/order/modifyTime/modifyTime",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "查看:U8返回信息.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"], "code": arrIds[0]["CODE"], });//传递id
    }
    var bbExport = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/system/ww/order/bbExport/bbExport",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts);//传递id
    }
    var withdraw = function () {
        var arrIds = getSelectData(gridName2Grid);
        if (arrIds.length < 1) {
            alertMsg("请至少选择一条数据进行推送!");
            return;
        }
        var object = [];
        for (var i = 0; i < arrIds.length; i++) {
            object.push({
                ID: arrIds[i], //主表ID
                U8_DATA_CHECK: "N",
                PUSH_STATE: "待推送", //更新状态
            });
        }

        //更新状态
        var tableName = "BIO_WW_ORDER";
        var params = { "tableName": tableName, "objects": object, "where": { "ID": "${ID}" } }; //插入任务明细记录
        var url = "/system/jdbc/save/batch/table/where";
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    refreshGrid();

                    alertMsg("操作成功!", "success");
                } else {
                    alertMsg(" 操作失败!", "error");

                }
            },
        });
    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "open": open,
        "add": add,
        "pusherror": pusherror,
        "pusherrorcg": pusherrorcg,
        "openck": openck,
        "bbExport": bbExport,
        "edit": edit,
        "withdraw": withdraw,
        "edit2": edit2,
        "openviews": openviews,
        "refreshGrid": refreshGrid,
        "deleteInfo": deleteInfo,
        "sumbit": sumbit,//提交方法
        "callBack": callBack,//回调方法
        "pushs": pushs,
        "modifyTime": modifyTime,
        "importData": importData,
    });
});