$(document).ready(function () {
	var pathValue = "biomarker-system-ww-order-ckindex-ckindex";
	var paramsValue;
	var gridNameGrid;
      debugger;
	// 主单数据库表
	var initData = function () {
		return {tableName:"BIO_WW_ORDER"};
	};

	// 页面初始化
var init = function (params) {
		//params["SOURCE"] = "研发结果";
		paramsValue = params;
		getInfo("form", pathValue, params);
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id	
		init2();
	};	

	var init2=function(params){
		 /**
		  * 列表-按钮-定义
		  */
		 var toolbar=getButtonTemplates(pathValue,[
		 //{name:"add",target:"add"},
	       //  {name:"edit",target:"edit"},
		//  {name:"delete",target:"deleteInfo"},
		//	{name:"import",target:"importData",title:"导入"},
	     ]);//工具条
		 //请求参数
                  var ORDER_ID =$("#ID"+pathValue ).val()
debugger;
		 var gridNameGridJson={
			 url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
			 sort: "",//排序
			 toolbar: toolbar,
			 read:{"query":"query_BIO_WW_ORDERZB_view","objects":[[ORDER_ID]]},
			 headerFilter:function(cols,i){	 },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
                                 var ID = e.data.ID;
				var ORDERZB_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: "",
					height: 320,
					read: { "query": "query_BIO_WW_ORDERZBMX_view", "objects": [[ORDERZB_ID]] }
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ID + "_" + pathValue, subGrid_N_JSON);
				//gridNameS1.push(subGrid_N);
			}
		 };
		 gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
	}
	// 从页面删除数据
	var deleteMaterial = function () {
		var arrSelect = getSelectData(gridNameGrid);
		var oldlist = getGridItemsData(gridNameGrid);
		if (arrSelect.length == 0) {
			alertMsg("请至少选择一条数据进行删除操作!");
			return;
		}
		if (oldlist.length - arrSelect.length < 1) {
			alertMsg("至少要有一条物料明细,不能全部删除");
			return;
		}
		confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function () {
			for (var i = 0; i < arrSelect.length; i++) {
				var params = { tableName: "BIO_RD_MATERIAL_DELIVERY_MX", ids: arrSelect[i]};
				var url = "system/jdbc/delete/batch/table";
				deleteGridDataByIds(url, params, refreshGrid);
			}
		});
	};


	var refreshGrid = function () {
		if (gridNameGrid) {
			gridNameGrid.dataSource.read(); //重新读取--刷新
		}
	};

	var callBack = function () {
		refreshGrid();
	};


	// 删除主单对应列表在数据库的数据
	var isExist = function () {
		var params = {
			tableName: "BIO_RD_MATERIAL_DELIVERY_MX",
			where: { M_ID: $("#ID" + pathValue).val(), },
		};
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/delete/one/table/where",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					updateListMx();
				} else {
					alertMsg("删除旧列表失败", "error");
				}
			},
		});
	};

	// 主单在数据库是否对应有数据 有删除
	var deleteMaterials = function () {
		var params = {
			query: "query_BIO_RD_MATERIAL_DELIVERY_view",
			objects: [$("#ID" + pathValue).val()],
		}; //查询列表
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxData: params,
			ajaxAsync: false,
			succeed: function (result) {
				if (result["code"] > 0 && result["rows"].length > 0) {
					isExist();
				} else {
					updateListMx();
				}
			},
		});
	};
	// 保存
	var submit = function () {
		debugger;
		var g = getGridItemsData(gridNameGrid);
		if (g.length == 0) {
			alertMsg("至少要有一条材料明细");
			return;
		}

		formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					if ($("#ID" + pathValue).val() == "") {
						$("#ID" + pathValue).val(result["ID"]);
						isAdd = true;
						updateListMx();
					}
					alertMsg("保存主单成功", "success");
					funcExce(pathValue+"pageCallBack");//执行回调
                    funcExce(pathValue+"close");//关闭页面
					
				} else {
					alertMsg("保存主单失败", "error");
				}
			},
		});
	};

	// 回填添加到列表上
	var addSub = function (wlDataList) {
		for (var i = 0; i < wlDataList.length; i++) {
			gridNameGrid.dataSource.add(wlDataList[i]);
		}
	}
	funcPushs(pathValue, {
		"init": init,
		"deleteMaterial": deleteMaterial,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"submit": submit,
		"addSub": addSub,
	});
});