$(document).ready(function () {
    var pathValue = "biomarker-system-ww-order-modifyTime-modifyTime";
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "表名称是什么呢"
        };
    }
    var paramsValue;
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        paramsValue = params;
 
    }


    var submit = function () {
        debugger;
        var BTIME = $("#BTIME" + pathValue).val();   //开始时间
        var ETIME = $("#ETIME" + pathValue).val();   //排单对象
        if (BTIME == null || ETIME == null ||BTIME == "" || ETIME == "") {
            alertMsg("实验开始结束时间不能为空！");
            return
        }
        if (BTIME > ETIME) {
            alertMsg("实验开始时间不能大于结束时间！");
            return
        }
        var objectWwdd=[]; 
        objectWwdd.push({
            "ID":paramsValue["ID"],
            "PUSH_STATE":"待推送"
        })
        var wwddz;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_BIO_WW_ORDERZB_view", "objects": [paramsValue["ID"]]},
            succeed: function (rs) {
                wwddz = rs.rows;             
            }
        });
        var objectWwddz=[];           
        var wwddzID =[];
        for (let index = 0; index < wwddz.length; index++) {
            wwddzID.push(wwddz[index]["ID"]);
            objectWwddz.push({
                "ID":wwddz[index]["ID"],
                "ARRIVEDATE":ETIME,
                "STARTDATE":BTIME,
            })
        }
        var wwddzMX;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_BIO_WW_ORDERZBMX_view", "objects": [wwddzID]},
            succeed: function (rs) {
                wwddzMX = rs.rows;             
            }
        });
        var objectWwddzMX=[];     
        for (let index = 0; index < wwddzMX.length; index++) {
            objectWwddzMX.push({
                "ID":wwddzMX[index]["ID"],
                "REQUIREDDATE":ETIME,
            })
        }
        var clck;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_BIO_OUT_SOURCING_view","objects":[],"search": { "TRADE_NO": [paramsValue["code"]] } },
            succeed: function (rs) {
                clck = rs.rows;             
            }
        });
        var objectClck=[];     
        for (let index = 0; index < clck.length; index++) {
            objectClck.push({
                "ID":clck[index]["ID"],
                "DEFINE8":ETIME,
                "DEFINE7":BTIME,
            })
        }
        var urlsend="system/jdbc/save/batch/table";

        var paramsadd={"tableName":"BIO_WW_ORDER","objects":objectWwdd};
        putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");
 
        var paramsadd={"tableName":"BIO_WW_ORDERZB","objects":objectWwddz};
        putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");
 
        var paramsadd={"tableName":"BIO_WW_ORDERZBMX","objects":objectWwddzMX};
        putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");

        var paramsadd={"tableName":"BIO_OUT_SOURCING","objects":objectClck};
        putAddOrUpdata(urlsend,paramsadd,"是","推入下一步实验任务");

    }
      //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
    $.fn.ajaxPost({
        ajaxType:"post",
        ajaxUrl:urls,
        ajaxData:inobjjson,
        succeed:function(result){
            if(result["code"]>0){
                if(isDoCallBack=="是"){
                  alertMsg("提示:操作成功!");
                  funcExce(pathValue+"pageCallBack");//执行回调
                  funcExce(pathValue+"close");//关闭页面
                }
            }else{
                alertMsg(errMsg+"操作失败!");
            }
        }
    });
}

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});