$(document).ready(function() {
    var pathValue="biomarker-system-gmp-outs-index";
    var initData=function(){
        return {};
    }
    var gridNameGrid;
    var init=function(params){
         var toolbar=getButtonTemplates(pathValue,[
            // {name:"add",target:"add"},
             //{name:"edit",target:"edit",title:"提交"},
            // {name:"edit",target:"edit2",title:"提交"},
             //{name:"delete",target:"deleteInfo"}
         ]);
         var gridNameGridJson={
             url: "system/jdbc/query/one/table",
             sort: "",
             toolbar: toolbar,
             read:{"query":"query_BIO_GNP_OUTS_view","objects":[]},
             headerFilter:function(cols,i){
                 if(i){
                     if(cols[i]["field"]&&cols[i]["field"]=="TRADE_NO"){
                         setJsonParam(cols[i],"template",getTemplate("#= TRADE_NO #","funcExce(\'"+pathValue+"openviews\',\'#= ID #\');","txt"));
                     }
                 }
             }
         };
         gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }
     var add=function(){
         var winOpts={
             url:"biomarker/interface/projectinfo/rd/add/add",
             title:"新增:材料出库单接口.."
         };
         openWindow(winOpts);
     }
 
     var open=function(ID){
         var winOpts={
             url:"biomarker/interface/projectinfo/rd/add/add",
             title:"修改:材料出库单接口.."
         };
         openWindow(winOpts,{"ID":ID});//传递id
     }
     var openviews=function(ID){
         var winOpts={
             url:"biomarker/system/gmp/outs/xq/xq",
             title:"查看:材料出库单接口信息.."
         };
         openWindow(winOpts,{"ID":ID});//传递id
     }
     var edit=function(){
         var arrIds=getSelectData(gridNameGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行修改!");
             return ;
         }else if(arrIds.length!=1){
             alertMsg("请只选择一条数据进行修改操作!");
             return ;
         }
         open(arrIds[0]);
      }
     
     var edit2=function(){
         var g=getSelectData(gridNameGrid);
         if(g.length==0){
             alertMsg("请至少选择一条数据进行操作!");
             return ;
         }
         
         var objectupmain=[];
         var name=getLimsUser()["name"];
         var time=sysNowTimeFuncParams["sysNowTime"];
         for(var i=0;i < g.length;i++){
             //主单状态
             objectupmain.push({
                 "ID":g[i],
                "EXAMINE_MAN":name,//审核人
                "EXAMINE_TIME":time,//审核时间
                "EXAMINE_FLAG":"通过"//审核状态
                   });
           }
         var urlsend="system/jdbc/save/batch/table";
         var parammain={"tableName":"BIO_PROJECT_INFO","objects":objectupmain};
         putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");  
      }
     //批量执行插入
     var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:urls,
             ajaxData:inobjjson,
             succeed:function(result){
                 if(result["code"]>0){
                     if(isDoCallBack=="是"){
                         alertMsg("提示:操作成功!");
                         refreshGrid();
                     }
                 }else{
                     alertMsg(errMsg+"操作失败!");
                 }
             }
         });
     }
      
      var sumbit=function(){
         formSubmit({
             formId:"form",
             pathValue:pathValue
         });
      }
      
      var callBack=function(){
         refreshGrid();
      };
 
      var refreshGrid=function(){
         if(gridNameGrid){
             gridNameGrid.dataSource.read();
         }
         if(gridName1Grid){
             gridName1Grid.dataSource.read();
         }
      }
 
      var deleteInfo=function(){
         var arrIds=getSelectData(gridNameGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行删除操作!");
             return ;
         }
      confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function() {
         var params={"tableName":"BIO_PROJECT_INFO","ids":arrIds};
         var url="system/jdbc/delete/batch/table";
         deleteGridDataByIds(url,params,refreshGrid);
      })
      }
 
     //表格导入
     var importData=function(componentId){
         var grid=gridNameGrid;
         openComponent({
             name:"导入数据",//组件名称
             componentId:componentId,
             params:{
                 "template":grid,//单表导入
                 "tableName":"BIO_PROJECT_INFO",
             }
         });
     }
 
      funcPushs(pathValue,{
          "initData":initData,
          "init":init,
          "open":open,
          "add":add,
          "edit":edit,
          "edit2":edit2,
          "openviews":openviews,
          "refreshGrid":refreshGrid,
          "deleteInfo":deleteInfo,
          "sumbit":sumbit,//提交方法
          "callBack":callBack,//回调方法
          "importData":importData,
      });
 });