$(document).ready(function() {
   var pathValue="biomarker-basics-index-indexmcd-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"delete",target:"deleteInfo"},
	        {name:"import",target:"importData",title:"导入"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"QueryIndexList-MCD","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
       init1();
   }
    var init1=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"add1"},
            {name:"edit",target:"edit1"},
            {name:"delete",target:"deleteInfo1"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"QueryIndexBoardList","objects":[],"search": {"INDEX_TYPE":"MCD"}},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="BOARD_NAME"){
                        setJsonParam(cols[i],"template",getTemplate("#= BOARD_NAME #","funcExce(\'"+pathValue+"open1\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        gridName1Grid = initKendoGrid("#gridName1Grid"+pathValue,gridNameGridJson);
   }
   
    var add=function(){
        var winOpts={
            url:"biomarker/basics/index/indexinfo/add/add",
            title:"新增:index库.."
        };
        openWindow(winOpts);
    }

    var open=function(ID){
        var winOpts={
            url:"biomarker/basics/index/indexinfo/add/add",
            title:"修改:index库.."
        };
        openWindow(winOpts,{"ID":ID});
    }

    var edit=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
		open(arrIds[0]);
     }
   
     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_LIB_INDEX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);

     }

    //表格导入
    var importData=function(componentId){
        var grid=gridNameGrid;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":grid,//单表导入
                "tableName":"BIO_LIB_INDEX",
            }
        });
    }
    var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
        if(gridName1Grid){
            gridName1Grid.dataSource.read();
        }
     }
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
    var open1=function(ID){
        var winOpts={
            url:"biomarker/basics/index/indexmcd/addboard/addboard",
            title:"修改:index板.."
        };
       openWindow(winOpts,{"ID":ID});
    }
    var add1=function(){
        var winOpts={
            url:"biomarker/basics/index/indexmcd/addboard/addboard",
            title:"新增:index板.."
        };
       openWindow(winOpts,{"ID":""});
    }
    var edit1=function(){
        var arrIds=getSelectData(gridName1Grid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
		open1(arrIds[0]);
     }
     var deleteInfo1=function(){
         var arrIds=getSelectData(gridName1Grid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行删除操作!");
             return ;
         }
         var params={"tableName":"BIO_LIB_BOARD_INDEX","ids":arrIds};
         var url="system/jdbc/delete/batch/table";
         deleteGridDataByIds(url,params,refreshGrid);

         var url2="system/jdbc/delete/one/table/where";
          var paramsmx= {"tableName":"BIO_LIB_BOARD_INDEX_MX","where":{"BOARD_ID":arrIds}};
          putAddOrUpdata(url2,paramsmx,"否","");
      }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "open":open,
         "add":add,
         "edit":edit,
        "open1":open1,
        "add1":add1,
        "edit1":edit1,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "deleteInfo1":deleteInfo1,
         "callBack":callBack,
         "importData":importData,
     });
});