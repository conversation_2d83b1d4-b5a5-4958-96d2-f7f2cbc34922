$(document).ready(function() {
    var pathValue="biomarker-basics-index-indexmcd-addboard-addboard";

    var initData=function(){
        return {
            tableName:"BIO_LIB_BOARD_INDEX"
        };
    }
	
	var paramsValue;
	var gridNameGrid;
    var init=function(params){
	    paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addSel",title:"选择index"},
            {name:"excel",target:"importData1",title:"导入/模板"},
            {name:"delete",target:"deleteInfo",title:"移除明细"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           height: fullh-300,
           toolbar: toolbar,
           read:{"query":"QueryIndexBoardMxList-MCD","objects":[paramsValue["ID"]]},
		   headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="INDEX_NAME"){
                       setJsonParam(cols[i],"template",getTemplate("#= INDEX_NAME #","funcExce(\'"+pathValue+"editIndex\',\'#= ID #\');","txt"));
                    }
                }
            }
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }
	
     var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");
                        getInfo("form",pathValue,{ID:result["ID"]});
                        paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    
     var addSel=function(){
         if(paramsValue["ID"]==""){
             alertMsg("请先保存板号信息!");
             return ;
         } 
 	   	var winOpts={
 	   		   url:"biomarker/basics/index/indexmcd/addSel/addSel",
 	           title:"选择Index..",
			currUrl:replacePathValue(pathValue)
 	     };
 	   openWindow(winOpts,{"BOARD_ID":paramsValue["ID"]});
     }
     
     var editIndex=function(ID){
 	   	var winOpts={
 	         url:"biomarker/basics/index/indexmcd/editIndex/editIndex",
			currUrl:replacePathValue(pathValue),
 	           title:"编辑Index.."
 	     };
 	   openWindow(winOpts,{"ID":ID});
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_LIB_BOARD_INDEX_MX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }
     
         //表格导入
	var importData1=function(componentId){
              var arrIds=getSelectData(gridNameGrid);
	        if(arrIds.length==0){
	        	alertMsg("请至少选择一条记录进行操作!");
	        	return;
	        }

	openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                template:function(p,n){
                    return exportAndImportData({
                        expKey:"A",
                        tableName:"BIO_LIB_BOARD_INDEX_MX",
                        requestData:{
                            ajaxData:{"query":"QueryIndexBoardMxList-MCD","size":5000,"objects":[[paramsValue["ID"]]],"search":{"ID":arrIds}},
                        },
                        params:p,
                        name:n,
                    });
                }
            },
            callBack:refreshGrid
        });
 }
	  
     var callBack=function(){
         refreshGrid();
      }

      var refreshGrid=function(){
         if(gridNameGrid){
             gridNameGrid.dataSource.read({
             	"objects":[paramsValue["ID"]]
             });
         }
      }
	funcPushs(pathValue,{
		"initData":initData,
		"init":init,
		"submit":submit,
		"addSel":addSel,
		"editIndex":editIndex,
		"importData1":importData1,
		"deleteInfo":deleteInfo,
		"refreshGrid":refreshGrid,
		"callBack":callBack,
	});
 
 });