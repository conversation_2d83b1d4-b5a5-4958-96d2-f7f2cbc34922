$(document).ready(function() {
    var pathValue="biomarker-basics-index-indexinfo-addSel-addSel";
	var paramsValue;
	var gridNameGrid;
    var init=function(params){
    	paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"doAdd",title:"确认选择"},
       ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"QueryIndexList","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }
	
     var doAdd=function(){
         if(paramsValue["BOARD_ID"]==""){
             alertMsg("请板号信息丢失,请联系管理员!");
             return ;
         } 
      var g=getGridSelectData(gridNameGrid);    
        if(g.length==0){
            alertMsg("请至少选择一条数据进行添加样本操作!");
            return ;
        }
        var object=[];
        for(var i=0;i < g.length;i++){
        	object.push({
                    "BOARD_ID":paramsValue["BOARD_ID"],//关联板ID
                    "INDEX_NAME":g[i]["INDEX_NAME"],//index名称
                    "INDEX_ID":g[i]["ID"] 
                });
        }
       var params={"tableName":"BIO_LIB_BOARD_INDEX_MX","objects":object};
       var url="system/jdbc/save/batch/table";
       putAddOrUpdata(url,params,"是","");
     }
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		  funcExce(pathValue+"pageCallBack");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
	funcPushs(pathValue,{
		"init":init,
		"doAdd":doAdd,
	});
 
 });