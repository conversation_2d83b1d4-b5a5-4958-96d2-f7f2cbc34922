$(document).ready(function() {
   var pathValue="biomarker-basics-index-indexpb-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"delete",target:"deleteInfo"},
	        {name:"import",target:"importData",title:"导入"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"QueryIndexList-PBMCD","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }
   
    var add=function(){
        var winOpts={
            url:"biomarker/basics/index/indexinfo/add/add",
            title:"新增:index库.."
        };
        openWindow(winOpts);
    }

    var open=function(ID){
        var winOpts={
            url:"biomarker/basics/index/indexinfo/add/add",
            title:"修改:index库.."
        };
        openWindow(winOpts,{"ID":ID});
    }

    var edit=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
		open(arrIds[0]);
     }
   
     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_LIB_INDEX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);

     }

    //表格导入
    var importData=function(componentId){
        var grid=gridNameGrid;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":grid,//单表导入
                "tableName":"BIO_LIB_INDEX",
            }
        });
    }
    var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
        if(gridName1Grid){
            gridName1Grid.dataSource.read();
        }
     }
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "open":open,
         "add":add,
         "edit":edit,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "callBack":callBack,
         "importData":importData,
     });
});