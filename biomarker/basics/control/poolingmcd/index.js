$(document).ready(function () {
    var pathValue = "biomarker-basics-control-poolingmcd-index";

    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {};
    }

    var gridNameGrid;

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "copyControl", title: "复制control" },
            { name: "excel", target: "importData1", title: "实验导入/模板" },
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "queryDoMCDControlList", "objects": [] },
        }
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
    }




    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
    }

    var copyControl = function () {
        debugger;
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_control_BIO_TASK_LIBMX_list", "objects": [arrIds] },
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        if (rows.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd = { "tableName": "BIO_TASK_LIBMX", "objects": rows };
        putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    //表格导入
    var importData1 = function (componentId) {
        debugger;
        var arrIds = getSelectData(gridNameGrid);
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_TASK_LIBMX",
                        requestData: {
                            ajaxData: { "query": "queryDoMCDControlList", "search": { "ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }

    funcPushs(pathValue, {
        "initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
        "init": init,//初始化方法-在加载完初始化数据之后执行
        "copyControl": copyControl,
        "importData1":importData1,
        "refreshGrid": refreshGrid,
        "callBack": callBack,//回调方法
    });
});