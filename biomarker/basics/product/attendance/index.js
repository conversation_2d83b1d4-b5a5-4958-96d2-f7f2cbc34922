$(document).ready(function () {
    var pathValue = "biomarker-basics-product-attendance-index";

    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {};
    }

    var bioattendanceGrid;

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "add", target: "add" },
            { name: "edit", target: "edit" },
            { name: "delete", target: "deleteInfo" },
            { name: "excel", target: "importData1", title: "更新导入" },
        ]);//工具条
        //请求参数
        var bioattendanceGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_BIO_ATTENDANCE_list", "objects": [] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "点击弹出修改的列是什么") {
                        setJsonParam(cols[i], "template", getTemplate("#= 点击弹出修改的列是什么 #", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        bioattendanceGrid = initKendoGrid("#bioattendanceGrid" + pathValue, bioattendanceGridJson);//初始化表格的方法
    }

    var add = function () {
        var winOpts = {
            url: "biomarker/basics/product/attendance/add/add",
            title: "新增:人员出勤.."
        };
        openWindow(winOpts);
    }

    var open = function (ID) {
        var winOpts = {
            url: "biomarker/basics/product/attendance/add/add",
            title: "修改:人员出勤.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }

    var edit = function () {
        var arrIds = getSelectData(bioattendanceGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }

    var sumbit = function () {
        formSubmit({
            formId: "form",
            pathValue: pathValue
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (bioattendanceGrid) {
            bioattendanceGrid.dataSource.read();//重新读取--刷新
        }
    }

    var deleteInfo = function () {
        var arrIds = getSelectData(bioattendanceGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        var params = { "tableName": "BIO_ATTENDANCE", "ids": arrIds };
        var url = "system/jdbc/delete/batch/table";
        deleteGridDataByIds(url, params, refreshGrid);
    }

    //表格导入
    var importData1 = function (componentId) {

        var winOpts = {
            url: "biomarker/basics/product/attendance/importData1/importData1",
            title: "新增:人员通量.."
        }; 
        openWindow(winOpts);



        // openComponent({
        //     name: "导入数据",//组件名称
        //     componentId: componentId,
        //     params: {
        //         template: function (p, n) {
        //             return exportAndImportData({
        //                 expKey: "A",
        //                 tableName: "BIO_MMF",
        //                 requestData: {
        //                     ajaxData: { "query": "query_BIO_MMF_list" , "search": { "ID": arrIds } },
        //                 },
        //                 params: p,
        //                 name: n,
        //             });
        //         }
        //     },
        //     callBack: refreshGridS
        // });
    }

    funcPushs(pathValue, {
        "initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
        "init": init,//初始化方法-在加载完初始化数据之后执行
        "open": open,
        "add": add,//打开添加表单
        "edit": edit,
        "refreshGrid": refreshGrid,
        "deleteInfo": deleteInfo,
        "sumbit": sumbit,//提交方法
        "callBack": callBack,//回调方法
        "importData1": importData1,
    });
});