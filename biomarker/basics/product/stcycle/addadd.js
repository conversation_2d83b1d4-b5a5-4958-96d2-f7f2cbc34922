$(document).ready(function() {
    var pathValue="biomarker-basics-product-stcycle-addadd";
    var initData=function(){
        return {
            tableName:"BIO_STANDARD_CYCLE"
        };
    }
    var init=function(params){
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);//传入id
    }
 
 
    var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");
                        funcExce(pathValue+"close");
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });