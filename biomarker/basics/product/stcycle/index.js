$(document).ready(function() {
   var pathValue="biomarker-basics-product-stcycle-index";
   var initData=function(){
       return {};
   }

   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"add"},
            {name:"edit",target:"edit"},
            {name:"delete",target:"deleteInfo"},
			{name:"import",target:"importData",title:"导入"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"query_BIO_STANDARD_CYCLE_list","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="LIBRARY_TYPE"){
                        setJsonParam(cols[i],"template",getTemplate("#= LIBRARY_TYPE #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }

    var add=function(){
        var winOpts={
            url:"biomarker/basics/product/stcycle/add/add",
            title:"新增:实验标准周期.."
        };
        openWindow(winOpts);
    }

    var open=function(ID){
        var winOpts={
            url:"biomarker/basics/product/stcycle/add/add",
            title:"修改:实验标准周期.."
        };
        openWindow(winOpts,{"ID":ID});//传递id
    }

    var edit=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
		open(arrIds[0]);
     }
     
     var sumbit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_STANDARD_CYCLE","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }

    //表格导入
    var importData=function(componentId){
        var grid=gridNameGrid;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":grid,//单表导入
                "tableName":"BIO_STANDARD_CYCLE",
            }
        });
    }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "open":open,
         "add":add,
         "edit":edit,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "sumbit":sumbit,//提交方法
         "callBack":callBack,//回调方法
	 "importData":importData,
     });
});