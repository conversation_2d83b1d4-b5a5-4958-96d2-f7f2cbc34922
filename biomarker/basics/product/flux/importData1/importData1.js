$(document).ready(function() {
    var pathValue="biomarker-basics-product-flux-importData1-importData1";
	var paramsValue;
    debugger
	var gridNameGrid;
    var init=function(params){
		paramsValue = params;
        var toolbar = getButtonTemplates(pathValue, [ 
            { name: "excel", target: "importData1", title: "更新导入" },
		]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_BIO_MMF_GROUP_BY_list", "objects": [] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "点击弹出修改的列是什么") {
                        setJsonParam(cols[i], "template", getTemplate("#= 点击弹出修改的列是什么 #", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
    }
     //表格导入
     var importData1 = function (componentId) {
        var arrIds = getSelectData(gridNameGrid ,"F_PROCEDURE");
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行更新导入操作!");
            return;
        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_MMF",
                        requestData: {
                            ajaxData: { "query": "query_BIO_MMF_list" , "search": { "F_PROCEDURE": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
           callBack: refreshGridS
        });


 
    }

        var refreshGridS = function () {
        funcExce(pathValue+"pageCallBack");//执行回调
        funcExce(pathValue+"close");//关闭页面
}


    funcPushs(pathValue,{
        "init":init,
        "importData1":importData1,
    });
 
 });