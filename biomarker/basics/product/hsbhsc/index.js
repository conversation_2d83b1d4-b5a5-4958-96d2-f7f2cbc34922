$(document).ready(function () {
    var pathValue = "biomarker-basics-product-hsbhsc-index";
    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {};
    }

    var biommfGrid;

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "add", target: "add" },
            { name: "edit", target: "edit" },
			{name:"import",target:"importData",title:"导入"},
            { name: "delete", target: "deleteInfo" },
        ]);//工具条
        //请求参数
        var biommfGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_SYS_DATA_DICT_list_HSBH", "objects": [] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "点击弹出修改的列是什么") {
                        setJsonParam(cols[i], "template", getTemplate("#= 点击弹出修改的列是什么 #", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        biommfGrid = initKendoGrid("#biommfGrid" + pathValue, biommfGridJson);//初始化表格的方法
    }

    var add = function () {
        var winOpts = {
            url: "biomarker/basics/product/hsbhsc/add/add",
            title: "新增:人员通量.."
        };
        openWindow(winOpts);
    }

    var open = function (ID) {
        var winOpts = {
            url: "biomarker/basics/product/hsbhsc/add/add",
            title: "修改:人员通量.."
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }

    var edit = function () {
        var arrIds = getSelectData(biommfGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    }

    var sumbit = function () {
        formSubmit({
            formId: "form",
            pathValue: pathValue
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (biommfGrid) {
            biommfGrid.dataSource.read();//重新读取--刷新
        }
    }

    var deleteInfo = function () {
        var arrIds = getSelectData(biommfGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        var params = { "tableName": "SYS_DATA_DICT", "ids": arrIds };
        var url = "system/jdbc/delete/batch/table";
        deleteGridDataByIds(url, params, refreshGrid);
    }

        //表格导入
        var importData=function(componentId){
            var grid=biommfGrid;
            openComponent({
                name:"导入数据",//组件名称
                componentId:componentId,
                params:{
                    "template":grid,//单表导入
                    "tableName":"SYS_DATA_DICT",
                }
            });
        }

 
    funcPushs(pathValue, {
        "initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
        "init": init,//初始化方法-在加载完初始化数据之后执行
        "open": open,
        "add": add,//打开添加表单
        "importData":importData,
        "edit": edit,
        "refreshGrid": refreshGrid,
        "deleteInfo": deleteInfo,
        "sumbit": sumbit,//提交方法
        "callBack": callBack,//回调方法
    });
});