$(document).ready(function () {
    var pathValue = "biomarker-count-cost-outsourcing-expData-expData";
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "表名称是什么呢"
        };
    }
    var paramsValue;
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        paramsValue = params;
 
    }


    var submit = function () {
        debugger;
        var BTIME = $("#BTIME" + pathValue).val();   //开始时间
        var ETIME = $("#ETIME" + pathValue).val();   //结束时间
        saveResultDataToExcel({
            requestData: {
                ajaxData: {
                    "query": "query_count_cost_outsourcing_view", 
                    "objects": [], 
                    "size": 100000,
                     "search": { "IMPUTATIONDATA": BTIME, "IMPUTATIONDATATO": ETIME }
                },
            }
        },
            { "expKey": "A" }
        );

}

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});