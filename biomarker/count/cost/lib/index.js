$(document).ready(function() {
   var pathValue="biomarker-count-cost-lib-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var gridNameGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"excel",target:"expData",title:"报表导出"},
        ]);//工具条
        //请求参数
        var biommfGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"query_count_cost_lib_list","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="点击弹出修改的列是什么"){
                        setJsonParam(cols[i],"template",getTemplate("#= 点击弹出修改的列是什么 #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        biommfGrid = initKendoGrid("#biommfGrid"+pathValue,biommfGridJson);//初始化表格的方法
   }


     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(biommfGrid){
            biommfGrid.dataSource.read();//重新读取--刷新
        }
     }
      var expData = function () {
        var winOpts = {
            url: "biomarker/count/cost/lib/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts);//传递id
    }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
          "expData":expData,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});