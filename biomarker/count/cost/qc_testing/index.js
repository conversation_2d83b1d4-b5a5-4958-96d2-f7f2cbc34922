$(document).ready(function() {
   var pathValue="biomarker-count-cost-qc_testing-index";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var gridNameGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"excel",target:"expData",title:"报表导出"},
        ]);//工具条
        //请求参数
        var biommfGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_count_cost_qc_testing_list", "objects": [] },
            headerFilter: function (cols, i) {
                if (i < cols.length && cols[i]["field"] && cols[i]["field"] == "RJC_RIN") {//寄送情况拆分
                    debugger;
                    setJsonParam(cols[i], "template", function (dataItem) {
                        var RJC_RIN = dataItem["RJC_RIN"];//上机后QC结果
                        if(RJC_RIN=="A"||RJC_RIN=="B")RJC_RIN="合格";
                        if(RJC_RIN=="C"||RJC_RIN=="D")RJC_RIN="不合格";
                        return RJC_RIN;
                    });
                }
            }
        };
        biommfGrid = initKendoGrid("#biommfGrid" + pathValue, biommfGridJson);//初始化表格的方法
    }


     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(biommfGrid){
            biommfGrid.dataSource.read();//重新读取--刷新
        }
     }

      var expData = function () {
        var winOpts = {
            url: "biomarker/count/cost/qc_testing/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts);//传递id
    }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "refreshGrid":refreshGrid,
          "expData":expData,
         "callBack":callBack,//回调方法
     });
});