$(document).ready(function() {
   var pathValue="biomarker-count-lib-twoxmpe150-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"doReportLibQcGzRdPE150XM","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
     });
});