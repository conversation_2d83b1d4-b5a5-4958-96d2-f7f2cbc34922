$(document).ready(function() {
   var pathValue="biomarker-count-lib-libstep-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            { name: "exel", target: "importData", title: "备注导入" },
            {name:"excel",target:"expData",title:"报表导出"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"doReportLibGx","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }


    //表格实验导入
    var importData = function (componentId) {
        var arrIds = getSelectData(gridNameGrid);
        debugger;
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_LIB_QC_INFO",
                        requestData: {
                            ajaxData: { "query": "doReportLibGx", "size": 5000, "objects": [], "search": { "MID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }
    var callBack = function () {
        refreshGrid();
    };
    var refreshGrid = function () {
        gridNameGrid.dataSource.read();

    }

      var expData = function () {
        var winOpts = {
            url: "biomarker/count/lib/libstep/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts);//传递id
    }


     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
          "expData":expData,
        "importData": importData,
        "refreshGrid": refreshGrid,
        "callBack": callBack
     });
});