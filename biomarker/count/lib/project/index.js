$(document).ready(function () {
    var pathValue = "biomarker-count-lib-project-index";
    var initData = function () {
        return {};
    }
    var gridNameGrid;
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "exel", target: "importData", title: "备注导入" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "doReportLibProject", "objects": [] },
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
    }

    //表格实验导入
    var importFreeData = function (componentId) {
        var arrIds = getSelectData(gridNameGrid);

        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_TASK_LIB",
                        requestData: {
                            ajaxData: {
                                "query": "doReportLibProject", "size": 5000, "objects": [] , "search": { "ID": arrIds }
                            },
                            params: p,
                            name: n,
                        }
                    });
                }
            },
            callBack: refreshGrid
        });
    }


     //表格实验导入
		var importData=function(componentId){
            var arrIds=getSelectData(gridNameGrid);
debugger;
               if(arrIds.length==0){
                   alertMsg("请至少选择一条样本记录进行操作!");
                   return;
               }
               openComponent({
                       name:"导入数据",//组件名称
                       componentId:componentId,
                       params:{
                           template:function(p,n){
                               return exportAndImportData({
                                   expKey:"A",
                                   tableName:"BIO_TASK_LIB",
                                   requestData:{
                                       ajaxData:{"query":"doReportLibProject","size":5000,"objects":[],"search":{"MID":arrIds}},
                                   },
                                   params:p,
                                   name:n,
                               });
                           }
                       },
                       callBack:refreshGrid
                   });
           }
    var callBack = function () {
        refreshGrid();
    };
    var refreshGrid=function(){
        gridNameGrid.dataSource.read();
        
    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "importFreeData": importFreeData,
        "importData":importData,
        "refreshGrid": refreshGrid,
        "callBack":callBack
    });
});