$(document).ready(function () {
    var pathValue = "biomarker-count-dljs-jira-index";

    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {};
    }

    var gridNameGrid;

    // 获取当前日期
    let currentDate = new Date();
    // 获取当前年份和月份
    let currentYear = currentDate.getFullYear();
    let currentMonth = currentDate.getMonth();
    // 构造当前月的1号
    let firstDayOfMonth = new Date(currentYear, currentMonth, 1);
    // 构造次月的1号
    let firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1);
    var startTime = toDateFormatByZone(firstDayOfMonth, "yyyy-MM-dd") + " 00:00";
    var endTime = toDateFormatByZone(firstDayOfNextMonth, "yyyy-MM-dd") + " 00:00";

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        debugger
        var toolbar = getButtonTemplates(pathValue, [
            { name: "excel", target: "expData", title: "时间选择" },
        ]);



        var inobjjson = {
            "startTime": startTime, "endTime": endTime
        };
        var gridNameGridJson = {
            url: "function/system/settlement/nextNgs/iactJira",
            sort: "",
            toolbar: toolbar,
            size: 10000,
            read: inobjjson,
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] != "LANE_NO" && cols[i]["field"] != "ID" && cols[i]["field"] != "BASE") {

                    }

                }
            }
        };
        if (gridNameGrid) {
            gridNameGrid = setGridDataSource(
                "#gridNameGrid" + pathValue,
                gridNameGridJson
            );
        } else {
            gridNameGrid = initKendoGrid(
                "#gridNameGrid" + pathValue,
                gridNameGridJson
            ); //初始化表格的方法
        }
    }





    var expData = function () {
        debugger;
        var winOpts = {
            url: "biomarker/count/dljs/jira/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "时间选择.."
        };
        openWindow(winOpts);//传递id
    }

    var addSub = function (wlDataList) {
        startTime = wlDataList["startTime"]; endTime = wlDataList["endTime"];
        if (gridNameGrid) {
            gridNameGrid.dataSource.read(); //重新读取--刷新
            init();
        }

    };






    funcPushs(pathValue, {
        "initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
        "init": init,//初始化方法-在加载完初始化数据之后执行
        "addSub": addSub,
        "expData": expData,
    });
});