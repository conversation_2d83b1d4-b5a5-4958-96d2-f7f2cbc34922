$(document).ready(function () {
    var pathValue = "biomarker-count-dljs-jira-expData-expData";
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "表名称是什么呢"
        };
    }
    var paramsValue;
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        paramsValue = params;

    }


    var submit = function () {
        debugger;
        var startTime = $("#BTIME" + pathValue).val();   //开始时间
        var endTime = $("#ETIME" + pathValue).val();   //结束时间
		funcExce("biomarker-count-dljs-jira-index" + "addSub", {"startTime":toDateFormatByZone(startTime,"yyyy-MM-dd 00:00"),"endTime":toDateFormatByZone(endTime,"yyyy-MM-dd 00:00")}); 
		funcExce(pathValue + "close"); //关闭页面


    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});