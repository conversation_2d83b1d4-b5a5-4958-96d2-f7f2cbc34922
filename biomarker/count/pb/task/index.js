$(document).ready(function() {
   var pathValue="biomarker-count-pb-task-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"doReportPbTask","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
     });
});