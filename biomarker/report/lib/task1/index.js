$(document).ready(function() {
   var pathValue="biomarker-report-lib-task1-index-";
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"queryLibGzl","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }

     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
     }

     funcPushs(pathValue,{
         "init":init,
         "refreshGrid":refreshGrid,
         "callBack":callBack,
     });
});