$(document).ready(function () {
    var pathValue = "biomarker-report-tqqc-task1-expData-expData"; 
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "表名称是什么呢"
        };
    }
    var paramsValue;
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        paramsValue = params;
 
    }


    var submit = function () {
        debugger;
        var BTIME = $("#BTIME" + pathValue).val();   //开始时间
        var ETIME = $("#ETIME" + pathValue).val();   //结束时间
         
	 var date=paramsValue["DATE"];
       var    aa = { "TASK_ENDDATE": BTIME, "TASK_ENDDATETO": ETIME };
       if( date==2 ){   aa = { "TASK_CDATE": BTIME, "TASK_CDATETO": ETIME };     }
              

        saveResultDataToExcel({
            requestData: {
                ajaxData: {
                    "query": "doReportTqProject" ,
                    "objects": [], 
                    "size": 100000,
                     "search":aa 
                },
            }
        }, 
        );

}

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});