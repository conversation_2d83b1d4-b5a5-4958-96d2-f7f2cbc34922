$(document).ready(function() {
   var pathValue="biomarker-report-tqqc-task1-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"excel",target:"expData",title:"结单日期导出"},
            {name:"excel",target:"expData1",title:"审核日期导出"},
            { name: "edit", target: "edit", title: "填写不合格原因" },
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"doReportTqProject","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }


    var edit = function () {
        var arrIds = getSelectData(gridNameGrid);
            if(arrIds.length!=1){
          	alertMsg("请选择一条样本记录进行操作!");
        	return;
          }
        var url = "biomarker/report/tqqc/task1/upset/upset";

        var winOpts = {
            url: url,
            title: "不合格原因分析..."
        };
        openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
    }



      var expData = function () {
        var winOpts = {
            url: "biomarker/report/tqqc/task1/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts,{"DATE":  1});//传递 
    }
      var expData1 = function () {
        var winOpts = {
            url: "biomarker/report/tqqc/task1/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts,{"DATE":  2});//传递 
    }




     funcPushs(pathValue,{
         "initData":initData,
          "expData1":expData1,
          "expData":expData,
         "init":init,
         "edit":edit,
     });
});