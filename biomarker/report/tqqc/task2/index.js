$(document).ready(function() {
   var pathValue="biomarker-report-tqqc-task2-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"excel",target:"expData",title:"报表导出"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"doReportTqProjectSm","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }


      var expData = function () {
        var winOpts = {
            url: "biomarker/report/tqqc/task2/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts);//传递id
    }




     funcPushs(pathValue,{
         "initData":initData,
          "expData":expData,
         "init":init,
     });
});