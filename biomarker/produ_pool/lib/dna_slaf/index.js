$(document).ready(function() {
    var pathValue="biomarker-produ_pool-lib-dna_slaf-index";
    var initData=function(){
        return {};
    }

    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameS=[];
    var gridNamemx=[];
	var IDS = [];
    var EXEIDS= [];

    var init=function(params){

        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"editpool",title:"Pool文库实验填写.."},
            {name:"edit",target:"doSop",title:"设置SOP"},
            {name:"edit",target:"editmx",title:"子文库实验填写.."},
            {name:"excel",target:"expData",title:"实验导出"},
            {name:"excel",target:"importData2",title:"混库结果填写"},
            {name:"excel",target:"importData1",title:"子文库实验导入/模板"},
            {name:"edit",target:"doGo",title:"提交"},
            {name:"return",target:"doRerurn1",title:"退回"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"lib_pd_SHEET_list","objects":[["DNA混样建库-SLAF建库"],["已接收","审核退回"]]},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 420,
                    read:{"query":"queryTaskLibExMx-SLAF-Test","objects":[],"search":{"EXE_TQQC_ID":ROW_ID}},
                    detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
                    detailInit: function (e) {
                        var ROW_ID = e.data.ID;
                        var subGrid_N_JSON={
                            url: "system/jdbc/query/one/table",
                            sort: "",
                            toolbar: "",
                            height: 300,
                            read:{"query":"lib_sample_pool_all_list_slaf","objects":[],"search":{"POOL_ID1":[ROW_ID]}},
                        };
                        var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                        gridNamemx.push(subGrid_N);
                    }
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };

        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);

        init1();
    }


    var init1=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"return",target:"doRerurn2",title:"撤回"}
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"lib_pd_SHEET_list","objects":[["DNA混样建库-SLAF建库"],
                    ["建库提交","建库已审核","建库中","已完结","暂停","未建库","建库终止"]]},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read:{"query":"queryTaskLibExMx-SLAF-Test","objects":[],"search":{"EXE_TQQC_ID":ROW_ID}},
                    detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
                    detailInit: function (e) {
                        var ROW_ID = e.data.ID;
                        var subGrid_N_JSON={
                            url: "system/jdbc/query/one/table",
                            sort: "",
                            toolbar: "",
                            height: 220,
                            read:{"query":"lib_sample_pool_all_list_slaf","objects":[],"search":{"POOL_ID1":[ROW_ID]}},
                        };
                        var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                    }
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
    }

//pooling 批量填写
    var editpool=function(){
        debugger;
        var arrIds=[];
        for(var i=0;i<gridNameS.length;i++){
            var arrSubID=getGridSelectData(gridNameS[i]);
            if(arrSubID.length!=0){
                arrIds=arrIds.concat(arrSubID);
            }
        }
        if(arrIds.length==0){
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var ids=[];
        for(var i=0;i<arrIds.length;i++){
            ids.push(arrIds[i]["ID"]);
        }

        var winOpts={
            url:"biomarker/produ_pool/lib/dna_slaf/upsetpool/upsetpool",
            title:"Pooling文库填写.."
        };
        openWindow(winOpts,{"IDS":ids,"ID":ids[0]});
    }

    //结果填写
    var editmx=function(){
        var arrIds=[];
        for(var i=0;i<gridNamemx.length;i++){
            var arrSubID=getSelectData(gridNamemx[i]);
            if(arrSubID.length!=0){
                arrIds=arrIds.concat(arrSubID);
            }
        }
        if(arrIds.length==0){
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts={
            url:"biomarker/produ_pool/lib/dna_slaf/upset/upset",
            title:"SLAF.."
        };
        openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
    }
    //表格导入
    var importData1=function(componentId){
debugger;
        var arrIds=[];
		IDS = [];
		EXEIDS = [];
        for(var i=0;i<gridNamemx.length;i++){
            var arrSubID=getGridSelectData(gridNamemx[i]);
                                 for(var j = 0;j<arrSubID.length;j++){
					 if(arrSubID.length!=0){
						 arrIds=arrIds.concat(arrSubID[j]["ID"]);
						 IDS= IDS.concat(arrSubID[j]["ID"]);
					 }
                                      }
        }
        if(arrIds.length==0){
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                template:function(p,n){
                    return exportAndImportData({
                        expKey:"A",
                        tableName:"BIO_LIB_INFO",
                        requestData:{
                            ajaxData:{"query":"lib_sample_pool_all_list_slaf","size":5000,"objects":[],"search":{"ID":arrIds}},
                        },
                        params:p,
                        name:n,
                    });
                }
            },
            callBack:refreshGridS
        });
    }
    //混库结果填写
    var importData2=function(componentId){
        debugger;
        var arrIds=[];
        for(var i=0;i<gridNameS.length;i++){
            var arrSubID=getSelectData(gridNameS[i]);
            if(arrSubID.length!=0){
                arrIds=arrIds.concat(arrSubID);
            }
        }
        if(arrIds.length==0){
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                template:function(p,n){
                    return exportAndImportData({
                        expKey:"B",
                        tableName:"BIO_LIB_POOLING",
                        requestData:{
                            ajaxData:{"query":"queryTaskLibExMx-SLAF-Test","size":5000,"objects":[],"search":{"ID":arrIds}},
                        },
                        params:p,
                        name:n,
                    });
                }
            },
            callBack:refreshGrid
        });
    }
    //实验导出
    var expData=function(){
        var arrIds=[];
        for(var i=0;i<gridNamemx.length;i++){
            var arrSubID=getSelectData(gridNamemx[i]);
            if(arrSubID.length!=0){
                arrIds=arrIds.concat(arrSubID);
            }
        }
        if(arrIds.length==0){
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        saveResultDataToExcel({
                requestData:{
                    ajaxData:{
                        "query":"lib_sample_pool_all_list_slaf", "size":5000, "objects":[], "search":{"ID":arrIds}
                    },
                }
            },
            {"expKey":"B"}
        );

    }



    //确认提交结果
    var doGo=function(){
        debugger;
        var arrIds=getSelectData(gridNameDGrid);
        var g2=getGridSelectData(gridNameDGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条主单记录进行操作!");
            return ;
        }

		for (var i = 0; i < g2.length; i++) {
				if (g2[i]["SOP_NAME"] == null) {
					alertMsg("请先设置sop在提交");
					return;
				}
			}
        var objectupmain=[];
		var ids = [];
		var objectsop = [];
        for(var i=0;i<arrIds.length;i++){
            objectupmain.push({
                "ID":arrIds[i],
                "EX_RE_STATUS":"建库提交"
            });
		//	ids.push(arrIds[i]["ID"]);
        }

		debugger;
		var rows1 = [];
		$.fn.ajaxPost({
			 ajaxUrl: "system/jdbc/query/one/table",
			 ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_BIO_BZ_MATERIEL_SOP_cx_form", "objects": [arrIds] },
			succeed: function (rs) {
			//console.log(rs);				
			rows1 = rs["rows"];
		  }
		});

	   for(var i = 0;i<rows1.length;i++){
		   objectsop.push({
				 "ID":rows1[i]["ID"],
				 "SOP_REVIEW_FLAG":"待审核"
			   });
	   }

        var params={"query":"queryTaskLibExMxResult_SLAF","objects":[arrIds]};
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:"system/jdbc/query/one/table",
            ajaxData: params,
	    ajaxAsync: false,
            succeed:function(result){
debugger;
                if(result["code"]>0){
                    var rows=result["rows"];
                    var objectup=[];
                    var objectuptaskmx=[];
                    var objecuplibpool=[];
                    var time=sysNowTimeFuncParams["sysNowTime"];
                    var username=getLimsUser()["name"];
                    var poolids=[];
                    var uppoolings=[];
                    for(var i=0;i<rows.length;i++){
                        var g=rows[i];
                        //更新记录
                        objectup.push({
                            "ID":g["ID"],//联联任务ID
                            "JK_TASKMX_STATUS":"结果已提交",//结果状态
                            "SYS_INSERTTIME":time,//实验结果时间
                            "SYS_MAN":username//实验操作人
                        });
                        if(g2[0]["EX_LIB_TYPE"] == "SLAF"){
                          objectuptaskmx.push({
                            "ID":g["TASKMXLIBID"],//关联任务明细ID  
                            "SLAF_BS":"2",//slaf 预排标识
                            "TASK_LSMX_STATUS":"建库完成"//建库状态
                          });
                          }else{
                          objectuptaskmx.push({
                            "ID":g["TASKMXLIBID"],//关联任务明细ID
                            "TASK_LSMX_STATUS":"建库完成"//建库状态
                          });
                        }

                        if(poolids.indexOf(g["POOL_ID1"])<0){
                            poolids.push(g["POOL_ID1"]);//主单ID
                            uppoolings.push({
                                "ID":g["POOL_ID1"],
                                "POOL_STATUS":"混库待审核"
                            });
                            objecuplibpool.push({
                                "ID":g["POOL_ID1"],
                                "POOL_CODE":g["POOL_CODE"],
                                "LIBRARY_CODE":g["POOL_CODE"],
                                "SEQ_PLAT":"NGS",
                                "ISPOOL":"是",
                                "JK_TASKMX_L2100":g["JK_TASKMX_L2100"],
                                "JK_TASKMX_QPCR":g["JK_TASKMX_QPCR"],
                                "JK_TASKMX_QUBIT_IS":g["JK_TASKMX_QUBIT_IS"]
                            });
                             }
                    }
                    var url="system/jdbc/save/batch/table";
                    //文库实验结果
                    var paramsup={"tableName":"BIO_LIB_INFO","objects":objectup};
                    putAddOrUpdata(url,paramsup,"否","文库实验结果");
                    var paramsuppool={"tableName":"BIO_LIB_INFO","objects":objecuplibpool};
                    putAddOrUpdata(url,paramsuppool,"否","文库(与pooling对应)");
                    //更新任务状态
                    var paramsupmx={"tableName":"BIO_TASK_LIBMX","objects":objectuptaskmx};
                    putAddOrUpdata(url,paramsupmx,"否","明细状态更新");
					//更新SOP状态
					var paramsusop = { "tableName": "BIO_BZ_MATERIEL_SOP_JL", "objects": objectsop };
					putAddOrUpdata(url, paramsusop, "否", "更新SOP状态");

                    var paramsaddqcmx={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
                    putAddOrUpdata(url,paramsaddqcmx,"否","更新主单状态");

                    var upPool={"tableName":"BIO_LIB_POOLING","objects":uppoolings};
                    putAddOrUpdata(url,upPool,"是","更新POOLING主单状态");




                }else{
                    console.log(result);
                }
            }
        });
    }

    //退回
    var doRerurn1=function(){
        var g=getGridSelectData(gridNameDGrid);
        var objectup=[];
        for(var i=0;i<g.length;i++){
            objectup.push({
                "ID":g[i]["ID"],
                "EX_RE_STATUS":"实验退回"
            });
        }
        var urlsend="system/jdbc/save/batch/table";
        var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
        putAddOrUpdata(urlsend,paramsup,"是","提交");
    }
    //撤回
    var doRerurn2=function(){
        var g=getGridSelectData(gridNameD1Grid);
        if(g.length==0){
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup=[];
        for(var i=0;i<g.length;i++){
            if(g[i]["EX_RE_STATUS"]!="建库提交"){
                alertMsg("操作失败,所选记录存在“已审核”状态!");
                return;
            }else{
                objectup.push({
                    "ID":g[i]["ID"],
                    "EX_RE_STATUS":"已接收"
                });
            }
        }
        var urlsend="system/jdbc/save/batch/table";
        var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
        putAddOrUpdata(urlsend,paramsup,"是","提交");
    }

    //批量执行插入
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
                    if(isDoCallBack=="是"){
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                }else{
                    alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }

    var callBack=function(){
        refreshGrid();
    };

    var refreshGrid=function(){
        gridNameS=[];
        gridNamemx=[];
        if(gridNameDGrid){
            gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
            gridNameD1Grid.dataSource.read();
        }
    }
    var doSop = function() {
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条主单记录进行操作!");
            return;
        }
        if (arrIds.length > 1) {
            alertMsg("请只选择一条主单记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/produ_pool/lib/dna_slaf/doSop/doSop",
            title: "设置SOP..",
        };
        openWindow(winOpts, {"EXE_TQQC_ID": arrIds[0]["ID"],"EX_DH_NO": arrIds[0]["EX_DH_NO"],"EX_MX_NUMBER": arrIds[0]["EX_MX_NUMBER"]});
    }

	
	var refreshGridS = function () {
		gridNameS = [];

		if (gridNameDGrid) {
			gridNameDGrid.dataSource.read();
		}
		if (gridNameD1Grid) {
			gridNameD1Grid.dataSource.read();
		}
                        //alertMsg(gridNameS.length > 0);
                       // alertMsg(IDS.length >0);
                        //alertMsg(EXEIDS.length >0);
                var rows1;
               $.fn.ajaxPost({
                    ajaxUrl: "system/jdbc/query/one/table",
                    ajaxType: "post",
                    ajaxAsync: false,
                    ajaxData: { "query": "lib_sample_pool_all_list_slaf", "objects": [] ,"search":{"ID":IDS}},
                    succeed: function (rs) {
                    //console.log(rs);				
                    rows1 = rs["rows"];
                     }
                 });
debugger;
               for(var i =0;i<rows1.length;i++){
                     if(rows1[i]["JK_TASKMX_STDATE"] != ""&&rows1[i]["JK_TASKMX_STDATE"]  !=null &&rows1[i]["JK_TASKMX_ENDATE"] != ""&&rows1[i]["JK_TASKMX_ENDATE"] != null){
                           if(rows1[i]["JK_TASKMX_STDATE"] > rows1[i]["JK_TASKMX_ENDATE"]){
                                  alertMsg("建库实际开始日期不能大于建库实际结束日期");
                              }
                     }

                 }
                  
	}



    funcPushs(pathValue,{
        "initData":initData,
        "init":init,
        "editmx":editmx,
        "editpool":editpool,
        "doGo":doGo,
        "refreshGrid":refreshGrid,
        "callBack":callBack,
        "doRerurn1":doRerurn1,
        "doRerurn2":doRerurn2,
        "importData1":importData1,
        "importData2":importData2,
        "expData":expData,
        "doSop":doSop,
    });
});