$(document).ready(function() {
    var pathValue="biomarker-wwtask-lib-task-jira-jira";
    var paramsValue;
    var myDate = new Date();
     var time=sysNowTimeFuncParams["sysNowTime"];
 	var username=getLimsUser()["name"];
 	var status;
 	var mystatus;
 	var newstatus;
 	var gridNameGrid;
 	var pflag="form1";
 	var mainid="";
    var initData=function(){
        return {
            tableName:"JIRA_LSM_LIB"
        };
    }
    var init=function(params){

    	paramsValue=params;
    	mainid=paramsValue["ID"];
    	gridNameGrid=params["gridNameGrid"];
        getInfo("form1",pathValue,params);
        getInfo("form2",pathValue,params);
        
        var url="system/jdbc/query/info/"+initData().tableName;

        getInfo("form1",pathValue,params,url,function(v,p){
        getInfo("form1",pathValue,{"DD_TASK_LS_STATUS":"通过"});
             getInfo("form2",pathValue,p);
             if(p["TEST_FLAG"]=="是"){
             	pflag="form2"
                 $("#form2"+pathValue).show();
             	$("#form1"+pathValue).hide();
             }else{
             	pflag="form1"
                $("#form1"+pathValue).show();
             	$("#form2"+pathValue).hide();
             }

             var m=mask(pathValue,"正在获取jira状态,请稍等...");
             if(p["LSM_KEY_P"]!="") {
              	 getJira(p["LSM_KEY_P"],m);
              	 getJiraLSM(p["LSM_KEY"],m);
               }else{
              	 unmask(m);
              } 
             
        });
   	   // getInfo("form2",pathValue,params,url,function(p,v){
        // alert("2:"+p["LSM_KEY"]);
   	   // });

    }
    
 //审核
 var submitSh=function(){
debugger;
	 var flagstr="";
	 var flagstr2="";
	 var p = getJsonByForm(pflag, pathValue);
	 var passFlag=p["DD_TASK_LS_STATUS"];
	 var backmsg=p["TASK_LS_BACK"];
	 if(passFlag=="undefined"||passFlag==null||passFlag==""){
			 alertMsg("提示:请选择审核结果!");
			 return;
          }


	 if(passFlag=="通过"){
		 flagstr="待审核";
		 flagstr2="已审核";
	 }else if(passFlag=="退回"){
		 flagstr="草稿";
		 flagstr2="审核退回";
	 }else if(passFlag=="终止"){
		 flagstr="终止";
		 flagstr2="已终止";
	 }
	 
	 if(passFlag=="退回1"){
		 if(backmsg==""){
			 alertMsg("提示:请填写退回原因","error");
			 return;
		 }

	 }else if(passFlag=="终止1"){
		 if(backmsg==""){
			 alertMsg("提示:请填写终止原因","error");
			 return;
		 }
	 } 
	 
	 var object=[];
	 object.push({
     	"ID":mainid,
     	"DD_TASK_LS_STATUS":flagstr,
     	"TASK_LS_STATUS":flagstr2,
     	"TASK_LS_BACK":backmsg,
     	"TASK_LS_CDATE":time
     	});

	 //执行更新
	 var params={"tableName":"BIO_TASK_LIB","objects":object};
	 //插入任务明细记录
	 var url="system/jdbc/save/batch/table";
	 $.fn.ajaxPost({
	     ajaxType:"post",
	     ajaxUrl:url,
	     ajaxData:params,
	     succeed:function(result){

	         if(result["code"]>0){
	                if(passFlag!="通过"){
		                if(p["MAIN_ID"]){
		                  var url="system/jdbc/update/one/table/where";
		                  var jiraup= {"tableName":"JIRA_LSM_LIB","CUSTOMFIELD_14713":"非一次审核通过", "CUSTOMFIELD_14714":"非一次审核通过","where":{"MAIN_ID":[p["MAIN_ID"]]}};
		                  putAddOrUpdata(url,jiraup,"否","");
		                }
	               }
	         	 funcExce(pathValue+"pageCallBack");
	             funcExce(pathValue+"close");//关闭页面
	             alertMsg("提交成功!");
	         }else{
	         	console.log(result);
	         }
	     }
	 });   
 }
 

 
 var submit=function(){
	//表单校验
    var formJson = { formId:pflag, pathValue:pathValue };
    var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    if ( !validator.validate() ) {
           alertMsg("表单验证未通过","error");
           return false;
    }
    var m=mask(pathValue,"正在获取jira状态,请稍等...");
	submit1(m);
    unmask(m);
}


var submit1=function(m){
    var p = getJsonByForm(pflag, pathValue);
    
    if(mystatus=="等客户反馈"|| status=="等待启动" || status=="二次等客户反馈" || status=="三次等客户反馈"){
    		newstatus="提取检测结束";
    }else{
    	alertMsg("提示:jira 项目状态“"+status+"”, “等客户反馈、二次等客户反馈、三次等客户反馈”推送失败!");
        return false;
    }

    var params = {
        "jiraKey":p["LSM_KEY"],
        "oldStatusName":mystatus,
        "statusName":newstatus,
        "updateField":{
        	"customfield_13247":p["CUSTOMFIELD_13247"],	//建库测序任务单编号(13247)
        	"customfield_13258":myDate,	//结单日期(13258)
        	"customfield_13243":{"value":p["CUSTOMFIELD_13243"]}	//客户反馈(13243)            
        }
    };   
    var m=mask(pathValue,"正在推送到jira,请稍等...");

    var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
        	 unmask(m);
             if(result["code"]>0){
                 if(result.apiData.flag=="true"||result.apiData.flag){
                	   
                 	   var url="system/jdbc/update/one/table/where";
                 	   var paramjson={"tableName":"BIO_TASK_LIB","LSM_KEY":p["LSM_KEY"],"JIRA_STATUS":"已推送","where":{"ID":paramsValue["ID"]}};
                 	   putAddOrUpdata(url,paramjson,"否",m);
                 		if(pflag=="form1"){
                 			submit2(m);
                 		}else{
                 			submit3(m);
                 		}
                    }else{
                    	
                         alertMsg("提示:LSM推送失败(返回状态为:“"+mystatus+"”<font color=#ff0000>"+result.apiData.message+"</font>)!");
                         unmask(m);
                   }
              }else{
            	 alertMsg(errMsg+"操作失败!");
             }
         }
     });
 	unmask(m);
}


//项目实验
var submit2=function(m){
	debugger;
	//表单校验
    var p = getJsonByForm(pflag, pathValue);
    var z1=p["CUSTOMFIELD_14713"];
    var z2=p["CUSTOMFIELD_14714"];
    if(z1==null||z1=="") z1="一次审核通过";
    if(z2==null||z2=="") z2="一次审核通过";
    var params = {
        "jiraKey":p["LSM_KEY_P"],
        "oldStatusName":"等待启动",
        "statusName":"建库测序",
        "updateField":{
            "customfield_13801":p["CUSTOMFIELD_13247"],//任务单编号
            "customfield_12100":myDate,//建库测序下单日期
        	"customfield_10122":p["CUSTOMFIELD_10122"],	//	合同开始日期(10122)
        	"customfield_10123":p["CUSTOMFIELD_10123"],	//	合同结束日期(10123)
        	"customfield_10119":p["CUSTOMFIELD_10119"],	//	本期启动样品数(10119)
        	"customfield_11001":Number(p["CUSTOMFIELD_11001"]),	//	本期数据量(11001)
        	"customfield_10703":p["CUSTOMFIELD_10703"],	//	合同数据量(10703)
        	//"customfield_11010":{"value":p["CUSTOMFIELD_11010"]},	//	合同数据量单位(11010)
        	//"customfield_11002":{"value":p["CUSTOMFIELD_11002"]},	//	本期数据量单位(11002)
        	//"customfield_11404":Number(p["CUSTOMFIELD_11404"]),	//	合同金额(11404)
        	"customfield_11423":Number(p["CUSTOMFIELD_11423"]),	//	本期总金额(11423)
        	//"customfield_12000":Number(p["CUSTOMFIELD_12000"]),	//	质控金额(12000)
        	//"customfield_11426":Number(p["CUSTOMFIELD_11426"]),	//	常规报告金额(11426)
        	"customfield_12203":Number(p["CUSTOMFIELD_12203"]),	//	结题报告金额(12203)
        	"customfield_14713":{"value":z1},	//	建库任务单内审质量(14713)
        	"customfield_14714":{"value":z2},	//	建库任务单外审质量(14714)
        	"customfield_12600":{"value":p["CUSTOMFIELD_12600"]},//启动条件
        	"customfield_11520":p["CUSTOMFIELD_11520"],//合同剩余样品数
            "customfield_11525":p["CUSTOMFIELD_11525"],//运营经理描述信息
        	
        }
    };   
    
    var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
             if(result["code"]>0){
                 if(result.apiData.flag=="true"||result.apiData.flag){
                 	  var url="system/jdbc/update/one/table/where";
                 	   var paramjson={"tableName":"BIO_TASK_LIB","LSM_KEY_P":p["LSM_KEY_P"],"JIRA_STATUS":"已推送","where":{"ID":paramsValue["ID"]}};
                 	   putAddOrUpdata(url,paramjson,"是",m);
                    }else{
                         alertMsg("提示:项目,推送失败(返回状态为“等待启动”<font color=#ff0000>"+result.apiData.message+"</font>)!");
                   }
              }else{
            	 alertMsg(errMsg+"操作失败!");
             }
         }
     });   
}


//项目预实验
var submit3=function(m){
	//表单校验
    var p = getJsonByForm(pflag, pathValue);
    var params = {
        "jiraKey":p["LSM_KEY_P"],
        "oldStatusName":"等待启动",
        "statusName":"预实验建库测序",
        "updateField":{
        	"customfield_15502":p["CUSTOMFIELD_15502"],	//	预实验任务单编号(15502)
        	"customfield_15503":myDate,	//	预实验任务单下达日期(15503)
        	"customfield_15504":p["CUSTOMFIELD_15504"],	//	预实验开始日期(15504)
        	"customfield_15505":p["CUSTOMFIELD_15505"],	//	预实验结束日期(15505)
        	//"customfield_10117":{"emailAddress":p["CUSTOMFIELD_10117"]},	//	项目经理(10117)
        	"customfield_15506":p["CUSTOMFIELD_15506"],	//	预实验样品数(15506)
        	"customfield_15507":p["CUSTOMFIELD_15507"],	//	预实验数据量(15507)
        	"customfield_11525":p["CUSTOMFIELD_11525"],//运营经理描述信息
        }
    };   
    
    var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
             if(result["code"]>0){
                 if(result.apiData.flag=="true"||result.apiData.flag){
                 	   var url="system/jdbc/update/one/table/where";
                 	   var paramjson={"tableName":"BIO_TASK_LIB","LSM_KEY_P":p["LSM_KEY_P"],"JIRA_STATUS":"已推送","where":{"ID":paramsValue["ID"]}};
                 	   putAddOrUpdata(url,paramjson,"是",m);
                    }else{
                         alertMsg("提示:推送失败(<font color=#ff0000>"+result.apiData.message+"</font>)!");
                   }
              }else{
            	 alertMsg(errMsg+"操作失败!");
             }
         }
     });   
}


var getJira=function(keyinfo,m){
	 debugger;
if(keyinfo=="undefined"||keyinfo==null||keyinfo==""){
	unmask(m);
	return;
}
	var url="http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
	var parmars={"jiraKey":keyinfo,"fields":["customfield_11525","status"]};
    var inobjjson={ "url":url, "bodyParams": parmars };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
        	debugger;
             if(result["code"]>0){
            	 unmask(m);
            	 status=result.apiData[0].fields.status.name;
            	 if(result.apiData[0].fields.customfield_11525!=null){//存在值则累加
            		 
            		 var pp = getJsonByForm(pflag, pathValue);
            		
            		 var o=pp["CUSTOMFIELD_11525"];//存有的值
            		 
            		 if(o!=""&&o!=result.apiData[0].fields.customfield_11525){
            			 getInfo(pflag,pathValue,{"CUSTOMFIELD_11525":o+"\r"+result.apiData[0].fields.customfield_11525});            			 
            		 }else{
            			 if(result.apiData[0].fields.customfield_11525){
                			 getInfo(pflag,pathValue,{"CUSTOMFIELD_11525":result.apiData[0].fields.customfield_11525});            			 
            			 }
            		 }
            		
            	 }
            	 
            	
              }else{
            	  alertMsg("提示:加载获取jira信息失败!");
             }
         }
     });
 	
}

var getJiraLSM=function(keyinfo,m){
if(keyinfo=="undefined"||keyinfo==null||keyinfo=="") {
	unmask(m);
	return;
}
	var url="http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
	var parmars={"jiraKey":keyinfo,"fields":["customfield_11525","status"]};
    var inobjjson={ "url":url, "bodyParams": parmars };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
        	debugger;
             if(result["code"]>0){
            	 unmask(m);
            	 mystatus=result.apiData[0].fields.status.name;
              }else{
            	  alertMsg("提示:加载获取jira信息失败!");
             }
         }
     });
 	
}



var comfireData2=function(){
	debugger;
  	var m=mask(pathValue,"正在提交资金划拔,请稍等...");
	var ids=[];    	
	var object=[];

	var paramsF = getJsonByForm(pflag,pathValue);
	 var username=getLimsUser()["name"];
     var time=sysNowTimeFuncParams["sysNowTime"];
     
	 var passFlag=paramsF["DD_TASK_LS_STATUS"];
	 var backmsg=paramsF["TASK_LS_BACK"];
	 
	 var flagstr="";
	 var flagstr2="";
	 if(passFlag=="通过"){
		 flagstr="待审核";
		 flagstr2="已审核";
	
	 }else if(passFlag=="退回"){
		 
		 flagstr="草稿";
		 flagstr2="审核退回";
		 
	 }else if(passFlag=="终止"){
		 flagstr="终止";
		 flagstr2="已终止";
	 }

    
   if( passFlag=="通过"){
  	 
	    	 var arrIds=getGridSelectData(gridNameGrid);
	         //检测设置状态
	        
	         var params=[];
	 			var ht;
	 			var cp;
	 			var xm;
	 			var xh;
	 			var qd;
	 			var bx;
	         for(var i=0;i<arrIds.length;i++){
	 	      	  ids.push(arrIds[i]["ID"]);
	 	      	  if(arrIds[i]["BEGIN_STATUS"]=="已划拔"){
	 	                alertMsg("提示:已执行过划拔,请勿重复操作!");
	 	                 unmask(m);
	 	                return ;
	 	      	  }
	 				ht=arrIds[i]["CONTRACT_NO"];
	 				cp=arrIds[i]["PRODUCT_NO"];
	 				xm=arrIds[i]["PROJECT_NO"];
	 				xh=arrIds[i]["PROJECT_SUBNO"];
	 				qd=arrIds[i]["TASK_LS_SAMPLESUM"];
	 				bx=arrIds[i]["THE_DATA_SUM"];
	 		 if(qd>0 && bx>0){ }else{
 	 		  	 alertMsg("提示:启动样品数或本数据量小于0!");
	 	               //  unmask(m);
	 	              //  return ;
                      }
	 				params.push({
	       			  "contractNo":ht,//合同编号
	       			  "productNum":cp,// 产品编号
	       			  "projectNum":xm,//项目编号
	       			  "issue":xh,//项目期号
	       			  "startUpSampleNumber":qd,//启动样品数
	       			  "dataVolume":bx///本期数据量
	       	  });
	       	 
	         }
	  debugger;
	         var inobjjson={ "url":"http://"+JIRRA_URL+"/api/capital/pool/huaKai/start", "bodyParams":params };
	
	      	$.fn.ajaxPost({
	              ajaxType:"post",
	              ajaxUrl:"system/api/post/bodyParams",
	              ajaxData:inobjjson,
	              succeed:function(result){
	                   unmask(m);
	                  if(result["code"]>0){
	                	  debugger;
	                  var object2=[];
	           	     if(result.apiData.result=="true"||result.apiData.result){//总的情况

	           	           if(result.apiData.list[0].result=="true"||result.apiData.list[0].result){//每一项目情况
	                          for(var i=0;i < ids.length;i++){
	                           	 object2.push({
		                           		 "ID":ids[i],
		                           		 "DD_TASK_LS_STATUS":flagstr,
		                           		 "TASK_LS_STATUS":flagstr2,
		                           		 "TASK_LS_CDATE":time,
		                           		 "TASK_LS_BACK":backmsg,
		                           		 "BEGIN_STATUS":"已划拔"
	                                   });


	                            }
	                      
	                          var params={"tableName":"BIO_TASK_LIB","objects":object2};
	                          var url="system/jdbc/update/batch/table";
	                      	   putAddOrUpdata(url,params,"是",m);
	                      	   
	                      }else{//划拔失败
	                    	  debugger;
	                        	 for(var i=0;i < ids.length;i++){
		                           	 object2.push({
			                           		 "ID":ids[i],
			                           		 "DD_TASK_LS_STATUS":"草稿",
			                           		 "TASK_LS_STATUS":"审核退回",
			                           	         "BEGIN_STATUS":"已失败",
			                           		 "TASK_LS_BACK":result.apiData.list[0].msg
		                                   });

		                            }
	                        	unmask(m);
	                        	 var params={"tableName":"BIO_TASK_LIB","objects":object2};
		                          var url="system/jdbc/update/batch/table";
		                      	 putAddOrUpdata(url,params,"失败",m);
		                      	 
		                      	alertMsg("提示:"+result.apiData.list[0].msg);
	                        }
                  }else{//已存在划拔 
                	  debugger;
                      for(var i=0;i < ids.length;i++){
       	                           	 object2.push({
       		                           		 "ID":ids[i],
       		                           		 "DD_TASK_STATUS":flagstr,
       		                           		 "TASK_STATUS":flagstr2,
       		                           		 "TASK_CDATE":time,
       		                           		 "TASK_BACK":backmsg,
       		                           		 "BEGIN_STATUS":"已划拔"
       	                                   });

       	                            }
       	                          var params={"tableName":"BIO_TASK_LIB","objects":object2};
       	                          var url="system/jdbc/update/batch/table";

       	                      	putAddOrUpdata(url,params,"是",m);
       	                       alertMsg("提示:已存在划拔!");
                  }   
	                      
	                      
	                   }else{
	                 	 alertMsg(errMsg+"操作失败!");
	                  }
	              }
	          }); 
	      	
   }else{//不通过
debugger;

       for(var i=0;i < ids.length;i++){
         	 object.push({
 	        	"ID":ids[i],
	        	"DD_TASK_LS_STATUS":flagstr,
	        	"TASK_LS_STATUS":flagstr2,
	        	"TASK_LS_BACK":backmsg,
	        	"TASK_LS_CDATE":time
                 });

          }
       var params={"tableName":"BIO_TASK_LIB","objects":object};
       //插入任务明细记录
       var url="system/jdbc/save/batch/table";
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:url,
           ajaxData:params,
           succeed:function(result){
        	   unmask(m);
               if(result["code"]>0){
                    var url="system/jdbc/update/one/table/where";
                    var jiraup= {"tableName":"JIRA_LSM_LIB","CUSTOMFIELD_14713":"非一次审核通过", "CUSTOMFIELD_14714":"非一次审核通过","where":{"MAIN_ID":ids}};
                    putAddOrUpdata(url,jiraup,"否",m);
                      
               	  funcExce(pathValue+"pageCallBack");
                  alertMsg("提交成功!");
                  funcExce(pathValue+"close");
               }else{
               	console.log(result);
               }
           }
       }); 
  	 
   }
  
}


  //批量执行插入
	 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,m){
	     $.fn.ajaxPost({
	         ajaxType:"post",
	         ajaxUrl:urls,
	         ajaxData:inobjjson,
	         succeed:function(result){
	        	 unmask(m);
	             if(result["code"]>0){
	            	 if(isDoCallBack=="是"){
	            		 alertMsg("提示:操作成功!");
	            		 //refreshGrid();
	            	 }
	             }else{
	            	 alertMsg("提示:操作失败!");
	             }
	         }
	     });
	 }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "submitSh":submitSh,
        "comfireData2":comfireData2
    });
 
 });