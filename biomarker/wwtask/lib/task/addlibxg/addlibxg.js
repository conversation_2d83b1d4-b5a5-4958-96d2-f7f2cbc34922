$(document).ready(function() {
    var pathValue="biomarker-wwtask-lib-task-addlibxg-addlibxg";
    var mydata;
    var initData=function(){
        return {
            tableName:"BIO_TASK_LIB"
        };
    }
debugger;
	var paramsValue;
	var gridNameGrid;
	var passdo=0;
    var init=function(params){
		paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);//传入id
		
        //设表单固定值
        $("#TASK_LS_TYPE"+pathValue).val(params["TASK_LS_TYPE"]);
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"doSetff",title:"设置方法库.."},
            {name:"submit",target:"doUpset",title:"修改合同数据量.."},
            {name:"edit",target:"Splitsam",title:"拆分任务明细"},
            {name:"delete",target:"deleteInfo",title:"移除"},
            {name:"excel",target:"importData1",title:"导入/模板"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",//排序
           height: fullh-300,
           toolbar: toolbar,
           read:{"query":"query_BIO_TASK_LIBMX_list_WW","objects":[],"search":{"TASK_LS_ID":[paramsValue["ID"]]}},
           headerFilter:function(cols,i){
			   if(i){
				  if(cols[i]["field"]&&cols[i]["field"]=="ISPOOLSM"){
                  	var template="# if( ISPOOLSM=='混' ){ # <font onclick=\"funcExce('"+pathValue+"openpool','#= ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }" +
                  			" else if( ISPOOLSM=='拆' ){ # <font onclick=\"funcExce('"+pathValue+"openpool','#= BIO_SPLIT_ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }else{} #";
                  	setJsonParam(cols[i],"template",template);
                  }				  
			   }
			   
            },
           fetch:function(data){

                    mydata=data;
			   if(data.length>0){
					//样品数
					$("#TASK_LS_SAMPLESUM"+pathValue).val(data.length);
					var totalNumber=$("#ZL_SUM"+pathValue).val();
					var smSum=$("#SAMPLE_SUM"+pathValue).val();
					var dataSum=0;
					var thd_dataSum=0;

					if(totalNumber>=0&&smSum>0) dataSum=totalNumber/smSum;
					
					var type="";
					var THE_DATA_SUM=0;
					var upparmas=[];
					var LIBRARY_FLOW="";
					var the_ata=0;
					
					for(var i=0;i<data.length;i++){
						var row=data[i];
						LIBRARY_FLOW=row["LIBRARY_FLOW"];
						if(row["BUSINESS_UNIT"]){
							$("#BUSINESS_UNIT"+pathValue).val(row["BUSINESS_UNIT"]);
							$("#CYC_DW"+pathValue).val(row["CYC_DW"]);//执行周期单位
							$("#PROJECT_DEPT"+pathValue).val(row["MEHOD_JKPLAT"]);
							type=row["LIBRARY_TYPE_EN"];
						}
						
						if(row["DATA_UNIT"]&&row["DATA_UNIT"].indexOf("G")>-1){
							the_ata=row["DATA_SUM"]/0.3;
						}else{
							the_ata=row["DATA_SUM"];
						}
						if(the_ata==null) the_ata=0;
						upparmas.push({
							"ID":row["ID"],
							"THE_DATA_SUM":the_ata.toFixed(4),
							"THE_DATA_APSUM":the_ata.toFixed(4)
						});

					}
					
					
					 var url="system/jdbc/save/batch/table";
			         var paramsadd={"tableName":"BIO_TASK_LIBMX","objects":upparmas};
			         putAddOrUpdata(url,paramsadd,"否","");
					//根据方法库中的建库流向,设置 建库类别 

					
					if(LIBRARY_FLOW=="代谢建库") $("#TASK_LS_TYPE_LB"+pathValue).val("常规建库");
					if(LIBRARY_FLOW=="二代常规RNA建库") $("#TASK_LS_TYPE_LB"+pathValue).val("常规建库");
					if(LIBRARY_FLOW=="二代常规DNA建库") $("#TASK_LS_TYPE_LB"+pathValue).val("常规建库");
					if(LIBRARY_FLOW=="三代ONT基因组建库") $("#TASK_LS_TYPE_LB"+pathValue).val("常规建库");

					if(LIBRARY_FLOW=="ATAC建库") $("#TASK_LS_TYPE_LB"+pathValue).val("ATAC建库");
					if(LIBRARY_FLOW=="HIC建库") $("#TASK_LS_TYPE_LB"+pathValue).val("HIC建库");

					if(LIBRARY_FLOW=="DNA混样建库-MCD简化建库") $("#TASK_LS_TYPE_LB"+pathValue).val("MCD简化建库");

					if(LIBRARY_FLOW=="DNA混样建库-SLAF建库") $("#TASK_LS_TYPE_LB"+pathValue).val("混样建库");
					if(LIBRARY_FLOW=="PB混样-微生物全长") $("#TASK_LS_TYPE_LB"+pathValue).val("混样建库");
					if(LIBRARY_FLOW=="PB混样-全长转录组") $("#TASK_LS_TYPE_LB"+pathValue).val("混样建库");
					if(LIBRARY_FLOW=="PB混样-基因组") $("#TASK_LS_TYPE_LB"+pathValue).val("混样建库");
					if(LIBRARY_FLOW=="委外建库测序任务单-ONT建库-DNA") $("#TASK_LS_TYPE_LB"+pathValue).val("委外建库测序任务单");
					if(LIBRARY_FLOW=="委外建库测序任务单-ONT建库-Iso-RNA") $("#TASK_LS_TYPE_LB"+pathValue).val("委外建库测序任务单");
					if(LIBRARY_FLOW=="委外建库测序任务单-ONT建库-Iso-RNA") $("#TASK_LS_TYPE_LB"+pathValue).val("委外建库测序任务单");
					if(LIBRARY_FLOW=="委外建库测序任务单") $("#TASK_LS_TYPE_LB"+pathValue).val("委外建库测序任务单");



					// var url="system/jdbc/save/batch/table";
			        // var paramsadd={"tableName":"BIO_TASK_LIBMX","objects":upparmas};
			        // putAddOrUpdata(url,paramsadd,"否","");
					//两位小数
					//THE_DATA_SUM=THE_DATA_SUM.toFixed(4);
					
					$("#TASK_LIB_TYPE"+pathValue).val(type);
					//$("#THE_DATA_SUM"+pathValue).val(THE_DATA_SUM);
					THE_DATA_SUM=$("#THE_DATA_SUM"+pathValue).val();
					var countSm=data.length;
					if(type!=""&&passdo==1) {
						doCyc(type,countSm,THE_DATA_SUM,"建库标准用时",1);
						doCyc(type,countSm,THE_DATA_SUM,"实验交付标准用时",2);
						passdo=0;
					}else{
						// alertMsg("提示:文库类型为空,请设置方法库!!");
						 return;
					}
					
			   }
			   
			}
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
    }

    var open=function(ID){
        var winOpts={
        	url:"biomarker/operation/lib/task/edittask/edittask",
            title:"修改:委外建库测序任务单明细..",
			currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"ID":ID});//传递id
    }

     var openpool=function(ID){
  	 var winOpts={
  	   	url:"biomarker/wwtask/lib/task/poolsamlist/poolsamlist",
          title:"混样列表...",
          width:900,
          height:380,
          currUrl:replacePathValue(pathValue),
          position:{"top":150,"left":250}
        };    	 
  	 openWindow(winOpts,{"ID":ID});
   }
   
     var submit=function(){

        var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) {} else {
            alertMsg("验证未通过","wran");
            return ;
        }
        
        if (paramsF["TASK_LS_LDATE"] != "" && paramsF["TASK_TEST_DELIVERDATE"] != "") {
            if(paramsF["LSM_KEY"] == null||paramsF["LSM_KEY"] == ""||paramsF["LSM_KEY"] == undefined){
                alertMsg("jira_LSM关键字为空,JIRA更新失败!")
            }else{
                sendLSM();
            }
        }
	   var time=sysNowTimeFuncParams["sysNowTime"];
	   var username=getLimsUser()["name"];
           $("#DD_TASK_LS_STATUS"+pathValue).val("待填写");
           $("#SYS_MAN_L"+pathValue).val(username);
           $("#SYS_INSERTTIME_L"+pathValue).val(time);

         //通过后刷新一下列表,
         if(gridNameGrid){
             gridNameGrid.dataSource.read();
         }
        //如果本期数据量(M)为空值不能通过
       for(var i=0;i<mydata.length;i++){
              var sumdata=mydata[i]["THE_DATA_SUM"];
              if(sumdata<0){
                     alertMsg("提交失败!本期数据量(M)不能为空值!")
                     return;
              }
       }
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
     
     //获取周期定义,推算出截止结果日期
     var doCyc=function(type,countSm,smnumber,dep,selflag){
     	//样品提取检测标准用时
     	var cycdw=$("#CYC_DW"+pathValue).val();
         var bus=$("#BUSINESS_UNIT"+pathValue).val();
         var params="";
         var flag=0;
         if(cycdw=="样品数"){
        	 flag=0;
            params={"query":"checkCycSmNumber","objects":[bus,dep,type,countSm,countSm]};
         }else{
        	 flag=1;
            params={"query":"checkCycDataNumber","objects":[bus,dep,type,smnumber,smnumber]};
         }
  	   $.fn.ajaxPost({
  	        ajaxUrl:"system/jdbc/query/one/table",
  	        ajaxType: "post",
  	        ajaxData: params,
  	        succeed:function(result){
  	        	if(result["code"]>0){
  	        		 debugger;
 	   	        	var rows=result["rows"];
 	   	        	var m=getMyMonth();
 	   	        	var dateNumber=0;
 	   	        	var seleDateFlag="工作日";//日历取向
 		   	        for(var i=0;i<rows.length;i++){
 		   	        	var row=rows[i];
 		   	        	seleDateFlag=row["CYC_FLAG"];
 		   	        	if(m==1) dateNumber=row["MONTH_1"];
 		   	        	if(m==2) dateNumber=row["MONTH_2"];
 		   	        	if(m==3) dateNumber=row["MONTH_3"];
 		   	        	if(m==4) dateNumber=row["MONTH_4"];
 		   	        	if(m==5) dateNumber=row["MONTH_5"];
 		   	        	if(m==6) dateNumber=row["MONTH_6"];
 		   	        	if(m==7) dateNumber=row["MONTH_7"];
 		   	        	if(m==8) dateNumber=row["MONTH_8"];
 		   	        	if(m==9) dateNumber=row["MONTH_9"];
 		   	        	if(m==10) dateNumber=row["MONTH_10"];
 		   	        	if(m==11) dateNumber=row["MONTH_11"];
 		   	        	if(m==12) dateNumber=row["MONTH_12"];
 		   	        	
 		   	        	break;
 		   	        }
 		   	        //项目周期
 		   	        if(dateNumber==0){
 		   	        	$("#CYC_FLAG"+pathValue).val("");
 		   	        	if(selflag==1) $("#TASK_LS_EXCDAYS"+pathValue).val("");
 		   	        	if(selflag==2) $("#TASK_JF_EXCDAYS"+pathValue).val("");
 		   	        	var knumber=0;
 		   	        	if(flag==0){
 		   	        		knumber=countSm;
 		   	        	}else{
 		   	        		knumber=smnumber;
 		   	        	}
 		   	        	alert("提示:未获取到周期数,请检测条件是否满足(工序标准:“"+dep+"”,单位:“"+cycdw+"”,文库类型:“"+type+"”,执行参数:“"+knumber+"”)！");
 		   	        	return ;
 		   	        }else{
 		   	        	$("#CYC_FLAG"+pathValue).val(seleDateFlag);
 		   	        	if(selflag==1) $("#TASK_LS_EXCDAYS"+pathValue).val(dateNumber);
 		   	        	if(selflag==2) $("#TASK_JF_EXCDAYS"+pathValue).val(dateNumber);
 		   	        		
 			   	        doGetEndDate(seleDateFlag,dateNumber,dateNumber,selflag);
 		   	        }
 		   	      
  	        	}
  	        }
  	   });
     	
     }
     
  //推算截止日期
    var doGetEndDate=function(seleDateFlag,dateNumber1,dateNumber2,selflag){
       var thedate = new Date();
       var thedate2 = new Date();
       var params="";
       if(seleDateFlag=="工作日"){
    	   params={"query":"getCalendarInfo","objects":[thedate.getFullYear(),toDateFormatByZone(thedate,"yyyy-MM-dd")],"search":{"D_WEEKDAY_REMOVAL":"是"}};//取得当前日期后一年内所有的“工作日”排除日期
       }else{
    	   params={"query":"getCalendarInfo","objects":[thedate.getFullYear(),toDateFormatByZone(thedate,"yyyy-MM-dd")],"search":{"D_NATURAL_REMOVAL":"是"}};//取得当前日期后一年内所有的“自然日”排除日期
       }
      
   	   $.fn.ajaxPost({
   	        ajaxUrl:"system/jdbc/query/one/table",
   	        ajaxType: "post",
   	        ajaxData: params,
   	        succeed:function(result){
   	        	if(result["code"]>0){
  	   	        	var rows=result["rows"];
  	   	        	var noDoDateS=[];
  		   	        for(var i=0;i<rows.length;i++){
  		   	        	var row=rows[i];
  		   	        	noDoDateS.push(toDateFormatByZone(row["D_DATE"],"yyyy-MM-dd"));
  		   	        }
  		   	        if(selflag==1){
		  		   	        //建库标准日期
		  		   	        for(var i=0;i<dateNumber1;i++){
		  		   	        	var base = 1000 * 60 * 60 * 24;
		  		   	        	thedate=new Date(thedate.getTime() + base);
		  		   	        	for(var j=0;j<noDoDateS.length;j++){
			  		   	        	if(toDateFormatByZone(thedate,"yyyy-MM-dd")==noDoDateS[j]){//存在排除日期测
			  		   	        	  thedate=new Date(thedate.getTime()+base);//日期向前一天
			  		   	        	}
		  		   	        	}
		  		   	        	
		  		   	        }
		  		   	        //推算出的最终截止日期
		  		   	   //alert(toDateFormatByZone(thedate,"yyyy-MM-dd"));
		  		   	        $("#TASK_LS_LDATE"+pathValue).val(toDateFormatByZone(thedate,"yyyy-MM-dd"));
  		   	        }
		  		   	   if(selflag==2){
		  		   	       //实验标准交付日期
		 		   	        for(var i=0;i<dateNumber2;i++){
		 		   	        	var base = 1000 * 60 * 60 * 24;
		 		   	        	thedate2=new Date(thedate2.getTime() + base);
		 		   	        	for(var j=0;j<noDoDateS.length;j++){
			  		   	        	if(toDateFormatByZone(thedate2,"yyyy-MM-dd")==noDoDateS[j]){//存在排除日期测
			  		   	        	  thedate2=new Date(thedate2.getTime()+base);//日期向前一天
			  		   	        	}
		 		   	        	}
		 		   	        	
		 		   	        }
		 		   	        //推算出的最终截止日期
		 		   	        // alert(toDateFormatByZone(thedate2,"yyyy-MM-dd"));
		 		   	        $("#TASK_LS_DELIVERDATE"+pathValue).val(toDateFormatByZone(thedate2,"yyyy-MM-dd"));
		 		   	       //alertMsg("提示:计算标准日期成功,请注意复核!");
		  		   	   }
   	        	}
   	        }
   	   });
   	   
    }
    
     //当前月份
     var getMyMonth=function (){
 		 var date=new Date;
 		 var month=date.getMonth()+1;
 		 return month;
 	}
     //方法库
     var doSetff=function(){
         var arrIds=getSelectData(gridNameGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行修改!");
             return ;
         }
         var winOpts={
             url:"biomarker/dispatch/lib/task/setff/setff",
             title:"设置方法库..",
             currUrl:replacePathValue(pathValue)
         };
         passdo=1;
         openWindow(winOpts,{"IDS":arrIds,"PRODUCT_TYPE":$("#PRODUCT_TYPE"+pathValue).val()});
      }
 var doUpset=function(){
         var arrIds=getSelectData(gridNameGrid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行修改!");
             return ;
         }
         var winOpts={
             url:"biomarker/dispatch/lib/task/upset/upset",
             title:"修改合同数据量..",
             currUrl:replacePathValue(pathValue)
         };
         openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
      }
     
     var doJG=function(){

    	        var paramsF = getJsonByForm("form",pathValue);
    	    	 //取出表单值
    	    	 var passFlag=paramsF["TASK_COMFIRM_RESULT"];
    	    	 var flagstr="";
    	    	 var flagstr2="";
    	    	 if(passFlag=="通过"){
    	    		 flagstr="已审核";
    	    		 flagstr2="已审核";
    	    	
    	    	 }else if(passFlag=="退回"){
    	    		 
    	    		 flagstr="草稿";
    	    		 flagstr2="调度退回";
    	    		 
    	    	 }else if(passFlag=="终止"){
    	    		 flagstr="终止";
    	    		 flagstr2="已终止";
    	    	 }

    	    	$("#DD_TASK_LS_STATUS"+pathValue).val(flagstr);
    	    	$("#TASK_LS_STATUS"+pathValue).val(flagstr2);
    	    	
    	}
     //拆分明细
     var Splitsam=function(){
         var g=getGridSelectData(gridNameGrid);
         if(g.length!=1){
             alertMsg("请选择一条数据进行修改!");
             return ;
         }
         
    	 confirmMsg("确认", "确定要对选中的记录进行拆分吗?", "warn", function() {
    		 var newrecode=[];
	    	 newrecode.push($.extend({}, g[0],{"ID":getRandomId()}));
		 	 var newUrl="system/jdbc/save/one/table/objects";
	  	     var url="system/jdbc/save/batch/table";
	  		  var paramsnainadd={"tableName":"BIO_TASK_LIBMX","objects":newrecode};
	  		  putAddOrUpdata(newUrl,paramsnainadd,"是","生成");
	    });
    }
     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function() {
        var params={"tableName":"BIO_TASK_LIBMX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
});
     }
     //表格导入
 	var importData1=function(componentId){
 		var g=getGridSelectData(gridNameGrid);
        if(g.length==0){
            alertMsg("请选择记录数据进行修改!");
            return ;
        }
        var ids=[];
        for(var i=0;i<g.length;i++){
        	ids.push(g[i]["ID"]);
        }
        
	 	openComponent({
	             name:"导入数据",//组件名称
	             componentId:componentId,
	             params:{
	                 template:function(p,n){
	                     return exportAndImportData({
	                         expKey:"B",
	                         tableName:"BIO_TASK_LIBMX",
	                         requestData:{
	                             ajaxData:{"query":"query_BIO_TASK_LIBMX_list_WW","size":5000,"objects":[],"search":{"ID":ids,"TASK_LS_ID":[paramsValue["ID"]]}},
	                         },
	                         params:p,
	                         name:n,
	                     });
	                 }
	             },
	             callBack:refreshGrid
	         });
 	    }
   function getRandomId() {
    	 return ('BIO-C-' + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
    };  
     var callBack=function(){
         refreshGrid();
      };

      var refreshGrid=function(){
         if(gridNameGrid){
             gridNameGrid.dataSource.read();
         }
      }
     var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:urls,
             ajaxData:inobjjson,
             succeed:function(result){
                 if(result["code"]>0){
                	 if(isDoCallBack=="是"){
                		 refreshGrid(); 
                	 }
                	 
                 }else{
                	 alertMsg(errMsg+"操作失败!");
                 }
             }
         });
     }

var generate=function(){
debugger;
        var ID= $("#ID"+pathValue).val();
        var PROJECT_NO = $("#PROJECT_NO"+pathValue).val();
        var PROJECT_NAME = $("#PROJECT_NAME"+pathValue).val();
        var TASK_LS_NO = $("#TASK_LS_NO"+pathValue).val();
        var PROJECT_SUBNO = $("#PROJECT_SUBNO"+pathValue).val();
        var WW_SUPPLIER = $("#WW_SUPPLIER"+pathValue).val();
        var WW_PRODUCT_TYPE = $("#WW_PRODUCT_TYPE"+pathValue).val();
        var WW_NO = $("#WW_NO"+pathValue).val();
        var WW_DELIVERY_DATE = $("#WW_DELIVERY_DATE"+pathValue).val();
        openWindow({
            url: "biomarker/wwtask/lib/task/addlib/addsettlement/addsettlement",
            title: "添加",
            currUrl: "biomarker/wwtask/lib/task/addlib/addlib",
        }, {
            "TASK_LS_ID": ID,
            "PROJECT_NO": PROJECT_NO,
            "PROJECT_NAME": PROJECT_NAME,
            "TASK_LS_NO": TASK_LS_NO,
            "PROJECT_SUBNO": PROJECT_SUBNO,
            "WW_SUPPLIER": WW_SUPPLIER,
            "WW_PRODUCT_TYPE": WW_PRODUCT_TYPE,
            "WW_NO": WW_NO,
            "WW_DELIVERY_DATE": WW_DELIVERY_DATE

        });
}



    //正实验LSM - jira
    var sendLSM = function () {
        debugger;
        //var TASK_LS_CDATE = $("#TASK_LS_CDATE"+pathValue).val();
        //var TASK_JF_EXCDAYS= $("#TASK_JF_EXCDAYS"+pathValue).val()*1;
        //var tjes=addDate(TASK_LS_CDATE,TASK_JF_EXCDAYS);
        var odlStatus = "";
        var newStatus = "等客户反馈";
        var p = getJsonByForm("form", pathValue);
        var params = {
            "jiraKey": p["LSM_KEY_P"],
            // "oldStatusName":odlStatus,
            // "statusName":newStatus,
            "updateField": {
                "customfield_12101": p["TASK_LS_LDATE"],	//建库标准结单日期
                "customfield_14201": p["TASK_TEST_DELIVERDATE"],//建库计划完成日期 
                "customfield_10226": p["TASK_LS_DELIVERDATE"],//实验标准交付日期
                "customfield_13900": p["TASK_LS_DELIVERDATE"],//实验标准交付日期
            }
        };

        var m = mask(pathValue, "正在推送到jira,请稍等...");
        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                debugger;
                unmask(m);
                if (result["code"] > 0) {
                    alertMsg("提示:LSM推送成功!");
                } else {
                    alertMsg("提示:操作失败!");
                }
            },
            failed: function (res) {
                unmask(m);
                alertMsg("提示:提交保存失败", "error");
            }
        });

    }
  
	funcPushs(pathValue,{
		"initData":initData,
		"init":init,
		"refreshGrid":refreshGrid,
		"submit":submit,
		"openpool":openpool,
		"callBack":callBack,
		"doSetff":doSetff,
		"generate":generate,
		"doUpset":doUpset,
		"deleteInfo":deleteInfo,
       	 "Splitsam":Splitsam,
       	 "importData1":importData1,
		"doJG":doJG,
	});
 
 });