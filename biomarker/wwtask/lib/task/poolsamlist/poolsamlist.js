$(document).ready(function() {
	var pathValue="biomarker-wwtask-lib-task-poolsamlist-poolsamlist";
	var initData=function(){
		return {};
	}
 
	var gridNameGrid;
 
	var init=function(params){
 
		 var toolbar=getButtonTemplates(pathValue,[
		 ]);
		 var gridNameGridJson={
			 url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
			 sort: "",//排序
			 toolbar: toolbar,
			 width:900,
			 height:360,
			 read:{"query":"query_biolib_N_list","objects":[params["ID"]]}
		 };
		 gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
	}
 
	  funcPushs(pathValue,{
		  "initData":initData,
		  "init":init,
	  });
 });