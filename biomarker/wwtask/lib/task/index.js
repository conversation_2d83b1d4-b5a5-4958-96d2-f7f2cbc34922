$(document).ready(function () {
    var pathValue = "biomarker-wwtask-lib-task-index";
    var initData = function () {
        return {};
    }

    var gridNameGrid;
    var gridNameLGrid;
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            // {name:"edit",target:"opensel",title:"审核.."}
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_WW_BIO_TASK_LIB_view", "objects": [["待审核"], ["委外建库测序任务单"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "TASK_LS_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= TASK_LS_NO #", "funcExce(\'" + pathValue + "opensel\',\'#= ID #\',\'#= TASK_LS_TYPE #\',\'#= BEGIN_STATUS #\');", "txt"));
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);

        init_1();
    }

    var init_1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doReturn", title: "撤回" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_WW_BIO_TASK_LIB_view", "objects": [["待填写", "已终止", "已完成"], ["委外建库测序任务单"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "TASK_LS_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= TASK_LS_NO #", "funcExce(\'" + pathValue + "openselck\',\'#= ID #\',\'#= TASK_LS_TYPE #\');", "txt"));
                    }
                }
            }
        };
        gridNameLGrid = initKendoGrid("#gridNameLGrid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    var opensel = function (ID, type,BEGIN_STATUS) {debugger
        var winOpts = {
            url: "biomarker/wwtask/lib/task/addlib/addlib",
            title: "修改:" + type + "任务下达.."
        };
        openWindow(winOpts, { "ID": ID, "TASK_LS_TYPE": type ,"BEGIN_STATUS":BEGIN_STATUS});
    }

    var openselck = function (ID, type) {
        var winOpts = {
            url: "biomarker/wwtask/lib/task/addlibxg/addlibxg",
            title: "修改:" + type + "任务下达.."
        };
        openWindow(winOpts, { "ID": ID, "TASK_LS_TYPE": type });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
        if (gridNameLGrid) {
            gridNameLGrid.dataSource.read();
        }
    }

    var inputCheck1 = function () {
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length != 1) {
            alertMsg("请选择一条数据进行审核操作!");
            return;
        }
        var ids = [];
        lsmkeyps = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            lsmkeyps.push(arrIds[i]["LSM_KEY_P"]);
        }
        var winOpts = {
            url: "biomarker/wwtask/lib/task/addlib/addlib",
            title: "审核任务单.."
        }
        openWindow(winOpts, { "IDS": ids, "ID": ids[0], "LSMKEYP": lsmkeyps });

    }

    var doReturn = function () {
        var arrIds = getSelectData(gridNameLGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < arrIds.length; i++) {
            objectup.push({
                "ID": arrIds[i],
                "DD_TASK_LS_STATUS": "待审核" 
            });
        }
        var url = "system/jdbc/save/batch/table";
        var paramsaddqcmx = { "tableName": "BIO_TASK_LIB", "objects": objectup };
        putAddOrUpdata(url, paramsaddqcmx, "是", "更新主单状态");
    }

    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "opensel": opensel,
        "openselck": openselck,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "inputCheck1": inputCheck1,
        "doReturn": doReturn
    });
});