$(document).ready(function() {
    
    var pathValue="biomarker-wwtask-lib-result-comfirm-comfirm";
   var paramsValue;
   var initData=function(){
       return {
           tableName:"BIO_TASK_LIB"
       };
   }
   var init=function(params){
       
       paramsValue=params;
       getInfo("form",pathValue,params);
       var url="system/jdbc/query/info/"+initData().tableName;
       getInfo("form",pathValue,params,url);
   }

   
 var doSetCyc=function(){
      if($("#TASK_LS_EXCDAYS"+pathValue).val()<1||$("#TASK_JF_EXCDAYS"+pathValue).val()<1){
         alertMsg("执行天数不能小于1","wran");
         return ;
        }
     doGetEndDate(
             $("#CYC_FLAG"+pathValue).val(),
             $("#TASK_LS_EXCDAYS"+pathValue).val(),
             $("#TASK_JF_EXCDAYS"+pathValue).val()
    );
    
 }

 var comfireData=function(){
     var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
     if (validator.validate()) {} else {
         alertMsg("验证未通过","wran");
         return ;
     }
       var ids=paramsValue["IDS"];    	
       var object=[];
       var paramsF = getJsonByForm("form",pathValue);
        //取出表单值
        var passFlag=paramsF["TASK_COMFIRM_RESULT"];
        var backmsg=paramsF["TASK_BACK"];
        var typelb=paramsF["TASK_LS_TYPE_LB"];
        var tcr=paramsF["NEEDING_ATTENTION"];

       var TASK_PLAN_ETIME=paramsF["TASK_PLAN_ETIME"];
       var LSMKEYPS=paramsValue["LSMKEYP"];
        var TASK_LS_LDATE=paramsF["TASK_LS_LDATE"];
        var TASK_LS_CDATE=paramsF["TASK_LS_CDATE"];
        var TASK_LS_EXCDAYS=paramsF["TASK_LS_EXCDAYS"];
        var TASK_TEST_DELIVERDATE=paramsF["TASK_TEST_DELIVERDATE"];
        var TASK_LS_DELIVERDATE=paramsF["TASK_LS_DELIVERDATE"];
        var TASK_JF_EXCDAYS=paramsF["TASK_JF_EXCDAYS"];
        
      if(typelb=="undefined"||typelb==""||typelb==null) {
          alertMsg("提示:操作失败!任务类别未确认");
          return;
      }
        
        var flagstr="";
        var flagstr2="";
         flagstr="已完成";
         flagstr2="已完成";
     /*   if(passFlag=="通过"){
            flagstr="已完成";
            flagstr2="已完成";
       
        }else if(passFlag=="退回"){
            
            flagstr="草稿";
            flagstr2="调度退回";
            
        }else if(passFlag=="终止"){
            flagstr="终止";
            flagstr2="已终止";
        }
        */
        for(var i=0;i < ids.length;i++){
            object.push({
                    "ID":ids[i],
                   "DD_TASK_LS_STATUS":flagstr,
                   "TASK_LS_STATUS":flagstr2,
                   "TASK_LS_TYPE_LB":"委外建库",
                   "TASK_LS_BACK":backmsg,
                               "TASK_PLAN_ETIME":TASK_PLAN_ETIME,
                   "NEEDING_ATTENTION":tcr,
                   "TASK_LS_LDATE":TASK_LS_LDATE,
                   "TASK_LS_CDATE":TASK_LS_CDATE,
                   "TASK_LS_EXCDAYS":TASK_LS_EXCDAYS,
                   "TASK_LS_DELIVERDATE":TASK_LS_DELIVERDATE,
                   "TASK_JF_EXCDAYS":TASK_JF_EXCDAYS,
                   "TASK_TEST_DELIVERDATE":TASK_TEST_DELIVERDATE
                   });
        }
        //执行更新
        var params={"tableName":"BIO_TASK_LIB","objects":object};
        //插入任务明细记录
        var url="system/jdbc/save/batch/table";
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:url,
            ajaxData:params,
            succeed:function(result){
                
                if(result["code"]>0){//成功保存后执行流程提交
                    
                    if(passFlag=="通过"){
                        doJira(ids,LSMKEYPS,TASK_PLAN_ETIME);
                    }
                    funcExce(pathValue+"pageCallBack");//父执行回调
                
                    console.log(result);
                    
                 alertMsg("提交成功!");
                 
                 funcExce(pathValue+"close");//关闭页面
                    
                }else{
                    console.log(result);
                }
            }
        });    	
   }

 var doJira=function(IDS,keys2,yDate){
     var params2=[];
     for(var i=0;i<IDS.length;i++){
         params2.push({
             "jiraKey":keys2[i],
             "oldStatusName":"等待启动",
             "statusName":"建库测序",
             "updateField":{    		              
               "customfield_14201":yDate
              }
         });
      }
     for(var i=0;i<IDS.length;i++){
       putToJira(params2[i]);
    }
   }
   var putToJira=function(params){
       var inobjjson={ "url":"http://"+JIRRA_URL+"/api/jira/sysInfo", "bodyParams": params };
          $.fn.ajaxPost({
              ajaxType:"post",
              ajaxUrl:"system/api/post/bodyParams",
              ajaxData:inobjjson,
              succeed:function(result){
                  if(result["code"]>0){
                      if(result.apiData.flag=="true"||result.apiData.flag){
                             alertMsg(result.apiData.message);
                         }else{
                              alertMsg("提示:推送失败(<font color=#ff0000>"+result.apiData.message+"</font>)!");
                        }
                   }else{
                      alertMsg(errMsg+"操作失败!");
                  }
              }
          });
   }

   //推算截止日期
   var doGetEndDate=function(seleDateFlag,dateNumber1,dateNumber2){
       
      var thedate = new Date();
      var thedate2 = new Date();
      var params="";
      if(seleDateFlag=="工作日"){
          params={"query":"getCalendarInfo","objects":[thedate.getFullYear(),toDateFormatByZone(thedate,"yyyy-MM-dd")],"search":{"D_WEEKDAY_REMOVAL":"是"}};//取得当前日期后一年内所有的“工作日”排除日期
      }else{
          params={"query":"getCalendarInfo","objects":[thedate.getFullYear(),toDateFormatByZone(thedate,"yyyy-MM-dd")],"search":{"D_NATURAL_REMOVAL":"是"}};//取得当前日期后一年内所有的“自然日”排除日期
      }
     
         $.fn.ajaxPost({
              ajaxUrl:"system/jdbc/query/one/table",
              ajaxType: "post",
              ajaxData: params,
              succeed:function(result){
                  if(result["code"]>0){
                        var rows=result["rows"];
                        var noDoDateS=[];
                        for(var i=0;i<rows.length;i++){
                            var row=rows[i];
                            noDoDateS.push(toDateFormatByZone(row["D_DATE"],"yyyy-MM-dd"));
                        }
                        
                        //建库标准日期
                        for(var i=0;i<dateNumber1;i++){
                            var base = 1000 * 60 * 60 * 24;
                            thedate=new Date(thedate.getTime() + base);
                            for(var j=0;j<noDoDateS.length;j++){
                                if(toDateFormatByZone(thedate,"yyyy-MM-dd")==noDoDateS[j]){//存在排除日期测
                                  thedate=new Date(thedate.getTime()+base);//日期向前一天
                                }
                            }
                            
                        }
                        //推算出的最终截止日期
                       $("#TASK_LS_LDATE"+pathValue).val(toDateFormatByZone(thedate,"yyyy-MM-dd"));
                       
                       //实验标准交付日期
                       for(var i=0;i<dateNumber2;i++){
                           var base = 1000 * 60 * 60 * 24;
                           thedate2=new Date(thedate2.getTime() + base);
                           for(var j=0;j<noDoDateS.length;j++){
                                if(toDateFormatByZone(thedate2,"yyyy-MM-dd")==noDoDateS[j]){//存在排除日期测
                                  thedate2=new Date(thedate2.getTime()+base);//日期向前一天
                                }
                           }
                           
                       }
                       //推算出的最终截止日期
                      $("#TASK_LS_DELIVERDATE"+pathValue).val(toDateFormatByZone(thedate2,"yyyy-MM-dd"));
                      
                      alertMsg("提示:计算标准日期成功,请注意复核!");
                       
                  }
              }
         });        
   }
   
   //当前月份
   var getMyMonth=function (){
        var date=new Date;
        var month=date.getMonth()+1;
        return month;
   }
   funcPushs(pathValue,{
       "init":init,
       "comfireData":comfireData,
       "doSetCyc":doSetCyc,
   });

});