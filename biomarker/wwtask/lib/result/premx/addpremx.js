$(document).ready(function () {
	var pathValue = "biomarker-wwtask-lib-result-premx-addpremx";
	/**
 * 初始化数据-无参
 */
	var paramsValue;
	var initData = function () {
		return {
			tableName: "BIO_WW_PURCHASE_RECEIPT_MX"
		};
	}
	/**
	 * 初始化-获取参数-并执行调用
	 * @param {*} params 
	 */
	var init = function (params) {
		paramsValue = params;
		getInfo("form", pathValue, params);
		// 传入数组ids
		var url = "system/jdbc/query/info/" + initData().tableName;//后端请求路径
		getInfo("form", pathValue, params, url);//传入id
	}


	var submit = function () {
		$("#ITEMCLASSCODE" + pathValue).val("00");
		$("#ITEMCLASSNAME" + pathValue).val("T3项目管理");
		$("#INVENTORYCODE" + pathValue).val("900000");
		$("#INVNAME" + pathValue).val("委托加工");
		//  $("#QUANTITY"+pathValue).val("4");
		$("#CMASSUNITNAME" + pathValue).val("管数");
		var m1 = $("#IORIMONEY" + pathValue).val();
		var m2 = $("#TAXRATE" + pathValue).val();
		var m4 = $("#QUANTITY" + pathValue).val();
		var m3 = m1 * m2 / 100;
		debugger;
		var price = m3 / m4
		$("#IORITAXPRICE" + pathValue).val(m3);
		$("#COST" + pathValue).val(m1);
		$("#PRICE" + pathValue).val(price);
		var BILL_ID = paramsValue["BILL_ID"];
		var TAXRATE = $("#TAXRATE" + pathValue).val();
		var TASK_LS_NO = $("#CCCC" + pathValue).val();
		var DEFINE33 = $("#DEFINE33" + pathValue).val();
		var rows2;
		var DEPARTMENTCODE = $("#DEPARTMENTCODE" + pathValue).val();
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_cxwwcb_list", "objects": [[BILL_ID]] },
			succeed: function (rs) {
				//console.log(rs);				
				rows2 = rs;
			}
		});
		object = [];
		object.push({
			// M_ID:rows2["rows"][i]["ID"],
			"ID": rows2["rows"][0]["ID"],
			"DEPARTMENTCODE": DEPARTMENTCODE,
			"TAXRATE": TAXRATE,
			"WW_TASK_NO": TASK_LS_NO,
			"EX_DH_NO": TASK_LS_NO,
			"DEFINE33": DEFINE33,
		});
		var params = {
			tableName: "BIO_WW_PURCHASE_RECEIPT",
			objects: object,
		};


		var url = "system/jdbc/save/batch/table";
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: url,
			ajaxData: params,
			succeed: function (res) {
				if (res["code"] > 0) {
					//   alertMsg("保存列表成功", "seccess");
				} else {
					alertMsg("保存列表失败", "error");
				}
			},
		});


		formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					//提交成功
					var ID = result["ID"];
					$("#ID" + pathValue).val(ID)
					alertMsg("保存成功", "success", function () {
						//funcExce(pathValue+"pageCallBack");//执行回调
						// release(); 
					});
				} else {
					alertMsg("保存失败", "error");
				}
			}
		});
	}
	//提交
	var res = function () {
		$("#PUSH_STATE" + pathValue).val("待推送");
		$("#ITEMCLASSCODE" + pathValue).val("00");
		$("#ITEMCLASSNAME" + pathValue).val("T3项目管理");
		$("#INVENTORYCODE" + pathValue).val("900000");
		$("#INVNAME" + pathValue).val("委托加工");
		//  $("#QUANTITY"+pathValue).val("4");
		$("#CMASSUNITNAME" + pathValue).val("管数");
		var m1 = $("#IORIMONEY" + pathValue).val();
		var m2 = $("#TAXRATE" + pathValue).val();
		var m4 = $("#QUANTITY" + pathValue).val();
		var m3 = m1 * m2 / 100;
		debugger;
		var price = m3 / m4
		$("#IORITAXPRICE" + pathValue).val(m3);
		$("#COST" + pathValue).val(m1);
		$("#PRICE" + pathValue).val(price);
		var BILL_ID = paramsValue["BILL_ID"];
		var TAXRATE = $("#TAXRATE" + pathValue).val();
		var TASK_LS_NO = $("#CCCC" + pathValue).val();
		var DEFINE33 = $("#DEFINE33" + pathValue).val();
		var rows2;
		var DEPARTMENTCODE = $("#DEPARTMENTCODE" + pathValue).val();
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_cxwwcb_list", "objects": [[BILL_ID]] },
			succeed: function (rs) {
				//console.log(rs);				
				rows2 = rs;
			}
		});
		object = [];
		object.push({
			// M_ID:rows2["rows"][i]["ID"],
			"ID": rows2["rows"][0]["ID"],
			"DEPARTMENTCODE": DEPARTMENTCODE,
			"TAXRATE": TAXRATE,
			"WW_TASK_NO": TASK_LS_NO,
			"EX_DH_NO": TASK_LS_NO,
			"PUSH_STATE":"待推送",
			"DEFINE33": DEFINE33,
		});
		var params = {
			tableName: "BIO_WW_PURCHASE_RECEIPT",
			objects: object,
		};


		var url = "system/jdbc/save/batch/table";
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: url,
			ajaxData: params,
			succeed: function (res) {
				if (res["code"] > 0) {
					//   alertMsg("保存列表成功", "seccess");
				} else {
					alertMsg("保存列表失败", "error");
				}
			},
		});


		formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					//提交成功
					alertMsg("保存成功", "success", function () {
						release();
					});
				} else {
					alertMsg("保存失败", "error");
				}
			}
		});
	}

	var release = function () {
		var object = [];
		object.push({
			ID: paramsValue["WWID"], //样本ID
			DD_TASK_LS_STATUS: "已完成", //更新状态
			TASK_LS_STATUS: "已审核", //更新状态
		});
		debugger;
		var tableName = "BIO_TASK_LIB";
		var params = { tableName: tableName, objects: object };
		//插入任务明细记录
		var url = "system/jdbc/save/batch/table";
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: url,
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					//refreshGrid();
					// funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
					// alertMsg("保存成功!","success");
					// funcExce(pathValue+"close");//关闭页面
					funcExce(pathValue + "pageCallBack");//执行回调
					funcExce(pathValue + "close");//关闭页面
				} else {
					alertMsg("提交失败!", "error");
				}
			},
		});

	}

	var func = function () {
		var wdc = $("#DEPARTMENTCODE" + pathValue).val();
		if (wdc == "521A001") { $("#MEHOD_CBJRLAT" + pathValue).val("分子实验管理"); }
		if (wdc == "430200103") { $("#MEHOD_CBJRLAT" + pathValue).val("农学-DNA生信平台-基因组信息部"); }
		if (wdc == "*********") { $("#MEHOD_CBJRLAT" + pathValue).val("农学-DNA生信平台-群体信息部"); }
		if (wdc == "4403") { $("#MEHOD_CBJRLAT" + pathValue).val("医学事业部-信息部"); }
		if (wdc == "*********") { $("#MEHOD_CBJRLAT" + pathValue).val("农学-DNA生信平台-微生物二代"); }
		if (wdc == "*********") { $("#MEHOD_CBJRLAT" + pathValue).val("农学-RNA生信平台-转录调控二代"); }
	}
	var func33 = function () {
		debugger;
		var m1 = $("#IORISUM" + pathValue).val();
		var m2 = $("#TAXRATE" + pathValue).val();
		var m3;
		var aa = parseInt(m2) / 100;
		m3 = m1 / (1 + aa);
		$("#IORIMONEY" + pathValue).val(m3);
	}
	funcPushs(pathValue, {
		"init": init,
		"func": func,
		"func33": func33,
		"res": res,
		"submit": submit,
	});

});