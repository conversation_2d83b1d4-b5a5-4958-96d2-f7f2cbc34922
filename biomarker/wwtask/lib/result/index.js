$(document).ready(function () {
	var pathValue = "biomarker-wwtask-lib-result-index";
	var initData = function () {
		return {};
	}

	var gridNameGrid;
	var gridNameLGrid;
	var init = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			// {name:"edit",target:"opensel",title:"结果填写.."},
			//{ name: "edit", target: "edit4", title: "委外任务单" },
			{ name: "edit", target: "wwcb", title: "委外成本结算" },
			{ name: "excel", target: "importData2", title: "数据量导入" },
			{ name: "submit", target: "doGo", title: "批量提交" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_WW_BIO_TASK_LIB_view", "objects": [["待填写"], ["委外建库测序任务单"]] },
			headerFilter: function (cols, i) {
				if (i) {
					if (cols[i]["field"] && cols[i]["field"] == "TASK_LS_NO") {

						setJsonParam(cols[i], "template", getTemplate("#= TASK_LS_NO #", "funcExce(\'" + pathValue + "opensel\',\'#= ID #\',\'#= TASK_LS_TYPE #\');", "txt"));
					}
				}
			}
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);

		init_1();
	}

	var init_1 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "doReturn", title: "撤回" },
			{ name: "edit", target: "wwcbck", title: "查看委外成本结算" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_WW_BIO_TASK_LIB_view", "objects": [["已完成"], ["委外建库测序任务单"]] },
			headerFilter: function (cols, i) {
				if (i) {
					if (cols[i]["field"] && cols[i]["field"] == "TASK_LS_NO") {
						setJsonParam(cols[i], "template", getTemplate("#= TASK_LS_NO #", "funcExce(\'" + pathValue + "openselck\',\'#= ID #\',\'#= TASK_LS_TYPE #\');", "txt"));
					}
				}
			}
		};
		gridNameLGrid = initKendoGrid("#gridNameLGrid" + pathValue, gridNameGridJson);//初始化表格的方法
	}

	var opensel = function (ID, type) {
		var winOpts = {
			url: "biomarker/wwtask/lib/result/addlib/addlib",
			title: "填写:" + type + "结果.."
		};
		openWindow(winOpts, { "ID": ID, "TASK_LS_TYPE": type });
	}

	var wwcbck = function (ID) {
		debugger;
		var g = getGridSelectData(gridNameLGrid);

		if (g.length != 1) {
			alertMsg("请选择一条数据!");
			return;
		}
		var TASK_NO = g[0]["TASK_LS_NO"];
		var rows4 = "";
		//查询当前选中单是否已经生成委外成本结算
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_BIO_WW_PR_view", "objects": [[TASK_NO]] },
			succeed: function (rs) {
				//console.log(rs);				
				rows4 = rs;
			}
		});
		var arrIds = rows4["rows"][0];
		debugger;
		var BILL_ID = rows4["rows"][0]["BILL_ID"];

		var rows44;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_wwcbjsmx_list", "objects": [[BILL_ID]] },
			succeed: function (rs) {
				//console.log(rs);				
				rows44 = rs;
			}
		});
		openWindow({
			url: "biomarker/wwtask/lib/result/preck/addpreck",
			title: "添加",
			currUrl: "biomarker/wwtask/lib/result/index",
		}, {
			"ID": rows44["rows"][0]["ID"],
			"WW_ALLSAMPLES_NUMBER": rows44["rows"][0]["WW_ALLSAMPLES_NUMBER"],
			"IORISUM": rows44["rows"][0]["IORISUM"],
			"WW_SUMMARY_COST": rows44["rows"][0]["WW_SUMMARY_COST"],
			"WW_LIB_NORMAL": rows44["rows"][0]["WW_LIB_NORMAL"],
			"DEPARTMENTCODE": rows44["rows"][0]["DEPARTMENTCODE"],
			"MEHOD_CBJRLAT": rows44["rows"][0]["MEHOD_CBJRLAT"],
			"GENERATE_DATE": rows44["rows"][0]["GENERATE_DATE"],
			"CBDEFINE4": rows44["rows"][0]["CBDEFINE4"],
			"TAXRATE": rows44["rows"][0]["TAXRATE"],
			"WW_ABNORMAL_CAUSE": rows44["rows"][0]["WW_ABNORMAL_CAUSE"],
			"IORIMONEY": rows44["rows"][0]["IORIMONEY"],
			"WW_TOTAL_CA": rows44["rows"][0]["WW_TOTAL_CA"],
			"DEFINE28": rows44["rows"][0]["DEFINE28"],
			"PROJECT_NAME": rows44["rows"][0]["PROJECT_NAME"],
			"CBDEFINE3": rows44["rows"][0]["CBDEFINE3"],
			"DEFINE33": rows44["rows"][0]["DEFINE33"],
			"CCCC": g[0]["TASK_LS_NO"],
			"WW_SUPPLIER": rows44["rows"][0]["WW_SUPPLIER"],
			"CBDEFINE2": rows44["rows"][0]["CBDEFINE2"],
			"BILL_ID": rows44["rows"][0]["BILL_ID"],
			"ITEMCODE": rows44["rows"][0]["ITEMCODE"],
			"ITEMNAME": rows44["rows"][0]["ITEMNAME"],
			"BILL_CODE": rows44["rows"][0]["BILL_CODE"],
			"wwjfdate": rows44["rows"][0]["WW_DELIVERY_DATE"],
			"PROJECT_NAME": g[0]["PROJECT_NAME"],
			"WWID": g[0]["ID"],
		});


	}

	var openselck = function (ID, type) {
		debugger;
		var winOpts = {
			url: "biomarker/wwtask/lib/result/addlibxk/addlibxk",
			title: "填写:" + type + "结果.."
		};
		openWindow(winOpts, { "ID": ID, "TASK_LS_TYPE": type });
	}
	var callBack = function () {
		refreshGrid();
	};

	var refreshGrid = function () {
		if (gridNameGrid) {
			gridNameGrid.dataSource.read();
		}
		if (gridNameLGrid) {
			gridNameLGrid.dataSource.read();
		}
	}

	var inputCheck1 = function () {
		var arrIds = getGridSelectData(gridNameGrid);
		if (arrIds.length != 1) {
			alertMsg("请选择一条数据进行填写结果操作!");
			return;
		}
		var ids = [];
		lsmkeyps = [];
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			lsmkeyps.push(arrIds[i]["LSM_KEY_P"]);
		}
		var winOpts = {
			url: "biomarker/dispatch/dx/result/comfirm/comfirm",
			title: "委外建库测序任务结果填写.."
		}
		openWindow(winOpts, { "IDS": ids, "ID": ids[0], "LSMKEYP": lsmkeyps });

	}

	var doReturn = function () {

		var arrIds = getGridSelectData(gridNameLGrid);
		if (arrIds.length > 1) {
			alertMsg("请只选择一条数据进行操作!");
			return;
		}

		var params = { "query": "query_BIO_WW_PURCHASE_RECEIPT_view", "objects": [["待推送", "推送失败"]], "search": { "WW_TASK_NO": [arrIds[0]["TASK_LS_NO"]] } }
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var rows = result["rows"];
					var objectupww = [];
					objectupww.push({
						"ID": rows[0]["ID"],
						"PUSH_STATE": "待处理"
					});

					var objectup = [];
					objectup.push({
						"ID": arrIds[0]["ID"],
						"DD_TASK_LS_STATUS": "待填写"
					});

					var url = "system/jdbc/save/batch/table";
					var paramsaddqcmx = { "tableName": "BIO_TASK_LIB", "objects": objectup };
					putAddOrUpdata(url, paramsaddqcmx, "是", "更新主单状态");


				} else {
					alertMsg("提示:操作失败,该任务单对应的委外单已发往U8,无法更改!");
				}
			}
		});
	}

	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						alertMsg("提示:操作成功!");
						refreshGrid();
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}
	var edit4 = function () {

		debugger;
		var arrIds = getGridSelectData(gridNameGrid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行操作!");
			return;
		}
		var inobjjson = {
			"EX_DH_NO": arrIds[0]["TASK_LS_NO"],
			"DEFINE32": "委外建库",        //阶段
			"username": getLimsUser()["name"]
		}
		var RValue;
		$.fn.ajaxPost({
			ajaxUrl: "berry/automation/epo/wwtqjk",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: inobjjson,
			succeed: function (rs) {
				RValue = rs;
			}
		});


		alertMsg("操作成功!");
	}

	//委外成本结算
	var wwcb = function () {

		var g = getGridSelectData(gridNameGrid);
		var myDate = new Date();
		var days = sysNowTimeFuncParams["sysNowTime"];
		if (g.length != 1) {
			alertMsg("请选择一条数据!");
			return;
		}
		var TASK_NO = g[0]["TASK_LS_NO"];
		var rows4 = "";
		//查询当前选中单是否已经生成委外成本结算
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_BIO_WW_PR_view", "objects": [[TASK_NO]] },
			succeed: function (rs) {
				//console.log(rs);				
				rows4 = rs;
			}
		});
		//查询合同类型
		var PN = g[0]["PROJECT_NO"];
		var rows5 = [];
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_PT_NO_list", "objects": [[PN]] },
			succeed: function (rs) {
				//console.log(rs);				
				rows5 = rs;
			}
		});
		//获取序列    
		var rows6 = "";         //查询今日是否有生成过单号，有就查询出最后生成的单号 
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_dual_view", "objects": [] },
			succeed: function (rs) {
				//console.log(rs);				
				rows6 = rs;
			}
		});
		var sqlCode = 1000000000 + parseInt(rows6["rows"][0]["U8ID"]);
		//var arrIds = rows4["rows"][0];
		debugger;

		if (rows4["rows"].length > 0) {
			var BILL_ID = rows4["rows"][0]["BILL_ID"];
			if (rows4["rows"][0]["PUSH_STATE"] == "已推送") {
				alertMsg("此单已经推送，无法再做修改!");
				return;
			}
			var rows44;
			$.fn.ajaxPost({
				ajaxUrl: "system/jdbc/query/one/table",
				ajaxType: "post",
				ajaxAsync: false,
				ajaxData: { "query": "query_wwcbjsmx_list", "objects": [[BILL_ID]] },
				succeed: function (rs) {
					//console.log(rs);				
					rows44 = rs;
				}
			});
			if (rows44["rows"].length > 0) {

				openWindow({
					url: "biomarker/wwtask/lib/result/premx/addpremx",
					title: "添加",
					currUrl: "biomarker/wwtask/lib/result/index",
				}, {
					"ID": rows44["rows"][0]["ID"],
					"WW_ALLSAMPLES_NUMBER": rows44["rows"][0]["WW_ALLSAMPLES_NUMBER"],
					"IORISUM": rows44["rows"][0]["IORISUM"],
					"WW_SUMMARY_COST": rows44["rows"][0]["WW_SUMMARY_COST"],
					"WW_LIB_NORMAL": rows44["rows"][0]["WW_LIB_NORMAL"],
					"DEPARTMENTCODE": rows44["rows"][0]["DEPARTMENTCODE"],
					"MEHOD_CBJRLAT": rows44["rows"][0]["MEHOD_CBJRLAT"],
					"GENERATE_DATE": rows44["rows"][0]["GENERATE_DATE"],
					"CBDEFINE4": rows44["rows"][0]["CBDEFINE4"],
					"TAXRATE": rows44["rows"][0]["TAXRATE"],
					"WW_ABNORMAL_CAUSE": rows44["rows"][0]["WW_ABNORMAL_CAUSE"],
					"IORIMONEY": rows44["rows"][0]["IORIMONEY"],
					"WW_TOTAL_CA": rows44["rows"][0]["WW_TOTAL_CA"],
					"DEFINE28": rows44["rows"][0]["DEFINE28"],
					"PROJECT_NAME": rows44["rows"][0]["PROJECT_NAME"],
					"CBDEFINE3": rows44["rows"][0]["CBDEFINE3"],
					"DEFINE33": rows44["rows"][0]["DEFINE33"],
					"CCCC": g[0]["TASK_LS_NO"],
					"WW_SUPPLIER": rows44["rows"][0]["WW_SUPPLIER"],
					"CBDEFINE2": rows44["rows"][0]["CBDEFINE2"],
					"BILL_ID": rows44["rows"][0]["BILL_ID"],
					"ITEMCODE": rows44["rows"][0]["ITEMCODE"],
					"ITEMNAME": rows44["rows"][0]["ITEMNAME"],
					"BILL_CODE": rows44["rows"][0]["BILL_CODE"],
					"WW_DELIVERY_DATE": rows44["rows"][0]["WW_DELIVERY_DATE"],
					"PROJECT_NAME": g[0]["PROJECT_NAME"],
					"DEFINE32": "测序委外",
					"DEFINE31": rows44["rows"][0]["DEFINE31"],
					"WWID": g[0]["ID"],
					"DEFINE23": rows5["rows"][0]["CONTRACT_TYPE"],
				});
			} else {
				openWindow({
					url: "biomarker/wwtask/lib/result/premx/addpremx",
					title: "添加",
					currUrl: "biomarker/wwtask/lib/result/index",
				}, {
					"DEFINE28": g[0]["PROJECT_NO"],
					"PROJECT_NAME": g[0]["PROJECT_NAME"],
					"CBDEFINE3": g[0]["WW_NO"],
					"DEFINE33": g[0]["PROJECT_SUBNO"],
					"CCCC": g[0]["TASK_LS_NO"],
					"WW_SUPPLIER": g[0]["WW_SUPPLIER"],
					"CBDEFINE2": g[0]["WW_PRODUCT_TYPE"],
					"BILL_ID": BILL_ID,
					"BILL_CODE": BILL_ID,
					"WW_DELIVERY_DATE": g[0]["WW_DELIVERY_DATE"],
					"ITEMCODE": g[0]["CONTRACT_NO"],
					"ITEMNAME": rows5["rows"][0]["CONTRACT_NAME"],
					"AUTOID": sqlCode,
					"WWID": g[0]["ID"],
					"DEFINE32": "测序委外",
					"DEFINE31": sqlCode,
					"DEFINE23": rows5["rows"][0]["CONTRACT_TYPE"],

				});
			}



		} else {
			var year = myDate.getFullYear();
			var years = year.toString().substr(2, 2);
			var month = myDate.getMonth() + 1;
			var day = myDate.getDate();


            var NDAYPUSH;
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "query_SYS_DATA_DICT_list2", "objects": ["U8接口调用模式"], "search": { "ITEM_TEXT": "委外采购入库单" } },
                succeed: function (rs) {
                    NDAYPUSH = rs["rows"][0]["NDAYPUSH"];
    
                }
            });



			var jzday = days;
			if (day > NDAYPUSH) {
				if (month == 12) {
					year = year * 1 + 1;
					years = years * 1 + 1;
					month = 1;
					day = 1;
				} else {
					month = month * 1 + 1;
					day = 1;
				}
			}
			if (day < 10) {
				day = "0" + day;
			};
			if (month < 10) {
				month = "0" + month;
			};

			jzday = year + '-' + month + '-' + day;



			years = years.toString();
			//var sCode = years + month + day; 
			var sCode = years.concat(month, day);
			debugger;
			// var TASK_NO= g[0]["TASK_NO"];
			var ROW_ID = g[0]["ID"];
			var LIB_ID = g[0]["LIB_ID"];
			var rows3 = [];
			$.fn.ajaxPost({
				ajaxUrl: "system/jdbc/query/one/table",
				ajaxType: "post",
				ajaxAsync: false,
				ajaxData: { "query": "query_BIO_WW_PURCHASE_RECEIPT_lists", "objects": [sCode] },
				succeed: function (rs) {
					//console.log(rs);				
					rows3 = rs;
				}
			});

			var sopCode = rows3["rows"]["0"]["MAX(BILL_ID)"];
			if (rows3["rows"]["0"]["MAX(BILL_ID)"] != null) {
				// sopCode = sCode + String.format("%03d", parseInt(sqlCode.substring(6)) + 1);
				//sopCode = sCode.concat(parseInt(sopCode.substring(6)) + 1);
				var sc = parseInt(sopCode.substring(6)) + 1;
				if (sc < 10) {
					sc = "00" + sc;
				} else if (sc < 100) {
					sc = "0" + sc;
				}
				sopCode = sCode.concat(sc);
			} else {
				sopCode = sCode + "001";
			}
			//var rows1 = [];
			var rows2 = [];
			//查询委外供应商，供应商编号
			/* $.fn.ajaxPost({
			 ajaxUrl: "system/jdbc/query/one/table",
			 ajaxType: "post",
			 ajaxAsync: false,
			 ajaxData: { "query": "query_BIO_EXPERIMENT_OUTSOURCING_view", "objects": [[TASK_NO]]},
			 succeed: function (rs) {
				 //console.log(rs);				
				 rows1 = rs;	
			 }
		 });*/
			//查询子表数据
			$.fn.ajaxPost({
				ajaxUrl: "system/jdbc/query/one/table",
				ajaxType: "post",
				ajaxAsync: false,
				ajaxData: { "query": "query_BIO_TASK_LIBMX33_list", "objects": [], "search": { "TASK_LS_ID": [ROW_ID] } },
				succeed: function (rs) {
					//console.log(rs);				
					rows2 = rs;
				}
			});


			//var WW_SUPPLIER = rows1["rows"][0]["WW_SUPPLIER"];
			// var CONTRACT_TYPE = rows1["rows"][0]["CONTRACT_TYPE"];

			//保存主单数据
			var object = [];
			//var BILL_ID = rows2["rows"][0]["BILL_ID"];
			for (var i = 0; i < g.length; i++) {
				//获取所需要的主单数据
				object.push({
					// M_ID:rows2["rows"][i]["ID"],
					"PROJECT_NO": g[0]["PROJECT_NO"],
					"PROJECT_NAME": g[0]["PROJECT_NAME"],
					"WW_TASK_NO": g[0]["TASK_LS_NO"],
					"EX_DH_NO": g[0]["TASK_LS_NO"],
					"PROJECT_SUBNO": g[0]["PROJECT_SUBNO"],
					"W_NAME": g[0]["WW_SUPPLIER"],
					"WW_PRODUCT_TYPE": g[0]["PRODUCT_TYPE"],
					"WW_SUPJS_NO": g[0]["WW_NO"],
					"BILL_ID": sopCode,
					"BILL_CODE": sopCode,
					"WAREHOUSING_DATE": jzday,
					"GENERATE_DATE": jzday,
					"IMPUTATIONDATA": jzday,
					"RECEIVEFLAG": "1",
					"VOUCHTYPE": "01",
					"BUSINESSTYPE": "普通采购",
					"PURCHASETYPECODE": "01",
					"SOURCE": "库存",
					"WAREHOUSECODE": "10",
					"RECEIVECODE": "112",
					"MEMORY": "1",
					"MAKER": getLimsUser()["name"],
					"PUSH_STATE": "草稿",
					"VENDORCODE": g[0]["W_CODE"],
					"TAXRATE": "13",
					"EXCHNAME": "人民币",
					"EXCHRATE": "1",

				});
			}
			var params = {
				tableName: "BIO_WW_PURCHASE_RECEIPT",
				objects: object,
			};
			var url = "system/jdbc/save/batch/table";
			$.fn.ajaxPost({
				ajaxType: "post",
				ajaxUrl: url,
				ajaxData: params,
				succeed: function (res) {
					if (res["code"] > 0) {
						//  alertMsg("保存列表成功", "seccess");
					} else {
						// alertMsg("保存列表失败", "error");
					}
				},
			});

			openWindow({
				url: "biomarker/wwtask/lib/result/premx/addpremx",
				title: "添加",
				currUrl: "biomarker/wwtask/lib/result/index",
			}, {
				"DEFINE28": g[0]["PROJECT_NO"],
				"PROJECT_NAME": g[0]["PROJECT_NAME"],
				"CBDEFINE3": g[0]["WW_NO"],
				"DEFINE33": g[0]["PROJECT_SUBNO"],
				"CCCC": g[0]["TASK_LS_NO"],
				"WW_SUPPLIER": g[0]["WW_SUPPLIER"],
				"CBDEFINE2": g[0]["WW_PRODUCT_TYPE"],
				"BILL_ID": sopCode,
				"BILL_CODE": sopCode,
				"WW_DELIVERY_DATE": g[0]["WW_DELIVERY_DATE"],
				"ITEMCODE": g[0]["CONTRACT_NO"],
				"ITEMNAME": rows5["rows"][0]["CONTRACT_NAME"],
				"AUTOID": sqlCode,
				"WWID": g[0]["ID"],
				"DEFINE32": "测序委外",
				"DEFINE31": sqlCode,
				"DEFINE23": rows5["rows"][0]["CONTRACT_TYPE"],

			});
		}
	}
	var importData2 = function (componentId) {

		var arrIds = getSelectData(gridNameGrid);
		if (arrIds.length == 0) {
			alertMsg("请至选择一条记录进行操作!");
			return;
		}

 

		pass = 0;
		debugger;
		openComponent({
			name: "导入数据",//组件名称
			componentId: componentId,
			params: {
				template: function (p, n) {
					return exportAndImportData({
						expKey: "A",
						tableName: "BIO_TASK_LIB",
						requestData: {
							ajaxData: { "query": "query_WW_BIO_TASK_LIB_view", "size": 7000, "objects": [["待填写"], ["委外建库测序任务单"]], "search": { "ID": arrIds } },
						},
						params: p,
						name: n,
					});
				},
				"import": function (info, importPathValue) {
					var m = mask(importPathValue, "正在提交...");
					unmask(m);
					$.fn.ajaxPost({
						ajaxUrl: "/berry/automation/sop/wwjkcb",//导入数据
						ajaxType: "post",
						ajaxData: { "info": info, "username": getLimsUser()["name"] },
						succeed: function (results) {
							if (results["code"] > 0) {
								refreshGrid();
								alertMsg("导入成功!", "success", funcExce(importPathValue + "close"));

							} else {
								alertMsg("导入失败!");
							}

						},
						failed: function (result) {
							unmask(m);
							alertMsg(result["msg"], "error");
						}
					});

				}
			},
			callBack: refreshGrid
		});

	}


	//确认提交
	var doGo = function () {debugger
		var arrIds = getGridSelectData(gridNameGrid);
		if (arrIds.length == 0) {
			alertMsg("请至选择一条记录进行操作!");
			return;
		}

 

		var object = [];
		var objectNO = [];
		for (var i = 0; i < arrIds.length; i++) {
			objectNO.push(arrIds[i]["TASK_LS_NO"])
			object.push({
				ID: arrIds[i]["ID"], //样本ID
				DD_TASK_LS_STATUS: "已完成", //更新状态
				TASK_LS_STATUS: "已审核", //更新状态
			});
		}



		var params = { "query": "query_BIO_WW_PURCHASE_RECEIPT_view", "objects": [["草稿", "待推送", "推送失败"]], "search": { "WW_TASK_NO":objectNO } }
		var rows;
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxData: params,
			ajaxAsync: false,
			succeed: function (result) { 
				rows= result.rows;    
			}
		});
 
		var objectupww = [];

		for (var i = 0; i < arrIds.length; i++) {
			objectupww.push({
				"ID": rows[i]["ID"],
				"PUSH_STATE": "待推送"
			});
		}

		

		var url = "system/jdbc/save/batch/table";
		var paramsaddqcmx = { "tableName": "BIO_WW_PURCHASE_RECEIPT", "objects": objectupww };
		 putAddOrUpdata(url, paramsaddqcmx, "是", "更新主单状态");
	
  
		var tableName = "BIO_TASK_LIB";
		var params = { tableName: tableName, objects: object };
		 putAddOrUpdata(url, params, "是", "更新主单状态");



	}


	funcPushs(pathValue, {
		"initData": initData,
		"init": init,
		"openselck": openselck,
		"wwcb": wwcb,
		"doGo": doGo,
		"edit4": edit4,
		"wwcbck": wwcbck,
		"opensel": opensel,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"inputCheck1": inputCheck1,
		"importData2": importData2,
		"doReturn": doReturn
	});
});