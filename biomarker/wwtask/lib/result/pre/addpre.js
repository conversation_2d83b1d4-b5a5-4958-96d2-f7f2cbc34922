$(document).ready(function () {
	var pathValue = "biomarker-wwtask-lib-result-pre-addpre";
	var paramsValue;
	var gridNameGrid;
	var isAdd = false;
	debugger;
	// 主单数据库表
	var initData = function () {
		return {
			tableName: "BIO_WW_PURCHASE_RECEIPT_MX",
		};
	};

	// 页面初始化
	var DID;
	var U8ID;

	var init = function (params) {
		//params["SOURCE"] = "研发结果";
		paramsValue = params;
		getInfo("form", pathValue, params);
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
		gridNameGrid_init();
		

	};	
	// 物料明细
	var gridNameGrid_init = function () {
		/**
		 * 列表-按钮-定义 BILL_ID
		 */
        var BILL_ID = $("#BILL_ID"+pathValue).val();
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "add", target: "edit", title: "填写物料信息"},
			//{ name: "delete", target: "deleteMaterial", title: "删除"},
		]); //工具条
               
		//请求参数
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table", //请求json的路径-针对的查询
			sort: "", //排序
			height: fullh - 300,
			toolbar: toolbar,
			read: {
				"query": "query_BIO_WW_PURCHASE_RECEIPT_MX_list",
				"objects": [BILL_ID],
			},
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson); //初始化表格的方法
	}

	// 从页面删除数据
	var deleteMaterial = function () {
		var arrSelect = getSelectData(gridNameGrid);
		var oldlist = getGridItemsData(gridNameGrid);
		if (arrSelect.length == 0) {
			alertMsg("请至少选择一条数据进行删除操作!");
			return;
		}
		if (oldlist.length - arrSelect.length < 1) {
			alertMsg("至少要有一条物料明细,不能全部删除");
			return;
		}
		confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function () {
			for (var i = 0; i < arrSelect.length; i++) {
				var params = { tableName: "BIO_WW_COST_SETTLEMENT_MX", ids: arrSelect[i]};
				var url = "system/jdbc/delete/batch/table";
				deleteGridDataByIds(url, params, refreshGrid);
			}
		});
	};


	var refreshGrid = function () {
		if (gridNameGrid) {
			gridNameGrid.dataSource.read(); //重新读取--刷新
		}
	};

	var callBack = function () {
		refreshGrid();
	};


	// 删除主单对应列表在数据库的数据
	var isExist = function () {
		var params = {
			tableName: "BIO_WW_COST_SETTLEMENT_MX",
			where: { M_ID: $("#ID" + pathValue).val(), },
		};
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/delete/one/table/where",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					updateListMx();
				} else {
					alertMsg("删除旧列表失败", "error");
				}
			},
		});
	};

	// 主单在数据库是否对应有数据 有删除
	var deleteMaterials = function () {
		var params = {
			query: "query_BIO_WW_COST_SETTLEMENT_view",
			objects: [$("#ID" + pathValue).val()],
		}; //查询列表
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxData: params,
			ajaxAsync: false,
			succeed: function (result) {
				if (result["code"] > 0 && result["rows"].length > 0) {
					isExist();
				} else {
					updateListMx();
				}
			},
		});
	};

	// 更新列表到数据库
	var updateListMx = function () {
		var g = getGridItemsData(gridNameGrid);
		var object = [];
		for (var i = 0; i < g.length; i++) {
			//更新状态

			object.push({
				ID:g[i]["ID"],
				PROJECT_NO: g[i]["PROJECT_NO"],
				SAMPLE_BATCHNO: g[i]["SAMPLE_BATCHNO"],
				SAMPLE_CODE: g[i]["SAMPLE_CODE"],
				PRODUCT_TYPE: g[i]["PRODUCT_TYPE"],
				SAMPLE_NAME: g[i]["SAMPLE_NAME"],
				TASK_LSSQ_GODATA: g[i]["TASK_LSSQ_GODATA"],
				DATA_UNIT: g[i]["DATA_UNIT"],
				TASK_LSMX_FDATA: g[i]["TASK_LSMX_FDATA"],
				IS_HG: g[i]["IS_HG"],
				WW_COMPENSATION_AMOUNT: g[i]["WW_COMPENSATION_AMOUNT"],
				WW_SETTLEMENT_COST: g[i]["WW_SETTLEMENT_COST"]
			});
		}
		var params = {
			tableName: "BIO_WW_COST_SETTLEMENT_MX",
			objects: object,
		};
		var url = "system/jdbc/save/batch/table";
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: url,
			ajaxData: params,
			succeed: function (res) {
				if (res["code"] > 0) {
					alertMsg("保存成功", "success", function () {
						funcExce(pathValue + "pageCallBack"); //执行回调
						funcExce(pathValue + "close"); //关闭页面
					});
				} else {
					alertMsg("保存列表失败", "error");
				}
			},
		});
	};

	// 保存
	var submit = async function () {
		debugger;
	    var g = getGridItemsData(gridNameGrid);
		if (g.length == 0) {
			alertMsg("至少要有一条结果明细");
			return;
		}
for(var i = 0; i<g.length;i++){
            if (g[i]["IORITAXPRICE"]== null || g[i]["IORITAXPRICE"]== "") {
			alertMsg("请先填写完整物料信息");
			return;
		}
}
		 formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					//lbs();
				alertMsg("保存主单成功", "success");
				funcExce(pathValue+"pageCallBack");//执行回调
                funcExce(pathValue+"close");//关闭页面
					
				} else {
					alertMsg("保存主单失败", "error");
				}
			},
		});
	};
        // 提交
	var res =  function () {
		debugger;
	    var g = getGridItemsData(gridNameGrid);
		if (g.length == 0) {
			alertMsg("至少要有一条结果明细");
			return;
		}
            if (g[0]["INVENTORYCODE"]== null || g[0]["INVENTORYCODE"]== "") {
			alertMsg("请先填写结果明细");
			return;
		}
		 formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
                    lti();
					//lbs();
				alertMsg("提交成功", "success");
				funcExce(pathValue+"pageCallBack");//执行回调
                funcExce(pathValue+"close");//关闭页面
					
				} else {
					alertMsg("提交失败", "error");
				}
			},
		});
	};
        var lti = function(){
debugger;
            var object = [];
            var MID =  paramsValue["AA"];
            //var BILL_ID = rows2["rows"][0]["BILL_ID"];
          // for (var i = 0; i < rows2["rows"].length; i++) {
                //获取所需要的子表数据
                object.push({
                   ID:MID,
                   DD_TASK_LS_STATUS:"已完成",
                   TASK_LS_STATUS:"已审核"

                });
       //     }
            var params = {
                tableName: "BIO_TASK_LIB",
                objects: object,
                 };
            var url = "system/jdbc/save/batch/table";
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: url,
                ajaxData: params,
                succeed: function (res) {
                    if (res["code"] > 0) {
                        //  alertMsg("保存列表成功", "seccess");
                    } else {
                        // alertMsg("保存列表失败", "error");
                    }
                },
            });


        }


        //保存列表
	var lbs = function () {
		var g = getGridItemsData(gridNameGrid);
		var object = [];
		for (var i = 0; i < g.length; i++) {
			object.push({
				TASK_ID:g[i]["TASK_LS_ID"],
				TASK_LSSQ_GODATA: g[i]["TASK_LSSQ_GODATA"],
				DATA_UNIT: g[i]["DATA_UNIT"],
				TASK_LSMX_FDATA: g[i]["TASK_LSMX_FDATA"],
				IS_HG : g[i]["IS_HG"],
			});
		}
		var params = {
			tableName: "BIO_WW_PURCHASE_RECEIPT_MX",
			objects: object,
		};
		var url = "system/jdbc/save/batch/table";
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: url,
			ajaxData: params,
			succeed: function (res) {
				if (res["code"] > 0) {
					alertMsg("保存成功", "success", function () {
						funcExce(pathValue + "pageCallBack"); //执行回调
						funcExce(pathValue + "close"); //关闭页面
					});
				} else {
					alertMsg("保存列表失败", "error");
				}
			},
		});
	};
	// 回填添加到列表上
	var addSub = function (wlDataList) {
		for (var i = 0; i < wlDataList.length; i++) {
			gridNameGrid.dataSource.add(wlDataList[i]);
		}
	}

   var open=function(ID){
        var winOpts={
            url:"biomarker/wwtask/lib/result/premx/addpremx",
            title:"填写:委外成本明细.."
        };
        openWindow(winOpts,{"ID":ID});//传递id
    }
    var edit=function(){
        var arrIds=getSelectData(gridNameGrid);
             if(arrIds.length != 1){
               alertMsg("请有且只有选择一条数据!");
               return ;
         }       
		open(arrIds[0]);
     }
      var close1=function(){
       debugger;
        var arr=getGridItemsData(gridNameGrid);
       if(arr.length > 0){
            var M_ID= $("#ID"+pathValue).val();
           if(M_ID == null || M_ID ==""|| M_ID == undefined){
                 deleteInfo();
             }
         funcExce(pathValue+"close");//关闭页面
      }else{
           funcExce(pathValue+"close");//关闭页面
       }

        
    }

            var deleteInfo=function(){
               debugger;
               var arrIds = getGridItemsData(gridNameGrid);
               var object = [];
               for (var i = 0; i < arrIds.length; i++) {
                    //获取所需要的子表数据
                    object.push(arrIds[i]["ID"]);
                }
            var params = {
			tableName: "BIO_WW_PURCHASE_RECEIPT_MX",
			 "ids": object,
		};
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/delete/batch/table",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					//alertMsg("cg", "error");
				} else {
					alertMsg("删除明细列表失败", "error");
				}
			},
		});
     }

var func=function(){
    var wdc=$("#DEPARTMENTCODE"+pathValue).val(); 
       if(wdc=="521A001"){      $("#MEHOD_CBJRLAT"+pathValue).val("分子实验管理");  }      
       if(wdc=="430200103"){      $("#MEHOD_CBJRLAT"+pathValue).val("农学-DNA生信平台-基因组信息部");  }      
       if(wdc=="430200104"){      $("#MEHOD_CBJRLAT"+pathValue).val("农学-DNA生信平台-群体信息部");  }      
       if(wdc=="4403"){      $("#MEHOD_CBJRLAT"+pathValue).val("医学事业部-信息部");  }      
       if(wdc=="430200105"){      $("#MEHOD_CBJRLAT"+pathValue).val("农学-DNA生信平台-微生物二代");  }      
       if(wdc=="430200202"){      $("#MEHOD_CBJRLAT"+pathValue).val("农学-RNA生信平台-转录调控二代");  }      
     }
	funcPushs(pathValue, {
		"init": init,
		"deleteMaterial": deleteMaterial,
                "deleteInfo":deleteInfo,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"submit": submit,
                "func":func,
                "edit":edit,
                "res":res,
                "close1":close1,
                "deleteInfo":deleteInfo,
		"addSub": addSub,
	});
});