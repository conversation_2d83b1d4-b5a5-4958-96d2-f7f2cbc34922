$(document).ready(function() {
    
	var pathValue="biomarker-wwtask-tq-result-comfirm-comfirm";
   
   var paramsValue;
	var gridNameGrid;
   var initData=function(){
	   return {
		   tableName:"BIO_TASK_LIB"
	   };
   }
   var init=function(params){
	   paramsValue=params;
	   gridNameGrid=params["gridNameGrid"];
	   getInfo("form",pathValue,params);
	   // 传入数组ids
	   var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
	   getInfo("form",pathValue,params,url);//传入id
   }


 var comfireData=function(){
	   
	   //取出IDS
	   var ids=paramsValue["IDS"];    	
	   var object=[];


	   var paramsF = getJsonByForm("form",pathValue);
		var username=getLimsUser()["name"];
		var time=sysNowTimeFuncParams["sysNowTime"];
		var passFlag=paramsF["TASK_COMFIRM_RESULT"];
		var backmsg=paramsF["EX_BACK"];
		
		var flagstr="";
		var flagstr2="";
		if(passFlag=="通过"){
			flagstr="待审核";
			flagstr2="已审核";
	   
		}else if(passFlag=="退回"){
			
			flagstr="草稿";
			flagstr2="审核退回";
			
		}else if(passFlag=="终止"){
			flagstr="终止";
			flagstr2="已终止";
		}

		for(var i=0;i < ids.length;i++){

			object.push({
					   "ID":ids[i],
					   "DD_TASK_LS_STATUS":flagstr,
					   "TASK_LS_STATUS":flagstr2,
					   "TASK_LS_BACK":backmsg,
					   "TASK_LS_CDATE":time
					   });
		}

		//执行更新
		var params={"tableName":"BIO_TASK_LIB","objects":object};

		//插入任务明细记录
		var url="system/jdbc/save/batch/table";
		$.fn.ajaxPost({
			ajaxType:"post",
			ajaxUrl:url,
			ajaxData:params,
			succeed:function(result){
				
				if(result["code"]>0){
					   if(passFlag!="通过"){
									var url="system/jdbc/update/one/table/where";
									var jiraup= {"tableName":"JIRA_LSM_LIB","CUSTOMFIELD_14713":"非一次审核通过", "CUSTOMFIELD_14714":"非一次审核通过","where":{"MAIN_ID":ids}};
						   putAddOrUpdata(url,jiraup,"否","");
					  }

					funcExce(pathValue+"pageCallBack");
 
				 alertMsg("提交成功!");
				 
				 funcExce(pathValue+"close");//关闭页面
					
				}else{
					console.log(result);
				}
			}
		});    	
   }


 
   
   var comfireData2=function(){
		 var m=mask(pathValue,"正在提交资金划拔,请稍等...");
debugger;
	   var ids=paramsValue["IDS"];    	
	   var object=[];


	   var paramsF = getJsonByForm("form",pathValue);
		var username=getLimsUser()["name"];
		var time=sysNowTimeFuncParams["sysNowTime"];
		var passFlag=paramsF["TASK_COMFIRM_RESULT"];
		var backmsg=paramsF["EX_BACK"];
		
		var flagstr="";
		var flagstr2="";
		if(passFlag=="通过"){
			flagstr="待审核";
			flagstr2="已审核";
	   
		}else if(passFlag=="退回"){
			
			flagstr="草稿";
			flagstr2="审核退回";
			
		}else if(passFlag=="终止"){
			flagstr="终止";
			flagstr2="已终止";
		}

	   
	  if( passFlag=="通过"){
		  
			  var arrIds=getGridSelectData(gridNameGrid);
			  //检测设置状态
			  var ids=[];
			  var params=[];
				  var ht;
				  var cp;
				  var xm;
				  var xh;
				  var qd;
				  var bx;
			  for(var i=0;i<arrIds.length;i++){
					  ids.push(arrIds[i]["ID"]);
					  if(arrIds[i]["BEGIN_STATUS"]=="已划拔"){
						  alertMsg("提示:已执行过划拔,请勿重复操作!");
						   unmask(m);
						  return ;
					  }
					  ht=arrIds[i]["CONTRACT_NO"];
					  cp=arrIds[i]["PRODUCT_NO"];
					  xm=arrIds[i]["PROJECT_NO"];
					  xh=arrIds[i]["PROJECT_SUBNO"];
					  qd=arrIds[i]["TASK_LS_SAMPLESUM"];
					  bx=arrIds[i]["THE_DATA_SUM"];
			   if(qd>0 && bx>0){ }else{
						alertMsg("提示:启动样品数或本数据量小于0!");
						   unmask(m);
						  return ;
						 }
					  params.push({
						  "contractNo":ht,//合同编号
						  "productNum":cp,// 产品编号
						  "projectNum":xm,//项目编号
						  "issue":xh,//项目期号
						  "startUpSampleNumber":qd,//启动样品数
						  "dataVolume":bx///本期数据量
				  });
				 
			  }
	   
			  var inobjjson={ "url":"http://"+JIRRA_URL+"/api/capital/pool/huaKai/start", "bodyParams":params };
	 
			   $.fn.ajaxPost({
				   ajaxType:"post",
				   ajaxUrl:"system/api/post/bodyParams",
				   ajaxData:inobjjson,
				   succeed:function(result){
debugger;
						unmask(m);
					 var m2=mask(pathValue,"资金划拔成功,正在提交审核,请稍等...");
					   if(result["code"]>0){
						   
					   var object2=[];
						 if(result.apiData.result=="true"||result.apiData.result){//总的情况

							   if(result.apiData.list[0].result=="true"||result.apiData.list[0].result){//每一项目情况
							   for(var i=0;i < ids.length;i++){
									 object2.push({
											 "ID":ids[i],
											 "DD_TASK_LS_STATUS":flagstr,
											 "TASK_LS_STATUS":flagstr2,
											 "TASK_LS_CDATE":time,
											 "TASK_LS_BACK":backmsg,
											 "BEGIN_STATUS":"已划拔"
										});


								 }
							 unmask(m2);
							   var params={"tableName":"BIO_TASK_LIB","objects":object2};
							   var url="system/jdbc/update/batch/table";
								  putAddOrUpdata(url,params,"是","");
								  
						   }else{//划拔失败
								  
								  for(var i=0;i < ids.length;i++){
										 object2.push({
												 "ID":ids[i],
												 "TASK_LS_STATUS":"审核退回",
												 "BEGIN_STATUS":"已失败",
												 "TASK_LS_BACK":result.apiData.list[0].msg
											});

									 }
								 unmask(m2);
								  var params={"tableName":"BIO_TASK_LIB","objects":object2};
								   var url="system/jdbc/update/batch/table";
									putAddOrUpdata(url,params,"失败",result.apiData.list[0].msg);
									
							 }
					 }else{//已存在划拔
						 for(var i=0;i < ids.length;i++){
											  object2.push({
													  "ID":ids[i],
													  "DD_TASK_STATUS":flagstr,
													  "TASK_STATUS":flagstr2,
													  "TASK_TYPE_LB":typelb,
													  "TASK_CDATE":time,
													  "TASK_BACK":backmsg,
													  "BEGIN_STATUS":"已划拔"
												 });

										  }
										var params={"tableName":"BIO_TQ_TASK","objects":object2};
										var url="system/jdbc/update/batch/table";
								   unmask(m2);
										putAddOrUpdata(url,params,"是","");
				}   
						   
						   
						}else{
						   alertMsg(errMsg+"操作失败!");
					   }
				   }
			   }); 
			   
	  }else{//不通过
debugger;
		  unmask(m);
		  for(var i=0;i < ids.length;i++){
				 object.push({
					"ID":ids[i],
				   "DD_TASK_LS_STATUS":flagstr,
				   "TASK_LS_STATUS":flagstr2,
				   "TASK_LS_BACK":backmsg,
				   "TASK_LS_CDATE":time
					});

			 }
		  var params={"tableName":"BIO_TASK_LIB","objects":object};
		  //插入任务明细记录
		  var url="system/jdbc/save/batch/table";
		  $.fn.ajaxPost({
			  ajaxType:"post",
			  ajaxUrl:url,
			  ajaxData:params,
			  succeed:function(result){
				  unmask(m);
				  if(result["code"]>0){
					   var url="system/jdbc/update/one/table/where";
					   var jiraup= {"tableName":"JIRA_LSM_LIB","CUSTOMFIELD_14713":"非一次审核通过", "CUSTOMFIELD_14714":"非一次审核通过","where":{"MAIN_ID":ids}};
					   putAddOrUpdata(url,jiraup,"否","");
						 
						funcExce(pathValue+"pageCallBack");
					 alertMsg("提交成功!");
					 funcExce(pathValue+"close");
				  }else{
					  console.log(result);
				  }
			  }
		  }); 
		  
	  }
	   
	   
   }

 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
	 $.fn.ajaxPost({
		 ajaxType:"post",
		 ajaxUrl:urls,
		 ajaxData:inobjjson,
		 succeed:function(result){
			 if(result["code"]>0){
				 if(isDoCallBack=="是"){
					 alertMsg("提示:操作成功!");
					 funcExce(pathValue+"pageCallBack");
					 funcExce(pathValue+"close");
				 }else if(isDoCallBack=="失败"){
					 alertMsg("提示:操作失败,原因("+errMsg+")!");
					 funcExce(pathValue+"pageCallBack");
					 funcExce(pathValue+"close");
				 }
			 }else{
				 alertMsg(errMsg+"操作失败!");
			 }
		 }
	 });
 }
   funcPushs(pathValue,{
	   "init":init,
	   "comfireData":comfireData,
	   "comfireData2":comfireData2
   });

});