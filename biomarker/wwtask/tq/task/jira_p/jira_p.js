$(document).ready(function() {
    var pathValue="biomarker-wwtask-tq-task-jira_p-jira_p";
    var paramsValue;
   var initData=function(){
       return {
           tableName:"JIRA_LSM_TQ_P"
       };
   }
   var init=function(params){
       paramsValue=params;
       getInfo("form",pathValue,params);
       var url="system/jdbc/query/info/"+initData().tableName;
       getInfo("form",pathValue,params,url);
  }

var submit=function(){
   //表单校验
   var formJson = { formId:"form", pathValue:pathValue };
   var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
   if ( !validator.validate() ) {
          alertMsg("表单验证未通过","error");
          return false;
   }
   var p = getJsonByForm("form", pathValue);
   var params = {
       "jiraKey":p["LSM_KEY"],
       "oldStatusName":"已录入待下单",
       "statusName":"样品提取检测",
       "updateField":{
           "customfield_11713":p["CUSTOMFIELD_11713"],//批次编号
           "customfield_10112":p["CUSTOMFIELD_10112"],//项目编号
           "customfield_11008":{"value":p["CUSTOMFIELD_11008"]},//事业部
           "customfield_11009":p["CUSTOMFIELD_11009"],//本期首次到样日期
           "customfield_11416":p["CUSTOMFIELD_11416"],//预计启动样品数
           "customfield_11525":p["CUSTOMFIELD_11525"],//运营经理描述信息
           "customfield_14707":{"value":p["CUSTOMFIELD_14707"]},//提取检测任务单内审质量
           "customfield_14708":{"value":p["CUSTOMFIELD_14708"]}//提取检测任务单外审质量
       }
   };   
           
    
   var inobjjson={ "url":"http://"+JIRRA_URL+"/api/jira/sysInfo", "bodyParams": params };
    $.fn.ajaxPost({
        ajaxType:"post",
        ajaxUrl:"system/api/post/bodyParams",
        ajaxData:inobjjson,
        succeed:function(result){
            if(result["code"]>0){
                if(result.apiData.flag=="true"){
                       var url="system/jdbc/update/one/table/where";
                       var paramjson={"tableName":"BIO_TQ_TASK","LSM_KEY_P":p["LSM_KEY"],"JIRA_STATUS_P":"已推送","where":{"ID":paramsValue["ID"]}};
                       putAddOrUpdata(url,paramjson,"是","");
                   }else{
                        alertMsg("提示:推送失败(<font color=#ff0000>"+result.apiData.message+"</font>)!");
                  }
             }else{
                alertMsg(errMsg+"操作失败!");
            }
        }
    });
}


var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
                  if(isDoCallBack=="是"){
                     alertMsg("推送成功!");
                   funcExce(pathValue+"pageCallBack");
                  }
              }else{
                  alertMsg(errMsg+"操作失败!");
              }
          }
      });
}

funcPushs(pathValue,{
   "init":init,
   "submit":submit,
});

});