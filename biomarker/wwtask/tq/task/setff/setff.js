$(document).ready(function () {
    var pathValue = "biomarker-wwtask-tq-task-setff-setff";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "BIO_TQ_TASK_MX"
        };
    }
    var init = function (params) {
        paramsValue = params;
        getMyInfo();
    }

    var getMyInfo = function () {

        var TASK_TYPE = paramsValue["TASK_TYPE"];


        var paramssql = { "query": "query_BIO_QC_MD_LIBRARY_SRC_list", "objects": [], "search": { "PRODUCT_TYPE": [paramsValue["PRODUCT_TYPE"]] } };
        if (paramsValue["NUM"] != 0) {
            paramssql = { "query": "query_BIO_QC_MD_LIBRARY_SRC_list", "objects": [], "search": { "PRODUCT_TYPE": [paramsValue["PRODUCT_TYPE"]], SPECIES_ORIGIN_UNION: paramsValue["VARIETY"] } };
        }





        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxData: paramssql,
            succeed: function (result) {
                debugger;
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        getInfo("form", pathValue, row);
                        break;
                    }
                    if (rows.length < 1) {
                        alertMsg("自动获取方法库“<font color=#FF0000>" + paramsValue["PRODUCT_TYPE"] + "</font>”失败,请检查!");
                    }
                }
                $("#VARIETY" + pathValue).val(paramsValue["VARIETY"]);
                // if (paramsValue["task_type"] == "HE染色" || paramsValue["task_type"] == "组织优化" || paramsValue["task_type"] == "基因表达" || paramsValue["task_type"] == "单细胞预实验") {
                //     $("#MEHOD_PLAT" + pathValue).val("非常规提取");
                //     $("#MEHOD_JCFLOW" + pathValue).val("医学检测");
                //     $("#LIBRARY_FLOW" + pathValue).val("HIC建库");
                //     //$("MEHOD_PLAT"+pathValue).val("非常规提取");
                // }
            }
        });
    }

    var submit = function () {
        var validator = $("#form" + pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) { } else {
            alertMsg("验证未通过", "wran");
            return;
        }
        var ids = paramsValue["IDS"];
        var jsonData = getJsonByForm("form", pathValue);
        var object = [];


        var TASK_TYPE = paramsValue["TASK_TYPE"];
        for (var i = 0; i < ids.length; i++) {
            object.push($.extend({}, jsonData, { "ID": ids[i] }));
            if (TASK_TYPE == "制备" && paramsValue["object"][i]["LIBRARY_TYPE_EN"] != null) {
                delete object[i]["LIBRARY_TYPE_EN"];
                delete object[i]["LIBRARY_TYPE"];
            }
        }
        var url = "system/jdbc/save/batch/table";
        var params = { "tableName": "BIO_TQ_TASK_MX", "objects": object };
        putAddOrUpdata(url, params, "是", "");
    }

    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        funcExce(pathValue + "pageCallBack");
                        alertMsg("提交成功!");
                        funcExce(pathValue + "close");
                    }

                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});