$(document).ready(function() {
    var pathValue="biomarker-wwtask-tq-task-jira-jira";

    var paramsValue;
    var status;
    var LSMstatus;
    var PCHNumber=0;//第几次下单
    var time=sysNowTimeFuncParams["sysNowTime"];
    var username=getLimsUser()["name"];
   var initData=function(){
       return {
           tableName:"JIRA_LSM_TQ"
       };
   }
   var init=function(params){
       paramsValue=params;
       getInfo("form",pathValue,params);
       var url="system/jdbc/query/info/"+initData().tableName;

       getInfo("form",pathValue,params,url,function(p,v){
           debugger;
            getJira(p["LSM_KEY_P"]);
            getJiraLSM(p["LSM_KEY"]);
            queryCheckPCNumber(p["CUSTOMFIELD_14600"]);
            getInfo("form",pathValue,{"CUSTOMFIELD_13205":time});
       });  
  }
  

var submit=function(){
    var p = getJsonByForm("form", pathValue);
   //表单校验
   var formJson = { formId:"form", pathValue:pathValue };
   var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
   if ( !validator.validate() ) {
          alertMsg("表单验证未通过","error");
          return false;
   }
   
   var ph=p["CUSTOMFIELD_14600"];
   if(ph==""){
       alertMsg("提示:批次号不能为空!");
       return;
   }
   submit1();
   submit2();

}
//LSM
var submit1=function(){

   var p = getJsonByForm("form", pathValue);
   var field;
   
   if(PCHNumber==0){
       field={
               "customfield_11713":p["CUSTOMFIELD_14600"],//批次编号
               "customfield_10112":p["CUSTOMFIELD_10112"],//项目编号
               "customfield_11008":{"value":p["CUSTOMFIELD_11008"]},//事业部
               "customfield_13205":p["CUSTOMFIELD_13205"],//任务单下单日期
               "customfield_13332":{"value":"正常"},
               "customfield_13207":p["CUSTOMFIELD_13207"],//提取任务单下单要求个数
               "customfield_10115":{"emailAddress":p["CUSTOMFIELD_10115"]},//客户服务经理
              "customfield_13332":{"value":"合格"}//提取检测任务单是否合格
               };
   }else if(PCHNumber>=1){
       field={
               "customfield_11713":p["CUSTOMFIELD_14600"],//批次编号
               "customfield_10112":p["CUSTOMFIELD_10112"],//项目编号
               "customfield_11008":{"value":p["CUSTOMFIELD_11008"]},//事业部
               "customfield_13205":p["CUSTOMFIELD_13205"],//任务单下单日期
               "customfield_13203":{"value":"改提取方案"},//二次提取检测任务下单原因
               "customfield_13207":p["CUSTOMFIELD_13207"],//提取任务单下单要求个数
               "customfield_10115":{"emailAddress":p["CUSTOMFIELD_10115"]},//客户服务经理
               "customfield_13332":{"value":"合格"}//提取检测任务单是否合格
           }
       
   }
   

   var p = getJsonByForm("form", pathValue);
   debugger;
   var oldStatus="";
   var newkStatus="";
  
   if(PCHNumber==0){
       oldStatus="已录入待下单";
       newkStatus="样品提取检测";
   }else if(PCHNumber==1){//二次下单
       oldStatus=LSMstatus;
       newkStatus="二次提取检测";
   }else{
       oldStatus=LSMstatus;
       newkStatus="三次提取检测";
   }
   
   var params = {
       "jiraKey":p["LSM_KEY"],
       "oldStatusName":oldStatus,
       "statusName":newkStatus,
       "updateField":field
   };   

   var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
    $.fn.ajaxPost({
        ajaxType:"post",
        ajaxUrl:"system/api/post/bodyParams",
        ajaxData:inobjjson,
        succeed:function(result){
            if(result["code"]>0){
                debugger;
                if(result.apiData.flag=="true"||result.apiData.flag){
                      var url="system/jdbc/update/one/table/where";
                      var paramjson={"tableName":"BIO_TQ_TASK","LSM_KEY":p["LSM_KEY"],"JIRA_STATUS":"已推送","where":{"ID":paramsValue["ID"]}};
                      putAddOrUpdata(url,paramjson,"是","");
                   }else{
                        alertMsg("提示:推送失败(<font color=#ff0000>"+result.apiData.message+"</font>)!");
                  }
             }else{
                alertMsg(errMsg+"操作失败!");
            }
        }
    });
    
}


//项目
var submit2=function(){
   //表单校验
   var p = getJsonByForm("form", pathValue);
   var params = {
       "jiraKey":p["LSM_KEY_P"],
        "oldStatusName":"样品准备",
       "statusName":"样品提取检测",
       "updateField":{
           "customfield_14600":p["CUSTOMFIELD_14600"],//样品批次编号
           "customfield_11009":p["CUSTOMFIELD_11009"],//本期首次到样日期
           "customfield_11416":Number(p["CUSTOMFIELD_11416"]),//预计启动样品数
           "customfield_10115":{"emailAddress":p["CUSTOMFIELD_10115"]},//客户服务经理
           "customfield_11525":p["CUSTOMFIELD_11525"],//运营经理描述信息
           "customfield_14707":{"value":p["CUSTOMFIELD_14707"]},//提取检测任务单内审质量
           "customfield_14708":{"value":p["CUSTOMFIELD_14708"]}//提取检测任务单外审质量
           
       }
   };   
    

   var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
    $.fn.ajaxPost({
        ajaxType:"post",
        ajaxUrl:"system/api/post/bodyParams",
        ajaxData:inobjjson,
        succeed:function(result){
            if(result["code"]>0){
                if(result.apiData.flag=="true"||result.apiData.flag){
                       var url="system/jdbc/update/one/table/where";
                       var paramjson={"tableName":"BIO_TQ_TASK","LSM_KEY_P":p["LSM_KEY_P"],"JIRA_STATUS":"已推送","where":{"ID":paramsValue["ID"]}};
                       putAddOrUpdata(url,paramjson,"是","");
                   }else{
                        alertMsg("提示:推送失败(<font color=#ff0000>"+result.apiData.message+"</font>)!");
                  }
             }else{
                alertMsg(errMsg+"操作失败!");
            }
        }
    });
    
}

var queryCheckPCNumber=function(PCH){
   debugger;
   var params={"query":"checkPsTqCount","objects":[[PCH]]};
      $.fn.ajaxPost({
           ajaxUrl:"system/jdbc/query/one/table",
           ajaxType: "post",
           ajaxData: params,
           succeed:function(result){
               debugger;
               var mynumber=0;
               if(result["code"]>0){
                   var rows=result["rows"];
                   if(rows.length>0){
                       var row=rows[0];
                       PCHNumber=row["CHECKNUMBER"];
                   }
               }
               
           }
      });
}

var getJira=function(keyinfo){
   debugger
   if(keyinfo==""){
       status=null;
       return;
   }
   var url="http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
   var parmars={"jiraKey":keyinfo,"fields":["customfield_11525","status"]};
   var inobjjson={ "url":url, "bodyParams": parmars };
    $.fn.ajaxPost({
        ajaxType:"post",
        ajaxUrl:"system/api/post/bodyParams",
        ajaxData:inobjjson,
        succeed:function(result){
           debugger;
            if(result["code"]>0){
                status=result.apiData[0].fields.status.name;
                if(result.apiData[0].fields.customfield_11525!=null){//存在值则累加
                    var o=$("#CUSTOMFIELD_11525"+pathValue).val();//存有的值
                    if(o!=""&&o!=result.apiData[0].fields.customfield_11525){
                        $("#CUSTOMFIELD_11525"+pathValue).val(o+"\r"+result.apiData[0].fields.customfield_11525);
                    }else{
                        if(result.apiData[0].fields.customfield_11525){
                            $("#CUSTOMFIELD_11525"+pathValue).val(result.apiData[0].fields.customfield_11525);
                        }
                    }
                   
                }
                
               
             }else{
                 alertMsg("提示:加载获取jira信息失败!");
            }
        }
    });
    
}
   

var getJiraLSM=function(keyinfo){
   debugger
   if(keyinfo==""){
       LSMstatus=null;
       return;
   }
   var url="http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
   var parmars={"jiraKey":keyinfo,"fields":["status"]};
   var inobjjson={ "url":url, "bodyParams": parmars };
    $.fn.ajaxPost({
        ajaxType:"post",
        ajaxUrl:"system/api/post/bodyParams",
        ajaxData:inobjjson,
        succeed:function(result){
           debugger;
            if(result["code"]>0){
                LSMstatus=result.apiData[0].fields.status.name;
             }else{
                 alertMsg("提示:加载获取jira信息失败!");
            }
        }
    });
    
}
var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
                  if(isDoCallBack=="是"){
                     alertMsg("推送成功!");
                   funcExce(pathValue+"pageCallBack");
                  }
              }else{
                  alertMsg(errMsg+"操作失败!");
              }
          }
      });
}

funcPushs(pathValue,{
   "init":init,
   "submit":submit,
});

});