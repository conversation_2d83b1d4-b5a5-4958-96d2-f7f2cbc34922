$(document).ready(function() {
   var pathValue="biomarker-wwtask-tq-task-index";
   var initData=function(){
       return {};
   }

   var gridNameGrid;
   var gridName1Grid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
          //  {name:"edit",target:"opensel",title:"审核.."}
            
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"query_WW_BIO_TQ_TASK_view","objects":[["待审核"]]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="TASK_NO"){
                    	
                        setJsonParam(cols[i],"template",getTemplate("#= TASK_NO #","funcExce(\'"+pathValue+"opensel\',\'#= ID #\',\'#= TASK_TYPE #\');","txt"));          
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        
        init_1();
   }
   //---提交待审核---
   var init_1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	  // {name:"edit",target:"doReturn",title:"撤单"}
       ]);//工具条
       //请求参数
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           toolbar: toolbar,
           read:{"query":"query_WW_BIO_TQ_TASK_view","objects":[["待填写","审核退回","终止"]]},
           headerFilter:function(cols,i){
               if(i){
                   if(cols[i]["field"]&&cols[i]["field"]=="TASK_NO"){
                	   
                        setJsonParam(cols[i],"template",getTemplate("#= TASK_NO #","funcExce(\'"+pathValue+"openselck\',\'#= ID #\',\'#= TASK_TYPE #\');","txt"));
                               
                   }
               }
           }
       };
       gridName1Grid = initKendoGrid("#gridName1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
       
  }

    //总的编辑入加
    var opensel=function(ID){    	
    //	if(type=="委外提取检测任务单") 
             opentq(ID);
    }

  var openselck=function(ID,type){    
debugger;
    	var winOpts={
            url:"biomarker/wwtask/tq/task/addck/addck",
            title:"修改:委外提取任务下达.."
        };
        openWindow(winOpts,{"ID":ID,"TASK_TYPE":"委外提取检测任务单"});//传递
    }

    var opentq=function(ID){
        var winOpts={
            url:"biomarker/wwtask/tq/task/add/add",
            title:"修改:委外提取任务下达.."
        };
        openWindow(winOpts,{"ID":ID,"TASK_TYPE":"委外提取检测任务单"});//传递
    } 
     var sumbit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
        if(gridName1Grid){
            gridName1Grid.dataSource.read();//重新读取--刷新
        }
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_TQ_TASK","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }
     
     var inputCheck1=function(){
          var arrIds=getGridSelectData(gridNameGrid);
          if(arrIds.length!=1){
                 alertMsg("请选择一条数据进行审核操作!");
                 return ;
           } 
          var ids=[];
          lsmkeys=[];
          lsmkeyps=[];
         for(var i=0;i<arrIds.length;i++){
              ids.push(arrIds[i]["ID"]);
              lsmkeys.push(arrIds[i]["LSM_KEY"]);
              lsmkeyps.push(arrIds[i]["LSM_KEY_P"]);
         }
           var winOpts={
                 url:"biomarker/wwtask/tq/task/comfirm/comfirm",
                 title:"审核.."
             };
            openWindow(winOpts,{"IDS":ids,"ID":ids[0],"LSMKEY":lsmkeys,"LSMKEYP":lsmkeyps});
      }
     
     //撤回操作
     var doReturn=function(){
   	  
   	  var arrIds=getSelectData(gridName1Grid);
         if(arrIds.length==0){
             alertMsg("请至少选择一条数据进行操作!");
             return ;
         }
         
         var params={"query":"checkTaskMxTQReturn","objects":[arrIds]};
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:"system/jdbc/query/one/table",
   	         ajaxData: params,
                succeed:function(result){
		           if(result["code"]>0){
		                var rows=result["rows"];                	
		                var flag=0;
		                var objectupmx=[];
		       	        for(var i=0;i<rows.length;i++){
		       	        	var g=rows[i];
		   	       	   	    if(g["EX_MAN_ID"]||g["EX_MAN_ID"]==""){
		   	       	   	       flag=1;
		   	       	   	       break;
		   	       	   	    }
		   	       	   	    objectupmx.push({
       	        				"ID":g["ID"],
       	        				"EX_MAN_ID":null
       	        			});
		       	        }
		       	        if(flag==0){
		       	        		
		       	        		var objectup=[];
		       	        		for(var i=0;i<arrIds.length;i++){
		       	        			objectup.push({
		       	        				"ID":arrIds[i],
		       	        				"DD_TASK_STATUS":"待审核",
		       	        				"TASK_STATUS":"待审核",
		       	        			});
		       	        		}
		   		       	     var url="system/jdbc/save/batch/table";
		   		       	     var paramsupmain={"tableName":"BIO_TQ_TASK","objects":objectup};
		   		    	     putAddOrUpdata(url,paramsupmain,"是","更新主单状态");
		   		    	     
		   		    	     var paramsupmain={"tableName":"BIO_TQ_TASK_MX","objects":objectupmx};
		   		    	     putAddOrUpdata(url,paramsupmain,"否","更新明细");
		   		    	     
		       	        }else{
		       	        	 alertMsg("提示:操作失败,存在任务已排单接收!");
		       	        }
		                                	
		                 }else{
                 	console.log(result);
                 }
             }
         });	
       }

     var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:urls,
             ajaxData:inobjjson,
             succeed:function(result){
                 if(result["code"]>0){
                	 if(isDoCallBack=="是"){
                		 alertMsg("提示:操作成功!");
                		 refreshGrid();
                	 }
                 }else{
                	 alertMsg(errMsg+"操作失败!");
                 }
             }
         });
     }
     
     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "opensel":opensel,
         "openselck":openselck,
         "opentq":opentq,
        "inputCheck1":inputCheck1,
        "doReturn":doReturn,
         "refreshGrid":refreshGrid,
         "sumbit":sumbit,//提交方法
         "callBack":callBack//回调方法
     });
});