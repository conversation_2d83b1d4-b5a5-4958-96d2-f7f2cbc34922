$(document).ready(function () {
	var pathValue = "biomarker-wwtask-libcx-setm-add-add";
	var paramsValue;
	var gridNameGrid;
	var isAdd = false;

	// 主单数据库表
	var initData = function () {
		return {
			tableName: "BIO_WW_COST_SETTLEMENT",
		};
	};

	// 页面初始化
	var no1;
	var no2;
	var DID;
	var U8ID;
	var CODES;
	var init = function (params) {
		//params["SOURCE"] = "研发结果";
		paramsValue = params;
		getInfo("form", pathValue, params);
        getInfo("form", pathValue, params,url);
		 gridNameGrid_init();
		

	};
	
	// 物料明细
	var gridNameGrid_init = function () {
		/**
		 * 列表-按钮-定义
		 */
		debugger;
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "add", target: "addMaterial", title: "新增" },
			{ name: "delete", target: "deleteMaterial", title: "删除" },
		]); //工具条
		 var PROJECT_NO = $("#PROJECT_NO" + pathValue).val();
		//请求参数
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table", //请求json的路径-针对的查询
			sort: "", //排序
			height: fullh - 300,
			toolbar: toolbar,
			read: {
				"query": "query_BIO_WW_COST_SETTLEMENT_MX_list",
				"objects": [PROJECT_NO],
			}
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson); //初始化表格的方法
	}

	// 从页面删除数据
	var deleteMaterial = function () {
		var arrSelect = getSelectData(gridNameGrid);
		var oldlist = getGridItemsData(gridNameGrid);
		if (arrSelect.length == 0) {
			alertMsg("请至少选择一条数据进行删除操作!");
			return;
		}
		if (oldlist.length - arrSelect.length < 1) {
			alertMsg("至少要有一条物料明细,不能全部删除");
			return;
		}
		confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function () {
			for (var i = 0; i < arrSelect.length; i++) {
				var params = { tableName: "BIO_WW_COST_SETTLEMENT_MX", ids: arrSelect[i]};
				var url = "system/jdbc/delete/batch/table";
				deleteGridDataByIds(url, params, refreshGrid);
			}
		});
	};


	var refreshGrid = function () {
		if (gridNameGrid) {
			gridNameGrid.dataSource.read(); //重新读取--刷新
		}
	};

	var callBack = function () {
		refreshGrid();
	};


	// 删除主单对应列表在数据库的数据
	var isExist = function () {
		var params = {
			tableName: "BIO_WW_COST_SETTLEMENT_MX",
			where: { M_ID: $("#ID" + pathValue).val(), },
		};
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/jdbc/delete/one/table/where",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					updateListMx();
				} else {
					alertMsg("删除旧列表失败", "error");
				}
			},
		});
	};
	// 更新列表到数据库
	var updateListMx = function () {
		var g = getGridItemsData(gridNameGrid);
		var object = [];
		for (var i = 0; i < g.length; i++) {
			//更新状态

			object.push({
				ID:g[i]["ID"],
				PROJECT_NAME: g[i]["PROJECT_NAME"],
				PROJECT_NO: g[i]["#PROJECT_NO"],
				TASK_LS_NO: g[i]["TASK_LS_NO"],
				PROJECT_SUBNO: g[i]["PROJECT_SUBNO"],
				WW_SUPPLIER: g[i]["WW_SUPPLIER"],
				WW_PRODUCT_TYPE: g[i]["WW_PRODUCT_TYPE"],
				WW_NO: g[i]["WW_NO"],
				WW_DELIVERY_DATE: g[i]["WW_DELIVERY_DATE"],
				WW_NUMBER_SAMPLES: g[i]["WW_NUMBER_SAMPLES"],
				WW_SUMMARY_COST: g[i]["WW_SUMMARY_COST"],
				WW_TOTAL_COMPENSATION_AMOUNT: g[i]["WW_TOTAL_COMPENSATION_AMOUNT"],
				WW_WHETHER_NORMAL: g[i]["WW_WHETHER_NORMAL"],
				WW_ABNORMAL_CAUSE: g[i]["WW_ABNORMAL_CAUSE"],
				COST_RECORDED_DEPARTMENT: g[i]["COST_RECORDED_DEPARTMENT"],
				SUBMIT_WAREHOUSING_DATE: g[i]["SUBMIT_WAREHOUSING_DATE"],
				PAYMENT_TYPE: g[i]["PAYMENT_TYPE"],
				TAX_RATE: g[i]["TAX_RATE"],
				WW_TASK_LS_STATUS: g[i]["WW_TASK_LS_STATUS"],
			});
		}
		var params = {
			tableName: "BIO_WW_COST_SETTLEMENT_MX",
			objects: object,
		};
		var url = "system/jdbc/save/batch/table";
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: url,
			ajaxData: params,
			succeed: function (res) {
				if (res["code"] > 0) {
					alertMsg("保存成功", "success", function () {
						funcExce(pathValue + "pageCallBack"); //执行回调
						funcExce(pathValue + "close"); //关闭页面
					});
				} else {
					alertMsg("保存列表失败", "error");
				}
			},
		});
	};

	// 保存
	var submit = function () {
		debugger;
		var g = getGridItemsData(gridNameGrid);
		if (g.length == 0) {
			alertMsg("至少要有一条材料明细");
			return;
		}

		formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					if ($("#ID" + pathValue).val() == "") {
						$("#ID" + pathValue).val(result["ID"]);
						isAdd = true;
						updateListMx();
					}
					alertMsg("保存主单成功", "success");
					funcExce(pathValue+"pageCallBack");//执行回调
                    funcExce(pathValue+"close");//关闭页面
					
				} else {
					alertMsg("保存主单失败", "error");
				}
			},
		});
	};

	// 添加明细
	var addMaterial = function (p) {
		debugger;
		var PROJECT_NO = $("#PROJECT_NO"+pathValue).val();
		//var M_ID = paramsValue.M_ID;
		if (PROJECT_NO == null || PROJECT_NO == "") {
			alertMsg("请先填写主单PROJECT_NO");
			return;
		}
		    //var a;
			//var LIMS_TO_U8ID = U8ID;
			//$.fn.ajaxPost({
			//	ajaxUrl: "system/jdbc/query/one/table",
			//	ajaxType: "post",
			//	ajaxData: { "query": "query_BIO_RD_MATERIAL_DELIVERY_view", "objects": [],"search":{"LIMS_TO_U8ID":LIMS_TO_U8ID}},
			//	ajaxAsync: false,
			//	succeed: function (rs) {
			//		console.log(rs);
			//		if (rs["rows"].length > 0) {
			//			a = 1;
			//		}
			//	}
			//});
			//if(a == 1){
			//	alertMsg("重复，请重新输入!");
			//	return;
			//}

		
		openWindow({
			url: "biomarker/wwtask/libcx/setm/add/add",
			title: "添加",
			currUrl: "biomarker/wwtask/libcx/setm/add/addlist/addlist",
		}, {
			"PROJECT_NO": PROJECT_NO
		});
	}

	// 回填添加到列表上
	var addSub = function (wlDataList) {
		for (var i = 0; i < wlDataList.length; i++) {
			gridNameGrid.dataSource.add(wlDataList[i]);
		}
	}
	//推送
	var pushs = function () {
		formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					if ($("#ID" + pathValue).val() == "") {
						$("#ID" + pathValue).val(result["ID"]);
						isAdd = true;
						updateListMx();
					}
					alertMsg("保存主单成功", "success");
					
				} else {
					alertMsg("保存主单失败", "error");
				}
			},
		});

		var object = [];
		var Ids = [];
		var LIMS_TO_U8ID = $("#LIMS_TO_U8ID"+pathValue).val();
				Ids.push(LIMS_TO_U8ID);//样本ID
			var params = {"Ids": Ids}; //插入任务明细记录
			var url = "system/jdbc/rd/material";
			var jsondate;
			$.fn.ajaxPost({
				ajaxType: "post",
				ajaxUrl: url,
				ajaxData: params,
				ajaxAsync: false,
				succeed: function (result) {
					jsondate=result
					
				},
			});
			if (jsondate["code"] > 0) {
					object.push({
						"ID": DID, //样本ID
						"U8_CODE": U8ID, //样本ID
						"STATE": "已推送", //更新状态
					});
				debugger;
				var tableName = "BIO_RD_MATERIAL_DELIVERY";
				var params = { tableName: tableName, objects: object }; //插入任务明细记录
				var url = "system/jdbc/save/batch/table";
				$.fn.ajaxPost({
					ajaxType: "post",
					ajaxUrl: url,
					ajaxData: params,
					ajaxAsync: false,
					succeed: function (result) {
						if (result["code"] > 0) {
							refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
							alertMsg("提交成功!", "success"); // funcExce(pathValue+"close");//关闭页面
						} else {
							alertMsg("提交失败!", "error");
						}
					},
				});
				funcExce(pathValue+"pageCallBack");//执行回调
                funcExce(pathValue+"close");//关闭页面
			} else {
				alertMsg("提交失败", "error");
			}
	};


	funcPushs(pathValue, {
		"init": init,
		"deleteMaterial": deleteMaterial,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"addMaterial": addMaterial,
		"submit": submit,
		"pushs":pushs,
		"addSub": addSub,
	});
});