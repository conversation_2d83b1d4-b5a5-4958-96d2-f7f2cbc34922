$(document).ready(function () {
	var pathValue = "biomarker-wwtask-libcx-setm-index";

	/**
	 * 初始化数据-无参
	 */
	var initData = function () {
		return {};
	};

	var gridNameGrid;
	var gridName2Grid;
	var gridNameS1 = [];
	var gridNameS2 = [];
	/**
	 * 初始化-获取参数-并执行调用
	 * @param {*} params
	 */
	var init = function (params) {
		/**
		 * 列表-按钮-定义
		 */
		var toolbar = getButtonTemplates(pathValue, [
			//{name:"add",target:"add"},
			{name:"edit",target:"edit"},
			// {name:"delete",target:"deleteInfo"},
			{ name: "pushs", target: "pushs", title: "推送" }
		]); //工具条
		//请求参数
		debugger;
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table", //请求json的路径-针对的查询
			sort: "", //排序
			toolbar: toolbar,
			read: {
				query: "query_BIO_WW_COST_SETTLEMENT_view",
				objects: [["待推送"]],
				//objects: [],"search":{"STATE":"待推送"}
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
                                var ID = e.data.ID;
				//var PROJECT_NO= e.data.PROJECT_NO;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: "",
					height: 320,
					//read: { "query": "query_BIO_WW_COST_SETTLEMENT_MX_list", "objects": [[PROJECT_NO]] }
                                        read: { "query": "query_BIO_WW_COST_SETTLEMENT_MX_list", "objects": [[ID]] }
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ID+ "_" + pathValue, subGrid_N_JSON);
				gridNameS1.push(subGrid_N);
			}
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson); //初始化表格的方法
		init2();
	}
	var init2 = function (params) {
		/**
		 * 列表-按钮-定义
		 */
		var toolbar = getButtonTemplates(pathValue, [
			//{ name: "add", target: "add" },
			//{ name: "edit", target: "edit" },
			//{ name: "delete", target: "deleteInfo" },
			//{ name: "import", target: "importData", title: "导入" },
		]); //工具条
		//请求参数
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table", //请求json的路径-针对的查询
			sort: "", //排序
			toolbar: toolbar,
			read: {
				query: "query_BIO_WW_COST_SETTLEMENT_view",
				objects: [["已推送"]],
				//objects: [],"search":{"STATE":"已推送"}
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				//var M_ID = e.data.ID;
				var PROJECT_NO= e.data.PROJECT_NO;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_BIO_WW_COST_SETTLEMENT_MX_list", "objects": [[PROJECT_NO]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + PROJECT_NO+ "_" + pathValue, subGrid_N_JSON);
				gridNameS2.push(subGrid_N);
			}

		};
		gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson); //初始化表格的方法
	}
	var add = function () {
		var winOpts = {
			url: "biomarker/wwtask/libcx/setm/add/add",
			title: "新增:待推送..",
		};
		openWindow(winOpts);
	};

	var open = function (ID) {
		var winOpts = {
			url: "${openPath}",
			title: "修改:待推送..",
		};
		openWindow(winOpts, { ID: ID }); //传递id
	};

	var edit = function () {
		var arrIds = getSelectData(gridNameGrid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行修改!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行修改操作!");
			return;
		}
		open(arrIds[0]);
	};

	var sumbit = function () {
		formSubmit({
			formId: "form",
			pathValue: pathValue,
		});
	};

	var callBack = function () {
		refreshGrid();
	};

	var refreshGrid = function () {
		if (gridNameGrid) {
			gridNameGrid.dataSource.read(); //重新读取--刷新
			gridName2Grid.dataSource.read(); //重新读取--刷新
		}else{
			gridNameGrid.dataSource.read(); //重新读取--刷新
			gridName2Grid.dataSource.read(); //重新读取--刷新
		}
	};

	var deleteInfo = function () {
		var arrIds = getSelectData(gridNameGrid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行删除操作!");
			return;
		}
		var params = { tableName: "BIO_WW_COST_SETTLEMENT", ids: arrIds };
		var url = "system/jdbc/delete/batch/table";
		deleteGridDataByIds(url, params, refreshGrid);
	};

	//表格导入
	var importData = function (componentId) {
		var grid = gridNameGrid;
		openComponent({
			name: "导入数据", //组件名称
			componentId: componentId,
			params: {
				template: grid, //单表导入
				tableName: "BIO_WW_COST_SETTLEMENT",
			},
		});
	};
	
         var pushs = function(){
         alertMsg("推送测试!", "error");
}
	var pushs1 = function () {
		var arrIds = getGridSelectData(gridNameGrid);

		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行推送!");
			return;
		}
		var object = [];
		var Ids = [];
		//confirmMsg("确认", "确定要对选中的记录进行推送吗?", "warn", function () {
			for (var i = 0; i < arrIds.length; i++) {
				Ids.push(arrIds[i]["LIMS_TO_U8ID"]);//样本ID
			}
			var params = {"Ids": Ids}; //插入任务明细记录
			var url = "system/jdbc/rd/material";
			var jsondate;
			$.fn.ajaxPost({
				ajaxType: "post",
				ajaxUrl: url,
				ajaxData: params,
				ajaxAsync: false,
				succeed: function (result) {
					jsondate=result
					
				},
			});
			if (jsondate["code"] > 0) {
				for (var i = 0; i < arrIds.length; i++) {
					//var keys2;
                    //
					object.push({
						ID: arrIds[i]["WID"], //样本ID
						U8_CODE: arrIds[i]["ID"], //样本ID
						//U8_CODE: arrIds[i]["ID"], //样本ID
						STATE: "已推送", //更新状态
					});
				}
				debugger;
				var tableName = "BIO_RD_MATERIAL_DELIVERY";
				var params = { tableName: tableName, objects: object }; //插入任务明细记录
				var url = "system/jdbc/save/batch/table";
				$.fn.ajaxPost({
					ajaxType: "post",
					ajaxUrl: url,
					ajaxData: params,
					ajaxAsync: false,
					succeed: function (result) {
						if (result["code"] > 0) {
							refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
							alertMsg("提交成功!", "success"); // funcExce(pathValue+"close");//关闭页面
						} else {
							alertMsg("提交失败!", "error");
						}
					},
				});
				refreshGrid(); // funcExce(pathValue + "pageCallBack", "0", "0"); //父执行回调
				//alertMsg("提交成功", "success"); // funcExce(pathValue+"close");//关闭页面
			} else {
				alertMsg("提交失败", "error");
			}
		//});
	};
	funcPushs(pathValue, {
		"initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
		"init": init,//初始化方法-在加载完初始化数据之后执行
		"open": open,
		"add": add,//打开添加表单
		"edit": edit,
		"pushs": pushs, //推送
		"refreshGrid": refreshGrid,
		"deleteInfo": deleteInfo,
		"sumbit": sumbit,//提交方法
		"callBack": callBack,//回调方法
		"importData": importData,
	});
});