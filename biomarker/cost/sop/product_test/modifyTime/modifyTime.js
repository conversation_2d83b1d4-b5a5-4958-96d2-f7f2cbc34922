$(document).ready(function () {
    var pathValue = "biomarker-cost-sop-product_test-modifyTime-modifyTime";
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "表名称是什么呢"
        };
    }
    var paramsValue;
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        paramsValue = params;

    }


    var submit = function () {
        debugger;
        var BTIME = $("#BTIME" + pathValue).val();   //开始时间 
        if (BTIME == null || BTIME == "") {
            alertMsg("实验开始时间不能为空！");
            return
        }

        var objectWwdd = [];
        objectWwdd.push({
            "ID": paramsValue["ID"],
            "SYS_INSERTTIME_JL": BTIME
        })
        var urlsend = "system/jdbc/save/batch/table";


        var paramsadd = { "tableName": "BIO_BZ_MATERIEL_SOP_JL", "objects": objectWwdd };
        putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");

    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");//执行回调
                        funcExce(pathValue + "close");//关闭页面
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});