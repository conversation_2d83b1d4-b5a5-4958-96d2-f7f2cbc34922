$(document).ready(function () {
    var pathValue = "biomarker-cost-sop-product_test-editck-editck";
    var paramsValue;
    var gridNameGrid;
    var isAdd = false;

    // 主单数据库表
    var initData = function () {
        return {
            tableName: "BIO_BZ_MATERIEL_SOP_JL",
        };
    };

    // 页面初始化
    debugger;

    var SOP_REVIEW_DATA = sysNowTimeFuncParams["sysNowTime"];       //获取当前系统时间
    var SOP_REVIEW_MAN = getLimsUser()["name"];
    var init = function (params) {

        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        // var SOP_REVIEW_MAN = $("#SOP_REVIEW_MAN"+pathValue).val();
        // var SOP_REVIEW_DATA= $("#SOP_REVIEW_DATA"+pathValue).val();
        getInfo("form", pathValue, params, url, function (viewMode, params) {
            //var ID =  params.ID;
            params.SOP_REVIEW_DATA = SOP_REVIEW_DATA;
            params.SOP_REVIEW_MAN = SOP_REVIEW_MAN;
            getInfo("form", pathValue, params);
            initTablesGrid_1();
            getreTamout();
        });//传入id


        $("#SOP_REVIEW_MAN" + pathValue).val(SOP_REVIEW_MAN);
        $("#SOP_REVIEW_DATA" + pathValue).val(SOP_REVIEW_DATA);
        gridNameGrid_init();
        //$("#form" + pathValue).css("pointerEvents", "none");




    };

    // 物料明细
    var gridNameGrid_init = function () {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "add", target: "addMaterial", title: "新增" },
            { name: "delete", target: "exportData", title: "批量导入更新" },
            { name: "delete", target: "deleteMaterial", title: "删除" },
        ]); //工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table", //请求json的路径-针对的查询
            sort: "", //排序
            height: fullh - 300,
            toolbar: toolbar,
            read: {
                query: "query_BIO_MATERIEL_cost_SOP",
                objects: [paramsValue["ID"]],
            },
            headerFilter: function (cols, i) {
                if (i) {
                    var disableList = ["W_CODE", "W_NAME", "W_REANAME", "W_NUMBER", "W_UNIT", "W_SPECIF", "W_CLASS",];
                    if (cols[i]["field"] && disableList.indexOf(cols[i]["field"]) > -1) {
                        setJsonParam(cols[i], "editable", function (dataItem) {
                            return false;
                        });
                    }
                    var numList = ["M_FY_NUMBER", "M_STANDUSE", "M_LOSS", "M_TOTAL_USE"];
                    if (cols[i]["field"] && numList.indexOf(cols[i]["field"]) > -1) {
                        setJsonParam(cols[i], "editor", function (container, options) {
                            $(`<input name="${options.field}" data-bind="value: ${options.field}"></input>`)
                                .appendTo(container)
                                .kendoNumericTextBox({
                                    format: "n2",
                                    decimals: 2,
                                    min: 0,
                                });
                        });
                    }
                    // 列表编辑 下拉选择
                    if (cols[i]["field"] && cols[i]["field"] == "M_IS_PR_ST") {
                        setJsonParam(cols[i], "template", function (dataItem) {
                            var value = "";
                            if (dataItem && dataItem[cols[i]["field"]]) {
                                value = dataItem[cols[i]["field"]]["value"]
                                    ? dataItem[cols[i]["field"]]["value"]
                                    : dataItem[cols[i]["field"]];
                            }
                            return value;
                        });
                        setJsonParam(cols[i], "editor", function (container, options) {
                            $(`<input name="${cols[i]["field"]}" data-bind="value:${cols[i]["field"]}"/>`)
                                .appendTo(container)
                                .kendoDropDownList({
                                    dataSource: {
                                        data: [
                                            { text: "是", value: "是" },
                                            { text: "否", value: "否" },
                                        ],
                                    },
                                    dataValueField: "value",
                                    dataTextField: "text",
                                });
                        });
                    }
                }
            },
            editable: true,
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson); //初始化表格的方法
    };

    // 从页面删除数据
    var deleteMaterial = function () {
        var arrSelect = getSelectData(gridNameGrid);
        var oldlist = getGridItemsData(gridNameGrid);
        if ($("#COLLECTION_FLAG" + pathValue).val() == "Y") {
            alertMsg("归集Flag等于Y,不可删除");
            return;
        }
        if (arrSelect.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        if (oldlist.length - arrSelect.length < 1) {
            alertMsg("至少要有一条物料明细,不能全部删除");
            return;
        }
        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function () {
            var params = { tableName: "BIO_BZ_MATERIEL_SOP_JL_MX", ids: arrSelect };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        });
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read(); //重新读取--刷新
        }
    };

    var callBack = function () {
        refreshGrid();
    };

    // 保存
    var submit = function () {
        var g = getGridItemsData(gridNameGrid);
        if (g.length == 0) {
            alertMsg("至少要有一条物料明细");
            return;
        }
        g.map((item) => {
            item.M_IS_PR_ST =
                typeof item.M_IS_PR_ST === "object" && item.M_IS_PR_ST != null
                    ? item.M_IS_PR_ST.value
                    : item.M_IS_PR_ST;
        });
        var params = { tableName: "BIO_BZ_MATERIEL_SOP_JL_MX", objects: g };
        var url = "system/jdbc/save/batch/table";
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    //提交成功
                    alertMsg("保存成功", "success", function () {
                        funcExce(pathValue + "pageCallBack"); //执行回调
                        funcExce(pathValue + "close"); //关闭页面
                    });
                } else {
                    alertMsg("保存失败", "error");
                }
            },
        });
    };

    // 添加物料
    var addMaterial = function () {
        openWindow(
            {
                url: "biomarker/cost/sop/product_test/edit/addMaterial/addMaterial",
                title: "添加物料",
                currUrl: "biomarker/cost/sop/product_test/edit/edit",
            },
            {
                ID: paramsValue["ID"],
                EXE_TQQC_ID: $("#EXE_TQQC_ID" + pathValue).val(),
            }
        );
    };
    // 提交
    var release = function () {
        //var arrIds = $("#EXAMINE_RS"+pathValue).val();
        //var object = [];
        confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {
            var SHJG = $("#SHJG"+pathValue).val();
            if(SHJG=="通过"){
                $("#SOP_REVIEW_FLAG" + pathValue).val("已审核");
            }           
          if(SHJG=="终止"){
                $("#SOP_REVIEW_FLAG" + pathValue).val("终止");
                $("#COLLECTION_FLAG" + pathValue).val("终止");
            }
            formSubmit({
                url: "system/jdbc/save/one/table",
                formId: "form",
                pathValue: pathValue,
                succeed: function (result) {
                    if (result["code"] > 0) {
                        //提交成功
                        alertMsg("提交成功", "success", function () {
                            funcExce(pathValue + "pageCallBack"); //执行回调
                            funcExce(pathValue + "close"); //关闭页面
                        });
                    } else {
                        alertMsg("提交失败", "error");
                    }
                }
            });
        });
    };

    /**
         * 获取列表的数据导入
         * @param {*} componentId
         */
    var exportData = function (componentId) {
        var wCode = getGridItemsData(gridNameGrid).filter((item) => {
            return item["ID"] === "";
        });
        if (wCode.length > 0) {
            alertMsg("请先保存");
            return;
        }
        var fh = "";
        debugger;
        openComponent({
            name: "导入数据", //组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    if (n == "import") {
                        return {
                            template: gridNameGrid.getOptions().columns,
                            expKey: "A",
                            tableName: "BIO_BZ_MATERIEL_SOP_JL_MX",
                        };
                    } else {
                        fh = saveGridDataToExcel({ grid: gridNameGrid, select: 9, expKey: "A" });
                    }
                },
                succeed: function () {
                    refreshGrid();
                    return;
                },
            },
        });
    };
    funcPushs(pathValue, {
        "init": init,
        "deleteMaterial": deleteMaterial,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "exportData": exportData,
        "release": release,
        "addMaterial": addMaterial,
        "submit": submit,
    });
});