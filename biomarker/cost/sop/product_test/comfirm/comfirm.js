$(document).ready(function () {

	var pathValue = "biomarker-cost-sop-product_test-comfirm-comfirm";
	var paramsValue;
	var initData = function () {
		return {
			tableName: "BIO_TASK_LIB"
		};
	}
	var init = function (params) {
		paramsValue = params;
	}

	// 提交
	var release = function () {
		confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {
			var SHJG = $("#SHJG" + pathValue).val();
			var SOP_REVIEW_FLAG = "";
			var COLLECTION_FLAG = "";
			if (SHJG == "通过") {
				SOP_REVIEW_FLAG = "已审核";
				COLLECTION_FLAG = "待归集";
			}
			if (SHJG == "终止") {
				SOP_REVIEW_FLAG = "终止";
				COLLECTION_FLAG = "终止";
			}

			var paramsF = getJsonByForm("form", pathValue);

			var ids = paramsValue["IDS"];
			var object = [];

			for (var i = 0; i < ids.length; i++) {
				object.push({
					"ID": ids[i],
					"SOP_REVIEW_FLAG": SOP_REVIEW_FLAG,
					"COLLECTION_FLAG": COLLECTION_FLAG,
					"SOP_REVIEW_DATA": paramsF["SOP_REVIEW_DATA"],
					"SOP_REVIEW_MAN": paramsF["SOP_REVIEW_MAN"],
					"SOP_REVIEW_REMARKS": paramsF["SOP_REVIEW_REMARKS"],
				});
			}
			//执行更新
			var params = { "tableName": "BIO_BZ_MATERIEL_SOP_JL", "objects": object };
			//插入任务明细记录
			var url = "system/jdbc/save/batch/table";
			$.fn.ajaxPost({
				ajaxType: "post",
				ajaxUrl: url,
				ajaxData: params,
				succeed: function (result) {

					if (result["code"] > 0) {//成功保存后执行流程提交

						funcExce(pathValue + "pageCallBack");//父执行回调
						alertMsg("提交成功!");
						funcExce(pathValue + "close");//关闭页面

					} else {
						console.log(result);
					}
				}
			});

		});
	};


	funcPushs(pathValue, {
		"init": init,
		"release": release,
	});

});