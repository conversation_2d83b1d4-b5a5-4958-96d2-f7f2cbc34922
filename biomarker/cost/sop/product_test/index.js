$(document).ready(function () {
    var pathValue = "biomarker-cost-sop-product_test-index";

    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {};
    }

    var gridNameGrid;
    var gridName1Grid;
    var gridName2Grid;

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit2", title: "批量审核" },
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "queryProdCos", "objects": [["待审核"], ["待归集"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "EX_DH_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= EX_DH_NO#", "funcExce(\'" + pathValue + "openck\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);//初始化表格的方法

        init1();
    }
    var init1 = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            //{name: "edit",target: "edit"},
            { name: "edit", target: "release", title: "归集" },
            { name: "edit", target: "withdraw", title: "撤回" },
            { name: "edit", target: "release2", title: "研发转生产" },
            { name: "edit", target: "modifyTime", title: "修改时间" },
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "queryProdCos", "objects": [["已审核"], ["待归集"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "EX_DH_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= EX_DH_NO#", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridName1Grid = initKendoGrid("#gridName1Grid" + pathValue, gridNameGridJson);//初始化表格的方法
        init2();
    }
    var init2 = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [

            { name: "edit", target: "chehui", title: "撤回" },

        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "queryProdCos", "objects": [["终止", "已审核", "待审核"], ["终止", "已归集", "&#x5df2;&#x5f52;&#x96c6;"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "EX_DH_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= EX_DH_NO#", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    var open = function (ID) {
        var winOpts = {
            url: "biomarker/cost/sop/product_test/edit/edit",
            title: "查看:成本发生明细..",
        };
        openWindow(winOpts, {
            ID: ID,
        }); //传递id
    };

    var openck = function (ID) {
        var winOpts = {
            url: "biomarker/cost/sop/product_test/editck/editck",
            title: "审核:成本发生明细..",
        };
        openWindow(winOpts, {
            ID: ID,
        }); //传递id
    };

    var edit = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        open(arrIds[0]);
    };
    var edit2 = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } 
        var winOpts = {
            url: "biomarker/cost/sop/product_test/comfirm/comfirm",
            title: "审核:成本发生明细..",
        };
        openWindow(winOpts, {
            IDS: arrIds,
        }); //传递id
    };

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read(); //重新读取--刷新
        }
        if (gridName1Grid) {
            gridName1Grid.dataSource.read(); //重新读取--刷新
        }
        if (gridName2Grid) {
            gridName2Grid.dataSource.read(); //重新读取--刷新
        }
    };

    /*var sumbit = function () {
        formSubmit({
            formId: "form",
            pathValue: pathValue,
        });
    };

    var deleteInfo = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        var params = {
            tableName: "BIO_PROD_COS",
            ids: arrIds,
        };
        var url = "system/jdbc/delete/batch/table";
        deleteGridDataByIds(url, params, refreshGrid);
    };

    //表格导入
    var importData = function (componentId) {
        var grid = gridNameGrid;
        openComponent({
            name: "导入数据", //组件名称
            componentId: componentId,
            params: {
                template: grid, //单表导入
                tableName: "BIO_PROD_COS",
            },
        });
    };*/
    // 提交
    var release = function () {
        debugger;
        var arrIds = getGridSelectData(gridName1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行提交!");
            return;
        }

        var object = [];
        confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {
            for (var i = 0; i < arrIds.length; i++) {
                var step = "";
                if (arrIds[i]["S_STEP"] == "提取") {
                    step = "提取结果审核";
                }
                if (arrIds[i]["S_STEP"] == "测序") {
                    step = "常规建库审核";
                }
                if (arrIds[i]["S_STEP"] == "建库") {
                    step = "常规建库审核";
                }
                if (arrIds[i]["SOURCE"] == "混样建库") {
                    step = "混样建库审核";
                }
                if (arrIds[i]["SOURCE"] == "ONT上机信息") {
                    step = "ONT上机信息";
                }
                if (arrIds[i]["SOURCE"] == "PB上机信息") {
                    step = "混样建库审核";
                }
                if (arrIds[i]["SOURCE"] == "HIC建库") {
                    step = "HIC建库审核";
                }
                if (arrIds[i]["SOURCE"] == "核酸质检") {
                    step = "核酸质检审核";
                }
                if (arrIds[i]["SOURCE"] == "ATAC建库") {
                    step = "HIC建库审核";
                }
                if (arrIds[i]["SOURCE"] == "DNA常规检测") {
                    step = "检测结果审核";
                }
                if (arrIds[i]["SOURCE"] == "医学检测") {
                    step = "检测结果审核";
                }
                if (arrIds[i]["SOURCE"] == "DNA检测-MCD检测") {
                    step = "MCD检测结果审核";
                }
                if (arrIds[i]["SOURCE"] == "DNA提取" || arrIds[i]["SOURCE"] == "RNA提取" || arrIds[i]["SOURCE"] == "空间" || arrIds[i]["SOURCE"] == "解离") {
                    step = "DNARNA提取";
                }
                if (  arrIds[i]["SOURCE"] == "空间" ) {
                    step = "空间成本";
                }
                if (arrIds[i]["SOURCE"] == "切胶结果填写-ONT") {
                    step = "ONT切胶";
                }
                if (arrIds[i]["SOURCE"] == "回收") {
                    step = "回收成本";
                }
                if (arrIds[i]["SOURCE"] == "单细胞建库") {
                    step = "单细胞建库成本";
                }
                if (arrIds[i]["SOURCE"] == "PB混样-微生物全长") {
                    step = "PB混样微生物";
                    if (arrIds[i]["SOP_TIQU_JC"] == "PCR") {
                        step = "PB混样微生物PCR";
                    }
                }
                if (arrIds[i]["SOURCE"] == "PB混样-全长转录组") {
                    step = "PB全长转录组";
                }
                if (arrIds[i]["SOURCE"] == "PB混样-基因组") {
                    step = "PB混样基因组";
                }
                if (arrIds[i]["SOURCE"] == "切胶纯化结果-MCD") {
                    step = "MCD切胶";
                }
                if (arrIds[i]["SOURCE"] == "DNA混样建库-SLAF建库") {
                    step = "DNA混样SLAF建库";
                }
                if (arrIds[i]["SOURCE"] == "PB上机结合") {
                    step = "PB上机结合";
                }
                if (arrIds[i]["SOURCE"] == "PB上机信息") {
                    step = "PB上机信息";
                }
                if (arrIds[i]["SOURCE"] == "二代上机信息") {
                    step = "二代上机信息";
                  if (arrIds[i]["SOP_TIQU_JC"] == "QPCR") {
                        step = "二代QPCR上机信息";
                    }
                }
                if (arrIds[i]["SOURCE"] == "文库浓度检测") {
                    step = "文库浓度检测";
                }
                if (arrIds[i]["SOURCE"] == "文库片段检测") {
                    step = "文库片段检测";
                }
                if (arrIds[i]["SOURCE"] == "文库QPCR检测") {
                    step = "文库QPCR检测";
                }

                var inobjjson = {
                    "EX_DH_NO": arrIds[i]["EX_DH_NO"],
                    "EXE_TQQC_ID": arrIds[i]["EXE_TQQC_ID"],
                    "DEFINE32": step,        //阶段
                    "username": getLimsUser()["name"]
                }
                var RValue;
                $.fn.ajaxPost({
                    ajaxUrl: "berry/automation/epo/wwdd",
                    ajaxType: "post",
                    ajaxAsync: false,
                    ajaxData: inobjjson,
                    succeed: function (rs) {
                        RValue = rs;

                    }
                });
            }
            if (RValue["code"] == 1) {
                alertMsg("归集成功!");
                refreshGrid();

            } else {
                alertMsg(RValue["message"] + RValue["msg"]);
                refreshGrid();
            }




        });
    };
    // 提交
    var withdraw = function () {
        debugger;
        var arrIds = getGridSelectData(gridName1Grid);
        if (arrIds.length != 1) {
            alertMsg("请只一条数据进行提交!");
            return;
        }
        var objectup = [{
            "ID": arrIds[0]["ID"],
            "SOP_REVIEW_FLAG": "待审核",
        }]
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_BZ_MATERIEL_SOP_JL", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "同步更新任务明细");
    }



    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var release2 = function () {


        var username = getLimsUser()["name"];
        if (username != "蓝勇胜" && username != "徐彦岭" && username != "管理员") {

            alertMsg("无此权限，如有需要请联系开发人员!");
            return;
        }

        debugger;
        var arrIds = getGridSelectData(gridName1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行提交!");
            return;
        }

        var object = [];
        confirmMsg("确认", "确定要对选中的记录进行提交吗?", "warn", function () {
            for (var i = 0; i < arrIds.length; i++) {
                var step = "";
                if (arrIds[i]["S_STEP"] == "提取") {
                    step = "提取结果审核";
                }
                if (arrIds[i]["S_STEP"] == "测序") {
                    step = "常规建库审核";
                }
                if (arrIds[i]["S_STEP"] == "建库") {
                    step = "常规建库审核";
                }
                if (arrIds[i]["SOURCE"] == "混样建库") {
                    step = "混样建库审核";
                }
                if (arrIds[i]["SOURCE"] == "ONT上机信息") {
                    step = "ONT上机信息";
                }
                if (arrIds[i]["SOURCE"] == "PB上机信息") {
                    step = "混样建库审核";
                }
                if (arrIds[i]["SOURCE"] == "HIC建库") {
                    step = "HIC建库审核";
                }
                if (arrIds[i]["SOURCE"] == "核酸质检") {
                    step = "核酸质检审核";
                }
                if (arrIds[i]["SOURCE"] == "ATAC建库") {
                    step = "HIC建库审核";
                }
                if (arrIds[i]["SOURCE"] == "DNA常规检测") {
                    step = "检测结果审核";
                }
                if (arrIds[i]["SOURCE"] == "DNA检测-MCD检测") {
                    step = "MCD检测结果审核";
                }
                if (arrIds[i]["SOURCE"] == "DNA提取" || arrIds[i]["SOURCE"] == "RNA提取" || arrIds[i]["SOURCE"] == "空间" || arrIds[i]["SOURCE"] == "解离") {
                    step = "DNARNA提取";
                }
                if (arrIds[i]["SOURCE"] == "切胶结果填写-ONT") {
                    step = "ONT切胶";
                }
                if (arrIds[i]["SOURCE"] == "回收") {
                    step = "回收成本";
                }
                if (arrIds[i]["SOURCE"] == "单细胞建库") {
                    step = "单细胞建库成本";
                }
                if (arrIds[i]["SOURCE"] == "PB混样-微生物全长") {
                    step = "PB混样微生物";
                    if (arrIds[i]["SOP_TIQU_JC"] == "PCR") {
                        step = "PB混样微生物PCR";
                    }
                }
                if (arrIds[i]["SOURCE"] == "PB混样-全长转录组") {
                    step = "PB全长转录组";
                }
                if (arrIds[i]["SOURCE"] == "PB混样-基因组") {
                    step = "PB混样基因组";
                }
                if (arrIds[i]["SOURCE"] == "切胶纯化结果-MCD") {
                    step = "MCD切胶";
                }
                if (arrIds[i]["SOURCE"] == "DNA混样建库-SLAF建库") {
                    step = "DNA混样SLAF建库";
                }
                if (arrIds[i]["SOURCE"] == "PB上机结合") {
                    step = "PB上机结合";
                }
                if (arrIds[i]["SOURCE"] == "PB上机信息") {
                    step = "PB上机信息";
                }
                if (arrIds[i]["SOURCE"] == "二代上机信息") {
                    step = "二代上机信息";
                  if (arrIds[i]["SOP_TIQU_JC"] == "QPCR") {
                        step = "二代QPCR上机信息";
                    }
                }
                if (arrIds[i]["SOURCE"] == "文库浓度检测") {
                    step = "文库浓度检测";
                }
                if (arrIds[i]["SOURCE"] == "文库片段检测") {
                    step = "文库片段检测";
                }
                if (arrIds[i]["SOURCE"] == "文库QPCR检测") {
                    step = "文库QPCR检测";
                }
                if (arrIds[i]["SOURCE"] == "代谢蛋白检测") {
                    step = "代谢蛋白检测";
                }
                if (arrIds[i]["SOURCE"] == "蛋白前处理") {
                    step = "蛋白前处理";
                }

                var inobjjson = {
                    "EX_DH_NO": arrIds[i]["EX_DH_NO"],
                    "EXE_TQQC_ID": arrIds[i]["EXE_TQQC_ID"],
                    "DEFINE32": step,        //阶段
                    "username": getLimsUser()["name"]
                }
                var RValue;
                $.fn.ajaxPost({
                    ajaxUrl: "berry/automation/rdtop/rdtop",   //研发
                    //   ajaxUrl: "berry/automation/epo/wwdd",
                    ajaxType: "post",
                    ajaxAsync: false,
                    ajaxData: inobjjson,
                    succeed: function (rs) {
                        RValue = rs;
                    }
                });

            }
            refreshGrid();

        });
    };


    var chehui = function () {


        var username = getLimsUser()["name"];
        if (username != "蓝勇胜" && username != "徐彦岭" && username != "管理员") {
            alertMsg("无此权限，如有需要请联系开发人员!");
            return;
        }

        debugger;
        var arrIds = getGridSelectData(gridName2Grid);
        if (arrIds.length != 1) {
            alertMsg("请至少选择只数据进行提交!");
            return;
        }

        var objectup = [];
        for (var i = 0; i < arrIds.length; i++) {
            var EXE_TQQC_ID = arrIds[i]["ID"];
            objectup.push({
                "ID": arrIds[i]["ID"],//联联任务ID
                "COLLECTION_FLAG": "待归集"
            });
        }










        if (EXE_TQQC_ID != null && EXE_TQQC_ID != "" && EXE_TQQC_ID != undefined) {

            var url = "system/jdbc/delete/one/table/where";
            var params1 = { "tableName": "BIO_BJ_PRODUCTION_ORDER", "where": { "EXE_TQQC_ID": EXE_TQQC_ID } };
            deleteGridDataByIds(url, params1, "移除");

            var params1 = { "tableName": "BIO_QD_PRODUCTION_ORDER", "where": { "EXE_TQQC_ID": EXE_TQQC_ID } };
            deleteGridDataByIds(url, params1, "移除");

            var params1 = { "tableName": "BIO_OUT_SOURCING", "where": { "EXE_TQQC_ID": EXE_TQQC_ID } };
            deleteGridDataByIds(url, params1, "移除");

            var params1 = { "tableName": "BIO_WW_ORDER", "where": { "EXE_TQQC_ID": EXE_TQQC_ID } };
            deleteGridDataByIds(url, params1, "移除");



            var urlsend = "system/jdbc/save/batch/table";
            var paramsup = { "tableName": "BIO_BZ_MATERIEL_SOP_JL", "objects": objectup };
            putAddOrUpdata(urlsend, paramsup, "是", "提交");



            var sample;
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: { "query": "query_BIO_GOO_RDRE_view", "objects": [["待推送", "推送失败", "已推送"]], "search": { "EXE_TQQC_ID": [EXE_TQQC_ID] } },
                succeed: function (rs) {
                    sample = rs.rows;
                }
            });
            if (sample.length > 0) {
                var RDRECORD09_ID = [];
                for (var i = 0; i < sample.length; i++) {
                    RDRECORD09_ID.push(sample[i]["ID"]);
                }
                if (RDRECORD09_ID.length > 0) {
                    var params1 = { "tableName": "BIO_GOO_RDRECORD09", "where": { "ID": RDRECORD09_ID } };
                    deleteGridDataByIds(url, params1, "移除");
                    var params1 = { "tableName": "BIO_GOO_RDRECORDS09MX", "where": { "RDRECORD09_ID": RDRECORD09_ID } };
                    deleteGridDataByIds(url, params1, "移除");
                }
            }
        }

    }


    var modifyTime = function () {
        debugger;
        var username = getLimsUser()["name"];
        if (username != "蓝勇胜" && username != "徐彦岭" && username != "管理员") {

            alertMsg("无此权限，如有需要请联系开发人员!");
            return;
        }
        var arrIds = getGridSelectData(gridName1Grid);
        if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/cost/sop/product_test/modifyTime/modifyTime",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "修改时间.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递id
    }

    funcPushs(pathValue, {
        "initData": initData,
        "withdraw": withdraw,
        "chehui": chehui,
        "init": init,
        "open": open,
        "release2": release2,
        "openck": openck,
        "modifyTime": modifyTime,
        "edit": edit,
        "edit2": edit2,
        "release": release,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
    });
});