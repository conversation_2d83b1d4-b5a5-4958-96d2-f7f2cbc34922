$(document).ready(function() {
   var pathValue="biomarker-cost-sop-product-index";
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"excel",target:"expData",title:"报表导出"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"query_BIO_MATERIEL_cost_SOP_bb","objects":[]},
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }


      var expData = function () {
        debugger;
        var winOpts = {
            url: "biomarker/cost/sop/product/expData/expData",
            width: 700,
            height: 423,
            position: { top: 200, left: 100 },
            title: "报表导出.."
        };
        openWindow(winOpts);//传递id
    }



     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "expData":expData,
     });
});