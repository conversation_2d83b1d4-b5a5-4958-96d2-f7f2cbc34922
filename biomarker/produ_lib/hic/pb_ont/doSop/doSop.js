$(document).ready(function () {
    var pathValue = "biomarker-produ_lib-hic-pb_ont-doSop-doSop";
    var paramsValue;
    var gridNameGrid;
    var paddingKey = {};

    // 主单数据库表
    var initData = function () {
        return {
            tableName: "BIO_BZ_MATERIEL_SOP_JL",
        };
    };

    // 页面初始化
    var init = function (params) {
        //-------主单数据
        params["ID"] = "";
        params["SOURCE"] = "HIC建库"; //来源
        params["S_STEP_SUB"] = params["SOURCE"];
        paramsValue = params;

        //-------执行单子列表数据
        paddingKey["childListQuery"] = "queryTaskLibExMxResultHicAtac"; // 执行单子列表key

        getInfo("form", pathValue, params);
        paddingData(); //根据执行单ID回填主单值
        gridNameGrid_init(); //物料明细列表

        resetSelectComponentAttr(
            {
                fieldID: "SOP_NAME",
                openBeforeCheck: function () {
                    // 选择框打开前验证: 不定义此函数(或)返回true继续打开, 否则不打开选择框
                    return true;
                },
                searchparamsSettings: function (o) {
                    // 设置查询条件参数
                    // 以下代码固定格式
                    o.params = o.params ? o.params : {};
                    o.params.search = { S_STEP_SUB: paramsValue["S_STEP_SUB"] };
                },
                addSettings: function (obj, value) {
                    var rayID = "";
                    if (obj instanceof Array) {
                        rayID = obj[0]["U8_ID"];
                    } else {
                        rayID = obj.U8_ID;
                    }
                    // 返回值后,追加自定义操作
                    selectList(rayID);
                },
            }, `SOP_NAME${pathValue}`, "form"
        );
    };

    /**
     * 回填主单数据
     */
    var paddingData = function () {
        var params = {
            query: "query_BIO_BZ_MATERIEL_SOP_JL_form",
            objects: [paramsValue.EXE_TQQC_ID],
        }; //查询列表
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0 && result["rows"].length > 0) {
                    paramsValue["ID"] = result["rows"][0]["ID"];
                    getInfo("form", pathValue, result["rows"][0]);
                }
            },
        });
    };

    /**
     * 物料明细列表
     */
    var gridNameGrid_init = function () {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "add", target: "addMaterial", title: "新增" },
            { name: "delete", target: "deleteMaterial", title: "删除" },
            { name: "edit", target: "calculateTotal", title: "计算总用量" },
            { name: "export", target: "exportData", title: "批量导入更新" },
        ]); //工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table", //请求json的路径-针对的查询
            sort: "", //排序
            height: fullh - 300,
            toolbar: toolbar,
            read: {
                query: "query_BIO_MATERIEL_cost_SOP",
                objects: [paramsValue["ID"]],
            },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "W_CODE") {
                        setJsonParam(cols[i], "template",
                            getTemplate(
                                "#= W_CODE#",
                                "funcExce('" + pathValue + "opensel','#= ID #','#= W_CODE#');",
                                "txt"
                            )
                        );
                    }
                    var disableList = ["NUM", "W_MATERIAL_BATCH", "W_CODE", "W_NAME", "W_REANAME", "W_NUMBER", "W_UNIT", "W_SPECIF", "W_CLASS",];
                    if (cols[i]["field"] && disableList.indexOf(cols[i]["field"]) > -1) {
                        setJsonParam(cols[i], "editable", function (dataItem) {
                            return false;
                        });
                    }
                    var numList = ["M_STANDUSE", "M_LOSS", "M_TOTAL_USE", "PRACTICAL_TOTAL", "THEORETICAL_TOTAL",];
                    if (cols[i]["field"] && numList.indexOf(cols[i]["field"]) > -1) {
                        setJsonParam(cols[i], "editor", function (container, options) {
                            $(`<input name="${options.field}" data-bind="value: ${options.field}"></input>`)
                                .appendTo(container)
                                .kendoNumericTextBox({
                                    format: "n2",
                                    decimals: 2,
                                    min: 0,
                                });
                        });
                    }
                    if (cols[i]["field"] && cols[i]["field"] == "M_FY_NUMBER") {
                        setJsonParam(cols[i], "editor", function (container, options) {
                            $(`<input name="${options.field}" data-bind="value: ${options.field}"></input>`)
                                .appendTo(container)
                                .kendoNumericTextBox({
                                    format: "n",
                                    decimals: 0,
                                    min: 0,
                                });
                        });
                    }
                    // 列表编辑 下拉选择
                    if (cols[i]["field"] && cols[i]["field"] == "M_IS_PR_ST") {
                        setJsonParam(cols[i], "template", function (dataItem) {
                            var value = "";
                            var dataField = dataItem[cols[i]["field"]];
                            if (dataItem && dataField) {
                                value = dataField["value"] ? dataField["value"] : dataField;
                            }
                            return value;
                        });
                        setJsonParam(cols[i], "editor", function (container, options) {
                            $(`<input name="${cols[i]["field"]}" data-bind="value:${cols[i]["field"]}"/>`)
                                .appendTo(container)
                                .kendoDropDownList({
                                    dataSource: {
                                        data: [
                                            { text: "是", value: "是" },
                                            { text: "否", value: "否" },
                                        ],
                                    },
                                    dataValueField: "value",
                                    dataTextField: "text",
                                });
                        });
                    }
                }
            },
            editable: true,
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson); //初始化表格的方法
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read({
                query: "query_BIO_MATERIEL_cost_SOP",
                objects: [paramsValue["ID"]],
            }); //重新读取--刷新
        }
    };

    var callBack = function () {
        refreshGrid();
    };

    /**
     * 更新sop对应明细列表数据
     * @param {*} ID 主单ID
     */
    var selectList = function (ID) {
        paramsValue["U8_ID"] = ID;
        // 获取反应数
        var params1 = {
            query: paddingKey["childListQuery"],
            search: { EXE_TQQC_ID: [paramsValue["EXE_TQQC_ID"]] },
            size: 2000,
        }; //查询列表
        let sums = 0;
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxData: params1,
            ajaxAsync: false,
            succeed: function (res) {
                if (res["code"] > 0 && res["rows"].length > 0) {
                    if (res["rows"][0]["EXT_SAMPLENUM"] === undefined) {
                        sums = res["rows"].length;
                    } else {
                        for (let index = 0; index < res["rows"].length; index++) {
                            sums += res["rows"][index]["EXT_SAMPLENUM"];
                        }
                    }
                }
            },
        });

        // 获取列表数据
        var params = {
            query: "query_BIO_BZ_MATERIEL_SOP_list-mx",
            objects: [[ID]],
        }; //查询列表
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    // 清空列表
                    var gridData = getGridItemsData(gridNameGrid);
                    for (var j = 0; j < gridData.length; j++) {
                        gridNameGrid.dataSource.remove(gridData[j]);
                    }
                    // 列表重新赋值
                    let items = result["rows"];
                    items.map((item) => {
                        item.M_FY_NUMBER = sums; //反应数
                        item.ID = "";
                        return item;
                    });
                    for (var i = 0; i < items.length; i++) {
                        gridNameGrid.dataSource.add(items[i]);
                    }
                }
            },
        });
    };

    /**
     * 计算总用量
     */
    var calculateTotal = function () {
        var itemsData = getGridSelectData(gridNameGrid);
        if (itemsData.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定计算总用量吗？计算后理论总用量将覆盖实际总用量！", "info", function () {
            for (let index = 0; index < itemsData.length; index++) {
                var item = itemsData[index];
                if (
                    (!item["M_STANDUSE"] && item["M_STANDUSE"] != 0) ||
                    (!item["M_LOSS"] && item["M_LOSS"] != 0) ||
                    (!item["M_FY_NUMBER"] && item["M_FY_NUMBER"] != 0)
                ) {
                    alertMsg("反应数，标准用量，损耗不能为空");
                    return;
                }
                //Math.floor(nums * 100) / 100;
                let nums = ((item["M_STANDUSE"] * 100 + item["M_LOSS"] * 100) * item["M_FY_NUMBER"]) / 100;
                item["THEORETICAL_TOTAL"] = item["PRACTICAL_TOTAL"] = nums;
            }
            gridNameGrid.refresh();
            alertMsg("计算总用量完成，请保存");
        }
        );
    };

    /**
     * 从页面删除数据
     */
    var deleteMaterial = function () {
        var arrSelect = getGridSelectData(gridNameGrid);
        var oldlist = getGridItemsData(gridNameGrid);
        if (arrSelect.length == 0) {
            alertMsg("请至少选择一条数据进行删除操作!");
            return;
        }
        if (oldlist.length - arrSelect.length < 1) {
            alertMsg("至少要有一条物料明细,不能全部删除");
            return;
        }
        confirmMsg("确认", "确定要删除选中的数据吗?", "warn", function () {
            for (var i = 0; i < arrSelect.length; i++) {
                gridNameGrid.dataSource.remove(arrSelect[i]);
            }
        });
    };

    /**
     * 获取列表的数据导入
     * @param {*} componentId
     */
    var exportData = function (componentId) {
        var wCode = getGridItemsData(gridNameGrid).filter((item) => {
            return item["ID"] === "";
        });
        if (wCode.length > 0) {
            alertMsg("请先保存");
            return;
        }
        openComponent({
            name: "导入数据", //组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    if (n == "import") {
                        return {
                            template: gridNameGrid.getOptions().columns,
                            expKey: "A",
                            tableName: "BIO_BZ_MATERIEL_SOP_JL_MX",
                        };
                    } else {
                        saveGridDataToExcel({ grid: gridNameGrid, select: 9, expKey: "A" });
                    }
                },
                succeed: function () {
                    refreshGrid();
                    return false;
                },
            },
        });
    };

    /**
     * 删除主单对应列表在数据库的数据
     */
    var isExist = function () {
        var params = {
            tableName: "BIO_BZ_MATERIEL_SOP_JL_MX",
            where: { BIO_BZ_MATERIEL_SOP_ID: paramsValue["ID"] },
        };

        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/delete/one/table/where",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    updateListMx();
                } else {
                    alertMsg("删除旧列表失败", "error");
                }
            },
        });
    };

    /**
     * 主单在数据库是否对应有数据 有则删除
     */
    var deleteMaterials = function () {
        var params = {
            query: "query_BIO_MATERIEL_cost_SOP",
            objects: [paramsValue["ID"]],
        }; //查询列表
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0 && result["rows"].length > 0) {
                    isExist();
                } else {
                    updateListMx();
                }
            },
        });
    };

    // 更新列表到数据库
    var updateListMx = function () {
        var g = getGridItemsData(gridNameGrid);
        var object = [];
        for (var i = 0; i < g.length; i++) {
            //更新状态
            var M_IS_PR_ST =
                typeof g[i]["M_IS_PR_ST"] === "object" && g[i]["M_IS_PR_ST"] != null
                    ? g[i]["M_IS_PR_ST"]["value"]
                    : g[i]["M_IS_PR_ST"];
            object.push({
                M_FY_NUMBER: g[i]["M_FY_NUMBER"],
                BIO_BZ_MATERIEL_SOP_ID: paramsValue["ID"], //主单ID
                M_IS_PR_ST: M_IS_PR_ST,
                M_LOSS: g[i]["M_LOSS"],
                M_STANDUSE: g[i]["M_STANDUSE"],
                M_TOTAL_USE: g[i]["M_TOTAL_USE"],
                W_CLASS: g[i]["W_CLASS"],
                W_CODE: g[i]["W_CODE"],
                W_NAME: g[i]["W_NAME"],
                W_NUMBER: g[i]["W_NUMBER"],
                W_REANAME: g[i]["W_REANAME"],
                W_SPECIF: g[i]["W_SPECIF"],
                W_UNIT: g[i]["W_UNIT"],
                U8_ID: paramsValue["U8_ID"],
                U8_MX_ID: g[i]["U8_MX_ID"],
                W_UNIT_CODE: g[i]["W_UNIT_CODE"],
                COLLECTION_FLAG: "N",
                EXE_TQQC_ID: paramsValue["EXE_TQQC_ID"],
                THEORETICAL_TOTAL: g[i]["THEORETICAL_TOTAL"],
                PRACTICAL_TOTAL: g[i]["PRACTICAL_TOTAL"],
                W_MATERIAL_DATE: g[i]["W_MATERIAL_DATE"],
                W_MATERIAL_BATCH: g[i]["W_MATERIAL_BATCH"],
				S_DIFFER_VALUE: g[i]["S_DIFFER_VALUE"],
				S_DIFFER_REASON: g[i]["S_DIFFER_REASON"],
            });
        }
        var params = {
            tableName: "BIO_BZ_MATERIEL_SOP_JL_MX",
            objects: object,
        };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/save/batch/table",
            ajaxData: params,
            succeed: function (res) {
                if (res["code"] > 0) {
                    alertMsg("保存成功", "success", function () {
                        funcExce(pathValue + "pageCallBack"); //执行回调
                        //funcExce(pathValue + "close"); //关闭页面
                        refreshGrid();
                    });
                } else {
                    alertMsg("保存列表失败", "error");
                }
            },
        });
    };

    /**
     * 保存
     */
    var submit = function () {
        var g = getGridItemsData(gridNameGrid);
        if (g.length == 0) {
            alertMsg("至少要有一条物料明细");
            return;
        }
        for (let index = 0; index < g.length; index++) {
            if (!g[index]["PRACTICAL_TOTAL"] || g[index]["PRACTICAL_TOTAL"] <= 0) {
                alertMsg("任务实际总用量不能为空或0");
                return;
            }
			if(!g[index]["M_FY_NUMBER"] || g[index]["M_FY_NUMBER"]<=0){
				alertMsg("反应数不能为空或0");
				return;
			}
        }
        //计算差异值
       for(var i = 0;i<g.length;i++){
           var a = g[i]["THEORETICAL_TOTAL"];
           var b = g[i]["PRACTICAL_TOTAL"];
           var ast = (b-a)/a;
           ast = ast.toFixed(2)/1;
           var s_differ_reason = g[i]["S_DIFFER_REASON"];
           if(ast <= -0.05 || ast >= 0.05){  
               if(s_differ_reason == null || s_differ_reason==undefined || s_differ_reason == ""){ 
                   ast = ast*100;
                   var ass = ast + "%";
                   g[i]["S_DIFFER_VALUE"]=ass;
                   alertMsg("差异值为:"+ass+",超出范围,请填写差异原因");
                   gridNameGrid.setDataSource(new kendo.data.DataSource({ data: [] }));
                   gridNameGrid.setDataSource(new kendo.data.DataSource({ data: g }));
                   return ;
               }
               
           }
           ast = ast*100;
           var ass1 = ast + "%";
           g[i]["S_DIFFER_VALUE"]=ass1;
       }
        var isAdd = paramsValue["ID"] == "" ? true : false;
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    paramsValue["ID"] = result["ID"];
                    getInfo("form", pathValue, { ID: paramsValue["ID"] });

                    // 保存SOP名称到执行单
                    var params = {
                        tableName: "EXE_TQQC_SHEET",
                        ID: paramsValue["EXE_TQQC_ID"],
                        SOP_NAME: $("#SOP_NAME" + pathValue).val(),
                    };
                    $.fn.ajaxPost({
                        ajaxType: "post",
                        ajaxUrl: "system/jdbc/save/one/table",
                        ajaxData: params,
                        succeed: function (res) {
                            if (res["code"] > 0) {
                                isAdd ? updateListMx() : deleteMaterials();
                            } else {
                                alertMsg("保存执行单失败", "error");
                            }
                        },
                    });
                } else {
                    alertMsg("保存主单失败", "error");
                }
            },
        });
    };

    /**
     * 添加物料
     */
    var addMaterial = function () {
        var wCode = getGridItemsData(gridNameGrid).map((item) => {
            return item["W_CODE"];
        });
        openWindow({
            url: `${pathValueReplace(pathValue)}doSop/addMaterial/addMaterial`,
            title: "添加物料",
            currUrl: `${pathValueReplace(pathValue)}doSop/doSop`,
        },
            { pPathValue: pathValue, wCode: wCode }
        );
    };

    /**
     * 回填物料添加到列表上
     * @param {*} wlDataList 添加的物料列表
     */
    var addSub = function (wlDataList) {
        for (var i = 0; i < wlDataList.length; i++) {
            gridNameGrid.dataSource.add(wlDataList[i]);
        }
    };

    /**
     * 替换path路径的-
     * @param {*} paths 原path
     * @returns 替换后的path
     */
    var pathValueReplace = function (paths) {
        return (paths + "").replace(/doSop-doSop$/, "").replace(/-/g, "/");
    };

    /**
     * 总的编辑入加
     * @param {*} ID
     * @param {*} wCode
     */
    var opensel = function (ID, wCode) {
        if (ID === "") {
            alertMsg("请先保存");
            return;
        }
        var winOpts = {
            url: `${pathValueReplace(pathValue)}popup/popup`,
            title: "物料批次号...",
            width: 900,
            height: 450,
            currUrl: `${pathValueReplace(pathValue)}doSop/doSop`,
            position: { top: 200, left: 100 },
        };
        openWindow(winOpts, { ID: ID, wCode: wCode }); //传递
    };

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
        "addSub": addSub,
        "opensel": opensel,
        "callBack": callBack,
        "selectList": selectList,
        "refreshGrid": refreshGrid,
        "addMaterial": addMaterial,
        "deleteMaterial": deleteMaterial,
        "calculateTotal": calculateTotal,
        "pathValueReplace": pathValueReplace,
        "exportData": exportData,
    });
});