$(document).ready(function() {
   var pathValue="biomarker-produ_lib-receive-libsm-index";
   var initData=function(){
       return {};
   }

   var gridNameDGrid;
   var gridNameD1Grid;
   var gridNameD2Grid;

   var init=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"doReMyTask",title:"确认接收"},
            {name:"return",target:"doBack",title:"退回排单"}
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
 read:{"query":"lib_pd_SHEET_list","objects":[
        ["蛋白前处理","二代常规DNA建库","二代常规RNA建库","三代PB基因组建库","三代ONT基因组建库","代谢建库"],
        ["待接收","实验退回"]],"search":{"EX_LB":["正常","切胶"],"EX_EXECUTE_MODE":"实验员"}},

            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"queryTaskLibExMx","objects":[[ROW_ID]]},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        init1();
         init2();
   }
   
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
        {name:"return",target:"doRerurn",title:"撤回"}
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
                read:{"query":"lib_pd_SHEET_list","objects":[
        	   ["蛋白前处理","二代常规DNA建库","二代常规RNA建库","三代PB基因组建库","三代ONT基因组建库","代谢建库"],
        	   ["已接收","切胶结果已审核","建库提交","审核退回","建库已审核"]],"search":{"EX_LB":["正常","切胶"],"EX_EXECUTE_MODE":"实验员"}},
           headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
            	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"queryTaskLibExMx","objects":[[ROW_ID]]},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
            }
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
  }
  var init2=function(params){
         var toolbar=getButtonTemplates(pathValue,[
             { name: "edit", target: "submit", title: "提交" }
         ]);
         var gridNameGridJson={
             url: "system/jdbc/query/one/table",
             sort: "",
             toolbar: toolbar,
  read:{"query":"lib_pd_SHEET_list","objects":[
         ["蛋白前处理","二代常规DNA建库","二代常规RNA建库","三代PB基因组建库","三代ONT基因组建库","代谢建库"],
         ["待接收提交","实验退回"]],"search":{"EX_LB":["正常","切胶"]}},
 
             headerFilter:function(cols,i){},
             detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
             detailInit: function (e) {
                 var ROW_ID = e.data.ID;
                var toolbar1= getButtonTemplates(pathValue, [
                ]);
                 var subGrid_N_JSON={
                     url: "system/jdbc/query/one/table",
                     sort: "",
                     toolbar: toolbar1,
                     height: 320,
                     read:{"query":"queryTaskLibExMx","objects":[[ROW_ID]]},
                 };
                 var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
             }
         };
         gridNameD2Grid= initKendoGrid("#gridNameD2Grid"+pathValue,gridNameGridJson);//初始化表格的方法
 }
   
	function getRandomId() {
    	   return (('FDSX' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
     };
     
     //根据执行单ID获取明细信息
     var doReMyTask=function(){
    	 
    		   var g=getGridSelectData(gridNameDGrid);  
    	       if(g.length!=1){
    	           alertMsg("请至少选择一条数据进行接收操作!");
    	           return ;
    	       }
    	       
    	       var time=sysNowTimeFuncParams["sysNowTime"];
    	       var username=getLimsUser()["name"];
    	       var objectupmain=[];
    	       var objectoutMain=[];
    	       var objectinMain=[];
    	       var outMainIDs=[];
    	       for(var i=0;i < g.length;i++){
    	    	   var mainid=g[i]["ID"];
    	    	   outMainIDs.push(mainid);
    	    	   //主单状态
    	    	   objectupmain.push({
        	       		"ID":mainid,
                            "EX_RE_STATUS":"待接收提交"
        	       	});
    	    	    objectoutMain.push({
    	    		      "ID":mainid,//唯一标识
    	    		      "IO_TYPE":"出库",//出入库类别
    	    		      "FROM_DH_NUMBER":g[i]["EX_DH_NO"],//来源执行单号
    	    		      "IO_NO":"OUT-"+g[i]["EX_DH_NO"],//出入库单号
    	    		      "IO_SOURCE":"建库",//来源说明
    	    		      "I0_APPLYDEPARTMENT":"",//申请部门
    	    		      "IO_APPLYPERSON":username,//申请人
    	    		      "OUTREASON":"0",//出库原因
    	    		      "SAMPLETYPE":"核酸",//样品类型
    	    		      "SYS_MAN":username,//操作人
    	    		      "SYS_INSERTTIME":time,//操作时间
    	    		      "IO_STATUS":"草稿"//状态
    	    		});
    	    	    objectinMain.push({
    	    		      "ID":"IN-"+mainid,//唯一标识
    	    		      "IO_TYPE":"入库",//出入库类别
    	    		      "FROM_DH_NUMBER":g[i]["EX_DH_NO"],//来源执行单号
    	    		      "IO_NO":"IN-"+g[i]["EX_DH_NO"],//出入库单号
    	    		      "IO_SOURCE":"建库",//来源说明
    	    		      "I0_APPLYDEPARTMENT":"",//申请部门
    	    		      "IO_APPLYPERSON":username,//申请人
    	    		      "OUTREASON":"0",//出库原因
    	    		      "SAMPLETYPE":"核酸",//样品类型
    	    		      "SYS_MAN":username,//操作人
    	    		      "SYS_INSERTTIME":time,//操作时间
    	    		      "IO_STATUS":"草稿"//状态
    	    		});
    	    	 }
    	     //生成样本出库单
    	     var newUrl="system/jdbc/save/one/table/objects";
    	     var paramsnainadd={"tableName":"BIO_SMPLE_IOBOM","objects":objectoutMain};
    	     putAddOrUpdata(newUrl,paramsnainadd,"否","生成样本出库单:");
    	     var paramsnainaddin={"tableName":"BIO_SMPLE_IOBOM","objects":objectinMain};
    	     putAddOrUpdata(newUrl,paramsnainaddin,"否","生成样本入库单:");
    	     //执行单状态更新
    	       var urlsend="system/jdbc/save/batch/table";
               var parammain={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
        	  putAddOrUpdata(urlsend,parammain,"是","执行单状态更新:");	       
        	   //主单明细提交处理
        	   doRequset(outMainIDs);
    	    
     }
     
     var doRequset=function(outMainIDs){
    	 
    	 var time=sysNowTimeFuncParams["sysNowTime"];
         var username=getLimsUser()["name"];
    	 var params={"query":"QueryCheckGeneFormOutLib","objects":[outMainIDs]};
    	   $.fn.ajaxPost({
    	        ajaxUrl:"system/jdbc/query/one/table",
    	        ajaxType: "post",
    	        ajaxData: params,
    	        succeed:function(result){
    	        	
    	        	if(result["code"]>0){//退回后再入理一条单
    	        		
    	        	var objectadd=[];
    	     	    var objectup=[];
    	     	    var objectOutMx=[];
    	     	   var objectInMx=[];
      	        	var rows=result["rows"];
      	        	
      	        	for(var i=0;i<rows.length;i++){
      	        		var row=rows[i];

      	    	        //更新记录---明细BIO_TASK_LIBMX
      	    	       	objectup.push({
      	    	       		"ID":row["ID"],//联联任务ID
      	    	       		"EX_RE_STATUS":"已接收",//接收状态
      	    	       		"TASK_LSMX_STATUS":"建库中",
      	    	       		"EX_RE_TIME":time//接收时间
      	    	       		
      	    	       	});
      	    	       	
      	    	       	//生成出库记录明细BIO_SMPLE_IOBOM_MX
      	    	       	objectOutMx.push({
      	    	       		"IO_ID":row["EXE_TQQC_ID"],//关联出入库主表ID,即与执行单ID等同
      	    	       		"IO_TYPE":"出库",//出入库类别
      	    	       		"TASK_MX_ID":row["ID"],//任务明细ID
      	    	       		"SAMPLE_ID":row["SAMPLE_ID"],//关联样本ID
      	    	       		"SAMPLE_GEN_LIB_NO":row["BIO_CODE"],
      	    	       		"SYS_MAN":username,//操作人
      	    	       		"SYS_INSERTTIME":time//操作时间
      	    	       	});  
      	    	      objectInMx.push({
    	    	       		"IO_ID":"IN-"+row["EXE_TQQC_ID"],//关联出入库主表ID,即与执行单ID等同
    	    	       		"IO_TYPE":"入库",//出入库类别
      	    	       		"TASK_MX_ID":row["ID"],//任务明细ID
    	    	       		"SAMPLE_ID":row["SAMPLE_ID"],//关联样本ID
    	    	       		"SAMPLE_GEN_LIB_NO":row["BIO_CODE"],
    	    	       		"SYS_MAN":username,//操作人
    	    	       		"SYS_INSERTTIME":time//操作时间
    	    	       	}); 
      	    	 	
      	        	}
      	        	  var urlsend="system/jdbc/save/batch/table";
      	        	  
      	              var paramsup={"tableName":"BIO_TASK_LIBMX","objects":objectup};
      	              
      	              var paramOutMx={"tableName":"BIO_SMPLE_IOBOM_MX","objects":objectOutMx};
      	              var paramOutMxIn={"tableName":"BIO_SMPLE_IOBOM_MX","objects":objectInMx};


      	              putAddOrUpdata(urlsend,paramsup,"否","同步更新任务明细:");
      	              putAddOrUpdata(urlsend,paramOutMx,"否","同步出库明细:");
      	             putAddOrUpdata(urlsend,paramOutMxIn,"否","同步出库明细:");
      		    	    	               
    	        	}
    	        		
    	        	
    	        },
    	        failed:function(result){
    	            alertMsg("提示:操作异常!","error");
    	        }
    	    });
    	 
     }
 
 //批量执行插入
 var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
     $.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:urls,
         ajaxData:inobjjson,
         succeed:function(result){
             if(result["code"]>0){
            	 if(isDoCallBack=="是"){
            		 alertMsg("提示:操作成功!");
            		 refreshGrid();
            	 }
             }else{
            	 alertMsg(errMsg+"操作失败!");
             }
         }
     });
 }

   //撤回
 var doRerurn=function(){
 	var g=getGridSelectData(gridNameD1Grid);
     if(g.length==0){
        	alertMsg("请至少选择一条记录进行操作!");
        	return;
      }
     var objectup=[];
     for(var i=0;i<g.length;i++){
	   	  	if(g[i]["EX_RE_STATUS"]=="建库提交"){  
	   	  		alertMsg("操作失败,所选记录已为“结果提交”状态!");
	   	  		return;
	   	  	}else{
		   	  	objectup.push({
	   	       		"ID":g[i]["ID"],
	   		       	"EX_RE_STATUS":"待接收"
	   		    });
	   	  	}
     }
    var urlsend="system/jdbc/save/batch/table";
    var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
 	putAddOrUpdata(urlsend,paramsup,"是","提交");
 }
 
var doBack=function(){
         var arrIds=getSelectData(gridNameDGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行操作!");
            return ;
        }      
        var winOpts={
            url:"biomarker/produ_lib/receive/libsm/doBack/doBack",
            title:"退回操作..",
            width:450,
            height:320,
            position:{"top":150,"left":200}
         };
         openWindow(winOpts,{"IDS":arrIds});
    }
   
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();//重新读取--刷新
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();//重新读取--刷新
        }
        if(gridNameD2Grid){
        	gridNameD2Grid.dataSource.read();//重新读取--刷新
        }
     }

     //预处理提交
     var submit = function () {
         debugger;
         var arrIds = getGridSelectData(gridNameD2Grid);
         if (arrIds.length != 1) {
             alertMsg("请只选择一条数据进行修改!");
             return;
         }
         var objectSheet = [];
         for (var i = 0; i < arrIds.length; i++) {
           if (arrIds[i]["EX_EXECUTE_MODE"].indexOf("PE")>-1)
          {
                    objectSheet.push({
                 "ID": arrIds[i]["ID"],//id
                 "EX_RE_STATUS": "前处理提交"       //状态
             });
            }else{
             objectSheet.push({
                 "ID": arrIds[i]["ID"],//id
                 "EX_RE_STATUS": "已接收"       //状态
             });
}



         }
 
         var urlsend = "system/jdbc/save/batch/table";
         var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
         putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
 
     }
    

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "submit":submit,
         "refreshGrid":refreshGrid,
         "doReMyTask":doReMyTask,
         "callBack":callBack,
         "doRerurn":doRerurn,
         "doBack":doBack,
     });
});