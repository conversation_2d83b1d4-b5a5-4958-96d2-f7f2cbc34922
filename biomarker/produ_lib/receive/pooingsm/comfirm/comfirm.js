$(document).ready(function() {
    
	var pathValue="biomarker-produ_lib-receive-pooingsm-comfirm-comfirm";
    var paramsValue;

    var initData=function(){
        return {
            tableName:"BIO_LIB_POOLING"
        };
    }

    var init=function(params){
    	
    	paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
 
 
  var comfireData=function(){
    	
    	//取出IDS
    	var ids=paramsValue["IDS"];    	
    	var object=[];
    	var paramsF = getJsonByForm("form",pathValue);
    	 //取出表单值
    	 var passFlag=paramsF["TASK_COMFIRM_RESULT"];
    	 var backmsg=paramsF["TASK_BACK"];
    	 
    	 var flagstr="";
    	 var flagstr2="";
    	 if(passFlag=="通过"){
    		 flagstr="已接收";
    	 }else if(passFlag=="退回"){
    		 flagstr="接收退回";
    	 }
         for(var i=0;i < ids.length;i++){
        	 object.push({"ID":ids[i],"POOL_STATUS":flagstr,"POOL_BACK":backmsg});
         }
         //执行更新
         var params={"tableName":"BIO_LIB_POOLING","objects":object};
         var url="system/jdbc/save/batch/table";
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:url,
             ajaxData:params,
             succeed:function(result){
                 if(result["code"]>0){
                 	console.log(result);
                  funcExce(pathValue+"pageCallBack",passFlag);
                  funcExce(pathValue+"close");
                 }else{
                 	console.log(result);
                 }
             }
         });    	
    }
 
  
    funcPushs(pathValue,{
        "init":init,
        "comfireData":comfireData
    });
 
 });