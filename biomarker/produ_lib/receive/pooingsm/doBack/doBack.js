$(document).ready(function() {
     var pathValue="biomarker-produ_lib-receive-pooingsm-doBack-doBack";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"EXE_TQQC_SHEET"
        };
    }
    var init=function(params){
    	paramsValue=params;
    }

  var submit=function(){
    	
        var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) {} else {
            alertMsg("验证未通过","wran");
            return ;
        }

    	var ids=paramsValue["IDS"];
    	var jsonData = getJsonByForm("form",pathValue);
    	
    	 var object=[];
         for(var i=0;i < ids.length;i++){
        	 object.push($.extend({},jsonData,{"ID":ids[i],"EX_RE_STATUS":"接收退回"}));
         }

         var params={"tableName":"EXE_TQQC_SHEET","objects":object};
         var url="system/jdbc/save/batch/table";
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:url,
             ajaxData:params,
             succeed:function(result){
                 if(result["code"]>0){
                 	funcExce(pathValue+"pageCallBack");
                       alertMsg("提交成功!");
                       funcExce(pathValue+"close");
                 }else{
                 	console.log(result);
                 }
             }
         });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit
    });
 
 });