$(document).ready(function() {
    var pathValue="biomarker-dispatch-dx-task-setff-setff";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_TASK_LIBMX"
        };
    }
    var init=function(params){
        paramsValue=params;
        getMyInfo();
    }
    
    var getMyInfo=function(){
    	var paramssql={"query":"query_lib_type_libarry_src_select","objects":[],"search":{"PRODUCT_TYPE":[paramsValue["PRODUCT_TYPE"]]}};
		$.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:"system/jdbc/query/one/table",
             ajaxData:paramssql,
             succeed:function(result){
            	 debugger;
                 if(result["code"]>0){
                	var rows=result["rows"];
 	  	        	for(var i=0;i<rows.length;i++){
 	  	        		var row=rows[i];
 	  	        		getInfo("form",pathValue,row);
 	  	        		break;
 	  	        	}
                              if(rows.length<1){
                                   alertMsg("自动获取方法库“<font color=#FF0000>"+paramsValue["PRODUCT_TYPE"]+"</font>”失败,请检查!");
                              }
                 }
             }
         });
    }
 
    var submit=function(){
        var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) {} else {
            alertMsg("验证未通过","wran");
            return ;
        }
        var ids=paramsValue["IDS"];
    	var jsonData = getJsonByForm("form",pathValue);
        var object=[];
         for(var i=0;i < ids.length;i++){
            object.push($.extend({},jsonData,{"ID":ids[i]}));
         }
         var url="system/jdbc/save/batch/table";
         var params={"tableName":"BIO_TASK_LIBMX","objects":object};
         putAddOrUpdata(url,params,"是","");
    }

    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
               	 if(isDoCallBack=="是"){
               	     funcExce(pathValue+"pageCallBack");
                     alertMsg("提交成功!");
                     funcExce(pathValue+"close");
               	 }
               	 
                }else{
               	 alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });