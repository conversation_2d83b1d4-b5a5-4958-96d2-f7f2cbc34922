$(document).ready(function () { 
    var pathValue = "biomarker-dispatch-dx-task-index";
    var initData = function () {
        return {};
    }

    var gridNameGrid;
    var gridNameLGrid;
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "inputCheck1", title: "批量提交" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_DD_BIO_TASK_LIB_list", "objects": [["待审核"], ["蛋白建库", "代谢建库"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "TASK_LS_NO") {

                         setJsonParam(cols[i], "template", getTemplate("#= TASK_LS_NO #", "funcExce(\'" + pathValue + "opensel\',\'#= ID #\',\'#= TASK_LS_TYPE #\',\'#= TASK_LS_CDATE#\',\'#= BEGIN_STATUS#\');", "txt"));
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);

        init_1();
    }

    var init_1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doReturn", title: "撤回" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_DD_BIO_TASK_LIB_list", "objects": [["已审核", "已终止", "终止"], ["蛋白建库", "代谢建库"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "TASK_LS_NO") {
                        setJsonParam(cols[i], "template", getTemplate("#= TASK_LS_NO #", "funcExce(\'" + pathValue + "opensel2\',\'#= ID #\',\'#= TASK_LS_TYPE #\');", "txt"));
                    }
                }
            }
        };
        gridNameLGrid = initKendoGrid("#gridNameLGrid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    var opensel = function (ID, type, TASK_LS_CDATE,BEGIN_STATUS) {
        var winOpts = {
            url: "biomarker/dispatch/dx/task/addlib/addlib",
            title: "修改:" + type + "任务下达.."
        };
        openWindow(winOpts, {"BEGIN_STATUS":BEGIN_STATUS, "ID": ID, "TASK_LS_TYPE": type, "TASK_LL": TASK_LS_CDATE, "SIGN": 1 });
    }
    var opensel2 = function (ID, type) {
        var winOpts = {
            url: "biomarker/dispatch/dx/task/addlib/addlib",
            title: "修改:" + type + "任务下达.."
        };
        openWindow(winOpts, { "ID": ID, "TASK_LS_TYPE": type, "SIGN": 2 });
    }
    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
        if (gridNameLGrid) {
            gridNameLGrid.dataSource.read();
        }
    }

    var inputCheck1 = function () {
        debugger
        var arrIds = getGridSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行审核操作!");
            return;
        }
        var username = getLimsUser()["name"];

        objectBTT = [];//更新主单
        upparmas = [];//更新明细 
        for (var i = 0; i < arrIds.length; i++) {
            bttrow = arrIds[i];//主单信息

            var BTTMXrows;//全明细信息
            $.fn.ajaxPost({
                ajaxUrl: "system/jdbc/query/one/table",
                ajaxType: "post",
                ajaxAsync: false,
                ajaxData: {
                    "query": "query_BIO_TASK_LIBMX_list", "size": 3000,
                    "search": { "TASK_LS_ID": [bttrow["ID"]] }
                },
                succeed: function (rs) {
                    BTTMXrows = rs.rows;
                }
            });

            if (BTTMXrows[0]["BUSINESS_UNIT"]) { //判断方法库时候已设置


                for (var j = 0; j < BTTMXrows.length; j++) {

                    var row = BTTMXrows[j];


                    if (row["DATA_UNIT"] && row["DATA_UNIT"].indexOf("G") > -1) {
                        the_ata = row["DATA_SUM"] / 0.3;
                    } else {
                        the_ata = row["DATA_SUM"];
                    }
                    if (the_ata == null) the_ata = 0;
                    upparmas.push({
                        "ID": row["ID"],
                        "THE_DATA_SUM": the_ata.toFixed(4),
                        "THE_DATA_APSUM": the_ata.toFixed(4)
                    });
                }

                var TASK_LS_TYPE_LB = "";

                if (BTTMXrows[0]["LIBRARY_FLOW"] == "代谢建库") TASK_LS_TYPE_LB = "常规建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "二代常规RNA建库") TASK_LS_TYPE_LB = "常规建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "蛋白前处理") TASK_LS_TYPE_LB = "常规建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "二代常规DNA建库") TASK_LS_TYPE_LB = "常规建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "三代ONT基因组建库") TASK_LS_TYPE_LB = "常规建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "ATAC建库") TASK_LS_TYPE_LB = "ATAC建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "HIC建库") TASK_LS_TYPE_LB = "HIC建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "DNA混样建库-MCD简化建库") TASK_LS_TYPE_LB = "MCD简化建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "DNA混样建库-SLAF建库") TASK_LS_TYPE_LB = "混样建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "PB混样-微生物全长") TASK_LS_TYPE_LB = "混样建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "PB混样-全长转录组") TASK_LS_TYPE_LB = "混样建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "PB混样-基因组") TASK_LS_TYPE_LB = "混样建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "混样建库-ONT建库-DNA") TASK_LS_TYPE_LB = "混样建库";
                if (BTTMXrows[0]["LIBRARY_FLOW"] == "混样建库-ONT建库-Iso-RNA") TASK_LS_TYPE_LB = "混样建库";

                objectBTT.push({
                    "ID": bttrow["ID"],
                    "TASK_LS_TYPE_LB": TASK_LS_TYPE_LB,
                    "TASK_LIB_TYPE": BTTMXrows[0]["LIBRARY_TYPE_EN"],//文库类型
                    "TASK_LS_SAMPLESUM": BTTMXrows.length, //样品数
                    "BUSINESS_UNIT": row["BUSINESS_UNIT"],//业务单元	
                    "CYC_DW": row["CYC_DW"],//执行周期单位
                    "PROJECT_DEPT": row["MEHOD_JKPLAT"],//建库部门	
                })


                var IS_SHORT_PERIOD = "否";
                var IS_FULL_LIFE_CYCLE2 = "否";
                var IS_FULL_LIFE_CYCLE = bttrow["IS_FULL_LIFE_CYCLE"];
                if (IS_FULL_LIFE_CYCLE == "是") {
                    IS_FULL_LIFE_CYCLE2 = "是"
                } else {
                    var CUSTOMER_VIP = bttrow["CUSTOMER_VIP"];
                    if (CUSTOMER_VIP.indexOf(2) > -1) {
                        IS_SHORT_PERIOD = "是";
                    }

                }


                var bb = doCycJK(BTTMXrows[0]["LIBRARY_TYPE_EN"], BTTMXrows.length, bttrow["THE_DATA_SUM"], "建库标准用时", 1, BTTMXrows[0]["CYC_DW"], BTTMXrows[0]["BUSINESS_UNIT"], bttrow["TASK_LS_CDATE"], IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);
                if (!bb) {//判断建库标准周期缺失是否获取
                    objectBTT[i]["REASON_AUDIT_ERROR"] = "标准周期缺失，维护标准周期";//自动审核错误原因
                    continue;
                } else {
                    objectBTT[i]["TASK_LS_EXCDAYS"] = bb["TASK_LS_EXCDAYS"];//建库执行天数
                    objectBTT[i]["TASK_LS_LDATE"] = bb["TASK_LS_LDATE"];//建库标准结单日期
                    objectBTT[i]["TASK_TEST_DELIVERDATE"] = bb["TASK_LS_LDATE"];//建库标准结单日期 --“建库计划完成日期”，取值等于 建库标准结单日期
                }

                var cc = doCycJK(BTTMXrows[0]["LIBRARY_TYPE_EN"], BTTMXrows.length, bttrow["THE_DATA_SUM"], "实验交付标准用时", 2, BTTMXrows[0]["CYC_DW"], BTTMXrows[0]["BUSINESS_UNIT"], bttrow["TASK_LS_CDATE"], IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);
                if (!cc) {//判断实验标准周期缺失是否获取
                    objectBTT[i]["REASON_AUDIT_ERROR"] = "标准周期缺失，维护标准周期";//自动审核错误原因	
                    continue;
                } else {
                    objectBTT[i]["TASK_JF_EXCDAYS"] = cc["TASK_JF_EXCDAYS"];//实验交付执行天数
                    objectBTT[i]["TASK_LS_DELIVERDATE"] = cc["TASK_LS_DELIVERDATE"];//实验标准交付日期
                }

                var dd = doCycJK(BTTMXrows[0]["LIBRARY_TYPE_EN"], BTTMXrows.length, bttrow["THE_DATA_SUM"], "测序标准用时", 3, BTTMXrows[0]["CYC_DW"], BTTMXrows[0]["BUSINESS_UNIT"], new Date(bb["TASK_LS_LDATE"]), IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);
                if (!dd) {//判断实验标准周期缺失是否获取
                    objectBTT[i]["REASON_AUDIT_ERROR"] = "标准周期缺失，维护标准周期";//自动审核错误原因	
                    continue;
                } else {
                    objectBTT[i]["TASK_SEQ_EDATE"] = dd["TASK_SEQ_EDATE"];//测序标准完成日期 
                }


                if (bttrow["LSM_KEY_P"] == null) {//判断LSM关键字是否存在//判断项目关键字是否存在
                    objectBTT[i]["REASON_AUDIT_ERROR"] = "项目关键字缺失";//自动审核错误原因	
                    continue;
                }




                sendLSM(bttrow["LSM_KEY_P"], bb["TASK_LS_LDATE"], cc["TASK_LS_DELIVERDATE"], objectBTT[i]["TASK_SEQ_EDATE"],bttrow["PROJECT_OUT_TYPE"]);

                var time = sysNowTimeFuncParams["sysNowTime"];
                var username = getLimsUser()["name"];
                objectBTT[i]["SYS_MAN_L"] = username;
                objectBTT[i]["SYS_INSERTTIME_L"] = time;
                objectBTT[i]["DD_LS_AUDIT_TIME"] = time;

                //   doGenNo(bttrow["TASK_LS_NO"], BTTMXrows); //提前生成文库编号


                flagstr = "已审核";
                flagstr2 = "已审核";

                objectBTT[i]["DD_TASK_LS_STATUS"] = flagstr;
                objectBTT[i]["TASK_LS_STATUS"] = flagstr2;

            }
        }
        if (objectBTT.length > 0) {
            var url = "system/jdbc/save/batch/table";
            var paramsadd = { "tableName": "BIO_TASK_LIB", "objects": objectBTT };
            putAddOrUpdata(url, paramsadd, "是", "");
        }
        if (upparmas.length > 0) {
            var url = "system/jdbc/save/batch/table";
            var paramsadd = { "tableName": "BIO_TASK_LIBMX", "objects": upparmas };
            putAddOrUpdata(url, paramsadd, "是", "");
        }


    }

    var doReturn = function () {

        var arrIds = getSelectData(gridNameLGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }

        var params = { "query": "checkTaskMxReturn", "objects": [arrIds] };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var flag = 0;
                    for (var i = 0; i < rows.length; i++) {
                        var g = rows[i];
                        if (g["TASK_LSMX_STATUS"] == "草稿") {
                            flag = 0;
                            break;
                        } else {
                            flag = 1;
                            break;
                        }
                    }
                    var username = getLimsUser()["name"];
                    if (username == "蓝勇胜" || username == "徐彦岭" || username == "郭丽丽") flag = 0;
                    if (flag == 0) {
                        var objectup = [];
                        for (var i = 0; i < arrIds.length; i++) {
                            objectup.push({
                                "ID": arrIds[i],
                                "DD_TASK_LS_STATUS": "待审核",
                                "EX_MAN_ID": null
                            });
                        }
                        var url = "system/jdbc/save/batch/table";
                        var paramsaddqcmx = { "tableName": "BIO_TASK_LIB", "objects": objectup };
                        putAddOrUpdata(url, paramsaddqcmx, "是", "更新主单状态");
                    } else {
                        alertMsg("提示:操作失败,存在任务已排单接收!");
                    }

                } else {
                    console.log(result);
                }
            }
        });
    }

    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    //正实验LSM
    var sendLSM = function (LSM_KEY_P, TASK_LS_LDATE, TASK_LS_DELIVERDATE, TASK_SEQ_EDATE,PROJECT_OUT_TYPE) { 

        var fmt2 = "yyyy-MM-ddTHH:mm:ss.000+0800";
        var params = {
            "jiraKey": LSM_KEY_P,
            "updateField": {
                "customfield_12101": TASK_LS_LDATE,	//建库标准结单日期
                "customfield_14201": TASK_LS_LDATE,//建库计划完成日期  
                "customfield_18820": toDateFormatByZone(TASK_LS_DELIVERDATE, fmt2),//实验标准交付日期
                "customfield_13900": TASK_LS_DELIVERDATE,//实验标准交付日期
                //   "customfield_19108": { "value": PROJECT_OUT_TYPE },//实验生产类型
                "customfield_10227": TASK_SEQ_EDATE
            }
        };
        if (PROJECT_OUT_TYPE !== null && PROJECT_OUT_TYPE !== undefined && PROJECT_OUT_TYPE !== "") {
            params["updateField"]["customfield_19112"] = { "value": PROJECT_OUT_TYPE };//实验生产类型
        }  

        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
            },
            failed: function (res) {
            }
        });

    }

    //提前生成文库编号
    var doGenNo = function (tasklsno, BTTMXrows) {
        order = [];
        order_mn = [];
        ordersm = [];
        ordersm_mn = [];
        ordersm_no = [];
        ordersm_no_mn = [];
        var g = BTTMXrows;
        //取对应表
        var params = { "query": "queryBioLibTypeList", "objects": [] };
        var rows
        var iniFist = "";
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    rows = result["rows"];
                }
            }
        });

        var libtypename = [];
        var initials = [];
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            libtypename.push(row["LIB_TYPE_NAME"]);
            initials.push(row["INITIALS"]);
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            //更新记录
            iniFist = checkInitals(g[i]["LIBRARY_TYPE_EN"], libtypename, initials);
            objectup.push({
                "ID": g[i]["ID"],
                "DATA_LIBCODE": getLibCodeNo(tasklsno, iniFist, 0, g[i]["ISPOOLSM"], g[i]["SAMPLE_CODE"])
            });

        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "否", "提交");
    }

    //比对取对照
    var checkInitals = function (name, names, initals) {
        for (var i = 0; i < names.length; i++) {
            if (name == names[i]) {
                return initals[i];
            }
        }
        return "";
    }


    var getLibCodeNo = function (orderno, c, n, ispoolsm, smcode) {
        if (ispoolsm == "拆文库") {//拆文库
            var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
            var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
            var mn;
            if (indexn > -1) {
                mn = ordersm_no_mn[indexn] + 1;
                ordersm_no_mn[indexn] = mn;
            } else {
                mn = 1;
                ordersm_no.push(orderno + "_" + smcode + "_no");
                ordersm_no_mn.push(mn);
            }
            var indexn2 = order.indexOf(orderno);
            var indexn3 = ordersm.indexOf(orderno + "_" + smcode);
            var mn2;
            if (indexn3 > -1) {
                mn2 = ordersm_mn[indexn3];
            } else if (indexn2 > -1) {
                mn2 = order_mn[indexn2] + 1;
                order_mn[indexn2] = mn2;
                ordersm.push(orderno + "_" + smcode);
                ordersm_mn.push(mn2);
            } else {
                mn2 = n + 1;
                order.push(orderno);
                order_mn.push(mn2);
                ordersm.push(orderno + "_" + smcode);
                ordersm_mn.push(mn2);
            }
            tempcode = tempcode + c + getNo(mn2, 0) + "-01";
            return tempcode;
        } else {//拆样
            var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
            var indexn = order.indexOf(orderno);
            var mn = 1;
            if (indexn > -1) {
                mn = order_mn[indexn] + 1;
                order_mn[indexn] = mn;
            } else {
                order.push(orderno);
                order_mn.push(n + 1);
                mn = n + 1;
            }
            tempcode = tempcode + c + getNo(mn, 0) + "-01";
            return tempcode;


        }
    }
    //获取周期定义,推算出截止结果日期
    var doCycJK = function (type, countSm, smnumber, dep, selflag, CYC_DW, BUSINESS_UNIT, TASK_LS_CDATE, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2) {
        //样品提取检测标准用时
        var cycdw = CYC_DW;
        var bus = BUSINESS_UNIT;
        var params = "";
        var flag = 0;
        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        } else {
            flag = 1;
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        }
        var rows;

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    rows = result["rows"];
                } else {
                    return false;
                }
            }
        });


        var m = getMyMonth();
        var dateNumber = 0;
        var seleDateFlag = "工作日";//日历取向
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            seleDateFlag = row["CYC_FLAG"];
            if (m == 1) dateNumber = row["MONTH_1"];
            if (m == 2) dateNumber = row["MONTH_2"];
            if (m == 3) dateNumber = row["MONTH_3"];
            if (m == 4) dateNumber = row["MONTH_4"];
            if (m == 5) dateNumber = row["MONTH_5"];
            if (m == 6) dateNumber = row["MONTH_6"];
            if (m == 7) dateNumber = row["MONTH_7"];
            if (m == 8) dateNumber = row["MONTH_8"];
            if (m == 9) dateNumber = row["MONTH_9"];
            if (m == 10) dateNumber = row["MONTH_10"];
            if (m == 11) dateNumber = row["MONTH_11"];
            if (m == 12) dateNumber = row["MONTH_12"];

            break;
        }
        //项目周期
        if (dateNumber == 0) {
            return false;
        } else {
            var aa = doGetEndDateJK(seleDateFlag, dateNumber, dateNumber, selflag, TASK_LS_CDATE);
            if (selflag == 1) aa["TASK_LS_EXCDAYS"] = dateNumber;
            if (selflag == 2) aa["TASK_JF_EXCDAYS"] = dateNumber;
            return aa;
        }


    }
    //推算截止日期
    var doGetEndDateJK = function (seleDateFlag, dateNumber1, dateNumber2, selflag, TASK_LS_CDATE) {
        var thedate = new Date(TASK_LS_CDATE * 1);
        var thedate2 = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }
        var rows
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    rows = result["rows"];
                }
            }
        });

        var noDoDateS = [];
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
        }
        if (selflag == 1) {
            //建库标准日期
            for (var i = 0; i < dateNumber1; i++) {
                var base = 1000 * 60 * 60 * 24;
                thedate = new Date(thedate.getTime() + base);
                for (var j = 0; j < noDoDateS.length; j++) {
                    if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                        thedate = new Date(thedate.getTime() + base);//日期向前一天
                    }
                }

            }
            return {
                "TASK_LS_LDATE": toDateFormatByZone(thedate, "yyyy-MM-dd")
            }

        }
        if (selflag == 2) {
            //实验标准交付日期
            for (var i = 0; i < dateNumber2; i++) {
                var base = 1000 * 60 * 60 * 24;
                // var TASK_LLS = paramsValue["TASK_LL"] * 1;  
                //thedate2=new Date(thedate2.getTime() + base);
                if (i == 0) {
                    var TASK_LLS = TASK_LS_CDATE * 1;
                    thedate2 = new Date(TASK_LLS + (base));
                } else {
                    //TASK_LLS=new Date(thedate.getTime() + base);
                    thedate2 = new Date(thedate2.getTime() + base);
                }
                for (var j = 0; j < noDoDateS.length; j++) {
                    if (toDateFormatByZone(thedate2, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                        thedate2 = new Date(thedate2.getTime() + base);//日期向前一天
                    }
                }
            }

            return {
                "TASK_LS_DELIVERDATE": toDateFormatByZone(thedate2, "yyyy-MM-dd")
            }
        }

        if (selflag == 3) {
            //建库标准日期
            for (var i = 0; i < dateNumber1; i++) {
                var base = 1000 * 60 * 60 * 24;
                thedate = new Date(thedate.getTime() + base);
                for (var j = 0; j < noDoDateS.length; j++) {
                    if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                        thedate = new Date(thedate.getTime() + base);//日期向前一天
                    }
                }

            }
            return {
                "TASK_SEQ_EDATE": toDateFormatByZone(thedate, "yyyy-MM-dd")
            }

        }

    }
    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }

    //文库/切胶流程号段(项目期号内)
    var getNo = function (num, ki) {
        num = num + ki;
        if (num < 10) {
            num = "000" + num;
            return num;
        }
        if (num >= 10 && num < 100) {
            num = "00" + num;
            return num;
        }
        if (num >= 100 && num < 1000) {
            num = "0" + num;
            return num;
        }
        return num;
    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "opensel": opensel,
        "opensel2": opensel2,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "inputCheck1": inputCheck1,
        "doReturn": doReturn
    });
});