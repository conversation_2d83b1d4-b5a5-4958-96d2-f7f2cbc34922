$(document).ready(function () {
    var pathValue = "biomarker-dispatch-dx-task-addlib-addlib";
    var initData = function () {
        return {
            tableName: "BIO_TASK_LIB"
        };
    }
    var paramsValue;
    var gridNameGrid;
    var TASK_LS_LDATE;
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);//传入id
        debugger;
        //设表单固定值
        $("#TASK_LS_TYPE" + pathValue).val(params["TASK_LS_TYPE"]);
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doSetff", title: "设置方法库.." },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",//排序
            height: fullh - 300,
            toolbar: toolbar,
            size: 5000,
            read: { "query": "query_BIO_TASK_LIBMX_list-dx", "objects": [], "search": { "TASK_LS_ID": [paramsValue["ID"]] } },
            fetch: function (data) {
                if (data.length > 0) {
                    //样品数
                    $("#TASK_LS_SAMPLESUM" + pathValue).val(data.length);
                    var totalNumber = $("#ZL_SUM" + pathValue).val();
                    var smSum = $("#SAMPLE_SUM" + pathValue).val();
                    var dataSum = 0;
                    if (totalNumber >= 0 && smSum > 0) dataSum = totalNumber / smSum;

                    var type = "";
                    var THE_DATA_SUM = 0;
                    var upparmas = [];
                    var LIBRARY_FLOW = "";
                    for (var i = 0; i < data.length; i++) {
                        var row = data[i];
                        var the_ata = 0;
                        LIBRARY_FLOW = row["LIBRARY_FLOW"];
                        if (row["BUSINESS_UNIT"]) {
                            $("#BUSINESS_UNIT" + pathValue).val(row["BUSINESS_UNIT"]);
                            $("#CYC_DW" + pathValue).val(row["CYC_DW"]);//执行周期单位
                            $("#PROJECT_DEPT" + pathValue).val(row["MEHOD_JKPLAT"]);
                            type = row["LIBRARY_TYPE_EN"];
                        }
                        //根据合同数据量,统计并转换赋给本期数据量M  Reads数（M）=合同数据量（备注，单位是G）/0.3

                        if (dataSum > 0) {
                            if (type == "Reseq_ONT(8K)" ||
                                type == "Reseq_ONT(20K)" ||
                                type == "Iso-RNA(ONT)" ||
                                type == "Denovo-Pac(hifi)" ||
                                type == "Denovo-Pac(20-30K)" ||
                                type == "Iso-RNA(Pac)"
                            ) {

                                THE_DATA_SUM += dataSum;
                                the_ata = dataSum;
                            } else {

                                if (row["DATA_UNIT"] && row["DATA_UNIT"].indexOf("G") > -1) {
                                    THE_DATA_SUM = THE_DATA_SUM + dataSum / 0.3;
                                    the_ata = dataSum / 0.3;
                                } else {
                                    THE_DATA_SUM = THE_DATA_SUM + dataSum;
                                    the_ata = dataSum;
                                }
                            }
                        }
                        upparmas.push({
                            "ID": row["ID"],
                            "DATA_SUM": dataSum,
                            "THE_DATA_SUM": the_ata.toFixed(2)
                        });
                    }
                    //根据方法库中的建库流向,设置 建库类别 


                    if (LIBRARY_FLOW == "代谢建库") $("#TASK_LS_TYPE_LB" + pathValue).val("常规建库");
                    if (LIBRARY_FLOW == "蛋白前处理") $("#TASK_LS_TYPE_LB" + pathValue).val("常规建库");
                    if (LIBRARY_FLOW == "二代常规RNA建库") $("#TASK_LS_TYPE_LB" + pathValue).val("常规建库");
                    if (LIBRARY_FLOW == "二代常规DNA建库") $("#TASK_LS_TYPE_LB" + pathValue).val("常规建库");
                    if (LIBRARY_FLOW == "三代ONT基因组建库") $("#TASK_LS_TYPE_LB" + pathValue).val("常规建库");

                    if (LIBRARY_FLOW == "ATAC建库") $("#TASK_LS_TYPE_LB" + pathValue).val("ATAC建库");
                    if (LIBRARY_FLOW == "HIC建库") $("#TASK_LS_TYPE_LB" + pathValue).val("HIC建库");

                    if (LIBRARY_FLOW == "DNA混样建库-MCD简化建库") $("#TASK_LS_TYPE_LB" + pathValue).val("MCD简化建库");

                    if (LIBRARY_FLOW == "DNA混样建库-SLAF建库") $("#TASK_LS_TYPE_LB" + pathValue).val("混样建库");
                    if (LIBRARY_FLOW == "PB混样-微生物全长") $("#TASK_LS_TYPE_LB" + pathValue).val("混样建库");
                    if (LIBRARY_FLOW == "PB混样-全长转录组") $("#TASK_LS_TYPE_LB" + pathValue).val("混样建库");
                    if (LIBRARY_FLOW == "PB混样-基因组") $("#TASK_LS_TYPE_LB" + pathValue).val("混样建库");
                    if (LIBRARY_FLOW == "混样建库-ONT建库-DNA") $("#TASK_LS_TYPE_LB" + pathValue).val("混样建库");
                    if (LIBRARY_FLOW == "混样建库-ONT建库-Iso-RNA") $("#TASK_LS_TYPE_LB" + pathValue).val("混样建库");

                    var url = "system/jdbc/save/batch/table";
                    var paramsadd = { "tableName": "BIO_TASK_LIBMX", "objects": upparmas };
                    putAddOrUpdata(url, paramsadd, "否", "");
                    //两位小数
                    THE_DATA_SUM = THE_DATA_SUM.toFixed(2);

                    $("#TASK_LIB_TYPE" + pathValue).val(type);
                    $("#THE_DATA_SUM" + pathValue).val(THE_DATA_SUM);
                    var countSm = data.length;
                    if (type != ""&& paramsValue["SIGN"] == 1) {
                        doCyc(type, countSm, THE_DATA_SUM, "建库标准用时", 1);
                        doCyc(type, countSm, THE_DATA_SUM, "实验交付标准用时", 2);
                        doCyc(type, countSm, THE_DATA_SUM, "测序标准用时", 3);
                    } else {     if ( paramsValue["SIGN"] == 1) {
                        alertMsg("提示:文库类型为空,请设置方法库!!");
                        return;
                    }
                    }

                }

            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    var open = function (ID) {
        var winOpts = {
            url: "biomarker/operation/lib/task/edittask/edittask",
            title: "修改:建库任务明细..",
            currUrl: replacePathValue(pathValue)
        };
        openWindow(winOpts, { "ID": ID });//传递id
    }


    var submit = function () { debugger

        var paramsF = getJsonByForm("form", pathValue);
        if (paramsValue["SIGN"] == 2) {

            var upparmas = [];

            upparmas.push({
                "ID": paramsValue["ID"],
                "LSM_KEY": paramsF["LSM_KEY"],
                "LSM_KEY_P": paramsF["LSM_KEY_P"]
            });
            var url = "system/jdbc/save/batch/table";
            var paramsadd = { "tableName": "BIO_TASK_LIB", "objects": upparmas };
            putAddOrUpdata(url, paramsadd, "是", "");

            return;

        }
        var validator = $("#form" + pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) { } else {
            alertMsg("验证未通过", "wran");
            return;
        }
        //通过后刷新一下列表,
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }

        if (paramsF["TASK_LS_LDATE"] != "" && paramsF["TASK_TEST_DELIVERDATE"] != "" && paramsF["TASK_COMFIRM_RESULT"] == "通过") {
            sendLSM();
        }


        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        $("#SYS_MAN_L" + pathValue).val(username);
        $("#SYS_INSERTTIME_L" + pathValue).val(time);
        $("#DD_LS_AUDIT_TIME" + pathValue).val(time);
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if ( (paramsF["TASK_COMFIRM_RESULT"] == "退回" ||paramsF["TASK_COMFIRM_RESULT"] == "终止" ) && paramsValue["BEGIN_STATUS"]=="划拨成功") {
                        zjcRefund()
                    }
                    //提交成功
                    alertMsg("提交成功", "success", function () {
                        funcExce(pathValue + "pageCallBack");//执行回调
                        funcExce(pathValue + "close");//关闭页面
                    });
                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });
    }
    var zjcRefund = function () {
        var paramsF = getJsonByForm("form", pathValue);
        var params = {
            "jiraKey": paramsF["LSM_KEY_P"],//项目关键字
            "contractNo": paramsF["CONTRACT_NO"],//合同编号
            "issue": paramsF["PROJECT_SUBNO"],//项目期号
            "currentTotalAmount": paramsF["CURRENT_TOTAL_AMOUNT"],//本期总金额
            "startUpSampleNumber": paramsF["TASK_LS_SAMPLESUM"]//启动样品数
        };


        var aa = "划拨退回失败";
        var BEGIN_STATUS = "划拨退回失败";
        var inobjjson = { "url": "http://"+JIRRA_URL+"/api/capital/pool/huaKai/cancelStartUp", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (result.apiData.result == "true" || result.apiData.result) {
                        aa = "划拨退回成功:接口返回值【" + result.apiData.msg + "】!";
                        BEGIN_STATUS= "划拨退回成功";
                    } else {
                        aa = "划拨退回失败:接口返回值【" + result.apiData.msg + "】!";
                    }
                } else {
                    aa = "划拨退回失败:无法访问到【https://"+JIRRA_URL+"/api/capital/pool/huaKai/cancelStartUp】!";
                }
            }
        });
        var object=[];
        object.push({
            "ID": paramsValue["ID"],
            "BEGIN_STATUS": BEGIN_STATUS,
            "FUNDS_TRANSFER_MSG": aa
        });
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd = { "tableName": "BIO_TASK_LIB", "objects": object };
        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");
    }


    //获取周期定义,推算出截止结果日期
    var doCyc = function (type, countSm, smnumber, dep, selflag) {

        //样品提取检测标准用时
        var cycdw = $("#CYC_DW" + pathValue).val();
        var bus = $("#BUSINESS_UNIT" + pathValue).val();
        var params = "";
        var flag = 0;

        var IS_FULL_LIFE_CYCLE = $("#IS_FULL_LIFE_CYCLE" + pathValue).val();

        var IS_SHORT_PERIOD = "否";
        var IS_FULL_LIFE_CYCLE2 = "否";

        if (IS_FULL_LIFE_CYCLE == "是") {
            IS_FULL_LIFE_CYCLE2 = "是"
        } else {
            var CUSTOMER_VIP = $("#CUSTOMER_VIP" + pathValue).val();
            if (CUSTOMER_VIP.indexOf(2) > -1) {
                IS_SHORT_PERIOD = "是";
            }
        }



        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        } else {
            flag = 1;
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        }
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var m = getMyMonth();
                    var dateNumber = 0;
                    var seleDateFlag = "工作日";//日历取向
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        seleDateFlag = row["CYC_FLAG"];
                        if (m == 1) dateNumber = row["MONTH_1"];
                        if (m == 2) dateNumber = row["MONTH_2"];
                        if (m == 3) dateNumber = row["MONTH_3"];
                        if (m == 4) dateNumber = row["MONTH_4"];
                        if (m == 5) dateNumber = row["MONTH_5"];
                        if (m == 6) dateNumber = row["MONTH_6"];
                        if (m == 7) dateNumber = row["MONTH_7"];
                        if (m == 8) dateNumber = row["MONTH_8"];
                        if (m == 9) dateNumber = row["MONTH_9"];
                        if (m == 10) dateNumber = row["MONTH_10"];
                        if (m == 11) dateNumber = row["MONTH_11"];
                        if (m == 12) dateNumber = row["MONTH_12"];

                        break;
                    }
                    //项目周期
                    if (dateNumber == 0) {
                        $("#CYC_FLAG" + pathValue).val("");
                        if (selflag == 1) $("#TASK_LS_EXCDAYS" + pathValue).val("");
                        if (selflag == 2) $("#TASK_JF_EXCDAYS" + pathValue).val("");
                        var knumber = 0;
                        if (flag == 0) {
                            knumber = countSm;
                        } else {
                            knumber = smnumber;
                        }
                        alert("提示:未获取到周期数,请检测条件是否满足(工序标准:“" + dep + "”,单位:“" + cycdw + "”,文库类型:“" + type + "”,执行参数:“" + knumber + "”)！");
                        return;
                    } else {
                        $("#CYC_FLAG" + pathValue).val(seleDateFlag);
                        if (selflag == 1) $("#TASK_LS_EXCDAYS" + pathValue).val(dateNumber);
                        if (selflag == 2) $("#TASK_JF_EXCDAYS" + pathValue).val(dateNumber);

                        doGetEndDate(seleDateFlag, dateNumber, dateNumber, selflag);
                    }

                }
            }
        });

    }

    //推算截止日期
    var doGetEndDate = function (seleDateFlag, dateNumber1, dateNumber2, selflag) {
        var thedate = new Date(paramsValue["TASK_LL"] * 1);
        if (selflag == 3) thedate = TASK_LS_LDATE;
        var thedate2 = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    if (selflag == 1) {
                        //建库标准日期
                        for (var i = 0; i < dateNumber1; i++) {
                            var base = 1000 * 60 * 60 * 24;
                            thedate = new Date(thedate.getTime() + base);
                            for (var j = 0; j < noDoDateS.length; j++) {
                                if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                    thedate = new Date(thedate.getTime() + base);//日期向前一天
                                }
                            }

                        }

                        //推算出的最终截止日期
                        $("#TASK_LS_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                        TASK_LS_LDATE=thedate;
                        $("#TASK_TEST_DELIVERDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                    }
                    if (selflag == 2) {
                        //实验标准交付日期
                        for (var i = 0; i < dateNumber2; i++) {
                            var base = 1000 * 60 * 60 * 24;
                            thedate2 = new Date(thedate2.getTime() + base);
                            for (var j = 0; j < noDoDateS.length; j++) {
                                if (toDateFormatByZone(thedate2, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                    thedate2 = new Date(thedate2.getTime() + base);//日期向前一天
                                }
                            }

                        }
                        //推算出的最终截止日期
                        $("#TASK_LS_DELIVERDATE" + pathValue).val(toDateFormatByZone(thedate2, "yyyy-MM-dd"));
                    }
                    // alertMsg("提示:计算标准日期成功,请注意复核!");
                    if (selflag == 3) {
                        //建库标准日期
                        for (var i = 0; i < dateNumber1; i++) {
                            var base = 1000 * 60 * 60 * 24;
                            thedate = new Date(thedate.getTime() + base);
                            for (var j = 0; j < noDoDateS.length; j++) {
                                if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                    thedate = new Date(thedate.getTime() + base);//日期向前一天
                                }
                            }
                        }
                        //推算出的最终截止日期  
                        $("#TASK_SEQ_EDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd")); 
                    }

                }
            }
        });

    }

    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }
    //方法库
    var doSetff = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/lib/task/setff/setff",
            title: "设置方法库..",
            currUrl: replacePathValue(pathValue)
        };
        openWindow(winOpts, { "IDS": arrIds, "PRODUCT_TYPE": $("#PRODUCT_TYPE" + pathValue).val() });
    }

    var doJG = function () {

        var paramsF = getJsonByForm("form", pathValue);
        //取出表单值
        var passFlag = paramsF["TASK_COMFIRM_RESULT"];
        var flagstr = "";
        var flagstr2 = "";
        if (passFlag == "通过") {
            flagstr = "已审核";
            flagstr2 = "已审核";

        } else if (passFlag == "退回") {

            flagstr = "草稿";
            flagstr2 = "调度退回";

        } else if (passFlag == "终止") {
            flagstr = "终止";
            flagstr2 = "已终止";
        }

        $("#DD_TASK_LS_STATUS" + pathValue).val(flagstr);
        $("#TASK_LS_STATUS" + pathValue).val(flagstr2);

    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();
        }
    }
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        refreshGrid();
                    }

                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }



    //正实验LSM
    var sendLSM = function () {
        debugger;
        //var TASK_LS_CDATE = $("#TASK_LS_CDATE"+pathValue).val();
        //var TASK_JF_EXCDAYS= $("#TASK_JF_EXCDAYS"+pathValue).val()*1;
        //var tjes=addDate(TASK_LS_CDATE,TASK_JF_EXCDAYS);
        var odlStatus = "";
        var newStatus = "等客户反馈";
        var p = getJsonByForm("form", pathValue);
        var params = {
            "jiraKey": p["LSM_KEY_P"],
            // "oldStatusName":odlStatus,
            // "statusName":newStatus,
            "updateField": {
                "customfield_10227": p["TASK_SEQ_EDATE"],	//：测序标准完成日期
                "customfield_12101": p["TASK_LS_LDATE"],	//建库标准结单日期
                //   "customfield_19108": { "value": p["PROJECT_OUT_TYPE"] },//实验生产类型
                "customfield_14201": p["TASK_TEST_DELIVERDATE"],//建库计划完成日期 
                //   "customfield_10226": p["TASK_LS_DELIVERDATE"],//实验标准交付日期
                "customfield_18820": toDateFormatByZone(p["TASK_LS_DELIVERDATE"], "yyyy-MM-ddTHH:mm:ss.000+0800"),//实验标准交付日期
                "customfield_13900": p["TASK_LS_DELIVERDATE"],//实验标准交付日期
            }
        };
        
        if ( p["PROJECT_OUT_TYPE"]  !== null &&  p["PROJECT_OUT_TYPE"]  !== undefined &&  p["PROJECT_OUT_TYPE"]  !== "") {
            params["updateField"]["customfield_19112"] = { "value":  p["PROJECT_OUT_TYPE"]  };//实验生产类型
        }  

        var m = mask(pathValue, "正在推送到jira,请稍等...");
        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                debugger;
                unmask(m);
                if (result["code"] > 0) {
                    alertMsg("提示:LSM推送成功!");
                } else {
                    alertMsg("提示:操作失败!");
                }
            },
            failed: function (res) {
                unmask(m);
                alertMsg("提示:提交保存失败", "error");
            }
        });

    }


    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "refreshGrid": refreshGrid,
        "submit": submit,
        "callBack": callBack,
        "doSetff": doSetff,
        "doJG": doJG,
    });

});