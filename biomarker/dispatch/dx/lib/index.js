$(document).ready(function() {

	var pathValue="biomarker-dispatch-dx-lib-index";
	var initData=function(){
		return {};
	}
 
	var gridNameDGrid;
	var gridNameD1Grid;
	
	var gridNameS=[];
	var IDS = [];
    var EXEIDS= [];
 
	var init=function(params){
	   
		var toolbar=getButtonTemplates(pathValue,[
			{name:"edit",target:"edit",title:"实验填写.."},
			{name:"edit",target:"doSop",title:"设置SOP"},
			{name:"excel",target:"importData1",title:"实验导入/模板"},
			{name:"edit",target:"doGo",title:"提交"},
			{name:"return",target:"doRerurn1",title:"退回"},
		]);
		var gridNameGridJson={
				
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read:{"query":"lib_pd_SHEET_list","objects":[["代谢建库"],["已接收","审核退回"]]},
			headerFilter:function(cols,i){},
			detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON={
					url: "system/jdbc/query/one/table",
					sort: "",
					 toolbar: null,
					height: 320,
					read:{"query":"queryTaskLibExMxResult-DX","objects":[],"search":{"EXE_TQQC_ID":[ROW_ID]}},                
				};
				var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
				gridNameS.push(subGrid_N);
			} 
		};
		
		gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
		
		init1();
	   }
	
	
	var init1=function(params){
	 var toolbar=getButtonTemplates(pathValue,[
			 {name:"return",target:"doRerurn2",title:"撤回"}
		   ]);
			var gridNameGridJson={
				url: "system/jdbc/query/one/table",
				sort: "",
				toolbar: toolbar,
				read:{"query":"lib_pd_SHEET_list","objects":[["代谢建库"],["建库提交","建库已审核"]]},
				headerFilter:function(cols,i){},
				detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
				detailInit: function (e) {
					var ROW_ID = e.data.ID;
					var subGrid_N_JSON={
						url: "system/jdbc/query/one/table",
						sort: "",
							toolbar: null,
						height: 320,
							read:{"query":"queryTaskLibExMxResult-DX","objects":[],"search":{"EXE_TQQC_ID":[ROW_ID]}},                
					};
					var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
				} 
			};	       
			gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
   }
	//结果填写
	 var edit=function(){
		 var arrIds=[];
		 for(var i=0;i<gridNameS.length;i++){
			 var arrSubID=getSelectData(gridNameS[i]);
			 if(arrSubID.length!=0){
				 arrIds=arrIds.concat(arrSubID);
			 }
		 }
		 if(arrIds.length==0){
			 alertMsg("请至少选择一条样本记录进行操作!");
			 return;
		 }
		 var winOpts={
 
			 url:"biomarker/dispatch/dx/lib/upset/upset",
			 title:"代谢建库.."
		  };
		 openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
	  }
	  
	//表格导入
   var importData1=function(componentId){
debugger;
		  var arrIds=[];
		  IDS = [];
		  EXEIDS = [];
 
			 for(var i=0;i<gridNameS.length;i++){
				 var arrSubID=getGridSelectData(gridNameS[i]);
					 if(arrSubID.length!=0){
						 arrIds=arrIds.concat(arrSubID);
					 }
			 }
				 
			 if(arrIds.length==0){
				 alertMsg("请至少选择一条样本记录进行操作!");
				 return;
			 }
		  var ids = [];
	 var exeid = [];
	 for (var i = 0; i < arrIds.length; i++) {
		 ids.push(arrIds[i]["ID"]);
		 if (exeid.indexOf(arrIds[i]["EXE_TQQC_ID"]) > -1) {} else {
			 exeid.push(arrIds[i]["EXE_TQQC_ID"]);
		 }

			 IDS.push(arrIds[i]["ID"]);
			 EXEIDS.push(arrIds[i]["EXE_TQQC_ID"]);
	 }
			 openComponent({
				 name:"导入数据",//组件名称
				 componentId:componentId,
				 params:{
					 template:function(p,n){
						 return exportAndImportData({
							 expKey:"A",
							 tableName:"BIO_LIB_INFO",
							 requestData:{
								 ajaxData:{"query":"queryTaskLibExMxResult-DX","size":5000,"objects":[],"search":{"ID":ids,"EXE_TQQC_ID":exeid}},
							 },
							 params:p,
							 name:n,
						 });
					 }
				 },
				 callBack:refreshGridS
			 });
		 }
	  var callBack=function(){
		 refreshGrid();
	  };
 
	  var refreshGrid=function(){
	   gridNameS=[];
		 if(gridNameDGrid){
			 gridNameDGrid.dataSource.read();
		 }
		 if(gridNameD1Grid){
			 gridNameD1Grid.dataSource.read();
		 }
	  }
 
 
	   //确认提交结果
	  var doGo=function(){
		  var arrIds=getSelectData(gridNameDGrid);
		 var arr=getGridSelectData(gridNameDGrid);
		  if(arr.length==0){
			  alertMsg("请至少选择一条主单记录进行操作!");
			  return ;
		  }
		  var objectupmain=[];
		  var ids = [];
		  var objectsop = [];
		  for(var i=0;i<arr.length;i++){
			 if (arr[i]["SOP_NAME"] == null) {
				 alertMsg("请先设置sop在提交");
			     return;
			 }
			  objectupmain.push({
				  "ID":arr[i]["ID"],
				  "EX_RE_STATUS":"建库提交"
			  });
			  ids.push(arr[i]["ID"]);
		  }
		  debugger;
                 var rows1 = [];
                 $.fn.ajaxPost({
                      ajaxUrl: "system/jdbc/query/one/table",
                      ajaxType: "post",
                     ajaxAsync: false,
                     ajaxData: { "query": "query_BIO_BZ_MATERIEL_SOP_cx_form", "objects": [ids] },
                     succeed: function (rs) {
                     //console.log(rs);				
                     rows1 = rs["rows"];
                   }
                 });

				for(var i = 0;i<rows1.length;i++){
					objectsop.push({
								"ID":rows1[i]["ID"],
								"SOP_REVIEW_FLAG":"待审核"

						});
				}
		  var params={"query":"queryTaskLibExMxResult","objects":[arrIds]};
		  $.fn.ajaxPost({
			  ajaxType:"post",
			  ajaxUrl:"system/jdbc/query/one/table",
			   ajaxData: params,
			  succeed:function(result){
				  if(result["code"]>0){
					 var rows=result["rows"];
					 var objectup=[];
					 var objectuptaskmx=[];
					 var objectaddqcmx=[];
					 var time=sysNowTimeFuncParams["sysNowTime"];
					 var username=getLimsUser()["name"];
						for(var i=0;i<rows.length;i++){
							var g=rows[i];
								  //更新记录
								  objectup.push({
									  "ID":g["ID"],//联联任务ID
									  "JK_TASKMX_STATUS":"结果已提交",//结果状态
									  "SYS_INSERTTIME":time,//实验结果时间
									  "SYS_MAN":username//实验操作人
								  });
						}
						
						 var url="system/jdbc/save/batch/table";
						 //文库实验结果
						 var paramsup={"tableName":"BIO_LIB_INFO","objects":objectup};
						 putAddOrUpdata(url,paramsup,"否","文库实验结果");
						 //更新SOP状态
					 	 var paramsusop = { "tableName": "BIO_BZ_MATERIEL_SOP_JL", "objects": objectsop };
						 putAddOrUpdata(url, paramsusop, "否", "更新SOP状态");
							
						 var paramsaddqcmx={"tableName":"EXE_TQQC_SHEET","objects":objectupmain};
					  putAddOrUpdata(url,paramsaddqcmx,"是","更新主单状态");
				  
									 
				  }else{
					  console.log(result);
				  }
			  }
		  });	 
	  }
	  
	  //退回
	  var doRerurn1=function(){
		  var g=getGridSelectData(gridNameDGrid);
		  var objectup=[];
		  for(var i=0;i<g.length;i++){
			  objectup.push({
						   "ID":g[i]["ID"],
						   "EX_RE_STATUS":"实验退回"
				   });
		  }
		 var urlsend="system/jdbc/save/batch/table";
		 var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
		  putAddOrUpdata(urlsend,paramsup,"是","提交");
	  }
	 //撤回
	  var doRerurn2=function(){
		  var g=getGridSelectData(gridNameD1Grid);
		  if(g.length==0){
				 alertMsg("请至少选择一条记录进行操作!");
				 return;
		   }
		  var objectup=[];
		  for(var i=0;i<g.length;i++){
					  if(g[i]["EX_RE_STATUS"]!="建库提交"){  
						  alertMsg("操作失败,所选记录存在“已审核”状态!");
						  return;
					  }else{
						  objectup.push({
							   "ID":g[i]["ID"],
							   "EX_RE_STATUS":"已接收"
						});
					  }
		  }
		 var urlsend="system/jdbc/save/batch/table";
		 var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
		  putAddOrUpdata(urlsend,paramsup,"是","提交");
	  }
	  
	  //批量执行插入
	  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
		  $.fn.ajaxPost({
			  ajaxType:"post",
			  ajaxUrl:urls,
			  ajaxData:inobjjson,
			  succeed:function(result){
				  if(result["code"]>0){
					  if(isDoCallBack=="是"){
						  alertMsg("提示:操作成功!");
						  refreshGrid();
					  }
				  }else{
					  alertMsg(errMsg+"操作失败!");
				  }
			  }
		  });
	  }
	 var doSop = function() {
		 var arrIds = getGridSelectData(gridNameDGrid);
		 if (arrIds.length == 0) {
			 alertMsg("请至少选择一条主单记录进行操作!");
			 return;
		 }
		 if (arrIds.length > 1) {
			 alertMsg("请只选择一条主单记录进行操作!");
			 return;
		 }
		 var winOpts = {
			 url: "biomarker/dispatch/dx/lib/doSop/doSop",
			 title: "设置SOP..",
		 };
		 openWindow(winOpts, {"EXE_TQQC_ID": arrIds[0]["ID"],"EX_DH_NO": arrIds[0]["EX_DH_NO"]});
	 }

	 var refreshGridS = function () {
		gridNameS = [];

		if (gridNameDGrid) {
			gridNameDGrid.dataSource.read();
		}
		if (gridNameD1Grid) {
			gridNameD1Grid.dataSource.read();
		}
                        //alertMsg(gridNameS.length > 0);
                       // alertMsg(IDS.length >0);
                        //alertMsg(EXEIDS.length >0);
                var rows1;
               $.fn.ajaxPost({
                    ajaxUrl: "system/jdbc/query/one/table",
                    ajaxType: "post",
                    ajaxAsync: false,
                    ajaxData: { "query": "queryTaskLibExMxResult-DX", "objects": [] ,"search":{"ID":IDS,"EXE_TQQC_ID":EXEIDS}},
                    succeed: function (rs) {
                    //console.log(rs);				
                    rows1 = rs["rows"];
                     }
                 });
debugger;
               for(var i =0;i<rows1.length;i++){
                     if(rows1[i]["JK_TASKMX_STDATE"] != ""&&rows1[i]["JK_TASKMX_STDATE"]  !=null &&rows1[i]["JK_TASKMX_ENDATE"] != ""&&rows1[i]["JK_TASKMX_ENDATE"] != null){
                           if(rows1[i]["JK_TASKMX_STDATE"] > rows1[i]["JK_TASKMX_ENDATE"]){
                                  alertMsg("建库实际开始日期不能大于建库实际结束日期");
                              }
                     }

                 }
                  
	}

   
	  funcPushs(pathValue,{
		  "initData":initData,
		  "init":init,
		  "edit":edit,
		  "doGo":doGo,
		  "refreshGrid":refreshGrid,
		  "callBack":callBack,
		  "doRerurn1":doRerurn1,
		  "doRerurn2":doRerurn2,
		  "importData1":importData1,
		  "doSop":doSop,
	  });
 });