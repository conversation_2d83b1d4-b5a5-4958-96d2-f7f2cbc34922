$(document).ready(function () {
	var pathValue = "biomarker-dispatch-dx-lib-doSop-addMaterial-addMaterial";
	var paramsValue;
	var gridNameGrid;
	/**
	 * 初始化数据-无参
	 */
	var initData = function () {
		return {};
	};
	/**
	 * 初始化-获取参数-并执行调用
	 * @param {*} params
	 */
	var init = function (params) {
		paramsValue = params;
		gridNameGrid_init(params);
	};

	/**
	 * 提取前样本
	 * @param {*} params 
	 */
	var gridNameGrid_init = function (params) {
		/**
		 * 列表-按钮-定义
		 */
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "add", target: "addFample", title: "确认选择" },
		]); //工具条
		//请求参数
		var mainSampleMxJson = {
			url: "system/jdbc/query/one/table", //请求json的路径-针对的查询
			sort: "", //排序
			height: fullh - 120,
			toolbar: toolbar,
			read: {
				query: "query_BIO_SC_MATERIEL",
				search: {"W_CODE_SOP":paramsValue["wCode"]}        
			},
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, mainSampleMxJson); //初始化表格的方法
	};

	/**
	 * 添加物料
	 * @returns 
	 */
	var addFample = function () {
		var g = getGridSelectData(gridNameGrid);
		if (g.length == 0) {
			alertMsg("请至少选择一条数据!");
			return;
		}
		var object = [];
		for (var i = 0; i < g.length; i++) {
			//更新状态
			object.push({
				ID:"",
				M_FY_NUMBER: g[i]["M_FY_NUMBER"],
				BIO_BZ_MATERIEL_SOP_ID: "", //主单ID
				M_IS_PR_ST: g[i]["M_IS_PR_ST"],
				M_LOSS: g[i]["M_LOSS"],
				M_STANDUSE: g[i]["M_STANDUSE"],
				M_TOTAL_USE: g[i]["M_TOTAL_USE"],
				W_CLASS: g[i]["W_CLASS"],
				W_CODE: g[i]["W_CODE"],
				W_NAME: g[i]["W_NAME"],
				W_NUMBER: g[i]["W_NUMBER"],
				W_REANAME: g[i]["W_REANAME"],
				W_SPECIF: g[i]["W_SPECIF"],
				W_UNIT: g[i]["W_UNIT"],
				EXE_TQQC_ID: "",
			});
		}
		funcExce(paramsValue["pPathValue"] + "addSub", object);
		funcExce(pathValue + "close"); //关闭页面
	};

    funcPushs(pathValue,{
        "init":init,
        "addFample":addFample
    });
});