$(document).ready(function () {
    var pathValue = "biomarker-dispatch-tq-taskdan-jira-jira";
    var paramsValue;
    var addOrUpdate = "";
    var PCHNumber = 0;
    var initData = function () {
        return {
            tableName: "JIRA_LSM_TQ"
        };
    }
    var init = function (params) {
        debugger;
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url, function (p, v) {
            queryCheckPCNumber(paramsValue["SAMPLE_BATCHNO"]);
            getJira(paramsValue["LSM_KEY"]);
            func();
        });

    }
    //暂保存
    var submit = function () {
        //表单校验
        var formJson = { formId: "form", pathValue: pathValue };
        var validator = $("#" + formJson.formId + formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
        if (!validator.validate()) {
            alertMsg("表单验证未通过", "error");
            return false;
        }

        Bttztxg();
        var m = mask(pathValue, "正在提交保存,请稍等...");
        var formparams = getJsonByForm("form", pathValue);
        var params = { "tableName": "JIRA_LSM_TQ", "objects": [formparams] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/save/batch/table",
            ajaxData: params,
            ajaxType: "post",
            succeed: function (result) {
                unmask(m);
                if (result["code"] > 0) {
                    funcExce(pathValue + "pageCallBack");
                    //funcExce(pathValue+"close");
                } else {
                    alertMsg("提交失败", "error");
                }
            },
            failed: function (res) {
                unmask(m);
                alertMsg("提交失败", "error");
            }
        });
    }

    //保存并推送
    var submitSendJira = function () {

        debugger;

        //表单校验
        var formJson = { formId: "form", pathValue: pathValue };
        var validator = $("#" + formJson.formId + formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
        if (!validator.validate()) {
            alertMsg("表单验证未通过", "error");
            return false;
        }
        Bttztxg();
        var m = mask(pathValue, "正在提交到jira,请稍等...");
        var formparams = getJsonByForm("form", pathValue);
        var params = { "tableName": "JIRA_LSM_TQ", "objects": [formparams] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/save/batch/table",
            ajaxData: params,
            ajaxType: "post",
            succeed: function (result) {
                unmask(m);
                if (result["code"] > 0) {
                    //推送jira
                    sendLSM();//LSM
                    sendPM();//项目
                } else {
                    alertMsg("提示:提交保存失败", "error");
                }
            },
            failed: function (res) {
                alertMsg("提示:提交保存失败", "error");
            }
        });
    }

    var sendLSM = function () {



        var odlStatus = "样品提取检测";
        var newStatus = "等客户反馈";
        var p = getJsonByForm("form", pathValue);
        var params;
        if (PCHNumber == 0) {
            params = {
                "jiraKey": p["LSM_KEY"],
                //  "oldStatusName":odlStatus,
                //  "statusName":newStatus,
                "updateField": {
                    "customfield_14300": p["CUSTOMFIELD_14300"],	//提取检测标准完成日期
                    //	"customfield_13254": p["CUSTOMFIELD_13254"],	//提取检测暂停开始日期
                    //	"customfield_13255": p["CUSTOMFIELD_13255"],	//提取检测暂停结束日期
                    // 	"customfield_14301": { "value": p["CUSTOMFIELD_14301"] },	//暂停原因     
                    "customfield_14500": p["TASK_JH_ENDDATE"],	//提取检测预计合格日期
                    "customfield_13237": p["CUSTOMFIELD_13237"],	//提取具体情况描述  
                    //	"customfield_13229": p["CUSTOMFIELD_13229"],	//样品合格情况
                    //	"customfield_13229": p["CUSTOMFIELD_13229"]	//样品合格情况
                }
            };
			if (p["CUSTOMFIELD_13229"] != null && p["CUSTOMFIELD_13229"] != "") {
				params["updateField"]["customfield_13229"] = p["CUSTOMFIELD_13229"];
			}
        } else if (PCHNumber == 1) {
            params = {
                "jiraKey": p["LSM_KEY"],
                //  "oldStatusName":odlStatus,
                //  "statusName":newStatus,
                "updateField": {
                    //"customfield_13227":p["CUSTOMFIELD_14300"],	//提取检测标准完成日期
                  //  "customfield_13254": p["CUSTOMFIELD_13254"],	//提取检测暂停开始日期
                    "customfield_14500": p["TASK_JH_ENDDATE"],	//提取检测预计合格日期
                    "customfield_13237": p["CUSTOMFIELD_13237"],	//提取具体情况描述  
                    //	"customfield_13229":p["CUSTOMFIELD_13229"],	//样品合格情况
                    //"customfield_13253": { "value": "提取异常，待运营反馈" }	     //提取状态
                }
            };
        } else if (PCHNumber >= 2) {
            params = {
                "jiraKey": p["LSM_KEY"],
                //  "oldStatusName":odlStatus,
                //  "statusName":newStatus,
                "updateField": {
                    //"customfield_13228":p["CUSTOMFIELD_14300"],	//提取检测标准完成日期
                   // "customfield_13254": p["CUSTOMFIELD_13254"],	//提取检测暂停开始日期
                    "customfield_14500": p["TASK_JH_ENDDATE"],	//提取检测预计合格日期
                    "customfield_13237": p["CUSTOMFIELD_13237"],	//提取具体情况描述  
                    //"customfield_13229":p["CUSTOMFIELD_13229"],	//样品合格情况
                    //"customfield_13253": { "value": "提取异常，待运营反馈" }	     //提取状态
                }
            };
        }
		if (p["CUSTOMFIELD_14301"] != null && p["CUSTOMFIELD_14301"] != "") {
			params["updateField"]["customfield_14301"] = { "value": p["CUSTOMFIELD_14301"] };
		}

		if (p["CUSTOMFIELD_13254"] != null && p["CUSTOMFIELD_13254"] != "") {
			params["updateField"]["customfield_13254"] = p["CUSTOMFIELD_13254"];
		}

		if (p["CUSTOMFIELD_13255"] != null && p["CUSTOMFIELD_13255"] != "") {
			params["updateField"]["customfield_13255"] = p["CUSTOMFIELD_13255"];
		}



        var m = mask(pathValue, "正在推送到jira,请稍等...");
        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                unmask(m);
                if (result["code"] > 0) {
                    alertMsg("提示:LSM推送成功!");
                } else {
                    alertMsg("提示:操作失败!");
                }
            },
            failed: function (res) {
                unmask(m);
                alertMsg("提示:提交保存失败", "error");
            }
        });

    }


    //项目
    var sendPM = function (m) {
        var odlStatus = "样品提取检测";
        var newStatus = "等客户反馈";
        var p = getJsonByForm("form", pathValue);
        var params = {
            "jiraKey": p["LSM_KEY_P"],
            // "oldStatusName":odlStatus,
            // "statusName":newStatus,
            "updateField": {
                "customfield_14500": p["CUSTOMFIELD_14500"],	//提取检测预计合格日期
                "customfield_14300": p["CUSTOMFIELD_14300"],	//提取检测标准完成日期
            }
        };
        var m = mask(pathValue, "正在推送到jira,请稍等...");
        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                unmask(m);
                if (result["code"] > 0) {
                    alertMsg("提示:LSM推送成功!");
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            },
            failed: function (res) {
                unmask(m);
                alertMsg("提示:提交保存失败", "error");
            }
        });
    }



    //LSM查询 
    var getJira = function (keyinfo) {
        if (keyinfo == "") {
            Pstatus = null;
            return;
        }
        var url = "http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
        var parmars = { "jiraKey": keyinfo, "fields": ["customfield_13237", "status", "customfield_14600"] };
        var inobjjson = { "url": url, "bodyParams": parmars };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (result.apiData[0].fields.customfield_13237 != null) {//存在值则累加
                        var o = $("#CUSTOMFIELD_13237" + pathValue).val();//存有的值
                        if (o != "" && o != result.apiData[0].fields.customfield_13237) {
                            $("#CUSTOMFIELD_13237" + pathValue).val(o + "\r" + result.apiData[0].fields.customfield_13237);
                        } else {
                            if (result.apiData[0].fields.customfield_13237) {
                                $("#CUSTOMFIELD_13237" + pathValue).val(result.apiData[0].fields.customfield_13237);
                            }
                        }

                    }


                } else {
                    alertMsg("提示:加载获取jira信息失败!");
                }
            }
        });

    }

    var queryCheckPCNumber = function (PCH) {
        debugger;
        var params = { "query": "checkPsTqCount", "objects": [[PCH]] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                debugger;
                var mynumber = 0;
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    if (rows.length > 0) {
                        var row = rows[0];
                        PCHNumber = row["CHECKNUMBER"];
                        $("#PCHNumber" + pathValue).val(PCHNumber);
                    }
                }

            }
        });
    }

    var func = function () {

        var p = getJsonByForm("form", pathValue);

        if (p["CUSTOMFIELD_13254"] == null || p["CUSTOMFIELD_13255"] == null || p["CUSTOMFIELD_13254"] == "" || p["CUSTOMFIELD_13255"] == "") {
            $("#CUSTOMFIELD_14300" + pathValue).val(paramsValue["CUSTOMFIELD_14300"]);
        } else {
            var date1 = new Date(p["CUSTOMFIELD_13254"]);
            var date2 = new Date(p["CUSTOMFIELD_13255"]);
            var date = (date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24) + 1;/*不用考虑闰年否*/
            doGetEndDateJK(date, paramsValue["CUSTOMFIELD_14300"])
        }

    }

    //推算截止日期
    var doGetEndDateJK = function (dateNumber, thedate) {
        var thedate = new Date(thedate);
        var params = "";

        params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期


        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    //建库标准日期
                    for (var i = 0; i < dateNumber; i++) {
                        var base = 1000 * 60 * 60 * 24;
                        thedate = new Date(thedate.getTime() + base);
                        for (var j = 0; j < noDoDateS.length; j++) {
                            if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                thedate = new Date(thedate.getTime() + base);//日期向前一天
                            }
                        }

                        //推算出的最终截止日期 
                        $("#CUSTOMFIELD_14300" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                    }
                }
            }
        });

    }


    //任务单状态修改
    var Bttztxg = function () {

        var p = getJsonByForm("form", pathValue);

        var obj = [];
        if (p["CUSTOMFIELD_13254"] != null && p["CUSTOMFIELD_13254"] != "" && (p["CUSTOMFIELD_13255"] == null || p["CUSTOMFIELD_13255"] == "")) {

            obj.push({ "ID": paramsValue["MAIN_ID"], "TASK_STATUS": "暂停中" });

        }
        if (p["CUSTOMFIELD_13254"] != null && p["CUSTOMFIELD_13254"] != "" && p["CUSTOMFIELD_13255"] != null && p["CUSTOMFIELD_13255"] != "") {

            if (paramsValue["TASK_TYPE"] == "提取") {

                obj.push({ "ID": paramsValue["MAIN_ID"], "TASK_STATUS": "提取中" });
            } else {
                obj.push({ "ID": paramsValue["MAIN_ID"], "TASK_STATUS": "检测中" });
            }

        }
        if (obj.length > 0) {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd1 = { "tableName": "BIO_TQ_TASK", "objects": obj };
            putAddOrUpdata(urlsend, paramsadd1, "否", "移除");
        }


    }

    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }


    funcPushs(pathValue, {

        "func": func,
        "init": init,
        "submit": submit,
        "submitSendJira": submitSendJira
    });

});