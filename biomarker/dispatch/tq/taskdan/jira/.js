$(document).ready(function() {
     var pathValue="biomarker-dispatch-tq-taskdan-jira-jira";
    var paramsValue;
     var addOrUpdate="";
    var initData=function(){
        return {
            tableName:"JIRA_LSM_TQ"
        };
    }
    var init=function(params){
    	paramsValue=params;
console.log("=================>"+params);
debugger;
    	getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url, function(viewModel, params){
debugger;
		if (params["LSM_KEY"]) {
			addOrUpdate="update";
		}
      });
     
   }
    
var submit=function(){
	if (addOrUpdate=="update") {
		submit2();
	}else {
		submit1();
	}
}
var submit1=function(){
	//表单校验
    var formJson = { formId:"form", pathValue:pathValue };
    var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    if ( !validator.validate() ) {
           alertMsg("表单验证未通过","error");
           return false;
    }
    var formparams = getJsonByForm("form", pathValue);
    var params = {"tableName":"JIRA_LSM_TQ","objects":[formparams]};
    $.fn.ajaxPost({
        ajaxUrl:"system/jdbc/save/one/table/objects",
        ajaxData: params,
           ajaxType:"post",
           succeed:function(res){
              // if(result["code"]>0){
                   alertMsg("提交成功","success",function(){
                       funcExce(pathValue+"close");
                   });
              // }else{
                 //  alertMsg("提交失败","error");
              // }
           },
           failed:function(res){
        	   alertMsg("提交失败","error");
           }
       });
}
var submit2=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"close");
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });