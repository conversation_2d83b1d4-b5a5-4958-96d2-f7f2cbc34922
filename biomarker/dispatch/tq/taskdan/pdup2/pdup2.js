$(document).ready(function() {
    var pathValue="biomarker-dispatch-tq-taskdan-pdup2-pdup2";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"EXE_TQQC_SHEET"
        };
    }
    var init=function(params){
        paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        $("#EX_TYPE"+pathValue).val(paramsValue["EX_TYPE"]);
        $("#EX_RE_STATUS"+pathValue).val("待审核");
    }

  var subUpData=function(){
	  	 var time=sysNowTimeFuncParams["sysNowTime"];
	     var username= $("#EX_MAN"+pathValue).val();
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                     //提交成功
                     	var ids=paramsValue["IDS"];
                     	var taskids=paramsValue["TASKIDS"];
                        var tqqcids=paramsValue["TQQCIDS"];
                        var codes=paramsValue["CODES"];
                    	var objectadd=[];
                    	var objectup=[];
                    	var objecttqqcup=[];
                    	var objectupmx=[];
                         for(var i=0;i < ids.length;i++){
                        		objectadd.push({
              	    	       		"TASK_TQ_ID":ids[i],//联联任务ID
              	    	       		"EXE_TQQC_ID":result["ID"],//关联执行单
              	    	       		"SAMPLE_CODE":codes[i],
              	    	       		"TQ_MAN":username,//提取实验员
              	    	       		"TQ_ST_DATE":time//开始提取日期
              	    	       	});
              	    	       	objectupmx.push({"ID":ids[i],"TASK_SM_STATUS":"已排单"});
                         }
                         for(var i=0;i < taskids.length;i++){
                        	 objectup.push({
           	    	       		"ID":taskids[i],
           	    	       		"TASK_STATUS":"提取中",
           	    	       		"SYS_MAN_L":time,
           	    	       		"SYS_INSERTTIME_L":username
           	    	       	});
                         }
                         for(var i=0;i < tqqcids.length;i++){
                        	 objecttqqcup.push({
           	    	       		"ID":tqqcids[i],
           	    	       		"CH_STATUS":"已处理"
           	    	       	});
                         }
                         //执行添加到文库
                         var urlsend="system/jdbc/save/batch/table";
         	        	 var paramsadd={"tableName":"BIO_DNA_RNA_QC","objects":objectadd};
         	        	 putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");

         	        	 var paramstqqcup={"tableName":"BIO_DNA_RNA_QC","objects":objecttqqcup};
         	        	 //putAddOrUpdata(urlsend,paramstqqcup,"否","更新");

         	        	var paramsup2={"tableName":"BIO_TQ_TASK_MX","objects":objectupmx};
	         	      	 putAddOrUpdata(urlsend,paramsup2,"否","提取中");

         	        	 var paramsup={"tableName":"BIO_TQ_TASK","objects":objectup};
	         	      	 putAddOrUpdata(urlsend,paramsup,"是","提取中");

                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });
    }
  
  //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提示:操作成功!");
             		funcExce(pathValue+"pageCallBack");
                    funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 
    var submit=function(){
       subUpData();
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });