$(document).ready(function() {

    var pathValue="biomarker-dispatch-tq-taskdan-updateman-updateman";

debugger;
	var paramsValue;
	var gridNameGrid;
        var gridNameS2;


    var init=function(params){
debugger;
	paramsValue=params;
        getInfo("form",pathValue,params);//传入id



    }
  var close1=function(){
	funcExce(pathValue + "pageCallBack"); //执行回调
	funcExce(pathValue + "close"); //关闭页面

   }
 
  var subUpData=function(){
	 var id=paramsValue["ID"];
	 var eman=paramsValue["EX_MAN"];
	 var exem=paramsValue["EX_EXECUTE_MODE"];

        var man=$("#EX_MAN"+pathValue).val(); 
        var eem=$("#EX_EXECUTE_MODE"+pathValue).val(); 
       
	 var object=[];

     if(eem=="PE"){

  object.push({"ID":id,"EX_MAN":man, "EX_EXECUTE_MODE":"PE" });
}else{

  object.push({"ID":id,"EX_MAN":man, "EX_EXECUTE_MODE":"实验员" });
}






       var newUrl="system/jdbc/save/batch/table";
  	var paramsadd={"tableName":"EXE_TQQC_SHEET","objects":object};
  	putAddOrUpdata(newUrl,paramsadd,"是","更新");  
 }
  






  //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提示:操作成功!");
                	refreshGrid();
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }

 
    var submit=function(){
       subUpData();
    }

	funcPushs(pathValue,{

		"init":init,
		"close1":close1,
		"submit":submit

	});
 
 });