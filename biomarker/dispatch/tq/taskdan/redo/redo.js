$(document).ready(function() {
   var pathValue="biomarker-dispatch-tq-taskdan-redo-redo";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var paramsValue;
   var gridNameGrid;
   var gridName1Grid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
	   paramsValue=params;

        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"add",title:"确认选择"},
            {name:"edit",target:"doend",title:"结单"}
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"query_YPD_BIO_TQ_TASK_MX_redo","objects":[["否"],["DNA提取"]]},
            headerFilter:function(cols,i){
                if(i){
                   
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        
        init1();
   }

   var init1=function(params){
	   
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"query_YPD_BIO_TQ_TASK_MX_redo","objects":[["是"],["DNA提取"]]},
            headerFilter:function(cols,i){
                if(i){
                    
                }
            }
        };
        gridName1Grid = initKendoGrid("#gridName1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
   }

  
   
    var add=function(){
    	
    	var g=getGridSelectData(gridNameGrid); 
        if(g.length==0){
            alertMsg("请至少选择一条数据进行添加样本操作!");
            return ;
        }
   
        var object=[];
        for(var i=0;i < g.length;i++){
        	
        	var newid=getRandomId();
        	object.push({
        		"ID":newid,//唯一标识
        		"TASK_ID":g[i]["TASK_ID"],//关联主单ID
        		"ISREDO":"是",//设为重下单记录
        		"SAMPLE_CODE":g[i]["SAMPLE_CODE"],//样本编号
        		"SAMPLE_BATCHNO":g[i]["SAMPLE_BATCHNO"],//样本批次号
        		"RECEIVE_STATUS":g[i]["RECEIVE_STATUS"],//组织样送样状态
        		"SAMPLE_DETAIL_TYPE":g[i]["SAMPLE_DETAIL_TYPE"],//样本类型
        		"SAMPLE_NAME":g[i]["SAMPLE_NAME"],//样品名称
        		"SPECIES":g[i]["SPECIES"],//物种
        		"SPECIES_ORIGIN":g[i]["SPECIES_ORIGIN"],//组织部位/微生物样本来源
        		"GET_SAMPLE_NUM":g[i]["GET_SAMPLE_NUM"],//送样个数
        		"IS_MIN_SAMPLE":g[i]["IS_MIN_SAMPLE"],//是否微小样本
        		"TEST_DATA_LEN":g[i]["TEST_DATA_LEN"],//填写数据量大小
        		"LATIN_NAME":g[i]["LATIN_NAME"],//物种拉丁文
        		"GEN_LENGTH":g[i]["GEN_LENGTH"],//基因组大小
        		"AMPLIFY_REGIONAL":g[i]["AMPLIFY_REGIONAL"],//扩增区域
        		"N_MXTYPE":g[i]["N_MXTYPE"],//混样拆分
        		"PRODUCT_CODE":g[i]["PRODUCT_CODE"],//产品编号
        		"PRODUCT_TYPE":g[i]["PRODUCT_TYPE"],//产品类型
        		"METHOD_PLAT":g[i]["METHOD_PLAT"],//平台
        		"EXTDR_METHOD":g[i]["EXTDR_METHOD"],//提取方法
        		"EXT_SAMPLENUM":g[i]["EXT_SAMPLENUM"],//提取管数
        		"CKDR_METHOD":g[i]["CKDR_METHOD"],//检测方法
        		"TQ_REMARKS":g[i]["TQ_REMARKS"],//备注
        		"SAMPLE_ID":g[i]["SAMPLE_ID"],//样本ID
        		"PROJECT_NO":g[i]["PROJECT_NO"],//项目编号
        		"PROJECT_NAME":g[i]["PROJECT_NAME"],//项目名称
        		"MEHOD_PLAT":g[i]["MEHOD_PLAT"],//提取流向
        		"MEHOD_JCFLOW":g[i]["MEHOD_JCFLOW"],//检测流向
        		"NEXT_STEP_NAME":g[i]["NEXT_STEP_NAME"],//质检下一步流向
        		"LIBRARY_FLOW":g[i]["LIBRARY_FLOW"],//建库流向
        		"SEQ_PLAT":g[i]["SEQ_PLAT"],//测序平台
        		"LIBRARY_CH_TYPE":g[i]["LIBRARY_CH_TYPE"],//混样常规建库类型
        		"LIBRARY_QJ_TYPE":g[i]["LIBRARY_QJ_TYPE"],//是否切胶
        		"LIBRARY_TYPE":g[i]["LIBRARY_TYPE"],//库类型-中文
        		"LIBRARY_TYPE_EN":g[i]["LIBRARY_TYPE_EN"]//文库类型-英文
        	});
       
        }
       var params={"tableName":"BIO_TQ_TASK_MX","objects":object};
       //插入任务明细记录
       var newUrl="system/jdbc/save/one/table/objects";
       putAddOrUpdata(newUrl,params,"是","重提任务");
       
    }
    
    var doend=function(){
    	
    	var g=getGridSelectData(gridNameGrid); 
        if(g.length==0){
            alertMsg("请至少选择一条数据进行添加样本操作!");
            return ;
        }
   
        var object=[];
        for(var i=0;i < g.length;i++){
        	object.push({
        		"ID":g[i]["ID"],
        		"ISEND":"是"
        	});
        }
        
        var params={"tableName":"BIO_TQ_TASK_MX","objects":object};
        //插入任务明细记录
        var newUrl="system/jdbc/save/batch/table";
        putAddOrUpdata(newUrl,params,"本界面","结单");
        
        
    }
 
  
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
        if(gridName1Grid){
        	gridName1Grid.dataSource.read();
        }
     }
     
    //批量执行插入
     var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:urls,
             ajaxData:inobjjson,
             succeed:function(result){
                 if(result["code"]>0){
                	 if(isDoCallBack=="是"){
                         alertMsg("提交成功!");
                         funcExce(pathValue+"pageCallBack","1");
                         console.log(result);
                         funcExce(pathValue+"close");
                	 }else if(isDoCallBack=="本界面"){
                		 alertMsg("操作成功!");
                		 refreshGrid();
                	 }
                 }else{
                	 alertMsg(errMsg+"操作失败!");
                 }
             }
         });
     }
     
 function getRandomId() {
  	   return (('REDO-TQ-Task-' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
 };

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,
         "add":add,
         "doend":doend,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});