$(document).ready(function() {
   var pathValue="biomarker-dispatch-tq-taskdan-toex-toex";
   var paramsValue;
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
	   paramsValue=params;
	   var toolbar=getButtonTemplates(pathValue,[
           {name:"edit",target:"add",title:"确认选择"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"query_DD_OK_EXE_TQQC_SHEET_list","objects":[["DNA提取","DNA纯化"],["待审核","接收退回"]]},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"query_YPD_BIO_TQ_TASK_MX_list","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }             
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
  }
  
   var add=function(){
	   var myids=getGridSelectData(gridNameGrid);
       if(myids.length!=1){
          	alertMsg("请选择一条记录进行操作!");
          	return;
        }
        if( myids[0]["NUCLEIC_NO"] != null && myids[0]["NUCLEIC_NO"] != undefined && myids[0]["NUCLEIC_NO"] != ""  ){

          	alertMsg("执行单"+ myids[0]["EX_DH_NO"] +"已经生成核酸板孔号，禁止添加样本");
          	return;
        }
       var arrIds=[];
       var exman=[];
       for(var i=0;i<myids.length;i++){
    	   arrIds.push(myids[i]["ID"]);
    	   exman.push(myids[i]["EX_MAN"]);
       }
       var objectadd=[];
       var ids=paramsValue["IDS"]; 
	   var codes=paramsValue["CODES"];                     	                 	
  	   var time=sysNowTimeFuncParams["sysNowTime"];
  	   var username=getLimsUser()["name"];
           var tqffs=paramsValue["TQFFS"];      
  	   
       //检查执行单是否存在
       
       var params={"query":"doCheckExSmInfo","objects":[arrIds,codes]};
	   $.fn.ajaxPost({
	        ajaxUrl:"system/jdbc/query/one/table",
	        ajaxType: "post",
	        ajaxData: params,
	        succeed:function(result){
	        	if(result["code"]>0){
	        		var rows=result["rows"];
	        		var s="";
                    	        var objectupmx=[];
	        		for(var i=0;i<rows.length;i++){
	  	        		var row=rows[i];
	  	        		if(i==0){
	  	        			s=row["SAMPLE_CODE"];
	  	        		}else{
	  	        			s+=","+row["SAMPLE_CODE"];
	  	        		}
	        		}
	  	        	if(s!=""){
	  	        		 alertMsg("提示:操作失败!存在样品编号(“"+s+"”)重复!");
	  	        	}else{
		  	        	for(var i=0;i<arrIds.length;i++){
		  	       	  	   for(var j=0;j < ids.length;j++){
		  	       	       		objectadd.push({
		  	       	       			"TASK_TQ_ID":ids[j],//联联任务ID
		  	       		       		"EXE_TQQC_ID":arrIds[i],//关联执行单
		  	       		       		"SAMPLE_CODE":codes[j],
              	    	       		                        "EXTDR_METHOD":tqffs[i],//提取方法
		  	       		       		"TQ_MAN":exman[i],//提取实验员
              	    	       		                        //"DJC_MAN":exman[i],//检测实验员
		  	       		       		"TQ_ST_DATE":time//开始提取日期
		  	       		    	});
   		  	       		    	objectupmx.push({"ID":ids[j],"TASK_SM_STATUS":"提取中"});
		  	       	        }
		  	              	}
		  	             var urlsend="system/jdbc/save/batch/table";

         	        	var paramsup2={"tableName":"BIO_TQ_TASK_MX","objects":objectupmx};
	         	      	 putAddOrUpdata(urlsend,paramsup2,"否","提取中");

	         	      	          var objectup=[];
	         	      	           objectup.push({
                        	 			"ID":paramsValue["TASK_ID"],
                        	 			"TASK_STATUS":"提取中",
           	    	       		               "SYS_MAN_L":username,
           	    	       		              "SYS_INSERTTIME_L":time
							 });
						

         	        	var paramsup3={"tableName":"BIO_TQ_TASK","objects":objectup};
	         	      	 putAddOrUpdata(urlsend,paramsup3,"否","提取中");

		  	          	 var paramsadd={"tableName":"BIO_DNA_RNA_QC","objects":objectadd};
		  	          	 putAddOrUpdata(urlsend,paramsadd,"是","推入下一步实验任务");	  	        		
	  	        	}
	        	}
	        }
	   });
    } 
    
   //批量执行插入
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		 refreshGrid();
              		funcExce(pathValue+"pageCallBack");
                    funcExce(pathValue+"close");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
  
   
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "add":add,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});