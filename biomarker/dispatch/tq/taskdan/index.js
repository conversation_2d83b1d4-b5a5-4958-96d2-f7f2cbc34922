$(document).ready(function () { 
	var pathValue = "biomarker-dispatch-tq-taskdan-index";
	var initData = function () {
		return {};
	}
	var gridNameDGrid;
	var gridNameD1Grid;
	var gridNameD2Grid;
	var gridNameD3Grid;
	var gridNameD4Grid;
	var gridNameD5Grid;
	var gridNameD6Grid;
	var gridNameD7Grid;
	var gridNameD8Grid;
	var gridNameD9Grid;
	var gridNameD10Grid;
	var gridNameS = [];
	var gridNameS2 = [];
	var gridNameS3 = [];
	var gridNameS4 = [];
	debugger;
	var init = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "edit", title: "生成执行单.." },
			{ name: "edit", target: "upsmStatus", title: "修改样本状态.." },
			{ name: "edit", target: "upsmFF", title: "修改提取方法.." },
			{ name: "edit", target: "addToEx", title: "追加样本到执行单.." },
			{ name: "edit", target: "doTaskStatus", title: "任务单状态修改.." },
			{ name: "edit", target: "doTaskP", title: "任务单补充.." },
			{ name: "edit", target: "setjira", title: "JIRA推送-暂停" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_BIO_TQ_TASK2_list", "objects": [["暂停中", "已审核", "提取中", "待提取", "待检测", "检测中", "审核中", "已完成"], ["DNA提取"]], "search": { "DD_TASK_STATUS": ["已审核"] } },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_PD_BIO_TQ_TASK_MX2_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS.push(subGrid_N);
			}
		};
		gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
		init1();
		init2();
		init3();
		init4();
		init5();
		init6();
		// init7();
		init8();
		init9();
		init10();

	}
	//纯化待排单
	var init1 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "edit2", title: "生成执行单" },
			{ name: "edit", target: "addToEx2", title: "追加样本到执行单.." },
			{ name: "edit", target: "upsmFF1", title: "修改提取方法.." },
			{ name: "ok", target: "doTochTest", title: "暂不处理" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_PD_BIO_TQ_TASK_MX2_list_ch", "objects": [["DNA提取"], ["待处理"]] },
		};
		gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
		gridNameS4.push(gridNameD1Grid);
	}
	//待审核
	var init2 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "doGenNo", title: "生成核酸编号" },
			{ name: "edit", target: "doNucleic", title: "生成核酸板孔" },
			{ name: "edit", target: "upsmFF2", title: "修改提取方法.." },
			{ name: "edit", target: "upsmFF3", title: "修改提取实验员" },
			{ name: "edit", target: "doExeM", title: "修改执行单.." },
			// { name: "ok", target: "doOK2", title: "拆单分派.." },
			{ name: "ok", target: "doOK", title: "提交" },
			{ name: "delete", target: "remove", title: "移除任务明细" },
			{ name: "delete", target: "doDelete", title: "删除执行单" },
			{ name: "edit", target: "doUpdate", title: "修改实验员" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", "objects": [["DNA提取", "DNA纯化"], ["待审核", "接收退回", "实验退回"]] },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: [],
					height: 320,
					read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS2.push(subGrid_N);
			}
		};
		gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);
	}
	//已处理
	var init3 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "return", target: "doReturn", title: "撤回" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", "objects": [["DNA提取", "DNA纯化"], ["待接收", "已接收", "提取结果提交", "检测结果待审核", "检测结果已审核"]], "search": { "EX_EXECUTE_MODE": "实验员" } },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
			}
		};
		gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);
	}
	//已结单
	var init4 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "return", target: "doReturn2", title: "移至待审核" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_BIO_TQ_TASK2_list", "objects": [["结单", "暂停", "未提取", "终止"], ["DNA提取"]] },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_PD_BIO_TQ_TASK_MX2_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
			}
		};
		gridNameD4Grid = initKendoGrid("#gridNameD4Grid" + pathValue, gridNameGridJson);

	}
	//纯化已处理
	var init5 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "return", target: "doReturn3", title: "移至纯化待排单" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_PD_BIO_TQ_TASK_MX2_list_ch", "objects": [["DNA提取"], ["已处理", "暂停"]] },
			headerFilter: function (cols, i) { },
		};
		gridNameD5Grid = initKendoGrid("#gridNameD5Grid" + pathValue, gridNameGridJson);
	}
	//  待排任务
	var init6 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "editA", title: "智能排单" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_BIO_TQ_TASK_MX_RNA_list", "objects": [["已审核", "提取中", "待提取", "待检测", "检测中", "审核中", "已完成"], ["DNA提取"]] },
		};
		gridNameD6Grid = initKendoGrid("#gridNameD6Grid" + pathValue, gridNameGridJson);
	}
	// 预处理
	var init7 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "checkSample", title: "核验" },
			{ name: "edit", target: "submit", title: "提交" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", "objects": [["DNA提取", "DNA纯化"], ["预处理"]] },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_YPD_BIO_TQ_TASK_MX_list_Check", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				//      gridNameS7.push(subGrid_N);
			}
		};
		gridNameD7Grid = initKendoGrid("#gridNameD7Grid" + pathValue, gridNameGridJson);
	}
	//PE任务待处理
	var init8 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "pesubmit", title: "提交" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", "objects": [["DNA提取", "DNA纯化"], ["待接收", "已接收", "实验退回", "提取结果提交", "检测结果待审核", "检测结果已审核"]], "search": { "EX_EXECUTE_MODE": "PE" } },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
			}
		};
		gridNameD8Grid = initKendoGrid("#gridNameD8Grid" + pathValue, gridNameGridJson);
	}
	//PE任务待处理
	var init9 = function (params) {

		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "pesubmit1", title: "提交" },
			{ name: "edit", target: "collection", title: "采集" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", "objects": [["DNA提取", "DNA纯化"], ["PE执行状态"]], "search": { "EX_EXECUTE_MODE": "PE" } },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
			}
		};
		gridNameD9Grid = initKendoGrid("#gridNameD9Grid" + pathValue, gridNameGridJson);
	}
	//异常任务
	var init10 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "editR", title: "生成执行单" },
			{ name: "edit", target: "addToExRe", title: "追加任务到执行单" },
			{ name: "edit", target: "editZZ", title: "终止" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "query_YC_BIO_TQ_TASK_MX_list",
				"objects": [["重提重建"], ["DNA提取"]]
			},
		};
		gridNameD10Grid = initKendoGrid("#gridNameD10Grid" + pathValue, gridNameGridJson);
	}

	//排单
	var edit = function () {debugger
		var arrIds = [];
		for (var i = 0; i < gridNameS.length; i++) {
			var arrSubID = getGridSelectData(gridNameS[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}

		//判断类型是否全部分同一类,并取出形成类型单
		//MEHOD_PLAT 提取流向
		//MEHOD_JCFLOW 检测流向
		var g = arrIds;
		var a = "";
		var b = "";
		var a1 = "";
		var b1 = "";
		var code = [];
		var faskdate = "";


		for (var i = 0; i < g.length; i++) {
			if (g[i]["TASK_SM_STATUS"] == "已完成" || g[i]["TASK_SM_STATUS"] == "提取中" || g[i]["TASK_SM_STATUS"] == "已排单") {
				alertMsg("提示:样品“" + g[i]["SAMPLE_CODE"] + g[i]["TASK_SM_STATUS"] + "“");
				return;
			}
			if (i == 0) {
				a = g[i]["MEHOD_PLAT"];
				b = g[i]["MEHOD_PLAT"];
				a1 = g[i]["MEHOD_JCFLOW"];
				b1 = g[i]["MEHOD_JCFLOW"];
				code.push(g[i]["SAMPLE_CODE"]);
				faskdate = g[i]["TASK_FIRSTDATE"];
			} else {
				a = g[i - 1]["MEHOD_PLAT"];
				b = g[i]["MEHOD_PLAT"];
				a1 = g[i - 1]["MEHOD_JCFLOW"];
				b1 = g[i]["MEHOD_JCFLOW"];

				if (code.indexOf(g[i]["SAMPLE_CODE"]) > -1) {//同一个执行单不允许重复
					if (b1 != "DNA检测-MCD检测") {
						alertMsg("提示:存在所选编号“" + g[i]["SAMPLE_CODE"] + "重复!”");
						return;
					}
				} else {
					code.push(g[i]["SAMPLE_CODE"]);
				}
			}
			if (a != b) {
				alertMsg("存在所选记录提取类型“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
				return;
			}
			if (a1 != b1) {
				alertMsg("存在所选记录检测类型“<font color=#ff0000>" + a1 + "--" + b1 + "</font>”前后不一致!");
				return;
			}
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/pdup/pdup",
			title: a
		};
		var ids = [];
		var taskids = [];
		var codes = [];
		var tqffs = [];
		var TASK_PLAT=arrIds[0]["TASK_PLAT"];
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			codes.push(arrIds[i]["SAMPLE_CODE"]);
			tqffs.push(arrIds[i]["EXTDR_METHOD"]);
			if (taskids.indexOf(arrIds[i]["TASK_ID"]) < 0) {
				taskids.push(arrIds[i]["TASK_ID"]);//主单ID
			}
			if (TASK_PLAT.indexOf(arrIds[i]["TASK_PLAT"]) < 0) {
				TASK_PLAT=TASK_PLAT+";"+arrIds[i]["TASK_PLAT"];
			}
		}
		openWindow(winOpts, { "TASK_PLAT": TASK_PLAT, "MEHOD_TJPLAT":arrIds[0]["MEHOD_TJPLAT"], "IDS": ids, "TASKIDS": taskids, "CODES": codes, "EX_TYPE": "DNA提取", "EX_TYPE_LB": a1, "FASKDATE": faskdate, "TQFFS": tqffs });
	}

	var edit2 = function () {
		var arrIds = getGridSelectData(gridNameD1Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}

		//判断类型是否全部分同一类,并取出形成类型单
		//MEHOD_PLAT 提取流向
		//MEHOD_JCFLOW 检测流向
		var g = arrIds;
		var a = "";
		var b = "";
		var a1 = "";
		var b1 = "";
		var code = [];

		for (var i = 0; i < g.length; i++) {
			if (i == 0) {
				a = g[i]["MEHOD_PLAT"];
				b = g[i]["MEHOD_PLAT"];
				a1 = g[i]["MEHOD_JCFLOW"];
				b1 = g[i]["MEHOD_JCFLOW"];
				code.push(g[i]["SAMPLE_CODE"]);
			} else {
				a = g[i - 1]["MEHOD_PLAT"];
				b = g[i]["MEHOD_PLAT"];
				a1 = g[i - 1]["MEHOD_JCFLOW"];
				b1 = g[i]["MEHOD_JCFLOW"];
				if (code.indexOf(g[i]["SAMPLE_CODE"]) > -1) {//同一个执行单不允许重复
					alertMsg("提示:存在所选编号“" + g[i]["SAMPLE_CODE"] + "重复!”");
					return;
				} else {
					code.push(g[i]["SAMPLE_CODE"]);
				}
			}
			if (a != b) {
				alertMsg("存在所选记录提取类型“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
				return;
			}
			if (a1 != b1) {
				alertMsg("存在所选记录检测类型“<font color=#ff0000>" + a1 + "--" + b1 + "</font>”前后不一致!");
				return;
			}
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/pdup2/pdup2",
			title: a
		};
		var ids = [];
		var taskids = [];
		var tqqcids = [];
		var codes = [];
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			tqqcids.push(arrIds[i]["TQQCID"]);
			codes.push(arrIds[i]["SAMPLE_CODE"]);
			if (taskids.indexOf(arrIds[i]["TASK_ID"]) < 0) {
				taskids.push(arrIds[i]["TASK_ID"]);//主单ID
			}
		}
		openWindow(winOpts, { "IDS": ids, "TASKIDS": taskids, "CODES": codes, "TQQCIDS": tqqcids, "EX_TYPE": "DNA纯化", "EX_TYPE_LB": a1 });
	}
	//审核提交
	 var doOK = function () { 
		debugger
		var IDS = getSelectData(gridNameD2Grid);
		var inobjjson = {
			"IDS": IDS,
			"OPERATE": "审核提交",
		}
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "function/system/settlement/tqScheduling/executionSubmission",
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] == 1) {
					alertMsg("提示:操作成功!");
					refreshGrid();
				} else {
					alertMsg(result["apiData"]["msg"]);
					refreshGrid();
					}
				}
			})
	// 	var arrIds = getSelectData(gridNameD2Grid);
	// 	if (arrIds.length == 0) {
	// 		alertMsg("请至少选择一条记录进行操作!");
	// 		return;
	// 	}
	// 	//校验核酸编号是否为空
	// 	var s = "";
	// 	var params = { "query": "doCheckBioCOdeIsPass", "objects": [arrIds] };
	// 	$.fn.ajaxPost({
	// 		ajaxUrl: "system/jdbc/query/one/table",
	// 		ajaxType: "post",
	// 		ajaxData: params,
	// 		succeed: function (result) {
	// 			if (result["code"] > 0) {
	// 				var objectup = [];
	// 				var rows = result["rows"];
	// 				for (var i = 0; i < rows.length; i++) {
	// 					var row = rows[i];
	// 					if (row["SAMPLE_GENNO"] == "" || row["SAMPLE_GENNO"] == null) {
	// 						if (s == "") {
	// 							s = row["EX_DH_NO"];
	// 						}
	// 					}
	// 				}
	// 				if (s != "") {
	// 					alertMsg("提示:单号“" + s + "”存在核酸编号为空!");
	// 					return;
	// 				}
	// 				var objectup = [];
	// 				for (var i = 0; i < arrIds.length; i++) {
	// 					var time = sysNowTimeFuncParams["sysNowTime"];
	// 					var username = getLimsUser()["name"];
	// 					objectup.push({
	// 						"ID": arrIds[i],//联联任务ID
	// 						"EX_RE_STATUS": "待接收"
	// 					});
	// 				}
	// 				var urlsend = "system/jdbc/save/batch/table";
	// 				var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
	// 				putAddOrUpdata(urlsend, paramsup, "是", "提交");
	// 				doRequeDoUpTaskmxSmStatus(arrIds, "提取中");

	// 			}
	// 		}
	// 	});

	}
	//修改实验员 
	var doUpdate = function () {
		var arrIds = getGridSelectData(gridNameD2Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行修改!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行修改操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/updateman/updateman",
			title: "自动排单明细.."
		};
		openWindow(winOpts, { "ID": arrIds[0]["ID"], "EX_MAN": arrIds[0]["EX_MAN"] });//传递
	}

	//自动化排单
	var editA = function () {

		var gridData = getGridSelectData(gridNameD6Grid);
		debugger;
		if (gridData.length == 0) {
			alertMsg("至少选择一个样本");
			return;

		}

		if (gridData.length > 999) {
			alertMsg("单次只能排小于1000个样本！");
			return;
		}


		var params = { "ids": [], "keys": [] };

		for (var j = 0; j < gridData.length; j++) {
			var id = gridData[j]["ID"];
			var lfo2 = gridData[j]["TASK_TYPE_LB"]; //工序
			var wor2 = gridData[j]["MEHOD_TJPLAT"]; //样品执行组
			var met2 = gridData[j]["EXTDR_METHOD"]; //样品执行方法
			var mp2 = gridData[j]["MEHOD_PLAT"]; //提取流向
			var mj2 = gridData[j]["MEHOD_JCFLOW"]; //检测流向


			params.ids.push(id);
			if (params.keys.indexOf(lfo2 + "-" + wor2 + "-" + met2 + "-" + mp2 + "-" + mj2) < 0) {
				params.keys.push(lfo2 + "-" + wor2 + "-" + met2 + "-" + mp2 + "-" + mj2);
				params[lfo2 + "-" + wor2 + "-" + met2 + "-" + mp2 + "-" + mj2] = {
					lfo: lfo2,
					met: met2,
					wor: wor2,
					mp: mp2,
					mj: mj2,
					ids: []
				};

			}
			params[lfo2 + "-" + wor2 + "-" + met2 + "-" + mp2 + "-" + mj2].ids.push(id);

		}

		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/automate/automate",
			title: "自动排单明细.."
		};
		openWindow(winOpts, params);//传递

	}

	//PE任务待处理提交
	var pesubmit = function () {
		var arrIds = getGridSelectData(gridNameD8Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行提交!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行提交!");
			return;
		}
		if (arrIds[0]["PLATE_CODE"] == null) {

			alertMsg("执行单" + arrIds[0]["EX_DH_NO"] + "尚未分配板孔号，不能提交");
			return;
		}
		var PEtoken;
		var PEVariable = { "ClientName": PE_ClientName, ClientPwd: ClientPwd };
		// var inobjjson={"url":PE_URL + "api/clientInfo/login","PEVariable":PEVariable}
		// $.fn.ajaxPost({
		//     ajaxUrl: "/berry/automation/rowsingle/rowsingle",
		//     ajaxType: "post",
		//     ajaxAsync: false,
		//     ajaxData:inobjjson,
		//     succeed: function (rs) {
		//         PEtoken = rs.apiData.result.token;
		//     }
		// });
		var sample;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "queryTaskLibExMx", "objects": [[arrIds[0]["ID"]]] },
			succeed: function (rs) {
				sample = rs.rows;             //样品
			}
		});
		var PEsamples = [];
		for (var j = 0; j < sample.length; j++) {
			PEsamples.push({
				"Well": sample[j]["PLATE_WELL"],
				"SampleNo": sample[j]["SAMPLE_CODE"],
				"LibraryType": sample[j]["LIBRARY_TYPE_EN"]
			});
		}
		time = sysNowTimeFuncParams["sysNowTime"];
		var PEVariable = {
			"TimeStamp": time,
			"Token": PEtoken,
			"ClientId": "",
			"Cmd": "RNASeq",
			"RQData": {
				"TaskNo": arrIds[0]["EX_DH_NO"],
				"BarCode": arrIds[0]["PLATE_CODE"],
				"Samples": PEsamples,
				"IndexBarCode": arrIds[0]["PLATE_CODE"]
			}
		};
		var inobjjson = { "url": PE_URL + "api/order/create", "PEVariable": PEVariable }
		var RValue;
		// $.fn.ajaxPost({
		//     ajaxUrl: "/berry/automation/rowsingle/rowsingle",
		//     ajaxType: "post",
		//     ajaxAsync: false,
		//     ajaxData:inobjjson,
		//     succeed: function (rs) {
		//         RValue = rs;
		//     }
		// });
		// if(!RValue.apiData.success){
		//     alertMsg(RValue.apiData.msg);
		//     return;
		// }
		var objectSheet = [];
		objectSheet.push({
			"ID": arrIds[0]["ID"],//id
			"EX_RE_STATUS": "PE执行状态"       //状态
		});
		var urlsend = "system/jdbc/save/batch/table";
		var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
		putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
	}
	//样品核验 
	var checkSample = function () {
		var arrIds = getGridSelectData(gridNameD7Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行核验");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行核验操作!");
			return;
		}

		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/check/check",
			title: "自动排单明细.."
		};
		openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递
	}
	//撤回
	var doReturn = function () {
		var g = getGridSelectData(gridNameD3Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		var arrIds = [];
		for (var i = 0; i < g.length; i++) {
			if (g[i]["EX_RE_STATUS"] != "待接收") {
				alertMsg("操作失败,所选记录存在非“待接收”状态!");
				return;
			} else {
				objectup.push({
					"ID": g[i]["ID"],
					"EX_RE_STATUS": "待审核"
				});
				arrIds.push(g[i]["ID"]);
			}
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
		doRequeDoUpTaskmxSmStatus(arrIds, "待提取");
	}
	//任务单-对应执行单下明细状态修改BIO_DNA_RNA_QC
	var doRequeDoUpTaskmxSmStatus = function (mainExIds, status) {
		var time = sysNowTimeFuncParams["sysNowTime"];
		var username = getLimsUser()["name"];
		var params = { "query": "doRequeDoUpTaskmxSmStatus", "objects": [mainExIds, mainExIds] };
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var objectup = [];
					var objectupmain = [];
					debugger;
					var rows = result["rows"];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];

						if (objectupmain.indexOf(row["TASK_ID"]) < 0) {
							objectupmain.push(row["TASK_ID"]);
						}
						//更新记录---明细
						objectup.push({
							"ID": row["TASKMXID"],
							"TASK_SM_STATUS": status
						});
					}

					var urlsend = "system/jdbc/save/batch/table";
					var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objectup };
					putAddOrUpdata(urlsend, paramsup, "否", "同步更新任务明细");

					var urlmain = "system/jdbc/update/one/table/where";
					var paramsupmain = {
						"tableName": "BIO_TQ_TASK",
						"TASK_STATUS": status,
						"SYS_MAN_L": username,
						"SYS_INSERTTIME_L": time,
						"where": { "ID": objectupmain }
					};
					putAddOrUpdata(urlmain, paramsupmain, "否", "同步更新任务");

				}
			},
			failed: function (result) {
				alertMsg("提示:操作异常!", "error");
			}
		});
	}

	//移至待审核
	var doReturn2 = function () {
		var g = getGridSelectData(gridNameD4Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		for (var i = 0; i < g.length; i++) {
			if (g[i]["TASK_STATUS"] == "暂停" || g[i]["TASK_STATUS"] == "未提取" || g[i]["TASK_STATUS"] == "结单") {
				objectup.push({
					"ID": g[i]["ID"],
					"TASK_STATUS": "已审核"
				});
			} else {
				alertMsg("操作失败,只有“<font color=#ff0000>暂停、未提取</font>”状态方允许操作!");
				return;
			}
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
	}
	//移至纯化待排
	var doReturn3 = function () {
		var g = getGridSelectData(gridNameD5Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		for (var i = 0; i < g.length; i++) {
			objectup.push({
				"ID": g[i]["TQQCID"],
				"CH_STATUS": "待处理"
			});
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "BIO_DNA_RNA_QC", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
	}
	//移暂不入理
	var doTochTest = function () {
		var g = getGridSelectData(gridNameD1Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		for (var i = 0; i < g.length; i++) {
			objectup.push({
				"ID": g[i]["TQQCID"],
				"CH_STATUS": "暂停"
			});
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "BIO_DNA_RNA_QC", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
	}
	//样本状态修改
	var upsmStatus = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS.length; i++) {
			var arrSubID = getSelectData(gridNameS[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/upsmstatus/upsmstatus",
			title: "修改样本状态.."
		};
		openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
	}
	//修改方法
	var upsmFF = function () {
		debugger;
		var arrIds = [];
		for (var i = 0; i < gridNameS.length; i++) {
			var arrSubID = getSelectData(gridNameS[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/upsmff/upsmff",
			title: "修改提取方法.."
		};
		openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
	}
	//修改方法1
	var upsmFF1 = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS4.length; i++) {
			var arrSubID = getSelectData(gridNameS4[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/upsmff/upsmff",
			title: "修改提取方法.."
		};
		openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
	}
	//修改方法2
	var upsmFF2 = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS2.length; i++) {
			var arrSubID = getSelectData(gridNameS2[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/upsmff/upsmff",
			title: "修改提取方法.."
		};
		openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
	}


	//修改方法3  修改提取实验员
	var upsmFF3 = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS2.length; i++) {
			var arrSubID = getGridSelectData(gridNameS2[i]);  //获取全部getGrid
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var ids = [];
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["LIBID"]); // 关联ID
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/upsmff3/upsmff3",
			title: "修改提取实验员.."
		};
		openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
	}


	//修改执行单
	var doExeM = function () {

		var arrIds = getSelectData(gridNameD2Grid);
		if (arrIds.length != 1) {
			alertMsg("请选择一条记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/doExeM/doExeM",
			title: "修改执行单.."
		};
		openWindow(winOpts, { "ID": arrIds[0] });
	}


	//追加
	var addToEx = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS.length; i++) {
			var arrSubID = getGridSelectData(gridNameS[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}

		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/toex/toex",
			title: "追加样本到执行单.."
		};
		var ids = [];
		var codes = [];
		var tqffs = [];
		var TASK_ID = "";
		for (var i = 0; i < arrIds.length; i++) {
			if (arrIds[i]["TASK_SM_STATUS"] == "检测完成" || arrIds[i]["TASK_SM_STATUS"] == "提取中" || arrIds[i]["TASK_SM_STATUS"] == "已排单") {
				alertMsg("提示:样品“" + arrIds[i]["SAMPLE_CODE"] + arrIds[i]["TASK_SM_STATUS"] + "”");
				return;
			}
			TASK_ID = arrIds[i]["TASK_ID"];
			ids.push(arrIds[i]["ID"]);
			tqffs.push(arrIds[i]["EXTDR_METHOD"]);
			codes.push(arrIds[i]["SAMPLE_CODE"]);
		}
		openWindow(winOpts, { "IDS": ids, "CODES": codes, "TQFFS": tqffs, "TASK_ID": TASK_ID });
	}
	//追加2
	var addToEx2 = function () {
		var arrIds = getGridSelectData(gridNameD1Grid);

		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}

		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/addtoex2/addtoex2",
			title: "追加样本到执行单.."
		};
		var ids = [];
		var codes = [];
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			codes.push(arrIds[i]["SAMPLE_CODE"]);
		}

		openWindow(winOpts, { "IDS": ids, "CODES": codes });
	}

	//任务单状态修改
	var doTaskStatus = function () {
		debugger;
		var arrIds = getGridSelectData(gridNameDGrid);
		if (arrIds.length != 1) {
			alertMsg("请选择一条记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/uptaskstatus/uptaskstatus",
			title: "修改任务单状态.."
		};

		openWindow(winOpts, {
			"IDS": [arrIds[0]["ID"]], "LSM_KEY": arrIds[0]["LSM_KEY"], "CUSTOMFIELD_13229": arrIds[0]["QCRESULT"],
			"SAMPLE_BATCHNO": arrIds[0]["SAMPLE_BATCHNO"]
		});
	}
	//任务单状态修改
	var doTaskP = function () {

		var arrIds = getSelectData(gridNameDGrid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/uptaskp/uptaskp",
			title: "任务单补充.."
		};
		openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
	}


	var callBack = function () {
		refreshGrid();
	};

	var refreshGrid = function () {
		gridNameS = [];
		gridNameS2 = [];
		gridNameS3 = [];
		if (gridNameDGrid) {
			gridNameDGrid.dataSource.read();
		}
		if (gridNameD1Grid) {
			gridNameD1Grid.dataSource.read();
		}
		if (gridNameD2Grid) {
			gridNameD2Grid.dataSource.read();
		}
		if (gridNameD3Grid) {
			gridNameD3Grid.dataSource.read();
		}
		if (gridNameD4Grid) {
			gridNameD4Grid.dataSource.read();
		}
		if (gridNameD5Grid) {
			gridNameD5Grid.dataSource.read();
		}
		if (gridNameD9Grid) {
			gridNameD9Grid.dataSource.read();
		}
		if (gridNameD8Grid) {
			gridNameD8Grid.dataSource.read();
		}
		if (gridNameD7Grid) {
			gridNameD7Grid.dataSource.read();
		}
		if (gridNameD6Grid) {
			gridNameD6Grid.dataSource.read();
		}

	}
	//移除明细
	var remove = function () {
		debugger
		var arrg = [];
		var arrIds = [];
		var obj = [];
		var IDS = [];
		for (var i = 0; i < gridNameS2.length; i++) {
			var arrSubID = getGridSelectData(gridNameS2[i]);
			var IDS_ = getSelectData(gridNameS2[i], "EXE_TQQC_ID");
			IDS = IDS.concat(IDS_);
			arrg = arrg.concat(arrSubID);

		}
		if (arrg.length == 0) {
			alertMsg("请至少选择一条数据进行操作!");
			return;
		}

		for (var i = 0; i < arrg.length; i++) {
			arrIds.push(arrg[i]["LIBID"]);
			obj.push({ "ID": arrg[i]["ID"], "TASK_SM_STATUS": "待提取" });
		}
		confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
			var urlsend = "system/jdbc/save/batch/table";
			var paramsadd1 = { "tableName": "BIO_TQ_TASK_MX", "objects": obj };
			putAddOrUpdata(urlsend, paramsadd1, "否", "移除");
			var params = { "tableName": "BIO_DNA_RNA_QC", "ids": arrIds };
			var url = "system/jdbc/delete/batch/table";
			deleteGridDataByIds(url, params, "否");

			var inobjjson = {
				"IDS": IDS,
				"OPERATE": "删除生成",
			}
			$.fn.ajaxPost({
				ajaxType: "post",
				ajaxUrl: "function/system/settlement/tqScheduling/schsbk",
				ajaxData: inobjjson,
				succeed: function (result) {
					refreshGrid();
				}
			})

		});
	}
	//删除执行单
	var doDelete = function () {
		var arrIds = getSelectData(gridNameD2Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行操作!");
			return;
		}
		confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
			var url = "system/jdbc/delete/one/table/where";
			var params1 = { "tableName": "BIO_DNA_RNA_QC", "where": { "EXE_TQQC_ID": arrIds } };
			deleteGridDataByIds(url, params1, refreshGrid);
			var params2 = { "tableName": "EXE_TQQC_SHEET", "where": { "ID": arrIds } };
			deleteGridDataByIds(url, params2, refreshGrid);
		});
	}
	//预处理提交
	var submit = function () {
		var arrIds = getGridSelectData(gridNameD7Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行修改!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行修改操作!");
			return;
		}

		var sample;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: { "query": "query_YPD_BIO_TQ_TASK_MX_list_Check", "objects": [[arrIds[0]["ID"]]] },
			succeed: function (rs) {
				sample = rs.rows;             //样品
				var num = 0;
				for (var j = 0; j < sample.length; j++) {
					if (sample[j]["JK_CHECK"] == "OK") {
						num = num + 1;

					}
				}
				if (num < sample.length) {
					alertMsg("还有" + (sample.length - num) + "条未核验，不能提交");
					return;
				}
				var objectSheet = [];
				objectSheet.push({
					"ID": arrIds[0]["ID"],//id
					"EX_RE_STATUS": "待审核"       //状态

				});
				var urlsend = "system/jdbc/save/batch/table";
				var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
				putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
			}
		});

	}

	//核酸编号
	var doGenNo = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS2.length; i++) {
			var arrSubID = getGridSelectData(gridNameS2[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}


		var objectup = [];
		var tasks = [];
		var taskjtnos = [];

		var codes = [];
		var codesdon = [];
		var hesuanS = "";
		for (var i = 0; i < arrIds.length; i++) {
			if (arrIds[i]["SAMPLE_GENNO"]) {
				hesuanS = hesuanS + arrIds[i]["SAMPLE_GENNO"] + ";";
			} else {
				//更新记录
				var jtno = 0;
				var taskid = arrIds[i]["TASK_ID"];
				var bttmid = arrIds[i]["ID"];
				var maxjt = arrIds[i]["DONJT"];
				var dotqnum = arrIds[i]["DOTQN"];
				var smcode = arrIds[i]["SAMPLE_CODE"];
				var flwtype = arrIds[i]["MEHOD_JCFLOW"];
				var indexn = tasks.indexOf(taskid);
				if (maxjt >= 1) {//存在最大编号了
					if (indexn > -1) {
						jtno = parseInt(taskjtnos[indexn]) + 1;
						taskjtnos[indexn] = parseInt(jtno);
					} else {
						jtno = parseInt(maxjt);
						tasks.push(taskid);
						taskjtnos.push(parseInt(jtno));
					}
				} else {
					if (indexn > -1) {
						jtno = parseInt(taskjtnos[indexn]) + 1;
						taskjtnos[indexn] = parseInt(jtno);
					} else {
						jtno = 1;
						tasks.push(taskid);
						taskjtnos.push(parseInt(jtno));
					}
				}

				if (flwtype == "DNA检测-MCD检测") {
					var SAMPLE_GENNOS;
					$.fn.ajaxPost({
						ajaxUrl: "system/jdbc/database/execute/sqlcode",
						ajaxType: "post",
						ajaxAsync: false,
						ajaxData: {
							"sqlcode": " select  max(SAMPLE_GENNO) as SAMPLE_GENNO ,  count(1) as NUM from BIO_DNA_RNA_QC where   SAMPLE_GENNO IS NOT NULL and  TASK_TQ_ID  =  '" + bttmid + "'"
						},
						succeed: function (rs) {
							SAMPLE_GENNOS = rs.rows;
						}
					});

					var SAMPLE_GENNO = SAMPLE_GENNOS[0]["SAMPLE_GENNO"];
					if (SAMPLE_GENNO == null) {
						var smn = codes.indexOf(smcode);
						var smno = 1;
						if (smn > -1) {
							smno = parseInt(codesdon[smn]) + 1;
							codesdon[smn] = parseInt(codesdon[smn]) + 1;
						} else {
							if (!dotqnum || dotqnum < 1) {
								dotqnum = 1;
							}
							else {
								dotqnum = dotqnum + 1;
							}
							smno = dotqnum;
							codes.push(smcode);
							codesdon.push(smno);
						}
						objectup.push({
							"ID": arrIds[i]["LIBID"],//关联更新ID
							"DJC_JT_NO": jtno,
							"SAMPLE_GENNO": smcode + getXX(smno) + "-1" //核酸编号
						});
					} else {
						var NUM = SAMPLE_GENNOS[0]["NUM"] + 1;
						var SAMPLE_GENNO = SAMPLE_GENNOS[0]["SAMPLE_GENNO"];
						var aa = SAMPLE_GENNO.lastIndexOf("-");
						var cc = SAMPLE_GENNO.slice(0, aa + 1);
						objectup.push({
							"ID": arrIds[i]["LIBID"],//关联更新ID
							"DJC_JT_NO": jtno,
							"SAMPLE_GENNO": cc + NUM //核酸编号
						});
					}
				} else {
					var smn = codes.indexOf(smcode);
					var smno = 1;
					if (smn > -1) {
						smno = parseInt(codesdon[smn]) + 1;
						codesdon[smn] = parseInt(codesdon[smn]) + 1;
					} else {
						if (!dotqnum || dotqnum < 1) {
							dotqnum = 1;
						}
						else {
							dotqnum = dotqnum + 1;
						}
						smno = dotqnum;
						codes.push(smcode);
						codesdon.push(smno);
					}
					objectup.push({
						"ID": arrIds[i]["LIBID"],//关联更新ID
						"DJC_JT_NO": jtno,
						"SAMPLE_GENNO": smcode + "-" + smno //核酸编号
					});

				}


			}
		}
		if (hesuanS.length > 0) {
			alertMsg("核酸编号“" + hesuanS + "”已存在!");
		}
		if (objectup.length > 0) {
			var paramsup = { "tableName": "BIO_DNA_RNA_QC", "objects": objectup };
			var url = "system/jdbc/save/batch/table";
			putAddOrUpdata(url, paramsup, "是", "提取");
		}
	}
	var getXX = function (n) {
		debugger;
		var s = "";
		for (var i = 1; i < n; i++) {
			s += "x";
		}
		return s;
	}
	var setjira = function () {
		debugger;
		var arrIds = getGridSelectData(gridNameDGrid);
		if (arrIds.length != 1) {
			alertMsg("请选择一条数据进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskdan/jira/jira",
			title: "jira信息填写..",
			width: 1280,
			height: 480,
			position: { "top": 100, "left": 30 }
		};

		var p = arrIds[0];
		openWindow(winOpts, {
			"ID": p["LSMID"],
			"TASK_TYPE": p["TASK_TYPE"],
			"MAIN_ID": p["ID"],
			"TASK_JH_ENDDATE": p["TASK_JH_ENDDATE"],
			"SAMPLE_BATCHNO": p["SAMPLE_BATCHNO"],//批次号
			"LSM_KEY": p["LSM_KEY"],//LSM关键字
			"LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字
			//"CUSTOMFIELD_13226":p[""],//提取完成日期
			"CUSTOMFIELD_14300": p["TASK_LDATE"],//提取检测标准完成日期--
			//"CUSTOMFIELD_13254":p[""],//提取检测暂停开始日期
			"CUSTOMFIELD_13237": p["TASK_REMARKS_DES"],//提取具体情况描述
			// "CUSTOMFIELD_13229"://样品合格情况
		});

	}

	//异常追加
	var addToExRe = function () {
		var arrIds = getGridSelectData(gridNameD10Grid);

		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}

		var winOpts = {
			url: "biomarker/dispatch/tq/taskrna/addtoex/addtoex",
			title: "追加样本到执行单.."
		};
		var ids = [];
		var codes = [];
		var tqffs = [];
		var TASK_ID = "";
		for (var i = 0; i < arrIds.length; i++) {
			TASK_ID = arrIds[i]["TASK_ID"];
			ids.push(arrIds[i]["ID"]);
			tqffs.push(arrIds[i]["EXTDR_METHOD"]);
			codes.push(arrIds[i]["SAMPLE_CODE"]);
		}

		openWindow(winOpts, { "IDS": ids, "CODES": codes, "TQFFS": tqffs, "TASK_ID": TASK_ID, "YC": 1 });
	}

	//批量执行插入
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						alertMsg("提示:操作成功!");
						refreshGrid();
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}
	var doOK2 = function () {

		var mainIds = getGridSelectData(gridNameD2Grid);
		if (mainIds.length != 1) {
			alertMsg("请选择一条主单条记录进行操作!");
			return;
		}
		var mxIds = [];
		for (var i = 0; i < gridNameS2.length; i++) {
			var arrSubID = getGridSelectData(gridNameS2[i]);
			if (arrSubID.length != 0) {
				mxIds = mxIds.concat(arrSubID);
			}
		}
		if (mxIds.length == 0) {
			alertMsg("请选择明细记录!");
			return;
		}
		confirmMsg("确认", "确定要对选中的记录进行拆单吗?", "warn", function () {
			//新的执行单生成
			var newmainId = getRandomId();
			var newrecode = [];
			var time = sysNowTimeFuncParams["sysNowTime"];
			var username = getLimsUser()["name"];

			newrecode.push({//EXE_TQQC_SHEET
				"ID": newmainId,
				"EX_RE_STATUS": mainIds[0]["EX_RE_STATUS"],
				"EX_DH_NO": mainIds[0]["EX_DH_NO"] + "-1",//执行单号
				"EX_MAN_ID": mainIds[0]["EX_MAN_ID"],//关联用户ID
				"EX_MAN": mainIds[0]["EX_MAN"],//实验员
				"EX_TYPE": mainIds[0]["EX_TYPE"],//执行单类型
				"EX_TYPE_LB": mainIds[0]["EX_TYPE_LB"],//单据类别
				"EX_TIME": mainIds[0]["EX_TIME"],//下单时间
				"EX_BACK": mainIds[0]["EX_BACK"],//退回原因 
				"EX_NEEDING_ATN": mainIds[0]["EX_NEEDING_ATN"],//注意事项 
				"EX_REMARK": mainIds[0]["EX_REMARK"]//提取备注
			});


			//将记录转移到新的执行
			var newMxs = [];
			for (var i = 0; i < mxIds.length; i++) {
				newMxs.push({//BIO_DNA_RNA_QC
					"ID": mxIds[i]["LIBID"],//检测ID
					"EXE_TQQC_ID": newmainId
				});
			}

			var newUrl = "system/jdbc/save/one/table/objects";
			var paramsnainadd = { "tableName": "EXE_TQQC_SHEET", "objects": newrecode };
			putAddOrUpdata(newUrl, paramsnainadd, "否", "生成");

			var url = "system/jdbc/save/batch/table";
			var paramsmx = { "tableName": "BIO_DNA_RNA_QC", "objects": newMxs };
			putAddOrUpdata(url, paramsmx, "是", "");
		});

	}

	function getRandomId() {
		return (('FDSX-HSEXE-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
	};


	var doNucleic = function () {
		debugger
		var IDS = getSelectData(gridNameD2Grid);
		var inobjjson = {
			"IDS": IDS,
			"OPERATE": "首次生成",
		}
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "function/system/settlement/tqScheduling/schsbk",
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] == 1) {
					alertMsg("提示:操作成功!");
					refreshGrid();
				} else {

					var alertDialog = $('<div class="dialog-box"></div>').kendoDialog({
						animation: { open: { effects: 'fade:in' }, close: { effects: 'fade:out' } },
						maxWidth: '30%',
						maxHeight: '30%',
						minWidth: 380,
						minHeight: 196,
						title: '二三代确认',
						content: result["apiData"]["msg"],
						actions: [
							{
								text: '确认生成',
								primary: true,
								action: function () {

									alertMsgNoBtn('确认生成', 'info');

									var inobjjson = {
										"IDS": result["apiData"]["ExIdS"],
										"OPERATE": "确认生成",
									}
									$.fn.ajaxPost({
										ajaxType: "post",
										ajaxUrl: "function/system/settlement/tqScheduling/schsbk",
										ajaxData: inobjjson,
										succeed: function (result) {
											alertMsg("提示:操作成功!");
											refreshGrid();
										}
									});
								}
							},
							{
								text: '取消生成',
								action: function () {

									alertMsgNoBtn('取消生成', 'info');
									var objectup = [];
									var g = result["apiData"]["ExIdS"]

									for (var i = 0; i < g.length; i++) {
										//更新记录
										objectup.push({
											"ID": g[i],//联联任务ID
											"SDTQ": "否"
										});
									}
									var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
									var url = "system/jdbc/save/batch/table";
									putAddOrUpdata(url, paramsup, "是", "提交");
								}
							}
						],
						close: function () {
							alertDialog.destroy();
						}
					}).data('kendoDialog');
					alertDialog.open();

				}
			}
		});
	}
	funcPushs(pathValue, {
		"initData": initData,
		"init": init,
		"upsmStatus": upsmStatus,
		"doOK2": doOK2,
		"doNucleic": doNucleic,
		"addToEx": addToEx,
		"addToExRe": addToExRe,
		"addToEx2": addToEx2,
		"doTaskStatus": doTaskStatus,
		"doTaskP": doTaskP,
		"edit": edit,
		"edit2": edit2,
		"upsmFF": upsmFF,
		"upsmFF1": upsmFF1,
		"upsmFF2": upsmFF2,
		"upsmFF3": upsmFF3,
		"doOK": doOK,
		"editA": editA,
		"submit": submit,
		"pesubmit": pesubmit,
		"doReturn": doReturn,
		"doUpdate": doUpdate,
		"checkSample": checkSample,
		"doReturn2": doReturn2,
		"doReturn3": doReturn3,
		"doTochTest": doTochTest,
		"doGenNo": doGenNo,
		"remove": remove,
		"doDelete": doDelete,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"doExeM": doExeM,
		"setjira": setjira,
	});
});