$(document).ready(function() {
   var pathValue="biomarker-dispatch-tq-taskch-index";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var gridNameCGrid;
   var gridNameC1Grid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"edit",title:"指派实验员"}
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"query_PD_BIO_CH_MX_list","objects":[]},
            headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="PROJECT_NO"){
                        setJsonParam(cols[i],"template",getTemplate("#= PROJECT_NO #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
        };
        gridNameCGrid = initKendoGrid("#gridNameCGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        init1();
   }
   
   var init1=function(params){
       /**
        * 列表-按钮-定义
        */
       var toolbar=getButtonTemplates(pathValue,[

       ]);//工具条
       //请求参数
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           toolbar: toolbar,
           read:{"query":"query_YPD_BIO_CH_MX_list","objects":[]},

           headerFilter:function(cols,i){
               if(i){
                   if(cols[i]["field"]&&cols[i]["field"]=="PROJECT_NO"){
                       //setJsonParam(cols[i],"template",getTemplate("#= PROJECT_NO #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                   }
               }
           }
       };
       gridNameC1Grid = initKendoGrid("#gridNameC1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
  }

    var add=function(){
        var winOpts={
            url:"biomarker/dispatch/tq/taskch/pdup/pdup",
            title:"纯化排单.."
        };
        openWindow(winOpts);
    }

    var open=function(IDS){
        var winOpts={
            url:"biomarker/dispatch/tq/taskch/pdup/pdup",
            title:"纯化排单.."
        };
        openWindow(winOpts,{"IDS":IDS});//传递id
    }

    var edit=function(){
        var arrIds=getSelectData(gridNameCGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
		open(arrIds);
     }
     
     var sumbit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameCGrid){
        	gridNameCGrid.dataSource.read();//重新读取--刷新
        }
        if(gridNameC1Grid){
        	gridNameC1Grid.dataSource.read();//重新读取--刷新
        }
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(gridCameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_CH_MX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }

    //表格导入
    var importData=function(componentId){
        var grid=gridNameGrid;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":grid,//单表导入
                "tableName":"BIO_CH_MX",
            }
        });
    }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,
         "add":add,//打开添加表单
         "edit":edit,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "sumbit":sumbit,//提交方法
         "callBack":callBack,//回调方法
		 "importData":importData,
     });
});