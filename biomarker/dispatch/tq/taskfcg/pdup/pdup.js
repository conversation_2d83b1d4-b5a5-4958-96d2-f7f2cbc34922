$(document).ready(function () {
    var pathValue = "biomarker-dispatch-tq-taskfcg-pdup-pdup";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "EXE_TQQC_SHEET"
        };
    }
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
        $("#EX_TYPE" + pathValue).val(paramsValue["EX_TYPE"]);
        $("#EX_RE_STATUS" + pathValue).val("待审核");

        $("#NO_CHAR" + pathValue).val("HY");

    }

    var subUpData = function () {
        debugger;


        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = $("#EX_MAN" + pathValue).val();
        var adflat = 0;
        $("#TASKTYPE_FLAT" + pathValue).val(adflat);
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    //提交成功
                    var ids = paramsValue["IDS"];
                    var SAMPLE_NAME = paramsValue["SAMPLE_NAME"];
                    var taskids = paramsValue["TASKIDS"];
                    var codes = paramsValue["CODES"];
                    var objectadd = [];
                    var objectup = [];
                    var faskdate = paramsValue["FASKDATE"];
                    var tqffs = paramsValue["TQFFS"];
                    var objectupmx = [];

                    var SAMPLE_GENNOS = getSAMPLE_GENNO(ids);
                    for (var i = 0; i < ids.length; i++) {
                        for (var j = 0; j < SAMPLE_GENNOS.length; j++) {
                            if (ids[i] == SAMPLE_GENNOS[j]["ID"]) {
                                var aa = 0;
                                if (SAMPLE_NAME[i].indexOf("GEX") > -1) aa = 1;




                                objectadd.push({
                                    "TASK_TQ_ID": ids[i],//联联任务ID
                                    "EXE_TQQC_ID": result["ID"],//关联执行单
                                    "SAMPLE_CODE": codes[i],//样本编号
                                    "CONCATENATE_KEYWORDS": getCONCATENATE_KEYWORDS(codes[i]),//串联关键字
                                    "SAMPLE_GENNO": SAMPLE_GENNOS[j]["SAMPLE_GENNO"], //核酸编号
                                    "EXTDR_METHOD": tqffs[i],//提取方法
                                    "TQ_MAN": username,//提取实验员
                                    "EXT_SAMPLENUM": aa,  //默认是1，可以修改上传
                                    "HSEXT_SAMPLENUM": aa,  //默认是1，可以修改上传
                                    "DJC_MAN": username,//检测实验员
                                    "TQ_ST_DATE": time//开始提取日期
                                });
                            }

                        }
                        objectupmx.push({ "ID": ids[i], "TASK_SM_STATUS": "已排单" });
                    }
                    for (var i = 0; i < taskids.length; i++) {
                        if (faskdate == null || faskdate == "" || faskdate == "undefined") {
                            objectup.push({
                                "ID": taskids[i],
                                "TASK_STATUS": "提取中",
                                "TASK_FIRSTDATE": time,
                                "SYS_MAN_L": time,
                                "SYS_INSERTTIME_L": username
                            });
                        } else {
                            objectup.push({
                                "ID": taskids[i],
                                "TASK_STATUS": "提取中",
                                "SYS_MAN_L": time,
                                "SYS_INSERTTIME_L": username
                            });
                        }
                    }
                    //执行添加到文库
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsadd = { "tableName": "BIO_DNA_RNA_QC", "objects": objectadd };
                    putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");


                    var paramsup2 = { "tableName": "BIO_TQ_TASK_MX", "objects": objectupmx };
                    putAddOrUpdata(urlsend, paramsup2, "否", "已排单");

                    var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "提取中");



                    alertMsg("提示:操作成功!");
                    funcExce(pathValue + "pageCallBack");
                    funcExce(pathValue + "close");



                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });
    }
    /**
 * 串联关键字	
 * @param ID 任务单明细ID 
 */
    var getCONCATENATE_KEYWORDS = function (codes) {
        if (paramsValue["EX_TYPE"] == "HE染色") {
            return codes + "-a";
        } else {
            if (paramsValue["EX_TYPE"] == "基因表达" || paramsValue["EX_TYPE"] == "组织优化") {

                var SAMPLE_GENNOS;
                $.fn.ajaxPost({
                    ajaxUrl: "system/jdbc/database/execute/sqlcode",
                    ajaxType: "post",
                    ajaxAsync: false,
                    ajaxData: {
                        "sqlcode": " SELECT CONCATENATE_KEYWORDS FROM   BIO_DNA_RNA_QC  where SAMPLE_CODE = '" + codes + "'"
                    },
                    succeed: function (rs) {
                        SAMPLE_GENNOS = rs.rows;
                    }
                });
                if(SAMPLE_GENNOS.length>0){
                    return  SAMPLE_GENNOS[0]["CONCATENATE_KEYWORDS"] ; 
                }else{
                    return null;  
                }


            } else {
                return null;
            }
        }


    }
    /**
     * 生成核酸编号
     * @param ID 任务单明细ID 
     */
    var getSAMPLE_GENNO = function (IDS) {


        var objectup = [];

        var codes = [];
        var codesdon = [];
        var bttidstr = "";
        for (var i = 0; i < IDS.length; i++) {
            if (bttidstr.length == 0) {
                bttidstr = "'" + IDS[i] + "'";
            } else {
                bttidstr = bttidstr + ",'" + IDS[i] + "'";
            }
        }

        var SAMPLE_GENNOS;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/database/execute/sqlcode",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: {
                "sqlcode": " select  max(b.SAMPLE_GENNO) as SAMPLE_GENNO , " +
                    " count(1) as ANUM, " +
                    " max(a.SAMPLE_CODE) as SAMPLE_CODE, " +
                    " max( k.DOTQN ) as DOTQN, " +
                    " a.id " +
                    "from BIO_TQ_TASK_MX a left join  BIO_DNA_RNA_QC b on  a.id = b.TASK_TQ_ID " +
                    " left join  (select SAMPLE_CODE,count(1) as   DOTQN from BIO_DNA_RNA_QC  where NOT SAMPLE_GENNO IS NULL GROUP BY SAMPLE_CODE ) k on k.SAMPLE_CODE=a.SAMPLE_CODE " +
                    "where    a.id  in (" + bttidstr + ") " +
                    "GROUP BY  a.id  "
            },
            succeed: function (rs) {
                SAMPLE_GENNOS = rs.rows;
            }
        });
        for (var i = 0; i < SAMPLE_GENNOS.length; i++) {

            var SAMPLE_GENNO = SAMPLE_GENNOS[i]["SAMPLE_GENNO"];
            var smcode = SAMPLE_GENNOS[i]["SAMPLE_CODE"];
            var dotqnum = SAMPLE_GENNOS[i]["DOTQN"];
            if (SAMPLE_GENNO == null) {
                var smn = codes.indexOf(smcode);
                var smno = 1;
                if (smn > -1) {
                    smno = parseInt(codesdon[smn]) + 1;
                    codesdon[smn] = parseInt(codesdon[smn]) + 1;
                } else {
                    if (!dotqnum || dotqnum < 1) {
                        dotqnum = 1;
                    }
                    else {
                        dotqnum = dotqnum + 1;
                    }
                    smno = dotqnum;
                    codes.push(smcode);
                    codesdon.push(smno);
                }
                objectup.push({
                    "ID": SAMPLE_GENNOS[i]["ID"],
                    "SAMPLE_GENNO": smcode + getXX(smno) + "-1" //核酸编号
                });
            } else {
                var ANUM = SAMPLE_GENNOS[i]["ANUM"] + 1;
                var SAMPLE_GENNO = SAMPLE_GENNOS[i]["SAMPLE_GENNO"];
                var aa = SAMPLE_GENNO.lastIndexOf("-");
                var cc = SAMPLE_GENNO.slice(0, aa + 1);
                objectup.push({
                    "ID": SAMPLE_GENNOS[i]["ID"],
                    "SAMPLE_GENNO": cc + ANUM //核酸编号
                });
            }
        }
        return objectup;


    }
    var getXX = function (n) {
        var s = "";
        for (var i = 1; i < n; i++) {
            s += "x";
        }
        return s;
    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var submit = function () {
        subUpData();
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});