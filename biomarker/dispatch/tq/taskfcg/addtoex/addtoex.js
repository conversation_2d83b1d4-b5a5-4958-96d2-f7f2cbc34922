$(document).ready(function () {
	var pathValue = "biomarker-dispatch-tq-taskfcg-addtoex-addtoex";
	var paramsValue;
	var initData = function () {
		return {};
	}
	var gridNameGrid;
	var init = function (params) {
		paramsValue = params;
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "add", title: "确认选择" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", 
                                  "objects": [["HE染色","组织优化","基因表达","制备","预实验"], ["待审核", "实验退回"]] },
			headerFilter: function (cols, i) { },
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
	}

	var add = function () {

		var myids = getGridSelectData(gridNameGrid);
		if (myids.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		} else if (myids.length != 1) {
			alertMsg("一次只允许选择一条记录进行操作!");
			return;
		}
		var arrIds = [];
		var exman = [];
		for (var i = 0; i < myids.length; i++) {
			arrIds.push(myids[i]["ID"]);
			exman.push(myids[i]["EX_MAN"]);
		}
		var objectadd = [];
		var ids = paramsValue["IDS"];
		var codes = paramsValue["CODES"];
		var time = sysNowTimeFuncParams["sysNowTime"];
		var username = getLimsUser()["name"];
		var tqffs = paramsValue["TQFFS"];
		//检查执行单是否存在

		var params = { "query": "doCheckExSmInfo", "objects": [arrIds, codes] };
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var rows = result["rows"];
					var s = "";
					var objectupmx = [];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						if (i == 0) {
							s = row["SAMPLE_CODE"];
						} else {
							s += "," + row["SAMPLE_CODE"];
						}
					}
					var SAMPLE_GENNOS = getSAMPLE_GENNO(ids);

					for (var i = 0; i < SAMPLE_GENNOS.length; i++) {
						for (var j = 0; j < ids.length; j++) {
							if (ids[j] == SAMPLE_GENNOS[i]["ID"]) {
								objectadd.push({
									"TASK_TQ_ID": ids[j],//联联任务ID
									"EXE_TQQC_ID": arrIds[0],//关联执行单
									"SAMPLE_CODE": codes[j],
									"SAMPLE_GENNO": SAMPLE_GENNOS[i]["SAMPLE_GENNO"], //核酸编号
									"EXTDR_METHOD": tqffs[i],//提取方法
                                                                          "EXT_SAMPLENUM": 1,  //默认是1，可以修改上传
                                                                          "HSEXT_SAMPLENUM": 1,  //默认是1，可以修改上传
									"TQ_MAN": exman[0],//提取实验员
									"DJC_MAN": exman[0],//检测实验员
									"TQ_ST_DATE": time//开始提取日期
								});
								objectupmx.push({ "ID": ids[j], "TASK_SM_STATUS": "已排单" });
								break;
							}
						}
					}
					var urlsend = "system/jdbc/save/batch/table";

					var paramsup2 = { "tableName": "BIO_TQ_TASK_MX", "objects": objectupmx };
					putAddOrUpdata(urlsend, paramsup2, "否", "已排单");

					var paramsadd = { "tableName": "BIO_DNA_RNA_QC", "objects": objectadd };
					putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");

				}debugger
			}
		});
	}
	/**
		 * 生成核酸编号
		 * @param ID 任务单明细ID 
		 */
	var getSAMPLE_GENNO = function (IDS) {


		var objectup = [];

		var codes = [];
		var codesdon = [];
		var bttidstr = "";
		for (var i = 0; i < IDS.length; i++) {
			if (bttidstr.length == 0) {
				bttidstr = "'" + IDS[i] + "'";
			} else {
				bttidstr = bttidstr + ",'" + IDS[i] + "'";
			}
		}

		var SAMPLE_GENNOS;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/database/execute/sqlcode",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: {
				"sqlcode": " select  max(b.SAMPLE_GENNO) as SAMPLE_GENNO , " +
					" count(1) as ANUM, " +
					" max(a.SAMPLE_CODE) as SAMPLE_CODE, " +
					" max( k.DOTQN ) as DOTQN, " +
					" a.id " +
					"from BIO_TQ_TASK_MX a left join  BIO_DNA_RNA_QC b on  a.id = b.TASK_TQ_ID " +
					" left join  (select SAMPLE_CODE,count(1) as   DOTQN from BIO_DNA_RNA_QC  where NOT SAMPLE_GENNO IS NULL GROUP BY SAMPLE_CODE ) k on k.SAMPLE_CODE=a.SAMPLE_CODE " +
					"where    a.id  in (" + bttidstr + ") " +
					"GROUP BY  a.id  "
			},
			succeed: function (rs) {
				SAMPLE_GENNOS = rs.rows;
			}
		});
		for (var i = 0; i < SAMPLE_GENNOS.length; i++) {

			var SAMPLE_GENNO = SAMPLE_GENNOS[i]["SAMPLE_GENNO"];
			var smcode = SAMPLE_GENNOS[i]["SAMPLE_CODE"];
			var dotqnum = SAMPLE_GENNOS[i]["DOTQN"];
			if (SAMPLE_GENNO == null) {
				var smn = codes.indexOf(smcode);
				var smno = 1;
				if (smn > -1) {
					smno = parseInt(codesdon[smn]) + 1;
					codesdon[smn] = parseInt(codesdon[smn]) + 1;
				} else {
					if (!dotqnum || dotqnum < 1) {
						dotqnum = 1;
					}
					else {
						dotqnum = dotqnum + 1;
					}
					smno = dotqnum;
					codes.push(smcode);
					codesdon.push(smno);
				}
				objectup.push({
					"ID": SAMPLE_GENNOS[i]["ID"],
					"SAMPLE_GENNO": smcode + getXX(smno) + "-1" //核酸编号
				});
			} else {
				var ANUM = SAMPLE_GENNOS[i]["ANUM"] + 1;
				var SAMPLE_GENNO = SAMPLE_GENNOS[i]["SAMPLE_GENNO"];
				var aa = SAMPLE_GENNO.lastIndexOf("-");
				var cc = SAMPLE_GENNO.slice(0, aa + 1);
				objectup.push({
					"ID": SAMPLE_GENNOS[i]["ID"],
					"SAMPLE_GENNO": cc + ANUM //核酸编号
				});
			}
		}
		return objectup;


	}
	var getXX = function (n) {
		var s = "";
		for (var i = 1; i < n; i++) {
			s += "x";
		}
		return s;
	}

	//批量执行插入
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						alertMsg("提示:操作成功!");
						refreshGrid();
						funcExce(pathValue + "pageCallBack");
						funcExce(pathValue + "close");
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}


	var callBack = function () {
		refreshGrid();
	};

	var refreshGrid = function () {
		if (gridNameGrid) {
			//gridNameGrid.dataSource.read();
		}
	}

	funcPushs(pathValue, {
		"initData": initData,
		"init": init,
		"add": add,
		"refreshGrid": refreshGrid,
		"callBack": callBack,//回调方法
	});
});