$(document).ready(function () {
	var pathValue = "biomarker-dispatch-tq-taskfcg-index";
	var initData = function () {
		return {};
	}
	var gridNameDGrid;
	var gridNameD1Grid;
	var gridNameD2Grid;
	var gridNameD3Grid;
	var gridNameS = [];
	var gridNameS2 = [];
	var gridNameS3 = [];
	var sydates = 0;
	var cxdates = 0;

	var init = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "edit", title: "生成执行单" },
			{ name: "edit", target: "upsmStatus", title: "修改样本状态" },
			{ name: "edit", target: "addToEx", title: "追加样本到执行单" },
			{ name: "edit", target: "doTaskStatus", title: "任务单状态修改.." },
			{ name: "edit", target: "batchSettlement", title: "批量结单" },
			{ name: "edit", target: "setjiraStop", title: "JIRA推送-暂停" },
			//	{ name: "edit", target: "setjira", title: "jira推送.." },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_BIO_TQ_TASK2_list", "objects": [["暂停中", "已审核", "提取中", "待提取", "待检测", "检测中", "审核中", "已完成"], ["单细胞提取"]], "search": { "DD_TASK_STATUS": ["已审核"] } },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_PD_BIO_TQ_TASK_MX_OTHER_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS.push(subGrid_N);
			}
		};
		gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
		init2();
		init3();
		init4();
	}

	//待审核
	var init2 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "delete", target: "remove", title: "移除任务明细" },
			{ name: "delete", target: "doDelete", title: "删除执行单" },
			{ name: "edit", target: "doUpdate", title: "修改实验员" },
			{ name: "ok", target: "doOK", title: "提交" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", "objects": [["HE染色", "组织优化", "基因表达", "制备", "预实验"], ["待审核", "接收退回", "实验退回"]] },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_YPD_BIO_TQ_TASK_MX_OTHER_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS2.push(subGrid_N);
			}
		};
		gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
	}
	//已处理
	var init3 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "return", target: "doReturn", title: "撤回" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_DD_OK_EXE_TQQC_SHEET_list", "objects": [["HE染色", "组织优化", "基因表达", "制备", "预实验"], ["待接收", "已接收", "提取结果提交", "检测结果待审核", "检测结果已审核", "实验退回", "已完结"]] },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_YPD_BIO_TQ_TASK_MX_OTHER_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
			}
		};
		gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);
	}
	//已结单
	var init4 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "return", target: "doReturn2", title: "移至待排单" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "query_BIO_TQ_TASK2_list", "objects": [["结单", "暂停", "未提取", "终止"], ["单细胞提取"]] },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "query_PD_BIO_TQ_TASK_MX_OTHER_list", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
			}
		};
		gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);

	}


	var edit = function () {

		var arrIds = [];
		for (var i = 0; i < gridNameS.length; i++) {
			var arrSubID = getGridSelectData(gridNameS[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}

		//判断类型是否全部分同一类,并取出形成类型单
		//MEHOD_PLAT 提取流向
		//MEHOD_JCFLOW 检测流向
		var g = arrIds;
		var a = "";
		var b = "";
		var b1 = "";
		var code = [];
		var faskdate = "";

		for (var i = 0; i < g.length; i++) {
			if (g[i]["TASK_SM_STATUS"] == "已完成" || g[i]["TASK_SM_STATUS"] == "提取中") {
				alertMsg("提示:样品“" + g[i]["SAMPLE_CODE"] + g[i]["TASK_SM_STATUS"] + "”");
				return;
			}
			if (i == 0) {
				a = g[i]["TASK_TYPE"];
				b = g[i]["TASK_TYPE"];
				// a1 = g[i]["MEHOD_JCFLOW"];
				// b1 = g[i]["MEHOD_JCFLOW"];
				code.push(g[i]["SAMPLE_CODE"]);
				faskdate = g[i]["TASK_FIRSTDATE"];

			} else {
				a = g[i - 1]["TASK_TYPE"];
				b = g[i]["TASK_TYPE"];
				// a1 = g[i - 1]["MEHOD_JCFLOW"];
				// b1 = g[i]["MEHOD_JCFLOW"];

			}
			if (a != b) {
				alertMsg("存在所选记录任务类型“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
				return;
			}

		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskfcg/pdup/pdup",
			title: a
		};
		var ids = [];
		var taskids = [];
		var codes = [];
		var tqffs = [];
		var SAMPLE_NAME = [];
		var tasktype;
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			codes.push(arrIds[i]["SAMPLE_CODE"]);
			tqffs.push(arrIds[i]["EXTDR_METHOD"]);
			SAMPLE_NAME.push(arrIds[i]["SAMPLE_NAME"]);
			tasktype = arrIds[i]["TASK_TYPE"];
			if (taskids.indexOf(arrIds[i]["TASK_ID"]) < 0) {
				taskids.push(arrIds[i]["TASK_ID"]);//主单ID
			}
		}
		openWindow(winOpts, { "SAMPLE_NAME": SAMPLE_NAME, "IDS": ids, "TASKIDS": taskids, "CODES": codes, "EX_TYPE": a, "EX_TYPE_LB": a, "FASKDATE": faskdate, "TQFFS": tqffs, "tasktype": tasktype });

	}
	//修改实验员 
	var doUpdate = function () {
		var arrIds = getGridSelectData(gridNameD1Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行修改!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行修改操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskfcg/updateman/updateman",
			title: "自动排单明细.."
		};
		openWindow(winOpts, { "ID": arrIds[0]["ID"], "EX_MAN": arrIds[0]["EX_MAN"] });//传递
	}
	//审核提交
	var doOK = function () {
		var arrIds = getSelectData(gridNameD1Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		//校验核酸编号是否为空
		var s = "";
		var params = { "query": "doCheckBioCOdeIsPass", "objects": [arrIds] };
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var objectup = [];
					var rows = result["rows"];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						if (row["SAMPLE_GENNO"] == "" || row["SAMPLE_GENNO"] == null) {
							if (s == "") {
								s = row["EX_DH_NO"];
							}
						}
					}
					if (s != "") {
						alertMsg("提示:单号“" + s + "”存在核酸编号为空!");
						return;
					}
					var objectup = [];
					for (var i = 0; i < arrIds.length; i++) {
						var time = sysNowTimeFuncParams["sysNowTime"];
						var username = getLimsUser()["name"];
						objectup.push({
							"ID": arrIds[i],//联联任务ID
							"EX_RE_STATUS": "已接收"
						});
					}
					var urlsend = "system/jdbc/save/batch/table";
					var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
					putAddOrUpdata(urlsend, paramsup, "是", "提交");
					//	doRequeDoUpTaskmxSmStatus(arrIds, "提取中");

				}
			}
		});

	}




	//撤回
	var doReturn = function () {
		var g = getGridSelectData(gridNameD2Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		var arrIds = [];
		for (var i = 0; i < g.length; i++) {
			if (g[i]["EX_RE_STATUS"] != "待接收") {
				alertMsg("操作失败,所选记录存在非“待接收”状态!");
				return;
			} else {
				objectup.push({
					"ID": g[i]["ID"],
					"EX_RE_STATUS": "待审核"
				});
				arrIds.push(g[i]["ID"]);
			}
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
		doRequeDoUpTaskmxSmStatus(arrIds, "待提取");
	}


	//任务单-对应执行单下明细状态修改BIO_DNA_RNA_QC
	var doRequeDoUpTaskmxSmStatus = function (mainExIds, status) {
		var time = sysNowTimeFuncParams["sysNowTime"];
		var username = getLimsUser()["name"];
		var params = { "query": "doRequeDoUpTaskmxSmStatus", "objects": [mainExIds] };
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var objectup = [];
					var objectupmain = [];
					debugger;
					var rows = result["rows"];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];

						if (objectupmain.indexOf(row["TASK_ID"]) < 0) {
							objectupmain.push(row["TASK_ID"]);
						}
						//更新记录---明细
						objectup.push({
							"ID": row["TASKMXID"],
							"TASK_SM_STATUS": status
						});
					}

					var urlsend = "system/jdbc/save/batch/table";
					var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objectup };
					putAddOrUpdata(urlsend, paramsup, "否", "同步更新任务明细");

					var urlmain = "system/jdbc/update/one/table/where";
					var paramsupmain = {
						"tableName": "BIO_TQ_TASK",
						"TASK_STATUS": status,
						"SYS_MAN_L": username,
						"SYS_INSERTTIME_L": time,
						"where": { "ID": objectupmain }
					};
					putAddOrUpdata(urlmain, paramsupmain, "否", "同步更新任务");

				}
			},
			failed: function (result) {
				alertMsg("提示:操作异常!", "error");
			}
		});
	}
	//预处理提交
	var submit = function () {
		var arrIds = getGridSelectData(gridNameD5Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行修改!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行修改操作!");
			return;
		}

		var sample;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: { "query": "query_YPD_BIO_TQ_TASK_MX_list_Check", "objects": [[arrIds[0]["ID"]]] },
			succeed: function (rs) {
				sample = rs.rows;             //样品
				var num = 0;
				for (var j = 0; j < sample.length; j++) {
					if (sample[j]["JK_CHECK"] == "OK") {
						num = num + 1;

					}
				}
				if (num < sample.length) {
					alertMsg("还有" + (sample.length - num) + "条未核验，不能提交");
					return;
				}
				var objectSheet = [];
				objectSheet.push({
					"ID": arrIds[0]["ID"],//id
					"EX_RE_STATUS": "待审核"       //状态

				});
				var urlsend = "system/jdbc/save/batch/table";
				var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
				putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
			}
		});

	}

	//移至待审核
	var doReturn2 = function () {
		var g = getGridSelectData(gridNameD3Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		for (var i = 0; i < g.length; i++) {
			if (g[i]["TASK_STATUS"] == "暂停" || g[i]["TASK_STATUS"] == "未提取" || g[i]["TASK_STATUS"] == "结单") {
				objectup.push({
					"ID": g[i]["ID"],
					"TASK_STATUS": "已审核"
				});
			} else {
				alertMsg("操作失败,只有“<font color=#ff0000>暂停、未提取</font>”状态方允许操作!");
				return;
			}
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
	}


	//追加
	var addToEx = function () {

		var arrIds = [];
		for (var i = 0; i < gridNameS.length; i++) {
			var arrSubID = getGridSelectData(gridNameS[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}

		var winOpts = {
			url: "biomarker/dispatch/tq/taskfcg/addtoex/addtoex",
			title: "追加样本到执行单.."
		};
		var ids = [];
		var codes = [];
		var tqffs = [];

		for (var i = 0; i < arrIds.length; i++) {
			if (arrIds[i]["TASK_SM_STATUS"] == "检测完成" || arrIds[i]["TASK_SM_STATUS"] == "提取中") {
				alertMsg("提示:样品“" + arrIds[i]["SAMPLE_CODE"] + arrIds[i]["TASK_SM_STATUS"] + "”");
				return;
			}
			ids.push(arrIds[i]["ID"]);
			tqffs.push(arrIds[i]["EXTDR_METHOD"]);
			codes.push(arrIds[i]["SAMPLE_CODE"]);
		}

		openWindow(winOpts, { "IDS": ids, "CODES": codes, "TQFFS": tqffs });
	}

	var batchSettlement = function () {
		debugger

		var arrIds = getSelectData(gridNameDGrid);
		if (arrIds.length == 0) {
			alertMsg("请选择一条记录进行操作!");
			return;
		}




		var objects = getGridSelectData(gridNameDGrid);
		var bttidstr = "";


		for (var i = 0; i < objects.length; i++) {

			if (objects[i]["TASK_TYPE"] == "组织优化" || objects[i]["TASK_TYPE"] == "基因表达") {
				sendPM(objects[i]["LSM_KEY_P"], objects[i]["TASK_TYPE"])
			}

			if (objects[i]["TASK_TYPE"] == "制备") {
				sendLSM(objects[i]["LSM_KEY"], objects[i]["TASK_TYPE"], objects[i]["QCRESULT"])
				sendPM2(setjira(objects[i]));
			}
			if (bttidstr.length == 0) {
				bttidstr = "'" + objects[i]["ID"] + "'";
			} else {
				bttidstr = bttidstr + ",'" + objects[i]["ID"] + "'";
			}
		}

		var bttrows;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/database/execute/sqlcode",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: {
				"sqlcode": "select bttm.id FROM BIO_TQ_TASK btt inner JOIN BIO_TQ_TASK_MX bttm on btt.ID=bttm.TASK_ID where  bttm.TASK_SM_STATUS != '已终止'  and btt.id in (" + bttidstr + ") "
			},
			succeed: function (rs) {
				bttrows = rs.rows;
			}
		});

		var objectMX = [];
		var TASK_SM_STATUS = "已完成";
		for (var j = 0; j < bttrows.length; j++) {
			objectMX.push({
				"ID": bttrows[j]["ID"],
				"TASK_SM_STATUS": TASK_SM_STATUS
			})
		}

		var urlsend = "system/jdbc/save/batch/table";


		var paramsmx = { "tableName": "BIO_TQ_TASK_MX", "objects": objectMX };
		putAddOrUpdata(urlsend, paramsmx, "否", "更新任务明细");


		var urlmain = "system/jdbc/update/one/table/where";
		var paramsupmain = {
			"tableName": "BIO_TQ_TASK",
			"TASK_ENDDATE": sysNowTimeFuncParams["sysNowTime"],
			"TASK_STATUS": "结单",
			"where": { "ID": arrIds }
		};
		putAddOrUpdata(urlmain, paramsupmain, "是", "同步更新任务");
	}

	var sendPM = function (LSM_KEY_P, TASK_TYPE) {
		var p = getJsonByForm("form", pathValue);
		var params;
		if (TASK_TYPE == "组织优化") {
			params = {
				"jiraKey": LSM_KEY_P,
				"updateField": {
					"customfield_19005": { "value": "组织优化结束" },	//提取任务单类型
				}
			};

		} else {
			if (p["TASK_TYPE"] == "基因表达") {
				params = {
					"jiraKey": LSM_KEY_P,
					"updateField": {
						"customfield_19005": { "value": "基因表达结束" },	//提取任务单类型
					}
				};

			} else {
				return;
			}
		}

		var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/api/post/bodyParams",
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
				} else {
					alertMsg("提示:操作失败!");
				}
			},
			failed: function (res) {
				alertMsg("提示:提交保存失败", "error");
			}

		});

	}

	//获取LSM状态
	var getJiraLSM = function (keyinfo) {
		debugger
		var LSMstatus;
		if (keyinfo == "") {
			LSMstatus = null;
			return;
		}
		var url = "http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
		var parmars = { "jiraKey": keyinfo, "fields": ["status"] };
		var inobjjson = { "url": url, "bodyParams": parmars };
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/api/post/bodyParams",
			ajaxData: inobjjson,
			ajaxAsync: false,
			succeed: function (result) {
				debugger;
				if (result["code"] > 0) {
					LSMstatus = result.apiData[0].fields.status.name;

				} else {
					alertMsg("提示:加载获取jira信息失败!");
				}
			}
		});
		return LSMstatus;

	}








	//LSM更新 
	var sendLSM = function (LSM_KEY, TASK_TYPE, QCRESULT) {
		var LSMstatus = getJiraLSM(LSM_KEY);
		var odlStatus = "样品提取检测";
		var newStatus = "等客户反馈";
		var p = getJsonByForm("form", pathValue);
		var params;


		var fmt2 = "yyyy-MM-ddTHH:mm:ss.000+0800";
		var TASK_ENDDATE = toDateFormatByZone(sysNowTimeFuncParams["sysNowTime"], fmt2);
		//////////////////////////////////////////////////////////////////////
		if (LSMstatus == "样品提取检测") {
			params = {
				"jiraKey": LSM_KEY,
				"oldStatusName": odlStatus,
				"statusName": newStatus,
				"updateField": {
					//      "customfield_13226": sysNowTimeFuncParams["sysNowTime"],	//提取完成日期
					"customfield_18811": TASK_ENDDATE,	//提取完成日期
					"customfield_13229": QCRESULT + ""	//合格样品数

				}
			};
		} else if (LSMstatus == "二次提取检测") {
			params = {
				"jiraKey": LSM_KEY,
				"oldStatusName": "二次提取检测",
				"statusName": "二次等客户反馈",
				"updateField": {
					//       "customfield_13227": sysNowTimeFuncParams["sysNowTime"],	//二次提取完成日期
					"customfield_18812": TASK_ENDDATE,	//提取完成日期
					"customfield_13229": QCRESULT + ""	//合格样品数

				}
			};
		} else if (LSMstatus == "三次提取检测") {
			params = {
				"jiraKey": LSM_KEY,
				"oldStatusName": "三次提取检测",
				"statusName": "三次等客户反馈",
				"updateField": {
					//    "customfield_13228": sysNowTimeFuncParams["sysNowTime"],	//三次提取完成日期
					"customfield_18813": TASK_ENDDATE,	//提取完成日期
					"customfield_13229": QCRESULT + ""	//合格样品数

				}
			};
		}

		//////////////////////////////////////////////////////////////////////
		var m = mask(pathValue, "正在推送到jira,请稍等...");
		var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/api/post/bodyParams",
			ajaxData: inobjjson,
			succeed: function (result) {
				unmask(m);
				if (result["code"] > 0) {
					alertMsg("提示:任务单更新及LSM推送成功!");
				} else {
					alertMsg("提示:操作失败!");
				}
			},
			failed: function (res) {
				unmask(m);
				alertMsg("提示:提交保存失败", "error");
			}
		});


	}





	var sendPM2 = function (p) {
		debugger;

		var params;

		params = {
			"jiraKey": p["LSM_KEY_P"],
			"updateField": {
				// "customfield_14202": toDateFormatByZone(p["CUSTOMFIELD_14202"],  "yyyy-MM-dd HH-mm-ss"),	//测序计划完成日期
				"customfield_12101": toDateFormatByZone(p["CUSTOMFIELD_12101"], "yyyy-MM-dd HH-mm-ss"),	//建库标准完成日期
				"customfield_14201": toDateFormatByZone(p["CUSTOMFIELD_14201"], "yyyy-MM-dd HH-mm-ss"),	//建库计划完成日期 
				// "customfield_18820": toDateFormatByZone(p["CUSTOMFIELD_10226"], "yyyy-MM-ddTHH:mm:ss.000+0800"),//实验标准交付日期
				// "customfield_13900": toDateFormatByZone(p["CUSTOMFIELD_13900"],  "yyyy-MM-dd HH-mm-ss") ,	//实验计划交付日期
			}
		};

		var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/api/post/bodyParams",
			ajaxData: inobjjson,
			succeed: function (result) {

			},
			failed: function (res) {
			}
		});
	}


	var callBack = function () {
		refreshGrid();
	};

	var refreshGrid = function () {
		gridNameS = [];
		gridNameS2 = [];

		if (gridNameDGrid) {
			gridNameDGrid.dataSource.read();
		}
		if (gridNameD1Grid) {
			gridNameD1Grid.dataSource.read();
		}
		if (gridNameD2Grid) {
			gridNameD2Grid.dataSource.read();
		}
		if (gridNameD3Grid) {
			gridNameD3Grid.dataSource.read();
		}
	}

	var remove = function () {
		var arrg = [];
		var arrIds = [];
		var obj = [];
		for (var i = 0; i < gridNameS2.length; i++) {
			var arrSubID = getGridSelectData(gridNameS2[i]);
			arrg = arrg.concat(arrSubID);

		}
		if (arrg.length == 0) {
			alertMsg("请至少选择一条数据进行操作!");
			return;
		}
		for (var i = 0; i < arrg.length; i++) {
			arrIds.push(arrg[i]["LIBID"]);
			obj.push({ "ID": arrg[i]["ID"], "TASK_SM_STATUS": "待提取" });
		}
		confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
			var urlsend = "system/jdbc/save/batch/table";
			var paramsadd1 = { "tableName": "BIO_TQ_TASK_MX", "objects": obj };
			putAddOrUpdata(urlsend, paramsadd1, "否", "提交");
			var params = { "tableName": "BIO_DNA_RNA_QC", "ids": arrIds };
			var url = "system/jdbc/delete/batch/table";
			deleteGridDataByIds(url, params, refreshGrid);
		});
	}
	//删除执行单
	var doDelete = function () {
		var arrIds = getSelectData(gridNameD1Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行操作!");
			return;
		}
		confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
			var url = "system/jdbc/delete/one/table/where";
			var params1 = { "tableName": "BIO_DNA_RNA_QC", "where": { "EXE_TQQC_ID": arrIds } };
			deleteGridDataByIds(url, params1, refreshGrid);
			var params2 = { "tableName": "EXE_TQQC_SHEET", "where": { "ID": arrIds } };
			deleteGridDataByIds(url, params2, refreshGrid);
		});
	}
	//核酸编号
	var doGenNo = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS2.length; i++) {
			var arrSubID = getGridSelectData(gridNameS2[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}



		var objectup = [];
		var tasks = [];
		var taskjtnos = [];

		var codes = [];
		var codesdon = [];
		for (var i = 0; i < arrIds.length; i++) {
			//更新记录
			var jtno = 0;
			var taskid = arrIds[i]["TASK_ID"]
			var maxjt = arrIds[i]["DONJT"];
			var dotqnum = arrIds[i]["DOTQN"];
			var smcode = arrIds[i]["SAMPLE_CODE"];

			var indexn = tasks.indexOf(taskid);
			if (maxjt >= 1) {//存在最大编号了
				if (indexn > -1) {
					jtno += taskjtnos[indexn] + 1;
					taskjtnos[indexn] = jtno;
				} else {
					jtno = maxjt + 1;
					tasks.push(taskid);
					taskjtnos.push(jtno);
				}
			} else {
				if (indexn > -1) {
					jtno += taskjtnos[indexn] + 1;
					taskjtnos[indexn] = jtno;
				} else {
					jtno = 1;
					tasks.push(taskid);
					taskjtnos.push(jtno);
				}
			}

			var smn = codes.indexOf(smcode);
			var smno = 1;
			if (smn > -1) {
				smno = codesdon[smn] + 1;
				codesdon[smn] = codesdon[smn] + 1;
			} else {
				if (!dotqnum || dotqnum < 1) {
					dotqnum = 1;
				}
				else {
					dotqnum = dotqnum + 1;
				}
				smno = dotqnum;
				codes.push(smcode);
				codesdon.push(smno);
			}

			objectup.push({
				"ID": arrIds[i]["LIBID"],//关联更新ID
				"DJC_JT_NO": jtno,
				"SAMPLE_GENNO": smcode + "-" + smno //核酸编号
			});

		}

		var paramsup = { "tableName": "BIO_DNA_RNA_QC", "objects": objectup };
		var url = "system/jdbc/save/batch/table";
		putAddOrUpdata(url, paramsup, "是", "提取");
	}

	var setjira = function (arrIds) {
		debugger;
		var arrIds

		var rows1;

		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "query_PD_BIO_TQ_TASK_MX_OTHER_list", "objects": [[arrIds["ID"]]] },
			succeed: function (rs) {
				//console.log(rs);				
				rows1 = rs["rows"];
			}
		});

		var THE_DATA_SUM = 0;
		var type;
		for (var i = 0; i < rows1.length; i++) {
			THE_DATA_SUM += rows1[i]["DATA_SUM"];
			type = rows1[i]["LIBRARY_TYPE_EN"];
		}
		var dwtype = rows1[0]["DATA_UNIT"];
		var cyc_dws = rows1[0]["CYC_DW"];
		var business_unit = rows1[0]["BUSINESS_UNIT"];


		var IS_FULL_LIFE_CYCLE = rows1[0]["IS_FULL_LIFE_CYCLE"];
		var IS_SHORT_PERIOD = "否";
		var IS_FULL_LIFE_CYCLE2 = "否";
		if (IS_FULL_LIFE_CYCLE == "是") {
			IS_FULL_LIFE_CYCLE2 = "是"
		} else {
			var CUSTOMER_VIP = rows1[0]["CUSTOMER_VIP"];
			if (CUSTOMER_VIP.indexOf(2) > -1) {
				IS_SHORT_PERIOD = "是";
			}
		}
		doCyc(type, rows1.length, THE_DATA_SUM, "测序标准用时", dwtype, cyc_dws, business_unit, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);

		doCyc(type, rows1.length, THE_DATA_SUM, "实验交付标准用时", dwtype, cyc_dws, business_unit, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);
		var p = arrIds;
		var time = Date.parse(sysNowTimeFuncParams["sysNowTime"]);
		var customield_10227 = time + (cxdates * 86400000);
		var customield_10226 = time + (sydates * 86400000);
		return {
			"ID": p["LSMID"],
			"MAIN_ID": p["ID"],
			"TASK_JH_ENDDATE": p["TASK_JH_ENDDATE"],
			"MEHOD_TJPLAT": p["MEHOD_TJPLAT"],//提取部门
			"LSM_KEY": p["LSM_KEY"],//LSM关键字
			"LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字 
			"SAMPLE_BATCHNO": p["SAMPLE_BATCHNO"],//LSM关键字
			//"CUSTOMFIELD_13226":p[""],//提取完成日期
			"CUSTOMFIELD_14300": p["TASK_LDATE"],//提取检测标准完成日期--
			"CUSTOMFIELD_12103": time,//建库完成日期
			"CUSTOMFIELD_12101": time,//建库标准完成日期
			"CUSTOMFIELD_14201": time,//建库计划完成日期
			"DATETG": time,//建库审核通过日期

			"CUSTOMFIELD_10227": customield_10227,//测序标准完成日期
			"CUSTOMFIELD_14202": customield_10227,//测序计划完成日期
			"CUSTOMFIELD_10226": customield_10226,//实验标准交付日期
			"CUSTOMFIELD_13900": customield_10226,//实验计划交付日期
			//"CUSTOMFIELD_13254":p[""],//提取检测暂停开始日期
			"CUSTOMFIELD_13237": p["TASK_REMARKS_DES"],//提取具体情况描述
			"sydates": sydates,//实验周期
			"cxdates": cxdates,//测序周期
			// "CUSTOMFIELD_13229"://样品合格情况
		};

	}

	//批量执行插入
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						alertMsg("提示:操作成功!");
						refreshGrid();
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}



	//获取周期定义,推算出截止结果日期
	var doCyc = function (type, countSm, smnumber, dep, sa, cyc_dws, business_unit, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2) {
		debugger;
		//样品提取检测标准用时
		var cycdw = cyc_dws;
		var bus = business_unit;

		if (cycdw == "样品数") {
			flag = 0;
			params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
		} else {
			flag = 1;
			if (sa == "CELL") {
				if (type == "Denovo-Pac(20-30K)") smnumber = smnumber * 140;
				if (type == "Denovo-Pac(hifi)") smnumber = smnumber * 24;
				if (type == "Iso-RNA(Pac)") smnumber = smnumber * 300;
				if (type == "DNA-8K(ONT)") smnumber = smnumber * 80;
				if (type == "Denovo-20k(ONT)") smnumber = smnumber * 80;
				if (type == "Iso-RNA(ONT)") smnumber = smnumber * 140;
			}
			params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
		}


		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			ajaxAsync: false,
			succeed: function (result) {
				if (result["code"] > 0) {
					debugger;
					var rows = result["rows"];
					var m = getMyMonth();
					var dateNumber = 0;
					var seleDateFlag = "工作日";//日历取向
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						seleDateFlag = row["CYC_FLAG"];
						if (m == 1) dateNumber = row["MONTH_1"];
						if (m == 2) dateNumber = row["MONTH_2"];
						if (m == 3) dateNumber = row["MONTH_3"];
						if (m == 4) dateNumber = row["MONTH_4"];
						if (m == 5) dateNumber = row["MONTH_5"];
						if (m == 6) dateNumber = row["MONTH_6"];
						if (m == 7) dateNumber = row["MONTH_7"];
						if (m == 8) dateNumber = row["MONTH_8"];
						if (m == 9) dateNumber = row["MONTH_9"];
						if (m == 10) dateNumber = row["MONTH_10"];
						if (m == 11) dateNumber = row["MONTH_11"];
						if (m == 12) dateNumber = row["MONTH_12"];

						break;
					}
					//执行天数
					saveRemind = 1;
					if (dep == "测序标准用时") {
						cxdates = dateNumber;
					} else {
						sydates = dateNumber;
					}
					// $("#TASK_EXCDAYS" + pathValue).val(dateNumber);
					// $("#CYC_FLAG" + pathValue).val(seleDateFlag);
					// doGetEndDate(seleDateFlag, dateNumber);
				}
			}
		});

	}
	//推算截止日期
	var doGetEndDate = function (seleDateFlag, dateNumber) {

		var thedate = new Date();
		var params = "";
		if (seleDateFlag == "工作日") {
			params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
		} else {
			params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
		}

		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var rows = result["rows"];
					var noDoDateS = [];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
					}
					for (var i = 0; i < dateNumber; i++) {
						var base = 1000 * 60 * 60 * 24;
						//thedate=new Date(thedate.getTime() + base); 
						if (i == 0) {
							var TASK_LLS = paramsValue["TASK_LL"] * 1;
							thedate = new Date(TASK_LLS + (base));
						} else {
							//TASK_LLS=new Date(thedate.getTime() + base);
							thedate = new Date(thedate.getTime() + base);
						}
						for (var j = 0; j < noDoDateS.length; j++) {
							if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
								thedate = new Date(thedate.getTime() + base);//日期向前一天
							}
						}

					}
					//推算出的最终截止日期
					// $("#TASK_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
					// $("#TASK_JH_ENDDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));

				}
			}
		});

	}


	//当前月份
	var getMyMonth = function () {
		var date = new Date;
		var month = date.getMonth() + 1;
		return month;
	}
	//样本状态修改
	var upsmStatus = function () {
		var arrIds = [];
		for (var i = 0; i < gridNameS.length; i++) {
			var arrSubID = getSelectData(gridNameS[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskfcg/upsmstatus/upsmstatus",
			title: "修改样本状态.."
		};
		openWindow(winOpts, { "IDS": arrIds });
	}


	var setjiraStop = function () {
		debugger;
		var arrIds = getGridSelectData(gridNameDGrid);
		if (arrIds.length != 1) {
			alertMsg("请选择一条数据进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskfcg/setjiraStop/setjiraStop",
			title: "jira信息填写..",
			width: 1280,
			height: 480,
			position: { "top": 100, "left": 30 }
		};

		var p = arrIds[0];
		openWindow(winOpts, {
			"ID": p["LSMID"],
			"TASK_TYPE": p["TASK_TYPE"],
			"MAIN_ID": p["ID"],
			"TASK_JH_ENDDATE": p["TASK_JH_ENDDATE"],
			"SAMPLE_BATCHNO": p["SAMPLE_BATCHNO"],//批次号
			"LSM_KEY": p["LSM_KEY"],//LSM关键字
			"LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字
			//"CUSTOMFIELD_13226":p[""],//提取完成日期
			"CUSTOMFIELD_14300": p["TASK_LDATE"],//提取检测标准完成日期--
			//"CUSTOMFIELD_13254":p[""],//提取检测暂停开始日期
			"CUSTOMFIELD_13237": p["TASK_REMARKS_DES"],//提取具体情况描述
			// "CUSTOMFIELD_13229"://样品合格情况
		});

	}
	//任务单状态修改
	var doTaskStatus = function () {
		debugger;
		var arrIds = getGridSelectData(gridNameDGrid);
		if (arrIds.length != 1) {
			alertMsg("请选择一条记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/tq/taskfcg/uptaskstatus/uptaskstatus",
			title: "修改任务单状态.."
		};

		openWindow(winOpts, {
			"IDS": [arrIds[0]["ID"]], "LSM_KEY": arrIds[0]["LSM_KEY"], "CUSTOMFIELD_13229": arrIds[0]["QCRESULT"],
			"SAMPLE_BATCHNO": arrIds[0]["SAMPLE_BATCHNO"]
		});
	}

	funcPushs(pathValue, {
		"setjiraStop": setjiraStop,
		"initData": initData,
		"init": init,
		"addToEx": addToEx,
		"batchSettlement": batchSettlement,
		"doTaskStatus": doTaskStatus,
		"upsmStatus": upsmStatus,
		"edit": edit,
		"doOK": doOK,
		"doReturn": doReturn,
		"submit": submit,
		"doUpdate": doUpdate,
		"doReturn2": doReturn2,
		"doGenNo": doGenNo,
		"remove": remove,
		"doDelete": doDelete,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"setjira": setjira,
	});
});