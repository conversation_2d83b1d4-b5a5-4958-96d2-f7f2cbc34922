$(document).ready(function() {
     var pathValue="biomarker-dispatch-tq-taskrna-doMark-doMark";
    var paramsValue;   
    var initData=function(){
        return {
            tableName:"BIO_TQ_TASK"
        };
    }
    var init=function(params){
    	paramsValue=params;
    }

  var submit=function(){
    	debugger;
        var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
        if (validator.validate()) {} else {
            alertMsg("验证未通过","wran");
            return ;
        }
    	var ids=paramsValue["IDS"];
    	var jsonData = getJsonByForm("form",pathValue);
    	
    	 var object=[];
         for(var i=0;i < ids.length;i++){
        	 object.push({
                     "ID":ids[i],
                      "TASK_REMARKS_DES":jsonData["TASK_REMARKS_DES"]
                   });
         }
	
         var url="system/jdbc/save/batch/table";
	 var paramsuplane={"tableName":"BIO_TQ_TASK","objects":object};
	 putAddOrUpdata(url,paramsuplane,"是","更新");
    }


//批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提示:操作成功!");
             		 refreshGrid();
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
  
    funcPushs(pathValue,{
        "init":init,
        "submit":submit
    });
 
 });