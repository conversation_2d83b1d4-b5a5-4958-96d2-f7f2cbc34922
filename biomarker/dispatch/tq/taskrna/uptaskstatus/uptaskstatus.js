$(document).ready(function () {
	var pathValue = "biomarker-dispatch-tq-taskrna-uptaskstatus-uptaskstatus";
	var paramsValue;
	var PCHNumber = 0;

	var LSMstatus;//LSM

	var initData = function () {
		return {
			tableName: "BIO_TQ_TASK"
		};
	}
	var init = function (params) {
		paramsValue = params;
		getInfo("form", pathValue, params);
		var url = "system/jdbc/query/info/" + initData().tableName;
		getInfo("form", pathValue, params, url, function (p, v) {
			// queryCheckPCNumber(paramsValue["SAMPLE_BATCHNO"]);
			getJiraLSM(paramsValue["LSM_KEY"]);
		});
	}

	var subUpData = function () {
		var ids = paramsValue["IDS"];
		var object = [];
		var jsonData = getJsonByForm("form", pathValue);//获取表单json
		var bttidstr = "";
		for (var i = 0; i < ids.length; i++) {
			object.push($.extend({}, jsonData, { "ID": ids[i] }));//表单值继承

			if (bttidstr.length == 0) {
				bttidstr = "'" + ids[i] + "'";
			} else {
				bttidstr = bttidstr + ",'" + ids[i] + "'";
			}
		}
		var objectMX = [];
		var bttrows;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/database/execute/sqlcode",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: {
				"sqlcode": "select bttm.id FROM BIO_TQ_TASK btt inner JOIN BIO_TQ_TASK_MX bttm on btt.ID=bttm.TASK_ID where bttm.TASK_SM_STATUS != '已终止'  and  btt.id in (" + bttidstr + ") "
			},
			succeed: function (rs) {
				bttrows = rs.rows;
			}
		});
		var TASK_SM_STATUS = "已完成";
		if (jsonData["TASK_STATUS"] == "终止") { TASK_SM_STATUS = "已终止"; }
		for (var j = 0; j < bttrows.length; j++) {
			objectMX.push({
				"ID": bttrows[j]["ID"],
				"TASK_SM_STATUS": TASK_SM_STATUS
			})
		}



		var urlsend = "system/jdbc/save/batch/table";

		var paramsmx = { "tableName": "BIO_TQ_TASK_MX", "objects": objectMX };
		putAddOrUpdata(urlsend, paramsmx, "否", "更新任务明细");
		var paramsadd = { "tableName": "BIO_TQ_TASK", "objects": object };
		putAddOrUpdata(urlsend, paramsadd, "是", "更新");
	}

	//批量执行插入
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						sendLSM();
						// alertMsg("提示:操作成功!");
						funcExce(pathValue + "pageCallBack");
						funcExce(pathValue + "close");
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}


	var sendLSM = function () { debugger
		var odlStatus = "样品提取检测";
		var newStatus = "等客户反馈";
		var p = getJsonByForm("form", pathValue);
		var params;


		var fmt2 = "yyyy-MM-ddTHH:mm:ss.000+0800";
        var TASK_ENDDATE = toDateFormatByZone(p["TASK_ENDDATE"], fmt2);
		//////////////////////////////////////////////////////////////////////
		if (LSMstatus == "样品提取检测") {
			params = {
				"jiraKey": p["LSM_KEY"],
				"oldStatusName": odlStatus,
				"statusName": newStatus,
				"updateField": {
					"customfield_13226": p["TASK_ENDDATE"],	//提取完成日期
					"customfield_18811": TASK_ENDDATE,	//提取完成日期
					"customfield_13229": p["CUSTOMFIELD_13229"]	//合格样品数

				}
			};
		} else if (LSMstatus == "二次提取检测") {
			params = {
				"jiraKey": p["LSM_KEY"],
				"oldStatusName": "二次提取检测",
				"statusName": "二次等客户反馈",
				"updateField": {
					"customfield_13227": p["TASK_ENDDATE"],	//二次提取完成日期
					"customfield_18812": TASK_ENDDATE,	//提取完成日期
					"customfield_13229": p["CUSTOMFIELD_13229"]	//合格样品数

				}
			};
		} else if (LSMstatus == "三次提取检测") {
			params = {
				"jiraKey": p["LSM_KEY"],
				"oldStatusName": "三次提取检测",
				"statusName": "三次等客户反馈",
				"updateField": {
					"customfield_13228": p["TASK_ENDDATE"],	//三次提取完成日期
					"customfield_18813": TASK_ENDDATE,	//提取完成日期
					"customfield_13229": p["CUSTOMFIELD_13229"]	//合格样品数

				}
			};
		}
		//////////////////////////////////////////////////////////////////////

		var m = mask(pathValue, "正在推送到jira,请稍等...");
		var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/api/post/bodyParams",
			ajaxData: inobjjson,
			succeed: function (result) {
				unmask(m);
				if (result["code"] > 0) {
					alertMsg("提示:任务单更新及LSM推送成功!");
				} else {
					alertMsg("提示:操作失败!");
				}
			},
			failed: function (res) {
				unmask(m);
				alertMsg("提示:提交保存失败", "error");
			}
		});

	}
	var queryCheckPCNumber = function (PCH) {
		debugger;
		var params = { "query": "checkPsTqCount", "objects": [[PCH]] };
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				debugger;
				var mynumber = 0;
				if (result["code"] > 0) {
					var rows = result["rows"];
					if (rows.length > 0) {
						var row = rows[0];
						PCHNumber = row["CHECKNUMBER"];
						$("#PCHNumber" + pathValue).val(PCHNumber);
					}
				}

			}
		});
	}

	//获取LSM状态
	var getJiraLSM = function (keyinfo) {
		debugger
		if (keyinfo == "") {
			LSMstatus = null;
			return;
		}
		var url = "http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
		var parmars = { "jiraKey": keyinfo, "fields": ["status"] };
		var inobjjson = { "url": url, "bodyParams": parmars };
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: "system/api/post/bodyParams",
			ajaxData: inobjjson,
			ajaxAsync: false,
			succeed: function (result) {
				debugger;
				if (result["code"] > 0) {
					LSMstatus = result.apiData[0].fields.status.name;
				} else {
					alertMsg("提示:加载获取jira信息失败!");
				}
			}
		});

	}

	var submit = function () {
		subUpData();
	}

	funcPushs(pathValue, {
		"init": init,
		"submit": submit,
	});

});