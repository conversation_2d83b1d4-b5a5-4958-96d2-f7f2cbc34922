$(document).ready(function () {
    var pathValue = "biomarker-dispatch-tq-taskrna-pdup-pdup";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "EXE_TQQC_SHEET"
        };
    }
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
        $("#EX_TYPE" + pathValue).val(paramsValue["EX_TYPE"]);
        $("#EX_RE_STATUS" + pathValue).val("待审核");
    }

    var subUpData = function () {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = $("#EX_MAN" + pathValue).val();
        $("#EX_MAN2_ID" + pathValue).val($("#EX_MAN_ID" + pathValue).val());
        $("#EX_MAN2" + pathValue).val(username);

        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    //提交成功
                    var ids = paramsValue["IDS"];
                    var taskids = paramsValue["TASKIDS"];
                    var codes = paramsValue["CODES"];
                    var faskdate = paramsValue["FASKDATE"];
                    var tqffs = paramsValue["TQFFS"];
                    var objectadd = [];
                    var objectup = [];
                    var objectupmx = [];
                    for (var i = 0; i < ids.length; i++) {
                        objectadd.push({
                            "TASK_TQ_ID": ids[i],//联联任务ID
                            "EXE_TQQC_ID": result["ID"],//关联执行单
                            "SAMPLE_CODE": codes[i],//样本编号
                            "EXTDR_METHOD": tqffs[i],//提取方法
                            "TQ_MAN": username,//提取实验员
                            //"DJC_MAN":username,//检测实验员
                            "TQ_ST_DATE": time//开始提取日期
                        });
                        objectupmx.push({ "ID": ids[i], "TASK_SM_STATUS": "提取中" });
                    }
                    for (var i = 0; i < taskids.length; i++) {
                        if (faskdate == null || faskdate == "" || faskdate == "undefined") {
                            objectup.push({
                                "ID": taskids[i],
                                "TASK_STATUS": "提取中",
                                "TASK_FIRSTDATE": time,
                                "SYS_MAN_L": username,
                                "SYS_INSERTTIME_L": time
                            });
                        } else {
                            objectup.push({
                                "ID": taskids[i],
                                "TASK_STATUS": "提取中",
                                "SYS_MAN_L": username,
                                "SYS_INSERTTIME_L": time
                            });
                        }
                    }
                    //执行添加到文库
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsadd = { "tableName": "BIO_DNA_RNA_QC", "objects": objectadd };
                    putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

                    var paramsup2 = { "tableName": "BIO_TQ_TASK_MX", "objects": objectupmx };
                    putAddOrUpdata(urlsend, paramsup2, "否", "提取中");

                    var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "提取中");


                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    

                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });
    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var submit = function () {
        subUpData();
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});