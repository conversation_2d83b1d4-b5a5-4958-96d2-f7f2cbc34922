$(document).ready(function() {
    var pathValue="biomarker-dispatch-tq-taskrna2-editjc-editjc";
    var paramsValue;
        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"BIO_TQ_TASK_MX"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
       paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
 

//保存方式
var subUpData = function() {
     debugger;
	var ids = paramsValue["IDS"];
        var YN = paramsValue["YN"];
	var jsonData = getJsonByForm("form", pathValue); //获取表单json
	var object = [];
	var objectyf = [];
	for (var i = 0; i < ids.length; i++) {
          if(YN[i] == "是"){
		objectyf.push($.extend({}, jsonData, {
			"ID": ids[i],
			"CKDR_METHOD":jsonData["CKDR_METHOD"]
		})); //表单值继承
            }else{
		object.push($.extend({}, jsonData, {
			"ID": ids[i],
			"CKDR_METHOD":jsonData["CKDR_METHOD"]
		})); //表单值继承
            }
          
          
	}
       if(objectyf.length > 0){
	//执行更新
	var params = {
		"tableName": "BIO_RD_TASK_DNA_RNA_QC",
		"objects": objectyf
	};

	//插入任务明细记录
	var url = "system/jdbc/save/batch/table";
	$.fn.ajaxPost({
		ajaxType: "post",
		ajaxUrl: url,
		ajaxData: params,
		succeed: function(result) {

			if (result["code"] > 0) { //成功保存后执行流程提交
			} else {
				console.log(result);
			}
		}
	});
}
 if(object.length > 0){
	//执行更新
	var params = {
		"tableName": "BIO_TQ_TASK_MX",
		"objects": object
	};

	//插入任务明细记录
	var url = "system/jdbc/save/batch/table";
	$.fn.ajaxPost({
		ajaxType: "post",
		ajaxUrl: url,
		ajaxData: params,
		succeed: function(result) {

			if (result["code"] > 0) { //成功保存后执行流程提交

			} else {
				console.log(result);
			}
		}
	});
}
funcExce(pathValue + "pageCallBack"); //父执行回调

				//console.log(result);

				alertMsg("提交成功!");

				funcExce(pathValue + "close"); //关闭页面
}


    funcPushs(pathValue,{
        "init":init,
        "subUpData":subUpData
    });
 
 });