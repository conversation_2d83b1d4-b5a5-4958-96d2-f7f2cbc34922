$(document).ready(function() {
   var pathValue="biomarker-dispatch-tq-task-poolsamlist-poolsamlist";
   
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var gridNameGrid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            width:900,
            height:360,
            read:{"query":"query_sample_N_list","objects":[params["ID"]]}
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
   }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
     });
});