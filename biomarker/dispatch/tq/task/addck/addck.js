$(document).ready(function () {
    var pathValue = "biomarker-dispatch-tq-task-addck-addck";
    var initData = function () {
        return {
            tableName: "BIO_TQ_TASK"
        };
    }
    var paramsValue;
    var gridNameGrid;
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);

        //设表单固定值
        $("#TASK_TYPE" + pathValue).val(params["TASK_TYPE"]);
        var toolbar = getButtonTemplates(pathValue, [
            // {name:"edit",target:"doSetff",title:"设置方法库.."},
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            height: fullh - 300,
            toolbar: toolbar,
            read: { "query": "query_BIO_TQ_TASK_MX_list", "objects": [params["ID"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "ISPOOLSM") {
                        var template = "# if( ISPOOLSM=='混' ){ # <font onclick=\"funcExce('" + pathValue + "openpool','#= ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }" +
                            " else if( ISPOOLSM=='拆' ){ # <font onclick=\"funcExce('" + pathValue + "openpool','#= SM_SPLIT_ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }else{ #  #=ISPOOLSM# # } #";
                        setJsonParam(cols[i], "template", template);
                    }

                }

            },
            fetch: function (data) {
                $("#TASK_SAMPLESUM" + pathValue).val(data.length);//记录数YY


                var type = "";
                var dataSum = $("#ZL_SUM" + pathValue).val();
                var smSum = $("#SAMPLE_SUM" + pathValue).val();
                var avgsum = 0;
                var upparmas = [];
                if (dataSum > 0 && smSum > 0) avgsum = dataSum / smSum;
                var THE_DATA_SUM = 0;
                for (var i = 0; i < data.length; i++) {
                    var row = data[i];
                    $("#MEHOD_TJPLAT" + pathValue).val(row["MEHOD_TJPLAT"]);//提取部门
                    if (row["BUSINESS_UNIT"]) {
                        $("#BUSINESS_UNIT" + pathValue).val(row["BUSINESS_UNIT"]);
                        $("#CYC_DW" + pathValue).val(row["CYC_DW"]);
                        $("#MEHOD_TJPLAT" + pathValue).val(row["MEHOD_TJPLAT"]);
                        $("#PROJECT_DEPT" + pathValue).val(row["MEHOD_JKPLAT"]);
                        // $("#TASK_PLAT"+pathValue).val(row["SEQ_PLAT"]);
                        type = row["LIBRARY_TYPE_EN"];
                    }
                    //根据提取流程和检测流程置 任务单类别
                    //DNA提取/RNA提取/非常规提取/核酸检测
                    if (row["MEHOD_PLAT"] == "RNA提取") {
                        $("#TASK_TYPE_LB" + pathValue).val("RNA提取");
                    } else if (row["MEHOD_PLAT"] == "DNA提取") {
                        $("#TASK_TYPE_LB" + pathValue).val("DNA提取");
                    } else if (row["MEHOD_PLAT"] == "-" || row["MEHOD_PLAT"] == "" || row["MEHOD_PLAT"] == null) {
                        $("#TASK_TYPE_LB" + pathValue).val("核酸检测");
                    } else if (row["MEHOD_PLAT"] == "非常规提取") {
                        $("#TASK_TYPE_LB" + pathValue).val("非常规提取");
                    }

                    THE_DATA_SUM += row["DATA_SUM"];

                    //根据合同数据量,统计并转换赋给本期数据量M  Reads数（M）=合同数据量（备注，单位是G）/0.3
                    /**
                    var the_ata=0;
                    if(avgsum>0){
                        if(row["DATA_UNIT"]&&row["DATA_UNIT"].indexOf("G")>-1){
                            THE_DATA_SUM=+avgsum/0.3;
                            the_ata=avgsum/0.3;
                        }else{
                            THE_DATA_SUM=+avgsum;
                            the_ata=avgsum;
                        }
                    }
                    upparmas.push({
                        "ID":row["ID"],
                        "DATA_SUM":avgsum,
                        "THE_DATA_SUM":the_ata.toFixed(2)
                    });
                    **/
                }

                //var url="system/jdbc/save/batch/table";
                // var paramsadd={"tableName":"BIO_TQ_TASK_MX","objects":upparmas};
                //putAddOrUpdata(url,paramsadd,"否","");
                //两位小数
                //THE_DATA_SUM=THE_DATA_SUM.toFixed(2);


                $("#TASK_LIB_TYPE" + pathValue).val(type);
                $("#THE_DATA_SUM" + pathValue).val(THE_DATA_SUM.toFixed(4));

                //	doCyc(type,data.length,THE_DATA_SUM,"样品提取检测标准用时");
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
    }

    var submit = function () { 

        var paramsF = getJsonByForm("form", pathValue);
        var paramsUpTask = [{ "ID":paramsF["ID"] ,"LSM_KEY":paramsF["LSM_KEY"] , "LSM_KEY_P":paramsF["LSM_KEY_P"] } ];
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_TQ_TASK", "objects": paramsUpTask };
        putAddOrUpdata(urlsend, paramsup, "否", "提交");
        alertMsg("jira_LSM关键字与jira_项目关键字保存成功!");
        funcExce(pathValue+"pageCallBack");//执行回调
        funcExce(pathValue+"close");//关闭页面
         return;
         
    }
    //方法库
    var doSetff = function () {
        var arrIds = getSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/tq/task/setff/setff",
            title: "设置方法库..",
            currUrl: replacePathValue(pathValue)
        };
        openWindow(winOpts, { "IDS": arrIds, "PRODUCT_TYPE": $("#PRODUCT_TYPE" + pathValue).val() });
    }




    //获取周期定义,推算出截止结果日期
    var doCyc = function (type, countSm, smnumber, dep) {
        debugger;
        //样品提取检测标准用时
        var cycdw = $("#CYC_DW" + pathValue).val();
        var bus = $("#BUSINESS_UNIT" + pathValue).val();

        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm] };
        } else {
            flag = 1;
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber] };
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    debugger;
                    var rows = result["rows"];
                    var m = getMyMonth();
                    var dateNumber = 0;
                    var seleDateFlag = "工作日";//日历取向
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        seleDateFlag = row["CYC_FLAG"];
                        if (m == 1) dateNumber = row["MONTH_1"];
                        if (m == 2) dateNumber = row["MONTH_2"];
                        if (m == 3) dateNumber = row["MONTH_3"];
                        if (m == 4) dateNumber = row["MONTH_4"];
                        if (m == 5) dateNumber = row["MONTH_5"];
                        if (m == 6) dateNumber = row["MONTH_6"];
                        if (m == 7) dateNumber = row["MONTH_7"];
                        if (m == 8) dateNumber = row["MONTH_8"];
                        if (m == 9) dateNumber = row["MONTH_9"];
                        if (m == 10) dateNumber = row["MONTH_10"];
                        if (m == 11) dateNumber = row["MONTH_11"];
                        if (m == 12) dateNumber = row["MONTH_12"];

                        break;
                    }
                    //执行天数
                    saveRemind = 1;
                    $("#TASK_EXCDAYS" + pathValue).val(dateNumber);
                    $("#CYC_FLAG" + pathValue).val(seleDateFlag);
                    doGetEndDate(seleDateFlag, dateNumber);
                }
            }
        });

    }
    //推算截止日期
    var doGetEndDate = function (seleDateFlag, dateNumber) {

        var thedate = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    for (var i = 0; i < dateNumber; i++) {
                        var base = 1000 * 60 * 60 * 24;
                        thedate = new Date(thedate.getTime() + base);
                        for (var j = 0; j < noDoDateS.length; j++) {
                            if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                thedate = new Date(thedate.getTime() + base);//日期向前一天
                            }
                        }

                    }
                    //推算出的最终截止日期
                    $("#TASK_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));

                }
            }
        });

    }


    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }

    var openpool = function (ID) {
        var winOpts = {
            url: "biomarker/dispatch/tq/task/poolsamlist/poolsamlist",
            title: "混样列表...",
            width: 900,
            height: 380,
            currUrl: replacePathValue(pathValue),
            position: { "top": 150, "left": 250 }
        };
        openWindow(winOpts, { "ID": ID });
    }

    var doJG = function () {


        var paramsF = getJsonByForm("form", pathValue);
        //取出表单值
        var passFlag = paramsF["TASK_COMFIRM_RESULT"];
        var flagstr = "";
        var flagstr2 = "";
        if (passFlag == "通过") {
            flagstr = "已审核";
            flagstr2 = "已审核";
        } else if (passFlag == "退回") {
            flagstr = null;
            flagstr2 = "退回";

        } else if (passFlag == "终止") {
            flagstr = "终止";
            flagstr2 = "已终止";
        }

        $("#DD_TASK_STATUS" + pathValue).val(flagstr);
        $("#TASK_STATUS" + pathValue).val(flagstr2);

    }

    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        debugger;
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                debugger;
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        refreshGrid();
                        //----
                    } else if (isDoCallBack == "关") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                    //------
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read({
                "objects": [paramsValue["ID"]]
            });
        }
    }

    //LSM更新 
    var sendLSM = function () {
        var odlStatus = "样品提取检测";
        var newStatus = "等客户反馈";
        var p = getJsonByForm("form", pathValue);
        var params;
        if (PCHNumber == 0) {
            params = {
                "jiraKey": p["LSM_KEY"],
                //  "oldStatusName":odlStatus,
                //  "statusName":newStatus,
                "updateField": {
                    "customfield_14300": p["TASK_LDATE"],	//提取检测标准完成日期
                    "customfield_14500": p["TASK_JH_ENDDATE"]	//提取检测预计合格日期
                }
            };
        }
        var m = mask(pathValue, "正在推送到jira,请稍等...");
        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    alertMsg("提示:LSM推送成功!");
                } else {
                    alertMsg("提示:操作失败!");
                }
            },
            failed: function (res) {
                alertMsg("提示:提交保存失败", "error");
            }
        });

    }


    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "doSetff": doSetff,
        "refreshGrid": refreshGrid,
        "submit": submit,
        "callBack": callBack,
        "doJG": doJG,
        "openpool": openpool,
    });

});