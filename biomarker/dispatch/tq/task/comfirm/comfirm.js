$(document).ready(function() {
     var pathValue="biomarker-dispatch-tq-task-comfirm-comfirm";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_TQ_TASK"
        };
    }
    var init=function(params){
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);//传入id
        $("#TASK_CDATE"+pathValue).val(sysNowTimeFuncParams["sysNowTime"]);
    }

    
  var comfireData=function(){

	  doGetEndDate(
			  $("#CYC_FLAG"+pathValue).val(),
			  $("#TASK_EXCDAYS"+pathValue).val()
	 );
	 
  }
  
  var comfireData2=function(){
      var validator=$("#form"+pathValue).kendoValidator(getValidateJson({})).data("kendoValidator");
      if (validator.validate()) {} else {
          alertMsg("验证未通过","wran");
          return ;
      }
      
    	var ids=paramsValue["IDS"];    	
    	var object=[];
    	var paramsF = getJsonByForm("form",pathValue);
    	 //取出表单值
    	 var passFlag=paramsF["TASK_COMFIRM_RESULT"];
    	 var backmsg=paramsF["TASK_BACK"];
    	 var typelb=paramsF["TASK_TYPE_LB"];
        var name=getLimsUser()["name"];
    	var time=sysNowTimeFuncParams["sysNowTime"];
    	var TASK_JH_ENDDATE=paramsF["TASK_JH_ENDDATE"];
    	var LSMKEYS=paramsValue["LSMKEY"];
    	var LSMKEYPS=paramsValue["LSMKEYP"];
    	var TASK_LDATE=paramsF["TASK_LDATE"];
    	
    	 var flagstr="";
    	 var flagstr2="";
    	 if(passFlag=="通过"){
    		 flagstr="已审核";
    		 flagstr2="已审核";
    	 }else if(passFlag=="退回"){
    		 flagstr=null;
    		 flagstr2="退回";
    		 
    	 }else if(passFlag=="终止"){
    		 flagstr="终止";
    		 flagstr2="已审核";
    	 }
    	 
          for(var i=0;i < ids.length;i++){
        	 object.push({"ID":ids[i],
        		 "DD_TASK_STATUS":flagstr,
        		 "TASK_STATUS":flagstr2,
        		 "TASK_TYPE_LB":typelb,
        		 "TASK_CDATE":time,
        		 "TASK_JH_ENDDATE":TASK_JH_ENDDATE,
        		 "TASK_BACK":backmsg,
        		 "TASK_LDATE":TASK_LDATE});
         }
         
         //执行更新
         var params={"tableName":"BIO_TQ_TASK","objects":object};

         //插入任务明细记录
         var url="system/jdbc/save/batch/table";
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:url,
             ajaxData:params,
             succeed:function(result){
                 if(result["code"]>0){
                	 if(passFlag=="通过"){
                		 doJira(ids,LSMKEYS,LSMKEYPS,TASK_JH_ENDDATE);
                	 }
                         alertMsg("提交成功!");
                         funcExce(pathValue+"pageCallBack");
                         funcExce(pathValue+"close");
                 }else{
                 	console.log(result);
                 }
             }
         });    	
    }


  var doJira=function(IDS,keys1,keys2,yDate){
      var params=[];
      var params2=[];
      for(var i=0;i<IDS.length;i++){
    	  params.push({
    		          "jiraKey":keys1[i],
    		          "oldStatusName":"样本准备",
    		          "statusName":"样品提取检测",
    		          "updateField":{    		              
    		              "customfield_14200":yDate// 提取检测计划完成时间
    		           }
    		});
    	  params2.push({
	          "jiraKey":keys2[i],
	          "oldStatusName":"样本准备",
	          "statusName":"样品提取检测",
	          "updateField":{    		              
	              "customfield_14200":yDate// 提取检测计划完成时间
	           }
    	  });
       }
      for(var i=0;i<IDS.length;i++){
        putToJira(params[i]);
        putToJira(params2[i]);
     }
    }
    var putToJira=function(params){
        var inobjjson={ "url":"http://"+JIRRA_URL+"/api/jira/sysInfo", "bodyParams": params };
       	$.fn.ajaxPost({
               ajaxType:"post",
               ajaxUrl:"system/api/post/bodyParams",
               ajaxData:inobjjson,
               succeed:function(result){
                   if(result["code"]>0){
                       if(result.apiData.flag=="true"||result.apiData.flag){
                       	  alertMsg(result.apiData.message);
                          }else{
                               alertMsg("提示:推送失败(<font color=#ff0000>"+result.apiData.message+"</font>)!");
                         }
                    }else{
                  	 alertMsg(errMsg+"操作失败!");
                   }
               }
           });
    }

    //推算截止日期
    var doGetEndDate=function(seleDateFlag,dateNumber){
        alert(seleDateFlag+"::"+dateNumber);
       var thedate = new Date();
       var params="";
       if(seleDateFlag=="工作日"){
    	   params={"query":"getCalendarInfo","objects":[thedate.getFullYear(),toDateFormatByZone(thedate,"yyyy-MM-dd")],"search":{"D_WEEKDAY_REMOVAL":"是"}};//取得当前日期后一年内所有的“工作日”排除日期
       }else{
    	   params={"query":"getCalendarInfo","objects":[thedate.getFullYear(),toDateFormatByZone(thedate,"yyyy-MM-dd")],"search":{"D_NATURAL_REMOVAL":"是"}};//取得当前日期后一年内所有的“自然日”排除日期
       }
      
   	   $.fn.ajaxPost({
   	        ajaxUrl:"system/jdbc/query/one/table",
   	        ajaxType: "post",
   	        ajaxData: params,
   	        succeed:function(result){
   	        	if(result["code"]>0){
  	   	        	var rows=result["rows"];
  	   	        	var noDoDateS=[];
  		   	        for(var i=0;i<rows.length;i++){
  		   	        	var row=rows[i];
  		   	        	noDoDateS.push(toDateFormatByZone(row["D_DATE"],"yyyy-MM-dd"));
  		   	        }
  		   	        for(var i=0;i<dateNumber;i++){
  		   	        	var base = 1000 * 60 * 60 * 24;
  		   	        	thedate=new Date(thedate.getTime() + base);
  		   	        	for(var j=0;j<noDoDateS.length;j++){
	  		   	        	if(toDateFormatByZone(thedate,"yyyy-MM-dd")==noDoDateS[j]){//存在排除日期测
	  		   	        	  thedate=new Date(thedate.getTime()+base);//日期向前一天
	  		   	        	}
  		   	        	}
  		   	        	
  		   	        }
  		   	        //推算出的最终截止日期
  		   	       $("#TASK_LDATE"+pathValue).val(toDateFormatByZone(thedate,"yyyy-MM-dd"));
  		   	       
  		   	       comfireData2();//执行提交
  		   	       
   	        	}
   	        }
   	   });
   	   
    }
    
    //当前月份
    var getMyMonth=function (){
		 var date=new Date;
		 var month=date.getMonth()+1;
		 return month;
	}
    funcPushs(pathValue,{
        "init":init,
        "comfireData":comfireData
    });
 
 });