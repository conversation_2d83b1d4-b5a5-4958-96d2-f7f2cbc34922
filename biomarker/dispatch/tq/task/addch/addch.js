$(document).ready(function() {
    var pathValue="biomarker-dispatch-tq-task-addch-addch";
        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"BIO_TQ_TASK"
        };
    }
	
	var paramsValue;
	var gridNameGrid;
	
	

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
		paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        //设表单固定值
        $("#TASK_TYPE"+pathValue).val(params["TASK_TYPE"]);
        
		        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
       ]);//工具条
       //请求参数
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           height: fullh-300,
           toolbar: toolbar,
           read:{"query":"query_BIO_CH_MX_list","objects":[params["ID"]]},
		   headerFilter:function(cols,i){
                if(i){
                    if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_CODE"){
                        setJsonParam(cols[i],"template",getTemplate("#= SAMPLE_CODE #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                    }
                }
            }
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
    }
	


    var open=function(ID){
        var winOpts={
            url:"biomarker/operation/tq/task/estqmx/estqmx",
            title:"修改:任务明细..",
			currUrl:replacePathValue(pathValue)
        };
        openWindow(winOpts,{"ID":ID,"TASK_ID":paramsValue["ID"]});//传递id
    }

    var edit=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
		open(arrIds[0]);
     }
     
     var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        //funcExce(pathValue+"close");//关闭页面
						getInfo("form",pathValue,{ID:result["ID"]});//传入id
                        paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
    	 if(gridNameGrid){
             gridNameGrid.dataSource.read({
             	"objects":[paramsValue["ID"]]
             });//重新读取--刷新
         }
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_CH_TASK_MX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }

    //表格导入
    var importData=function(componentId){
        var grid=gridNameGrid;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":grid,//单表导入
                "tableName":"BIO_CH_TASK_MX",
            }
        });
    }
	

	funcPushs(pathValue,{
		"initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
		"init":init,//初始化方法-在加载完初始化数据之后执行
		"open":open,
		"edit":edit,
		"refreshGrid":refreshGrid,
		"deleteInfo":deleteInfo,
		"submit":submit,//提交方法
		"callBack":callBack,//回调方法
		"importData":importData,
	});
 
 });