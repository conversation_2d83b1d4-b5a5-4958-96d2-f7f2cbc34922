$(document).ready(function () {
    var pathValue = "biomarker-dispatch-tq-task-add-add";
    var initData = function () {
        return {
            tableName: "BIO_TQ_TASK"
        };
    }
    var paramsValue;
    var gridNameGrid;
    //对同一任务单号进行文库产生计数器
    var order = [];
    var order_mn = [];
    var ordersm = [];
    var ordersm_mn = [];
    var ordersm_no = [];
    var ordersm_no_mn = [];
    var typest;
    var init = function (params) {
        debugger;
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);

        //设表单固定值
        $("#TASK_TYPE" + pathValue).val(params["TASK_TYPE"]);

        typest = params["TASK_TYPE"];
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doSetff", title: "设置方法库.." },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            height: fullh - 300,
            toolbar: toolbar,
            size: 5000,
            read: { "query": "query_BIO_TQ_TASK_MX_list", "objects": [params["ID"]] },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "ISPOOLSM") {
                        var template = "# if( ISPOOLSM=='混' ){ # <font onclick=\"funcExce('" + pathValue + "openpool','#= ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }" +
                            " else if( ISPOOLSM=='拆' ){ # <font onclick=\"funcExce('" + pathValue + "openpool','#= SM_SPLIT_ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }else{ #  #=ISPOOLSM# # } #";
                        setJsonParam(cols[i], "template", template);
                    }

                }

            },
            fetch: function (data) {
                $("#TASK_SAMPLESUM" + pathValue).val(data.length);//记录数YY


                var type = "";
                var dataSum = $("#ZL_SUM" + pathValue).val();
                var smSum = $("#SAMPLE_SUM" + pathValue).val();
                var avgsum = 0;
                var upparmas = [];
                if (dataSum > 0 && smSum > 0) avgsum = dataSum / smSum;
                var THE_DATA_SUM = 0;
                for (var i = 0; i < data.length; i++) {
                    var row = data[i];
                    $("#MEHOD_TJPLAT" + pathValue).val(row["MEHOD_TJPLAT"]);//提取部门
                    if (row["BUSINESS_UNIT"]) {
                        $("#BUSINESS_UNIT" + pathValue).val(row["BUSINESS_UNIT"]);
                        $("#CYC_DW" + pathValue).val(row["CYC_DW"]);
                        $("#MEHOD_TJPLAT" + pathValue).val(row["MEHOD_TJPLAT"]);
                        $("#PROJECT_DEPT" + pathValue).val(row["MEHOD_JKPLAT"]);
                        //    $("#TASK_PLAT" + pathValue).val(row["SEQ_PLAT"]);
                        type = row["LIBRARY_TYPE_EN"];
                    }
                    //根据提取流程和检测流程置 任务单类别
                    //DNA提取/RNA提取/非常规提取/核酸检测
                    // if (row["MEHOD_PLAT"] == "RNA提取") {
                    // 	$("#TASK_TYPE_LB" + pathValue).val("RNA提取");
                    // } else if (row["MEHOD_PLAT"] == "DNA提取") {
                    // 	$("#TASK_TYPE_LB" + pathValue).val("DNA提取");
                    // } else if (row["MEHOD_PLAT"] == "-" || row["MEHOD_PLAT"] == "" || row["MEHOD_PLAT"] == null) {
                    // 	$("#TASK_TYPE_LB" + pathValue).val("核酸检测");
                    // } else if (row["MEHOD_PLAT"] == "非常规提取") {
                    // 	$("#TASK_TYPE_LB" + pathValue).val("非常规提取");
                    // }

                    if (row["MEHOD_PLAT"] == "-" || row["MEHOD_PLAT"] == "" || row["MEHOD_PLAT"] == null)
                        $("#TASK_TYPE_LB" + pathValue).val("核酸检测");
                    else $("#TASK_TYPE_LB" + pathValue).val(row["MEHOD_PLAT"]);

                    THE_DATA_SUM += row["DATA_SUM"];

                    //根据合同数据量,统计并转换赋给本期数据量M  Reads数（M）=合同数据量（备注，单位是G）/0.3
                    /**
                    var the_ata=0;
                    if(avgsum>0){
                        if(row["DATA_UNIT"]&&row["DATA_UNIT"].indexOf("G")>-1){
                            THE_DATA_SUM=+avgsum/0.3;
                            the_ata=avgsum/0.3;
                        }else{
                            THE_DATA_SUM=+avgsum;
                            the_ata=avgsum;
                        }
                    }
                    upparmas.push({
                        "ID":row["ID"],
                        "DATA_SUM":avgsum,
                        "THE_DATA_SUM":the_ata.toFixed(2)
                    });
                    **/
                }

                //var url="system/jdbc/save/batch/table";
                // var paramsadd={"tableName":"BIO_TQ_TASK_MX","objects":upparmas};
                //putAddOrUpdata(url,paramsadd,"否","");
                //两位小数
                //THE_DATA_SUM=THE_DATA_SUM.toFixed(2);

                $("#TASK_LIB_TYPE" + pathValue).val(type);
                $("#THE_DATA_SUM" + pathValue).val(THE_DATA_SUM.toFixed(4));
                var typeJc;
                if (typest == "检测") {
                    typeJc = "样品检测标准用时";
                } else {
                    typeJc = "样品提取检测标准用时";
                }
                var dwtype = data[0]["DATA_UNIT"];

                //20220216h                doCyc(type, data.length, THE_DATA_SUM, "样品提取检测标准用时");
                doCyc(type, data.length, THE_DATA_SUM, typeJc, dwtype);
                doCycJK(type, data.length, THE_DATA_SUM, "建库标准用时", 1);
                doCycJK(type, data.length, THE_DATA_SUM, "实验交付标准用时", 2);
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
    }

    var submit = function () {
        //-----
        var paramsF = getJsonByForm("form", pathValue);
        var doresult = paramsF["TASK_COMFIRM_RESULT"];
        if (doresult != "通过") {
            if (paramsF["TASK_BACK"] == "") {
                alertMsg("请填写退回原因!");
                return;
            }
            var parmasup = [];
            parmasup.push({
                "ID": $("#ID" + pathValue).val(),
                "DD_TASK_STATUS": paramsF["DD_TASK_STATUS"],
                "TASK_STATUS": paramsF["TASK_STATUS"],
                "TASK_BACK": paramsF["TASK_BACK"]
            });

            var url = "system/jdbc/save/batch/table";
            var paramsadd = { "tableName": "BIO_TQ_TASK", "objects": parmasup };
            putAddOrUpdata(url, paramsadd, "关", "");

        } else { 

            if (paramsF["TASK_TYPE_LB"] == "非常规提取" || paramsF["TASK_TYPE_LB"] == "单细胞提取") {
                doGenNo(paramsF["TASK_NO"]); //生成文库编号
            }
            //---------- 
            if (paramsF["TASK_LDATE"] != "" && paramsF["TASK_JH_ENDDATE"] != "") {
                if (paramsF["TASK_TYPE"] != "HE染色") {
                    if (paramsF["TASK_TYPE"] != "预实验") {

                        sendPM();
                    }
                    if (paramsF["TASK_TYPE"] != "组织优化" && paramsF["TASK_TYPE"] != "基因表达") {

                        sendLSM();
                    }


                }

            }
            var time = sysNowTimeFuncParams["sysNowTime"];
            var username = getLimsUser()["name"];
            $("#SYS_MAN_L" + pathValue).val(username);
            $("#SYS_INSERTTIME_L" + pathValue).val(time);
            $("#DD_AUDIT_TIME" + pathValue).val(time);
            formSubmit({
                url: "system/jdbc/save/one/table",
                formId: "form",
                pathValue: pathValue,
                succeed: function (result) {
                    if (result["code"] > 0) {
                        //提交成功
                        alertMsg("提交成功", "success", function () {
                            funcExce(pathValue + "pageCallBack");
                            funcExce(pathValue + "close");
                        });
                    } else {
                        alertMsg("提交失败", "error");
                    }
                }
            });
            //------- 
        }
        //-----
    }
     

    //方法库
    var doSetff = function () {
        typest = $("#TASK_TYPE" + pathValue).val();
        var task_type = $("#TASK_TYPE" + pathValue).val();
        var SAMPLE_BATCHNO = $("#SAMPLE_BATCHNO" + pathValue).val();
        var PRODUCT_TYPE = $("#PRODUCT_TYPE" + pathValue).val();
        var arrIds = getSelectData(gridNameGrid);

        var object = getGridSelectData(gridNameGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/tq/task/setff/setff",
            title: "设置方法库..",
            currUrl: replacePathValue(pathValue)
        };

        var VARIETYS;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/database/execute/sqlcode",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: {
                "sqlcode": "select  VARIETY  from BIO_SAMPLE_INFO where SAMPLE_BATCHNO  =  '" + SAMPLE_BATCHNO + "'  GROUP BY VARIETY "
            },
            succeed: function (rs) {
                VARIETYS = rs.rows;
            }
        });
        var VARIETY = VARIETYS[0]["VARIETY"];

        var NUM;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/database/execute/sqlcode",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: {
                "sqlcode": "SELECT count(1) as AA  FROM BIO_QC_MD_LIBRARY bqml WHERE bqml.METHOD_STATUS='启用' " +
                    "AND (NOT bqml.MEHOD_JCFLOW IS NULL OR NOT bqml.LIBRARY_FLOW IS NULL) AND " +
                    "bqml.PRODUCT_TYPE ='" + PRODUCT_TYPE + "' AND bqml.SPECIES_ORIGIN_UNION LIKE  ('%" + VARIETY + "%') "
            },
            succeed: function (rs) {
                if (rs.rows[0]["AA"] == 0) {
                    NUM = 0;
                };
            }
        });
        openWindow(winOpts, { "object": object, "TASK_TYPE": $("#TASK_TYPE" + pathValue).val(), "IDS": arrIds, "task_type": task_type, "PRODUCT_TYPE": $("#PRODUCT_TYPE" + pathValue).val(), "VARIETY": VARIETY, "NUM": NUM });
    }

    //获取周期定义,推算出截止结果日期
    var doCycJK = function (type, countSm, smnumber, dep, selflag) {
        //样品提取检测标准用时
        var cycdw = $("#CYC_DW" + pathValue).val();
        var bus = $("#BUSINESS_UNIT" + pathValue).val();
        var params = "";
        var flag = 0;
        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm] };
        } else {
            flag = 1;
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber] };
        }
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    debugger;
                    var rows = result["rows"];
                    var m = getMyMonth();
                    var dateNumber = 0;
                    var seleDateFlag = "工作日";//日历取向
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        seleDateFlag = row["CYC_FLAG"];
                        if (m == 1) dateNumber = row["MONTH_1"];
                        if (m == 2) dateNumber = row["MONTH_2"];
                        if (m == 3) dateNumber = row["MONTH_3"];
                        if (m == 4) dateNumber = row["MONTH_4"];
                        if (m == 5) dateNumber = row["MONTH_5"];
                        if (m == 6) dateNumber = row["MONTH_6"];
                        if (m == 7) dateNumber = row["MONTH_7"];
                        if (m == 8) dateNumber = row["MONTH_8"];
                        if (m == 9) dateNumber = row["MONTH_9"];
                        if (m == 10) dateNumber = row["MONTH_10"];
                        if (m == 11) dateNumber = row["MONTH_11"];
                        if (m == 12) dateNumber = row["MONTH_12"];

                        break;
                    }
                    //项目周期
                    if (dateNumber == 0) {
                        $("#CYC_FLAG" + pathValue).val("");
                        if (selflag == 1) $("#TASK_LS_EXCDAYS" + pathValue).val("");
                        if (selflag == 2) $("#TASK_JF_EXCDAYS" + pathValue).val("");
                        var knumber = 0;
                        if (flag == 0) {
                            knumber = countSm;
                        } else {
                            knumber = smnumber;
                        }
                        //    alert("提示:未获取到周期数,请检测条件是否满足(工序标准:“" + dep + "”,单位:“" + cycdw + "”,文库类型:“" + type + "”,执行参数:“" + knumber + "”)！");
                        return;
                    } else {
                        $("#CYC_FLAG" + pathValue).val(seleDateFlag);
                        if (selflag == 1) $("#TASK_LS_EXCDAYS" + pathValue).val(dateNumber);
                        if (selflag == 2) $("#TASK_JF_EXCDAYS" + pathValue).val(dateNumber);

                        doGetEndDateJK(seleDateFlag, dateNumber, dateNumber, selflag);
                    }

                }
            }
        });

    }

    //推算截止日期
    var doGetEndDateJK = function (seleDateFlag, dateNumber1, dateNumber2, selflag) {
        var thedate = new Date();
        var thedate2 = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    if (selflag == 1) {
                        //建库标准日期
                        for (var i = 0; i < dateNumber1; i++) {
                            var base = 1000 * 60 * 60 * 24;
                            thedate = new Date(thedate.getTime() + base);
                            for (var j = 0; j < noDoDateS.length; j++) {
                                if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                    thedate = new Date(thedate.getTime() + base);//日期向前一天
                                }
                            }

                        }
                        //推算出的最终截止日期
                        //alert(toDateFormatByZone(thedate,"yyyy-MM-dd"));
                        $("#TASK_LS_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                    }
                    if (selflag == 2) {
                        //实验标准交付日期
                        for (var i = 0; i < dateNumber2; i++) {
                            var base = 1000 * 60 * 60 * 24;
                            thedate2 = new Date(thedate2.getTime() + base);
                            for (var j = 0; j < noDoDateS.length; j++) {
                                if (toDateFormatByZone(thedate2, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                    thedate2 = new Date(thedate2.getTime() + base);//日期向前一天
                                }
                            }

                        }
                        //推算出的最终截止日期
                        // alert(toDateFormatByZone(thedate2,"yyyy-MM-dd"));
                        $("#TASK_LS_DELIVERDATE" + pathValue).val(toDateFormatByZone(thedate2, "yyyy-MM-dd"));
                        //alertMsg("提示:计算标准日期成功,请注意复核!");
                    }
                }
            }
        });

    }


    //提前生成文库编号
    var doGenNo = function (tasklsno) {
        order = [];
        order_mn = [];
        ordersm = [];
        ordersm_mn = [];
        ordersm_no = [];
        ordersm_no_mn = [];
        var g = getGridItemsData(gridNameGrid);
        //取对应表
        var params = { "query": "queryBioLibTypeList", "objects": [] };
        var iniFist = "";
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var libtypename = [];
                    var initials = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        libtypename.push(row["LIB_TYPE_NAME"]);
                        initials.push(row["INITIALS"]);
                    }
                    var objectup = [];
                    for (var i = 0; i < g.length; i++) {
                        //更新记录
                        iniFist = checkInitals(g[i]["LIBRARY_TYPE_EN"], libtypename, initials);
                        objectup.push({
                            "ID": g[i]["ID"],
                            "DATA_LIBCODE": getLibCodeNo(tasklsno, iniFist, 0)
                        });

                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "提交");
                }
            }
        });
    }
    var getLibCodeNo = function (orderno, c, n) {
        var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
        var indexn = order.indexOf(orderno);
        var mn = 1;
        if (indexn > -1) {
            mn = order_mn[indexn] + 1;
            order_mn[indexn] = mn;
        } else {
            order.push(orderno);
            order_mn.push(n + 1);
            mn = n + 1;
        }
        tempcode = tempcode + c + getNo(mn, 0) + "-01";
        return tempcode;
    }
    //文库/切胶流程号段(项目期号内)
    var getNo = function (num, ki) {
        num = num + ki;
        if (num < 10) {
            num = "000" + num;
            return num;
        }
        if (num >= 10 && num < 100) {
            num = "00" + num;
            return num;
        }
        if (num >= 100 && num < 1000) {
            num = "0" + num;
            return num;
        }
        return num;
    }
    //比对取对照
    var checkInitals = function (name, names, initals) {
        for (var i = 0; i < names.length; i++) {
            if (name == names[i]) {
                return initals[i];
            }
        }
        return "";
    }

    //获取周期定义,推算出截止结果日期
    var doCyc = function (type, countSm, smnumber, dep, sa) {

        //样品提取检测标准用时
        var cycdw = $("#CYC_DW" + pathValue).val();
        var bus = $("#BUSINESS_UNIT" + pathValue).val();

        var IS_FULL_LIFE_CYCLE = $("#IS_FULL_LIFE_CYCLE" + pathValue).val();

        var IS_SHORT_PERIOD = "否";
        var IS_FULL_LIFE_CYCLE2 = "否";

        if (IS_FULL_LIFE_CYCLE == "是") {
            IS_FULL_LIFE_CYCLE2 = "是"
        } else {
            var CUSTOMER_VIP = $("#CUSTOMER_VIP" + pathValue).val();
            if (CUSTOMER_VIP.indexOf(2) > -1) {
                IS_SHORT_PERIOD = "是";
            }

        }

        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        } else {
            flag = 1;
            if (sa == "CELL") {
                if (type == "Denovo-Pac(20-30K)") smnumber = smnumber * 140;
                if (type == "Denovo-Pac(hifi)") smnumber = smnumber * 24;
                if (type == "Iso-RNA(Pac)") smnumber = smnumber * 300;
                if (type == "DNA-8K(ONT)") smnumber = smnumber * 80;
                if (type == "Denovo-20k(ONT)") smnumber = smnumber * 80;
                if (type == "Iso-RNA(ONT)") smnumber = smnumber * 140;
            }
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var m = getMyMonth();
                    var dateNumber = 0;
                    var seleDateFlag = "工作日";//日历取向
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        seleDateFlag = row["CYC_FLAG"];
                        if (m == 1) dateNumber = row["MONTH_1"];
                        if (m == 2) dateNumber = row["MONTH_2"];
                        if (m == 3) dateNumber = row["MONTH_3"];
                        if (m == 4) dateNumber = row["MONTH_4"];
                        if (m == 5) dateNumber = row["MONTH_5"];
                        if (m == 6) dateNumber = row["MONTH_6"];
                        if (m == 7) dateNumber = row["MONTH_7"];
                        if (m == 8) dateNumber = row["MONTH_8"];
                        if (m == 9) dateNumber = row["MONTH_9"];
                        if (m == 10) dateNumber = row["MONTH_10"];
                        if (m == 11) dateNumber = row["MONTH_11"];
                        if (m == 12) dateNumber = row["MONTH_12"];

                        break;
                    }
                    
                    if (dateNumber == 0) {
                        alert("提示:未获取到周期数,请检测条件是否满足(工序标准:“" + dep + "”,单位:“" + cycdw + "”,文库类型:“" + type + "”,执行参数:“" + knumber + "”)！");
                        return;
                    }else{
                    //执行天数
                    saveRemind = 1;
                    $("#TASK_EXCDAYS" + pathValue).val(dateNumber);
                    $("#CYC_FLAG" + pathValue).val(seleDateFlag);
                    doGetEndDate(seleDateFlag, dateNumber);
                    }
                }
            }
        });

    }
    //推算截止日期
    var doGetEndDate = function (seleDateFlag, dateNumber) {

        var thedate = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    for (var i = 0; i < dateNumber; i++) {
                        var base = 1000 * 60 * 60 * 24;
                        //thedate=new Date(thedate.getTime() + base); 
                        if (i == 0) {
                            var TASK_LLS = paramsValue["TASK_LL"] * 1;
                            thedate = new Date(TASK_LLS + (base));
                        } else {
                            //TASK_LLS=new Date(thedate.getTime() + base);
                            thedate = new Date(thedate.getTime() + base);
                        }
                        for (var j = 0; j < noDoDateS.length; j++) {
                            if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                thedate = new Date(thedate.getTime() + base);//日期向前一天
                            }
                        }

                    }
                    //推算出的最终截止日期
                    $("#TASK_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                    $("#TASK_JH_ENDDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));

                }
            }
        });

    }


    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }

    var openpool = function (ID) {
        var winOpts = {
            url: "biomarker/dispatch/tq/task/poolsamlist/poolsamlist",
            title: "混样列表...",
            width: 900,
            height: 380,
            currUrl: replacePathValue(pathValue),
            position: { "top": 150, "left": 250 }
        };
        openWindow(winOpts, { "ID": ID });
    }

    var doJG = function () {


        var paramsF = getJsonByForm("form", pathValue);
        //取出表单值
        var passFlag = paramsF["TASK_COMFIRM_RESULT"];
        var flagstr = "";
        var flagstr2 = "";
        if (passFlag == "通过") {
            flagstr = "已审核";
            flagstr2 = "已审核";
        } else if (passFlag == "退回") {
            flagstr = null;
            flagstr2 = "退回";

        } else if (passFlag == "终止") {
            flagstr = "终止";
            flagstr2 = "已终止";
        }

        $("#DD_TASK_STATUS" + pathValue).val(flagstr);
        $("#TASK_STATUS" + pathValue).val(flagstr2);

    }

    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {

        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        refreshGrid();
                        //----
                    } else if (isDoCallBack == "关") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                    //------
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read({
                "objects": [paramsValue["ID"]]
            });
        }
    }

    //LSM更新 
    var sendPM = function () {
        var odlStatus = "样品提取检测";
        var newStatus = "等客户反馈";
        var p = getJsonByForm("form", pathValue);
        var params;


        if (p["TASK_TYPE"] == "组织优化") {
            params = {
                "jiraKey": p["LSM_KEY_P"],
                "updateField": {
                    "customfield_12101": toDateFormatByZone(p["TASK_LS_LDATE"], "yyyy-MM-ddTHH:mm:ss.000+0800"),//建库完成日期：
                    "customfield_18820": toDateFormatByZone(p["TASK_LS_DELIVERDATE"], "yyyy-MM-ddTHH:mm:ss.000+0800"),//实验标准交付日期： 
                    "customfield_13900": toDateFormatByZone(p["TASK_LS_DELIVERDATE"], "yyyy-MM-ddTHH:mm:ss.000+0800"),//  实验计划交付日期：
                    "customfield_19005": { "value": "组织优化" },	//提取任务单类型
                }
            };

        } else {
            if (p["TASK_TYPE"] == "基因表达") {
                params = {
                    "jiraKey": p["LSM_KEY_P"],
                    "updateField": {
                        "customfield_19005": { "value": "基因表达" },	//提取任务单类型
                    }
                };

            } else {
                if (p["TASK_TYPE"] == "制备") {
                    params = {
                        "jiraKey": p["LSM_KEY_P"],
                        "updateField": {
                            "customfield_18820": toDateFormatByZone(p["TASK_LS_DELIVERDATE"], "yyyy-MM-ddTHH:mm:ss.000+0800"),//实验标准交付日期： 
                            "customfield_13900": toDateFormatByZone(p["TASK_LS_DELIVERDATE"], "yyyy-MM-ddTHH:mm:ss.000+0800"),//  实验计划交付日期：
                        }
                    };
                } else {
                    params = {
                        "jiraKey": p["LSM_KEY_P"],
                        //  "oldStatusName":odlStatus,
                        //  "statusName":newStatus,
                        "updateField": {
                            //"customfield_14300":p["TASK_LDATE"],	//提取检测标准完成日期
                            "customfield_14500": p["TASK_JH_ENDDATE"],	//提取检测预计合格日期
                        }
                    };
                }
            }
        }
		//params["updateField"]["customfield_19108"] = { "value": p["PROJECT_OUT_TYPE"] };//实验生产类型

        
        if ( p["PROJECT_OUT_TYPE"]  !== null &&  p["PROJECT_OUT_TYPE"]  !== undefined &&  p["PROJECT_OUT_TYPE"]  !== "") {
            params["updateField"]["customfield_19112"] = { "value":  p["PROJECT_OUT_TYPE"]  };//实验生产类型
        }  
        
        var m = mask(pathValue, "正在推送到jira,请稍等...");
        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    alertMsg("提示:BRO推送成功!");
                    unmask(m);
                } else {
                    unmask(m);
                    alertMsg("提示:操作失败!");
                }
            },
            failed: function (res) {
                alertMsg("提示:提交保存失败", "error");
                unmask(m);
            }

        });

    }

    //LSM更新 
    var sendLSM = function () {
        var odlStatus = "样品提取检测";
        var newStatus = "等客户反馈";
        var p = getJsonByForm("form", pathValue);
        var params;
        var customfield_17600 = {
            "value": p["CUSTOMER_VIP"] + ""
        }


        if (p["PRODUCT_TYPE"].indexOf('医学') == 1) {
            params = {
                "jiraKey": p["LSM_KEY"],
                //  "oldStatusName":odlStatus,
                //  "statusName":newStatus,
                "updateField": {
                    "customfield_14300": p["TASK_LDATE"],	//提取检测标准完成日期
                    "customfield_14500": p["TASK_JH_ENDDATE"],	//提取检测预计合格日期
                    "customfield_10113": p["PROJECT_SUBNO"],	//项目期号
                    "customfield_17600": customfield_17600,	//项目等级
                    // "customfield_13900": p["TASK_LDATE"],	//实验计划交付日期
                    //  "customfield_10226": p["TASK_LDATE"],	//实验标准交付日期
                    // "customfield_12100": p["TASK_LDATE"],	//建库标准完成日期
                    //"customfield_14201": p["TASK_LDATE"],	//建库计划完成日期
                }
            };

        } else {
            params = {
                "jiraKey": p["LSM_KEY"],
                //  "oldStatusName":odlStatus,
                //  "statusName":newStatus,
                "updateField": {
                    "customfield_14300": p["TASK_LDATE"],	//提取检测标准完成日期
                    "customfield_10113": p["PROJECT_SUBNO"],	//项目期号
                    "customfield_17600": customfield_17600,	//项目等级
                    "customfield_14500": p["TASK_JH_ENDDATE"],	//提取检测预计合格日期
                }
            };

        }

        var m = mask(pathValue, "正在推送到jira,请稍等...");

        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    alertMsg("提示:LSM推送成功!");
                    unmask(m);
                } else {
                    unmask(m);
                    alertMsg("提示:操作失败!");
                }
            },
            failed: function (res) {
                alertMsg("提示:提交保存失败", "error");
                unmask(m);
            }

        });

    }

    //时间计算
    var addDate = function (date, days) {
        var d = new Date(date);
        d.setDate(d.getDate() + days);
        var m = d.getMonth() + 1;
        return d.getFullYear() + '-' + m + '-' + d.getDate();
    }



    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "doSetff": doSetff,
        "refreshGrid": refreshGrid,
        "submit": submit,
        "callBack": callBack,
        "doJG": doJG,
        "openpool": openpool,
    });

});