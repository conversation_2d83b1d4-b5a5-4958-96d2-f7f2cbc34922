$(document).ready(function() {
   var pathValue="biomarker-dispatch-pd-libmcd-index";
   var initData=function(){
       return {};
   }
   var gridNameDGrid;
   var gridNameD1Grid;
   var gridNameD2Grid;
   var gridNameD3Grid;
   var gridNameS=[];
   var gridNameS1=[];
   var gridNameS2=[];
   var gridNameS3=[];
   //待排
   var init=function(params){
      var toolbar=getButtonTemplates(pathValue,[
           {name:"edit",target:"edit",title:"生成执行单"},
           {name:"edit",target:"editP",title:"任务单补充"},
           {name:"edit",target:"upsmStatus",title:"修改建库状态"},
           {name:"edit",target:"addToEx",title:"追加任务到执行单"},
           {name:"edit",target:"doTaskStatus",title:"任务单状态修改"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"query_BIO_TASK_LIB_list_pd",
        	   "objects":[["已审核","建库中"],["混样建库"]],
        	   "search":{"TASK_LS_TYPE_LB":"MCD简化建库"}},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"query_BIO_TASK_LIBMX_list","objects":[ROW_ID]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
               gridNameS.push(subGrid_N);
           }
       };
       gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
       init1();
       init2();
       init3();
   }
   //待审核
   var init1=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	   {name:"ok",target:"doOK",title:"提交"},
           {name:"edit",target:"doGenNo",title:"生成文库编号"},
    	   {name:"delete",target:"remove",title:"移除任务明细"},
    	   {name:"delete",target:"doDelete",title:"删除执行单"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"lib_pd_SHEET_list","objects":[["DNA混样建库-MCD简化建库"],["待审核","已接收","接收退回","建库提交","建库已审核"]]},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"queryTaskLibExMx","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
               gridNameS1.push(subGrid_N);
           }
           
           
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
  }
   //已处理
   var init2=function(params){
       var toolbar=getButtonTemplates(pathValue,[
    	   {name:"return",target:"doReturn",title:"撤回"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"lib_pd_SHEET_list","objects":[["DNA混样建库-MCD简化建库"],["待接收","已接收","实验退回","建库提交","建库待审核","建库已审核"]]},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"queryTaskLibExMx","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
               gridNameS2.push(subGrid_N);
           }      
       };
       gridNameD2Grid = initKendoGrid("#gridNameD2Grid"+pathValue,gridNameGridJson);//初始化表格的方法
  }
   //完成
   var init3=function(params){
	 
	   var toolbar=getButtonTemplates(pathValue,[
		   {name:"return",target:"doReturn2",title:"移至待审核"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"query_BIO_TASK_LIB_list",
        	   "objects":[["上机中","结单","测序","暂停","终止","未建库"],["混样建库"]],
        	   "search":{"TASK_LS_TYPE_LB":"MCD简化建库"}},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"query_BIO_TASK_LIBMX_list","objects":[ROW_ID]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
               gridNameS3.push(subGrid_N);
           }
       };
       gridNameD3Grid = initKendoGrid("#gridNameD3Grid"+pathValue,gridNameGridJson);
 
   }

    //排单生成
    var edit=function(){
    	var arrIds=[];
        for(var i=0;i<gridNameS.length;i++){
        	var arrSubID=getGridSelectData(gridNameS[i]);
        	if(arrSubID.length!=0){
        		arrIds=arrIds.concat(arrSubID);
        	}
        }
        if(arrIds.length==0){
        	alertMsg("请至少选择一条样本记录进行操作!");
        	return;
        }
            //判断类型是否全部分同一为,并取出形成类型单
            var g=arrIds;
            var a="";
            var b="";
            var code=[];

            for(var i=0;i<g.length;i++){
            	
            	if(code.indexOf(g[i]["BIO_CODE"])>-1){//同一个执行单不允许重复
        			alertMsg("提示:存在所选编号“"+g[i]["BIO_CODE"]+"重复!”");
        			return;
        		}else{
        			code.push(g[i]["BIO_CODE"]);
        		}
            	if(i==0){
    	        	a=g[i]["LIBRARY_FLOW"];
    	        	b=g[i]["LIBRARY_FLOW"];
            	}else{
            		a=g[i-1]["LIBRARY_FLOW"];
            		b=g[i]["LIBRARY_FLOW"];
            	}
            	
            	if(a!=b){
            		alertMsg("存在所选记录建库流向“<font color=#ff0000>"+a+"--"+b+"</font>”前后不一致!");
            		return;
            	}
            	
            }

            var ids=[];
            var taskids=[];
            var biocodes=[];
            var samplecodes=[];
            var libtypes=[];
            var libtypemxs=[];
            for(var i=0;i<arrIds.length;i++){
            	ids.push(arrIds[i]["ID"]);
            	libtypemxs.push(arrIds[i]["LIBRARY_TYPE_EN"]);
                biocodes.push(arrIds[i]["BIO_CODE"]);
                samplecodes.push(arrIds[i]["SAMPLE_CODE"]);
                if(libtypes.indexOf(arrIds[i]["LIBRARY_TYPE_EN"])<0){
                	libtypes.push(arrIds[i]["LIBRARY_TYPE_EN"]);//文库类型
            	}
            	if(taskids.indexOf(arrIds[i]["TASK_LS_ID"])<0){
            		taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            	}
            }
        	var winOpts={
                    url:"biomarker/dispatch/pd/libmcd/pddu/pddu",
                    title:"常规建库排单.."
                };
            openWindow(winOpts,{"IDS":ids,"TASKIDS":taskids,"SAMPLECODES":samplecodes,"BIOCODES":biocodes,"LIBTYPES":libtypes,"LIBTYPEMXS":libtypemxs,"EX_TYPE":a});
     }
    //追加任务
    var addToEx=function(){
	   	var arrIds=[];
        for(var i=0;i<gridNameS.length;i++){
        	var arrSubID=getGridSelectData(gridNameS[i]);
        	if(arrSubID.length!=0){
        		arrIds=arrIds.concat(arrSubID);
        	}
        }
        if(arrIds.length==0){
        	alertMsg("请至少选择一条样本记录进行操作!");
        	return;
        }
        
	   	var winOpts={
	   		   url:"biomarker/dispatch/pd/libmcd/addtoex/addtoex",
	           title:"追加样本到执行单.."
	       };
	   	var ids=[];
	   	var codes=[];
	   	for(var i=0;i<arrIds.length;i++){
	   		ids.push(arrIds[i]["ID"]);
	   		codes.push(arrIds[i]["BIO_CODE"]);
        }
	   	
	   openWindow(winOpts,{"IDS":ids,"CODES":codes});
	}
    //文编号
    var doGenNo=function(){
   	 var g=[];
        for(var i=0;i<gridNameS1.length;i++){
        	var arrSubID=getGridSelectData(gridNameS1[i]);
        	if(arrSubID.length!=0){
        		g=g.concat(arrSubID);
        	}
        }
        if(g.length==0){
         	alertMsg("请至少选择一条样本记录进行操作!");
         	return;
         }
        
      //取对应表
      var params={"query":"queryBioLibTypeList","objects":[]};
      var iniFist="";
 	   $.fn.ajaxPost({
 	        ajaxUrl:"system/jdbc/query/one/table",
 	        ajaxType: "post",
 	        ajaxData: params,
 	        succeed:function(result){
 	        	if(result["code"]>0){
 	        	var libtypename=[];
 	     	    var initials=[];
   	        	var rows=result["rows"];
   	        	for(var i=0;i<rows.length;i++){
   	        		var row=rows[i];
   	        		libtypename.push(row["LIB_TYPE_NAME"]);
   	        		initials.push(row["INITIALS"]);
   	        	}
   	            var objectup=[];
   	            for(var i=0;i < g.length;i++){
   	     	       	//更新记录
   	            	iniFist=checkInitals(g[i]["LIBRARY_TYPE_EN"],libtypename,initials);
   	     	       	objectup.push({
   	     	       		"ID":g[i]["LIBID"],//关联更新ID
   	     	       		"LIBRARY_CODE":g[i]["PROJECT_SUBNO"]+iniFist
   	     	       		+getNo(g[i]["DON"],i+1)+"-"+getBioCodeNo(g[i]["DON2"]+1)
   	     	       	});
   	            }
   	           var urlsend="system/jdbc/save/batch/table";
   	           var paramsup={"tableName":"BIO_LIB_INFO","objects":objectup};
   	      	 	putAddOrUpdata(urlsend,paramsup,"是","提交");    	        	
   	        	
 	        	}
 	        }
 	   });
     
    }
    //比对取对照
    var checkInitals=function(name,names,initals){
   	 for(var i=0;i<names.length;i++){
   		 if(name==names[i]){
   			 return initals[i];
   		 }
   	 }
   	 return "";
    }
    //文库流程号段(项目期号内)
    var getNo=function(num,ki){
   	 num=num+ki;
   	 if(num<10){
   		 num="000"+num;
   		 return num;
   	 }
   	 if(num>=10 && num<100){
   		 num="00"+num;
   		 return num;
   	 }
   	 if(num>=100 && num<1000){
   		 num="0"+num;
   		 return num;
   	 }
   	 return num;
    }
    //核酸号段(项目期号内)
    var getBioCodeNo=function(num){
   	 if(num<10){
   		 num="0"+num;
   		 return num;
   	 }
   	 return num;
    }
    
	var editP=function(){
		var arrIds=getSelectData(gridNameDGrid);
        if(arrIds.length==0){
           	alertMsg("请至少选择一条记录进行操作!");
           	return;
         }
        var winOpts={
           url:"biomarker/dispatch/pd/libmcd/editP/editP",
           title:"补充任务单计划.."
        };
   	 	openWindow(winOpts,{"IDS":arrIds,"ID":arrIds[0]});
	}

    //提交
     var doOK=function(){
    	  var arrIds=getSelectData(gridNameD1Grid);
          if(arrIds.length==0){
             	alertMsg("请至少选择一条记录进行操作!");
             	return;
           }
      	 //校验文库编号是否为空
         var s="";
         var params={"query":"doCheckLibCodeIsPass","objects":[arrIds]};
     	   $.fn.ajaxPost({
     	        ajaxUrl:"system/jdbc/query/one/table",
     	        ajaxType: "post",
     	        ajaxData: params,
     	        succeed:function(result){
     	        	if(result["code"]>0){
     	        		var objectup=[];
       	        	var rows=result["rows"];
       	        	for(var i=0;i<rows.length;i++){
       	        		var row=rows[i];
       	        		if(row["EX_TYPE"]=="三代ONT基因组建库"){//判断切胶编号
       	        			if(row["JK_QJ_CODE"]==""||row["JK_QJ_CODE"]==null){
           	        			if(s==""){
           	        				s=row["EX_DH_NO"];
           	        			}
           	        		}
       	        		}else{
       	        			if(row["LIBRARY_CODE"]==""||row["LIBRARY_CODE"]==null){
           	        			if(s==""){
           	        				s=row["EX_DH_NO"];
           	        			}
           	        		}
       	        		}
       	        		
       	        	}
       	        	 if(s!=""){
       	          		 alertMsg("提示:单号“"+s+"”存在文库编号(或切胶编号为空--ONT)为空!");
       	          		 return;
       	          	   }
	       	          var objectup=[];
	       	          for(var i=0;i<arrIds.length;i++){
	       		   	  	   var time=sysNowTimeFuncParams["sysNowTime"];
	       		   	  	   var username=getLimsUser()["name"];
	       		   	  	   objectup.push({
	       		   	       		"ID":arrIds[i],//联联任务ID
	       		   		       	"EX_RE_STATUS":"待接收"
	       		   		    });
	       	          	}
	       	         var urlsend="system/jdbc/save/batch/table";
	       	      	 var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
	       	      	 putAddOrUpdata(urlsend,paramsup,"是","提交");
	       	      	 doRequeDoUpTaskLibmxStatus(arrIds,"建库待接收");
     	        	}
     	        }
     	   });
    }
  //任务单-对应执行单下明细状态修改  
  var doRequeDoUpTaskLibmxStatus=function(mainExIds,status){
   	 var time=sysNowTimeFuncParams["sysNowTime"];
     var username=getLimsUser()["name"];
   	 var params={"query":"doRequeDoUpTaskLibmxStatus","objects":[mainExIds]};
   	   $.fn.ajaxPost({
   	        ajaxUrl:"system/jdbc/query/one/table",
   	        ajaxType: "post",
   	        ajaxData: params,
   	        succeed:function(result){
   	        	if(result["code"]>0){
   	        		var objectup=[];
     	        	var rows=result["rows"];
     	        	for(var i=0;i<rows.length;i++){
     	        		var row=rows[i];
     	    	        //更新记录---明细
     	    	       	objectup.push({
     	    	       		"ID":row["TASKLIBMXID"],
     	    	       		"TASK_LSMX_STATUS":status
     	    	       	});
     	        	}
     	        	  var urlsend="system/jdbc/save/batch/table";
     	              var paramsup={"tableName":"BIO_TASK_LIBMX","objects":objectup};
     	              putAddOrUpdata(urlsend,paramsup,"否","同步更新任务明细:");
   	        	}
   	        },
   	        failed:function(result){
   	            alertMsg("提示:操作异常!","error");
   	        }
   	    });
    }

     
    //撤回doRequeDoUpTaskLibmxStatus
    var doReturn=function(){
    	var g=getGridSelectData(gridNameD2Grid);
        if(g.length==0){
           	alertMsg("请至少选择一条记录进行操作!");
           	return;
         }
        var objectup=[];
        var arrIds=[];
        for(var i=0;i<g.length;i++){
	   	  	if(g[i]["EX_RE_STATUS"]!="待接收"){  
	   	  		alertMsg("操作失败,所选记录存在已“已接收”状态!");
	   	  		return;
	   	  	}else{
		   	  	objectup.push({
	   	       		"ID":g[i]["ID"],
	   		       	 "EX_RE_STATUS":"待审核"
	   		    });
		   	 arrIds.push(g[i]["ID"]);
	   	  	}
        }
       var urlsend="system/jdbc/save/batch/table";
       var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
       putAddOrUpdata(urlsend,paramsup,"是","提交");
       doRequeDoUpTaskLibmxStatus(arrIds,"草稿");

    }
    //移至待审核
    var doReturn2=function(){
    	var g=getGridSelectData(gridNameD3Grid);
        if(g.length==0){
           	alertMsg("请至少选择一条记录进行操作!");
           	return;
         }
        var objectup=[];
        for(var i=0;i<g.length;i++){
	   	  	if(g[i]["TASK_LS_STATUS"]=="暂停"||g[i]["TASK_LS_STATUS"]=="未建库"){  
		   	 	objectup.push({
	   	       		"ID":g[i]["ID"],
	   		       	"TASK_LS_STATUS":"已审核"
		   	 	});
	   	  	}else{
	   	  		alertMsg("操作失败,只有“<font color=#ff0000>暂停、未建库</font>”状态方允许操作!");
	   	  		return;
	   	  	}
        }
       var urlsend="system/jdbc/save/batch/table";
       var paramsup={"tableName":"BIO_TASK_LIB","objects":objectup};
    	putAddOrUpdata(urlsend,paramsup,"是","提交");
    }
  
    //任务单状态修改
	var doTaskStatus=function(){
		var arrIds=getSelectData(gridNameDGrid);
        if(arrIds.length==0){
           	alertMsg("请至少选择一条记录进行操作!");
           	return;
         }
        var winOpts={
           url:"biomarker/dispatch/pd/libmcd/uptaskstatus/uptaskstatus",
           title:"修改任务单状态.."
        };
   	 	openWindow(winOpts,{"IDS":arrIds});
	}
  //样本状态修改
    var upsmStatus=function(){
    	 var arrIds=[];
         for(var i=0;i<gridNameS.length;i++){
         	var arrSubID=getSelectData(gridNameS[i]);
         	if(arrSubID.length!=0){
         		arrIds=arrIds.concat(arrSubID);
         	}
         }
         if(arrIds.length==0){
         	alertMsg("请至少选择一条样本记录进行操作!");
         	return;
         }
    	var winOpts={
            url:"biomarker/dispatch/pd/libmcd/upsmstatus/upsmstatus",
            title:"修改样本状态.."
        };
    	 openWindow(winOpts,{"IDS":arrIds});
    }
    
  //记录移除
   var remove=function(){
  	 var arrg=[];
  	 var arrIds=[];
       for(var i=0;i<gridNameS1.length;i++){
       	var arrSubID=getGridSelectData(gridNameS1[i]);
       	arrg=arrg.concat(arrSubID);
       	
       }
       if(arrg.length==0){
           alertMsg("请至少选择一条数据进行操作!");
           return ;
       }
       for(var i=0;i < arrg.length;i++){
       	arrIds.push(arrg[i]["LIBID"]);
       }
       confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function() {
          var params={"tableName":"BIO_LIB_INFO","ids":arrIds};
          var url="system/jdbc/delete/batch/table";
          deleteGridDataByIds(url,params,refreshGrid);
       });
    }
   
   //删除执行单
   var doDelete=function(){
  	 var arrIds=getSelectData(gridNameD1Grid);
       if(arrIds.length==0){
           alertMsg("请至少选择一条数据进行操作!");
           return ;
       }
       confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function() {
          var url="system/jdbc/delete/one/table/where";
          var params1= {"tableName":"BIO_LIB_INFO","where":{"EXE_TQQC_ID":arrIds}};
          deleteGridDataByIds(url,params1,refreshGrid);
          var params2= {"tableName":"EXE_TQQC_SHEET","where":{"ID":arrIds}};
          deleteGridDataByIds(url,params2,refreshGrid);
       });
    }
   
   //批量执行插入
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		 refreshGrid();
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
   
     var callBack=function(){
        refreshGrid();
     };

    var refreshGrid=function(){
    	 gridNameS=[];
    	 gridNameS1=[];
    	 gridNameS2=[];
    	 gridNameS3=[];
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();
        }
        if(gridNameD2Grid){
        	gridNameD2Grid.dataSource.read();
        }
      if(gridNameD3Grid){
        	gridNameD3Grid.dataSource.read();
        }
     }
   
     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "edit":edit,
         "editP":editP,
         "upsmStatus":upsmStatus,
         "addToEx":addToEx,
         "doTaskStatus":doTaskStatus,
         "doOK":doOK,
         "doGenNo":doGenNo,
         "remove":remove,
         "doDelete":doDelete,
         "doReturn":doReturn,
         "doReturn2":doReturn2,
         "refreshGrid":refreshGrid,
         "callBack":callBack,
     });
});