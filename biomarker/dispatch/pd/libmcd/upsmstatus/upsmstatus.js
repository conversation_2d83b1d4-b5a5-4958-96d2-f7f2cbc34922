$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-libmcd-upsmstatus-upsmstatus";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_TASK_LIBMX"
        };
    }
    var init=function(params){
        paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
    }

  var subUpData=function(){
	 var ids=paramsValue["IDS"];
	 var object=[];
	 var jsonData = getJsonByForm("form",pathValue);//获取表单json
	 for(var i=0;i < ids.length;i++){
     	 object.push($.extend({},jsonData,{"ID":ids[i]}));//表单值继承
      }
	var urlsend="system/jdbc/save/batch/table";
  	var paramsadd={"tableName":"BIO_TASK_LIBMX","objects":object};
  	putAddOrUpdata(urlsend,paramsadd,"是","更新");  
 }
  
  //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提示:操作成功!");
             		 funcExce(pathValue+"pageCallBack");
                     funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 
    var submit=function(){
       subUpData();
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });