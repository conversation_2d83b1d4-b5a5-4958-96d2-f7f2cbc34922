$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-lib-libadd-libadd";
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "{{tableName}}"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var paramsValue;
    var gridNameGrid;
    var init = function (params) {
        paramsValue = params;
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "add", title: "确认选择" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list",
                "objects": [[
                    "二代常规DNA建库",
                    "二代常规RNA建库",
                    "三代ONT基因组建库",
                    "代谢建库"],
                ["待审核", "接收退回"]]
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    var add = function () {debugger;
        var gridData = getGridSelectData(gridNameGrid);
        if (gridData.length != 1) {
            alertMsg("请选择一条记录进行操作!");
            return;
        }
        if (gridData[0]["EX_LIB_TYPE"] != paramsValue["LIBRARY_TYPE_EN"]) {
            alertMsg("文库类型不同!不允许追加");
            return;
        }

        var subGrid_N = [];
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "queryTaskLibExMx", "objects": [[gridData[0]["ID"]]] },
            succeed: function (rs) {
                subGrid_N = rs.rows;             //执行单明细
            }
        });

        var indexs = [];
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "QueryIndexBoardList", "objects": [], "search": { "PLATE_NO": gridData[0]["PLATE_NO_384"], "INDEX_QUADRANT": gridData[0]["INDEX_QUADRANT"] } },
            succeed: function (rs) {
                indexs = rs.rows;             //index
            }
        });

        var num = 0;
        if(subGrid_N.length==subGrid_N[subGrid_N.length-1]["PLATE_WELL_NO"]){
            num=subGrid_N.length+1;
        }else{
            for (let index = 0; index < subGrid_N.length; index++) {
                if(subGrid_N[index]["PLATE_WELL_NO"]!=index+1){
                    num=index+1;
                    break;
                } 
            }
        }


        var username = getLimsUser()["name"];
        var m = getWell(num-1, "竖排");              //孔位号
         var n =num   //孔位ID(顺序)
 
        var index = [];
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "QueryIndexBoardMxList", "objects": [[indexs[0]["ID"]]], "search": { "CELL_NUM": [m] } },
            succeed: function (rs) {
                index = rs.rows;                                                    //index MX
            }
        });
        var objectadd = [];
        var objectupmx = [];
        time = sysNowTimeFuncParams["sysNowTime"];
        objectadd.push({
            "TASK_LIB_MX_ID": paramsValue["ID"],                           //关联任务单明细ID
            "BIO_CODE": paramsValue["BIO_CODE"],                        //枋酸编号
            "LIBRARY_TYPE": paramsValue["LIBRARY_FLOW"],          //文库类型
            "LIBRARY_TYPE_EN":paramsValue["LIBRARY_FLOW"],     //文库类型
            "SAMPLE_CODE": paramsValue["SAMPLE_CODE"],          //样品编号
            "EXE_TQQC_ID": gridData[0]["ID"],                                                    //关联执行单
            "SEQ_PLAT": subGrid_N[0]["SEQ_PLAT"],                                           //测序平台
            "JK_TASKMX_MAN": subGrid_N[0]["JK_TASKMX_MAN"],                   //建库实验员
            "JK_TASKMX_STDATE": time,
            "SYS_MAN": username,                                           //实验员
            "PLATE_CODE": gridData[0]["PLATE_CODE"],                                      //板号
            "PLATE_WELL": m,                                                    //孔位号
            "PLATE_WELL_NO": n,                                            //孔位ID(顺序)
            "SYS_INSERTTIME": time,                                      //开始日期
            "INDEX_NAME": index[0]["INDEX_NAME"],//Index名称
            "SEQ_I7_1": index[0]["SEQ_I7_1"],//I7端(或正向)
            "SEQ_I7_2": index[0]["SEQ_I7_2"],//I7端2
            "SEQ_I7_3": index[0]["SEQ_I7_3"],//I7端3
            "SEQ_I7_4": index[0]["SEQ_I7_4"],//I7端4
            "SEQ_I5_NAVA": index[0]["SEQ_I5_NAVA"],//I5端-Nova(或反向)
            "SEQ_I5_XTEN": index[0]["SEQ_I5_XTEN"],//I5端-Xten
            "GEN_N": index[0]["GEN_N"],//错位碱基数
            "ER_SEQ": index[0]["ER_SEQ"],//错位碱基序列
            "LIB_KIT": index[0]["LIB_KIT"]//建库试剂盒
        });
        objectupmx.push({ "ID":  paramsValue["ID"],   "RL_STATUS": "已排" });

        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectadd };
        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");



        var paramsupmx = { "tableName": "BIO_TASK_LIBMX", "objects": objectupmx };
        putAddOrUpdata(urlsend, paramsupmx, "是", "建库中");

    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    var getWell = function (data, tvo) {

        if (tvo == "竖排") {
            var k = Math.floor(data / 8) + 1;
            var l = data % 8;
            switch (l) {
                case 0: z = "A"; break;
                case 1: z = "B"; break;
                case 2: z = "C"; break;
                case 3: z = "D"; break;
                case 4: z = "E"; break;
                case 5: z = "F"; break;
                case 6: z = "G"; break;
                case 7: z = "H"; break;

            }
            if (k < 10) { var x = z + "0" + k; } else { var x = z + k; }

            return x;
        } else {
            var k = Math.floor(data / 12);
            var l = data % 12
            switch (k) {
                case 0: z = "A"; break;
                case 1: z = "B"; break;
                case 2: z = "C"; break;
                case 3: z = "D"; break;
                case 4: z = "E"; break;
                case 5: z = "F"; break;
                case 6: z = "G"; break;
                case 7: z = "H"; break;

            }
            if (k < 10) { var x = z + "0" + l; } else { var x = z + l; }

            return x;
        }
    }


    var submit = function () {
        funcExce(pathValue + "pageCallBack");//执行回调
        funcExce(pathValue + "close");//关闭页面
    }

    funcPushs(pathValue, {
        "init": init,
        "add": add,
        "submit": submit,
    });

});