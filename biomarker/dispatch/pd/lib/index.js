$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-lib-index";
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var gridNameD4Grid;
    var gridNameD5Grid;
    var gridNameD6Grid;
    var gridNameD7Grid;
    var gridNameD8Grid;
    var gridNameD9Grid;
    var gridNameS = [];
    var gridNameS1 = [];
    var gridNameS2 = [];
    var gridNameS3 = [];
    var gridNameS5 = [];
    var gridNameS6 = [];
    var sydates = 0;
    var cxdates = 0;


    //对同一任务单号进行文库产生计数器
    var order = [];
    var order_mn = [];
    var ordersm = [];
    var ordersm_mn = [];
    var ordersm_no = [];
    var ordersm_no_mn = [];
    //待排
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit", title: "生成执行单" },
            { name: "edit", target: "editP", title: "任务单补充" },
            { name: "edit", target: "upsmStatus", title: "修改建库状态" },
            { name: "edit", target: "addToEx", title: "追加任务到执行单" },
            { name: "edit", target: "doTaskStatus", title: "任务单状态修改" },
            { name: "edit", target: "setjira", title: "jira推送.." },
            { name: "edit", target: "exportDetails", title: "明细导出" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_BIO_TASK_LIB_list_pd",
                "objects": [["已审核", "待建库", "建库中", "质检中", "测序中", "测序完成", "数据处理中", "数据处理完成", "已完成"], ["蛋白建库", "常规建库", "代谢建库"]],
                "search": { "TASK_LS_TYPE_LB": "常规建库" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var readsql = "query_BIO_TASK_LIBMX_list";
                var ROW_ID = e.data.ID;
                if (e.data.TASK_LS_TYPE == "代谢建库") readsql = "query_BIO_TASK_LIBMX_list-dx";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": readsql, "objects": [], "search": { "TASK_LS_ID": [ROW_ID] } },

                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);


        gridNameDGrid.bind("dataBound", function (e) {
            var tablesGrid_rowAll = gridNameDGrid.dataSource.data();
            if (tablesGrid_rowAll.length > 0) {
                var table_trs = $("#gridNameDGrid" + pathValue + " .k-grid-content table tbody tr");
                for (var i = 0; i < tablesGrid_rowAll.length && table_trs.length; i++) { 
                    var flag = tablesGrid_rowAll[i]["TASK_LS_LDATE"];
                    var time = Date.now();
                    var a = time - flag;
                    var b = 1 * 1000 * 60 * 60 * 24 * 2;
                    if (a > b) {
                        $(table_trs[i]).css("background", "red");
                    }
                }
            }
        });





        init1();
        init2();
        init3();
        init4();
        init5();
        //    init6();
        init7();
        init8();
        init9();
    }
    //待审核
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "ok", target: "doOK", title: "提交" },
            { name: "edit", target: "doGenNo", title: "生成文库编号" },
            { name: "edit", target: "doLib", title: "修改建库方法" },
            { name: "edit", target: "doQJNo", title: "生成切胶编号(ONT)" },
            { name: "edit", target: "doGetIndex", title: "分配index" },
            { name: "edit", target: "generatePlate", title: "生成96孔板位置" },
            { name: "delete", target: "remove", title: "移除任务明细" },
            { name: "delete", target: "doDelete", title: "删除执行单" },
            { name: "edit", target: "doUpdate", title: "修改实验员" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list",
                "objects": [[
                    "二代常规DNA建库",
                    "蛋白前处理",
                    "二代常规RNA建库",
                    "三代ONT基因组建库",
                    "代谢建库"],
                ["待审核", "接收退回"]]
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {

                var ROW_ID = e.data.ID;
                debugger;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMx", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }


        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    //已处理
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list",
                "objects": [[
                    "蛋白前处理",
                    "二代常规DNA建库",
                    "二代常规RNA建库",
                    "三代ONT基因组建库",
                    "代谢建库"],
                ["待接收", "已接收", "实验退回", "建库提交", "建库待审核", "建库已审核", "切胶结果已审核"]], "search": { "EX_EXECUTE_MODE": "实验员" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMx", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    //完成
    var init3 = function (params) {

        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "移至待审核" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_BIO_TASK_LIB_list",
                "objects": [["上机中", "结单", "测序", "暂停", "终止", "未建库", "建库完成"], ["常规建库", "混样建库", "代谢建库"]],
                "search": { "TASK_LS_TYPE_LB": "常规建库" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_BIO_TASK_LIBMX_list", "objects": [], "search": { "TASK_LS_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS3.push(subGrid_N);
            }
        };
        gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);

    }

    //异常任务
    var init4 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "schemeFilling", title: "重建库方案填写" },
            { name: "edit", target: "editR", title: "生成执行单" },
            { name: "edit", target: "addToExRe", title: "追加任务到执行单" },
            { name: "edit", target: "editZZ", title: "终止" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "ib_test_qc_all_task_sh_list_abo2",
                "objects": [["重建库", "重新纯化", "重建库验证", "重建库加测"]]
            },
        };
        gridNameD4Grid = initKendoGrid("#gridNameD4Grid" + pathValue, gridNameGridJson);

    }
    //待排任务
    var init5 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "editA", title: "智能排单" },
            { name: "edit", target: "editB", title: "追加执行单" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_BIO_TASK_LIB_list_lib",
                "objects": [["已审核", "待建库", "建库中", "质检中", "测序中", "测序完成", "数据处理中", "数据处理完成", "已完成"], ["常规建库", "代谢建库"]],
                "search": { "TASK_LS_TYPE_LB": "常规建库" }
            },
            fetch: function (data) {
                gridNameS5 = data;
            }

        };
        gridNameD5Grid = initKendoGrid("#gridNameD5Grid" + pathValue, gridNameGridJson);
    }

    // 预处理
    var init6 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "checkSample", title: "核验" },
            { name: "edit", target: "submit", title: "提交" }

        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list", "objects": [[
                    "二代常规DNA建库",
                    "二代常规RNA建库",
                    "三代ONT基因组建库",
                    "代谢建库"],
                ["预处理"]]
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;

                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMx-Check", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS6.push(subGrid_N);
            }
        };
        gridNameD6Grid = initKendoGrid("#gridNameD6Grid" + pathValue, gridNameGridJson);
    }
    // PE任务待处理
    var init7 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit", title: "提交" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list", "objects": [[
                    "二代常规DNA建库",
                    "二代常规RNA建库",
                    "三代ONT基因组建库",
                    "代谢建库"],
                ["前处理提交"]], "search": { "EX_EXECUTE_MODE": "PE" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;

                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMx", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }

        };
        gridNameD7Grid = initKendoGrid("#gridNameD7Grid" + pathValue, gridNameGridJson);
    }

    // PE执行状态
    var init8 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit1", title: "提交" },
            { name: "edit", target: "collection", title: "采集" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list", "objects": [[
                    "二代常规DNA建库",
                    "二代常规RNA建库",
                    "三代ONT基因组建库",
                    "代谢建库"],
                ["已接收"]], "search": { "EX_EXECUTE_MODE": "PE" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;

                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMx", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }

        };
        gridNameD8Grid = initKendoGrid("#gridNameD8Grid" + pathValue, gridNameGridJson);
    }
    //终止
    var init9 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_test_qc_all_task_sh_list_abo",
                "objects": [], "search": { "TASKZJ_ZJ_STATUS": ["终止"] }
            },
        };
        gridNameD9Grid = initKendoGrid("#gridNameD9Grid" + pathValue, gridNameGridJson);

    }
    //排单生成
    var edit = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //判断类型是否全部分同一为,并取出形成类型单
        var g = arrIds;
        var a = "";
        var b = "";
        var code = [];

        for (var i = 0; i < g.length; i++) {

            if (code.indexOf(g[i]["BIO_CODE"]) > -1) {//同一个执行单不允许重复
                //alertMsg("提示:存在所选编号“"+g[i]["BIO_CODE"]+"重复!”");
                //return;
            } else {
                code.push(g[i]["BIO_CODE"]);
            }
            if (i == 0) {
                a = g[i]["LIBRARY_FLOW"];
                b = g[i]["LIBRARY_FLOW"];
            } else {
                a = g[i - 1]["LIBRARY_FLOW"];
                b = g[i]["LIBRARY_FLOW"];
            }

            if (a != b) {
                alertMsg("存在所选记录建库流向“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
                return;
            }

        }

        var ids = [];
        var taskids = [];
        var biocodes = [];
        var samplecodes = [];
        var libtypes = [];
        var libtypemxs = [];
        var LIBQCIDS = [];

        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            libtypemxs.push(arrIds[i]["LIBRARY_TYPE_EN"]);
            biocodes.push(arrIds[i]["BIO_CODE"]);
            samplecodes.push(arrIds[i]["SAMPLE_CODE"]);
            if (arrIds[i]["LIBRARY_TYPE_EN"] == null || arrIds[i]["LIBRARY_TYPE_EN"] == "") {
                alertMsg("提示:存在所选编号“" + arrIds[i]["SAMPLE_CODE"] + "文库类型为空!”");
                return;
            }
            if (libtypes.indexOf(arrIds[i]["LIBRARY_TYPE_EN"]) < 0) {
                libtypes.push(arrIds[i]["LIBRARY_TYPE_EN"]);//文库类型
            }
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/pdup/pdup",
            title: "常规建库排单.."
        };
        openWindow(winOpts, {
            "IDS": ids, "TASKIDS": taskids, "SAMPLECODES": samplecodes, "BIOCODES": biocodes, "LIBTYPES": libtypes,
            "LIBTYPEMXS": libtypemxs, "EX_TYPE": a, "LIBQCIDS": LIBQCIDS, "YC": "否"
        });
    }
    //排单生成
    var editR = function () {
        var arrIds = getGridSelectData(gridNameD4Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //判断类型是否全部分同一为,并取出形成类型单
        var g = arrIds;
        var a = "";
        var b = "";
        var code = [];

        for (var i = 0; i < g.length; i++) {

            //            if (code.indexOf(g[i]["BIO_CODE"]) > -1) {//同一个执行单不允许重复
            //                alertMsg("提示:存在所选编号“" + g[i]["BIO_CODE"] + "重复!”");
            //               return;
            //            } else {
            code.push(g[i]["BIO_CODE"]);
            //            }
            if (i == 0) {
                if (g[i]["LIBRARY_FLOW"] == null || g[i]["LIBRARY_FLOW"] == "") {
                    if (g[i]["LIBRARY_TYPE_EN"] == "Hi-c") {
                        a = "HIC建库";
                        b = "HIC建库";
                    } else if (g[i]["LIBRARY_TYPE_EN"] == "ATAC-seq") {
                        a = "ATAC建库";
                        b = "ATAC建库";
                    }
                } else {
                    a = g[i]["LIBRARY_FLOW"];
                    b = g[i]["LIBRARY_FLOW"];
                }
            } else {
                if (g[i]["LIBRARY_FLOW"] == null || g[i]["LIBRARY_FLOW"] == "") {
                    if (g[i]["LIBRARY_TYPE_EN"] == "Hi-c") {
                        a = "HIC建库";
                        b = "HIC建库";
                    } else if (g[i]["LIBRARY_TYPE_EN"] == "ATAC-seq") {
                        a = "ATAC建库";
                        b = "ATAC建库";
                    }
                } else {
                    a = g[i]["LIBRARY_FLOW"];
                    b = g[i]["LIBRARY_FLOW"];
                }
            }

            if (a != b) {
                alertMsg("存在所选记录建库流向“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
                return;
            }

        }

        var ids = [];
        var taskids = [];
        var biocodes = [];
        var samplecodes = [];
        var libtypes = [];
        var libtypemxs = [];
        var LIBQCIDS = [];


        var INDEX_NAME = [];
        var SEQ_I7_1 = [];
        var SEQ_I7_2 = [];
        var SEQ_I7_3 = [];
        var SEQ_I7_4 = [];
        var SEQ_I5_NAVA = [];
        var SEQ_I5_XTEN = [];
        var GEN_N = [];
        var ER_SEQ = [];
        var LIB_KIT = [];
        var REBUILD_LIB_SCHEME = [];

        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            LIBQCIDS.push(arrIds[i]["LIBQCID"]);

            libtypemxs.push(arrIds[i]["LIBRARY_TYPE_EN"]);
            biocodes.push(arrIds[i]["BIO_CODE"]);
            samplecodes.push(arrIds[i]["SAMPLE_CODE"]);

            INDEX_NAME.push(arrIds[i]["INDEX_NAME"]);
            SEQ_I7_1.push(arrIds[i]["SEQ_I7_1"]);
            SEQ_I7_2.push(arrIds[i]["SEQ_I7_2"]);
            SEQ_I7_3.push(arrIds[i]["SEQ_I7_3"]);
            SEQ_I7_4.push(arrIds[i]["SEQ_I7_4"]);
            SEQ_I5_NAVA.push(arrIds[i]["SEQ_I5_NAVA"]);
            SEQ_I5_XTEN.push(arrIds[i]["SEQ_I5_XTEN"]);
            GEN_N.push(arrIds[i]["GEN_N"]);
            ER_SEQ.push(arrIds[i]["ER_SEQ"]);
            LIB_KIT.push(arrIds[i]["LIB_KIT"]);
            REBUILD_LIB_SCHEME.push(arrIds[i]["REBUILD_LIB_SCHEME"]);

            if (libtypes.indexOf(arrIds[i]["LIBRARY_TYPE_EN"]) < 0) {
                libtypes.push(arrIds[i]["LIBRARY_TYPE_EN"]);//文库类型
            }
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/pdup/pdup",
            title: "常规建库排单.."
        };
        openWindow(winOpts, {
            "IDS": ids, "TASKIDS": taskids, "SAMPLECODES": samplecodes, "BIOCODES": biocodes, "LIBTYPES": libtypes, "LIBTYPEMXS": libtypemxs, "EX_TYPE": a, "LIBQCIDS": LIBQCIDS,
            "INDEX_NAME": INDEX_NAME, "SEQ_I7_1": SEQ_I7_1, "SEQ_I7_2": SEQ_I7_2, "SEQ_I7_3": SEQ_I7_3, "SEQ_I7_4": SEQ_I7_4, "SEQ_I5_NAVA": SEQ_I5_NAVA,
            "SEQ_I5_XTEN": SEQ_I5_XTEN, "GEN_N": GEN_N, "ER_SEQ": ER_SEQ, "LIB_KIT": LIB_KIT, "YC": "是", "REBUILD_LIB_SCHEME": REBUILD_LIB_SCHEME,
        });
    }
    //追加任务
    var addToEx = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/lib/addtoex/addtoex",
            title: "追加样本到执行单.."
        };
        var ids = [];
        var codes = [];
        var taskids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            codes.push(arrIds[i]["BIO_CODE"]);
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
        }
        var LIBQCIDS = [];
        openWindow(winOpts, { "REBUILD_LIB_SCHEME": [],"IDS": ids, "CODES": codes, "LIBQCIDS": LIBQCIDS, "TASKIDS": taskids });
    }
    var doLib = function () {
        var g = getSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        } else if (g.length == 2) {
            alertMsg("请选择一条样本记录进行操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/lib/editjianku",
            title: "修改建库方法.."
        };

        openWindow(winOpts, {
            "ID": g[0]
        });

    }

    //PE任务待处理提交
    var pesubmit = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameD7Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行提交!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行提交!");
            return;
        }
        if (arrIds[0]["PLATE_CODE"] == null) {

            // alertMsg("执行单"+arrIds[0]["EX_DH_NO"]+"尚未分配板孔号，不能提交");
            //  return;
        }
        var PEtoken;
        var PEVariable = { "ClientName": PE_ClientName, ClientPwd: ClientPwd };
        var inobjjson = { "url": PE_URL + "api/clientInfo/login", "PEVariable": PEVariable }
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                PEtoken = rs.apiData.result.token;
            }
        });
        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "queryTaskLibExMx", "objects": [[arrIds[0]["ID"]]] },
            succeed: function (rs) {
                sample = rs.rows;             //样品
            }
        });
        var PEsamples = [];
        for (var j = 0; j < sample.length; j++) {
            PEsamples.push({
                "Well": sample[j]["PLATE_WELL"],
                "SampleNo": sample[j]["SAMPLE_CODE"],
                "LibraryType": sample[j]["LIBRARY_TYPE_EN"],
                "LibraryCode": sample[j]["LIBRARY_CODE"]
            });
        }
        time = sysNowTimeFuncParams["sysNowTime"];
        var PEVariable = {
            "TimeStamp": time,
            "Token": PEtoken,
            "ClientId": "",
            "Cmd": "RNASeq",
            "RQData": {
                "TaskNo": arrIds[0]["EX_DH_NO"],
                "BarCode": arrIds[0]["PLATE_CODE"],
                "Samples": PEsamples,
                "IndexBarCode": arrIds[0]["INDEX_QUADRANT"]
            }
        };
        var inobjjson = { "url": PE_URL + "api/order/create", "PEVariable": PEVariable }
        var RValue;
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                RValue = rs;
            }
        });
        if (!RValue.apiData.success) {
            alertMsg(RValue.apiData.msg);
            return;
        }
        var objectSheet = [];
        objectSheet.push({
            "ID": arrIds[0]["ID"],//id
            "EX_RE_STATUS": "已接收"       //状态
        });
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
        putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
    }
    //采集
    var collection = function () {
        debugger;
        var PSLMap = {
            "SampleName": "PE_SAMPLE_NAME",
            "WellLabel": "PE_WELL_LABEL",
            "Comment": "PE_COMMENT",
            "%Purity": "PE_PURITY",
            "Type": "PE_TYPE",
            "Size[BP]": "PE_REGION_SIZE_BP",
            "Conc.(ng/ul)": "PE_CONC",
            "MigrationTime(sec)-Start": "PE_START",
            "MigrationTime(sec)-End": "PE_END",

            "UserComment": "PE_USERCOMMENT",
            "PeakCount": "PE_PEAKCOUNT",
            "TotalConc.(ng/ul)": "PE_TOTALCONC",
            "UpperMarkerTime(sec)": "PE_UPPERMARKERTIME",
            "EP650Size[BP]": "PE_EP650_SIZE",
            "EP650Conc.(ng/ul)": "PE_EP650_CONC",
            "EP1550Size[BP]": "PE_EP1550_SIZE",
            "EP1550Conc.(ng/ul)": "PE_EP1550_CONC",
            "Region[360-560]Size[BP]": "PE_REGION_360_560_SIZE_BP",
            "Region[360-560]Conc.(ng/ul)": "PE_REGION_360_560_CONC",
            "Region[450-650]Size[BP]": "PE_REGION_450_650_SIZE_BP",
            "Region[450-650]Conc.(ng/ul)": "PE_REGION_450_650_CONC",
            "Region[585-715]Size[BP]": "PE_REGION_585_715_SIZE_BP",
            "Region[585-715]Conc.(ng/ul)": "PE_REGION_585_715_CONC",
            "Result": "PE_RESULT",

            "Region[300-800]Size[BP]": "PE_REGION_300_800_SIZE_BP",
            "Region[300-800]Conc.(ng/ul)": "PE_REGION_300_800_CONC",
            "Region[250-800]Size[BP]": "PE_REGION_250_800_SIZE_BP",
            "Region[250-800]Conc.(ng/ul)": "PE_REGION_250_800_CONC",
            "Region[150-800]Size[BP]": "PE_REGION_150_800_SIZE_BP",
            "Region[150-800]Conc.(ng/ul)": "PE_REGION_150_800_CONC",
            "Region[130-165]Size[BP]": "PE_REGION_130_165_SIZE_BP",
            "Region[130-165]Conc.(ng/ul)": "PE_REGION_130_165_CONC",
            "Region[300-1000]Size[BP]": "PE_REGION_300_1000_SIZE_BP",
            "Region[300-1000]Conc.(ng/ul)": "PE_REGION_300_1000_CONC",

            "Conc": "Conc",

        };
        var arrIds = getGridSelectData(gridNameD8Grid);
        var inobjjson;
        inobjjson = { "EXE_TQQC_ID": arrIds[0]["EX_DH_NO"], "Way": "手动", "PSLMap": PSLMap }


        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/peQCCollection",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson
        });
        alertMsg("提示:操作成功!");

    }
    //追加
    var editB = function () {
        var gridData = getGridSelectData(gridNameD5Grid);
        debugger;
        if (gridData.length != 1) {
            alertMsg("只能选择一个样本");
            return;

        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/libadd/libadd",
            title: "追加执行单.."
        };
        openWindow(winOpts, gridData[0]);//传递

    }
    //自动化排单
    var editA = function () {

        var gridData = getGridSelectData(gridNameD5Grid);
        debugger;
        if (gridData.length == 0) {
            alertMsg("至少选择一个样本");
            return;

        }

        if (gridData.length > 999) {
            alertMsg("单次只能排小于1000个样本！");
            return;
        }

        var params = { "ids": [], "keys": [] };

        for (var j = 0; j < gridData.length; j++) {
            var id = gridData[j]["ID"];
            var lfo2 = gridData[j]["LIBRARY_FLOW"]; //工序
            var wor2 = gridData[j]["MEHOD_JKPLAT"]; //样品执行组
            var met2 = gridData[j]["LIBRARY_METHOD"]; //样品执行方法
         // var lte2 = gridData[j]["LIBRARY_TYPE_EN"]; //文库类型
            var lte2 = " "; //文库类型
            params.ids.push(id);
            if (params.keys.indexOf(lfo2 + "-" + wor2 + "-" + met2 + "-" + lte2) < 0) {
                params.keys.push(lfo2 + "-" + wor2 + "-" + met2 + "-" + lte2);
                params[lfo2 + "-" + wor2 + "-" + met2 + "-" + lte2] = {
                    lfo: lfo2,
                    met: met2,
                    wor: wor2,
                    lte: lte2,
                    ids: []
                };

            }
            params[lfo2 + "-" + wor2 + "-" + met2 + "-" + lte2].ids.push(id);

        }

        var winOpts = {
            url: "biomarker/dispatch/pd/lib/automate/automate",
            title: "自动排单明细.."
        };
        openWindow(winOpts, params);//传递

    }
    //追加任务
    var addToExRe = function () {
        var arrIds = getGridSelectData(gridNameD4Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/addtoex/addtoex",
            title: "追加样本到执行单.."
        };
        var ids = [];
        var codes = [];
        var LIBQCIDS = [];
        var taskids = [];
        var REBUILD_LIB_SCHEME = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            codes.push(arrIds[i]["BIO_CODE"]);
            REBUILD_LIB_SCHEME.push(arrIds[i]["REBUILD_LIB_SCHEME"]);
            LIBQCIDS.push(arrIds[i]["LIBQCID"]);
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
        }

        openWindow(winOpts, { "REBUILD_LIB_SCHEME": REBUILD_LIB_SCHEME, "IDS": ids, "CODES": codes, "LIBQCIDS": LIBQCIDS, "TASKIDS": taskids });
    }
    var doLib = function () {
        var g = getSelectData(gridNameD1Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        } else if (g.length == 2) {
            alertMsg("请选择一条样本记录进行操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/lib/editjianku",
            title: "修改建库方法.."
        };

        openWindow(winOpts, {
            "ID": g[0]
        });

    }

    //样品核验 
    var checkSample = function () {
        var arrIds = getGridSelectData(gridNameD6Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行核验操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/lib/check/check",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递
    }
    //预处理提交
    var submit = function () {
        var arrIds = getGridSelectData(gridNameD6Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        /*
                var sample;
                $.fn.ajaxPost({
                    ajaxUrl: "system/jdbc/query/one/table",
                    ajaxType: "post",
                    ajaxData: { "query": "queryTaskLibExMx-Check", "objects": [[arrIds[0]["ID"]]] },
                    succeed: function (rs) {
                        sample = rs.rows;             //样品
                var num = 0;
                for (var j = 0; j < sample.length; j++) {
                    if (sample[j]["JK_CHECK"] == "OK") {
                        num = num + 1;
        
                    }
                }
                if (num < sample.length) {
                    alertMsg("还有"+(sample.length-num)+"条未核验，不能提交");
                          return;
                } */
        var objectSheet = [];
        objectSheet.push({
            "ID": arrIds[0]["ID"],//id
            "EX_RE_STATUS": "待审核"       //状态

        });
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
        putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
        //    }
        //  });

    }
    //文编号
    var doGenNo = function () {
        order = [];
        order_mn = [];
        ordersm = [];
        ordersm_mn = [];
        ordersm_no = [];
        ordersm_no_mn = [];
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //取对应表
        var params = { "query": "queryBioLibTypeList", "objects": [] };
        var iniFist = "";
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var libtypename = [];
                    var initials = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        libtypename.push(row["LIB_TYPE_NAME"]);
                        initials.push(row["INITIALS"]);
                    }
                    var objectup = [];
                    debugger;
                    for (var i = 0; i < g.length; i++) {
                        //更新记录
                        iniFist = checkInitals(g[i]["LIBRARY_TYPE_EN"], libtypename, initials);
                        if (g[i]["LIBRARY_CODE"]) {
                            alertMsg("文库编号“" + g[i]["LIBRARY_CODE"] + "”已存在!");
                        } else {
                            objectup.push({
                                "ID": g[i]["LIBID"],
                                "LIBRARY_CODE": getLibCodeNo(g[i]["TASK_LS_NO"], iniFist, g[i]["DON"], g[i]["LIBMAXCODE"], g[i]["ISPOOLSM"], g[i]["SAMPLE_CODE"], g[i]["DATA_LIBCODE"])
                            });
                        }
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "提交");

                }
            }
        });

    }

    //切胶编号
    var doQJNo = function () {
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            //更新记录
            objectup.push({
                "ID": g[i]["LIBID"],//关联更新ID
                "JK_QJ_CODE": g[i]["BIO_CODE"] + "bp" + getBioCodeNo(g[i]["QJN"] + 1)
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");

    }

    //比对取对照
    var checkInitals = function (name, names, initals) {
        for (var i = 0; i < names.length; i++) {
            if (name == names[i]) {
                return initals[i];
            }
        }
        return "";
    }
    var getLibCodeNo = function (orderno, c, n, maxcode, ispoolsm, smcode, datalibcode) {
        if (ispoolsm == "拆文库") {//拆文库
            if (maxcode) {//已生成过文库
                var maxcode_1 = maxcode.substr(0, maxcode.lastIndexOf("-") + 1);
                var maxcode_2 = maxcode.replace(maxcode_1, "");
                maxcode_2 = parseInt(maxcode_2);
                var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
                var mn;
                if (indexn > -1) {
                    mn = ordersm_no_mn[indexn] + 1;
                    ordersm_no_mn[indexn] = mn;
                } else {
                    mn = maxcode_2 + 1;
                    ordersm_no.push(orderno + "_" + smcode + "_no");
                    ordersm_no_mn.push(mn);
                }
                return maxcode_1 + getNo1(mn, 0);
            } else {//没有生成过文库
                if (datalibcode) { //运营提前生产
                    var maxcode_1 = datalibcode.substr(0, datalibcode.lastIndexOf("-") + 1);
                    var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
                    var mn;
                    if (indexn > -1) {
                        mn = ordersm_no_mn[indexn] + 1;
                        ordersm_no_mn[indexn] = mn;
                    } else {
                        mn = 1;
                        ordersm_no.push(orderno + "_" + smcode + "_no");
                        ordersm_no_mn.push(mn);
                    }
                    return maxcode_1 + getNo1(mn, 0);
                } else {//运营没有提前生产
                    var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
                    var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
                    var mn;
                    if (indexn > -1) {
                        mn = ordersm_no_mn[indexn] + 1;
                        ordersm_no_mn[indexn] = mn;
                    } else {
                        mn = 1;
                        ordersm_no.push(orderno + "_" + smcode + "_no");
                        ordersm_no_mn.push(mn);
                    }
                    var indexn2 = order.indexOf(orderno);
                    var indexn3 = ordersm.indexOf(orderno + "_" + smcode);
                    var mn2;
                    if (indexn3 > -1) {
                        mn2 = ordersm_mn[indexn3];
                    } else if (indexn2 > -1) {
                        mn2 = order_mn[indexn2] + 1;
                        order_mn[indexn2] = mn2;
                        ordersm.push(orderno + "_" + smcode);
                        ordersm_mn.push(mn2);
                    } else {
                        mn2 = n + 1;
                        order.push(orderno);
                        order_mn.push(mn2);
                        ordersm.push(orderno + "_" + smcode);
                        ordersm_mn.push(mn2);
                    }

                    tempcode = tempcode + c + getNo(mn2, 0) + "-" + getNo1(mn, 0);

                    return tempcode;
                }
            }
        } else {//拆样
            if (maxcode) {//已生成过文库
                var a = maxcode.split("-");
                var mn = parseInt(a[a.length - 1]) + 1;//取最后的数
                //重新接回形成新的编号
                var tempcode = a[0];
                for (var i = 1; i < a.length - 1; i++) {
                    tempcode = tempcode + "-" + a[i];
                }
                tempcode = tempcode + "-" + getBioCodeNo(mn);
                return tempcode;
            } else {//没有生成过文库
                if (datalibcode) { //运营提前生产
                    var a = datalibcode.split("-");
                    var mn = parseInt(a[a.length - 1]) + 1;//取最后的数
                    //重新接回形成新的编号
                    var tempcode = a[0];
                    for (var i = 1; i < a.length - 1; i++) {
                        tempcode = tempcode + "-" + a[i];
                    }
                    tempcode = tempcode + "-01";
                    return tempcode;
                } else {//运营没有提前生产
                    var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
                    var indexn = order.indexOf(orderno);
                    var mn = 1;
                    if (indexn > -1) {
                        mn = order_mn[indexn] + 1;
                        order_mn[indexn] = mn;
                    } else {
                        order.push(orderno);
                        order_mn.push(n + 1);
                        mn = n + 1;
                    }
                    tempcode = tempcode + c + getNo(mn, 0) + "-01";
                    return tempcode;
                }
            }
        }
    }
    //文库/切胶流程号段(项目期号内)
    var getNo = function (num, ki) {
        num = num + ki;
        if (num < 10) {
            num = "000" + num;
            return num;
        }
        if (num >= 10 && num < 100) {
            num = "00" + num;
            return num;
        }
        if (num >= 100 && num < 1000) {
            num = "0" + num;
            return num;
        }
        return num;
    }
    //文库/切胶流程号段(项目期号内)
    var getNo1 = function (num, ki) {
        num = num + ki;
        if (num < 10) {
            num = "0" + num;
            return num;
        }
        return num;
    }


    //核酸号段(项目期号内)
    var getBioCodeNo = function (num) {
        if (num < 10) {
            num = "0" + num;
            return num;
        }
        return num;
    }

    var editP = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/editP/editP",
            title: "补充任务单计划.."
        };
        openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
    }
    //分派index
    var doGetIndex = function () {
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //检查是不是为同一文库类型
        var libtype = "";
        for (var i = 0; i < g.length; i++) {
            if (i == 0) libtype = g[i]["LIBRARY_TYPE_EN"];
            if (libtype != g[i]["LIBRARY_TYPE_EN"]) {
                alertMsg("提示:无法分配,存在不同的文库类型(<font color=#ff0000>" + libtype + "与" + g[i]["LIBRARY_TYPE_EN"] + "</font>)!");
                return;
            }
        }

        var params = { "INDEX_TYPE": libtype, "INDEX_NUMBER": g.length };
        $.fn.ajaxPost({
            ajaxUrl: "bio/lib/modular/listIndex",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] == 1) {
                    var rows = result["INDEX"];
                    var objectup = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        objectup.push({
                            "ID": g[i]["LIBID"],//关联更新ID
                            "INDEX_NAME": row["INDEX_NAME"],//Index名称
                            "SEQ_I7_1": row["SEQ_I7_1"],//I7端(或正向)
                            "SEQ_I7_2": row["SEQ_I7_2"],//I7端2
                            "SEQ_I7_3": row["SEQ_I7_3"],//I7端3
                            "SEQ_I7_4": row["SEQ_I7_4"],//I7端4
                            "SEQ_I5_NAVA": row["SEQ_I5_NAVA"],//I5端-Nova(或反向)
                            "SEQ_I5_XTEN": row["SEQ_I5_XTEN"],//I5端-Xten
                            "GEN_N": row["GEN_N"],//错位碱基数
                            "ER_SEQ": row["ER_SEQ"],//错位碱基序列
                            "LIB_KIT": row["LIB_KIT"]//建库试剂盒
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "更新index");
                } else {
                    alertMsg("提示:无法分配,请检查“" + libtype + "”类型的index库是否维护!</font>)!");
                }
            }
        });

    }
    //提交
    var doOK = function () {
        debugger;
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        if (arrIds.length > 1) {
            alertMsg("请最多选择一条记录进行操作!");
            return;
        }


        //校验核酸编号是否为空
        var s = "";
        var si = "";
        var params = { "query": "doCheckLibCodeIsPass", "objects": [arrIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var rows = result["rows"];

                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];

                        if (row["EX_TYPE"] != "代谢建库" && row["EX_TYPE"] != "蛋白前处理") {
                            if (row["EX_TYPE"] == "三代ONT基因组建库") {//判断切胶编号
                                if (row["JK_QJ_CODE"] == "" || row["JK_QJ_CODE"] == null) {
                                    if (s == "") {
                                        s = row["EX_DH_NO"];
                                    }
                                }
                            } else {
                                if (row["LIBRARY_CODE"] == "" || row["LIBRARY_CODE"] == null) {
                                    if (s == "") {
                                        s = row["EX_DH_NO"];
                                    }
                                }
                                if (row["INDEX_NAME"] == "" || row["INDEX_NAME"] == null) {
                                    if (si == "") {
                                        si = row["EX_DH_NO"];
                                    }
                                }

                            }
                        }


                    }

                    if (s != "") {
                        alertMsg("提示:单号“" + s + "”存在文库编号(或切胶编号为空--ONT)为空!");
                        return;
                    }
                    if (si != "") {
                        if (rows[0]["EX_MAN"] == "实验-委外") {
                            objectup.push({
                                "ID": rows[0]["ID"],//联联任务ID
                                "EX_RE_STATUS": "已提交委外"
                            });
                            var urlsend = "system/jdbc/save/batch/table";
                            var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
                            putAddOrUpdata(urlsend, paramsup, "是", "提交");
                            return;
                        }
                        alertMsg("提示:单号“" + si + "”存在“index名称”为空!");
                        return;
                    }
                    var objectup = [];
                    for (var i = 0; i < arrIds.length; i++) {
                        var time = sysNowTimeFuncParams["sysNowTime"];
                        var username = getLimsUser()["name"];
                        objectup.push({
                            "ID": arrIds[i],//联联任务ID
                            "EX_RE_STATUS": "待接收"
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "提交");
                    doRequeDoUpTaskLibmxStatus(arrIds, "建库待接收");
                }
            }
        });
    }



    //任务单-对应执行单下明细状态修改  
    var doRequeDoUpTaskLibmxStatus = function (mainExIds, status) {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var params = { "query": "doRequeDoUpTaskLibmxStatus", "objects": [mainExIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        //更新记录---明细
                        objectup.push({
                            "ID": row["TASKLIBMXID"],
                            "TASK_LSMX_STATUS": status
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "同步更新任务明细:");
                }
            },
            failed: function (result) {
                alertMsg("提示:操作异常!", "error");
            }
        });
    }


    //撤回doRequeDoUpTaskLibmxStatus
    var doReturn = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        var arrIds = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["EX_RE_STATUS"] != "待接收") {
                alertMsg("操作失败,所选记录存在已“已接收”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "EX_RE_STATUS": "待审核"
                });
                arrIds.push(g[i]["ID"]);
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
        doRequeDoUpTaskLibmxStatus(arrIds, "草稿");

    }
    //移至待审核
    var doReturn2 = function () {
        var g = getGridSelectData(gridNameD3Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }

   var username = getLimsUser()["name"];
        if (username != "蓝勇胜" && username != "徐彦岭"&& username != "王宏"&& username != "曹利群"&& username != "郭丽丽"&& username != "冯璨") {
            alertMsg("无此权限!请联系徐彦岭!");
            return;
       }


        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["TASK_LS_STATUS"] == "暂停" || g[i]["TASK_LS_STATUS"] == "未建库" || g[i]["TASK_LS_STATUS"] == "建库完成") {
                objectup.push({
                    "ID": g[i]["ID"],
                    "TASK_LS_STATUS": "已审核"
                });
            } else {
                alertMsg("操作失败,只有“<font color=#ff0000>暂停、未建库</font>”状态方允许操作!");
                return;
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_TASK_LIB", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }

    //任务单状态修改
    var doTaskStatus = function () {
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行审核操作!");
            return;
        }
        debugger;
        var ids = [];
        lsmkeyps = [];
        var day1 = new Date();
        day1.setTime(day1.getTime() - 24 * 60 * 60 * 1000);
        var s1 = day1.getFullYear() + "-" + (day1.getMonth() + 1) + "-" + day1.getDate();
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            lsmkeyps.push(arrIds[i]["LSM_KEY_P"]);
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/uptaskstatus/uptaskstatus",
            title: "修改任务单状态.."
        };
        openWindow(winOpts, { "arrIds":arrIds, "IDS": ids, "LSMKEYP": lsmkeyps, "JK_END_LDATE": s1 });
    }
    //样本状态修改
    var upsmStatus = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/upsmstatus/upsmstatus",
            title: "修改样本状态.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    }

    //修改实验员 
    var doUpdate = function () {
        var arrIds = getGridSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/updateman/updateman",
            title: "修改实验员.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"],"EX_MAN3": arrIds[0]["EX_MAN3"], "EX_MAN": arrIds[0]["EX_MAN"], "EX_EXECUTE_MODE": arrIds[0]["EX_EXECUTE_MODE"] });//传递
    }
    //记录移除
    var remove = function () {
        var arrg = [];
        var arrIds = [];
        var obj = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            arrg = arrg.concat(arrSubID);

        }
        if (arrg.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        for (var i = 0; i < arrg.length; i++) {
            arrIds.push(arrg[i]["LIBID"]);
            obj.push({ "ID": arrg[i]["ID"], "TASK_LSMX_STATUS": "待建库","RL_STATUS": "待排" });
        }
        confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd1 = { "tableName": "BIO_TASK_LIBMX", "objects": obj };
            putAddOrUpdata(urlsend, paramsadd1, "否", "移除");
            var params = { "tableName": "BIO_LIB_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);


        });

    }

    //删除执行单
    var doDelete = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {



			var sample;
			$.fn.ajaxPost({
				ajaxUrl: "system/jdbc/query/one/table",
				ajaxType: "post",
				ajaxAsync: false,
				ajaxData: { "query": "queryTaskLibExMx", "objects": [arrIds] },
				succeed: function (rs) {
					sample = rs.rows;             //样品
				}
			});
			var obj = [];
			for (var i = 0; i < sample.length; i++) { 
				obj.push({ "ID": sample[i]["ID"], "TASK_LSMX_STATUS": "待建库","RL_STATUS": "待排" });
			}
            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd1 = { "tableName": "BIO_TASK_LIBMX", "objects": obj };
            putAddOrUpdata(urlsend, paramsadd1, "否", "移除");





            var url = "system/jdbc/delete/one/table/where";
            var params1 = { "tableName": "BIO_LIB_INFO", "where": { "EXE_TQQC_ID": arrIds } };
            deleteGridDataByIds(url, params1, refreshGrid);
            var params2 = { "tableName": "EXE_TQQC_SHEET", "where": { "ID": arrIds } };
            deleteGridDataByIds(url, params2, refreshGrid);
        });
    }

    var setjira = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length != 1) {
            alertMsg("请选择一条数据进行操作!");
            return;
        }
        //////////////////////////////////////////////////////////////////////////////新增jira需求220602
        var rows1;
        var url = "query_BIO_TASK_LIBMX_list";
        if (arrIds[0]["TASK_LS_TYPE"] == "代谢建库") url = "query_BIO_TASK_LIBMX_list-dx";
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": url, "objects": [], "search": { "TASK_LS_ID": [arrIds[0]["ID"]] } },
            succeed: function (rs) {
                //console.log(rs);				
                rows1 = rs["rows"];
            }
        });

        var THE_DATA_SUM = 0;
        var type;
        for (var i = 0; i < rows1.length; i++) {
            THE_DATA_SUM += rows1[i]["DATA_SUM"];
            type = rows1[i]["LIBRARY_TYPE_EN"];
        }
        var dwtype = rows1[0]["DATA_UNIT"];
        var cyc_dws = rows1[0]["CYC_DW"];
        var business_unit = rows1[0]["BUSINESS_UNIT"];

        doCyc(type, rows1.length, THE_DATA_SUM, "测序标准用时", dwtype, cyc_dws, business_unit);

        //doCyc(type, rows1.length, THE_DATA_SUM, "实验交付标准用时", dwtype, cyc_dws, business_unit);
        var p = arrIds[0];
        var time = Date.parse(sysNowTimeFuncParams["sysNowTime"]);
        var customield_10227 = time + (cxdates * 86400000) - 86400000;
        //var customield_10226 = time + (sydates * 86400000);




        //////////////////////////////////////////////////////////////////////////////
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/jira/jira",
            title: "jira信息填写..",
            width: 1280,
            height: 480,
            position: { "top": 100, "left": 30 }
        };
        openWindow(winOpts, {
            "ID": p["LSMID"],
            "MAIN_ID": p["ID"],
            "LSM_KEY": p["LSM_KEY"],//LSM关键字
            "LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字
            "CUSTOMFIELD_12101": p["TASK_LS_LDATE"],//建库标准结单日期
            "CUSTOMFIELD_12100": p["TASK_LS_CDATE"],//建库测序任务单下达日期
            // "CUSTOMFIELD_12103":p[""],//建库完成日期
            "CUSTOMFIELD_14201": p["TASK_TEST_DELIVERDATE"],//建库计划完成日期
            "CUSTOMFIELD_10227": customield_10227,//测序标准完成日期
            "CUSTOMFIELD_14202": customield_10227,//测序计划完成日期
            // "CUSTOMFIELD_15508":p[""],//预实验建库完成日期
            "CUSTOMFIELD_15530": p["TASK_LS_CDATE"],//预实验SLAF酶切方案下达日期
        });

    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var callBack = function () {
        refreshGrid();
    };
    var editZZ = function () {
        var arrIds = getGridSelectData(gridNameD4Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        var objectup = [];
        for (let index = 0; index < arrIds.length; index++) {
            objectup.push({
                "ID": arrIds[index]["LIBQCID"],
                "TASKZJ_ZJ_STATUS": "终止"
            })
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_LIB_QC_INFO", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");

    };

    var refreshGrid = function () {
        gridNameS = [];
        gridNameS1 = [];
        gridNameS2 = [];
        gridNameS3 = [];
        gridNameS5 = [];
        gridNameS6 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }
        if (gridNameD3Grid) {
            gridNameD3Grid.dataSource.read();
        }
        if (gridNameD4Grid) {
            gridNameD4Grid.dataSource.read();
        }
        if (gridNameD5Grid) {
            gridNameD5Grid.dataSource.read();
        }
        if (gridNameD6Grid) {
            gridNameD6Grid.dataSource.read();
        }
        if (gridNameD7Grid) {
            gridNameD7Grid.dataSource.read();
        }
        if (gridNameD8Grid) {
            gridNameD8Grid.dataSource.read();
        }
        if (gridNameD9Grid) {
            gridNameD9Grid.dataSource.read();
        }
    }


    //获取周期定义,推算出截止结果日期
    var doCyc = function (type, countSm, smnumber, dep, sa, cyc_dws, business_unit) {
        debugger;
        //测序标准用时
        var cycdw = cyc_dws;
        var bus = business_unit;

        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm] };
        } else {
            flag = 1;
            if (sa == "CELL") {
                if (type == "Denovo-Pac(20-30K)") smnumber = smnumber * 140;
                if (type == "Denovo-Pac(hifi)") smnumber = smnumber * 24;
                if (type == "Iso-RNA(Pac)") smnumber = smnumber * 300;
                if (type == "DNA-8K(ONT)") smnumber = smnumber * 80;
                if (type == "Denovo-20k(ONT)") smnumber = smnumber * 80;
                if (type == "Iso-RNA(ONT)") smnumber = smnumber * 140;
            }
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber] };
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    debugger;
                    var rows = result["rows"];
                    var m = getMyMonth();
                    var dateNumber = 0;
                    var seleDateFlag = "工作日";//日历取向
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        seleDateFlag = row["CYC_FLAG"];
                        if (m == 1) dateNumber = row["MONTH_1"];
                        if (m == 2) dateNumber = row["MONTH_2"];
                        if (m == 3) dateNumber = row["MONTH_3"];
                        if (m == 4) dateNumber = row["MONTH_4"];
                        if (m == 5) dateNumber = row["MONTH_5"];
                        if (m == 6) dateNumber = row["MONTH_6"];
                        if (m == 7) dateNumber = row["MONTH_7"];
                        if (m == 8) dateNumber = row["MONTH_8"];
                        if (m == 9) dateNumber = row["MONTH_9"];
                        if (m == 10) dateNumber = row["MONTH_10"];
                        if (m == 11) dateNumber = row["MONTH_11"];
                        if (m == 12) dateNumber = row["MONTH_12"];

                        break;
                    }
                    //执行天数
                    saveRemind = 1;
                    if (dep == "测序标准用时") {
                        cxdates = dateNumber;
                    } else {
                        sydates = dateNumber;
                    }
                    // $("#TASK_EXCDAYS" + pathValue).val(dateNumber);
                    // $("#CYC_FLAG" + pathValue).val(seleDateFlag);
                    // doGetEndDate(seleDateFlag, dateNumber);
                }
            }
        });

    }
    //推算截止日期
    var doGetEndDate = function (seleDateFlag, dateNumber) {

        var thedate = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    for (var i = 0; i < dateNumber; i++) {
                        var base = 1000 * 60 * 60 * 24;
                        //thedate=new Date(thedate.getTime() + base); 
                        if (i == 0) {
                            var TASK_LLS = paramsValue["TASK_LL"] * 1;
                            thedate = new Date(TASK_LLS + (base));
                        } else {
                            //TASK_LLS=new Date(thedate.getTime() + base);
                            thedate = new Date(thedate.getTime() + base);
                        }
                        for (var j = 0; j < noDoDateS.length; j++) {
                            if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                thedate = new Date(thedate.getTime() + base);//日期向前一天
                            }
                        }

                    }
                    //推算出的最终截止日期
                    // $("#TASK_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                    // $("#TASK_JH_ENDDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));

                }
            }
        });

    }


    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }



    var generatePlate = function () {
        debugger;



        var object = getGridSelectData(gridNameD1Grid);
        var EX_E_TIME = null;
        for (var i = 0; i < object.length; i++) {
            if (object[i]["PLATE_CODE"]) {
                alertMsg("执行单【" + object[i]["EX_DH_NO"] + "】已经生成96孔板，请重新选择");
                return;
            }
            if (EX_E_TIME == null) {
                EX_E_TIME = object[i]["EX_E_TIME"]
            } else {
                if (EX_E_TIME != object[i]["EX_E_TIME"]) {
                    alertMsg("执行单出库日期不一致，请重新选择");
                    return;
                }
            }

        }
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据生成板号!");
            return;
        }
        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "queryTaskLibExMx", "size": 2000,"objects": [arrIds] },
            succeed: function (rs) {
                sample = rs.rows;             //样品
            }
        });
        if (sample.length < 80) {
            alertMsg("所选样本小于80个，不能生成96板");
            return;
        }
        if (sample.length > 96) {
            alertMsg("所选样本大于96个，不能生成96板");
            return;
        }


        var PLATE_NO = toDateFormatByZone(EX_E_TIME, "yyyyMMdd"); // 前缀
        var PLATE_TYPE = "W";    //关键字
        var objectPNS = [];

        var queryResults;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/database/execute/sqlcode",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: {
                "sqlcode": "select ID, BOARD_SEQUENCE from BIO_PLATE_NUMBER_SEQUENCE where PLATE_TYPE = '" + PLATE_TYPE + "' and PLATE_NO = '" + PLATE_NO + "'"
            },
            succeed: function (rs) {
                queryResults = rs.rows;
            }
        });

        var BOARD_SEQUENCE;
        if (queryResults.length) {
            BOARD_SEQUENCE = queryResults[0]["BOARD_SEQUENCE"] + 1;
            objectPNS.push({
                "ID": queryResults[0]["ID"],
                "BOARD_SEQUENCE": BOARD_SEQUENCE
            });
        } else {
            BOARD_SEQUENCE = 1;
            objectPNS.push({
                "PLATE_TYPE": PLATE_TYPE,
                "PLATE_NO": PLATE_NO,
                "BOARD_SEQUENCE": BOARD_SEQUENCE
            });
        }
        if (BOARD_SEQUENCE <= 9) {
            BOARD_SEQUENCE = "0" + BOARD_SEQUENCE
        }

        var no = PLATE_NO + "-" + PLATE_TYPE + BOARD_SEQUENCE;
        var objectLIB = [];
        for (var i = 0; i < sample.length; i++) {

            if (sample[i]["LIBRARY_CODE"] == "" || sample[i]["LIBRARY_CODE"] == null) {
                alertMsg("样本【" + sample[i]["SAMPLE_CODE"] + "】未生成文库编号，请先生成文库编号");
                return;
            }
            objectLIB.push({
                "ID": sample[i]["LIBID"],
                "PLATE_CODE": no,
                "PLATE_WELL": getWell(i, "竖排"),
            });
        };

        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd = { "tableName": "BIO_PLATE_NUMBER_SEQUENCE", "objects": objectPNS };
        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");
        var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectLIB };
        putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");
    }

    var getWell = function (data, tvo) {

        if (tvo == "竖排") {
            var k = Math.floor(data / 8) + 1;
            var l = data % 8;
            switch (l) {
                case 0:
                    z = "A";
                    break;
                case 1:
                    z = "B";
                    break;
                case 2:
                    z = "C";
                    break;
                case 3:
                    z = "D";
                    break;
                case 4:
                    z = "E";
                    break;
                case 5:
                    z = "F";
                    break;
                case 6:
                    z = "G";
                    break;
                case 7:
                    z = "H";
                    break;

            }
            if (k < 10) {
                var x = z + "0" + k;
            } else {
                var x = z + k;
            }
            return x;
        } else {
            var k = Math.floor(data / 12);
            var l = data % 12
            switch (k) {
                case 0:
                    z = "A";
                    break;
                case 1:
                    z = "B";
                    break;
                case 2:
                    z = "C";
                    break;
                case 3:
                    z = "D";
                    break;
                case 4:
                    z = "E";
                    break;
                case 5:
                    z = "F";
                    break;
                case 6:
                    z = "G";
                    break;
                case 7:
                    z = "H";
                    break;

            }
            if (k < 10) {
                var x = z + "0" + l;
            } else {
                var x = z + l;
            }
            return x;
        }
    }
    //重建库方案填写
    var schemeFilling = function () {
        debugger;
        var arrIds = getSelectData(gridNameD4Grid, "LIBID");
        if (arrIds.length != 1) {
            alertMsg("请只选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/lib/schemeFilling/schemeFilling",
            title: "重建库方案填写"
        };

        openWindow(winOpts, { "ID": arrIds[0] });
    }
    
    var exportDetails = function () {

        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length <1 ) {
            alertMsg("请至少选择一条任务单记录进行操作!");
            return;
        }
        saveResultDataToExcel({
            requestData:{
                ajaxData:{"query":"query_BIO_TASK_LIBMX_list","size":5000,"objects":[], "search": { "TASK_LS_ID": arrIds } },
            }
        });

    }



    funcPushs(pathValue, {
        "initData": initData,
        "exportDetails":exportDetails,
        "generatePlate": generatePlate,
        "init": init,
        "edit": edit,
        "editR": editR,
        "editP": editP,
        "editA": editA,
        "editB": editB,
        "submit": submit,
        "upsmStatus": upsmStatus,
        "checkSample": checkSample,
        "addToEx": addToEx,
        "addToExRe": addToExRe,
        "doTaskStatus": doTaskStatus,
        "doOK": doOK,
        "doGenNo": doGenNo,
        "doQJNo": doQJNo,
        "pesubmit": pesubmit,
        "collection": collection,
        "remove": remove,
        "doDelete": doDelete,
        "doReturn": doReturn,
        "doReturn2": doReturn2,
        "editZZ": editZZ,
        "doGetIndex": doGetIndex,
        "refreshGrid": refreshGrid,
        "doUpdate": doUpdate,
        "callBack": callBack,
        "doLib": doLib,
        "schemeFilling": schemeFilling,
        "setjira": setjira,
    });
});