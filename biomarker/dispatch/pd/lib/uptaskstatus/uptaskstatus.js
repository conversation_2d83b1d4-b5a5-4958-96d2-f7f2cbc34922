$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-lib-uptaskstatus-uptaskstatus";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "BIO_TASK_LIB"
        };
    }
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
    }

    var subUpData = function () {
        var ids = paramsValue["IDS"];
        var arrIds = paramsValue["arrIds"];
        var LSMKEYPS = paramsValue["LSMKEYP"];

        var object = [];
        var jsonData = getJsonByForm("form", pathValue);//获取表单json
        var endDate = jsonData["JK_END_LDATE"];


        var Str = "";

        var params2 = [];

        for (var i = 0; i < arrIds.length; i++) {

            if (jsonData["TASK_LS_STATUS"] == "建库完成" && arrIds[i]["TASK_SEQ_EDATE"] == null && arrIds[i]["TASK_LS_TYPE"] == "常规建库") {

                var IS_SHORT_PERIOD = "否";
                var IS_FULL_LIFE_CYCLE2 = "否";
                var IS_FULL_LIFE_CYCLE = arrIds[i]["IS_FULL_LIFE_CYCLE"];
                if (IS_FULL_LIFE_CYCLE == "是") {
                    IS_FULL_LIFE_CYCLE2 = "是"
                } else {
                    var CUSTOMER_VIP = arrIds[i]["CUSTOMER_VIP"];
                    if (CUSTOMER_VIP.indexOf(2) > -1) {
                        IS_SHORT_PERIOD = "是";
                    }
                }
                var cc = doCycJK(arrIds[i]["TASK_LIB_TYPE"], arrIds[i]["TASK_LS_SAMPLESUM"], arrIds[i]["THE_DATA_SUM"], "测序标准用时", 2, arrIds[i]["CYC_DW"], arrIds[i]["BUSINESS_UNIT"], jsonData["JK_END_LDATE"], IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);

                if (!cc) {
                    Str = Str + arrIds[i]["TASK_LS_NO"] + ",";
                } else {
                    object.push($.extend({}, jsonData, { "ID": arrIds[i]["ID"], "TASK_SEQ_EDATE": cc["TASK_SEQ_EDATE"] }));//表单值继承
                    params2.push({
                        "jiraKey": arrIds[i]["LSM_KEY_P"],
                        "oldStatusName": "建库",
                        "statusName": "测序",
                        "updateField": {
                            "customfield_12103": endDate,
                            "customfield_10227": cc["TASK_SEQ_EDATE"]
                        }
                    });
                }

            } else {
                object.push($.extend({}, jsonData, { "ID": arrIds[i]["ID"] }));//表单值继承
            }

        }
        if(Str.length>1){
            
            alertMsg("任务单编号【"+Str+"】测序标准用时查询为空，请维护|！");

        }
        for (var i = 0; i < params2.length; i++) {
            putToJira(params2[i]);
        }
        var urlsend = "system/jdbc/save/batch/table";
          var paramsadd = { "tableName": "BIO_TASK_LIB", "objects": object };
        putAddOrUpdata(urlsend, paramsadd, "是", "更新");


    }


    var doJira = function (IDS, keys2, yDate,) {
        debugger;
        var params2 = [];
        for (var i = 0; i < IDS.length; i++) {
            params2.push({
                "jiraKey": keys2[i],
                "oldStatusName": "建库",
                "statusName": "测序",
                "updateField": {
                    "customfield_12103": yDate
                }
            });
        }
        for (var i = 0; i < IDS.length; i++) {
            putToJira(params2[i]);
        }
    }
    var putToJira = function (params) {
        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (result.apiData.flag == "true" || result.apiData.flag) {
                        alertMsg(result.apiData.message);
                    } else {
                        alertMsg("提示:推送失败(<font color=#ff0000>" + result.apiData.message + "</font>)!");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var submit = function () {debugger
        subUpData();
    }


    //获取周期定义,推算出截止结果日期
    var doCycJK = function (type, countSm, smnumber, dep, selflag, CYC_DW, BUSINESS_UNIT, TASK_LS_CDATE, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2) {
        //样品提取检测标准用时
        var cycdw = CYC_DW;
        var bus = BUSINESS_UNIT;
        var params = "";
        var flag = 0;
        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        } else {
            flag = 1;
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        }
        var rows;

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    rows = result["rows"];
                } else {
                    return false;
                }
            }
        });


        var m = getMyMonth();
        var dateNumber = 0;
        var seleDateFlag = "工作日";//日历取向
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            seleDateFlag = row["CYC_FLAG"];
            if (m == 1) dateNumber = row["MONTH_1"];
            if (m == 2) dateNumber = row["MONTH_2"];
            if (m == 3) dateNumber = row["MONTH_3"];
            if (m == 4) dateNumber = row["MONTH_4"];
            if (m == 5) dateNumber = row["MONTH_5"];
            if (m == 6) dateNumber = row["MONTH_6"];
            if (m == 7) dateNumber = row["MONTH_7"];
            if (m == 8) dateNumber = row["MONTH_8"];
            if (m == 9) dateNumber = row["MONTH_9"];
            if (m == 10) dateNumber = row["MONTH_10"];
            if (m == 11) dateNumber = row["MONTH_11"];
            if (m == 12) dateNumber = row["MONTH_12"];

            break;
        }
        //项目周期
        if (dateNumber == 0) {
            return false;
        } else {
            var aa = doGetEndDateJK(seleDateFlag, dateNumber, dateNumber, selflag, TASK_LS_CDATE);
            return aa;
        }


    }

    //推算截止日期
    var doGetEndDateJK = function (seleDateFlag, dateNumber1, dateNumber2, selflag, TASK_LS_CDATE) {
        var thedate = new Date(TASK_LS_CDATE );
        var thedate2 = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }
        var rows
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    rows = result["rows"];
                }
            }
        });

        var noDoDateS = [];
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
        }
        for (var i = 0; i < dateNumber1; i++) {
            var base = 1000 * 60 * 60 * 24;
            thedate = new Date(thedate.getTime() + base);
            for (var j = 0; j < noDoDateS.length; j++) {
                if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                    thedate = new Date(thedate.getTime() + base);//日期向前一天
                }
            }

        }
        return {
            "TASK_SEQ_EDATE": toDateFormatByZone(thedate, "yyyy-MM-dd")
        }



    }
    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});