$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-lib-pdup-pdup";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "EXE_TQQC_SHEET"
        };
    }
    var init = function (params) {  
                        debugger;
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
        $("#EX_TYPE" + pathValue).val(paramsValue["EX_TYPE"]);
        if (paramsValue["EX_TYPE"] == "三代ONT基因组建库") {
            $("#EX_LB" + pathValue).val("切胶");
        }
        $("#EX_MX_NUMBER" + pathValue).val(paramsValue["IDS"].length);
        var libtyps = paramsValue["LIBTYPES"];
        var strlibthype = "";
        for (var i = 0; i < libtyps.length; i++) {
            if (i == 0) {
                strlibthype = libtyps[i];
            } else {
                strlibthype += "," + libtyps[i];
            }
        }
        $("#EX_LIB_TYPE" + pathValue).val(strlibthype);

        //文库执行单号编号字符
        // 二代RNA建库执行单号	JR+日期+两位数流水号
        // 二代DNA常规建库执行单号	JD+日期+两位数流水号
        // 代谢建库执行单号	JX+日期+两位数流水号
        // ONT基因组建库执行单号	JN+日期+两位数流水号
        var typelb = $("#EX_TYPE" + pathValue).val();
        var numstr = "";
        if (typelb == "蛋白前处理") numstr = "DQ";
        if (typelb == "二代常规DNA建库") numstr = "JD";
        if (typelb == "二代常规RNA建库") numstr = "JR";
        if (typelb == "三代ONT基因组建库") numstr = "JN";
        if (typelb == "代谢建库") numstr = "JX";
        if (typelb == "HIC建库") numstr = "JH";
        if (typelb == "ATAC建库") numstr = "JA";
        if (typelb == "单细胞建库") numstr = "DJ";
        $("#NO_CHAR" + pathValue).val(numstr);

    }


    var subUpData = function () {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        /**
         * 平台很重要,后台上机排单需要分流
         */
        var SEQ_PLAT = "";
        if (paramsValue["EX_TYPE"] == "二代常规DNA建库" ||
            paramsValue["EX_TYPE"] == "二代常规RNA建库" ||
            paramsValue["EX_TYPE"] == "HIC建库" ||
            paramsValue["EX_TYPE"] == "ATAC建库") SEQ_PLAT = "NGS";
        if (paramsValue["EX_TYPE"] == "三代PB基因组建库") SEQ_PLAT = "PB";
        if (paramsValue["EX_TYPE"] == "三代ONT基因组建库") SEQ_PLAT = "ONT";
        if (paramsValue["EX_TYPE"] == "代谢建库") SEQ_PLAT = "代谢";
        if (paramsValue["EX_TYPE"] == "蛋白前处理") SEQ_PLAT = "蛋白";
        //插入执行主单
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    alertMsg("提交成功,生成的草稿,请前往审核提交!", "success", function () {
                        var ids = paramsValue["IDS"];
                        var taskids = paramsValue["TASKIDS"];
                        var objectadd = [];
                        var objectup = [];
                        var objectupqc = [];
                        var biocodes = paramsValue["BIOCODES"];
                        var samplecodes = paramsValue["SAMPLECODES"];
                        var libtypemxs = paramsValue["LIBTYPEMXS"];
                        var REBUILD_LIB_SCHEME = paramsValue["REBUILD_LIB_SCHEME"];
                        var LIBQCIDS = paramsValue["LIBQCIDS"];
                        var upLIBQC = [];
                        var uplibmx = [];

                        var YC = paramsValue["YC"];
                        var INDEX_NAME = paramsValue["INDEX_NAME"];
                        var SEQ_I7_1 = paramsValue["SEQ_I7_1"];
                        var SEQ_I7_2 = paramsValue["SEQ_I7_2"];
                        var SEQ_I7_3 = paramsValue["YC"];
                        var SEQ_I7_4 = paramsValue["SEQ_I7_4"];
                        var SEQ_I5_NAVA = paramsValue["SEQ_I5_NAVA"];
                        var SEQ_I5_XTEN = paramsValue["SEQ_I5_XTEN"];
                        var GEN_N = paramsValue["GEN_N"];
                        var ER_SEQ = paramsValue["ER_SEQ"];
                        var LIB_KIT = paramsValue["LIB_KIT"];
                        if (libtypemxs[0] == "ATAC-seq" || libtypemxs[0] == "Hi-c") {

                            for (var i = 0; i < ids.length; i++) {
                                if (YC == "是") {
                                    objectadd.push({
                                        //										"FROM_TQ_EXE_ID": TQEXEIDS[i],//关联提取执行单ID
                                        "TASK_LIB_MX_ID": ids[i],//联联任务ID
                                        "TQQC_ID": taskids[i],//提取检测ID
                                        "EXE_TQQC_ID": result["ID"],//关联执行单
                                        "SEQ_PLAT": SEQ_PLAT,//测序平台
                                        "JK_TASKMX_STDATE": time,
                                        "BIO_CODE": biocodes[i],//枋酸编号
                                        "LIBRARY_TYPE": libtypemxs[i],//文库类型
                                        "LIBRARY_TYPE_E": libtypemxs[i],//文库类型
                                        "SAMPLE_CODE": samplecodes[i],//样品编号
                                        "REBUILD_LIB_SCHEME": REBUILD_LIB_SCHEME[i],//重建库方案
                                        "SYS_MAN": username,//实验员
                                        "SYS_INSERTTIME": time//开始日期
                                    });
                                }
                                else {
                                    objectadd.push({
                                        "TASK_LIB_MX_ID": ids[i],//联联任务ID
                                        "BIO_CODE": biocodes[i],//枋酸编号
                                        "LIBRARY_TYPE": libtypemxs[i],//文库类型
                                        "LIBRARY_TYPE_EN": libtypemxs[i],//文库类型
                                        "SAMPLE_CODE": samplecodes[i],//样品编号
                                        "EXE_TQQC_ID": result["ID"],//关联执行单
                                        "SEQ_PLAT": SEQ_PLAT,//测序平台
                                        "JK_TASKMX_MAN": $("#EX_MAN" + pathValue).val(),
                                        "JK_TASKMX_STDATE": time,
                                        "SYS_MAN": username,//实验员
                                        "SYS_INSERTTIME": time//开始日期
                                    });
                                }
                                if (LIBQCIDS.length > 0) {
                                    upLIBQC.push({
                                        "ID": LIBQCIDS[i],
                                        "IS_REGO": "是"
                                    });
                                }
                                //	for (var i = 0; i < taskids.length; i++) {
                                //		objectup.push({
                                //			"ID": taskids[i],
                                //			"EX_RE_STATUS2": "建库中"
                                //		});
                                //	}

                            }
                            for (var i = 0; i < taskids.length; i++) {
                                objectupqc.push({
                                    "ID": taskids[i],
                                    "LIB_STATES": "建库中"
                                });
                            }
                            //执行添加到文库
                            var urlsend = "system/jdbc/save/batch/table";
                            var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectadd };
                            putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

                            var paramsqc = { "tableName": "BIO_DNA_RNA_QC", "objects": objectupqc };
                            putAddOrUpdata(urlsend, paramsqc, "否", "推入下一步实验任务");


                            //								var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
                            //								putAddOrUpdata(urlsend,paramsup,"是","建库中");

                            if (upLIBQC.length > 0) {
                                var paramsupqc = { "tableName": "BIO_LIB_QC_INFO", "objects": upLIBQC };
                                putAddOrUpdata(urlsend, paramsupqc, "否", "");
                            }

                        } else {

                            for (var i = 0; i < ids.length; i++) {
                                if (YC == "是") {
                                    objectadd.push({
                                        "TASK_LIB_MX_ID": ids[i],//联联任务ID
                                        "BIO_CODE": biocodes[i],//枋酸编号
                                        "LIBRARY_TYPE": libtypemxs[i],//文库类型
                                        "LIBRARY_TYPE_EN": libtypemxs[i],//文库类型
                                        "SAMPLE_CODE": samplecodes[i],//样品编号
                                        "EXE_TQQC_ID": result["ID"],//关联执行单
                                        "SEQ_PLAT": SEQ_PLAT,//测序平台
                                        "JK_TASKMX_MAN": $("#EX_MAN" + pathValue).val(),
                                        "JK_TASKMX_STDATE": time,
                                        "SYS_MAN": username,//实验员
                                        "SYS_INSERTTIME": time,//开始日期
                                        "REBUILD_LIB_SCHEME": REBUILD_LIB_SCHEME[i],//重建库方案
                                        "INDEX_NAME": INDEX_NAME[i],
                                        "INDEX_NAME": INDEX_NAME[i],
                                        "SEQ_I7_1": SEQ_I7_1[i],
                                        "SEQ_I7_2": SEQ_I7_2[i],
                                        "SEQ_I7_3": SEQ_I7_3[i],
                                        "SEQ_I7_4": SEQ_I7_4[i],
                                        "SEQ_I5_NAVA": SEQ_I5_NAVA[i],
                                        "SEQ_I5_XTEN": SEQ_I5_XTEN[i],
                                        "GEN_N": GEN_N[i],
                                        "ER_SEQ": ER_SEQ[i],
                                        "LIB_KIT": LIB_KIT[i],
                                    });
                                }
                                else {
                                    objectadd.push({
                                        "TASK_LIB_MX_ID": ids[i],//联联任务ID
                                        "BIO_CODE": biocodes[i],//枋酸编号
                                        "LIBRARY_TYPE": libtypemxs[i],//文库类型
                                        "LIBRARY_TYPE_EN": libtypemxs[i],//文库类型
                                        "SAMPLE_CODE": samplecodes[i],//样品编号
                                        "EXE_TQQC_ID": result["ID"],//关联执行单
                                        "SEQ_PLAT": SEQ_PLAT,//测序平台
                                        "JK_TASKMX_MAN": $("#EX_MAN" + pathValue).val(),
                                        "JK_TASKMX_STDATE": time,
                                        "SYS_MAN": username,//实验员
                                        "SYS_INSERTTIME": time//开始日期
                                    });
                                }
                                if (LIBQCIDS.length > 0) {
                                    upLIBQC.push({
                                        "ID": LIBQCIDS[i],
                                        "IS_REGO": "是"
                                    });
                                }
                                uplibmx.push({
                                    "ID": ids[i],
                                    "TASK_LSMX_STATUS": "建库中"
                                });
                            }
                            for (var i = 0; i < taskids.length; i++) {
                                objectup.push({
                                    "ID": taskids[i],
                                    "TASK_LS_STATUS": "建库中"
                                });
                            }
                            //执行添加到文库
                            var urlsend = "system/jdbc/save/batch/table";
                            var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectadd };
                            putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

                            var paramsup = { "tableName": "BIO_TASK_LIB", "objects": objectup };
                            putAddOrUpdata(urlsend, paramsup, "是", "建库中");

                            var paramsupmx = { "tableName": "BIO_TASK_LIBMX", "objects": uplibmx };
                            putAddOrUpdata(urlsend, paramsupmx, "否", "建库中");

                            if (upLIBQC.length > 0) {
                                var paramsupqc = { "tableName": "BIO_LIB_QC_INFO", "objects": upLIBQC };
                                putAddOrUpdata(urlsend, paramsupqc, "否", "");
                            }

                        }


                    });
                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });

    }
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var submit = function () {
        subUpData();
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
        "subUpData": subUpData
    });

});