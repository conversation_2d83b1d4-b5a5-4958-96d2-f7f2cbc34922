$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-libzjwl-pdup-pdup";    
    var paramsValue;
        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"EXE_TQQC_SHEET"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
    	
    	paramsValue=params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
        
        $("#EX_TYPE"+pathValue).val(params["EX_TYPE"]);
    }
 
 
  var subUpData=function(){
    	
         //插入执行主单
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                     //提交成功
                     alertMsg("提交成功","success",function(){
                        
                    	 
                    	 //处理明细更新result["ID"]
                    	 //执行主单ID=EXE_TQQC_ID
                    	 $("#EXE_TQQC_ID"+pathValue).val(result["ID"]);
                    	 
                    	//取出IDS
                     	var ids=paramsValue["IDS"];
                     	var jsonData = getJsonByForm("form",pathValue);//获取表单json
                     	
                    	 var object=[];
                         for(var i=0;i < ids.length;i++){
                        	 object.push($.extend({},jsonData,{"ID":ids[i]}));//表单值继承
                         }
                       //执行更新
                         var params={"tableName":"BIO_TASK_LIBMX","objects":object};
                         //插入任务明细记录
                         var url="system/jdbc/save/batch/table";
                         $.fn.ajaxPost({
                             ajaxType:"post",
                             ajaxUrl:url,
                             ajaxData:params,
                             succeed:function(result){
                                 
                                 if(result["code"]>0){//成功保存后执行流程提交
                                 	
                                 	funcExce(pathValue+"pageCallBack");//父执行回调
                                 
                                 	console.log(result);
                                 	
                                  alertMsg("提交成功!");
                                  
                                  funcExce(pathValue+"close");//关闭页面
                                 	
                                 }else{
                                 	console.log(result);
                                 }
                             }
                         });
                    	 
                    	 
                    	 
                     });
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });

    }
 
    var submit=function(){
    	subUpData();
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "subUpData":subUpData
    });
 
 });