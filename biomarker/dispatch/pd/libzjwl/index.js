$(document).ready(function() {
   var pathValue="biomarker-dispatch-pd-libzjwl-index";

   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var gridNameDGrid;
   var gridNameD1Grid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"edit",title:"指派实验员"}
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"query_BIO_TASK_LIBQC_PD_list","objects":[["已审核"],["文库库检"]]},
            headerFilter:function(cols,i){
                if(i){
                   // if(cols[i]["field"]&&cols[i]["field"]=="PROJECT_NO"){
                        //setJsonParam(cols[i],"template",getTemplate("#= PROJECT_NO #","funcExce(\'"+pathValue+"open\',\'#= ID #\');","txt"));
                   // }
                }
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        init1();
   }
   
   var init1=function(params){
       /**
        * 列表-按钮-定义
        */
       var toolbar=getButtonTemplates(pathValue,[

       ]);//工具条
       //请求参数
       var gridNameGridJson={
    		   
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "",//排序
           toolbar: toolbar,
           read:{"query":"lib_pd_SHEET_list","objects":[["文库库检"],["待接收","已接收"]]},
           headerFilter:function(cols,i){},
           
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',//展开子明细
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               // 加载表格
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
                   sort: "",//排序
                   toolbar: null,
                   height: 320,
                   read:{"query":"query_YPD_BIO_TASK_LIBQC_PD_list","objects":[ROW_ID]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }
           
           
       };
       gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
  }

    var add=function(){
        var winOpts={
            url:"biomarker/dispatch/pd/libzjwl/pdup/pdup",
                  
            title:"常规建库排单.."
        };
        openWindow(winOpts);
    }

    var open=function(IDS){
        var winOpts={
            url:"biomarker/dispatch/pd/libzjwl/pdup/pdup",
            title:"常规建库排单.."
        };
        openWindow(winOpts,{"IDS":IDS,"EX_TYPE":"文库库检"});//传递id
    }

    var edit=function(){
        var arrIds=getSelectData(gridNameDGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
		open(arrIds);
     }
     
     var sumbit=function(){
        formSubmit({
            formId:"form",
            pathValue:pathValue
        });
     }
     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameDGrid){
        	gridNameDGrid.dataSource.read();//重新读取--刷新
        }
        if(gridNameD1Grid){
        	gridNameD1Grid.dataSource.read();//重新读取--刷新
        }
     }

     var deleteInfo=function(){
        var arrIds=getSelectData(gridNameGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"BIO_TASK_LIBMX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
     }

    //表格导入
    var importData=function(componentId){
        var grid=gridNameGrid;
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
                "template":grid,//单表导入
                "tableName":"BIO_TASK_LIBMX",
            }
        });
    }

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "open":open,
         "add":add,//打开添加表单
         "edit":edit,
         "refreshGrid":refreshGrid,
         "deleteInfo":deleteInfo,
         "sumbit":sumbit,//提交方法
         "callBack":callBack,//回调方法
		 "importData":importData,
     });
});