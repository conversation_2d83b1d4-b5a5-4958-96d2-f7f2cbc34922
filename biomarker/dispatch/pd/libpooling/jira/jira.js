$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-libpooling-jira-jira";
    var paramsValue;
    var addOrUpdate="";
    var initData=function(){
        return {
            tableName:"JIRA_LSM_LIB"
        };
    }
    var init=function(params){
    	debugger;
    	paramsValue=params;
    	getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        
   }
 //暂保存
var submit=function(){
	//表单校验
    var formJson = { formId:"form", pathValue:pathValue };
    var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    if ( !validator.validate() ) {
           alertMsg("表单验证未通过","error");
           return false;
    }
    var m=mask(pathValue,"正在提交保存,请稍等...");
    var formparams = getJsonByForm("form", pathValue);
    var params = {"tableName":"JIRA_LSM_LIB","objects":[formparams]};
    $.fn.ajaxPost({
        ajaxUrl:"system/jdbc/save/batch/table",
        ajaxData: params,
           ajaxType:"post",
           succeed:function(result){
        	   unmask(m);
               if(result["code"]>0){
                 funcExce(pathValue+"pageCallBack");
                 //funcExce(pathValue+"close");
               }else{
                   alertMsg("提交失败","error");
             }
           },
           failed:function(res){
        	   unmask(m);
        	   alertMsg("提交失败","error");
           }
       });
}

//保存并推送
var submitSendJira=function(){
	//表单校验
    var formJson = { formId:"form", pathValue:pathValue };
    var validator = $("#"+formJson.formId+formJson.pathValue).kendoValidator(getValidateJson(formJson.validatorJson)).data("kendoValidator");
    if ( !validator.validate() ) {
           alertMsg("表单验证未通过","error");
           return false;
    }
    var m=mask(pathValue,"正在提交到jira,请稍等...");
    var formparams = getJsonByForm("form", pathValue);
    var params = {"tableName":"JIRA_LSM_LIB","objects":[formparams]};
    $.fn.ajaxPost({
        ajaxUrl:"system/jdbc/save/batch/table",
        ajaxData: params,
           ajaxType:"post",
           succeed:function(result){
        	   unmask(m);
               if(result["code"]>0){
                   //推送jira
                   //sendLSM();//LSM
            	  sendPM();//项目
               }else{
                   alertMsg("提示:提交保存失败","error");
             }
           },
           failed:function(res){
        	   alertMsg("提示:提交保存失败","error");
           }
       });
}
//正实验LSM
var sendLSM=function(){
debugger;
    var odlStatus="";
    var newStatus="等客户反馈";
    var p = getJsonByForm("form", pathValue);  
    var params = {
        "jiraKey":p["LSM_KEY"],
       // "oldStatusName":odlStatus,
       // "statusName":newStatus,
        "updateField":{
        	"customfield_12100":p["CUSTOMFIELD_12100"],	//建库测序任务单下达日期
        	"customfield_12103":p["CUSTOMFIELD_12103"],	//建库完成日期
            	"customfield_12101":p["CUSTOMFIELD_12101"],	//建库标准结单日期
        	"customfield_14201":p["CUSTOMFIELD_14201"],	//建库计划完成日期 
        }
    };
    
    var m=mask(pathValue,"正在推送到jira,请稍等...");
    var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
debugger;
        	 unmask(m);
             if(result["code"]>0){
            	 alertMsg("提示:LSM推送成功!");
              }else{
                 alertMsg("提示:操作失败!");
             }
         },
         failed:function(res){
        	 unmask(m);
      	   alertMsg("提示:提交保存失败","error");
         }
     });
     
}


//预实验LSM
var sendLSM_P=function(){
    var odlStatus="样品提取检测";
    var newStatus="等客户反馈";
    var p = getJsonByForm("form", pathValue);  
    var params = {
        "jiraKey":p["LSM_KEY"],
        "oldStatusName":odlStatus,
        "statusName":newStatus,
        "updateField":{
        	"customfield_11530":p["CUSTOMFIELD_15530"],	//预实验SLAF酶切方案下达日期
        	"customfield_11508":p["CUSTOMFIELD_15508"],	//建库计划完成日期  

        }
    };
    
    var m=mask(pathValue,"正在推送到jira,请稍等...");
    var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
        	 unmask(m);
             if(result["code"]>0){
            	 alertMsg("提示:LSM推送成功!");
              }else{
                 alertMsg("提示:操作失败!");
             }
         },
         failed:function(res){
        	 unmask(m);
      	   alertMsg("提示:提交保存失败","error");
         }
     });
     
}


//项目
var sendPM=function(){
debugger;
	var odlStatus="样品提取检测";
    var newStatus="等客户反馈";
    var p = getJsonByForm("form", pathValue);  
    var params = {
        "jiraKey":p["LSM_KEY_P"],
       // "oldStatusName":odlStatus,
        //"statusName":newStatus,
        "updateField":{
        	"customfield_12100":p["CUSTOMFIELD_12100"],	//建库测序任务单下达日期
        	//"customfield_12103":p["CUSTOMFIELD_12103"],	//建库完成日期
            	//"customfield_12101":p["CUSTOMFIELD_12101"],	//建库标准结单日期
        	"customfield_14201":p["CUSTOMFIELD_14201"],	//建库计划完成日期 
        	"customfield_10228":p["CUSTOMFIELD_10228"],	//实验计划描述
                "customfield_10227": p["CUSTOMFIELD_10227"],	//测序标准完成日期
                "customfield_14202": p["CUSTOMFIELD_14202"],	//测序计划完成日期
        }
    };   
    var m=mask(pathValue,"正在推送到jira,请稍等...");
    var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
 	$.fn.ajaxPost({
         ajaxType:"post",
         ajaxUrl:"system/api/post/bodyParams",
         ajaxData:inobjjson,
         succeed:function(result){
debugger;
        	 unmask(m);
             if(result["code"]>0){
            	 alertMsg("提示:LSM推送成功!");
              }else{
                 alertMsg(errMsg+"操作失败!");
             }
         },
         failed:function(res){
        	 unmask(m);
      	   alertMsg("提示:提交保存失败","error");
         }
     });  
}


    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "submitSendJira":submitSendJira
    });
 
 });