$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-libpooling-uptaskstatus-uptaskstatus";
    var paramsValue;
    var initData=function(){
        return {
            tableName:"EXE_TQQC_SHEET"
        };
    }
    var init=function(params){
        paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
    }

  var subUpData=function(){
	 var ids=paramsValue["IDS"];
    	 var LSMKEYPS=paramsValue["LSMKEYP"];
	 var endDate=sysNowTimeFuncParams["sysNowTime"]; 
	 var object=[];
	 var jsonData = getJsonByForm("form",pathValue);//获取表单json
	 for(var i=0;i < ids.length;i++){
     	 object.push($.extend({},jsonData,{"ID":ids[i]}));//表单值继承
      }
	var urlsend="system/jdbc/save/batch/table";
  	var paramsadd={"tableName":"EXE_TQQC_SHEET","objects":object};
  	putAddOrUpdata(urlsend,paramsadd,"是","更新"); 
 if(jsonData["TASK_LS_STATUS"]=="已完结"&&LSMKEYPS  != undefine &&LSMKEYPS  != ""&&LSMKEYPS  != null){
            if(paramsValue["YN"] == "Y"){
             doJira(ids,LSMKEYPS,endDate);
              } 
            }
 }
  
  //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提示:操作成功!");
             		 funcExce(pathValue+"pageCallBack");
                         funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 ////////////////////////////////////////////////////////////////////////////////////////////

  var doJira=function(IDS,keys2,yDate){
debugger;
      var params2=[];
      for(var i=0;i<IDS.length;i++){
    	  params2.push({
	          "jiraKey":keys2[i],
	          "oldStatusName":"建库",
	          "statusName":"测序",
	          "updateField":{ 
	              "customfield_12103":yDate
	           }
    	  });
       }
      for(var i=0;i<IDS.length;i++){
        putToJira(params2[i]);
     }
    }
    var putToJira=function(params){
        var inobjjson={ "url":"http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
       	$.fn.ajaxPost({                       
               ajaxType:"post",
               ajaxUrl:"system/api/post/bodyParams",
               ajaxData:inobjjson,
               succeed:function(result){
                   if(result["code"]>0){
                       if(result.apiData.flag=="true"||result.apiData.flag){
                       	   alertMsg(result.apiData.message);
                          }else{
                               alertMsg("提示:推送失败(<font color=#ff0000>"+result.apiData.message+"</font>)!");
                         }
                    }else{
                  	 alertMsg(errMsg+"操作失败!");
                   }
               }
           });
    }


//////////////////////////////////////////////////////////////////////////////////////////////
    var submit=function(){
       subUpData();
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });