$(document).ready(function () {
	var pathValue = "biomarker-dispatch-pd-libpooling-index";
	var initData = function () {
		return {};
	}
	var gridNameDGrid;

	var gridNameD8Grid;
	var gridNameD9Grid;

	var gridNameD1Grid;
	var gridNameD2Grid;
	var gridNameD3Grid;
	var gridNameD4Grid;
	var gridNameD5Grid;
	var gridNameD6Grid;
	var gridNameD7Grid;
	var gridNameD10Grid;
	var gridNameS = [];
	var gridNameS1 = [];
	var gridNameS2 = [];
	var gridNameS3 = [];
	var gridNameS4 = [];
	var gridNameS5 = [];
	var gridNameS6 = [];
	var gridNameS7 = [];
	var gridNameS8 = [];
	var gridNameS9 = [];
	var gridNameS10 = [];
	var gridNameS11 = [];
	//待排
	var init = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "edit", title: "生成执行单" },
			{ name: "edit", target: "doCopyLib", title: "复制记录" },
			{ name: "delete", target: "deleteCopyLib", title: "删除复制记录" },
			{ name: "edit", target: "addToEx", title: "追加任务到执行单" },
			{ name: "edit", target: "doTaskStatus", title: "任务单状态修改" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "lib_pd_SHEET_list",
				"objects": [[
					"PB混样-全长转录组",
					"PB混样-微生物全长",
					"PB混样-基因组",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA"
				], ["建库已审核", "建库中", "接收退回"]]
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var querysql = "queryTaskLibExMxResult";

				if (e.data.EX_TYPE == "PB混样-微生物全长") querysql = "lib_sample_pool_all_list_pbwshqc_sh";
				if (e.data.EX_TYPE == "PB混样-全长转录组" || e.data.EX_TYPE == "PB混样-基因组") querysql = "lib_sample_pool_all_list_pbqczlz_sh";
				if (e.data.EX_TYPE == "混样建库-ONT建库-DNA" || e.data.EX_TYPE == "混样建库-ONT建库-Iso-RNA") querysql = "doLIbNo1ExMxlist_sh_2";
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": querysql, "objects": [ROW_ID] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS.push(subGrid_N);
			}
		};
		gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
		init1();
		init2();
		init3();
		init4();
		init5();
		init6();
		init7();
		init8();
		init9();
		init10();
	}
	//待审核
	var init1 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "doOK", title: "提交" },
			{ name: "excel", target: "importData1", title: "实验导入/模板" },
			{ name: "edit", target: "doUpdate", title: "修改安排数据量" },
			{ name: "delete", target: "remove", title: "移除任务明细" },
			{ name: "delete", target: "deleteex", title: "删除执行单" },
			{ name: "edit", target: "updateman", title: "修改实验员" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "ex_sm_pool_lib_list",
				"objects": [[
					"PB混样-全长转录组",
					"PB混样-微生物全长",
					"PB混样-基因组",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA"
				], ["草稿", "接收退回"]]
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "lib_sample_pool_all_list_S", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS1.push(subGrid_N);
			}
		};
		gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);//初始化表格的方法
	}
	//已处理
	var init2 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "return", target: "doReturn", title: "撤回" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "ex_sm_pool_lib_list",
				"objects": [[
					"PB混样-全长转录组",
					"PB混样-微生物全长",
					"PB混样-基因组",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA"],
				["混库待处理", "混库待接收", "接收退回", "混库已接收", "混库建库中", "混库待审核", "混库已审核"]], "search": { "EX_EXECUTE_MODE": "实验员" }
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "lib_sample_pool_all_list_S", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS2.push(subGrid_N);
			}
		};
		gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);//初始化表格的方法
	}
	//项目维度
	var init8 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "edit2", title: "生成执行单" },
			{ name: "edit", target: "doCopyLib2", title: "复制记录" },
			{ name: "delete", target: "deleteCopyLib2", title: "删除复制记录" },
			{ name: "edit", target: "addToEx2", title: "追加任务到执行单" },
			{ name: "edit", target: "doTaskStatus2", title: "任务单状态修改" },
			{ name: "edit", target: "setjira", title: "jira推送.." },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "query_PJ_SUB_LIBPOOLING_list",
				"objects": [[
					"PB混样-全长转录组",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA"
				], ['待排']]
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var querysql = "";
				// if(e.data.EX_TYPE=="PB混样-微生物全长") querysql="lib_sample_pool_all_list_pbwshqc_sh_S";
				if (e.data.EX_TYPE == "PB混样-全长转录组" || e.data.EX_TYPE == "PB混样-基因组") querysql = "lib_sample_pool_all_list_pbqczlz_sh_S";
				if (e.data.EX_TYPE == "混样建库-ONT建库-DNA" || e.data.EX_TYPE == "混样建库-ONT建库-Iso-RNA") querysql = "doLIbNo1ExMxlist_sh_S";
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: {
						"query": querysql, "objects": [[ROW_ID], [
							"PB混样-全长转录组",
							"混样建库-ONT建库-DNA",
							"混样建库-ONT建库-Iso-RNA"
						], ["建库已审核", "建库中", "接收退回", "委外已处理", "预处理", "PE执行状态", "数据已处理"]]
					},
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS8.push(subGrid_N);
			}
		};
		gridNameD8Grid = initKendoGrid("#gridNameD8Grid" + pathValue, gridNameGridJson);
	}
	//MCD
	var init9 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "edit3", title: "生成执行单" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "query_MCD_SUB_LIBPOOLING_list",
				"objects": [["PB混样-微生物全长"], ["建库已审核"]]
			},
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var EXID = e.data.EXE_TQQC_ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "queryTaskLibExMxResult", "objects": [[EXID]], "search": { "POOL_ID1": [ROW_ID] } },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS10.push(subGrid_N);
			}
		};
		gridNameD9Grid = initKendoGrid("#gridNameD9Grid" + pathValue, gridNameGridJson);
		gridNameS9.push(gridNameD9Grid);
	}

	//完成
	var init3 = function (params) {

		var toolbar = getButtonTemplates(pathValue, [
			{ name: "return", target: "doReturn2", title: "移至待排单" },
		]);

		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "lib_pd_SHEET_list",
				"objects": [[
					"PB混样-全长转录组",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA",
					"PB混样-微生物全长"
				], ["已完结", "暂停", "建库终止", "未建库", "建库中"]]
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "queryTaskLibExMxResult", "objects": [ROW_ID] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS3.push(subGrid_N);
			}
		};
		gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);

	}

	//待排任务
	var init4 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "editA", title: "智能排单" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "query_BIO_TQ_TASK_MX_RNA_list_sh",
				"objects": [], "search": {
					"EX_TYPE": [
						"PB混样-全长转录组",
						"PB混样-微生物全长",
						"混样建库-ONT建库-DNA",
						"混样建库-ONT建库-Iso-RNA"
					], "EX_RE_STATUS": ["建库已审核", "建库中", "接收退回"]
				}
			},
			fetch: function (data) {
				gridNameS5 = data;
			}

		};
		gridNameD4Grid = initKendoGrid("#gridNameD4Grid" + pathValue, gridNameGridJson);
	}

	var add = function () {
		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/pdup/pdup",
			title: "混样建库排单.."
		};
		openWindow(winOpts);
	}
	//   预处理
	var init5 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "checkSample", title: "核验" },
			{ name: "edit", target: "submit", title: "提交" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "ex_sm_pool_lib_list",
				"objects": [[
					"PB混样-全长转录组",
					"PB混样-微生物全长",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA"],
				["预处理"]]
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "lib_sample_pool_all_list-Check", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS5.push(subGrid_N);
			}
		};
		gridNameD5Grid = initKendoGrid("#gridNameD5Grid" + pathValue, gridNameGridJson);//初始化表格的方法
	}

	// PE任务待处理
	var init6 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "pesubmit", title: "提交" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "ex_sm_pool_lib_list",
				"objects": [[
					"PB混样-全长转录组",
					"PB混样-微生物全长",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA"],
				["混库待处理", "混库待接收", "接收退回", "混库已接收", "混库建库中", "混库待审核", "混库已审核"]], "search": { "EX_EXECUTE_MODE": "PE" }
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "lib_sample_pool_all_list_S", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS6.push(subGrid_N);
			}
		};
		gridNameD6Grid = initKendoGrid("#gridNameD6Grid" + pathValue, gridNameGridJson);//初始化表格的方法
	}
	// PE执行状态
	var init7 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "pesubmit1", title: "提交" },
			{ name: "edit", target: "collection", title: "采集" }
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: {
				"query": "ex_sm_pool_lib_list",
				"objects": [[
					"PB混样-全长转录组",
					"PB混样-微生物全长",
					"混样建库-ONT建库-DNA",
					"混样建库-ONT建库-Iso-RNA"],
				["PE执行状态"]], "search": { "EX_EXECUTE_MODE": "PE" }
			},
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "lib_sample_pool_all_list_S", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
				gridNameS7.push(subGrid_N);
			}
		};
		gridNameD7Grid = initKendoGrid("#gridNameD7Grid" + pathValue, gridNameGridJson);//初始化表格的方法
	}


	var init10 = function (params) {
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "edit4", title: "生成执行单" }
		]);
		//请求参数
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "lib_sample_pool_all_list_pbqczlz_sh_LIB", "objects": [], "search": { "MARK": ["无意义"] } },

		};
		gridNameD10Grid = initKendoGrid("#gridNameD10Grid" + pathValue, gridNameGridJson);
	}

	
	//排单生成
	var edit4 = function () {
		edit_([gridNameD10Grid]);
	}
	var edit3 = function () {
		edit_(gridNameS10);
	}
	var edit2 = function () {
		edit_(gridNameS8);
	}
	var edit = function () {
		edit_(gridNameS);
	}
	var edit_ = function (myGridName) {
		var arrIds = [];
		for (var i = 0; i < myGridName.length; i++) {
			var arrSubID = getGridSelectData(myGridName[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		//判断类型是否全部分同一为,并取出形成类型单

		var ids = [];
		var exeids = [];
		var taskmxids = [];
		var pooltype = "";

		for (var i = 0; i < arrIds.length; i++) {
			if (arrIds[i]["ISPOOL_TWO"] == "是") {
				alertMsg("提示:所选记录存在已排单!");
				return;
			}

			ids.push(arrIds[i]["ID"]);
			pooltype = arrIds[i]["EX_TYPE"];
			taskmxids.push(arrIds[i]["TASKMXLIBID"]);
			if (exeids.indexOf(arrIds[i]["EXE_TQQC_ID"]) < 0) {
				exeids.push(arrIds[i]["EXE_TQQC_ID"]);//主单ID
			}
		}
		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/pddu/pddu",
			title: "子文库混样建库排单.."
		};
		debugger;
		openWindow(winOpts, { "IDS": ids, "EXEIDS": exeids, "TASKMXIDS": taskmxids, "POOL_TYPE": pooltype });
	}


	//修改安排数据量
	var doUpdate = function () {

		var arrIds = [];
		for (var i = 0; i < gridNameS1.length; i++) {
			var arrSubID = getSelectData(gridNameS1[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length != 1) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/doUpdate/doUpdate",
			title: "修改安排数据量.."
		};
		openWindow(winOpts, { "IDS": arrIds });
		// openWindow(winOpts,{"IDS":ids,"ID":ids[0]});
	}



	//审核提交
	var doOK = function () {
		var arrIds = getSelectData(gridNameD1Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}

		var sample;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "lib_sample_pool_all_list_S", "objects": [arrIds] },
			succeed: function (rs) {
				sample = rs.rows;
			}
		});
		for (var i = 0; i < sample.length; i++) {
			if (sample[i]["LIBRARY_JHDATA"] == null || sample[i]["LIBRARY_JHDATA"] == "") {
				alertMsg("子文库编号【" + sample[i]["LIBRARY_CODE"] + "】安排数据量为空!");
				return;
			}
		}
		var objectup = [];
		for (var i = 0; i < arrIds.length; i++) {
			var time = sysNowTimeFuncParams["sysNowTime"];
			var username = getLimsUser()["name"];
			objectup.push({
				"ID": arrIds[i],//联联任务ID
				"POOL_STATUS": "混库待接收"
			});
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "BIO_LIB_POOLING", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
	}
	//撤回
	var doReturn = function () {
		var g = getGridSelectData(gridNameD2Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		for (var i = 0; i < g.length; i++) {
			if (g[i]["POOL_STATUS"] != "混库待接收") {
				alertMsg("操作失败,所选记录存在已“已接收”状态!");
				return;
			} else {
				objectup.push({
					"ID": g[i]["ID"],
					"POOL_STATUS": "草稿"
				});
			}
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "BIO_LIB_POOLING", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
	}

	//样品核验 
	var checkSample = function () {
		var arrIds = getGridSelectData(gridNameD5Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行核验");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行核验操作!");
			return;
		}

		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/check/check",
			title: "自动排单明细.."
		};
		openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递
	}


	//预处理提交
	var submit = function () {
		var arrIds = getGridSelectData(gridNameD5Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行修改!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行修改操作!");
			return;
		}

		var sample;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: { "query": "lib_sample_pool_all_list-Check", "objects": [[arrIds[0]["ID"]]] },
			succeed: function (rs) {
				sample = rs.rows;             //样品
				var num = 0;
				for (var j = 0; j < sample.length; j++) {
					if (sample[j]["JK_CHECK"] == "OK") {
						num = num + 1;

					}
				}
				if (num < sample.length) {
					alertMsg("还有" + (sample.length - num) + "条未核验，不能提交");
					return;
				}
				var objectSheet = [];
				objectSheet.push({
					"ID": arrIds[0]["ID"],//id
					"POOL_STATUS": "草稿"       //状态

				});
				var urlsend = "system/jdbc/save/batch/table";
				var paramsadd1 = { "tableName": "BIO_LIB_POOLING", "objects": objectSheet };
				putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
			}
		});

	}
	//移至待审核
	var doReturn2 = function () {
		debugger;
		var g = getGridSelectData(gridNameD3Grid);
		if (g.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var objectup = [];
		var username = getLimsUser()["name"];
		for (var i = 0; i < g.length; i++) {
			//  	if(g[i]["EX_RE_STATUS"]=="暂停"||g[i]["EX_RE_STATUS"]=="未建库"){  
			if (username == "赵庆") {
				objectup.push({
					"ID": g[i]["ID"],
					"EX_RE_STATUS": "建库已审核"
				});
			} else {
				alertMsg("无此权限！！！");
				return;
			}
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
		putAddOrUpdata(urlsend, paramsup, "是", "提交");
	}
	//追加任务
	var addToEx2 = function () {
		addToEx_(gridNameS8);
	}
	var addToEx = function () {
		addToEx_(gridNameS);
	}
	var addToEx_ = function (myGridName) {
		debugger;
		var arrIds = [];
		for (var i = 0; i < myGridName.length; i++) {
			var arrSubID = getGridSelectData(myGridName[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}

		var ids = [];
		var exeids = [];
		var taskmxids = [];

		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			taskmxids.push(arrIds[i]["TASKMXLIBID"]);
			if (exeids.indexOf(arrIds[i]["EXE_TQQC_ID"]) < 0) {
				exeids.push(arrIds[i]["EXE_TQQC_ID"]);//主单ID
			}
			if (arrIds[i]["ISPOOL_TWO"] == "是") {
				alertMsg("提示:所选记录存在已排单!");
				return;
			}

		}
		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/addtoex/addtoex",
			title: "子文库混库追加.."
		};
		openWindow(winOpts, { "IDS": ids, "EXEIDS": exeids, "TASKMXIDS": taskmxids });
	}
	//任务单状态修改
	var doTaskStatus2 = function () {
		doTaskStatus_(gridNameD8Grid, "Y");
	}
	var doTaskStatus = function () {
		doTaskStatus_(gridNameDGrid, "N");
	}
	var doTaskStatus_ = function (myGridName, yn) {
		debugger;
		var arrIds = getGridSelectData(myGridName);
		debugger;
		var ids = [];
		var lsmkeyps = [];
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			lsmkeyps.push(arrIds[i]["LSM_KEY_P"]);
		}

		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/uptaskstatus/uptaskstatus",
			title: "修改任务单状态.."
		};
		openWindow(winOpts, { "IDS": ids, "LSMKEYP": lsmkeyps, "YN": yn });
	}
	//复制记录
	var doCopyLib2 = function () {
		doCopyLib_(gridNameS8);
	}
	var doCopyLib = function () {
		doCopyLib_(gridNameS);
	}
	var doCopyLib_ = function (myGridName) {
		var arrIds = [];
		for (var i = 0; i < myGridName.length; i++) {
			var arrSubID = getSelectData(myGridName[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		doRequset(arrIds);
	}

	//PE任务待处理提交
	var pesubmit = function () {
		debugger;
		var arrIds = getGridSelectData(gridNameD6Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行提交!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行提交!");
			return;
		}
		if (arrIds[0]["PLATE_CODE"] == null) {

			alertMsg("执行单" + arrIds[0]["EX_DH_NO"] + "尚未分配板孔号，不能提交");
			return;
		}
		var PEtoken;
		var PEVariable = { "ClientName": PE_ClientName, ClientPwd: ClientPwd };
		var inobjjson = { "url": PE_URL + "api/clientInfo/login", "PEVariable": PEVariable }
		// var inobjjson={"url":PE_URL + "api/clientInfo/login","PEVariable":PEVariable}
		// $.fn.ajaxPost({
		//     ajaxUrl: "/berry/automation/rowsingle/rowsingle",
		//     ajaxType: "post",
		//     ajaxAsync: false,
		//     ajaxData:inobjjson,
		//     succeed: function (rs) {
		//         PEtoken = rs.apiData.result.token;     
		//     }
		// });
		var sample;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "ex_sm_pool_lib_list", "objects": [[arrIds[0]["ID"]]] },
			succeed: function (rs) {
				sample = rs.rows;             //样品
			}
		});
		var PEsamples = [];
		// for (var j = 0; j < sample.length; j++) {
		//     PEsamples.push({
		//          "Well":sample[j]["PLATE_WELL"],
		//          "SampleNo":sample[j]["SAMPLE_CODE"],
		//          "LibraryType":sample[j]["LIBRARY_TYPE_EN"]});
		// }

		time = sysNowTimeFuncParams["sysNowTime"];
		// var PEVariable = {
		//     "TimeStamp":time,
		//     "Token":PEtoken,
		//     "ClientId":"",
		//     "Cmd":"RNASeq",
		//     "RQData":{
		//         "TaskNo":arrIds[0]["EX_DH_NO"],
		//         "BarCode":arrIds[0]["PLATE_CODE"],
		//         "Samples":PEsamples,
		//         "IndexBarCode":arrIds[0]["PLATE_CODE"] 
		//     }
		// };
		var inobjjson = { "url": PE_URL + "api/order/create", "PEVariable": PEVariable }
		var RValue;
		// $.fn.ajaxPost({
		//     ajaxUrl: "/berry/automation/rowsingle/rowsingle",
		//     ajaxType: "post",
		//     ajaxAsync: false,
		//     ajaxData:inobjjson,
		//     succeed: function (rs) {
		//         RValue = rs;             
		//     }
		// });
		// if(!RValue.apiData.success){
		//     alertMsg(RValue.apiData.msg);
		//     return;
		// }
		var objectSheet = [];
		objectSheet.push({
			"ID": arrIds[0]["ID"],//id
			"POOL_STATUS": "PE执行状态"       //状态
		});
		var urlsend = "system/jdbc/save/batch/table";
		var paramsadd1 = { "tableName": "BIO_LIB_POOLING", "objects": objectSheet };
		putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
	}
	//自动化排单
	var editA = function () {

		var gridData = getGridSelectData(gridNameD4Grid);
		debugger;
		if (gridData.length == 0) {
			alertMsg("至少选择一个样本");
			return;

		}

		if (gridData.length > 999) {
			alertMsg("单次只能排小于1000个样本！");
			return;
		}


		var params = { "ids": [], "keys": [] };

		for (var j = 0; j < gridData.length; j++) {
			var id = gridData[j]["ID"];
			var lfo2 = gridData[j]["LIBRARY_FLOW"]; //工序
			var wor2 = gridData[j]["MEHOD_JKPLAT"]; //样品执行组
			var met2 = gridData[j]["LIBRARY_METHOD"]; //样品执行方法
			params.ids.push(id);
			if (params.keys.indexOf(lfo2 + "-" + wor2 + "-" + met2) < 0) {
				params.keys.push(lfo2 + "-" + wor2 + "-" + met2);
				params[lfo2 + "-" + wor2 + "-" + met2] = {
					lfo: lfo2,
					met: met2,
					wor: wor2,
					ids: []
				};

			}
			params[lfo2 + "-" + wor2 + "-" + met2].ids.push(id);

		}

		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/automate/automate",
			title: "自动排单明细.."
		};
		openWindow(winOpts, params);//传递

	}
	//任务单状态修改
	var doTaskStatus = function () {
		var arrIds = getSelectData(gridNameDGrid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/uptaskstatus/uptaskstatus",
			title: "修改任务单状态.."
		};
		openWindow(winOpts, { "IDS": arrIds });
	}

	var doRequset = function (libids) {
		var time = sysNowTimeFuncParams["sysNowTime"];
		var username = getLimsUser()["name"];
		var params = { "query": "doCopyLIbList", "objects": [libids] };
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var rows = result["rows"];
					var newrecode = [];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						newrecode.push($.extend({}, row, {
							"ID": getRandomId(),
							"ISPOOL_TWO": "否",
							"IS_CLONE": "是",
							"SYS_INSERTTIME": time,
							"POOL_ID": null,
							"JK_QJ_STDATE": null,
							"JK_QJ_ENDATE": null
						}));
					}
					var newUrl = "system/jdbc/save/one/table/objects";
					var paramsnainadd = { "tableName": "BIO_LIB_INFO", "objects": newrecode };
					putAddOrUpdata(newUrl, paramsnainadd, "是", "生成");
				}
			}
		});
	}
	//删除执行单
	var deleteex = function () {
		var arrIds = getSelectData(gridNameD1Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行操作!");
			return;
		}
		confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
			var urlup = "system/jdbc/update/one/table/where";
			var parmsup = { "tableName": "BIO_LIB_INFO", "POOL_ID": null, "ISPOOL_TWO": "否", "where": { "POOL_ID": arrIds } }
			putAddOrUpdata(urlup, parmsup, "否", "");

			var params = { "tableName": "BIO_LIB_POOLING", "ids": arrIds };
			var url = "system/jdbc/delete/batch/table";
			deleteGridDataByIds(url, params, refreshGrid);
		});
	}
	//修改实验员 
	var updateman = function () {
		var arrIds = getGridSelectData(gridNameD1Grid);
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条数据进行修改!");
			return;
		} else if (arrIds.length != 1) {
			alertMsg("请只选择一条数据进行修改操作!");
			return;
		}
		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/updateman/updateman",
			title: "自动排单明细.."
		};
		openWindow(winOpts, { "ID": arrIds[0]["ID"], "EX_MAN": arrIds[0]["POOL_MAN"] });//传递
	}
	//记录移除
	var remove = function () {
		var arrg = [];
		var arrIds = [];
		var taskmxids = [];
		for (var i = 0; i < gridNameS1.length; i++) {
			var arrSubID = getGridSelectData(gridNameS1[i]);
			arrg = arrg.concat(arrSubID);

		}
		if (arrg.length == 0) {
			alertMsg("请至少选择一条数据进行操作!");
			return;
		}

		for (var i = 0; i < arrg.length; i++) {
			arrIds.push(arrg[i]["ID"]);
			taskmxids.push(arrg[i]["TASKMXID"]);
		}
		confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
			var objectuplib = [];
			var objectupmx = [];
			for (var i = 0; i < arrIds.length; i++) {
				objectuplib.push({
					"ID": arrIds[i],
					"POOL_ID": null,
					"ISPOOL_TWO": "否"
				});
				objectupmx.push({
					"ID": taskmxids[i],
					"TASK_LSMX_STATUS": "建库完成"
				});
			}
			var urlsend = "system/jdbc/save/batch/table";

			var paramsmx = { "tableName": "BIO_TASK_LIBMX", "objects": objectupmx };
			putAddOrUpdata(urlsend, paramsmx, "否", "更新");

			var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectuplib };
			putAddOrUpdata(urlsend, paramsadd, "是", "更新");
		});
	}


	var deleteCopyLib2 = function () {
		deleteCopyLib_(gridNameS8);
	}
	var deleteCopyLib = function () {
		deleteCopyLib_(gridNameS);
	}
	var deleteCopyLib_ = function (myGridName) {
		var arrg = [];
		for (var i = 0; i < myGridName.length; i++) {
			var arrSubID = getGridSelectData(myGridName[i]);
			arrg = arrg.concat(arrSubID);

		}
		var arrIds = [];
		for (var i = 0; i < arrg.length; i++) {
			if (arrIds[i]["ISPOOL_TWO"] == "是") {
				alertMsg("提示:所选记录存在已排单!");
				return;
			}
			if (arrIds[i]["IS_CLONE"] != "是") {
				alertMsg("提示:所选记录非复制记录!");
				return;
			}
			arrIds.push(arrg[i]["ID"]);
		}
		confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
			var params = { "tableName": "BIO_LIB_INFO", "ids": arrIds };
			var url = "system/jdbc/delete/batch/table";
			deleteGridDataByIds(url, params, refreshGrid);
		});
	}
	//表格导入
	var importData1 = function (componentId) {
		var arrIds = [];
		for (var i = 0; i < gridNameS1.length; i++) {
			var arrSubID = getGridSelectData(gridNameS1[i]);
			if (arrSubID.length != 0) {
				arrIds = arrIds.concat(arrSubID);
			}
		}
		if (arrIds.length == 0) {
			alertMsg("请至少选择一条样本记录进行操作!");
			return;
		}
		var ids = [];
		var poolids = [];
		for (var i = 0; i < arrIds.length; i++) {
			ids.push(arrIds[i]["ID"]);
			if (poolids.indexOf(arrIds[i]["POOL_ID"]) < 0) {
				poolids.push(arrIds[i]["POOL_ID"]);
			}
		}

		openComponent({
			name: "导入数据",//组件名称
			componentId: componentId,
			params: {
				template: function (p, n) {
					return exportAndImportData({
						expKey: "B",
						tableName: "BIO_LIB_INFO",
						requestData: {
							ajaxData: { "query": "lib_sample_pool_all_list", "size": 5000, "objects": [poolids, poolids], "search": { "ID": ids } },
						},
						params: p,
						name: n,
					});
				}
			},
			callBack: refreshGrid
		});
	}

	//批量执行插入
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						alertMsg("提示:操作成功!");
						refreshGrid();
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}

	function getRandomId() {
		return (('FDSX' || '-COPY-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
	};
	var callBack = function () {
		refreshGrid();
	};

	var refreshGrid = function () {
		gridNameS = [];
		gridNameS1 = [];
		gridNameS2 = [];
		gridNameS3 = [];
		gridNameS4 = [];
		gridNameS5 = [];
		gridNameS6 = [];
		gridNameS7 = [];
		gridNameS8 = [];
		gridNameS9 = [];
		if (gridNameDGrid) {
			gridNameDGrid.dataSource.read();
		}
		if (gridNameD1Grid) {
			gridNameD1Grid.dataSource.read();
		}
		if (gridNameD2Grid) {
			gridNameD2Grid.dataSource.read();
		}
		if (gridNameD3Grid) {
			gridNameD3Grid.dataSource.read();
		}
		if (gridNameD4Grid) {
			gridNameD4Grid.dataSource.read();
		}

		if (gridNameD5Grid) {
			gridNameD5Grid.dataSource.read();
		}
		if (gridNameD6Grid) {
			gridNameD6Grid.dataSource.read();
		}
		if (gridNameD7Grid) {
			gridNameD7Grid.dataSource.read();
		}
		if (gridNameD8Grid) {
			gridNameD8Grid.dataSource.read();
		}
		if (gridNameD9Grid) {
			gridNameD9Grid.dataSource.read();
		}
		if (gridNameD10Grid) {
			gridNameD10Grid.dataSource.read();
		}

	}

	var setjira = function () {
		debugger;
		var arrIds = getGridSelectData(gridNameD8Grid);
		if (arrIds.length != 1) {
			alertMsg("请选择一条数据进行操作!");
			return;
		}
		//新增jira字段
		var rows1;
		var url = "query_BIO_TASK_LIBMX_list";
		if (arrIds[0]["TASK_LS_TYPE"] == "代谢建库") url = "query_BIO_TASK_LIBMX_list-dx";
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": url, "objects": [], "search": { "TASK_LS_ID": [arrIds[0]["ID"]] } },
			succeed: function (rs) {
				//console.log(rs);				
				rows1 = rs["rows"];
			}
		});

		var THE_DATA_SUM = 0;
		var type;
		for (var i = 0; i < rows1.length; i++) {
			THE_DATA_SUM += rows1[i]["DATA_SUM"];
			type = rows1[i]["LIBRARY_TYPE_EN"];
		}
		var dwtype = rows1[0]["DATA_UNIT"];
		var cyc_dws = rows1[0]["CYC_DW"];
		var business_unit = rows1[0]["BUSINESS_UNIT"];

		doCyc(type, rows1.length, THE_DATA_SUM, "测序标准用时", dwtype, cyc_dws, business_unit);

		//doCyc(type, rows1.length, THE_DATA_SUM, "实验交付标准用时", dwtype, cyc_dws, business_unit);
		var p = arrIds[0];
		var time = Date.parse(sysNowTimeFuncParams["sysNowTime"]);
		var customield_10227 = time + (cxdates * 86400000);
		//var customield_10226 = time + (sydates * 86400000);


		var winOpts = {
			url: "biomarker/dispatch/pd/libpooling/jira/jira",
			title: "jira信息填写..",
			width: 1280,
			height: 480,
			position: { "top": 100, "left": 30 }
		};

		var p = arrIds[0];
		openWindow(winOpts, {
			"ID": p["LSMID"],
			"MAIN_ID": p["ID"],
			"LSM_KEY": p["LSM_KEY"],//LSM关键字
			"LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字
			"CUSTOMFIELD_12101": p["TASK_LS_LDATE"],//建库标准结单日期
			"CUSTOMFIELD_12100": p["TASK_LS_CDATE"],//建库测序任务单下达日期
			// "CUSTOMFIELD_12103":p[""],//建库完成日期
			"CUSTOMFIELD_14201": p["TASK_TEST_DELIVERDATE"],//建库计划完成日期
			// "CUSTOMFIELD_15508":p[""],//预实验建库完成日期
			"CUSTOMFIELD_15530": p["TASK_LS_CDATE"],//预实验SLAF酶切方案下达日期
			"CUSTOMFIELD_14202": customield_10227,//测序计划完成日期
			"CUSTOMFIELD_10227": customield_10227,//测序标准完成日期
		});

	}


	//获取周期定义,推算出截止结果日期
	var doCyc = function (type, countSm, smnumber, dep, sa, cyc_dws, business_unit) {
		debugger;
		//测序标准用时
		var cycdw = cyc_dws;
		var bus = business_unit;

		if (cycdw == "样品数") {
			flag = 0;
			params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm] };
		} else {
			flag = 1;
			if (sa == "CELL") {
				if (type == "Denovo-Pac(20-30K)") smnumber = smnumber * 140;
				if (type == "Denovo-Pac(hifi)") smnumber = smnumber * 24;
				if (type == "Iso-RNA(Pac)") smnumber = smnumber * 300;
				if (type == "DNA-8K(ONT)") smnumber = smnumber * 80;
				if (type == "Denovo-20k(ONT)") smnumber = smnumber * 80;
				if (type == "Iso-RNA(ONT)") smnumber = smnumber * 140;
			}
			params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber] };
		}

		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			ajaxAsync: false,
			succeed: function (result) {
				if (result["code"] > 0) {
					debugger;
					var rows = result["rows"];
					var m = getMyMonth();
					var dateNumber = 0;
					var seleDateFlag = "工作日";//日历取向
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						seleDateFlag = row["CYC_FLAG"];
						if (m == 1) dateNumber = row["MONTH_1"];
						if (m == 2) dateNumber = row["MONTH_2"];
						if (m == 3) dateNumber = row["MONTH_3"];
						if (m == 4) dateNumber = row["MONTH_4"];
						if (m == 5) dateNumber = row["MONTH_5"];
						if (m == 6) dateNumber = row["MONTH_6"];
						if (m == 7) dateNumber = row["MONTH_7"];
						if (m == 8) dateNumber = row["MONTH_8"];
						if (m == 9) dateNumber = row["MONTH_9"];
						if (m == 10) dateNumber = row["MONTH_10"];
						if (m == 11) dateNumber = row["MONTH_11"];
						if (m == 12) dateNumber = row["MONTH_12"];

						break;
					}
					//执行天数
					saveRemind = 1;
					if (dep == "测序标准用时") {
						cxdates = dateNumber;
					} else {
						sydates = dateNumber;
					}
					// $("#TASK_EXCDAYS" + pathValue).val(dateNumber);
					// $("#CYC_FLAG" + pathValue).val(seleDateFlag);
					// doGetEndDate(seleDateFlag, dateNumber);
				}
			}
		});

	}
	//推算截止日期
	var doGetEndDate = function (seleDateFlag, dateNumber) {

		var thedate = new Date();
		var params = "";
		if (seleDateFlag == "工作日") {
			params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
		} else {
			params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
		}

		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxData: params,
			succeed: function (result) {
				if (result["code"] > 0) {
					var rows = result["rows"];
					var noDoDateS = [];
					for (var i = 0; i < rows.length; i++) {
						var row = rows[i];
						noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
					}
					for (var i = 0; i < dateNumber; i++) {
						var base = 1000 * 60 * 60 * 24;
						//thedate=new Date(thedate.getTime() + base); 
						if (i == 0) {
							var TASK_LLS = paramsValue["TASK_LL"] * 1;
							thedate = new Date(TASK_LLS + (base));
						} else {
							//TASK_LLS=new Date(thedate.getTime() + base);
							thedate = new Date(thedate.getTime() + base);
						}
						for (var j = 0; j < noDoDateS.length; j++) {
							if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
								thedate = new Date(thedate.getTime() + base);//日期向前一天
							}
						}

					}
					//推算出的最终截止日期
					// $("#TASK_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
					// $("#TASK_JH_ENDDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));

				}
			}
		});

	}


	//当前月份
	var getMyMonth = function () {
		var date = new Date;
		var month = date.getMonth() + 1;
		return month;
	}



	funcPushs(pathValue, {
		"initData": initData,
		"init": init,
		"edit": edit,
		"doCopyLib": doCopyLib,
		"addToEx": addToEx,
		"doTaskStatus": doTaskStatus,
		"doOK": doOK,
		"editA": editA,
		"deleteex": deleteex,
		"checkSample": checkSample,
		"updateman": updateman,
		"remove": remove,
		"deleteCopyLib": deleteCopyLib,
		"submit": submit,
		"doReturn": doReturn,
		"doReturn2": doReturn2,
		"pesubmit": pesubmit,
		"refreshGrid": refreshGrid,
		"callBack": callBack,
		"importData1": importData1,
		"doUpdate": doUpdate,
		"edit2": edit2,
		"edit3": edit3,
		"edit4":edit4,
		"doCopyLib2": doCopyLib2,
		"deleteCopyLib2": deleteCopyLib2,
		"addToEx2": addToEx2,
		"setjira": setjira,
		"doTaskStatus2": doTaskStatus2
	});
});