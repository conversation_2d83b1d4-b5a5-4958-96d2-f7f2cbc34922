$(document).ready(function() {
   var pathValue="biomarker-dispatch-pd-libpooling-addtoex-addtoex";
   var paramsValue;
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
	   paramsValue=params;
	   var toolbar=getButtonTemplates(pathValue,[
           {name:"edit",target:"add",title:"确认选择"},
       ]);
       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"ex_sm_pool_lib_list",
              	"objects":[[
                                 "PB混样-全长转录组",
				"PB混样-微生物全长",
				"混样建库-ONT建库-DNA",
				"混样建库-ONT建库-Iso-RNA"
                               ],["草稿","接收退回"]]},
                detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
                detailInit: function (e) {
           	var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"lib_sample_pool_all_list","objects":[[ROW_ID]]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
  }
  
   var add=function(){
	   
       var arrIds=getSelectData(gridNameGrid);
       if(arrIds.length!=1){
          	alertMsg("请选择一条记录进行操作!");
          	return;
        }
       var objectadd=[];
       var objctuptaskmx=[];
       var objectupex=[];
       for(var i=0;i<arrIds.length;i++){
    	   var ids=paramsValue["IDS"];
    	   var taskmxids=paramsValue["TASKMXIDS"];
	  	   var time=sysNowTimeFuncParams["sysNowTime"];
	  	   var username=getLimsUser()["name"];
	  	   for(var j=0;j < ids.length;j++){
	       		objectadd.push({
	       		   "ID":ids[j],
               	           "POOL_ID":arrIds[i],//关联执行单
               	    	    "ISPOOL_TWO":"是"//标识是否已排单
		    	});
	       		objctuptaskmx.push({
	       			"ID":taskmxids[i],
	    	       	        "TASK_LSMX_STATUS":"建库中"
			    });
	        }
       	}
       var exeids=paramsValue["EXEIDS"];
       for(var i=0;i < exeids.length;i++){
       	 objectupex.push({
  	       		"ID":exeids[i],
  	       		"EX_RE_STATUS":"建库中"
  	       	});
        }
       
        var urlsend="system/jdbc/save/batch/table";
   	    var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
   	    putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");
   	    
   	    var paramsuptaskmx={"tableName":"BIO_TASK_LIBMX","objects":objctuptaskmx};
   	    putAddOrUpdata(urlsend,paramsuptaskmx,"否","建库中");
   	    
   	    var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectupex};
    	 putAddOrUpdata(urlsend,paramsup,"是","建库中");
      
    } 
    
   //批量执行插入
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		funcExce(pathValue+"pageCallBack");
              		funcExce(pathValue+"close");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
  
   
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "add":add,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});