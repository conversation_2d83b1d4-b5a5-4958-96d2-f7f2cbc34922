$(document).ready(function() {

    var pathValue="biomarker-dispatch-pd-libpooling-check-check";
debugger;

	var paramsValue;
	var gridNameGrid;
        var gridNameS2;


    var init=function(params){

	paramsValue=params;


       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: "",
           read: {"query": "lib_sample_pool_all_list-Check", "objects": [[paramsValue["ID"]]],"search": {"JK_CHECK":"OK"}}

       };
    
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }

    var clickButton=function(html,id,obj){
      addch();
    
    }



     var func=function(){
        if (event.keyCode === 13) {
     	    addch();
        }
     }

    var addch=function(){
    var code=$("#CODE"+pathValue).val(); 

       //转换成数组
      snsArr=code.split(/[(\r\n)\r\n]+/);
      //删除空项
      snsArr.forEach((item,index)=>{
            if(!item){
               snsArr.splice(index,1);
            }
      })

                   var rows ;
                  var objectadd=[];
                   time=sysNowTimeFuncParams["sysNowTime"];
                  $.fn.ajaxPost({
                     ajaxUrl: "system/jdbc/query/one/table",
                     ajaxType: "post",
                      ajaxAsync: false,
                      ajaxData: {"query": "lib_sample_pool_all_list-Check", "objects": [[paramsValue["ID"]]]},
                      succeed: function (rs) {
                                   rows = rs.rows;             
        		       }
                              });
                     for(var i=0;i<snsArr.length;i++){    
                           for(var j=0;j<rows.length;j++){    
                                if(snsArr[i]==rows[j]["LIBRARY_CODE"]){
                                      objectadd.push({
               	    	       		"ID":rows[j]["ID"],   
               	    	       		"JK_CHECK":"OK", 
                                        "JK_CHECK_TIME":time, 
               	    	       	  });
                                    break;
                                 }    
                                 if(j==rows.length){alertMsg(snsArr[i]+"核验失败");}          
                        }

                  	 
                      }
                     if(objectadd.length>0){
                                var urlsend="system/jdbc/save/batch/table";
          	        	 var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
          	        	 putAddOrUpdata(urlsend,paramsadd,"是","推入下一步实验任务");
                         
                                
                         }
}






  //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		// alertMsg("提示:操作成功!");
                	refreshGrid();
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }

     var close1=function(){

     	funcExce(pathValue + "pageCallBack"); //执行回调
	funcExce(pathValue + "close"); //关闭页面
   }

     var refreshGrid=function(){
    
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }

     }

	funcPushs(pathValue,{
	         "refreshGrid":refreshGrid,
		"init":init,
		"close1":close1,
		"func":func,
                  "clickButton":clickButton

	});
 
 });