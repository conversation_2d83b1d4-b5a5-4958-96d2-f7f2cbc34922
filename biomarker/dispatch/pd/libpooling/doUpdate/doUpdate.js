$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-libpooling-doUpdate-doUpdate";
    /**
 * 初始化数据-无参
 */
    var initData = function () {
        return {
            tableName: "BIO_LIB_INFO"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var paramsValue;
    var init = function (params) {
        paramsValue = params;
    }


    var submit = function () {
        var object = [];
        var ids = paramsValue["IDS"];
        var LIBRARY_JHDATA = $("#LIBRARY_JHDATA" + pathValue).val();   //PE每板最大
        for (var i = 0; i < ids.length; i++) {
            object.push({
                "ID": ids[i],
                "LIBRARY_JHDATA": LIBRARY_JHDATA,
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": object };
        putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");

    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        funcExce(pathValue + "pageCallBack");//执行回调
                        funcExce(pathValue + "close");//关闭页面
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});