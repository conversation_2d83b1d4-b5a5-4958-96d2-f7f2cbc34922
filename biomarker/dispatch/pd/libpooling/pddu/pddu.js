$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-libpooling-pddu-pddu";    
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_LIB_POOLING"
        };
    }
    var init=function(params){
    	debugger;
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        $("#POOL_TYPE"+pathValue).val(paramsValue["POOL_TYPE"]);
        $("#POOL_LIB_NUM"+pathValue).val(paramsValue["IDS"].length);
        $("#NO_CHAR"+pathValue).val("JJ");

	     var extype=paramsValue["POOL_TYPE"];
	     $("#POOL_TYPE"+pathValue).val(extype);
	     if(extype=="PB混样-微生物全长")  $("#SEQ_PLAT"+pathValue).val("PB");
	     if(extype=="PB混样-全长转录组")  $("#SEQ_PLAT"+pathValue).val("PB");
	     if(extype=="混样建库-ONT建库-DNA")  $("#SEQ_PLAT"+pathValue).val("ONT");
	     if(extype=="混样建库-ONT建库-Iso-RNA")  $("#SEQ_PLAT"+pathValue).val("ONT");
	     if(extype=="DNA混样建库-MCD非简化建库")  $("#SEQ_PLAT"+pathValue).val("NGS");
	     if(extype=="DNA混样建库-SLAF建库")  $("#SEQ_PLAT"+pathValue).val("NGS");
	     if(extype=="PB混样-基因组")  $("#SEQ_PLAT"+pathValue).val("PB");

    }

  var subUpData=function(){
debugger;
	  	 var time=sysNowTimeFuncParams["sysNowTime"];
	     var username=getLimsUser()["name"];
         //插入执行主单
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
debugger;
                 if(result["code"]>0){
                     alertMsg("提交成功,生成的草稿,请前往审核提交!","success",function(){
debugger;
                    	var ids=paramsValue["IDS"];
                      	var exeids=paramsValue["EXEIDS"];
                      	var taskmxids=paramsValue["TASKMXIDS"];
                      	
                     	var objectadd=[];
                     	var objectlibup=[];
                     	var objectup=[];
                     	var oubjctuptaskmx=[];
                     	
                          for(var i=0;i < ids.length;i++){
                         		objectlibup.push({
                         			"ID":ids[i],
               	    	       	"POOL_ID":result["ID"],//关联执行单
               	    	       	"ISPOOL_TWO":"是"//标识是否已排单
               	    	       	});
               	    	       	if(taskmxids[i]!=""){
                         		     	    oubjctuptaskmx.push({//BIO_TASK_LIBMX
                         			     	"ID":taskmxids[i],
               	    	       	     	"TASK_LSMX_STATUS":"建库中"
               	    	       	   });
               	    	       	}
                          }
                          for(var i=0;i < exeids.length;i++){
                         	 objectup.push({
            	    	       		"ID":exeids[i],
            	    	       		"EX_RE_STATUS":"建库中"
            	    	       	});
                          }

                     objectadd.push({
               	    	   "ID":result["ID"],
               	    	   "LIBRARY_CODE":$("#POOL_CODE"+pathValue).val(),
               	    	   "SEQ_PLAT":$("#SEQ_PLAT"+pathValue).val(),
                	    	   "ISPOOL":"是",
               	    	    "ISPOOL_TWO":"否"
               	 });
                                //执行文库添加(pooling文库插入)
                                var urladd="system/jdbc/save/one/table/objects";
          	        	 var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
          	        	 putAddOrUpdata(urladd,paramsadd,"否","推入下一步实验任务");
                                //执行文库更新
                                var urlsend="system/jdbc/save/batch/table";
          	        	 var paramlibup={"tableName":"BIO_LIB_INFO","objects":objectlibup};
          	        	 putAddOrUpdata(urlsend,paramlibup,"否","推入下一步实验任务");

          	        	 var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
 	         	      	 putAddOrUpdata(urlsend,paramsup,"否","建库中");
 	         	      	 if(oubjctuptaskmx.length>0){
 	         	      	    var paramsuptaskmx={"tableName":"BIO_TASK_LIBMX","objects":oubjctuptaskmx};
	         	      	     putAddOrUpdata(urlsend,paramsuptaskmx,"是","建库中");
 	         	      	}
 
                     });
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });

    }
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		funcExce(pathValue+"pageCallBack");
                    funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 
    var submit=function(){
    	subUpData();
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "subUpData":subUpData
    });
 
 });