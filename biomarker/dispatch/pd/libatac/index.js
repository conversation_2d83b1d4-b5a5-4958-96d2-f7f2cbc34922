$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-libatac-index";
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var gridNameD4Grid;
    var gridNameD5Grid;
    var gridNameD6Grid;
    var gridNameD7Grid;
    var gridNameS = [];
    var gridNameS1 = [];
    var gridNameS2 = [];
    var gridNameS3 = [];

    //对同一任务单号进行文库产生计数器
    var order = [];
    var order_mn = [];
    var ordersm = [];
    var ordersm_mn = [];
    var ordersm_no = [];
    var ordersm_no_mn = [];
    //待排
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit", title: "生成执行单" },
            //{name:"edit",target:"upsmStatus",title:"修改建库状态"},
            { name: "edit", target: "addToEx", title: "追加任务到执行单" },
            { name: "edit", target: "doTaskStatus", title: "任务单状态修改" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_EXE_QC_SHEET_list",
                "objects": [["ATAC检测"], ["结果已审核", "建库中"]]
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: {
                        "query": "query_DO_QC_DNA_HIC_ATAC_list", "objects": [],
                        "search": { "EXE_TQQC_ID": [ROW_ID] }
                    },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        init1();
        init2();
        init3();
        init4();
        init5();
        init6();
        init7();
    }
    //待审核
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "ok", target: "doOK", title: "提交" },
            { name: "edit", target: "doGenNo", title: "生成文库编号" },
            { name: "edit", target: "doGetIndex", title: "分配index" },
            { name: "delete", target: "remove", title: "移除任务明细" },
            { name: "delete", target: "doDelete", title: "删除执行单" },
            { name: "edit", target: "doUpdate", title: "修改实验员" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "lib_pd_SHEET_list", "objects": [["ATAC建库"], ["待审核", "已接收", "接收退回", "审核退回"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMxHicAtac", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }


        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    //已处理
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "lib_pd_SHEET_list", "objects": [["ATAC建库"], ["待接收", "已接收", "实验退回", "建库提交", "建库待审核", "建库已审核"]], "search": { "EX_EXECUTE_MODE": "实验员" } },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMxHicAtac", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    //完成
    var init3 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "移至待排" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_EXE_QC_SHEET_list",
                "objects": [["ATAC检测"], ["已完结", "终止", "未建库", "暂停"]]
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: {
                        "query": "query_DO_QC_DNA_HIC_ATAC_list", "objects": [],
                        "search": { "EXE_TQQC_ID": [ROW_ID] }
                    },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);

    }
    //待排任务
    var init4 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "editA", title: "智能排单" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_EXE_QC_SHEET_await_list",
                "objects": [], "search": { "EX_TYPE_LB": ["ATAC检测"], "EX_RE_STATUS2": ["结果已审核", "建库中"] }
            },
            fetch: function (data) {
                gridNameS5 = data;
            }

        };
        gridNameD4Grid = initKendoGrid("#gridNameD4Grid" + pathValue, gridNameGridJson);
    }
    //预处理
    var init5 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "checkSample", title: "核验" },
            { name: "edit", target: "submit", title: "提交" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "lib_pd_SHEET_list", "objects": [["ATAC建库"], ["预处理"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMxHicAtac-Check", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);

            }


        };
        gridNameD5Grid = initKendoGrid("#gridNameD5Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    //PE任务待处理
    var init6 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit", title: "提交" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "lib_pd_SHEET_list", "objects": [["ATAC建库"], ["待接收", "已接收", "实验退回", "建库提交", "建库待审核", "建库已审核"]], "search": { "EX_EXECUTE_MODE": "PE" } },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMxHicAtac", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                //gridNameS2.push(subGrid_N);
            }
        };
        gridNameD6Grid = initKendoGrid("#gridNameD6Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    //   PE执行状态
    var init7 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit1", title: "提交" },
            { name: "edit", target: "collection", title: "采集" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "lib_pd_SHEET_list", "objects": [["ATAC建库"], ["PE执行状态"]], "search": { "EX_EXECUTE_MODE": "PE" } },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMxHicAtac", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                //gridNameS2.push(subGrid_N);
            }
        };
        gridNameD7Grid = initKendoGrid("#gridNameD7Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    var add = function () {
        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/pdup/pdup",
            title: "常规建库排单.."
        };
        openWindow(winOpts);
    }
    //排单生成
    var edit = function () {
        debugger;
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        var ids = [];
        var taskids = [];
        var qcids = [];
        var biocodes = [];
        var samplecodes = [];
        var libtypes = [];
        var libtypemxs = [];
        var TQEXEIDS = [];

        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            libtypemxs.push(arrIds[i]["LIBRARY_TYPE_EN"]);
            qcids.push(arrIds[i]["QCID"]);
            biocodes.push(arrIds[i]["SAMPLE_GENNO"]);
            samplecodes.push(arrIds[i]["SAMPLE_CODE"]);
            TQEXEIDS.push(arrIds[i]["TASK_LS_ID"]);
            if (libtypes.indexOf(arrIds[i]["LIBRARY_TYPE_EN"]) < 0) {
                libtypes.push(arrIds[i]["LIBRARY_TYPE_EN"]);//文库类型
            }
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/pdup/pdup",
            title: "ATAC建库排单.."
        };
        openWindow(winOpts, { "IDS": ids, "TASKIDS": taskids, "SAMPLECODES": samplecodes, "BIOCODES": biocodes, "QCIDS": qcids, "TQEXEIDS": TQEXEIDS, "LIBTYPES": libtypes, "LIBTYPEMXS": libtypemxs, "EX_TYPE": "ATAC建库" });
    }
    //分派index
    var doGetIndex = function () {
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //检查是不是为同一文库类型
        var libtype = "";
        for (var i = 0; i < g.length; i++) {
            if (i == 0) libtype = g[i]["LIBRARY_TYPE"];
            if (libtype != g[i]["LIBRARY_TYPE"]) {
                alertMsg("提示:无法分配,存在不同的文库类型(<font color=#ff0000>" + libtype + "与" + g[i]["LIBRARY_TYPE"] + "</font>)!");
                return;
            }
        }

        var params = { "INDEX_TYPE": libtype, "INDEX_NUMBER": g.length };
        $.fn.ajaxPost({
            ajaxUrl: "bio/lib/modular/listIndex",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] == 1) {
                    var rows = result["INDEX"];
                    var objectup = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        objectup.push({
                            "ID": g[i]["LIBID"],//关联更新ID
                            "INDEX_NAME": row["INDEX_NAME"],//Index名称
                            "SEQ_I7_1": row["SEQ_I7_1"],//I7端(或正向)
                            "SEQ_I7_2": row["SEQ_I7_2"],//I7端2
                            "SEQ_I7_3": row["SEQ_I7_3"],//I7端3
                            "SEQ_I7_4": row["SEQ_I7_4"],//I7端4
                            "SEQ_I5_NAVA": row["SEQ_I5_NAVA"],//I5端-Nova(或反向)
                            "SEQ_I5_XTEN": row["SEQ_I5_XTEN"],//I5端-Xten
                            "GEN_N": row["GEN_N"],//错位碱基数
                            "ER_SEQ": row["ER_SEQ"],//错位碱基序列
                            "LIB_KIT": row["LIB_KIT"]//建库试剂盒
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "更新index");
                } else {
                    alertMsg("提示:无法分配,请检查“" + libtype + "”类型的index库是否维护!</font>)!");
                }
            }
        });

    }
    var doOK = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        //校验文库编号是否为空
        var s = "";
        var si = "";
        var params = { "query": "doCheckLibCodeIsPass", "objects": [arrIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        if (row["EX_TYPE"] == "三代ONT基因组建库") {//判断切胶编号
                            if (row["JK_QJ_CODE"] == "" || row["JK_QJ_CODE"] == null) {
                                if (s == "") {
                                    s = row["EX_DH_NO"];
                                }
                            }
                        } else {
                            if (row["LIBRARY_CODE"] == "" || row["LIBRARY_CODE"] == null) {
                                if (s == "") {
                                    s = row["EX_DH_NO"];
                                }
                            }
                            if (row["INDEX_NAME"] == "" || row["INDEX_NAME"] == null) {
                                if (si == "") {
                                    si = row["EX_DH_NO"];
                                }
                            }
                        }

                    }
                    if (s != "") {
                        alertMsg("提示:单号“" + s + "”存在文库编号(或切胶编号为空--ONT)为空!");
                        return;
                    }
                    if (si != "") {
                        alertMsg("提示:单号“" + si + "”存在“index名称”为空!");
                        return;
                    }
                    var objectup = [];
                    for (var i = 0; i < arrIds.length; i++) {
                        var time = sysNowTimeFuncParams["sysNowTime"];
                        var username = getLimsUser()["name"];
                        objectup.push({
                            "ID": arrIds[i],//联联任务ID
                            "EX_RE_STATUS": "待接收"
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "提交");
                    doRequeDoUpTaskLibmxStatus(arrIds, "建库待接收");
                }
            }
        });
    }
    //修改实验员 
    var doUpdate = function () {
        var arrIds = getGridSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/updateman/updateman",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"], "EX_MAN": arrIds[0]["EX_MAN"], "EX_EXECUTE_MODE": arrIds[0]["EX_EXECUTE_MODE"] });//传递
    }
    //自动化排单
    var editA = function () {
        debugger;
        var gridData = getGridSelectData(gridNameD4Grid);
        debugger;
        if (gridData.length == 0) {
            alertMsg("至少选择一个样本");
            return;

        }

        if (gridData.length > 999) {
            alertMsg("单次只能排小于1000个样本！");
            return;
        }


        var params = { "ids": [], "keys": [] };

        for (var j = 0; j < gridData.length; j++) {
            var id = gridData[j]["ID"];
            var lfo2 = "ATAC建库"; //工序
            var wor2 = gridData[j]["MEHOD_JKPLAT"]; //样品执行组
            var met2 = gridData[j]["LIBRARY_METHOD"]; //样品执行方法
            params.ids.push(id);
            if (params.keys.indexOf(lfo2 + "-" + wor2 + "-" + met2) < 0) {
                params.keys.push(lfo2 + "-" + wor2 + "-" + met2);
                params[lfo2 + "-" + wor2 + "-" + met2] = {
                    lfo: lfo2,
                    met: met2,
                    wor: wor2,
                    ids: []
                };

            }
            params[lfo2 + "-" + wor2 + "-" + met2].ids.push(id);

        }

        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/automate/automate",
            title: "自动排单明细.."
        };
        openWindow(winOpts, params);//传递

    }
    //撤回
    var doReturn = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["EX_RE_STATUS"] != "待接收") {
                alertMsg("操作失败,所选记录存在已“已接收”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "EX_RE_STATUS": "待审核"
                });
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //移至待排
    var doReturn2 = function () {
        var g = getGridSelectData(gridNameD3Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["EX_RE_STATUS2"] == "暂停" || g[i]["EX_RE_STATUS2"] == "未建库" || g[i]["EX_RE_STATUS2"] == "已完结") {
                objectup.push({
                    "ID": g[i]["ID"],
                    "EX_RE_STATUS2": "结果已审核"
                });
            } else {
                alertMsg("操作失败,只有“<font color=#ff0000>暂停、未建库</font>”状态方允许操作!");
                return;
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //追加任务
    var addToEx = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var ids = [];
        var codes = [];
        var types = [];
        var qcids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            qcids.push(arrIds[i]["QCID"]);
            types.push(arrIds[i]["LIBRARY_TYPE_EN"]);
            codes.push(arrIds[i]["SAMPLE_GENNO"]);
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/addtoex/addtoex",
            title: "追加样本到执行单.."
        };
        openWindow(winOpts, { "IDS": ids, "CODES": codes, "types": types, "QCIDS": qcids });
    }
    //任务单状态修改
    var doTaskStatus = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/uptaskstatus/uptaskstatus",
            title: "修改任务单状态.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    }
    //样本状态修改
    var upsmStatus = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/upsmstatus/upsmstatus",
            title: "修改样本状态.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    }
    //文编号
    var doGenNo = function () {
        order = [];
        order_mn = [];
        ordersm = [];
        ordersm_mn = [];
        ordersm_no = [];
        ordersm_no_mn = [];
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        //取对应表
        var params = { "query": "queryBioLibTypeList", "objects": [] };
        var iniFist = "";
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var libtypename = [];
                    var initials = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        libtypename.push(row["LIB_TYPE_NAME"]);
                        initials.push(row["INITIALS"]);
                    }
                    var objectup = [];
                    var objectdata = [];
                    var objectdatas = [];
                    for (var i = 0; i < g.length; i++) {
                        var DATA_SUM = g[i]["DATA_SUM"];
                        if (g[i]["DATA_UNIT"] == "G") {
                            DATA_SUM = DATA_SUM / 0.3;
                        }
                        //更新记录
                        iniFist = checkInitals(g[i]["LIBRARY_TYPE"], libtypename, initials);
                        objectup.push({
                            "ID": g[i]["LIBID"],//关联更新ID
                            "THE_DATA_APSUM": DATA_SUM,
                            "LIBRARY_CODE": getLibCodeNo(g[i]["TASK_NO"], iniFist, g[i]["DON"], g[i]["LIBMAXCODE"], g[i]["SAMPLE_CODE"], g[i]["DATA_LIBCODE"])
                        });
                        objectdata.push({
                            "ID": g[i]["ID"],//关联更新ID
                            "THE_DATA_APSUM": DATA_SUM,
                            "THE_DATA_SUM": DATA_SUM,
                            "TEST_DATA_LEN": DATA_SUM
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup1 = { "tableName": "BIO_TQ_TASK_MX", "objects": objectdata };
                    putAddOrUpdata(urlsend, paramsup1, "否", "");
                    var paramsup3 = { "tableName": "BIO_LIB_INFO", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup3, "是", "提交");

                }
            }
        });

    }

    //文库编号
    var getLibCodeNo = function (orderno, c, n, maxcode, smcode, datalibcode) {

        if (maxcode) {//已生成过文库
            var maxcode_1 = maxcode.substr(0, maxcode.lastIndexOf("-") + 1);
            var maxcode_2 = maxcode.replace(maxcode_1, "");
            maxcode_2 = parseInt(maxcode_2);
            var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
            var mn;
            if (indexn > -1) {
                mn = ordersm_no_mn[indexn] + 1;
                ordersm_no_mn[indexn] = mn;
            } else {
                mn = maxcode_2 + 1;
                ordersm_no.push(orderno + "_" + smcode + "_no");
                ordersm_no_mn.push(mn);
            }
            return maxcode_1 + getBioCodeNo(mn, 0);
        } else {//没有生成过文库
            if (datalibcode) { //运营提前生产
                var maxcode_1 = datalibcode.substr(0, datalibcode.lastIndexOf("-") + 1);
                var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
                var mn;
                if (indexn > -1) {
                    mn = ordersm_no_mn[indexn] + 1;
                    ordersm_no_mn[indexn] = mn;
                } else {
                    mn = 1;
                    ordersm_no.push(orderno + "_" + smcode + "_no");
                    ordersm_no_mn.push(mn);
                }
                return maxcode_1 + getBioCodeNo(mn, 0);
            } else {//运营没有提前生产
                var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
                var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
                var mn;
                if (indexn > -1) {
                    mn = ordersm_no_mn[indexn] + 1;
                    ordersm_no_mn[indexn] = mn;
                } else {
                    mn = 1;
                    ordersm_no.push(orderno + "_" + smcode + "_no");
                    ordersm_no_mn.push(mn);
                }
                var indexn2 = order.indexOf(orderno);
                var indexn3 = ordersm.indexOf(orderno + "_" + smcode);
                var mn2;
                if (indexn3 > -1) {
                    mn2 = ordersm_mn[indexn3];
                } else if (indexn2 > -1) {
                    mn2 = order_mn[indexn2] + 1;
                    order_mn[indexn2] = mn2;
                    ordersm.push(orderno + "_" + smcode);
                    ordersm_mn.push(mn2);
                } else {
                    mn2 = n + 1;
                    order.push(orderno);
                    order_mn.push(mn2);
                    ordersm.push(orderno + "_" + smcode);
                    ordersm_mn.push(mn2);
                }
                var num = getNo(mn2, 0);
                tempcode = tempcode + c + num + "-" + getBioCodeNo(mn, 0);

                return tempcode;
            }
        }
    }
    //比对取对照
    var checkInitals = function (name, names, initals) {
        for (var i = 0; i < names.length; i++) {
            if (name == names[i]) {
                return initals[i];
            }
        }
        return "";
    }
    //文库流程号段(项目期号内)
    var getNo = function (num, ki) {
        num = num + ki;
        if (num < 10) {
            num = "000" + num;
            return num;
        }
        if (num >= 10 && num < 100) {
            num = "00" + num;
            return num;
        }
        if (num >= 100 && num < 1000) {
            num = "0" + num;
            return num;
        }
        return num;
    }
    //核酸号段(项目期号内)
    var getBioCodeNo = function (num) {
        if (num < 10) {
            num = "0" + num;
            return num;
        }
        return num;
    }

    //记录移除
    var remove = function () {
        var arrg = [];
        var arrIds = [];
        var obj = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            arrg = arrg.concat(arrSubID);

        }
        if (arrg.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        for (var i = 0; i < arrg.length; i++) {
            arrIds.push(arrg[i]["LIBID"]);
            obj.push({ "ID": arrg[i]["ID"], "TASK_SM_STATUS": "待提交" });
        }
        confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd1 = { "tableName": "BIO_TQ_TASK_MX", "objects": obj };
            putAddOrUpdata(urlsend, paramsadd1, "否", "移除");
            var params = { "tableName": "BIO_LIB_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        });
    }
    //样品核验 
    var checkSample = function () {
        var arrIds = getGridSelectData(gridNameD5Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行核验操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/libatac/check/check",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递
    }
    //预处理提交
    var submit = function () {
        var arrIds = getGridSelectData(gridNameD5Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }

        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: { "query": "queryTaskLibExMx-Check", "objects": [[arrIds[0]["ID"]]] },
            succeed: function (rs) {
                sample = rs.rows;             //样品
                var num = 0;
                for (var j = 0; j < sample.length; j++) {
                    if (sample[j]["JK_CHECK"] == "OK") {
                        num = num + 1;

                    }
                }
                if (num < sample.length) {
                    alertMsg("还有" + (sample.length - num) + "条未核验，不能提交");
                    return;
                }
                var objectSheet = [];
                objectSheet.push({
                    "ID": arrIds[0]["ID"],//id
                    "EX_RE_STATUS": "待审核"       //状态

                });
                var urlsend = "system/jdbc/save/batch/table";
                var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
                putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
            }
        });

    }
    //PE任务待处理提交
    var pesubmit = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameD6Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行提交!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行提交!");
            return;
        }
        if (arrIds[0]["PLATE_CODE"] == null) {

            alertMsg("执行单" + arrIds[0]["EX_DH_NO"] + "尚未分配板孔号，不能提交");
            return;
        }

        var objectSheet = [];
        objectSheet.push({
            "ID": arrIds[0]["ID"],//id
            "EX_RE_STATUS": "PE执行状态"       //状态
        });
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
        putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
    }
    //删除执行单
    var doDelete = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
            var url = "system/jdbc/delete/one/table/where";
            var params1 = { "tableName": "BIO_LIB_INFO", "where": { "EXE_TQQC_ID": arrIds } };
            deleteGridDataByIds(url, params1, refreshGrid);
            var params2 = { "tableName": "EXE_TQQC_SHEET", "where": { "ID": arrIds } };
            deleteGridDataByIds(url, params2, refreshGrid);
        });
    }
    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS = [];
        gridNameS1 = [];
        gridNameS2 = [];
        gridNameS3 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }
        if (gridNameD3Grid) {
            gridNameD3Grid.dataSource.read();
        }
    }

    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "edit": edit,
        "upsmStatus": upsmStatus,
        "addToEx": addToEx,
        "doTaskStatus": doTaskStatus,
        "checkSample": checkSample,
        "submit": submit,
        "doOK": doOK,
        "doGenNo": doGenNo,
        "remove": remove,
        "pesubmit": pesubmit,
        "editA": editA,
        "doDelete": doDelete,
        "doReturn": doReturn,
        "doReturn2": doReturn2,
        "doGetIndex": doGetIndex,
        "doUpdate": doUpdate,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
    });
});