$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-libhic-pdup-pdup";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "EXE_TQQC_SHEET"
        };
    }
    var init = function (params) {

        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);

        $("#EX_TYPE" + pathValue).val(paramsValue["EX_TYPE"]);
        $("#EX_MX_NUMBER" + pathValue).val(paramsValue["IDS"].length);
        //单号前缀
        $("#NO_CHAR" + pathValue).val("JH");
        var libtyps = paramsValue["LIBTYPES"];
        var strlibthype = "";
        for (var i = 0; i < libtyps.length; i++) {
            if (i == 0) {
                strlibthype = libtyps[i];
            } else {
                strlibthype += "," + libtyps[i];
            }
        }
        $("#EX_LIB_TYPE" + pathValue).val(strlibthype);
    }


    var subUpData = function () {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        /**
     * 平台很重要,后台上机排单需要分流
     */
        var SEQ_PLAT = "NGS-HIC";
        debugger;
        //插入执行主单
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    alertMsg("提交成功,生成的草稿,请前往审核提交!", "success", function () {
                        var ids = paramsValue["IDS"];
                        var taskids = paramsValue["TASKIDS"];
                        var qcids = paramsValue["QCIDS"];
                        var TQEXEIDS = paramsValue["TQEXEIDS"];
                        var objectadd = [];
                        var objectup = [];
                        var objectupqc = [];
                        var biocodes = paramsValue["BIOCODES"];
                        var samplecodes = paramsValue["SAMPLECODES"];
                        var libtypemxs = paramsValue["LIBTYPEMXS"];
                        for (var i = 0; i < ids.length; i++) {
                            objectadd.push({
                                "FROM_TQ_EXE_ID": TQEXEIDS[i],//关联提取执行单ID
                                "TASK_LIB_MX_ID": ids[i],//联联任务ID
                                "TQQC_ID": qcids[i],//提取检测ID
                                "EXE_TQQC_ID": result["ID"],//关联执行单
                                "SEQ_PLAT": SEQ_PLAT,//测序平台
                                "JK_TASKMX_STDATE": time,
                                "BIO_CODE": biocodes[i],//枋酸编号
                                "LIBRARY_TYPE": libtypemxs[i],//文库类型
                                "LIBRARY_TYPE_E": libtypemxs[i],//文库类型
                                "SAMPLE_CODE": samplecodes[i],//样品编号
                                "JK_TASKMX_MAN": username,//建库实验员
                                "SYS_MAN": username,//操作
                                "SYS_INSERTTIME": time//开始日期
                            });
                        }
                        for (var i = 0; i < taskids.length; i++) {
                            objectup.push({
                                "ID": taskids[i],
                                "EX_RE_STATUS2": "建库中"
                            });
                        }
                        for (var i = 0; i < qcids.length; i++) {
                            objectupqc.push({
                                "ID": qcids[i],
                                "LIB_STATES": "建库中"
                            });
                        }
                        //执行添加到文库
                        var urlsend = "system/jdbc/save/batch/table";
                        var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectadd };
                        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

                        var paramsqc = { "tableName": "BIO_DNA_RNA_QC", "objects": objectupqc };
                        putAddOrUpdata(urlsend, paramsqc, "否", "推入下一步实验任务");


                        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
                        putAddOrUpdata(urlsend, paramsup, "是", "建库中");

                    });
                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });

    }
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var submit = function () {
        subUpData();
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
        "subUpData": subUpData
    });

});