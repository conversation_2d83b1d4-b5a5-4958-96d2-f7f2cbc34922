$(document).ready(function() {
   var pathValue="biomarker-dispatch-pd-libont-redo-redo";
   /**
    * 初始化数据-无参
    */
   var initData=function(){
       return {};
   }

   var paramsValue;
   var gridNameGrid;
   var gridName1Grid;

   /**
    * 初始化-获取参数-并执行调用
    * @param {*} params 
    */
   var init=function(params){
	   
	   paramsValue=params;

        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"add",title:"确认选择"},
            {name:"edit",target:"doend",title:"结单"}
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"lib_pd_ont_ist_taskpool","objects":[["三代ONT基因组建库"],["已排单"],["已审核"],["否"]]},
            headerFilter:function(cols,i){
                if(i){
                   
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);//初始化表格的方法
        
        init1();
   }

   var init1=function(params){
	   
        var toolbar=getButtonTemplates(pathValue,[
        ]);//工具条
        //请求参数
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read:{"query":"lib_pd_ont_ist_taskpool","objects":[["三代ONT基因组建库"],["已排单"],["已审核"],["是"]]},
            headerFilter:function(cols,i){
                if(i){
                    
                }
            }
        };
        gridName1Grid = initKendoGrid("#gridName1Grid"+pathValue,gridNameGridJson);//初始化表格的方法
   }

  
   
    var add=function(){
    	
    	var g=getGridSelectData(gridNameGrid); 
        if(g.length==0){
            alertMsg("请至少选择一条数据进行添加样本操作!");
            return ;
        }
        
        var time=sysNowTimeFuncParams["sysNowTime"];
        var username=getLimsUser()["name"];
        var object=[];
        var objectadd=[];
        
        for(var i=0;i < g.length;i++){
        	
        	var newid=getRandomId();
        	object.push({
        		"ID":newid,//唯一标识
        		"ISREDO":"是",//设为重下单记录
        		"TASK_LS_ID":g[i]["TASK_LS_ID"],//关联主单ID
        		"BIO_CODE":g[i]["BIO_CODE"],//核酸编号
        		"SAMPLE_NAME":g[i]["SAMPLE_NAME"],//样品名称
        		"TASK_LSMX_TYPE":g[i]["TASK_LSMX_TYPE"],//建库平台
        		"SPECIES":g[i]["SPECIES"],//物种
        		"SPECIES_ORIGIN":g[i]["SPECIES_ORIGIN"],//组织部位/微生物样本来源
        		"PRODUCT_CODE":g[i]["PRODUCT_CODE"],//产品编号
        		"PRODUCT_TYPE":g[i]["PRODUCT_TYPE"],//产品类型
        		"LATIN_NAME":g[i]["LATIN_NAME"],//物种拉丁文
        		"TASK_LSMX_GC":g[i]["TASK_LSMX_GC"],//GC含量
        		"TASK_LSMX_BIOSAM":g[i]["TASK_LSMX_BIOSAM"],//生物学重复编号
        		"LIBRARY_TYPE":g[i]["LIBRARY_TYPE"],//文库类型
        		"LIBRARY_METHOD":g[i]["LIBRARY_METHOD"],//建库方法
        		"DATA_SUM":g[i]["DATA_SUM"],//合同数据量
        		"DATA_UNIT":g[i]["DATA_UNIT"],//合同数据量单位
        		"SAMPLE_BATCHNO":g[i]["SAMPLE_BATCHNO"],//样本批次编号
        		"BIO_JC_STTIME":toDateFormatByZone(g[i]["BIO_JC_STTIME"],"yyyy-MM-dd HH:mm:ss"),//检测开始时间
        		"BIO_JC_ENTIME":toDateFormatByZone(g[i]["BIO_JC_ENTIME"],"yyyy-MM-dd HH:mm:ss"),//检测完成时间
        		"BIO_JC_RESULT":g[i]["BIO_JC_RESULT"],//检测结果
        		"BIO_JC_REASON":g[i]["BIO_JC_REASON"],//不合格原因
        		"TASK_LSMX_FDATA":g[i]["TASK_LSMX_FDATA"],//实际测序（数据量）
        		"TASK_LSMX_SJDATA":g[i]["TASK_LSMX_SJDATA"],//总加测（数据量）
        		"TASK_LSMX_PCPAREA":g[i]["TASK_LSMX_PCPAREA"],//扩增区域
        		"TASK_LSMX_REMARKS":g[i]["TASK_LSMX_REMARKS"],//备注
        		"SAMPLE_CODE":g[i]["SAMPLE_CODE"],//样本编号
        		"PROJECT_NO":g[i]["PROJECT_NO"],//项目编号
        		"PROJECT_NAME":g[i]["PROJECT_NAME"],//项目名称
        		"SAMPLE_ID":g[i]["SAMPLE_ID"],//样本ID
        		"PROJECT_SUBNO":g[i]["PROJECT_SUBNO"],//项目期号
        		"LIBRARY_CH_TYPE":g[i]["LIBRARY_CH_TYPE"],//混样常规建库类型
        		"LIBRARY_QJ_TYPE":g[i]["LIBRARY_QJ_TYPE"],//是否切胶
        		"LIBRARY_TYPE_EN":g[i]["LIBRARY_TYPE_EN"],//文库类型-英文
        		"LIBRARY_FLOW":g[i]["LIBRARY_FLOW"],//建库流向
        		"MASS_RATIO":g[i]["MASS_RATIO"],//质量比例
        		"TEST_CODE":g[i]["TEST_CODE"],//实验编号
        		"REQUIRED_DEPTH":g[i]["REQUIRED_DEPTH"],//合同要求深度
        		"MCD_PASS":g[i]["MCD_PASS"]//MCD已排Lane
        	});
        	
        	objectadd.push({
        		"ID":getRandomId(),
	        	"TASK_LS_MX_ID":newid,//关联运营建库单明细ID
	        	"JK_TASKMX_STATUS":"待排单",//建库任务状态
	        	"ISFROM_QJ":"是",//来源切胶
	        	"JK_QJ_STATUS":"已审核",//切胶状态
	        	"JK_QJ_AFVOL":g[i]["JK_QJ_AFVOL"],//切胶回收体积
	        	"JK_QJ_AFNG":g[i]["JK_QJ_AFNG"],//切胶回收总量
	        	"JK_QJ_QUBIT":g[i]["JK_QJ_QUBIT"],//切胶Qubit浓度
	        	"JK_QJ_STDATE":g[i]["JK_QJ_STDATE"],//切胶实际开始日期
	        	"JK_QJ_ENDATE":g[i]["JK_QJ_ENDATE"],//切胶实际结束日期
	        	"JK_QJ_RESULT":g[i]["JK_QJ_RESULT"],//切胶结果
	        	"JK_QJ_UNRESULT":g[i]["JK_QJ_UNRESULT"],//切胶不合格原因
	        	"JK_QJ_AREA":g[i]["JK_QJ_AREA"],//切胶范围
	        	"JK_QJ_MAN":g[i]["JK_QJ_MAN"],//切胶负责人
	        	"JK_QJ_REMARK":g[i]["JK_QJ_REMARK"],//切胶备注
	        	"JK_QJ_BIO_SNG":g[i]["JK_QJ_BIO_SNG"],//切胶核酸用量
	        	"JK_QJ_BIO_NG":g[i]["JK_QJ_BIO_NG"],//核酸剩余量
	        	"JK_QJ_STEP":g[i]["JK_QJ_STEP"],//任务步骤
	        	"JK_QJ_DISS":g[i]["JK_QJ_DISS"],//打断条件
	        	"JK_QJ_DING":g[i]["JK_QJ_DING"],//打断起始量
	        	"JK_QJ_DIVO":g[i]["JK_QJ_DIVO"]//打断体积
        	});
       
        }
       var params={"tableName":"BIO_TASK_LIBMX","objects":object};
       var paramslibadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
       //插入任务明细记录
       var newUrl="system/jdbc/save/one/table/objects";
       putAddOrUpdata(newUrl,paramslibadd,"否","添加文库");
       putAddOrUpdata(newUrl,params,"是","重排任务");
       
    }
    
    var doend=function(){
    	
    	var g=getGridSelectData(gridNameGrid); 
        if(g.length==0){
            alertMsg("请至少选择一条数据进行添加样本操作!");
            return ;
        }
   
        var object=[];
        for(var i=0;i < g.length;i++){
        	object.push({
        		"ID":g[i]["ID"],
        		"ISEND":"是"
        	});
        }
        
        var params={"tableName":"BIO_TASK_LIBMX","objects":object};
        //插入任务明细记录
        var newUrl="system/jdbc/save/batch/table";
        putAddOrUpdata(newUrl,params,"本界面","结单");
        
        
    }

     
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();
        }
        if(gridName1Grid){
        	gridName1Grid.dataSource.read();
        }
     }
     
    //批量执行插入
     var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
         $.fn.ajaxPost({
             ajaxType:"post",
             ajaxUrl:urls,
             ajaxData:inobjjson,
             succeed:function(result){
                 if(result["code"]>0){
                	 if(isDoCallBack=="是"){
                         alertMsg("提交成功!");
                         funcExce(pathValue+"pageCallBack","1");
                         console.log(result);
                         funcExce(pathValue+"close");
                	 }else if(isDoCallBack=="本界面"){
                		 alertMsg("操作成功!");
                		 refreshGrid();
                	 }
                 }else{
                	 alertMsg(errMsg+"操作失败!");
                 }
             }
         });
     }
     
 function getRandomId() {
  	   return (('REDO-LIB-Task-' || '') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
 };

     funcPushs(pathValue,{
         "initData":initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
         "init":init,//初始化方法-在加载完初始化数据之后执行
         "add":add,
         "doend":doend,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});