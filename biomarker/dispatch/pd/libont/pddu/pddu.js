$(document).ready(function() {
	var pathValue="biomarker-dispatch-pd-libont-pddu-pddu";   
    var paramsValue;
    var initData=function(){
        return {
            tableName:"EXE_TQQC_SHEET"
        };
    }
    var init=function(params){
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        $("#EX_TYPE"+pathValue).val(paramsValue["EX_TYPE"]+"-建库");
        $("#EX_MX_NUMBER"+pathValue).val(paramsValue["IDS"].length);
        var libtyps=paramsValue["LIBTYPES"];
        var strlibthype="";
        for(var i=0;i<libtyps.length;i++){
        	if(i==0){
        		strlibthype=libtyps[i];
        	}else{
        		strlibthype+=","+libtyps[i];
        	}
        }
        $("#EX_LIB_TYPE"+pathValue).val(strlibthype);

    }

  var subUpData=function(){
	  $("#EX_RE_STATUS"+pathValue).val("待处理");
	 var time=sysNowTimeFuncParams["sysNowTime"];
	 var username=getLimsUser()["name"];
     var paramsF = getJsonByForm("form",pathValue);
     
         //插入执行主单
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                    	var ids=paramsValue["IDS"];//切胶文库ID
                    	var taskids=paramsValue["TASKIDS"];
                    	var taskmxids=paramsValue["TASKMXIDS"];
                    	var biocodes=paramsValue["BIOCODES"];
                        var samplecodes=paramsValue["SAMPLECODES"];
                        var libtypemxs=paramsValue["LIBTYPEMXS"];
                     	var objectadd=[];
                     	var objectup=[];
                     	var objectuplib=[];
                     	var poolid=null;
                     	var pooladd=[];
                     	if(paramsF["IS_POOL"]=="是"){//为混库排单的,测需要创建pool记录,关联到lib
                     		poolid=result["ID"];//一对一
                     		pooladd.push({
                     			"ID":poolid,
                     			"POOL_CODE":paramsF["EX_DH_NO"],
                     			"POOL_MAN_ID":paramsF["EX_MAN_ID"],
                     			"POOL_MAN":paramsF["EX_MAN"],
                     			"POOL_B_TIME":paramsF["EX_B_TIME"],
                     			"POOL_E_TIME":paramsF["EX_E_TIME"],
                     			"POOL_LIB_NUM":paramsF["EX_MX_NUMBER"],
                     			"SEQ_PLAT":"ONT",
                     			"POOL_BACK":paramsF["EX_NEEDING_ATN"],
                     			"POOL_TYPE":"三代ONT基因组建库-混",
                     			"POOL_STATUS":"混库待接收",
                     			"SYS_MAN":username,
                     			"SYS_INSERTTIME":time
                     		});
            				
                     	}

                     	
                          for(var i=0;i < ids.length;i++){
                         		objectadd.push({
                         			"FROM_ID":ids[i],
                         			"POOL_ID":poolid,
               	    	       		"EXE_TQQC2_ID":result["ID"],//关联执行单
               	    	       		"EXE_TQQC_ID":result["ID"],//关联执行单
                                    "JK_TASKMX_STATUS":"已排单",
	               	    	       	"TASK_LIB_MX_ID":taskmxids[i],//联联任务ID
	           	    	       		"BIO_CODE":biocodes[i],//枋酸编号
               	    	       		"LIBRARY_TYPE":libtypemxs[i],//文库类型
	           	    	       		"SAMPLE_CODE":samplecodes[i],//样品编号
	           	    	       		"SEQ_PLAT":"ONT",//测序平台
               	    	       	     "JK_TASKMX_MAN":$("#EX_MAN"+pathValue).val(),
	           	    	       		"SYS_MAN":username,//实验员
	           	    	       		"SYS_INSERTTIME":time//开始日期
               	    	       	});
                         		objectuplib.push({
              	    	       		"ID":ids[i],
              	    	       		"JK_TASKMX_STATUS":"待建库"
              	    	       	});
                          }
                          for(var i=0;i < taskids.length;i++){
                          	 objectup.push({
             	    	       		"ID":taskids[i],
             	    	       		"TASK_LS_STATUS":"建库中"
             	    	       	});
                           }

                         //混库的添加上pool
                         if(pooladd.length>0){
                        	 var urladd="system/jdbc/save/one/table/objects";
                        	 var parmarpool={"tableName":"BIO_LIB_POOLING","objects":pooladd};
                        	 putAddOrUpdata(urladd,parmarpool,"否","");
                         }
                         //执行添加到文库
                         var urlsend="system/jdbc/save/batch/table";
          	        	 var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
          	        	putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");
          	        	var paramslibup={"tableName":"BIO_LIB_INFO","objects":objectuplib};
          	        	 putAddOrUpdata(urlsend,paramslibup,"否","更新");
          	        	 var paramsup={"tableName":"BIO_TASK_LIB","objects":objectup};
 	         	      	 putAddOrUpdata(urlsend,paramsup,"是","");
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });

    }
  function getRandomId() {
      return 'FDSX-ONT-POOL-' + new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
   };

  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提交成功!");
             		funcExce(pathValue+"pageCallBack");
                    funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 
    var submit=function(){
    	subUpData();
    }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
        "subUpData":subUpData
    });
 
 });