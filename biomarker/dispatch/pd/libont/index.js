$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-libont-index";
    var initData=function(){
        return {};
    }
 
    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var gridNameS1=[];
    var gridNameS2=[];
    var gridNameS3=[];
 
    var init=function(params){
         var toolbar=getButtonTemplates(pathValue,[
             {name:"edit",target:"edit",title:"生成执行单"},
             {name:"edit",target:"upsmStatus",title:"修改建库状态"},
             {name:"edit",target:"addToEx",title:"追加任务到执行单"},
         ]);
         var gridNameGridJson={
             url: "system/jdbc/query/one/table",
             sort: "",
             toolbar: toolbar,
             read:{"query":"lib_pd_ont_ist","objects":[["切胶结果已审核"],["草稿","建库中","待建库"]]},
         };
         gridNameDGrid = initKendoGrid("#gridNameDGrid"+pathValue,gridNameGridJson);
         init1();
         init2();
         init3();
    }
    //待审核
        var init1=function(params){
        var toolbar=getButtonTemplates(pathValue,[
            {name:"ok",target:"doOK",title:"提交"},
            {name:"edit",target:"doGenNo",title:"生成文库编号"},
            {name:"delete",target:"remove",title:"移除任务明细"},
            {name:"delete",target:"doDelete",title:"删除执行单"},
        {name:"edit",target:"doUpdate",title:"修改安排数据量"},
         ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read:{"query":"lib_pd_SHEET_list",
                "objects":[["三代ONT基因组建库-建库"],
                    ["待处理","接收退回"]],
                    "search":{"EX_LB":["建库"]}},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"lib_pd_ont_ist_ok","objects":[ROW_ID]},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid"+pathValue,gridNameGridJson);
   }
        //已处理
        var init2=function(params){
         var toolbar=getButtonTemplates(pathValue,[
             {name:"return",target:"doReturn",title:"撤回"},	
          ]);
         var gridNameGridJson={
             url: "system/jdbc/query/one/table",
             sort: "",
             toolbar: toolbar,
             read:{"query":"lib_pd_SHEET_list",
                 "objects":[["三代ONT基因组建库-建库"],
                     ["待接收","实验退回","已接收","建库提交","建库已审核"]],
                     "search":{"EX_LB":["建库"]}},
             headerFilter:function(cols,i){},
             detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
             detailInit: function (e) {
                 var ROW_ID = e.data.ID;
                 var subGrid_N_JSON={
                     url: "system/jdbc/query/one/table",
                     sort: "",
                     toolbar: null,
                     height: 320,
                     read:{"query":"lib_pd_ont_ist_ok","objects":[ROW_ID]},
                 };
                 var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
                 gridNameS2.push(subGrid_N);
             }
             
             
         };
         gridNameD2Grid = initKendoGrid("#gridNameD2Grid"+pathValue,gridNameGridJson);
    }
 
     var init3=function(params){
         var toolbar=getButtonTemplates(pathValue,[
             {name:"return",target:"doReturn2",title:"移至待排单"},
 
         ]);
         var gridNameGridJson={
             url: "system/jdbc/query/one/table",
             sort: "",
             toolbar: toolbar,
             read:{"query":"lib_pd_ont_ist","objects":[["切胶结果已审核"],["上机中","结单","测序","暂停","终止","未建库"]]},
         };
         gridNameD3Grid = initKendoGrid("#gridNameD3Grid"+pathValue,gridNameGridJson);
    }
     //排单
     var edit=function(){
       var g=getGridSelectData(gridNameDGrid);
       if(g.length==0){
          alertMsg("请至少选择一条数据进行修改!");
          return ;
       }
       var ids=[];
       var taskids=[];
       for(var i=0;i<g.length;i++){
          
           ids.push(
               g[i]["ID"]
               );
           taskids.push(
               g[i]["TASKMXID"]
               );
       }
         var winOpts={
             url:"biomarker/dispatch/pd/libont/pddu/pddu",
             title:"切胶排单.."
         };
        //判断类型是否全部分同一为,并取出形成类型单
         var g=getGridSelectData(gridNameDGrid);
         var a="";
         var b="";
         var taskids=[];
         var biocodes=[];
         var samplecodes=[];
         var libtypes=[];
         var taskmxids=[];
         var libtypemxs=[];
         for(var i=0;i<g.length;i++){
             biocodes.push(g[i]["BIO_CODE"]);
             samplecodes.push(g[i]["SAMPLE_CODE"]);
             taskmxids.push(g[i]["TASKMXID"]);
             libtypemxs.push(g[i]["LIBRARY_TYPE_EN"]);
             if(taskids.indexOf(g[i]["TASK_LS_ID"])<0){
                 taskids.push(g[i]["TASK_LS_ID"]);//主单ID
             }
             if(libtypes.indexOf(g[i]["LIBRARY_TYPE_EN"])<0){
                  libtypes.push(g[i]["LIBRARY_TYPE_EN"]);//文库类型
              }
             if(i==0){
                 a=g[i]["LIBRARY_FLOW"];
                 b=g[i]["LIBRARY_FLOW"];
             }else{
                 a=g[i-1]["LIBRARY_FLOW"];
                 b=g[i]["LIBRARY_FLOW"];
             }
             
             if(a!=b){
                 alertMsg("存在所选记录建库流向“<font color=#ff0000>"+a+"--"+b+"</font>”前后不一致!");
                 return;
             }
         }
         openWindow(winOpts,{"IDS":ids,"TASKIDS":taskids,"TASKMXIDS":taskmxids,"SAMPLECODES":samplecodes,"BIOCODES":biocodes,"LIBTYPES":libtypes,"LIBTYPEMXS":libtypemxs,"EX_TYPE":a});	
      }
     //追加任务 
     var addToEx=function(){
 
         var g=getGridSelectData(gridNameDGrid);
         if(g.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
         }
         var ids=[];
         var taskids=[];
         for(var i=0;i<g.length;i++){
            
             ids.push(
                 g[i]["ID"]
                 );
             taskids.push(
                 g[i]["TASKMXID"]
                 );
         }
           var winOpts={
               url:"biomarker/dispatch/pd/libont/addtoex/addtoex",
               title:"任务追加.."
           };
          //判断类型是否全部分同一为,并取出形成类型单
           var g=getGridSelectData(gridNameDGrid);
           var a="";
           var b="";
           var taskids=[];
           var biocodes=[];
           var samplecodes=[];
           var libtypes=[];
           var taskmxids=[];
 
           for(var i=0;i<g.length;i++){
               biocodes.push(g[i]["BIO_CODE"]);
               samplecodes.push(g[i]["SAMPLE_CODE"]);
               taskmxids.push(g[i]["TASKMXID"]);
               if(taskids.indexOf(g[i]["TASK_LS_ID"])<0){
                   taskids.push(g[i]["TASK_LS_ID"]);//主单ID
               }
 
               if(i==0){
                   a=g[i]["LIBRARY_FLOW"];
                   b=g[i]["LIBRARY_FLOW"];
               }else{
                   a=g[i-1]["LIBRARY_FLOW"];
                   b=g[i]["LIBRARY_FLOW"];
               }
               
               if(a!=b){
                   alertMsg("存在所选记录建库流向“<font color=#ff0000>"+a+"--"+b+"</font>”前后不一致!");
                   return;
               }
           }
           openWindow(winOpts,{"IDS":ids,"TASKIDS":taskids,"TASKMXIDS":taskmxids,"SAMPLECODES":samplecodes,"BIOCODES":biocodes,"EX_TYPE":a});	
        
     }
     //文编号
     var doGenNo=function(){
         var g=[];
         for(var i=0;i<gridNameS1.length;i++){
             var arrSubID=getGridSelectData(gridNameS1[i]);
             if(arrSubID.length!=0){
                 g=g.concat(arrSubID);
             }
         }
         if(g.length==0){
              alertMsg("请至少选择一条样本记录进行操作!");
              return;
          }
         
       //取对应表
       var params={"query":"queryBioLibTypeList","objects":[]};
       var iniFist="";
         $.fn.ajaxPost({
              ajaxUrl:"system/jdbc/query/one/table",
              ajaxType: "post",
              ajaxData: params,
              succeed:function(result){
                  if(result["code"]>0){
                  var libtypename=[];
                   var initials=[];
                    var rows=result["rows"];
                    for(var i=0;i<rows.length;i++){
                        var row=rows[i];
                        libtypename.push(row["LIB_TYPE_NAME"]);
                        initials.push(row["INITIALS"]);
                    }
                    var objectup=[];
                    for(var i=0;i < g.length;i++){
                            //更新记录
                        iniFist=checkInitals(g[i]["LIBRARY_TYPE_EN"],libtypename,initials);
                            objectup.push({
                                "ID":g[i]["ID"],//关联更新ID
                                "LIBRARY_CODE":g[i]["PROJECT_SUBNO"]+iniFist
                                +getNo(g[i]["DON"],i+1)+"-"+getBioCodeNo(g[i]["DON2"]+1)
                            });
                    }
                   var urlsend="system/jdbc/save/batch/table";
                   var paramsup={"tableName":"BIO_LIB_INFO","objects":objectup};
                       putAddOrUpdata(urlsend,paramsup,"是","提交");    	        	
                    
                  }
              }
         });
      
     }
     //比对取对照
     var checkInitals=function(name,names,initals){
         for(var i=0;i<names.length;i++){
             if(name==names[i]){
                 return initals[i];
             }
         }
         return "";
     }
     //文库流程号段(项目期号内)
     var getNo=function(num,ki){
         num=num+ki;
         if(num<10){
             num="000"+num;
             return num;
         }
         if(num>=10 && num<100){
             num="00"+num;
             return num;
         }
         if(num>=100 && num<1000){
             num="0"+num;
             return num;
         }
         return num;
     }
     //核酸号段(项目期号内)
     var getBioCodeNo=function(num){
         if(num<10){
             num="0"+num;
             return num;
         }
         return num;
     }
     //提交
     var doOK=function(){
           var arrIds=getSelectData(gridNameD1Grid);
           if(arrIds.length==0){
                  alertMsg("请至少选择一条记录进行操作!");
                  return;
            }



			var sample;
			$.fn.ajaxPost({
				ajaxUrl: "system/jdbc/query/one/table",
				ajaxType: "post",
				ajaxAsync: false,
				ajaxData: { "query": "lib_pd_ont_ist_ok", "objects": arrIds },
				succeed: function (rs) {
					sample = rs.rows;
				}
			});
			for (var i = 0; i < sample.length; i++) {
				if (sample[i]["LIBRARY_JHDATA"] == null || sample[i]["LIBRARY_JHDATA"] == "") {
					alertMsg("子文库编号【" + sample[i]["LIBRARY_CODE"] + "】安排数据量为空!");
					return;
				}
			}





           var objectup=[];
           for(var i=0;i<arrIds.length;i++){
                     var time=sysNowTimeFuncParams["sysNowTime"];
                     var username=getLimsUser()["name"];
                     objectup.push({
                           "ID":arrIds[i],//联联任务ID
                            "EX_RE_STATUS":"待接收"
                    });
               }
          var urlsend="system/jdbc/save/batch/table";
            var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
            putAddOrUpdata(urlsend,paramsup,"是","提交");
     }
     //撤回
     var doReturn=function(){
         var g=getGridSelectData(gridNameD2Grid);
         if(g.length==0){
                alertMsg("请至少选择一条记录进行操作!");
                return;
          }
         var objectup=[];
         for(var i=0;i<g.length;i++){
                  if(g[i]["EX_RE_STATUS"]!="待接收"){  
                      alertMsg("操作失败,所选记录存在已“已接收”状态!");
                      return;
                  }else{
                      objectup.push({
                           "ID":g[i]["ID"],
                            "EX_RE_STATUS":"待处理"
                    });
                  }
         }
        var urlsend="system/jdbc/save/batch/table";
        var paramsup={"tableName":"EXE_TQQC_SHEET","objects":objectup};
         putAddOrUpdata(urlsend,paramsup,"是","提交");
     }
     //移至待排单
     var doReturn2=function(){
         var g=getGridSelectData(gridNameD3Grid);
         if(g.length==0){
                alertMsg("请至少选择一条记录进行操作!");
                return;
          }
         var objectup=[];
         for(var i=0;i<g.length;i++){
                  if(g[i]["JK_TASKMX_STATUS"]=="暂停"||g[i]["JK_TASKMX_STATUS"]=="未建库"){  
                     objectup.push({
                           "ID":g[i]["ID"],
                           "JK_TASKMX_STATUS":"草稿"
                     });
                  }else{
                      alertMsg("操作失败,只有“<font color=#ff0000>暂停、未建库</font>”状态方允许操作!");
                      return;
                  }
         }
        var urlsend="system/jdbc/save/batch/table";
        var paramsup={"tableName":"BIO_LIB_INFO","objects":objectup};
         putAddOrUpdata(urlsend,paramsup,"是","提交");
     }
   //样本状态修改
     var upsmStatus=function(){
          var arrIds=getSelectData(gridNameDGrid);
          if(arrIds.length==0){
              alertMsg("请至少选择一条样本记录进行操作!");
              return;
          }
         var winOpts={
             url:"biomarker/dispatch/pd/libont/upsmstatus/upsmstatus",
             title:"修改建库状态.."
         };
          openWindow(winOpts,{"IDS":arrIds});
     }
     //记录移除
     var remove=function(){
         var arrg=[];
         var arrIds=[];
         for(var i=0;i<gridNameS1.length;i++){
             var arrSubID=getGridSelectData(gridNameS1[i]);
             arrg=arrg.concat(arrSubID);
             
         }
         if(arrg.length==0){
             alertMsg("请至少选择一条数据进行操作!");
             return ;
         }
         for(var i=0;i < arrg.length;i++){
             arrIds.push(arrg[i]["ID"]);
         }
         confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function() {
            var params={"tableName":"BIO_LIB_INFO","ids":arrIds};
            var url="system/jdbc/delete/batch/table";
            deleteGridDataByIds(url,params,refreshGrid);
         });
      }
      //修改安排数据量
      var doUpdate=function(){
 
           var arrIds=[];
           for(var i=0;i<gridNameS1.length;i++){
                 var arrSubID=getSelectData(gridNameS1[i]);
                 if(arrSubID.length!=0){
                   arrIds=arrIds.concat(arrSubID);
                 }
           }
           if(arrIds.length!=1){
               alertMsg("请至少选择一条样本记录进行操作!");
               return;
           }
          var winOpts={
              url:"biomarker/dispatch/pd/libont/doUpdate/doUpdate",
              title:"修改安排数据量.."
          };
         openWindow(winOpts,{"ID":arrIds[0]});
               // openWindow(winOpts,{"IDS":ids,"ID":ids[0]});
      }
    //删除执行单
    var doDelete=function(){
        var arrIds=getSelectData(gridNameD1Grid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行操作!");
            return ;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function() {
           var url="system/jdbc/delete/one/table/where";
           var params1= {"tableName":"BIO_LIB_INFO","where":{"EXE_TQQC_ID":arrIds}};
           deleteGridDataByIds(url,params1,refreshGrid);
           var params2= {"tableName":"EXE_TQQC_SHEET","where":{"ID":arrIds}};
           deleteGridDataByIds(url,params2,refreshGrid);
        });
     }
    
    //批量执行插入
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
                    if(isDoCallBack=="是"){
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                }else{
                    alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }
    
      
      var callBack=function(){
         refreshGrid();
      };
 
      var refreshGrid=function(){
           gridNameS1=[];
           gridNameS2=[];
           gridNameS3=[];
         if(gridNameDGrid){
             gridNameDGrid.dataSource.read();
         }
         if(gridNameD1Grid){
             gridNameD1Grid.dataSource.read();
         }
         if(gridNameD2Grid){
             gridNameD2Grid.dataSource.read();
         }
         if(gridNameD3Grid){
             gridNameD3Grid.dataSource.read();
         }
      }
 
      funcPushs(pathValue,{
          "initData":initData,
          "init":init,
          "edit":edit,
          "upsmStatus":upsmStatus,
          "addToEx":addToEx,
          "doOK":doOK,
          "doGenNo":doGenNo,
          "remove":remove,
          "doDelete":doDelete,
          "doReturn":doReturn,
          "doReturn2":doReturn2,
          "refreshGrid":refreshGrid,
          "callBack":callBack,
      "doUpdate":doUpdate,
      });
 });