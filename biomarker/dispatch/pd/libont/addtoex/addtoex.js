$(document).ready(function() {
   var pathValue="biomarker-dispatch-pd-libont-addtoex-addtoex";
   var paramsValue;
   var initData=function(){
       return {};
   }
   var gridNameGrid;
   var init=function(params){
	   paramsValue=params;
	   var toolbar=getButtonTemplates(pathValue,[
           {name:"edit",target:"add",title:"确认选择"},
       ]);
      var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: toolbar,
           read:{"query":"lib_pd_SHEET_list",
        	   "objects":[["三代ONT基因组建库-建库"],
        		   ["待处理","调度退回"]],
        		   "search":{"EX_LB":["建库"]}},
           headerFilter:function(cols,i){},
           detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
           detailInit: function (e) {
           	var ROW_ID = e.data.ID;
               var subGrid_N_JSON={
                   url: "system/jdbc/query/one/table",
                   sort: "",
                   toolbar: null,
                   height: 320,
                   read:{"query":"lib_pd_ont_ist_ok","objects":[ROW_ID]},
               };
               var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
           }
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
  }
  
   var add=function(){
       var arrIds=getSelectData(gridNameGrid);
       if(arrIds.length==0){
          	alertMsg("请至少选择一条记录进行操作!");
          	return;
        }
       var codes=paramsValue["BIOCODES"]; 
	     var params={"query":"doCheckExLibInfo","objects":[arrIds,codes]};
		   $.fn.ajaxPost({
		        ajaxUrl:"system/jdbc/query/one/table",
		        ajaxType: "post",
		        ajaxData: params,
		        succeed:function(result){
		        	if(result["code"]>0){
		        		var rows=result["rows"];
		        		var s="";
		        		for(var i=0;i<rows.length;i++){
		  	        		var row=rows[i];
		  	        		if(i==0){
		  	        			s=row["BIO_CODE"];
		  	        		}else{
		  	        			s+=","+row["BIO_CODE"];
		  	        		}
		        		}
		  	        	if(s!=""){
		  	        		 alertMsg("提示:操作失败!存在样品编号(“"+s+"”)重复!");
		  	        	}else{
		  	        	  var objectadd=[];
		  	        	  var objectuplib=[];
		  	        	  var objectup=[];
		  	       	  	   var time=sysNowTimeFuncParams["sysNowTime"];
		  	       	  	   var username=getLimsUser()["name"];
	                    	var ids=paramsValue["IDS"];//切胶文库ID
	                    	var taskids=paramsValue["TASKIDS"];
	                    	var taskmxids=paramsValue["TASKMXIDS"];
	                    	var biocodes=paramsValue["BIOCODES"];
	                        var samplecodes=paramsValue["SAMPLECODES"];
		  	              for(var i=0;i<arrIds.length;i++){
			  	       	  	   for(var j=0;j < ids.length;j++){
			  	       	       		objectadd.push({
			  	       	       	     "FROM_ID":ids[i],
			  	                     "EXE_TQQC_ID":arrIds[i],//关联执行单
			  	                     "EXE_TQQC_ID":arrIds[i],//关联执行单
			  	       	       		 "TASK_LIB_MX_ID":taskmxids[j],//联联任务ID
			  	       	       	     "JK_TASKMX_STATUS":"已排单",
		           	    	       	 "BIO_CODE":biocodes[j],//枋酸编号
		           	    	       	 "SAMPLE_CODE":samplecodes[j],//样品编号
			  	                     "SEQ_PLAT":"ONT",//测序平台
			  	                     "BIO_CODE":codes[j],
			  	                     "SYS_MAN":username,//实验员
			  	                     "SYS_INSERTTIME":time//开始日期
			  	       		    	});
	                         		objectuplib.push({
	              	    	       		"ID":ids[j],
	              	    	       		"JK_TASKMX_STATUS":"待建库"
	              	    	       	});
			  	       	        }
		  	              	}
		  	              for(var i=0;i < taskids.length;i++){
                         	 objectup.push({
            	    	       		"ID":taskids[i],
            	    	       		"TASK_LS_STATUS":"建库中"
            	    	       	});
                          }
		  	            var urlsend="system/jdbc/save/batch/table";
         	        	var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
         	        	putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");
         	        	var paramslibup={"tableName":"BIO_LIB_INFO","objects":objectuplib};
         	        	 putAddOrUpdata(urlsend,paramslibup,"否","更新");
         	        	 var paramsup={"tableName":"BIO_TASK_LIB","objects":objectup};
	         	      	 putAddOrUpdata(urlsend,paramsup,"是","");
		  	          	    
		  	        	}
		        	}
		        }
		   });
    } 
    
   //批量执行插入
   var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
       $.fn.ajaxPost({
           ajaxType:"post",
           ajaxUrl:urls,
           ajaxData:inobjjson,
           succeed:function(result){
               if(result["code"]>0){
              	 if(isDoCallBack=="是"){
              		 alertMsg("提示:操作成功!");
              		funcExce(pathValue+"pageCallBack");
              		 funcExce(pathValue+"close");
              	 }
               }else{
              	 alertMsg(errMsg+"操作失败!");
               }
           }
       });
   }
  
   
     var callBack=function(){
        refreshGrid();
     };

     var refreshGrid=function(){
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
     }

     funcPushs(pathValue,{
         "initData":initData,
         "init":init,
         "add":add,
         "refreshGrid":refreshGrid,
         "callBack":callBack,//回调方法
     });
});