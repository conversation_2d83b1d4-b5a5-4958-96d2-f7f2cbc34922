$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-libmcd2-pddu-pddu";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "EXE_TQQC_SHEET"
        };
    }
    var init = function (params) {
        debugger;

        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
        $("#EX_TYPE" + pathValue).val(paramsValue["EX_TYPE"]);
        $("#EX_MX_NUMBER" + pathValue).val(paramsValue["IDS"].length);
        $("#EX_LIB_TYPE" + pathValue).val("MCD");
        $("#NO_CHAR" + pathValue).val("JM");
    }


    var subUpData = function () {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        /**
   * 平台很重要,后台上机排单需要分流
   */
        var SEQ_PLAT = "NGS";

        //插入执行主单
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    alertMsg("提交成功,生成的草稿,请前往审核提交!", "success", function () {
                        var ids = paramsValue["IDS"];
                        var taskids = paramsValue["TASKIDS"];
                        var objectadd = [];
                        var objectup = [];
                        var objectupmx = [];
                        var adddatas = paramsValue["ADD_DATAS"];
                        var biocodes = paramsValue["BIOCODES"];
                        var libtypcodes = paramsValue["LIBTYPCODES"];
                        var samplecodes = paramsValue["SAMPLECODES"];
                        for (var i = 0; i < ids.length; i++) {
                            objectadd.push({
                                "ADD_DATA": adddatas[i],//加测数据量
                                "TASK_LIB_MX_ID": ids[i],//联联任务明细ID
                                "EXE_TQQC_ID": result["ID"],//关联执行单
                                "LIBRARY_TYPE": "MCD",//文库类型
                                "SEQ_PLAT": SEQ_PLAT,//测序平台
                                "BIO_CODE": biocodes[i],//枋酸编号
                                "LIBRARY_CODE": libtypcodes[i],//文库编号
                                "JK_TASKMX_STDATE": time,
                                "SAMPLE_CODE": samplecodes[i],//样品编号
                                "JK_TASKMX_MAN": $("#EX_MAN" + pathValue).val(),
                                "SYS_MAN": username,//实验员
                                "SYS_INSERTTIME": time//开始日期
                            });
                            objectupmx.push({
                                "ID": ids[i],
                                "LIBRARY_CODE": libtypcodes[i],//文库编号
                            })
                        }
                        for (var i = 0; i < taskids.length; i++) {
                            objectup.push({
                                "ID": taskids[i],
                                "TASK_LS_STATUS": "建库中"
                            });
                        }
                        //执行添加到文库
                        var urlsend = "system/jdbc/save/batch/table";
                        var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectadd };
                        putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

                        var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objectupmx };
                        putAddOrUpdata(urlsend, paramsup, "是", "提取中");

                        var paramsup = { "tableName": "BIO_TASK_LIB", "objects": objectup };
                        putAddOrUpdata(urlsend, paramsup, "是", "提取中");

                    });
                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });

    }
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var submit = function () {
        subUpData();
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
        "subUpData": subUpData
    });

});