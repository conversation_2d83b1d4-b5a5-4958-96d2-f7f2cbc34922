$(document).ready(function() {

    var pathValue="biomarker-dispatch-pd-intelligence-list-list";

        var paramsValue;
        var gridNameGrid;
        var gridData;
        var gridNameS2;
        var ids;
        var keys;
        var new_sheer="";

    var init=function(params){
debugger;
        ids = params["ids"];
         keys=params.keys;
        paramsValue=params;


       var gridNameGridJson={
           url: "system/jdbc/query/one/table",
           sort: "",
           toolbar: "",
           read: {"query": "query_BIO_TASK_LIB_TEST_list", "objects": [],"search": {"ID":ids}},
            fetch:function(data){
        		   gridNameS2=data;
        		}

       };
    
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }

    var clickButton=function(html,id,obj){
debugger;

        var porm=$("#PE_OR_MAN"+pathValue).val();   //排单对象
        var prb=$("#PE_RULE_B"+pathValue).val();   //PE每板最大
        var prm=$("#PE_RULE_M"+pathValue).val();   //PE每板最小
        var mrb=$("#MAN_RULE_B"+pathValue).val();   //人每板最大
        var mrm=$("#MAN_RULE_M"+pathValue).val();   //人每板最小
        var tvo=$("#T_V_ORDER"+pathValue).val();   //板孔排列顺序


                                var objectSheet=[];       //执行单
                               	var objectadd=[];           //文库信息
                          	 var objectupmx=[];        //明细

////////////////////////////////////////
  switch(new Date().getDay()) {
    case 0:
        day = "D_SUNDAY";
        break;
     case 1:
        day = "D_MONDAY";
         break;
    case 2:
        day = "D_TUESDAY";
         break;
    case 3:
        day = "D_WEDNESDAY";
         break;
    case 4:
        day = "D_THURSDAY";
         break;
    case 5:
        day = "D_FRIDAY";
         break;
    case 6:
        day = "D_SATURDAY";
           } 

for(var k=0;k<keys.length;k++){
                    var keyVal=paramsValue[ keys[k] ];
                                var lfo = keyVal["lfo"];
                                var met =keyVal["met"];
                                var wor=keyVal["wor"];
                                var subids=keyVal["ids"];
                  var rows;
                  $.fn.ajaxPost({
                     ajaxUrl: "system/jdbc/query/one/table",
                     ajaxType: "post",
                      ajaxAsync: false,
                      ajaxData: {"query": "query_BIO_ATTENDANCE_MMF_list", "objects": [],"search": {"PROCEDURE":lfo,"WORKBY":wor,"METHOD":met,day :"是"}},
                      succeed: function (rs) {
                                   rows = rs.rows;             //人
        		       }
                              });

                        var typelb=lfo;
                        var numstr="";                                                          //执行单号前缀
                       if(typelb=="二代常规DNA建库") numstr="JD";
                       if(typelb=="二代常规RNA建库") numstr="JR";
                       if(typelb=="三代ONT基因组建库") numstr="JN";
                       if(typelb=="代谢建库") numstr="JX";

                      var SEQ_PLAT="";                                                           //测序平台，分流          
	     if(typelb=="二代常规DNA建库"||
	    		typelb=="二代常规RNA建库"||
	    		typelb=="HIC建库"||
	    		typelb=="ATAC建库") SEQ_PLAT="NGS";
	     if(typelb=="三代PB基因组建库") SEQ_PLAT="PB";
	     if(typelb=="三代ONT基因组建库") SEQ_PLAT="ONT";
	     if(typelb=="代谢建库") SEQ_PLAT="代谢";  

                  var   sample;
                  $.fn.ajaxPost({
                     ajaxUrl: "system/jdbc/query/one/table",
                     ajaxType: "post",
                      ajaxAsync: false,
                      ajaxData: {"query": "query_BIO_TASK_LIB_TEST_list", "objects": [],"search": {"ID":subids}},
                      succeed: function (rs) {
                                      sample = rs.rows;             //样品
        		       }
                              });
                     

                        for(var i=0;i<rows.length;i++){          //循环人

                        var time= rows[i]["F_ALLOCATION_TIME"];    //人员分配日期
                        var use= rows[i]["F_USED_FLUX"];                  //人员已用通量
                        var man= rows[i]["F_MAN_FLUX"];                 //人员标准通量
                        var noe= rows[i]["F_MAN_OR_MACHINE"];   //人或机
                         var  num=0;
                         var  libTypes="";//文库类型
                           if(time == null)
                          {     
    
                             use=0;
                           }else{
                                var date =new Date();
                                time =new Date(time );
                                if(time.getFullYear()==date .getFullYear()&&time.getMonth()==date .getMonth()&&time.getDate()==date .getDate()){
                               }else{   use=0;}
                           }
                      
                          if(use ==null){
                                   use=0;
                             }
                            time=sysNowTimeFuncParams["sysNowTime"];

                         if((man-use)>0){
                         var z=getRandomId();   //执行单id
                        
                              var username=getLimsUser()["name"];
                         $.fn.ajaxPost({
                         ajaxUrl: "system/settings/code/auto",
                          ajaxType: "post",
                         ajaxAsync: false,
                         ajaxData: {"limit": "无限制", "type": numstr,"format": "Šτ${NO_CHAR}|-|#yyyyMMdd#|-①","length": "2", "NO_CHAR": numstr},
                          succeed: function (rs) {
                                        no= rs.info.numberCode;       //执行单号
                 		            }
                          });


                        if( noe == "机" && sample.length>=prm&& (porm=="PE/实验员混排(PE优先)"||porm=="PE")&&(man-use)>=prm&&sample.length>0 ){
                                var jmax=sample.length;

                                if(jmax>prb){ 
                                      jmax=prb;
                                }
                               if(jmax>(man-use)){
                                          jmax=man-use;
                              }

                               for(var j=0;j<jmax;j++){         //循环样品

                        
                                      if(libTypes.length==0){
                                         libTypes=sample[j]["LIBRARY_TYPE_EN"];
                                     }else{ 
                                             if(libTypes.indexOf(sample[j]["LIBRARY_TYPE_EN"])<0){
                                                  libTypes=libTypes+";"+ sample[j]["LIBRARY_TYPE_EN"];
                                                 }
                                       }
                              
                                
        
                           var m=getWell(num,tvo);              //孔位号
                          if(num<9){var n =""; n ="00"+(num+1)} else{ var n =""; n ="0"+(num+1)}  //孔位ID(顺序)

                                         objectadd.push({
               	    	       		"TASK_LIB_MX_ID":sample[j]["ID"],                           //关联任务单明细ID
               	    	       		"BIO_CODE":sample[j]["BIO_CODE"],                        //枋酸编号
               	    	       		"LIBRARY_TYPE":sample[j]["LIBRARY_FLOW"],          //文库类型
               	    	       		"LIBRARY_TYPE_EN":sample[j]["LIBRARY_FLOW"],     //文库类型
               	    	       		"SAMPLE_CODE":sample[j]["SAMPLE_CODE"],          //样品编号
               	    	       		"EXE_TQQC_ID":z,                                                    //关联执行单
               	    	       		"SEQ_PLAT":SEQ_PLAT,                                           //测序平台
               	    	       	                "JK_TASKMX_MAN":rows[i]["F_MAN"],                   //建库实验员
                                                               "JK_TASKMX_STDATE":time,
               	    	       		"SYS_MAN":username,                                           //实验员
               	    	       		"PLATE_CODE":"0000123",                                      //板号
               	    	       		"PLATE_WELL":m,                                                    //孔位号
               	    	       		"PLATE_WELL_NO":n,                                            //孔位ID(顺序)
               	    	       		"SYS_INSERTTIME":time                                         //开始日期
               	    	       	});
                               objectupmx.push({"ID":sample[j]["ID"],"RL_STATUS":"已排"});

                                sample.splice(j, 1);
                                j=j-1;
                                jmax=jmax-1;

                              use=use+1;
                              num=num+1;

                                    }
		               objectSheet.push({
            	         	    	       "ID":z,//id
              	       	 	          	 "EX_DH_NO":no,                           //执行单单号
              	  	         	        "EX_TIME":time,                            //派单时间
              	     	        	         "EX_TYPE":lfo,                               //执行单类型
              	     	        	        "EX_MAN":rows[i]["F_MAN"],      //实验员
              	     	        	        "EX_B_TIME":time,                      //建库开始时间
              	     	        	        "EX_E_TIME":time,                       //建库结束时间
              	     	        	        "EX_LIB_TYPE":libTypes,                     //文库类型
              	    	 	                 "EX_RE_STATUS":"预处理",       //状态
              	    	 	                 "EX_ARRANGE_MODE":"自动排",       //排单方式
              	    	 	                 "EX_EXECUTE_MODE":"PE",       //执行方式
              	    	 	                "EX_LIBRARY_MECHOD":met   //建库方法
              	    	           });                                
                        }else{
                              if(noe != "机" && sample.length>=mrm&& (porm=="PE/实验员混排(PE优先)"||porm=="实验员")&&(man-use)>=mrm&&sample.length>0 ){

                               
                                var jmax=sample.length;
                                if(jmax>mrb){ 
                                      jmax=mrb;
                                }
                               if(jmax>(man-use)){
                                          jmax=man-use;
                              }
                           for(var j=0;j<jmax;j++){         //循环样品
    

                                      if(libTypes.length==0){
                                         libTypes=sample[j]["LIBRARY_TYPE_EN"];
                                     }else{ 
                                             if(libTypes.indexOf(sample[j]["LIBRARY_TYPE_EN"])<0){
                                                  libTypes=libTypes+";"+ sample[j]["LIBRARY_TYPE_EN"];
                                                 }
                                       }
                              
                           var m=getWell(num,tvo);              //孔位号
                          if(num<9){var n =""; n ="00"+(num+1)} else{ var n =""; n ="0"+(num+1)}  //孔位ID(顺序)

                                   objectadd.push({
               	    	       		"TASK_LIB_MX_ID":sample[j]["ID"],                               //关联任务单明细ID
               	    	       		"BIO_CODE":sample[j]["BIO_CODE"],                            //枋酸编号
               	    	       		"LIBRARY_TYPE":sample[j]["LIBRARY_FLOW"],             //文库类型
               	    	       		"LIBRARY_TYPE_EN":sample[j]["LIBRARY_FLOW"],       //文库类型
               	    	       		"SAMPLE_CODE":sample[j]["SAMPLE_CODE"],             //样品编号
               	    	       		"EXE_TQQC_ID":z,                                                       //关联执行单
               	    	       		"SEQ_PLAT":SEQ_PLAT,                                              //测序平台
               	    	       	          "JK_TASKMX_MAN":rows[i]["F_MAN"],                      //建库实验员
                                           "JK_TASKMX_STDATE":time,
               	    	       		"SYS_MAN":username,                                                //实验员
               	    	       		"PLATE_CODE":"0000123",                                      //板号
               	    	       		"PLATE_WELL":m,                                                    //孔位号
               	    	       		"PLATE_WELL_NO":n,                                            //孔位ID(顺序)
               	    	       		"SYS_INSERTTIME":time                                              //开始日期
               	    	       	  });
                                           objectupmx.push({"ID":sample[j]["ID"],"RL_STATUS":"已排"});

                                            sample.splice(j, 1);
                                            j=j-1;
                                            jmax=jmax-1;

                                           use=use+1;
                                         num=num+1;

                                      }
		               objectSheet.push({
            	         	    	       "ID":z,//id
              	       	 	          	 "EX_DH_NO":no,                           //执行单单号
              	  	         	        "EX_TIME":time,                            //派单时间
              	     	        	         "EX_TYPE":lfo,                               //执行单类型
              	     	        	        "EX_MAN":rows[i]["F_MAN"],      //实验员
              	     	        	        "EX_B_TIME":time,                      //建库开始时间
              	     	        	        "EX_E_TIME":time,                       //建库结束时间
              	     	        	        "EX_LIB_TYPE":libTypes,                     //文库类型
              	    	 	                 "EX_RE_STATUS":"预处理",       //状态
              	    	 	                 "EX_ARRANGE_MODE":"自动排",       //排单方式
              	    	 	                 "EX_EXECUTE_MODE":"实验员",       //执行方式
              	    	 	                "EX_LIBRARY_MECHOD":met   //建库方法
              	    	           });       
                               }
                             }
                                  if(objectadd.length>0){
                                  //执行添加到文库
                                 var newUrl="system/jdbc/save/one/table/objects";
                                var paramsadd1={"tableName":"EXE_TQQC_SHEET_TEST","objects":objectSheet};
         	        	 putAddOrUpdata(newUrl,paramsadd1,"否","添加执行单");

                                        if(new_sheer.length==0){new_sheer=objectSheet[0]["EX_DH_NO"]; }else{   new_sheer=new_sheer+"; "+objectSheet[0]["EX_DH_NO"];}

                                
                                var objectuse=[];
                                objectuse.push({"ID":rows[i]["ID"],"F_USED_FLUX":use,"F_ALLOCATION_TIME":time});

                                 var urlsend="system/jdbc/save/batch/table";
         	        	 var paramsadd={"tableName":"BIO_MMF","objects":objectuse};
         	        	 putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");

                                var urlsend="system/jdbc/save/batch/table";
          	        	 var paramsadd={"tableName":"BIO_LIB_INFO_TEST","objects":objectadd};
          	        	 putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");



          	        	 var paramsupmx={"tableName":"BIO_TASK_LIBMX_TEST","objects":objectupmx};
 	         	      	 putAddOrUpdata(urlsend,paramsupmx,"否","建库中");
                                    
                                 var objectSheet=[];
                               	var objectadd=[];
                          	 var objectupmx=[];
                                  }

                        }
                 
               }

            }
////////////////////////////////////////

      $("#NEW_SHEET"+pathValue).val(new_sheer);
            if(new_sheer.length>0){     
             alertMsg("提示:操作成功!");
            refreshGrid();
            }else{
             alertMsg("提示:样本不能满足人或机器排单要求!");
              }


    }


var getWell=function(data,tvo){

   if(tvo=="竖排"){
                 var k=Math.floor(data/8)+1;
                 var l=data%8;
                                            switch(l) {
                                                                case 0:   z="A" ;        break  ;    
                                                                case 1:   z="B"  ;       break  ;    
                                                                case 2:   z="C";         break  ;    
                                                                case 3:   z="D";         break  ;    
                                                                case 4:   z="E"   ;      break  ;    
                                                                case 5:   z="F"   ;      break  ;    
                                                                case 6:   z="G" ;        break  ;    
                                                                case 7:   z="H" ;        break ;   

                                                             }
                 if(k<10){var x=z+"0"+k;}else{ var x=z+k;}

                       return   x;
              }else{
                             var k=Math.floor(data/12);
                              var l=data%12
                                            switch(k) {
                                                                case 0:   z="A" ;        break  ;    
                                                                case 1:   z="B"  ;       break  ;    
                                                                case 2:   z="C";         break  ;    
                                                                case 3:   z="D";         break  ;    
                                                                case 4:   z="E"   ;      break  ;    
                                                                case 5:   z="F"   ;      break  ;    
                                                                case 6:   z="G" ;        break  ;    
                                                                case 7:   z="H" ;        break ;   

                                                             }
               if(k<10){var x=z+"0"+l;}else{ var x=z+l;}

                return       x;
                 }
}

  //批量执行插入
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		 alertMsg("提示:操作成功!");
                	refreshGrid();
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
function getRandomId() {
	 return 'FDSX-LANE-' + new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
};

var close1=function(){
	funcExce(pathValue + "pageCallBack"); //执行回调
	funcExce(pathValue + "close"); //关闭页面
    }

     var refreshGrid=function(){
    
        if(gridNameGrid){
            gridNameGrid.dataSource.read();//重新读取--刷新
        }

     }

	funcPushs(pathValue,{
	         "refreshGrid":refreshGrid,
		"init":init,
		"close1":close1,
                  "clickButton":clickButton

	});
 
 });