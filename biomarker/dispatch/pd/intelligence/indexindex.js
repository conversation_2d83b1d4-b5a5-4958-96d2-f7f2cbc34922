$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-intelligence-index";
    var initData = function () {
        return {};
    }
    var gridNameDGrid;　

    var init = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "editA", title: "下1载" }
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: { "query": "query_BIO_ATTENDANCE_MMF_list", "objects": [] },
            fetch: function (data) {
                gridNameS = data;
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);//初始化表格的方法

    }

    //排单
    var editA = function () {
        debugger
      
        $.fn.ajaxPost({
            ajaxUrl: "system/settings/code/auto",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData:   {"limit":"无限制","type":"合并样本编号","format":"S|#yyyyMMdd#||①","length":"2"},
            succeed: function (rs) {
                no = rs.info.numberCode;       //执行单号
            }
        });





    }
    funcPushs(pathValue, {
        "initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
        "init": init,//初始化方法-在加载完初始化数据之后执行
        "editA": editA　
    });
});