$(document).ready(function() {

    var pathValue="biomarker-dispatch-pd-intelligence-popup-popup";


	var paramsValue;
	var gridNameGrid;
    var gridNameS2;


    var init=function(params){
	    paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"edit",title:"确认选择"},
        ]);
       var gridNameGridJson={
           url: "berry/automation/sop/queryic",
           sort: "",
           toolbar: toolbar,
           read: {"cInvCode":"BMK-R-0074"}
       };
       gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
    }

      //批量执行插入
  var edit = function ( ) {
      debugger;
    var gridData=getGridSelectData(gridNameGrid);
  };


	funcPushs(pathValue,{
		"init":init,
        "edit":edit,

	});
 
 });