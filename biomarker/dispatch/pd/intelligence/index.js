$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-intelligence-index";

    /**
     * 初始化数据-无参
     */
    var initData = function () {
        return {};
    }

    var gridNameGrid;
    var gridName1Grid;
    var gridName2Grid;

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit1", title: "审核" },
            //   { name: "import", target: "importData", title: "导入" },
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: {
                "query": "query_BIO_ATTENDANCE_MMF_list", "objects": [["运营待审核", "独立核算退回"]],
                "search": { "EXPERIMENTAL_TYPE": ["正常"], "DLJS_TASK_STATUS": ["运营待审核", "独立核算退回"] }
            },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "点击弹出修改的列是什么") {
                        setJsonParam(cols[i], "template", getTemplate("#= 点击弹出修改的列是什么 #", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);//初始化表格的方法
     //   init1();
     //   init2();
    }
    var init1 = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit2", title: "提交" },

        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: {
                "query": "query_Independent_settlement_view4", "objects": [["运营已审核", "运营终止"]],
                "search": { "DLJS_TASK_STATUS": ["运营已审核", "运营终止"] }
            },
        };
        gridName1Grid = initKendoGrid("#gridName1Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    var init2 = function (params) {
        /**
         * 列表-按钮-定义
         */
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit2", title: "审核" },
            //   { name: "import", target: "importData", title: "导入" },
        ]);//工具条
        //请求参数
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            read: {
                "query": "query_Independent_settlement_view4", "objects": [["运营待审核", "独立核算退回"]],
                "search": { "EXPERIMENTAL_TYPE": ["实验验证", "实验赔偿"], "DLJS_TASK_STATUS": ["运营待审核", "独立核算退回"] }
            },
            headerFilter: function (cols, i) {
                if (i) {
                    if (cols[i]["field"] && cols[i]["field"] == "点击弹出修改的列是什么") {
                        setJsonParam(cols[i], "template", getTemplate("#= 点击弹出修改的列是什么 #", "funcExce(\'" + pathValue + "open\',\'#= ID #\');", "txt"));
                    }
                }
            }
        };
        gridName2Grid = initKendoGrid("#gridName2Grid" + pathValue, gridNameGridJson);//初始化表格的方法

    }





    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        if (gridNameGrid) {
            gridNameGrid.dataSource.read();//重新读取--刷新
        }
        if (gridName1Grid) {
            gridName1Grid.dataSource.read(); //重新读取--刷新
        }
        if (gridName2Grid) {
            gridName2Grid.dataSource.read(); //重新读取--刷新
        }
    }



    var edit1 = function () {debugger   

        var params = {};
        var inobjjson = {};
 
            params = {
                "jiraKey": "PHASE-68563",
                "fields": [
                    "customfield_19202",  
                    "status"
                ]

            };
            inobjjson = { "url": "http://"+JIRRA_URL + "/synchronize_info/api/jira/searchByFields", "bodyParams": params };
            var res = null;
            $.fn.ajaxPost({
                ajaxType: "post",
                ajaxUrl: "system/api/post/bodyParams",
                ajaxAsync: false,
                ajaxData: inobjjson,
                succeed: function (result) {
                    res = result;
                }
            });
         


    };
    var edit2 = function () {
        var g = getGridSelectData(gridName2Grid);

        if (g.length == 0) {
            alertMsg("请至少选择一条数据进行添加样本操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/independent/calculate/operate/examine/examine",
            title: "审核.."
        };
        openWindow(winOpts, { "gridNameGrid": g });//传递

    };



    funcPushs(pathValue, {
        "initData": initData,//初始化数据-需返回json--此方法非必须-加载单选下拉和多选下拉和一些文本标签默认参数时使用
        "init": init,//初始化方法-在加载完初始化数据之后执行   
        "edit1": edit1,
        "edit2": edit2,
        "refreshGrid": refreshGrid,
        "callBack": callBack,//回调方法
        //    "importData": importData,
    });
});