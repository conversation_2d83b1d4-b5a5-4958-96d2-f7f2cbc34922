$(document).ready(function () {
	var pathValue = "biomarker-dispatch-pd-libdxb-addtoex-addtoex"; 
	var paramsValue;
	//对同一任务单号进行文库产生计数器
	var order = [];
	var order_mn = [];
	var ordersm = [];
	var ordersm_mn = [];
	var ordersm_no = [];
	var ordersm_no_mn = [];
	var initData = function () {
		return {};
	}
	var gridNameGrid;
	var init = function (params) {
		paramsValue = params;
		var toolbar = getButtonTemplates(pathValue, [
			{ name: "edit", target: "add", title: "确认选择" },
		]);
		var gridNameGridJson = {
			url: "system/jdbc/query/one/table",
			sort: "",
			toolbar: toolbar,
			read: { "query": "lib_pd_SHEET_list_Dxb", "objects": [["单细胞建库"], ["待审核", "接收退回", "实验退回"]] },
			headerFilter: function (cols, i) { },
			detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
			detailInit: function (e) {
				var ROW_ID = e.data.ID;
				var subGrid_N_JSON = {
					url: "system/jdbc/query/one/table",
					sort: "",
					toolbar: null,
					height: 320,
					read: { "query": "queryTaskLibExMxHicDxbjk", "objects": [[ROW_ID]] },
				};
				var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
			}
		};
		gridNameGrid = initKendoGrid("#gridNameGrid" + pathValue, gridNameGridJson);
	}

	var add = function () {
		var myids = getGridSelectData(gridNameGrid);
		if (myids.length == 0) {
			alertMsg("请至少选择一条记录进行操作!");
			return;
		}
		var arrIds = [];
		var exman = [];
		for (var i = 0; i < myids.length; i++) {
			arrIds.push(myids[i]["ID"]);
			exman.push(myids[i]["EX_MAN"]);
		}


		var libtypename = [];
		var initials = [];
		var rows = getLibToLetter();
		for (var i = 0; i < rows.length; i++) {
			var row = rows[i];
			libtypename.push(row["LIB_TYPE_NAME"]);
			initials.push(row["INITIALS"]);
		}


		var codes = paramsValue["CODES"];

		var objectadd = [];
		
		var uplibmx = [];
		var ids = paramsValue["IDS"];
		var types = paramsValue["types"];
		var qcids = paramsValue["QCIDS"];
		for (var i = 0; i < arrIds.length; i++) {
			var time = sysNowTimeFuncParams["sysNowTime"];
			var username = getLimsUser()["name"];
			for (var j = 0; j < ids.length; j++) {
				var DATA_SUM = paramsValue["MxObject"][j]["DATA_SUM"];
				if (paramsValue["MxObject"][j]["DATA_UNIT"] == "G") {
					DATA_SUM = DATA_SUM / 0.3;
				}
				iniFist = checkInitals(paramsValue["MxObject"][j]["LIBRARY_TYPE_EN"], libtypename, initials);
				objectadd.push({
					"LIBRARY_CODE": getLibCodeNo(paramsValue["MxObject"][j]["TASK_NO"], iniFist, paramsValue["MxObject"][j]["DON"], paramsValue["MxObject"][j]["LIBMAXCODE"], paramsValue["MxObject"][j]["SAMPLE_CODE"], paramsValue["MxObject"][j]["DATA_LIBCODE"]),
					"TASK_LIB_MX_ID": ids[j],//联联任务ID
					"SEQ_PLAT":  "NGS",//测序平台TASK_PLAT
					"TQQC_ID": paramsValue["MxObject"][j]["BDRQID"],//提取检测ID
					"BIO_CODE": codes[i],
					"LIBRARY_TYPE": paramsValue["MxObject"][j]["LIBRARY_TYPE_EN"],//文库类型
					"LIBRARY_TYPE_E": paramsValue["MxObject"][j]["LIBRARY_TYPE_EN"],//文库类型
					"EXE_TQQC_ID": arrIds[i],//关联执行单
					"JK_TASKMX_MAN": exman[i],
					"JK_TASKMX_STDATE": time,
					"SYS_MAN": username,//实验员
					"SYS_INSERTTIME": time//开始日期
				});
				uplibmx.push({
					"ID": ids[j],
					"JK_TASK_SM_STATUS": "建库中",
					"THE_DATA_APSUM": DATA_SUM,
					"THE_DATA_SUM": DATA_SUM,
					"TEST_DATA_LEN": DATA_SUM
				});


			}
		}
		var urlsend = "system/jdbc/save/batch/table";
		var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectadd };
		putAddOrUpdata(urlsend, paramsadd, "是", "推入下一步实验任务");

	}



	//文库编号
	var getLibCodeNo = function (orderno, c, n, maxcode, smcode, datalibcode) {

		if (maxcode) {//已生成过文库
			var maxcode_1 = maxcode.substr(0, maxcode.lastIndexOf("-") + 1);
			var maxcode_2 = maxcode.replace(maxcode_1, "");
			maxcode_2 = parseInt(maxcode_2);
			var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
			var mn;
			if (indexn > -1) {
				mn = ordersm_no_mn[indexn] + 1;
				ordersm_no_mn[indexn] = mn;
			} else {
				mn = maxcode_2 + 1;
				ordersm_no.push(orderno + "_" + smcode + "_no");
				ordersm_no_mn.push(mn);
			}
			return maxcode_1 + getBioCodeNo(mn, 0);
		} else {//没有生成过文库
			if (datalibcode) { //运营提前生产
				var maxcode_1 = datalibcode.substr(0, datalibcode.lastIndexOf("-") + 1);
				var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
				var mn;
				if (indexn > -1) {
					mn = ordersm_no_mn[indexn] + 1;
					ordersm_no_mn[indexn] = mn;
				} else {
					mn = 1;
					ordersm_no.push(orderno + "_" + smcode + "_no");
					ordersm_no_mn.push(mn);
				}
				return maxcode_1 + getBioCodeNo(mn, 0);
			} else {//运营没有提前生产
				var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
				var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
				var mn;
				if (indexn > -1) {
					mn = ordersm_no_mn[indexn] + 1;
					ordersm_no_mn[indexn] = mn;
				} else {
					mn = 1;
					ordersm_no.push(orderno + "_" + smcode + "_no");
					ordersm_no_mn.push(mn);
				}
				var indexn2 = order.indexOf(orderno);
				var indexn3 = ordersm.indexOf(orderno + "_" + smcode);
				var mn2;
				if (indexn3 > -1) {
					mn2 = ordersm_mn[indexn3];
				} else if (indexn2 > -1) {
					mn2 = order_mn[indexn2] + 1;
					order_mn[indexn2] = mn2;
					ordersm.push(orderno + "_" + smcode);
					ordersm_mn.push(mn2);
				} else {
					mn2 = n + 1;
					order.push(orderno);
					order_mn.push(mn2);
					ordersm.push(orderno + "_" + smcode);
					ordersm_mn.push(mn2);
				}
				var num = getNo(mn2, 0);
				tempcode = tempcode + c + num + "-" + getBioCodeNo(mn, 0);

				return tempcode;
			}
		}
	}
	//比对取对照
	var checkInitals = function (name, names, initals) {
		for (var i = 0; i < names.length; i++) {
			if (name == names[i]) {
				return initals[i];
			}
		}
		return "";
	}
	//文库流程号段(项目期号内)
	var getNo = function (num, ki) {
		num = num + ki;
		if (num < 10) {
			num = "000" + num;
			return num;
		}
		if (num >= 10 && num < 100) {
			num = "00" + num;
			return num;
		}
		if (num >= 100 && num < 1000) {
			num = "0" + num;
			return num;
		}
		return num;
	}
	//核酸号段(项目期号内)
	var getBioCodeNo = function (num) {
		if (num < 10) {
			num = "0" + num;
			return num;
		}
		return num;
	}


	var getLibToLetter = function () {
		var rows;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "queryBioLibTypeList" },
			succeed: function (rs) {
				rows = rs.rows;
			}
		});
		return rows

	}

	//批量执行插入
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						alertMsg("提示:操作成功!");
						refreshGrid();
						funcExce(pathValue + "pageCallBack");
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}


	var callBack = function () {
		refreshGrid();
	};

	var refreshGrid = function () {
		if (gridNameGrid) {
			gridNameGrid.dataSource.read();//重新读取--刷新
		}
	}

	funcPushs(pathValue, {
		"initData": initData,
		"init": init,
		"add": add,
		"refreshGrid": refreshGrid,
		"callBack": callBack,//回调方法
	});
});