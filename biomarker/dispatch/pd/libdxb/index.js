$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-libdxb-index"; 
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var gridNameS = [];
    var gridNameS2 = [];
    var gridNameS3 = [];
    var sydates = 0;
    var cxdates = 0;

    //对同一任务单号进行文库产生计数器
    var order = [];
    var order_mn = [];
    var ordersm = [];
    var ordersm_mn = [];
    var ordersm_no = [];
    var ordersm_no_mn = [];

    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [  
            { name: "edit", target: "edit", title: "生成执行单" },
            { name: "edit", target: "addToEx", title: "追加样本到执行单" },
            { name: "edit", target: "doTaskStatus", title: "批量结单" },
            { name: "edit", target: "upsmStatus", title: "修改建库状态" },
            { name: "edit", target: "uptaskstatus", title: "任务单状态修改" },
            //    { name: "edit", target: "setjira", title: "jira推送.." },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_TQ_TASK_DXBJK_list", "objects": [["已审核", "待建库", "建库中", "质检中", "测序中" , "测序完成", "数据处理中", "数据处理完成", "已完成"], ["单细胞提取"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_BIO_TQ_TASK_DXBJKMX_list", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        init2();
        init3();
        init4();
    }

    //待审核
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "delete", target: "remove", title: "移除任务明细" },
            { name: "delete", target: "doDelete", title: "删除执行单" },
            { name: "edit", target: "doUpdate", title: "修改实验员" },
             { name: "excel", target: "importData1", title: "数据量修改" },
            { name: "edit", target: "doGenNo", title: "生成文库编号" },
            { name: "ok", target: "doOK", title: "提交" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "lib_pd_SHEET_list_Dxb", "objects": [["单细胞建库"], ["待审核", "接收退回", "实验退回"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMxHicDxbjk", "objects": [], "search": { "EXE_TQQC_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);
    }
    //已处理
    var init3 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "lib_pd_SHEET_list_Dxb", "objects": [["单细胞建库"], ["已接收", "审核退回", "建库已审核"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMxHicDxbjk", "objects": [], "search": { "EXE_TQQC_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS3.push(subGrid_N);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);
    }
    //已结单
    var init4 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "移至待排单" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_BIO_TQ_TASK_DXBJK_list", "objects": [["结单", "暂停", "未提取", "终止", "建库完成"], ["单细胞提取"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_PD_BIO_TQ_TASK_MX_OTHER_list", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);

    }


    var edit = function () {
        debugger
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //判断类型是否全部分同一为,并取出形成类型单
        var g = arrIds;
        var a = "";
        var b = "";
        var code = [];

        for (var i = 0; i < g.length; i++) {

            if (code.indexOf(g[i]["SAMPLE_GENNO"]) > -1) {//同一个执行单不允许重复
                //alertMsg("提示:存在所选编号“"+g[i]["BIO_CODE"]+"重复!”");
                //return;
            } else {
                code.push(g[i]["SAMPLE_GENNO"]);
            }
            // if (i == 0) {
            //     a = g[i]["LIBRARY_FLOW"];
            //     b = g[i]["LIBRARY_FLOW"];
            // } else {
            //     a = g[i - 1]["LIBRARY_FLOW"];
            //     b = g[i]["LIBRARY_FLOW"];
            // }

            // if (a != b) {
            //     alertMsg("存在所选记录建库流向“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
            //     return;
            // }

        }

        var ids = [];
        var taskids = [];
        var biocodes = [];
        var samplecodes = [];
        var libtypes = [];
        var libtypemxs = [];
        var LIBQCIDS = [];

        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            libtypemxs.push(arrIds[i]["LIBRARY_TYPE_EN"]);
            biocodes.push(arrIds[i]["SAMPLE_GENNO"]);
            samplecodes.push(arrIds[i]["SAMPLE_CODE"]);
            if (arrIds[i]["LIBRARY_TYPE_EN"] == null || arrIds[i]["LIBRARY_TYPE_EN"] == "") {
                alertMsg("提示:存在所选编号“" + arrIds[i]["SAMPLE_CODE"] + "文库类型为空!”");
                return;
            }
            if (libtypes.indexOf(arrIds[i]["LIBRARY_TYPE_EN"]) < 0) {
                libtypes.push(arrIds[i]["LIBRARY_TYPE_EN"]);//文库类型
            }
            if (taskids.indexOf(arrIds[i]["BTTID"]) < 0) {
                taskids.push(arrIds[i]["BTTID"]);//主单ID
            }
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libdxb/pdup/pdup",
            title: "常规建库排单.."
        };
        openWindow(winOpts, {
            "MxObject": arrIds, "IDS": ids, "TASKIDS": taskids, "SAMPLECODES": samplecodes, "BIOCODES": biocodes, "LIBTYPES": libtypes,
            "LIBTYPEMXS": libtypemxs, "EX_TYPE": "单细胞建库", "LIBQCIDS": LIBQCIDS, "YC": "否", "TASK_PLAT": arrIds[0]["TASK_PLAT"]
        });
    }
    //修改实验员 
    var doUpdate = function () {
        var arrIds = getGridSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libdxb/updateman/updateman",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"], "EX_MAN": arrIds[0]["EX_MAN"] });//传递
    }
    //审核提交
    var doOK = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        //校验核酸编号是否为空
        var s = "";
        var params = { "query": "doCheckBioCOdeIsPass", "objects": [arrIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        if (row["SAMPLE_GENNO"] == "" || row["SAMPLE_GENNO"] == null) {
                            if (s == "") {
                                s = row["EX_DH_NO"];
                            }
                        }
                    }
                    if (s != "") {
                        alertMsg("提示:单号“" + s + "”存在核酸编号为空!");
                        return;
                    }
                    var objectup = [];
                    for (var i = 0; i < arrIds.length; i++) {
                        var time = sysNowTimeFuncParams["sysNowTime"];
                        var username = getLimsUser()["name"];
                        objectup.push({
                            "ID": arrIds[i],//联联任务ID
                            "EX_RE_STATUS": "已接收"
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "提交");
                    //	doRequeDoUpTaskmxSmStatus(arrIds, "提取中");

                }
            }
        });

    }




    //撤回
    var doReturn = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        var arrIds = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["EX_RE_STATUS"] != "待接收") {
                alertMsg("操作失败,所选记录存在非“待接收”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "EX_RE_STATUS": "待审核"
                });
                arrIds.push(g[i]["ID"]);
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
        doRequeDoUpTaskmxSmStatus(arrIds, "待提取");
    }


    //任务单-对应执行单下明细状态修改BIO_DNA_RNA_QC
    var doRequeDoUpTaskmxSmStatus = function (mainExIds, status) {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var params = { "query": "doRequeDoUpTaskmxSmStatus", "objects": [mainExIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var objectupmain = [];
                    debugger;
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];

                        if (objectupmain.indexOf(row["TASK_ID"]) < 0) {
                            objectupmain.push(row["TASK_ID"]);
                        }
                        //更新记录---明细
                        objectup.push({
                            "ID": row["TASKMXID"],
                            "TASK_SM_STATUS": status
                        });
                    }

                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "同步更新任务明细");

                    var urlmain = "system/jdbc/update/one/table/where";
                    var paramsupmain = {
                        "tableName": "BIO_TQ_TASK",
                        "TASK_STATUS": status,
                        "SYS_MAN_L": username,
                        "SYS_INSERTTIME_L": time,
                        "where": { "ID": objectupmain }
                    };
                    putAddOrUpdata(urlmain, paramsupmain, "否", "同步更新任务");

                }
            },
            failed: function (result) {
                alertMsg("提示:操作异常!", "error");
            }
        });
    }
    //预处理提交
    var submit = function () {
        var arrIds = getGridSelectData(gridNameD5Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }

        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: { "query": "query_YPD_BIO_TQ_TASK_MX_list_Check", "objects": [[arrIds[0]["ID"]]] },
            succeed: function (rs) {
                sample = rs.rows;             //样品
                var num = 0;
                for (var j = 0; j < sample.length; j++) {
                    if (sample[j]["JK_CHECK"] == "OK") {
                        num = num + 1;

                    }
                }
                if (num < sample.length) {
                    alertMsg("还有" + (sample.length - num) + "条未核验，不能提交");
                    return;
                }
                var objectSheet = [];
                objectSheet.push({
                    "ID": arrIds[0]["ID"],//id
                    "EX_RE_STATUS": "待审核"       //状态

                });
                var urlsend = "system/jdbc/save/batch/table";
                var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
                putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
            }
        });

    }

    //移至待审核
    var doReturn2 = function () {
        var username = getLimsUser()["name"];
        if (username != "蓝勇胜" && username != "徐彦岭"&& username != "王甜甜") {
            alertMsg("无此权限，如有需要请联系开发人员!");
            return;
        }
        var g = getGridSelectData(gridNameD3Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
           // if (g[i]["TASK_STATUS"] == "暂停" || g[i]["TASK_STATUS"] == "未提取") {
                objectup.push({
                    "ID": g[i]["ID"],
                    "JK_TASK_STATUS": "已审核"
                });
      //      } else {
            //    alertMsg("操作失败,只有“<font color=#ff0000>暂停、未提取</font>”状态方允许操作!");
               // return;
        //    }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }


    //追加
    var addToEx = function () {

        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/libdxb/addtoex/addtoex",
            title: "追加样本到执行单.."
        };
        var ids = [];
        var codes = [];
        var tqffs = [];

        for (var i = 0; i < arrIds.length; i++) {
            if (arrIds[i]["JK_TASK_SM_STATUS"] == "建库完成" || arrIds[i]["JK_TASK_SM_STATUS"] == "建库中") {
                alertMsg("提示:样品“" + arrIds[i]["SAMPLE_CODE"] + arrIds[i]["JK_TASK_SM_STATUS"] + "”");
                return;
            }
            ids.push(arrIds[i]["ID"]);
            tqffs.push(arrIds[i]["EXTDR_METHOD"]);
            codes.push(arrIds[i]["SAMPLE_CODE"]);
        }

        openWindow(winOpts, { "IDS": ids, "CODES": codes, "TQFFS": tqffs, "MxObject": arrIds });
    }

    var doTaskStatus = function () {


        var objects = getGridSelectData(gridNameDGrid);
        var arrIds = getSelectData(gridNameDGrid);
        if (objects.length == 0) {
            alertMsg("请选择一条记录进行操作!");
            return;
        }
        for (var i = 0; i < objects.length; i++) {
            sendPM(setjira(objects[i]));
        }
        var urlmain = "system/jdbc/update/one/table/where";
        var paramsupmain = {
            "tableName": "BIO_TQ_TASK",
            "JK_END_LDATE": sysNowTimeFuncParams["sysNowTime"],
            "JK_TASK_STATUS": "结单",
            "where": { "ID": arrIds }
        };
        putAddOrUpdata(urlmain, paramsupmain, "是", "同步更新任务");
    }
    var sendPM = function (p) {
        debugger;
        var odlStatus = "样品提取检测";
        var newStatus = "测序";
        var mt = p["MEHOD_TJPLAT"];
        var params;
        var oldStatusName = getJira(p["LSM_KEY_P"]);

        params = {
            "jiraKey": p["LSM_KEY_P"],
            "oldStatusName": oldStatusName,
            "statusName": newStatus,
            "updateField": {
                "customfield_12103": sysNowTimeFuncParams["sysNowTime"],	//建库完成日期
                "customfield_10227": toDateFormatByZone(p["CUSTOMFIELD_10227"], "yyyy-MM-dd HH-mm-ss"),	//测序标准完成日期 
            }
        };

        var inobjjson = { "url": "http://"+JIRRA_URL+"/synchronize_info/api/jira/sysInfo", "bodyParams": params };
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            succeed: function (result) { },
            failed: function (res) { }
        });
    }


    //查询:项目状态及客户经理描述
    var getJira = function (keyinfo) {

        var url = "http://"+JIRRA_URL+"/synchronize_info/api/jira/searchByFields";
        var parmars = { "jiraKey": keyinfo, "fields": ["customfield_11525", "status", "customfield_14600"] };
        var inobjjson = { "url": url, "bodyParams": parmars };
        var oldStatusName = null;
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: "system/api/post/bodyParams",
            ajaxData: inobjjson,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    oldStatusName = result.apiData[0].fields.status.name;
                } else {
                    alertMsg("提示:加载获取jira信息失败!");
                }
            }
        });
        return oldStatusName;
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {

    var order = [];
    var order_mn = [];
    var ordersm = [];
    var ordersm_mn = [];
    var ordersm_no = [];
    var ordersm_no_mn = [];

        gridNameS = [];
        gridNameS2 = [];
        gridNameS3 = [];

        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }
        if (gridNameD3Grid) {
            gridNameD3Grid.dataSource.read();
        }
    }

    var remove = function () {
        var arrg = [];
        var arrIds = [];
        var obj = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getGridSelectData(gridNameS2[i]);
            arrg = arrg.concat(arrSubID);

        }
        if (arrg.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        for (var i = 0; i < arrg.length; i++) {
            arrIds.push(arrg[i]["LIBID"]);
            obj.push({ "ID": arrg[i]["ID"], "TASK_SM_STATUS": "已审核" });
        }
        confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd1 = { "tableName": "BIO_TQ_TASK_MX", "objects": obj };
            putAddOrUpdata(urlsend, paramsadd1, "否", "提交");
            var params = { "tableName": "BIO_LIB_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        });
    }
    //删除执行单
    var doDelete = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
            var url = "system/jdbc/delete/one/table/where";
            var params1 = { "tableName": "BIO_LIB_INFO", "where": { "EXE_TQQC_ID": arrIds } };
            deleteGridDataByIds(url, params1, refreshGrid);
            var params2 = { "tableName": "EXE_TQQC_SHEET", "where": { "ID": arrIds } };
            deleteGridDataByIds(url, params2, refreshGrid);
        });
    }
    //文库编号
    var doGenNo = function () {
        debugger;
        var arrIds = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getGridSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }



        var objectup = [];
        var libtypename = [];
        var initials = [];
        var rows = getLibToLetter();
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            libtypename.push(row["LIB_TYPE_NAME"]);
            initials.push(row["INITIALS"]);
        }

        for (var i = 0; i < arrIds.length; i++) {
            if ( arrIds[i]["LIBRARY_CODE"] !=null) {
                alertMsg("文库编号“" + arrIds[i]["LIBRARY_CODE"] + "”已存在!");
            } else {

                iniFist = checkInitals(arrIds[i]["LIBRARY_TYPE"], libtypename, initials);
                objectup.push({
                    "ID": arrIds[i]["LIBID"],//关联更新ID 
                    "SEQ_PLAT":"NGS",
                    "LIBRARY_CODE": getLibCodeNo(arrIds[i]["TASK_NO"], iniFist, arrIds[i]["DON"], arrIds[i]["LIBMAXCODE"], arrIds[i]["SAMPLE_CODE"], arrIds[i]["DATA_LIBCODE"],arrIds[i]["SAMPLE_NAME"]),
                });
            }

        }
        if (objectup.length > 0) {

            var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectup };
            var url = "system/jdbc/save/batch/table";
            putAddOrUpdata(url, paramsup, "是", "提取");
        }
    }

    var getLibToLetter = function () {
        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "queryBioLibTypeList" },
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        return rows

    }

    //核酸号段(项目期号内)
    var getBioCodeNo = function (num) {
        if (num < 10) {
            num = "0" + num;
            return num;
        }
        return num;
    }

    //比对取对照
    var checkInitals = function (name, names, initals) {
        for (var i = 0; i < names.length; i++) {
            if (name == names[i]) {
                return initals[i];
            }
        }
        return "";
    }

    var getLibCodeNo = function (orderno, c, n, maxcode, smcode, datalibcode,samplename) {

        if (maxcode) {//已生成过文库
            var maxcode_1 = maxcode.substr(0, maxcode.lastIndexOf("-") + 1);
            var maxcode_2 = maxcode.replace(maxcode_1, "");
            maxcode_2 = parseInt(maxcode_2);
            var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
            var mn;
            if (indexn > -1) {
                mn = ordersm_no_mn[indexn] + 1;
                ordersm_no_mn[indexn] = mn;
            } else {
                mn = maxcode_2 + 1;
                ordersm_no.push(orderno + "_" + smcode + "_no");
                ordersm_no_mn.push(mn);
            }
            return maxcode_1 + getBioCodeNo(mn, 0);
        } else {//没有生成过文库
            if (datalibcode) { //运营提前生产
                var maxcode_1 = datalibcode.substr(0, datalibcode.lastIndexOf("-") + 1);
                var indexn = ordersm_no.indexOf(orderno + "_" + smcode+ "_" +samplename + "_no");
                var mn;
                if (indexn > -1) {
                    mn = ordersm_no_mn[indexn] + 1;
                    ordersm_no_mn[indexn] = mn;
                } else {
                    mn = 1;
                    ordersm_no.push(orderno + "_" + smcode + "_" +samplename+ "_no");
                    ordersm_no_mn.push(mn);
                }
                return maxcode_1 + getBioCodeNo(mn, 0);
            } else {//运营没有提前生产
                var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
                var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_" +samplename + "_no");
                var mn;
                if (indexn > -1) {
                    mn = ordersm_no_mn[indexn] + 1;
                    ordersm_no_mn[indexn] = mn;
                } else {
                    mn = 1;
                    ordersm_no.push(orderno + "_" + smcode + "_" +samplename + "_no");
                    ordersm_no_mn.push(mn);
                }
                var indexn2 = order.indexOf(orderno);
                var indexn3 = ordersm.indexOf(orderno + "_" + smcode+ "_" +samplename);
                var mn2;
                if (indexn3 > -1) {
                    mn2 = ordersm_mn[indexn3];
                } else if (indexn2 > -1) {
                    mn2 = order_mn[indexn2] + 1;
                    order_mn[indexn2] = mn2;
                    ordersm.push(orderno + "_" + smcode+ "_" +samplename);
                    ordersm_mn.push(mn2);
                } else {
                    mn2 = n + 1;
                    order.push(orderno);
                    order_mn.push(mn2);
                    ordersm.push(orderno + "_" + smcode+ "_" +samplename);
                    ordersm_mn.push(mn2);
                }
                var num = getNo(mn2, 0);
                tempcode = tempcode + c + num + "-" + getBioCodeNo(mn, 0);

                return tempcode;
            }
        }
    }













    var setjira = function (arrIds) {
        debugger;
        var arrIds

        var rows1;

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_PD_BIO_TQ_TASK_MX_OTHER_list", "objects": [[arrIds["ID"]]] },
            succeed: function (rs) {
                //console.log(rs);				
                rows1 = rs["rows"];
            }
        });

        var THE_DATA_SUM = 0;
        var type;
        for (var i = 0; i < rows1.length; i++) {
            THE_DATA_SUM += rows1[i]["DATA_SUM"];
            type = rows1[i]["LIBRARY_TYPE_EN"];
        }
        var dwtype = rows1[0]["DATA_UNIT"];
        var cyc_dws = rows1[0]["CYC_DW"];
        var business_unit = rows1[0]["BUSINESS_UNIT"];


        var IS_FULL_LIFE_CYCLE = rows1[0]["IS_FULL_LIFE_CYCLE"];
        var IS_SHORT_PERIOD = "否";
        var IS_FULL_LIFE_CYCLE2 = "否";
        if (IS_FULL_LIFE_CYCLE == "是") {
            IS_FULL_LIFE_CYCLE2 = "是"
        } else {
            var CUSTOMER_VIP = rows1[0]["CUSTOMER_VIP"];
            if (CUSTOMER_VIP.indexOf(2) > -1) {
                IS_SHORT_PERIOD = "是";
            }
        }

        doCyc(type, rows1.length, THE_DATA_SUM, "测序标准用时", dwtype, cyc_dws, business_unit, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);

        doCyc(type, rows1.length, THE_DATA_SUM, "实验交付标准用时", dwtype, cyc_dws, business_unit, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2);
        var p = arrIds;
        var time = Date.parse(sysNowTimeFuncParams["sysNowTime"]);
        var customield_10227 = time + (cxdates * 86400000);
        var customield_10226 = time + (sydates * 86400000);
        return {
            "ID": p["LSMID"],
            "MAIN_ID": p["ID"],
            "TASK_JH_ENDDATE": p["TASK_JH_ENDDATE"],
            "MEHOD_TJPLAT": p["MEHOD_TJPLAT"],//提取部门
            "LSM_KEY": p["LSM_KEY"],//LSM关键字
            "LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字 
            "SAMPLE_BATCHNO": p["SAMPLE_BATCHNO"],//LSM关键字
            //"CUSTOMFIELD_13226":p[""],//提取完成日期
            "CUSTOMFIELD_14300": p["TASK_LDATE"],//提取检测标准完成日期--
            "CUSTOMFIELD_12103": time,//建库完成日期
            "CUSTOMFIELD_12101": time,//建库标准完成日期
            "CUSTOMFIELD_14201": time,//建库计划完成日期
            "DATETG": time,//建库审核通过日期

            "CUSTOMFIELD_10227": customield_10227,//测序标准完成日期
            "CUSTOMFIELD_14202": customield_10227,//测序计划完成日期
            "CUSTOMFIELD_10226": customield_10226,//实验标准交付日期
            "CUSTOMFIELD_13900": customield_10226,//实验计划交付日期
            //"CUSTOMFIELD_13254":p[""],//提取检测暂停开始日期
            "CUSTOMFIELD_13237": p["TASK_REMARKS_DES"],//提取具体情况描述
            "sydates": sydates,//实验周期
            "cxdates": cxdates,//测序周期
            // "CUSTOMFIELD_13229"://样品合格情况
        };

    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }



    //获取周期定义,推算出截止结果日期
    var doCyc = function (type, countSm, smnumber, dep, sa, cyc_dws, business_unit, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2) {
        debugger;
        //样品提取检测标准用时
        var cycdw = cyc_dws;
        var bus = business_unit;

        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        } else {
            flag = 1;
            if (sa == "CELL") {
                if (type == "Denovo-Pac(20-30K)") smnumber = smnumber * 140;
                if (type == "Denovo-Pac(hifi)") smnumber = smnumber * 24;
                if (type == "Iso-RNA(Pac)") smnumber = smnumber * 300;
                if (type == "DNA-8K(ONT)") smnumber = smnumber * 80;
                if (type == "Denovo-20k(ONT)") smnumber = smnumber * 80;
                if (type == "Iso-RNA(ONT)") smnumber = smnumber * 140;
            }
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber, IS_SHORT_PERIOD, IS_FULL_LIFE_CYCLE2] };
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    debugger;
                    var rows = result["rows"];
                    var m = getMyMonth();
                    var dateNumber = 0;
                    var seleDateFlag = "工作日";//日历取向
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        seleDateFlag = row["CYC_FLAG"];
                        if (m == 1) dateNumber = row["MONTH_1"];
                        if (m == 2) dateNumber = row["MONTH_2"];
                        if (m == 3) dateNumber = row["MONTH_3"];
                        if (m == 4) dateNumber = row["MONTH_4"];
                        if (m == 5) dateNumber = row["MONTH_5"];
                        if (m == 6) dateNumber = row["MONTH_6"];
                        if (m == 7) dateNumber = row["MONTH_7"];
                        if (m == 8) dateNumber = row["MONTH_8"];
                        if (m == 9) dateNumber = row["MONTH_9"];
                        if (m == 10) dateNumber = row["MONTH_10"];
                        if (m == 11) dateNumber = row["MONTH_11"];
                        if (m == 12) dateNumber = row["MONTH_12"];

                        break;
                    }
                    //执行天数
                    saveRemind = 1;
                    if (dep == "测序标准用时") {
                        cxdates = dateNumber;
                    } else {
                        sydates = dateNumber;
                    }
                    // $("#TASK_EXCDAYS" + pathValue).val(dateNumber);
                    // $("#CYC_FLAG" + pathValue).val(seleDateFlag);
                    // doGetEndDate(seleDateFlag, dateNumber);
                }
            }
        });

    }
    //推算截止日期
    var doGetEndDate = function (seleDateFlag, dateNumber) {

        var thedate = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    for (var i = 0; i < dateNumber; i++) {
                        var base = 1000 * 60 * 60 * 24;
                        //thedate=new Date(thedate.getTime() + base); 
                        if (i == 0) {
                            var TASK_LLS = paramsValue["TASK_LL"] * 1;
                            thedate = new Date(TASK_LLS + (base));
                        } else {
                            //TASK_LLS=new Date(thedate.getTime() + base);
                            thedate = new Date(thedate.getTime() + base);
                        }
                        for (var j = 0; j < noDoDateS.length; j++) {
                            if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                thedate = new Date(thedate.getTime() + base);//日期向前一天
                            }
                        }

                    }
                    //推算出的最终截止日期
                    // $("#TASK_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                    // $("#TASK_JH_ENDDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));

                }
            }
        });

    }

	//文库流程号段(项目期号内)
	var getNo = function (num, ki) {
		num = num + ki;
		if (num < 10) {
			num = "000" + num;
			return num;
		}
		if (num >= 10 && num < 100) {
			num = "00" + num;
			return num;
		}
		if (num >= 100 && num < 1000) {
			num = "0" + num;
			return num;
		}
		return num;
	}


    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }
    //表格导入
    var importData1 = function (componentId) {
        debugger;
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条执行单进行操作!");
            return;
        } 


        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "B",
                        tableName: "BIO_TQ_TASK_MX",
                        requestData: {
                            ajaxData: { "query": "queryTaskLibExMxHicDxbjk", "size": 5000, "objects": [], "search": { "EXE_TQQC_ID": arrIds } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }
    
    //样本状态修改
    var upsmStatus = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libdxb/upsmstatus/upsmstatus",
            title: "修改样本状态.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    } 
    
    //任务单状态修改
    var uptaskstatus = function () {
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行审核操作!");
            return;
        }
        debugger;
        var ids = [];
        lsmkeyps = [];
        var day1 = new Date();
        day1.setTime(day1.getTime() - 24 * 60 * 60 * 1000);
        var s1 = day1.getFullYear() + "-" + (day1.getMonth() + 1) + "-" + day1.getDate();
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            lsmkeyps.push(arrIds[i]["LSM_KEY_P"]);
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/libdxb/uptaskstatus/uptaskstatus",
            title: "修改任务单状态.."
        };
        openWindow(winOpts, { "arrIds":arrIds, "IDS": ids, "LSMKEYP": lsmkeyps, "JK_END_LDATE": s1 });
    }
    

    funcPushs(pathValue, {
        "initData": initData,
        "upsmStatus": upsmStatus,
        "uptaskstatus":uptaskstatus,
        "init": init,
        "addToEx": addToEx,
        "doTaskStatus": doTaskStatus,
        "edit": edit,
        "doOK": doOK,
        "doReturn": doReturn,
        "submit": submit,
        "importData1": importData1,
        "doUpdate": doUpdate,
        "doReturn2": doReturn2,
        "doGenNo": doGenNo,
        "remove": remove,
        "doDelete": doDelete,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "setjira": setjira,
    });
});