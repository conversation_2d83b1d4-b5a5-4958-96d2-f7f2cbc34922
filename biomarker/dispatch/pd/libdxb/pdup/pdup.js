$(document).ready(function () {
	var pathValue = "biomarker-dispatch-pd-libdxb-pdup-pdup";
	var paramsValue;
	//对同一任务单号进行文库产生计数器
	var order = [];
	var order_mn = [];
	var ordersm = [];
	var ordersm_mn = [];
	var ordersm_no = [];
	var ordersm_no_mn = [];
	var initData = function () {
		return {
			tableName: "EXE_TQQC_SHEET"
		};
	}
	var init = function (params) {
		paramsValue = params;
		getInfo("form", pathValue, params);
		var url = "system/jdbc/query/info/" + initData().tableName;
		getInfo("form", pathValue, params, url);
		$("#EX_TYPE" + pathValue).val(paramsValue["EX_TYPE"]);
		if (paramsValue["EX_TYPE"] == "三代ONT基因组建库") {
			$("#EX_LB" + pathValue).val("切胶");
		}
		$("#EX_MX_NUMBER" + pathValue).val(paramsValue["IDS"].length);
		var libtyps = paramsValue["LIBTYPES"];
		var strlibthype = "";
		for (var i = 0; i < libtyps.length; i++) {
			if (i == 0) {
				strlibthype = libtyps[i];
			} else {
				strlibthype += "," + libtyps[i];
			}
		}
		$("#EX_LIB_TYPE" + pathValue).val(strlibthype);


		var numstr = "DJ";

		$("#NO_CHAR" + pathValue).val(numstr);

	}


	var subUpData = function () {
		var time = sysNowTimeFuncParams["sysNowTime"];
		var username = getLimsUser()["name"];
		/**
		 * 平台很重要,后台上机排单需要分流
		 */
		var SEQ_PLAT = "NGS";  // paramsValue["TASK_PLAT"];     
		//插入执行主单
		formSubmit({
			url: "system/jdbc/save/one/table",
			formId: "form",
			pathValue: pathValue,
			succeed: function (result) {
				if (result["code"] > 0) {
					alertMsg("提交成功,生成的草稿,请前往审核提交!", "success", function () {
						var ids = paramsValue["IDS"];
						var taskids = paramsValue["TASKIDS"];
						var objectadd = [];
						var objectup = [];
						var objectupqc = [];
						var biocodes = paramsValue["BIOCODES"];
						var samplecodes = paramsValue["SAMPLECODES"];
						var libtypemxs = paramsValue["LIBTYPEMXS"];
						var REBUILD_LIB_SCHEME = paramsValue["REBUILD_LIB_SCHEME"];
						var LIBQCIDS = paramsValue["LIBQCIDS"];
						var upLIBQC = [];
						var uplibmx = [];

						var YC = paramsValue["YC"];
						var INDEX_NAME = paramsValue["INDEX_NAME"];
						var SEQ_I7_1 = paramsValue["SEQ_I7_1"];
						var SEQ_I7_2 = paramsValue["SEQ_I7_2"];
						var SEQ_I7_3 = paramsValue["YC"];
						var SEQ_I7_4 = paramsValue["SEQ_I7_4"];
						var SEQ_I5_NAVA = paramsValue["SEQ_I5_NAVA"];
						var SEQ_I5_XTEN = paramsValue["SEQ_I5_XTEN"];
						var GEN_N = paramsValue["GEN_N"];
						var ER_SEQ = paramsValue["ER_SEQ"];
						var LIB_KIT = paramsValue["LIB_KIT"];
						debugger;

						var libtypename = [];
						var initials = [];
						var rows = getLibToLetter();
						for (var i = 0; i < rows.length; i++) {
							var row = rows[i];
							libtypename.push(row["LIB_TYPE_NAME"]);
							initials.push(row["INITIALS"]);
						}



						for (var i = 0; i < ids.length; i++) {


							var DATA_SUM = paramsValue["MxObject"][i]["DATA_SUM"];
							if (paramsValue["MxObject"][i]["DATA_UNIT"] == "G") {
								DATA_SUM = DATA_SUM / 0.3;
							}
							iniFist = checkInitals(paramsValue["MxObject"][i]["LIBRARY_TYPE_EN"], libtypename, initials);

							if (YC == "是") {
								objectadd.push({
									"LIBRARY_CODE": getLibCodeNo(paramsValue["MxObject"][i]["TASK_NO"], iniFist, paramsValue["MxObject"][i]["DON"], paramsValue["MxObject"][i]["LIBMAXCODE"], paramsValue["MxObject"][i]["SAMPLE_CODE"], paramsValue["MxObject"][i]["DATA_LIBCODE"],paramsValue["MxObject"][i]["SAMPLE_NAME"]),
									"TQQC_ID": paramsValue["MxObject"][i]["BDRQID"],//提取检测ID
									"TASK_LIB_MX_ID": ids[i],//联联任务ID
									"BIO_CODE": biocodes[i],//枋酸编号
									"LIBRARY_TYPE": libtypemxs[i],//文库类型
									"LIBRARY_TYPE_EN": libtypemxs[i],//文库类型
									"SAMPLE_CODE": samplecodes[i],//样品编号
									"EXE_TQQC_ID": result["ID"],//关联执行单
									"SEQ_PLAT": SEQ_PLAT,//测序平台
									"JK_TASKMX_MAN": $("#EX_MAN" + pathValue).val(),
									"JK_TASKMX_STDATE": time,
									"SYS_MAN": username,//实验员
									"SYS_INSERTTIME": time,//开始日期
									"REBUILD_LIB_SCHEME": REBUILD_LIB_SCHEME[i],//重建库方案
									"INDEX_NAME": INDEX_NAME[i],
									"INDEX_NAME": INDEX_NAME[i],
									"SEQ_I7_1": SEQ_I7_1[i],
									"SEQ_I7_2": SEQ_I7_2[i],
									"SEQ_I7_3": SEQ_I7_3[i],
									"SEQ_I7_4": SEQ_I7_4[i],
									"SEQ_I5_NAVA": SEQ_I5_NAVA[i],
									"SEQ_I5_XTEN": SEQ_I5_XTEN[i],
									"GEN_N": GEN_N[i],
									"ER_SEQ": ER_SEQ[i],
									"LIB_KIT": LIB_KIT[i],
								});
							}
							else {
								objectadd.push({
									"LIBRARY_CODE": getLibCodeNo(paramsValue["MxObject"][i]["TASK_NO"], iniFist, paramsValue["MxObject"][i]["DON"], paramsValue["MxObject"][i]["LIBMAXCODE"], paramsValue["MxObject"][i]["SAMPLE_CODE"], paramsValue["MxObject"][i]["DATA_LIBCODE"],paramsValue["MxObject"][i]["SAMPLE_NAME"]),
									"TQQC_ID": paramsValue["MxObject"][i]["BDRQID"],//提取检测ID
									"TASK_LIB_MX_ID": ids[i],//联联任务ID
									"BIO_CODE": biocodes[i],//枋酸编号
									"LIBRARY_TYPE": libtypemxs[i],//文库类型
									"LIBRARY_TYPE_EN": libtypemxs[i],//文库类型
									"SAMPLE_CODE": samplecodes[i],//样品编号
									"EXE_TQQC_ID": result["ID"],//关联执行单
									"SEQ_PLAT": SEQ_PLAT,//测序平台
									"JK_TASKMX_MAN": $("#EX_MAN" + pathValue).val(),
									"JK_TASKMX_STDATE": time,
									"SYS_MAN": username,//实验员
									"SYS_INSERTTIME": time//开始日期
								});
							}
							if (LIBQCIDS.length > 0) {
								upLIBQC.push({
									"ID": LIBQCIDS[i],
									"IS_REGO": "是"
								});
							}
							uplibmx.push({
								"ID": ids[i],
								"JK_TASK_SM_STATUS": "建库中",
								"THE_DATA_APSUM": DATA_SUM,
								"THE_DATA_SUM": DATA_SUM,
								"TEST_DATA_LEN": DATA_SUM
							});
						}
						for (var i = 0; i < taskids.length; i++) {
							objectup.push({
								"ID": taskids[i],
								"JK_TASK_STATUS": "建库中"
							});
						}
						//执行添加到文库
						var urlsend = "system/jdbc/save/batch/table";
						var paramsadd = { "tableName": "BIO_LIB_INFO", "objects": objectadd };
						putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

						var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
						putAddOrUpdata(urlsend, paramsup, "是", "建库中");

						var paramsupmx = { "tableName": "BIO_TQ_TASK_MX", "objects": uplibmx };
						putAddOrUpdata(urlsend, paramsupmx, "否", "建库中");

						if (upLIBQC.length > 0) {
							var paramsupqc = { "tableName": "BIO_LIB_QC_INFO", "objects": upLIBQC };
							putAddOrUpdata(urlsend, paramsupqc, "否", "");
						}




					});
				} else {
					alertMsg("提交失败", "error");
				}
			}
		});

	}


	//文库编号
	var getLibCodeNo = function (orderno, c, n, maxcode, smcode, datalibcode,samplename) {

		if (maxcode) {//已生成过文库
			var maxcode_1 = maxcode.substr(0, maxcode.lastIndexOf("-") + 1);
			var maxcode_2 = maxcode.replace(maxcode_1, "");
			maxcode_2 = parseInt(maxcode_2);
			var indexn = ordersm_no.indexOf(orderno + "_" + smcode+ "_" +samplename + "_no");
			var mn;
			if (indexn > -1) {
				mn = ordersm_no_mn[indexn] + 1;
				ordersm_no_mn[indexn] = mn;
			} else {
				mn = maxcode_2 + 1;
				ordersm_no.push(orderno + "_" + smcode+ "_" +samplename + "_no");
				ordersm_no_mn.push(mn);
			}
			return maxcode_1 + getBioCodeNo(mn, 0);
		} else {//没有生成过文库
			if (datalibcode) { //运营提前生产
				var maxcode_1 = datalibcode.substr(0, datalibcode.lastIndexOf("-") + 1);
				var indexn = ordersm_no.indexOf(orderno + "_" + smcode+ "_" +samplename + "_no");
				var mn;
				if (indexn > -1) {
					mn = ordersm_no_mn[indexn] + 1;
					ordersm_no_mn[indexn] = mn;
				} else {
					mn = 1;
					ordersm_no.push(orderno + "_" + smcode+ "_" +samplename + "_no");
					ordersm_no_mn.push(mn);
				}
				return maxcode_1 + getBioCodeNo(mn, 0);
			} else {//运营没有提前生产
				var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
				var indexn = ordersm_no.indexOf(orderno + "_" + smcode+ "_" +samplename + "_no");
				var mn;
				if (indexn > -1) {
					mn = ordersm_no_mn[indexn] + 1;
					ordersm_no_mn[indexn] = mn;
				} else {
					mn = 1;
					ordersm_no.push(orderno + "_" + smcode+ "_" +samplename + "_no");
					ordersm_no_mn.push(mn);
				}
				var indexn2 = order.indexOf(orderno);
				var indexn3 = ordersm.indexOf(orderno + "_" + smcode+ "_" +samplename);
				var mn2;
				if (indexn3 > -1) {
					mn2 = ordersm_mn[indexn3];
				} else if (indexn2 > -1) {
					mn2 = order_mn[indexn2] + 1;
					order_mn[indexn2] = mn2;
					ordersm.push(orderno + "_" + smcode+ "_" +samplename);
					ordersm_mn.push(mn2);
				} else {
					mn2 = n + 1;
					order.push(orderno);
					order_mn.push(mn2);
					ordersm.push(orderno + "_" + smcode+ "_" +samplename);
					ordersm_mn.push(mn2);
				}
				var num = getNo(mn2, 0);
				tempcode = tempcode + c + num + "-" + getBioCodeNo(mn, 0);

				return tempcode;
			}
		}
	}
	//比对取对照
	var checkInitals = function (name, names, initals) {
		for (var i = 0; i < names.length; i++) {
			if (name == names[i]) {
				return initals[i];
			}
		}
		return "";
	}
	//文库流程号段(项目期号内)
	var getNo = function (num, ki) {
		num = num + ki;
		if (num < 10) {
			num = "000" + num;
			return num;
		}
		if (num >= 10 && num < 100) {
			num = "00" + num;
			return num;
		}
		if (num >= 100 && num < 1000) {
			num = "0" + num;
			return num;
		}
		return num;
	}
	//核酸号段(项目期号内)
	var getBioCodeNo = function (num) {
		if (num < 10) {
			num = "0" + num;
			return num;
		}
		return num;
	}


	var getLibToLetter = function () {
		var rows;
		$.fn.ajaxPost({
			ajaxUrl: "system/jdbc/query/one/table",
			ajaxType: "post",
			ajaxAsync: false,
			ajaxData: { "query": "queryBioLibTypeList" },
			succeed: function (rs) {
				rows = rs.rows;
			}
		});
		return rows

	}
	var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
		$.fn.ajaxPost({
			ajaxType: "post",
			ajaxUrl: urls,
			ajaxData: inobjjson,
			succeed: function (result) {
				if (result["code"] > 0) {
					if (isDoCallBack == "是") {
						funcExce(pathValue + "pageCallBack");
						funcExce(pathValue + "close");
					}
				} else {
					alertMsg(errMsg + "操作失败!");
				}
			}
		});
	}

	var submit = function () {
		subUpData();
	}

	funcPushs(pathValue, {
		"init": init,
		"submit": submit,
		"subUpData": subUpData
	});

});