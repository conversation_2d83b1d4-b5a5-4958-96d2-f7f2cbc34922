$(document).ready(function () {
    var pathValue = "biomarker-dispatch-pd-samplepooling-index";
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var gridNameD4Grid;
    var gridNameD5Grid;
    var gridNameD6Grid;
    var gridNameD7Grid;
    var gridNameD8Grid;
    var gridNameS = [];
    var gridNameS1 = [];
    var gridNameS2 = [];
    var gridNameS3 = [];
    var gridNameS4;
    //对同一任务单号进行文库产生计数器
    var ordersd = [];
    var order = [];
    var order_mn = [];
    var ordersm = [];
    var ordersm_mn = [];
    var ordersm_no = [];
    var ordersm_no_mn = [];
    //待排
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit", title: "生成执行单" },
            { name: "edit", target: "editP", title: "任务单补充" },
            { name: "edit", target: "upsmStatus", title: "修改建库状态" },
            { name: "edit", target: "addToEx", title: "追加任务到执行单" },
            { name: "edit", target: "doTaskStatus", title: "任务单状态修改" },
            { name: "excel", target: "importData1", title: "实验导入/模板" },
            { name: "edit", target: "setjira", title: "jira推送.." },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_BIO_TASK_LIB_list",
                "objects": [["已审核", "建库中", "测序中", "调度已审核", "加测"], ["混样建库", "SLAF"]], "search": { "TASK_LS_TYPE_LB": ["混样建库"] }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var type = e.data.TASK_LS_TYPE;
                var readsql = "query_BIO_TASK_LIBMX_list_pd_slaf";
                if (type == "SLAF") readsql = "query_BIO_TASK_LIBMX_list_pd_slaf";

                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: {
                        "query": readsql, "objects": [], "search": { "TASK_LS_ID": [ROW_ID] },
                        headerFilter: function (cols, i) {
                            if (i) {
                                if (cols[i]["field"] && cols[i]["field"] == "ISPOOLSM") {
                                    var template = "# if( ISPOOLSM=='混' ){ # <font onclick=\"funcExce('" + pathValue + "openpool','#= ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }" +
                                        " else if( ISPOOLSM=='拆' ){ # <font onclick=\"funcExce('" + pathValue + "openpool','#= BIO_SPLIT_ID #');\" style=\"color:blue;cursor:hand\"> #=ISPOOLSM# </font>  # }else{} #";
                                    setJsonParam(cols[i], "template", template);
                                }
                            }

                        },
                    },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        init1();
        init2();
        init3();
        init4();
        init5();
        init6();
        init7();
        init8();
    }
    //待审核
    var init1 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "ok", target: "doOK", title: "提交" },
            { name: "edit", target: "doGenNo", title: "生成文库编号" },
            { name: "ok", target: "doPooling1", title: "手动Pooling.." },
            { name: "ok", target: "doPooling2", title: "一键Pooling-SLAF.." },
            { name: "ok", target: "doPooling3", title: "一键Pooling-区域.." },
            { name: "edit", target: "doGetIndex", title: "分配index" },
            { name: "delete", target: "remove", title: "移除任务明细" },
            { name: "delete", target: "doDelete", title: "删除执行单" },
            { name: "edit", target: "doUpdate", title: "修改实验员" },


        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list",
                "objects": [["DNA混样建库-SLAF建库",
                    "PB混样-微生物全长",
                    "PB混样-全长转录组",
                    "PB混样-基因组",
                    "混样建库-ONT建库-DNA",
                    "DNA混样建库-MCD非简化建库",
                    "混样建库-ONT建库-Iso-RNA"],
                ["待审核", "接收退回"]]
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "queryTaskLibExMx";
                // if(e.data.EX_TYPE=="DNA混样建库-MCD非简化建库"
                //) readsql="queryTaskLibExMx-FMCD";
                if (e.data.EX_TYPE == "DNA混样建库-MCD非简化建库"
                ) readsql = "queryTaskLibExMx-Pb";
                if (e.data.EX_TYPE == "PB混样-微生物全长" ||
                    e.data.EX_TYPE == "PB混样-全长转录组" ||
                    e.data.EX_TYPE == "DNA混样建库-SLAF建库"
                ) readsql = "queryTaskLibExMx-Pb";
                if (e.data.EX_TYPE == "PB混样-基因组") readsql = "queryTaskLibExMx-PBjyz";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS1.push(subGrid_N);
            }


        };
        gridNameD1Grid = initKendoGrid("#gridNameD1Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    //已处理
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list",
                "objects": [["DNA混样建库-SLAF建库",
                    "PB混样-微生物全长",
                    "PB混样-全长转录组",
                    "PB混样-基因组",
                    "DNA混样建库-MCD非简化建库",
                    "混样建库-ONT建库-DNA",
                    "混样建库-ONT建库-Iso-RNA"],
                ["待接收", "已接收", "接收退回", "建库提交", "建库已审核", "建库中", "已完结", "暂停", "未建库", "建库终止"]], "search": { "EX_EXECUTE_MODE": "实验员" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "queryTaskLibExMx";
                if (e.data.EX_TYPE == "PB混样-微生物全长" ||
                    e.data.EX_TYPE == "PB混样-全长转录组" ||
                    e.data.EX_TYPE == "DNA混样建库-SLAF建库"
                ) readsql = "queryTaskLibExMx-Pb";
                if (e.data.EX_TYPE == "DNA混样建库-MCD非简化建库"
                ) readsql = "queryTaskLibExMx-FMCD";
                if (e.data.EX_TYPE == "PB混样-基因组") readsql = "queryTaskLibExMx-PBjyz";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }

    //3级导出
    var importData3 = function (componentId) {
        saveGridDataToExcel({ grid: gridNameS4, select: 1, expKey: "A" });
    }
    //完成
    var init3 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "移至待排单" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_BIO_TASK_LIB_list",
                "objects": [["结单", "暂停", "终止", "未建库", "建库完成"], ["混样建库", "SLAF"]],
                "search": { "TASK_LS_TYPE_LB": "混样建库" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var type = e.data.TASK_LS_TYPE;
                var readsql = "query_BIO_TASK_LIBMX_list";
                if (type == "SLAF") readsql = "query_BIO_TASK_LIBMX_list_slaf";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": readsql, "objects": [ROW_ID] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS3.push(subGrid_N);
            }
        };
        gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);

    }
    //待排任务
    var init4 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "editA", title: "智能排单" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_sample_pool_BIO_TASK_LIB_list",
                "objects": [],
                "search": { "TASK_LS_STATUS": ["已审核", "待建库"], "TASK_LS_TYPE": ["混样建库", "SLAF"], "TASK_LS_TYPE_LB": "混样建库" }
            },
            fetch: function (data) {
                gridNameS5 = data;
            }

        };
        gridNameD4Grid = initKendoGrid("#gridNameD4Grid" + pathValue, gridNameGridJson);
    }
    // 预处理
    var init5 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "checkSample", title: "核验" },
            { name: "edit", target: "submit", title: "提交" }

        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list", "objects": [["DNA混样建库-SLAF建库",
                    "PB混样-微生物全长",
                    "PB混样-全长转录组",
                    "PB混样-基因组",
                    "混样建库-ONT建库-DNA",
                    "DNA混样建库-MCD非简化建库",
                    "混样建库-ONT建库-Iso-RNA"],
                ["预处理"]]
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;

                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "queryTaskLibExMx-Check", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS5.push(subGrid_N);
            }
        };
        gridNameD5Grid = initKendoGrid("#gridNameD5Grid" + pathValue, gridNameGridJson);
    }
    // PE任务待处理
    var init6 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit", title: "提交" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list",
                "objects": [["DNA混样建库-SLAF建库",
                    "PB混样-微生物全长",
                    "PB混样-全长转录组",
                    "PB混样-基因组",
                    "DNA混样建库-MCD非简化建库",
                    "混样建库-ONT建库-DNA",
                    "混样建库-ONT建库-Iso-RNA"],
                ["待接收", "已接收", "接收退回", "建库提交", "建库已审核", "建库中", "已完结", "暂停", "未建库", "建库终止"]], "search": { "EX_EXECUTE_MODE": "PE" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "queryTaskLibExMx";
                if (e.data.EX_TYPE == "PB混样-微生物全长" ||
                    e.data.EX_TYPE == "PB混样-全长转录组" ||
                    e.data.EX_TYPE == "DNA混样建库-SLAF建库"
                ) readsql = "queryTaskLibExMx-Pb";
                if (e.data.EX_TYPE == "DNA混样建库-MCD非简化建库"
                ) readsql = "queryTaskLibExMx-FMCD";
                if (e.data.EX_TYPE == "PB混样-基因组") readsql = "queryTaskLibExMx-PBjyz";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS6.push(subGrid_N);
            }
        };
        gridNameD6Grid = initKendoGrid("#gridNameD6Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }
    // PE任务待处理
    var init7 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit1", title: "提交" },
            { name: "edit", target: "collection", title: "采集" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "lib_pd_SHEET_list",
                "objects": [["DNA混样建库-SLAF建库",
                    "PB混样-微生物全长",
                    "PB混样-全长转录组",
                    "PB混样-基因组",
                    "DNA混样建库-MCD非简化建库",
                    "混样建库-ONT建库-DNA",
                    "混样建库-ONT建库-Iso-RNA"],
                ["PE执行状态"]], "search": { "EX_EXECUTE_MODE": "PE" }
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var readsql = "queryTaskLibExMx";
                if (e.data.EX_TYPE == "PB混样-微生物全长" ||
                    e.data.EX_TYPE == "PB混样-全长转录组" ||
                    e.data.EX_TYPE == "DNA混样建库-SLAF建库"
                ) readsql = "queryTaskLibExMx-Pb";
                if (e.data.EX_TYPE == "DNA混样建库-MCD非简化建库"
                ) readsql = "queryTaskLibExMx-FMCD";
                if (e.data.EX_TYPE == "PB混样-基因组") readsql = "queryTaskLibExMx-PBjyz";
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: "",
                    height: 320,
                    read: { "query": readsql, "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS7.push(subGrid_N);
            }
        };
        gridNameD7Grid = initKendoGrid("#gridNameD7Grid" + pathValue, gridNameGridJson);//初始化表格的方法
    }



    //异常任务
    var init8 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "editR", title: "生成执行单" },
            { name: "edit", target: "addToExRe", title: "追加任务到执行单" },
            { name: "edit", target: "editZZ", title: "终止" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "ib_test_qc_all_task_sh_list_abo2",
                "objects": [["重建库", "重新纯化", "重建库验证", "重建库加测"]]
            },
        };
        gridNameD8Grid = initKendoGrid("#gridNameD8Grid" + pathValue, gridNameGridJson);

    }

    //排单生成
    var edit = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //判断类型是否全部分同一为,并取出形成类型单
        var g = arrIds;
        var a = "";
        var b = "";
        var code = [];

        for (var i = 0; i < g.length; i++) {
            var BIOCODE = g[i]["BIO_CODE"];
            if ("" != BIOCODE && null != BIOCODE) {
                if (code.indexOf(g[i]["BIO_CODE"]) > -1) {//同一个执行单不允许重复
                    //alertMsg("提示:存在所选编号“"+g[i]["BIO_CODE"]+"重复!”");
                    //return;
                } else {
                    code.push(g[i]["BIO_CODE"]);
                }
            }

            if (i == 0) {
                a = g[i]["LIBRARY_FLOW"];
                b = g[i]["LIBRARY_FLOW"];
            } else {
                a = g[i - 1]["LIBRARY_FLOW"];
                b = g[i]["LIBRARY_FLOW"];
            }

            if (a != b) {
                alertMsg("存在所选记录建库流向“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
                return;
            }

        }
        var ids = [];
        var taskids = [];
        var biocodes = [];
        var samplecodes = [];
        var libtypes = [];
        var libtypemxs = [];
        var indexDatas = [];
        var indexnubers = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            libtypemxs.push(arrIds[i]["LIBRARY_TYPE_EN"]);
            biocodes.push(arrIds[i]["BIO_CODE"]);
            samplecodes.push(arrIds[i]["SAMPLE_CODE"]);
            if (libtypes.indexOf(arrIds[i]["LIBRARY_TYPE_EN"]) < 0) {
                libtypes.push(arrIds[i]["LIBRARY_TYPE_EN"]);//文库类型
            }
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
            indexDatas.push({
                "INDEX_NAME": arrIds[i]["INDEX_NAME_"],
                "SEQ_I7_NAME": arrIds[i]["SEQ_I7_NAME_"],
                "SEQ_I5_NAME": arrIds[i]["SEQ_I5_NAME_"],
                "SEQ_I5_2": arrIds[i]["SEQ_I5_2_"],
                "SEQ_I7_1": arrIds[i]["SEQ_I7_1_"],
                "SEQ_I5_1": arrIds[i]["SEQ_I5_1_"],
                "SEQ_ODBY": arrIds[i]["SEQ_ODBY_"],
                "SEQ_CELL": arrIds[i]["SEQ_CELL_"],
            });
            if (a == "PB混样-微生物全长") {
                if (indexnubers.indexOf(arrIds[i]["INDEX_NAME_"]) > -1) {
                    var a = indexnubers.indexOf(arrIds[i]["INDEX_NAME_"]);
                    alertMsg("提示:存在index冲突:<br />" + arrIds[i]["INDEX_NAME_"] + ":" + arrIds[i]["TASK_LS_NO"] + "，" + arrIds[a]["TASK_LS_NO"] + "!");
                    return;
                }
                indexnubers.push(arrIds[i]["INDEX_NAME_"]);
            }
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/pdup/pdup",
            title: "混样建库排单.."
        };
        openWindow(winOpts, {
            "IDS": ids,
            "TASKIDS": taskids,
            "SAMPLECODES": samplecodes,
            "BIOCODES": biocodes,
            "LIBTYPES": libtypes,
            "LIBTYPEMXS": libtypemxs,
            "EX_TYPE": a,
            "indexDatas": indexDatas
        });
    }
    //采集
    var collection = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameD8Grid);
        var inobjjson;
        if (arrIds.length > 0) {
            var TaskNos = [];
            for (var i = 0; i < arrIds.length; i++) {
                TaskNos.push(arrIds[i]["EX_DH_NO"]);
            }
            inobjjson = { "TaskNos": TaskNos, "Way": "手动" }
        } else {
            inobjjson = { "Way": "手动" }
        }

        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/peCollection",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson
        });

    }

    //自动化排单
    var editA = function () {

        var gridData = getGridSelectData(gridNameD4Grid);
        debugger;
        if (gridData.length == 0) {
            alertMsg("至少选择一个样本");
            return;

        }

        if (gridData.length > 999) {
            alertMsg("单次只能排小于1000个样本！");
            return;
        }


        var params = { "ids": [], "keys": [] };

        for (var j = 0; j < gridData.length; j++) {
            var id = gridData[j]["ID"];
            var lfo2 = gridData[j]["LIBRARY_FLOW"]; //工序
            var wor2 = gridData[j]["MEHOD_JKPLAT"]; //样品执行组
            var met2 = gridData[j]["LIBRARY_METHOD"]; //样品执行方法
            var lte2 = gridData[j]["LIBRARY_TYPE_EN"]; //文库类型
            params.ids.push(id);
            if (params.keys.indexOf(lfo2 + "-" + wor2 + "-" + met2) < 0) {
                params.keys.push(lfo2 + "-" + wor2 + "-" + met2);
                params[lfo2 + "-" + wor2 + "-" + met2] = {
                    lfo: lfo2,
                    met: met2,
                    wor: wor2,
                    lte: lte2,
                    ids: []
                };

            }
            params[lfo2 + "-" + wor2 + "-" + met2].ids.push(id);

        }

        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/automate/automate",
            title: "自动排单明细.."
        };
        openWindow(winOpts, params);//传递

    }
    //样品核验
    var checkSample = function () {
        var arrIds = getGridSelectData(gridNameD5Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行核验操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/check/check",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递
    }
    //追加任务
    var addToEx = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/addtoex/addtoex",
            title: "追加样本到执行单.."
        };
        var ids = [];
        var codes = [];
        var indexDatas = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            codes.push(arrIds[i]["BIO_CODE"]);
            indexDatas.push({
                "INDEX_NAME": arrIds[i]["INDEX_NAME_"],
                "SEQ_I7_NAME": arrIds[i]["SEQ_I7_NAME_"],
                "SEQ_I5_NAME": arrIds[i]["SEQ_I5_NAME_"],
                "SEQ_I5_2": arrIds[i]["SEQ_I5_2_"],
                "SEQ_I7_1": arrIds[i]["SEQ_I7_1_"],
                "SEQ_I5_1": arrIds[i]["SEQ_I5_1_"],
                "SEQ_ODBY": arrIds[i]["SEQ_ODBY_"],
                "SEQ_CELL": arrIds[i]["SEQ_CELL_"],
            });
        }

        openWindow(winOpts, { "IDS": ids, "CODES": codes, "indexDatas": indexDatas });
    }

    //表格导入
    var importData1 = function (componentId) {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var exid = [];
        var ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            exid.push(arrIds[i]["EXID"]);
        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_DNA_RNA_QC",
                        requestData: {
                            ajaxData: { "query": "query_DO_QC_DNA_MCD_list", "size": 5000, "objects": [], "search": { "ID": ids, "EXE_TQQC_ID": exid } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }
    //预处理提交
    var submit = function () {
        var arrIds = getGridSelectData(gridNameD5Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }

        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: { "query": "queryTaskLibExMx-Check", "objects": [[arrIds[0]["ID"]]] },
            succeed: function (rs) {
                sample = rs.rows;             //样品
                var num = 0;
                for (var j = 0; j < sample.length; j++) {
                    if (sample[j]["JK_CHECK"] == "OK") {
                        num = num + 1;

                    }
                }
                if (num < sample.length) {
                    alertMsg("还有" + (sample.length - num) + "条未核验，不能提交");
                    return;
                }
                var objectSheet = [];
                objectSheet.push({
                    "ID": arrIds[0]["ID"],//id
                    "EX_RE_STATUS": "待审核"       //状态

                });
                var urlsend = "system/jdbc/save/batch/table";
                var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
                putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
            }
        });

    }
    //PE任务待处理提交
    var pesubmit = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameD6Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行提交!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行提交!");
            return;
        }
        if (arrIds[0]["PLATE_CODE"] == null) {

            alertMsg("执行单" + arrIds[0]["EX_DH_NO"] + "尚未分配板孔号，不能提交");
            return;
        }
        var PEtoken;
        var PEVariable = { "ClientName": PE_ClientName, ClientPwd: ClientPwd };
        var inobjjson = { "url": PE_URL + "api/clientInfo/login", "PEVariable": PEVariable }
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                PEtoken = rs.apiData.result.token;
            }
        });
        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "queryTaskLibExMx", "objects": [[arrIds[0]["ID"]]] },
            succeed: function (rs) {
                sample = rs.rows;             //样品
            }
        });
        var PEsamples = [];
        for (var j = 0; j < sample.length; j++) {
            PEsamples.push({
                "Well": sample[j]["PLATE_WELL"],
                "SampleNo": sample[j]["SAMPLE_CODE"],
                "LibraryType": sample[j]["LIBRARY_TYPE_EN"]
            });
        }
        time = sysNowTimeFuncParams["sysNowTime"];
        var PEVariable = {
            "TimeStamp": time,
            "Token": PEtoken,
            "ClientId": "",
            "Cmd": "RNASeq",
            "RQData": {
                "TaskNo": arrIds[0]["EX_DH_NO"],
                "BarCode": arrIds[0]["PLATE_CODE"],
                "Samples": PEsamples,
                "IndexBarCode": arrIds[0]["PLATE_CODE"]
            }
        };
        var inobjjson = { "url": PE_URL + "api/order/create", "PEVariable": PEVariable }
        var RValue;
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                RValue = rs;
            }
        });
        if (!RValue.apiData.success) {
            alertMsg(RValue.apiData.msg);
            return;
        }
        var objectSheet = [];
        objectSheet.push({
            "ID": arrIds[0]["ID"],//id
            "EX_RE_STATUS": "PE执行状态"       //状态
        });
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
        putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
    }
    //表格导入
    var importData1 = function (componentId) {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var exid = [];
        var ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            exid.push(arrIds[i]["EXID"]);
        }

        openComponent({
            name: "导入数据",//组件名称
            componentId: componentId,
            params: {
                template: function (p, n) {
                    return exportAndImportData({
                        expKey: "A",
                        tableName: "BIO_TASK_LIBMX ",
                        requestData: {
                            ajaxData: { "query": "query_BIO_TASK_LIBMX_list_pd_slaf", "size": 5000, "objects": [], "search": { "ID": ids, "EXE_TQQC_ID": exid } },
                        },
                        params: p,
                        name: n,
                    });
                }
            },
            callBack: refreshGrid
        });
    }



    //文编号
    var doGenNo = function () {
        debugger;
        ordersd = [];
        order = [];
        order_mn = [];
        ordersm = [];
        ordersm_mn = [];
        ordersm_no = [];
        ordersm_no_mn = [];
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        //取对应表
        var params = { "query": "queryBioLibTypeList", "objects": [] };
        var iniFist = "";
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var libtypename = [];
                    var initials = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        libtypename.push(row["LIB_TYPE_NAME"]);
                        initials.push(row["INITIALS"]);
                    }
                    var objectup = [];
                    for (var i = 0; i < g.length; i++) {
                        //更新记录
                        iniFist = checkInitals(g[i]["LIBRARY_TYPE_EN"], libtypename, initials);
                        if (g[i]["LIBRARY_CODE"]) {
                            alertMsg("文库编号“" + g[i]["LIBRARY_CODE"] + "”已存在!");
                        } else {
                            objectup.push({
                                "ID": g[i]["LIBID"],//关联更新ID
                                "LIBRARY_CODE": getLibCodeNo(g[i]["TASK_LS_NO"], iniFist, g[i]["DON"], g[i]["LIBMAXCODE"], g[i]["BIO_CODE"], g[i]["SAMPLE_CODE"] + g[i]["TASK_LSSQ_ENZEM"], g[i]["DATA_LIBCODE"])
                            });
                        }
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "提交");

                }
            }
        });

    }

    var getLibCodeNo = function (orderno, c, n, maxcode, biocode, smcode, datalibcode) {
        // if (maxcode) {//已生成过文库
        //     var a = maxcode.split("-");
        //     var mn = parseInt(a[a.length - 1]) + 1;//取最后的数
        //     //重新接回形成新的编号
        //     var tempcode = a[0];
        //     for (var i = 1; i < a.length - 1; i++) {
        //         tempcode = tempcode + "-" + a[i];
        //     }
        //     tempcode = tempcode + "-" + getBioCodeNo(mn);
        //     return tempcode;
        // } else {//没有生成过文库
        //     var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
        //     var indexn = order.indexOf(orderno);
        //     var mn = 1;
        //     if (indexn > -1) {
        //         mn = ordermn[indexn] + 1;
        //         ordermn[indexn] = mn;
        //     } else {
        //         order.push(orderno);
        //         ordermn.push(n + 1);
        //         mn = n + 1;
        //     }
        //     var num = getNo(mn, 0);
        //     if (biocode.indexOf("SD") > -1) { num = SD; }
        //     tempcode = tempcode + c + num + "-01";
        //     return tempcode;
        // }
        if (maxcode) {//已生成过文库
            var maxcode_1 = maxcode.substr(0, maxcode.lastIndexOf("-") + 1);
            var maxcode_2 = maxcode.replace(maxcode_1, "");
            maxcode_2 = parseInt(maxcode_2);
            var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
            var mn;
            if (indexn > -1) {
                mn = ordersm_no_mn[indexn] + 1;
                ordersm_no_mn[indexn] = mn;
            } else {
                mn = maxcode_2 + 1;
                ordersm_no.push(orderno + "_" + smcode + "_no");
                ordersm_no_mn.push(mn);
            }
            return maxcode_1 + getBioCodeNo(mn, 0);
        } else {//没有生成过文库
            if (datalibcode) { //运营提前生产
                var maxcode_1 = datalibcode.substr(0, datalibcode.lastIndexOf("-") + 1);
                var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
                var mn;
                if (indexn > -1) {
                    mn = ordersm_no_mn[indexn] + 1;
                    ordersm_no_mn[indexn] = mn;
                } else {
                    mn = 1;
                    ordersm_no.push(orderno + "_" + smcode + "_no");
                    ordersm_no_mn.push(mn);
                }
                return maxcode_1 + getBioCodeNo(mn, 0);
            } else {//运营没有提前生产
                var tempcode = orderno.substr(1, orderno.length - 1);;//去除第一位字符
                var indexn = ordersm_no.indexOf(orderno + "_" + smcode + "_no");
                var mn;
                if (indexn > -1) {
                    mn = ordersm_no_mn[indexn] + 1;
                    ordersm_no_mn[indexn] = mn;
                } else {
                    mn = 1;
                    ordersm_no.push(orderno + "_" + smcode + "_no");
                    ordersm_no_mn.push(mn);
                }
                var indexn2 = order.indexOf(orderno);
                var indexn3 = ordersm.indexOf(orderno + "_" + smcode);
                var mn2;
                if (indexn3 > -1) {
                    mn2 = ordersm_mn[indexn3];
                } else if (indexn2 > -1) {
                    mn2 = order_mn[indexn2] + 1;
                    order_mn[indexn2] = mn2;
                    ordersm.push(orderno + "_" + smcode);
                    ordersm_mn.push(mn2);
                } else {
                    mn2 = n + 1;
                    order.push(orderno);
                    order_mn.push(mn2);
                    ordersm.push(orderno + "_" + smcode);
                    ordersm_mn.push(mn2);
                }
                if (biocode.indexOf("SD") > -1) {
                    mn2 = ordersd.length + 1;
                    var num = getBioCodeNo(mn2, 0);
                    tempcode = tempcode + c + "SD" + num + "-01";
                    ordersd.push(tempcode)
                } else {
                    var num = getNo(mn2, 0);
                    tempcode = tempcode + c + num + "-" + getBioCodeNo(mn, 0);
                }
                return tempcode;
            }
        }

    }

    //比对取对照
    var checkInitals = function (name, names, initals) {
        for (var i = 0; i < names.length; i++) {
            if (name == names[i]) {
                return initals[i];
            }
        }
        return "";
    }


    //修改实验员
    var doUpdate = function () {
        var arrIds = getGridSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/updateman/updateman",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"], "EX_MAN": arrIds[0]["EX_MAN"], "EX_EXECUTE_MODE": arrIds[0]["EX_EXECUTE_MODE"] });//传递
    }

    //文库流程号段(项目期号内)
    var getNo = function (num, ki) {
        num = num + ki;
        if (num < 10) {
            num = "000" + num;
            return num;
        }
        if (num >= 10 && num < 100) {
            num = "00" + num;
            return num;
        }
        if (num >= 100 && num < 1000) {
            num = "0" + num;
            return num;
        }
        return num;
    }
    //核酸号段(项目期号内)
    var getBioCodeNo = function (num) {
        if (num < 10) {
            num = "0" + num;
            return num;
        }
        return num;
    }
    var editP = function () {
        var arrIds = getSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/editP/editP",
            title: "补充任务单计划.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    }

    var doOK = function () {

        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        if (arrIds.length > 1) {
            alertMsg("请最多选择一条记录进行操作!");
            return;
        }
        //校验文库编号是否为空
        var s = "";
        var si = "";
        var s2 = "";
        var params = { "query": "doCheckLibCodeIsPass", "objects": [arrIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];

                        if (row["EX_TYPE"] == "三代ONT基因组建库") {//判断切胶编号
                            if (row["JK_QJ_CODE"] == "" || row["JK_QJ_CODE"] == null) {
                                if (s == "") {
                                    s = row["EX_DH_NO"];
                                }
                            }
                        } else if (row["EX_TYPE"] == "DNA混样建库-SLAF建库") {//混库编号
                            if (row["POOL_CODE"] == "" || row["POOL_CODE"] == null) {
                                if (s2 == "") {
                                    s2 = row["EX_DH_NO"];
                                }
                            }
                        } else {
                            if (row["LIBRARY_CODE"] == "" || row["LIBRARY_CODE"] == null) {
                                if (s == "") {
                                    s = row["EX_DH_NO"];
                                }
                            }
                            if (row["INDEX_NAME"] == "" || row["INDEX_NAME"] == null) {
                                if (si == "") {
                                    si = row["EX_DH_NO"];
                                }
                            }
                        }

                    }
                    if (s2 != "") {
                        alertMsg("提示:单号“" + s + "”存在混库编号为空!");
                        return;
                    }
                    if (s != "") {
                        alertMsg("提示:单号“" + s + "”存在文库编号(或切胶编号为空--ONT)为空!");
                        return;
                    }
                    if (si != "") {
                        alertMsg("提示:单号“" + si + "”存在“index名称”为空!");
                        // return;
                    }
                    var objectup = [];
                    for (var i = 0; i < arrIds.length; i++) {
                        var time = sysNowTimeFuncParams["sysNowTime"];
                        var username = getLimsUser()["name"];
                        objectup.push({
                            "ID": arrIds[i],//联联任务ID
                            "EX_RE_STATUS": "待接收"
                        });
                    }
                    if (rows[0]["EX_MAN"] == "实验-委外") {
                        objectup.push({
                            "ID": rows[0]["ID"],//联联任务ID
                            "EX_RE_STATUS": "已提交委外"
                        });
                        var urlsend = "system/jdbc/save/batch/table";
                        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
                        putAddOrUpdata(urlsend, paramsup, "是", "提交");
                        return;
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "提交");
                    doRequeDoUpTaskLibmxStatus(arrIds, "建库待接收");
                }
            }
        });
    }

    //任务单-对应执行单下明细状态修改
    var doRequeDoUpTaskLibmxStatus = function (mainExIds, status) {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var params = { "query": "doRequeDoUpTaskLibmxStatus", "objects": [mainExIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        //更新记录---明细
                        objectup.push({
                            "ID": row["TASKLIBMXID"],
                            "TASK_LSMX_STATUS": status
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_TASK_LIBMX", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "同步更新任务明细:");
                }
            },
            failed: function (result) {
                alertMsg("提示:操作异常!", "error");
            }
        });
    }

    //撤回
    var doReturn = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        var arrIds = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["EX_RE_STATUS"] != "待接收") {
                alertMsg("操作失败,所选记录存在已“已接收”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "EX_RE_STATUS": "待审核"
                });
            }
            arrIds.push(g[i]["ID"]);
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
        doRequeDoUpTaskLibmxStatus(arrIds, "草稿");

    }
    //移至待审核
    var doReturn2 = function () {
        var g = getGridSelectData(gridNameD3Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }


        var username = getLimsUser()["name"];
        if (username != "蓝勇胜" && username != "徐彦岭"&& username != "王宏"&& username != "冯璨"&& username != "邵明艳"&& username != "杨玉平"&& username != "曹利群") {
            alertMsg("无此权限!请联系徐彦岭!");
            return;
        }



        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            objectup.push({
                "ID": g[i]["ID"],
                "TASK_LS_STATUS": "已审核"
            });
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_TASK_LIB", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }

    //任务单状态修改
    var doTaskStatus = function () {
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行审核操作!");
            return;
        }
        var ids = [];
        lsmkeyps = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            lsmkeyps.push(arrIds[i]["LSM_KEY_P"]);
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/uptaskstatus/uptaskstatus",
            title: "修改任务单状态.."
        };
        openWindow(winOpts, { "arrIds":arrIds, "IDS": ids, "LSMKEYP": lsmkeyps });
    }
    //样本状态修改
    var upsmStatus = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/upsmstatus/upsmstatus",
            title: "修改样本状态.."
        };
        openWindow(winOpts, { "IDS": arrIds });
    }

    //记录移除
    var remove = function () {
        var arrg = [];
        var arrIds = [];
        var obj = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            arrg = arrg.concat(arrSubID);

        }
        if (arrg.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        for (var i = 0; i < arrg.length; i++) {
            arrIds.push(arrg[i]["LIBID"]);
            obj.push({ "ID": arrg[i]["ID"], "TASK_LSMX_STATUS": null });
        }
        confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd1 = { "tableName": "BIO_TASK_LIBMX", "objects": obj };
            putAddOrUpdata(urlsend, paramsadd1, "否", "移除");
            var params = { "tableName": "BIO_LIB_INFO", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        });
    }
    //删除执行单
    var doDelete = function () {
        var arrIds = getSelectData(gridNameD1Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
            var url = "system/jdbc/delete/one/table/where";
            var params1 = { "tableName": "BIO_LIB_INFO", "where": { "EXE_TQQC_ID": arrIds } };
            var params2 = { "tableName": "bio_lib_pooling", "where": { "EXE_TQQC_ID": arrIds } };
            deleteGridDataByIds(url, params1, null);
            deleteGridDataByIds(url, params2, null);
            var params3 = { "tableName": "EXE_TQQC_SHEET", "where": { "ID": arrIds } };
            deleteGridDataByIds(url, params3, refreshGrid);
        });
    }
    //手动Pooling
    var doPooling1 = function () {
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var ids = [];
        var exid = "";
        var taskid = "";
        var EX_TYPE = "";
        for (var i = 0; i < g.length; i++) {
            ids.push(g[i]["LIBID"]);
            if (i == 0) {
                exid = g[i]["EXID"];
            }
            if (exid != g[i]["EXID"]) {
                alertMsg("不允许跨单Pool!!");
                return;
            }
            exid = g[i]["EXID"];
            taskid = g[i]["TASKLIBID"];
            EX_TYPE = g[i]["EX_TYPE"];
        }

        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/pooling1/pooling1",
            title: "手动pooling.."
        };
        openWindow(winOpts, { "IDS": ids, "EXID": exid, "TASKID": taskid, "EX_TYPE": EX_TYPE });
    }
    //自动Pooling--SLAF
    var doPooling2 = function () {
        debugger;
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //以组合方案确定有几个pool
        var poolnames = [];
        var poolcodes = [];
        var poolids = [];
        var exid = "";
        var taskid = [];
        var tasknotemp = "";
        var tasknumberIndex = [];
        var tasknonumber = 0;
        var tasknos = [];
        var taskids = [];

        for (var i = 0; i < g.length; i++) {
            var row = g[i];
            exid = row["EXID"];
            taskid = row["TASKLIBID"];
            var tempname = row["TASK_LSSQ_ENZEM"];//一个组合分酸一个pool_code
            tasknotemp = row["TASK_LS_NO"];
            tasknonumber = parseInt(row["POOLNUMBER"]);
            if (tempname) {
                if (poolnames.includes(tempname) == false) {
                    poolids.push(getRandomId());
                    poolnames.push(tempname);
                    var addnum = parseInt(tasknonumber + 1);
                    var addnumstr = getBioCodeNo(addnum);
                    if (tasknos.includes(tasknotemp) == false) {
                        taskids.push(taskid);
                        poolcodes.push(tasknotemp + "F" + addnumstr);
                        tasknumberIndex.push(addnum);
                        tasknos.push(tasknotemp);
                    } else {
                        taskids.push(taskid);
                        var indexn = tasknos.indexOf(tasknotemp);
                        var n = parseInt(tasknumberIndex[indexn] + 1);
                        addnumstr = getBioCodeNo(n);
                        poolcodes.push(tasknotemp + "F" + addnumstr);
                        tasknumberIndex[indexn] = n;
                    }
                }
            } else {
                alertMsg("提示:酶切组合,列存在空值!");
                return;
            }
        }
        debugger;
        var objectupmx = []
        var objaddpool = [];
        var objectlibpool = [];
        for (var i = 0; i < poolnames.length; i++) {
            var n = 0;
            for (var j = 0; j < g.length; j++) {
                var row = g[j];
                if (poolnames[i] == row["TASK_LSSQ_ENZEM"]) {
                    n++;
                    objectupmx.push({//BIO_LIB_INFO
                        "ID": row["LIBID"],
                        "POOL_ID": poolids[i],
                        "POOL_ID1": poolids[i],
                        "ISPOOL_TWO": "待排"
                    });
                }

            }
            objectlibpool.push({
                "ID": poolids[i],
                "LIBRARY_CODE": poolcodes[i],
                "POOL_CODE": poolcodes[i]
            });

            objaddpool.push({//BIO_LIB_POOLING
                "ID": poolids[i],
                "EXE_TQQC_ID": exid,
                "TASK_ID": taskids[i],
                "POOL_CODE": poolcodes[i],
                "POOL_TYPE": "DNA混样建库-SLAF建库",
                "POOL_STATUS": "草稿",
                "POOL_LIB_NUM": n,
                "SEQ_PLAT": "NGS",
                "POOL_MAN": username,
                "POOL_B_TIME": time,
                "SYS_MAN": username,
                "SYS_INSERTTIME": time
            });

        }//end for 1

        //生成
        var urlsend = "system/jdbc/save/batch/table";
        var paramslib = { "tableName": "BIO_LIB_INFO", "objects": objectupmx };
        putAddOrUpdata(urlsend, paramslib, "否", "更新");

        var addurl = "system/jdbc/save/one/table/objects";
        var paramslibpool = { "tableName": "BIO_LIB_INFO", "objects": objectlibpool };
        putAddOrUpdata(addurl, paramslibpool, "否", "更新");

        var paramsaddpool = { "tableName": "BIO_LIB_POOLING", "objects": objaddpool };
        putAddOrUpdata(addurl, paramsaddpool, "是", "添加pool");


    }


    //自动Pooling--区
    var doPooling3 = function () {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var gtwo = getGridSelectData(gridNameD1Grid);
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        if (gtwo.length == 0) {
            alertMsg("请至少选择一条主单记录进行操作!");
            return;
        }
        //以组合方案确定有几个pool
        var EX_DH_NO = gtwo[0]["EX_DH_NO"]
        var poolnames = [];
        var poolids = [];
        var exid = "";
        var taskid = "";
        var poolcodes = [];
        var tasknotemp = "";
        var tasknumberIndex = [];
        var tasknonumber = 0;
        var tasknos = [];

        for (var i = 0; i < g.length; i++) {
            var row = g[i];
            exid = row["EXID"];
            taskid = row["TASKLIBID"];
            tasknotemp = row["TASK_LS_NO"];
            tasknonumber = parseInt(row["POOLNUMBER"]);
            var tempname = row["AMPLIFY_REGIONAL"];

            if (tempname) {
                if (poolnames.includes(tempname) == false) {
                    poolids.push(getRandomId());
                    poolnames.push(tempname);

                    var addnum = parseInt(tasknonumber + 1);
                    var addnumstr = getBioCodeNo(addnum);
                    if (tasknos.includes(tasknotemp) == false) {

                        poolcodes.push(EX_DH_NO + "-" + tempname);
                        tasknumberIndex.push(addnum);
                        tasknos.push(tasknotemp);
                    } else {
                        var indexn = tasknos.indexOf(tasknotemp);
                        var n = parseInt(tasknumberIndex[indexn] + 1);
                        addnumstr = getBioCodeNo(n);
                        poolcodes.push(EX_DH_NO + "-" + tempname);
                        tasknumberIndex[indexn] = n;
                    }
                }
            } else {
                alertMsg("提示:扩增区域,列存在空值!");
                return;
            }
        }
        var objectupmx = []
        var objaddpool = [];
        var objectlibpool = [];

        for (var i = 0; i < poolnames.length; i++) {
            var n = 0;
            for (var j = 0; j < g.length; j++) {
                var row = g[j];
                if (poolnames[i] == row["AMPLIFY_REGIONAL"]) {
                    n++;
                    objectupmx.push({//BIO_LIB_INFO
                        "ID": row["LIBID"],
                        "POOL_ID": poolids[i],
                        "POOL_ID1": poolids[i],
                        "ISPOOL_TWO": "待排"
                    });
                }

            }
            objectlibpool.push({
                "ID": poolids[i],
                "LIBRARY_CODE": poolcodes[i],
                "POOL_CODE": poolcodes[i]
            });
            objaddpool.push({//BIO_LIB_POOLING
                "ID": poolids[i],
                "EXE_TQQC_ID": exid,
                "TASK_ID": taskid,
                "POOL_CODE": poolcodes[i],
                "POOL_TYPE": "DNA混样建库",
                "POOL_STATUS": "草稿",
                "POOL_LIB_NUM": n,
                "SEQ_PLAT": "PB",
                "ISPOOL": "否",
                "POOL_MAN": username,
                "POOL_B_TIME": time,
                "SYS_MAN": username,
                "SYS_INSERTTIME": time
            });

        }//end for 1

        //生成
        var urlsend = "system/jdbc/save/batch/table";
        var paramslib = { "tableName": "BIO_LIB_INFO", "objects": objectupmx };
        putAddOrUpdata(urlsend, paramslib, "否", "更新");

        var addurl = "system/jdbc/save/one/table/objects";
        var paramslibpool = { "tableName": "BIO_LIB_INFO", "objects": objectlibpool };
        putAddOrUpdata(addurl, paramslibpool, "否", "更新");

        var paramsaddpool = { "tableName": "BIO_LIB_POOLING", "objects": objaddpool };
        putAddOrUpdata(addurl, paramsaddpool, "是", "添加pool");

    }


    //分派index
    var doGetIndex = function () {
        var g = [];
        for (var i = 0; i < gridNameS1.length; i++) {
            var arrSubID = getGridSelectData(gridNameS1[i]);
            if (arrSubID.length != 0) {
                g = g.concat(arrSubID);
            }
        }
        if (g.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //检查是不是为同一文库类型
        var libtype = "";
        for (var i = 0; i < g.length; i++) {
            if (i == 0) libtype = g[i]["LIBRARY_TYPE_EN"];
            if (libtype != g[i]["LIBRARY_TYPE_EN"]) {
                alertMsg("提示:无法分配,存在不同的文库类型(<font color=#ff0000>" + libtype + "与" + g[i]["LIBRARY_TYPE_EN"] + "</font>)!");
                return;
            }
        }

        var params = { "INDEX_TYPE": libtype, "INDEX_NUMBER": g.length };
        $.fn.ajaxPost({
            ajaxUrl: "bio/lib/modular/listIndex",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] == 1) {
                    var rows = result["INDEX"];
                    var objectup = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        objectup.push({
                            "ID": g[i]["LIBID"],//关联更新ID
                            "INDEX_NAME": row["INDEX_NAME"],//Index名称
                            "SEQ_I7_1": row["SEQ_I7_1"],//I7端(或正向)
                            "SEQ_I7_2": row["SEQ_I7_2"],//I7端2
                            "SEQ_I7_3": row["SEQ_I7_3"],//I7端3
                            "SEQ_I7_4": row["SEQ_I7_4"],//I7端4
                            "SEQ_I5_NAVA": row["SEQ_I5_NAVA"],//I5端-Nova(或反向)
                            "SEQ_I5_XTEN": row["SEQ_I5_XTEN"],//I5端-Xten
                            "GEN_N": row["GEN_N"],//错位碱基数
                            "ER_SEQ": row["ER_SEQ"],//错位碱基序列
                            "LIB_KIT": row["LIB_KIT"]//建库试剂盒
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_LIB_INFO", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "更新index");
                } else {
                    alertMsg("提示:无法分配,请检查“" + libtype + "”类型的index库是否维护!</font>)!");
                }
            }
        });

    }


    var setjira = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length != 1) {
            alertMsg("请选择一条数据进行操作!");
            return;
        }

        //新增jira字段220602，周期计算 var rows1;
        var url = "query_BIO_TASK_LIBMX_list_pd_slaf";
        if (arrIds[0]["TASK_LS_TYPE"] == "SLAF") url = "query_BIO_TASK_LIBMX_list_pd_slaf";
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": url, "objects": [], "search": { "TASK_LS_ID": [arrIds[0]["ID"]] } },
            succeed: function (rs) {
                //console.log(rs);				
                rows1 = rs["rows"];
            }
        });

        var THE_DATA_SUM = 0;
        var type;
        for (var i = 0; i < rows1.length; i++) {
            THE_DATA_SUM += rows1[i]["DATA_SUM"];
            type = rows1[i]["LIBRARY_TYPE_EN"];
        }
        var dwtype = rows1[0]["DATA_UNIT"];
        var cyc_dws = rows1[0]["CYC_DW"];
        var business_unit = rows1[0]["BUSINESS_UNIT"];

        doCyc(type, rows1.length, THE_DATA_SUM, "测序标准用时", dwtype, cyc_dws, business_unit);

        //doCyc(type, rows1.length, THE_DATA_SUM, "实验交付标准用时", dwtype, cyc_dws, business_unit);
        var p = arrIds[0];
        var time = Date.parse(sysNowTimeFuncParams["sysNowTime"]);
        var customield_10227 = time + (cxdates * 86400000);
        //var customield_10226 = time + (sydates * 86400000);



        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/jira/jira",
            title: "jira信息填写..",
            width: 1280,
            height: 480,
            position: { "top": 100, "left": 30 }
        };

        var p = arrIds[0];
        openWindow(winOpts, {
            "ID": p["LSMID"],
            "MAIN_ID": p["ID"],
            "LSM_KEY": p["LSM_KEY"],//LSM关键字
            "LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字
            "CUSTOMFIELD_12101": p["TASK_LS_LDATE"],//建库标准结单日期
            "CUSTOMFIELD_12100": p["TASK_LS_CDATE"],//建库测序任务单下达日期
            "CUSTOMFIELD_14201": p["TASK_TEST_DELIVERDATE"],//建库计划完成日期
            "CUSTOMFIELD_14712": p["TASK_LS_CDATE"],//酶切方案下达日期
            "CUSTOMFIELD_10227": customield_10227,//测序标准完成日期
            "CUSTOMFIELD_14202": customield_10227,//测序计划完成日期
            "CUSTOMFIELD_15530": p["TASK_LS_CDATE"],//预实验SLAF酶切方案下达日期
        });

    }
    function getRandomId() {
        return 'FDSX-SLAF-POOL-' + new Date().getTime().toString(36) + Math.random().toString(36).slice(2);
    };

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    var openpool = function (ID) {
        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/poolsamlist/poolsamlist",
            title: "混样列表...",
            width: 900,
            height: 380,
            currUrl: replacePathValue(pathValue),
            position: { "top": 150, "left": 250 }
        };
        openWindow(winOpts, { "ID": ID });
    }

    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS = [];
        gridNameS1 = [];
        gridNameS2 = [];
        gridNameS3 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD1Grid) {
            gridNameD1Grid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }
        if (gridNameD3Grid) {
            gridNameD3Grid.dataSource.read();
        }
        if (gridNameD4Grid) {
            gridNameD4Grid.dataSource.read();
        }
        if (gridNameD5Grid) {
            gridNameD5Grid.dataSource.read();
        }
        if (gridNameD6Grid) {
            gridNameD6Grid.dataSource.read();
        }
        if (gridNameD7Grid) {
            gridNameD7Grid.dataSource.read();
        }
    }

    /////////////////////////////////////////////////////////异常任务
    //排单生成
    var editR = function () {
        var arrIds = getGridSelectData(gridNameD8Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        //判断类型是否全部分同一为,并取出形成类型单
        var g = arrIds;
        var a = "";
        var b = "";
        var code = [];

        for (var i = 0; i < g.length; i++) {

            //            if (code.indexOf(g[i]["BIO_CODE"]) > -1) {//同一个执行单不允许重复
            //                alertMsg("提示:存在所选编号“" + g[i]["BIO_CODE"] + "重复!”");
            //               return;
            //            } else {
            code.push(g[i]["BIO_CODE"]);
            //            }
            if (i == 0) {
                if (g[i]["LIBRARY_FLOW"] == null || g[i]["LIBRARY_FLOW"] == "") {
                    if (g[i]["LIBRARY_TYPE_EN"] == "Hi-c") {
                        a = "HIC建库";
                        b = "HIC建库";
                    } else if (g[i]["LIBRARY_TYPE_EN"] == "ATAC-seq") {
                        a = "ATAC建库";
                        b = "ATAC建库";
                    }
                } else {
                    a = g[i]["LIBRARY_FLOW"];
                    b = g[i]["LIBRARY_FLOW"];
                }
            } else {
                if (g[i]["LIBRARY_FLOW"] == null || g[i]["LIBRARY_FLOW"] == "") {
                    if (g[i]["LIBRARY_TYPE_EN"] == "Hi-c") {
                        a = "HIC建库";
                        b = "HIC建库";
                    } else if (g[i]["LIBRARY_TYPE_EN"] == "ATAC-seq") {
                        a = "ATAC建库";
                        b = "ATAC建库";
                    }
                } else {
                    a = g[i]["LIBRARY_FLOW"];
                    b = g[i]["LIBRARY_FLOW"];
                }
            }

            if (a != b) {
                alertMsg("存在所选记录建库流向“<font color=#ff0000>" + a + "--" + b + "</font>”前后不一致!");
                return;
            }

        }

        var ids = [];
        var taskids = [];
        var biocodes = [];
        var samplecodes = [];
        var libtypes = [];
        var libtypemxs = [];
        var LIBQCIDS = [];


        var INDEX_NAME = [];
        var SEQ_I7_1 = [];
        var SEQ_I7_2 = [];
        var SEQ_I7_3 = [];
        var SEQ_I7_4 = [];
        var SEQ_I5_NAVA = [];
        var SEQ_I5_XTEN = [];
        var GEN_N = [];
        var ER_SEQ = [];
        var LIB_KIT = [];

        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            LIBQCIDS.push(arrIds[i]["LIBQCID"]);

            libtypemxs.push(arrIds[i]["LIBRARY_TYPE_EN"]);
            biocodes.push(arrIds[i]["BIO_CODE"]);
            samplecodes.push(arrIds[i]["SAMPLE_CODE"]);

            INDEX_NAME.push(arrIds[i]["INDEX_NAME"]);
            SEQ_I7_1.push(arrIds[i]["SEQ_I7_1"]);
            SEQ_I7_2.push(arrIds[i]["SEQ_I7_2"]);
            SEQ_I7_3.push(arrIds[i]["SEQ_I7_3"]);
            SEQ_I7_4.push(arrIds[i]["SEQ_I7_4"]);
            SEQ_I5_NAVA.push(arrIds[i]["SEQ_I5_NAVA"]);
            SEQ_I5_XTEN.push(arrIds[i]["SEQ_I5_XTEN"]);
            GEN_N.push(arrIds[i]["GEN_N"]);
            ER_SEQ.push(arrIds[i]["ER_SEQ"]);
            LIB_KIT.push(arrIds[i]["LIB_KIT"]);

            if (libtypes.indexOf(arrIds[i]["LIBRARY_TYPE_EN"]) < 0) {
                libtypes.push(arrIds[i]["LIBRARY_TYPE_EN"]);//文库类型
            }
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/pdup/pdup",
            title: "常规建库排单.."
        };
        openWindow(winOpts, {
            "IDS": ids, "TASKIDS": taskids, "SAMPLECODES": samplecodes, "BIOCODES": biocodes, "LIBTYPES": libtypes, "LIBTYPEMXS": libtypemxs, "EX_TYPE": a, "LIBQCIDS": LIBQCIDS,
            "INDEX_NAME": INDEX_NAME, "SEQ_I7_1": SEQ_I7_1, "SEQ_I7_2": SEQ_I7_2, "SEQ_I7_3": SEQ_I7_3, "SEQ_I7_4": SEQ_I7_4, "SEQ_I5_NAVA": SEQ_I5_NAVA,
            "SEQ_I5_XTEN": SEQ_I5_XTEN, "GEN_N": GEN_N, "ER_SEQ": ER_SEQ, "LIB_KIT": LIB_KIT, "YC": "是",
        });
    }


    //追加任务
    var addToExRe = function () {
        var arrIds = getGridSelectData(gridNameD4Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/pd/samplepooling/addtoex/addtoex",
            title: "追加样本到执行单.."
        };
        var ids = [];
        var codes = [];
        var LIBQCIDS = [];
        var taskids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            codes.push(arrIds[i]["BIO_CODE"]);
            LIBQCIDS.push(arrIds[i]["LIBQCID"]);
            if (taskids.indexOf(arrIds[i]["TASK_LS_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_LS_ID"]);//主单ID
            }
        }

        openWindow(winOpts, { "IDS": ids, "CODES": codes, "LIBQCIDS": LIBQCIDS, "TASKIDS": taskids });
    }

    /////////////////////////////////////////////////////////



    //获取周期定义,推算出截止结果日期
    var doCyc = function (type, countSm, smnumber, dep, sa, cyc_dws, business_unit) {
        debugger;
        //测序标准用时
        var cycdw = cyc_dws;
        var bus = business_unit;

        if (cycdw == "样品数") {
            flag = 0;
            params = { "query": "checkCycSmNumber", "objects": [bus, dep, type, countSm, countSm] };
        } else {
            flag = 1;
            if (sa == "CELL") {
                if (type == "Denovo-Pac(20-30K)") smnumber = smnumber * 140;
                if (type == "Denovo-Pac(hifi)") smnumber = smnumber * 24;
                if (type == "Iso-RNA(Pac)") smnumber = smnumber * 300;
                if (type == "DNA-8K(ONT)") smnumber = smnumber * 80;
                if (type == "Denovo-20k(ONT)") smnumber = smnumber * 80;
                if (type == "Iso-RNA(ONT)") smnumber = smnumber * 140;
            }
            params = { "query": "checkCycDataNumber", "objects": [bus, dep, type, smnumber, smnumber] };
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            ajaxAsync: false,
            succeed: function (result) {
                if (result["code"] > 0) {
                    debugger;
                    var rows = result["rows"];
                    var m = getMyMonth();
                    var dateNumber = 0;
                    var seleDateFlag = "工作日";//日历取向
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        seleDateFlag = row["CYC_FLAG"];
                        if (m == 1) dateNumber = row["MONTH_1"];
                        if (m == 2) dateNumber = row["MONTH_2"];
                        if (m == 3) dateNumber = row["MONTH_3"];
                        if (m == 4) dateNumber = row["MONTH_4"];
                        if (m == 5) dateNumber = row["MONTH_5"];
                        if (m == 6) dateNumber = row["MONTH_6"];
                        if (m == 7) dateNumber = row["MONTH_7"];
                        if (m == 8) dateNumber = row["MONTH_8"];
                        if (m == 9) dateNumber = row["MONTH_9"];
                        if (m == 10) dateNumber = row["MONTH_10"];
                        if (m == 11) dateNumber = row["MONTH_11"];
                        if (m == 12) dateNumber = row["MONTH_12"];

                        break;
                    }
                    //执行天数
                    saveRemind = 1;
                    if (dep == "测序标准用时") {
                        cxdates = dateNumber;
                    } else {
                        sydates = dateNumber;
                    }
                    // $("#TASK_EXCDAYS" + pathValue).val(dateNumber);
                    // $("#CYC_FLAG" + pathValue).val(seleDateFlag);
                    // doGetEndDate(seleDateFlag, dateNumber);
                }
            }
        });

    }
    //推算截止日期
    var doGetEndDate = function (seleDateFlag, dateNumber) {

        var thedate = new Date();
        var params = "";
        if (seleDateFlag == "工作日") {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_WEEKDAY_REMOVAL": "是" } };//取得当前日期后一年内所有的“工作日”排除日期
        } else {
            params = { "query": "getCalendarInfo", "objects": [thedate.getFullYear(), toDateFormatByZone(thedate, "yyyy-MM-dd")], "search": { "D_NATURAL_REMOVAL": "是" } };//取得当前日期后一年内所有的“自然日”排除日期
        }

        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var rows = result["rows"];
                    var noDoDateS = [];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        noDoDateS.push(toDateFormatByZone(row["D_DATE"], "yyyy-MM-dd"));
                    }
                    for (var i = 0; i < dateNumber; i++) {
                        var base = 1000 * 60 * 60 * 24;
                        //thedate=new Date(thedate.getTime() + base); 
                        if (i == 0) {
                            var TASK_LLS = paramsValue["TASK_LL"] * 1;
                            thedate = new Date(TASK_LLS + (base));
                        } else {
                            //TASK_LLS=new Date(thedate.getTime() + base);
                            thedate = new Date(thedate.getTime() + base);
                        }
                        for (var j = 0; j < noDoDateS.length; j++) {
                            if (toDateFormatByZone(thedate, "yyyy-MM-dd") == noDoDateS[j]) {//存在排除日期测
                                thedate = new Date(thedate.getTime() + base);//日期向前一天
                            }
                        }

                    }
                    //推算出的最终截止日期
                    // $("#TASK_LDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));
                    // $("#TASK_JH_ENDDATE" + pathValue).val(toDateFormatByZone(thedate, "yyyy-MM-dd"));

                }
            }
        });

    }


    //当前月份
    var getMyMonth = function () {
        var date = new Date;
        var month = date.getMonth() + 1;
        return month;
    }



    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "edit": edit,
        "editP": editP,
        "upsmStatus": upsmStatus,
        "addToEx": addToEx,
        "doTaskStatus": doTaskStatus,
        "doOK": doOK,
        "doGenNo": doGenNo,
        "remove": remove,
        "doDelete": doDelete,
        "editA": editA,
        "doReturn": doReturn,
        "doReturn2": doReturn2,
        "collection": collection,
        "checkSample": checkSample,
        "pesubmit": pesubmit,
        "doPooling1": doPooling1,
        "doPooling2": doPooling2,
        "doPooling3": doPooling3,
        "importData3": importData3,
        "submit": submit,
        "doUpdate": doUpdate,
        "refreshGrid": refreshGrid,
        "doGetIndex": doGetIndex,
        "callBack": callBack,
        "importData1": importData1,
        "openpool": openpool,
        "setjira": setjira,
        "editR": editR,
        "addToExRe": addToExRe,

    });
});