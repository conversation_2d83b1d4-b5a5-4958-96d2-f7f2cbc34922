$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-samplepooling-pooling1-pooling1";    
    var paramsValue;
    var initData=function(){
        return {
            tableName:"BIO_LIB_POOLING"
        };
    }
    var init=function(params){
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        $("#POOL_LIB_NUM"+pathValue).val(paramsValue["IDS"].length);
        $("#EXE_TQQC_ID"+pathValue).val(paramsValue["EXID"]);
        $("#TASK_ID"+pathValue).val(paramsValue["TASKID"]);

        
	     //根据类型区分平台
	     var extype=paramsValue["EX_TYPE"];

	     $("#POOL_TYPE"+pathValue).val(extype);
	     if(extype=="PB混样-微生物全长")  $("#SEQ_PLAT"+pathValue).val("PB");
	     if(extype=="PB混样-全长转录组")  $("#SEQ_PLAT"+pathValue).val("PB");
	     if(extype=="混样建库-ONT建库-DNA")  $("#SEQ_PLAT"+pathValue).val("ONT");
	     if(extype=="混样建库-ONT建库-Iso-RNA")  $("#SEQ_PLAT"+pathValue).val("ONT");
	     if(extype=="DNA混样建库-MCD非简化建库")  $("#SEQ_PLAT"+pathValue).val("NGS");
	     if(extype=="DNA混样建库-SLAF建库")  $("#SEQ_PLAT"+pathValue).val("NGS");
	     if(extype=="PB混样-基因组")  $("#SEQ_PLAT"+pathValue).val("PB");

    }
 
 
  var submit=function(){
	  	 var time=sysNowTimeFuncParams["sysNowTime"];
	     var username=getLimsUser()["name"];
         //插入执行主单
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                    	var ids=paramsValue["IDS"];
                     	var objectup=[];
                     	var objectlibpool=[];
                          for(var i=0;i < ids.length;i++){
                        	  objectup.push({
                        		  "ID":ids[i],
                        		   "POOL_ID1":result["ID"],//关联POOLID
                                           "POOL_ID":result["ID"],//关联POOLID
               	    	       });
                          }
                 objectlibpool.push({
                       "ID":result["ID"],
                       "LIBRARY_CODE":$("#POOL_CODE"+pathValue).val(),
                       "POOL_CODE":$("#POOL_CODE"+pathValue).val()
                  });
                          var urlsend="system/jdbc/save/batch/table";
          	           var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectup};
          	           putAddOrUpdata(urlsend,paramsadd,"是","更新"); 

                         var addurl="system/jdbc/save/one/table/objects";
                         var paramslibpool={"tableName":"BIO_LIB_INFO","objects":objectlibpool};
                         putAddOrUpdata(addurl,paramslibpool,"否","添加pool到lib");
                   
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
        });
    }
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		funcExce(pathValue+"pageCallBack");
                    funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });