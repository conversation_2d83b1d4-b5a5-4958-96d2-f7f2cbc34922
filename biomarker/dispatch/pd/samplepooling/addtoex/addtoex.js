$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-samplepooling-addtoex-addtoex";
    var paramsValue;
    var initData=function(){
        return {};
    }
    var gridNameGrid;
    var init=function(params){
        paramsValue=params;
        var toolbar=getButtonTemplates(pathValue,[
            {name:"edit",target:"add",title:"确认选择"},
        ]);
        var gridNameGridJson={
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
         read:{"query":"lib_pd_SHEET_list",
        "objects":[["DNA混样建库-SLAF建库",
         "PB混样-微生物全长",
         "PB混样-全长转录组",
         "PB混样-基因组",
         "混样建库-ONT建库-DNA",
                "DNA混样建库-MCD非简化建库",
         "混样建库-ONT建库-Iso-RNA"],
         ["待审核","接收退回"]]},
            headerFilter:function(cols,i){},
            detailTemplate: '<div id="subGrid_#=ID#_'+pathValue+'"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON={
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read:{"query":"queryTaskLibExMx","objects":[[ROW_ID]]},
                };
                var subGrid_N=initKendoGrid("#subGrid_"+ROW_ID+"_"+pathValue,subGrid_N_JSON);
            }
        };
        gridNameGrid = initKendoGrid("#gridNameGrid"+pathValue,gridNameGridJson);
   }
   
    var add=function(){
 
     var ids=getGridSelectData(gridNameGrid);
        if(ids.length==0){
               alertMsg("请至少选择一条记录进行操作!");
               return;
         }
        var arrIds=[];
        var exman=[];
        var seqplat=[];
        for(var i=0;i<ids.length;i++){
            arrIds.push(ids[i]["ID"]);
            exman.push(ids[i]["EX_MAN"]);
            if(ids[i]["EX_TYPE"]=="DNA混样建库-SLAF建库") seqplat.push("NGS");
            if(ids[i]["EX_TYPE"]=="DNA混样建库-MCD非简化建库") seqplat.push("NGS");
            if(ids[i]["EX_TYPE"]=="PB混样-微生物全长") seqplat.push("PB");
            if(ids[i]["EX_TYPE"]=="PB混样-基因组") seqplat.push("PB");
            if(ids[i]["EX_TYPE"]=="混样建库-ONT建库-DNA") seqplat.push("ONT");
            if(ids[i]["EX_TYPE"]=="混样建库-ONT建库-Iso-RNA") seqplat.push("ONT");
        }
 
        var codes=paramsValue["CODES"]; 
          var params={"query":"doCheckExLibInfo","objects":[arrIds,codes]};
            $.fn.ajaxPost({
                 ajaxUrl:"system/jdbc/query/one/table",
                 ajaxType: "post",
                 ajaxData: params,
                 succeed:function(result){
                     if(result["code"]>0){
                         var rows=result["rows"];
                         var s="";
                         for(var i=0;i<rows.length;i++){
                               var row=rows[i];
                               if(i==0){
                                   s=row["BIO_CODE"];
                               }else{
                                   s+=","+row["BIO_CODE"];
                               }
                         }
                           if(s!=""){
                                alertMsg("提示:存在样品编号(“"+s+"”)重复!");
                             }
                       //	}else{
                             var objectadd=[];
                             for(var i=0;i<arrIds.length;i++){
                                     var ids=paramsValue["IDS"];                     	
                                       var time=sysNowTimeFuncParams["sysNowTime"];
                                       var username=getLimsUser()["name"];
                                       var indexDatas=paramsValue["indexDatas"];   
                                       for(var j=0;j < ids.length;j++){
                                       objectadd.push({
                                          "TASK_LIB_MX_ID":ids[j],//联联任务ID
                                              "SEQ_PLAT":seqplat[i],//测序平台
                                            "JK_TASKMX_MAN":exman[i],
                                            "JK_TASKMX_STDATE":time,
                                              "BIO_CODE":codes[i],
                                              "EXE_TQQC_ID":arrIds[i],//关联执行单
                                              "INDEX_NAME":indexDatas[j]["INDEX_NAME"],//index
                                            "SEQ_I7_NAME":indexDatas[j]["SEQ_I7_NAME"],//i7编号
                                            "SEQ_I5_NAME":indexDatas[j]["SEQ_I5_NAME"],//i5编号
                                            "SEQ_I7_1":indexDatas[j]["SEQ_I7_1"],//I7序列
                                            "SEQ_I5_2":indexDatas[j]["SEQ_I5_2"],//I5序列-V1.5
                                            "SEQ_I5_1":indexDatas[j]["SEQ_I5_1"],//I5序列-V1.0
                                            "SEQ_ODBY":indexDatas[j]["SEQ_ODBY"],//分组
                                            "SEQ_CELL":indexDatas[j]["SEQ_CELL"],//对应孔位
                                              "SYS_MAN":username,//实验员
                                              "SYS_INSERTTIME":time//开始日期
                                    });
                                      }
                                 }
                              var urlsend="system/jdbc/save/batch/table";
                                 var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
                                 putAddOrUpdata(urlsend,paramsadd,"是","推入下一步实验任务");
                         //  }
                     }
                 }
            });
     
     } 
     
    //批量执行插入
    var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:urls,
            ajaxData:inobjjson,
            succeed:function(result){
                if(result["code"]>0){
                    if(isDoCallBack=="是"){
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                       funcExce(pathValue+"pageCallBack");
                    }
                }else{
                    alertMsg(errMsg+"操作失败!");
                }
            }
        });
    }
   
    
      var callBack=function(){
         refreshGrid();
      };
 
      var refreshGrid=function(){
         if(gridNameGrid){
             gridNameGrid.dataSource.read();//重新读取--刷新
         }
      }
 
      funcPushs(pathValue,{
          "initData":initData,
          "init":init,
          "add":add,
          "refreshGrid":refreshGrid,
          "callBack":callBack,//回调方法
      });
 });