$(document).ready(function() {
    var pathValue="biomarker-dispatch-pd-samplepooling-pdup-pdup"
    var paramsValue;
    var initData=function(){
        return {
            tableName:"EXE_TQQC_SHEET"
        };
    }
    var init=function(params){
    	
    	paramsValue=params;
        getInfo("form",pathValue,params);
        var url="system/jdbc/query/info/"+initData().tableName;
        getInfo("form",pathValue,params,url);
        $("#EX_TYPE"+pathValue).val(paramsValue["EX_TYPE"]);
        $("#EX_MX_NUMBER"+pathValue).val(paramsValue["IDS"].length);
       $("#EX_MX_NUMBER"+pathValue).val(paramsValue["IDS"].length);
        var libtyps=paramsValue["LIBTYPES"];
        var strlibthype="";
        for(var i=0;i<libtyps.length;i++){
        	if(i==0){
        		strlibthype=libtyps[i];
        	}else{
        		strlibthype+=","+libtyps[i];
        	}
        }
        $("#EX_LIB_TYPE"+pathValue).val(strlibthype);
        //文库执行单号编号字符
		//SLAF建库执行单号	JS+日期+两位数流水号
		//PB微生物全长建库执行单号	JC+日期+两位数流水号
		//PB全长转录组建库执行单号	JI+日期+两位数流水号
		//PB-基因组建库执行单号	JG+日期+两位数流水号
		//ONT-ISO-RNA建库和DNA建库执行单号	JT+日期+两位数流水号
       
        var typelb=$("#EX_TYPE"+pathValue).val();
        var numstr="";
        if(typelb=="PB混样-微生物全长") numstr="JC";
        if(typelb=="PB混样-全长转录组") numstr="JI";
        if(typelb=="PB混样-基因组") numstr="JG";
        if(typelb=="DNA混样建库-SLAF建库") numstr="JS";
        if(typelb=="DNA混样建库-MCD非简化建库") numstr="JF";
        if(typelb=="混样建库-ONT建库-DNA"||typelb=="混样建库-ONT建库-Iso-RNA") numstr="JT";
       $("#NO_CHAR"+pathValue).val(numstr);
    }
 
 
  var submit=function(){
	   var time=sysNowTimeFuncParams["sysNowTime"];
	  var username=getLimsUser()["name"];
          var selplat="";
        var typelb=$("#EX_TYPE"+pathValue).val();
        if(typelb=="PB混样-微生物全长") selplat="PB";
        if(typelb=="PB混样-全长转录组") selplat="PB";
        if(typelb=="PB混样-基因组") selplat="PB";
        if(typelb=="DNA混样建库-SLAF建库") selplat="NGS";
        if(typelb=="DNA混样建库-MCD非简化建库") selplat="NGS";
        if(typelb=="混样建库-ONT建库-DNA"||typelb=="混样建库-ONT建库-Iso-RNA") selplat="NGS";


         //插入执行主单
         formSubmit({
             url:"system/jdbc/save/one/table",
             formId:"form",
             pathValue:pathValue,
             succeed:function(result){
                 if(result["code"]>0){
                     alertMsg("提交成功,生成的草稿,请前往审核提交!","success",function(){
                    	var ids=paramsValue["IDS"];
                      	var exeids=paramsValue["TASKIDS"];
                      	
                     	var objectadd=[];
                     	var objectup=[];
                     	var oubjctuptaskmx=[];
                        var biocodes=paramsValue["BIOCODES"];
                        var samplecodes=paramsValue["SAMPLECODES"];
                        var libtypemxs=paramsValue["LIBTYPEMXS"];
                        var indexDatas=paramsValue["indexDatas"];
                        debugger;
                          for(var i=0;i < ids.length;i++){//BIO_LIB_INFO
                         		objectadd.push({
               	    	       "EXE_TQQC_ID":result["ID"],//关联执行单
               	    	       "BIO_CODE":biocodes[i],//枋酸编号
               	    	       "LIBRARY_TYPE":libtypemxs[i],//文库类型
               	    	       "SAMPLE_CODE":samplecodes[i],//样品编号
                         	   "TASK_LIB_MX_ID":ids[i],
                               "SEQ_PLAT":selplat,
                               "JK_TASKMX_MAN":$("#EX_MAN"+pathValue).val(),
                               "INDEX_NAME":indexDatas[i]["INDEX_NAME"],//index
                               "SEQ_I7_NAME":indexDatas[i]["SEQ_I7_NAME"],//i7编号
                               "SEQ_I5_NAME":indexDatas[i]["SEQ_I5_NAME"],//i5编号
                               "SEQ_I7_1":indexDatas[i]["SEQ_I7_1"],//I7序列
                               "SEQ_I5_2":indexDatas[i]["SEQ_I5_2"],//I5序列-V1.5
                               "SEQ_I5_1":indexDatas[i]["SEQ_I5_1"],//I5序列-V1.0
                               "SEQ_ODBY":indexDatas[i]["SEQ_ODBY"],//分组
                               "SEQ_CELL":indexDatas[i]["SEQ_CELL"],//对应孔位
                                "JK_TASKMX_STDATE":time,
                                "SYS_MAN":username,//实验员
                                 "SYS_INSERTTIME":time//开始日期
               	    	       });
                         		oubjctuptaskmx.push({//BIO_TASK_LIBMX
                         			"ID":ids[i],
               	    	       		"TASK_LSMX_STATUS":"混库已排单"
               	    	       	});
                         		
                         		
                          }
                          for(var i=0;i < exeids.length;i++){
                         	 objectup.push({
            	    	       		"ID":exeids[i],
            	    	       		"TASK_LS_STATUS":"建库中"
            	    	       	});
                          }
                          //执行添加到文库
                         var urlsend="system/jdbc/save/batch/table";
          	        	 var paramsadd={"tableName":"BIO_LIB_INFO","objects":objectadd};
          	        	 putAddOrUpdata(urlsend,paramsadd,"否","推入下一步实验任务");

          	        	 var paramsup={"tableName":"BIO_TASK_LIB","objects":objectup};
 	         	      	 putAddOrUpdata(urlsend,paramsup,"否","建库中");
 	         	      	 
 	         	      	var paramsuptaskmx={"tableName":"BIO_TASK_LIBMX","objects":oubjctuptaskmx};
	         	      	 putAddOrUpdata(urlsend,paramsuptaskmx,"是","");
 
                     });
                 }else{
                     alertMsg("提交失败","error");
                 }
             }
         });

    }
  var putAddOrUpdata=function(urls,inobjjson,isDoCallBack,errMsg){
      $.fn.ajaxPost({
          ajaxType:"post",
          ajaxUrl:urls,
          ajaxData:inobjjson,
          succeed:function(result){
              if(result["code"]>0){
             	 if(isDoCallBack=="是"){
             		funcExce(pathValue+"pageCallBack");
                    funcExce(pathValue+"close");
             	 }
              }else{
             	 alertMsg(errMsg+"操作失败!");
              }
          }
      });
  }
 
    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });