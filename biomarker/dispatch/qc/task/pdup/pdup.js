$(document).ready(function () {
    var pathValue = "biomarker-dispatch-qc-task-pdup-pdup";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "EXE_TQQC_SHEET"
        };
    }
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
        $("#EX_TYPE" + pathValue).val(paramsValue["EX_TYPE"]);
        $("#EX_RE_STATUS2" + pathValue).val("核酸检测排单");

        var typelb = $("#EX_TYPE_LB" + pathValue).val();
        var numstr = "";
        if (typelb == "DNA常规检测") numstr = "HD";
        if (typelb == "RNA常规检测" || typelb == "RNA检测") numstr = "HR";
        if (typelb == "DNA检测-MCD检测") numstr = "HM";
        if (typelb == "医学检测") numstr = "HY";
        if (typelb == "代谢检测") numstr = "HX";
        if (typelb == "蛋白检测") numstr = "HA";
        $("#NO_CHAR" + pathValue).val(numstr);
    }

    var subUpData = function () {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = $("#EX_MAN2" + pathValue).val();
        formSubmit({
            url: "system/jdbc/save/one/table",
            formId: "form",
            pathValue: pathValue,
            succeed: function (result) {
                if (result["code"] > 0) {
                    //提交成功
                    var ids = paramsValue["IDS"];
                    var taskids = paramsValue["TASKIDS"];
                    var objectadd = [];
                    var objectup = [];
                    var tqffs = paramsValue["TQFFS"];
                    var ckffs = paramsValue["CKFFS"];
                    var codes = paramsValue["CODES"];
                    var uptaskmx = [];

                    for (var i = 0; i < ids.length; i++) {
                        objectadd.push({
                            "TASK_TQ_ID": ids[i],//联联任务ID
                            "EXE_TQQC_ID": result["ID"],//关联执行单
                            "SAMPLE_CODE": codes[i],//样本编号
                            "EXTDR_METHOD": tqffs[i],
                            "CKDR_METHOD": ckffs[i],
                            "DJC_MAN": username//检测实验员DJC_MAN
                            //"DJC_ST_DATE":time//开始检测日期
                        });
                        uptaskmx.push({
                            "ID": ids[i],
                            "TASK_SM_STATUS": "待检测",
                        });

                    }
                    for (var i = 0; i < taskids.length; i++) {
                        objectup.push({
                            "ID": taskids[i],
                            "TASK_STATUS": "检测中",
                            "SYS_MAN_L": username,
                            "SYS_INSERTTIME_L": time
                        });
                    }

                    //执行添加到文库
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsadd = { "tableName": "BIO_DNA_RNA_QC", "objects": objectadd };
                    putAddOrUpdata(urlsend, paramsadd, "否", "推入下一步实验任务");

                    var paramsup2 = { "tableName": "BIO_TQ_TASK_MX", "objects": uptaskmx };
                    putAddOrUpdata(urlsend, paramsup2, "否", "检测中");

                    var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "检测中");
                    debugger;


                    alertMsg("提示:操作成功!");
                    funcExce(pathValue + "pageCallBack");
                    funcExce(pathValue + "close");



                } else {
                    alertMsg("提交失败", "error");
                }
            }
        });
    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }

    var submit = function () {
        subUpData();
    }

    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});