$(document).ready(function () {
    var pathValue = "biomarker-dispatch-qc-task-index";
    var initData = function () {
        return {};
    }
    var gridNameDGrid;
    //var gridNameD1Grid;
    var gridNameD2Grid;
    var gridNameD3Grid;
    var gridNameD4Grid;
    var gridNameD5Grid;
    var gridNameD6Grid;
    var gridNameD7Grid;
    var gridNameD8Grid;
    var gridNameS = [];
    var gridNameS2 = [];
    var gridNameS3 = [];
    var init = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "edit", title: "生成执行单" },
            { name: "edit", target: "upsmStatus", title: "修改样本状态" },
            { name: "edit", target: "addToEx", title: "追加样本到执行单" },
            { name: "edit", target: "doTaskStatus", title: "任务单状态修改" },
            { name: "edit", target: "editjc", title: "修改检测方法" },
            { name: "edit", target: "setjira", title: "JIRA推送-暂停" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_PD_BIO_SAMPLE_QC_task_main", "objects": [["暂停中", "已审核", "检测中", "待检测", "已完成", "提取中"], ["检测"], ["DNA检测", "RNA检测", "核酸检测"]], "search": { "DD_TASK_STATUS": ["已审核"] } },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_PD_BIO_TQ_TASK_MX2_list", "objects": [[ROW_ID]] },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS.push(subGrid_N);
            }
        };
        gridNameDGrid = initKendoGrid("#gridNameDGrid" + pathValue, gridNameGridJson);
        // init1();
        init2();
        init3();
        init4();
        init5();
        init6();
        init7();
        init8();

    }

    //待审核
    var init2 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "doGenNo", title: "生成核酸编号" },
            { name: "edit", target: "editjc2", title: "修改检测方法" },
            { name: "delete", target: "remove", title: "移除任务明细" },
            { name: "ok", target: "doOK2", title: "拆单分派.." },
            { name: "ok", target: "doOK", title: "审核/分派.." },
            { name: "delete", target: "doDelete", title: "删除执行单" },
            { name: "edit", target: "doUpdate", title: "修改实验员" },
            { name: "edit", target: "doUp2date", title: "分派提交" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_EXE_QC_SHEET_list",
                "objects": [
                    ["DNA常规检测",
                        "DNA检测-MCD检测",
                        "蛋白检测",
                        "RNA检测",
                        "代谢检测",
                        "医学检测"],
                    ["核酸检测排单", "核酸检测", "接收退回", "实验退回"]]
            },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: [],
                    height: 320,
                    read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]], "search": { "EXE_TQQC2_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
                gridNameS2.push(subGrid_N);
            }
        };
        gridNameD2Grid = initKendoGrid("#gridNameD2Grid" + pathValue, gridNameGridJson);
    }
    //已处理
    var init3 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn", title: "撤回" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_EXE_QC_SHEET_list",
                "objects": [
                    ["DNA常规检测",
                        "DNA检测-MCD检测",
                        "RNA检测",
                        "蛋白检测",
                        "代谢检测",
                        "医学检测"],
                    ["核酸检测", "核酸检测待接收", "核酸检测中", "RNA初检完成", "核酸检测完成", "结果待审核", "结果已审核", "实验退回", "审核退回", "已分配"]], "search": { "EX_EXECUTE_MODE": "实验员" }
            },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]], "search": { "EXE_TQQC2_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD3Grid = initKendoGrid("#gridNameD3Grid" + pathValue, gridNameGridJson);
    }
    //已结单
    var init4 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "return", target: "doReturn2", title: "移至待排单" },
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: { "query": "query_PD_BIO_SAMPLE_QC_task_main", "objects": [["结单", "暂停", "未检测", "终止"], ["检测"], ["DNA检测", "RNA检测", "核酸检测"]] },
            headerFilter: function (cols, i) { },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_PD_BIO_TQ_TASK_MX2_list", "objects": [[ROW_ID]], "search": { "EXE_TQQC2_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD4Grid = initKendoGrid("#gridNameD4Grid" + pathValue, gridNameGridJson);

    }
    //  常规待排任务
    var init5 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "editA", title: "智能排单" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
             size:3000, 
            toolbar: toolbar,
            read: {
                "query": "query_PD_BIO_SAMPLE_QC_task_stay", "objects": [], "search": {
                    "TASK_TYPE": ["检测"],
                     "TASK_STATUS":"无意义",
                    "MEHOD_JCFLOW": ["DNA常规检测", "RNA检测"], 
                    "TASK_TYPE_LB": ["DNA检测", "RNA检测", "核酸检测"]
                }
            }
        };
        gridNameD5Grid = initKendoGrid("#gridNameD5Grid" + pathValue, gridNameGridJson);
    }
    //MCD待排任务
    var init6 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "editB", title: "智能排单" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
             size:3000, 
            toolbar: toolbar,
            read: {
                "query": "query_PD_BIO_SAMPLE_QC_task_stay", "objects": [], "search": {
                    "TASK_TYPE": ["检测"],
                    "TASK_STATUS":"无意义",
                    "MEHOD_JCFLOW": ["DNA检测-MCD检测"], 
                    "TASK_TYPE_LB": ["DNA检测", "RNA检测", "核酸检测"],
                    "AMPLIFY_REGIONAL": "1"
                }
            }
        };
        gridNameD6Grid = initKendoGrid("#gridNameD6Grid" + pathValue, gridNameGridJson);
    }
    //PE任务待处理
    var init7 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit", title: "提交" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_EXE_QC_SHEET_list",
                "objects": [
                    ["DNA常规检测",
                        "DNA检测-MCD检测",
                        "RNA检测",
                        "蛋白检测",
                        "代谢检测",
                        "医学检测"],
                    ["前处理提交"]], "search": { "EX_EXECUTE_MODE": "PE" }
            },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]], "search": { "EXE_TQQC2_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD7Grid = initKendoGrid("#gridNameD7Grid" + pathValue, gridNameGridJson);
    }
    //PE执行状态
    var init8 = function (params) {
        var toolbar = getButtonTemplates(pathValue, [
            { name: "edit", target: "pesubmit1", title: "提交" },
            { name: "edit", target: "collection", title: "采集" }
        ]);
        var gridNameGridJson = {
            url: "system/jdbc/query/one/table",
            sort: "",
            toolbar: toolbar,
            read: {
                "query": "query_EXE_QC_SHEET_list",
                "objects": [
                    ["DNA检测-MCD检测"],
                    ["核酸检测中", "Labchip检测", "结果审核退回"]]
            },
            detailTemplate: '<div id="subGrid_#=ID#_' + pathValue + '"></div>',
            detailInit: function (e) {
                var ROW_ID = e.data.ID;
                var subGrid_N_JSON = {
                    url: "system/jdbc/query/one/table",
                    sort: "",
                    toolbar: null,
                    height: 320,
                    read: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[ROW_ID]], "search": { "EXE_TQQC2_ID": [ROW_ID] } },
                };
                var subGrid_N = initKendoGrid("#subGrid_" + ROW_ID + "_" + pathValue, subGrid_N_JSON);
            }
        };
        gridNameD8Grid = initKendoGrid("#gridNameD8Grid" + pathValue, gridNameGridJson);
    }
    var edit = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }

        //判断类型是否全部分同一类,并取出形成类型单
        //MEHOD_JCFLOW 检测流向
        var g = arrIds;
        var a1 = "";
        var b1 = "";
        var code = [];


        for (var i = 0; i < g.length; i++) {
            if (g[i]["TASK_SM_STATUS"] == "检测完成" || g[i]["TASK_SM_STATUS"] == "提取中") {
                alertMsg("提示:样品“" + g[i]["SAMPLE_CODE"] + g[i]["TASK_SM_STATUS"] + "”");
                return;
            }
            if (i == 0) {
                a1 = g[i]["MEHOD_JCFLOW"];
                b1 = g[i]["MEHOD_JCFLOW"];
                code.push(g[i]["SAMPLE_CODE"]);
            } else {
                a1 = g[i - 1]["MEHOD_JCFLOW"];
                b1 = g[i]["MEHOD_JCFLOW"];
                if (code.indexOf(g[i]["SAMPLE_CODE"]) > -1) {//同一个执行单不允许重复
                    alertMsg("提示:存在所选编号“" + g[i]["SAMPLE_CODE"] + "重复!”");
                    return;
                } else {
                    code.push(g[i]["SAMPLE_CODE"]);
                }
            }
            if (a1 != b1) {
                alertMsg("存在所选记录检测类型“<font color=#ff0000>" + a1 + "--" + b1 + "</font>”前后不一致!");
                return;
            }
        }

        var winOpts = {
            url: "biomarker/dispatch/qc/task/pdup/pdup",
            title: a1
        };
        var ids = [];
        var taskids = [];
        var tqffs = [];
        var ckffs = [];
        var codes = [];

        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
            tqffs.push(arrIds[i]["EXTDR_METHOD"]);
            ckffs.push(arrIds[i]["CKDR_METHOD"]);
            codes.push(arrIds[i]["SAMPLE_CODE"]);

            if (taskids.indexOf(arrIds[i]["TASK_ID"]) < 0) {
                taskids.push(arrIds[i]["TASK_ID"]);//主单ID
            }
        }
        openWindow(winOpts, { "IDS": ids, "TASKIDS": taskids, "CODES": codes, "EX_TYPE": "检测", "EX_TYPE_LB": a1, "TQFFS": tqffs, "CKFFS": ckffs });
    }
    //审核提交
    var doOK = function () { 
        var arrIds = getSelectData(gridNameD2Grid);
        var arrData = getGridSelectData(gridNameD2Grid);
        if (arrIds.length < 1 ) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var rows;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [arrIds], "search": { "EXE_TQQC2_ID": arrIds } },
            succeed: function (rs) {
                rows = rs.rows;
            }
        });
        for (var i = 0; i < rows.length; i++) {
            if (rows[i]["SAMPLE_GENNO"] == null || rows[i]["SAMPLE_GENNO"] == "") {
                alertMsg(rows[i]["SAMPLE_CODE"] + "核酸编号为空!");
                return;
            }
        }
        var winOpts = {
            url: "biomarker/dispatch/qc/task/pdup2/pdup2",
            title: "核酸检测排单"
        };
        var zdID=[];
        var sdID=[];

        for(var i=0;i<arrData.length;i++){
           if(arrData[i]["EX_ARRANGE_MODE"]=="自动排") {
            zdID.push(arrData[i]["ID"]);
           }else{
            sdID.push(arrData[i]["ID"]);
           }
        }
        openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0], "zdID":zdID,"sdID":sdID});
    }
    //拆单
    var doOK2 = function () {

        var mainIds = getGridSelectData(gridNameD2Grid);
        if (mainIds.length != 1) {
            alertMsg("请选择一条主单条记录进行操作!");
            return;
        }
        var mxIds = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getGridSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                mxIds = mxIds.concat(arrSubID);
            }
        }
        if (mxIds.length == 0) {
            alertMsg("请选择明细记录!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行拆单吗?", "warn", function () {
            //新的执行单生成
            var newmainId = getRandomId();
            var newrecode = [];
            var time = sysNowTimeFuncParams["sysNowTime"];
            var username = getLimsUser()["name"];

            newrecode.push({//EXE_TQQC_SHEET
                "ID": newmainId,
                "EX_RE_STATUS": mainIds[0]["EX_RE_STATUS"],
                "EX_RE_STATUS2": mainIds[0]["EX_RE_STATUS2"],
                "EX_DH_NO": mainIds[0]["EX_DH_NO"] + "-1",//执行单号
                "EX_MAN_ID": mainIds[0]["EX_MAN_ID"],//关联用户ID
                "EX_MAN": mainIds[0]["EX_MAN"],//实验员
                "EX_MAN2_ID": mainIds[0]["EX_MAN2_ID"],//关联用户ID
                "EX_MAN2": mainIds[0]["EX_MAN2"],//实验员
                "EX_TYPE": mainIds[0]["EX_TYPE"],//执行单类型
                "EX_TYPE_LB": mainIds[0]["EX_TYPE_LB"],//单据类别
                "EX_BACK": mainIds[0]["EX_BACK"],//退回原因
                //"EX_BQC_TIME":mainIds[0]["EX_BQC_TIME"],//实验开始时间
                //"EX_EQC_TIME":mainIds[0]["EX_EQC_TIME"],//实验结束时间
                "EX_NEEDING_ATN": mainIds[0]["EX_NEEDING_ATN"],//注意事项
                "EX_TIME2": time,
                "EX_REMARK": mainIds[0]["EX_REMARK"]//提取备注
            });


            //将记录转移到新的执行
            var newMxs = [];
            for (var i = 0; i < mxIds.length; i++) {
                newMxs.push({//BIO_DNA_RNA_QC
                    "ID": mxIds[i]["LIBID"],//检测ID
                    "EXE_TQQC_ID": newmainId
                });
            }

            var newUrl = "system/jdbc/save/one/table/objects";
            var paramsnainadd = { "tableName": "EXE_TQQC_SHEET", "objects": newrecode };
            putAddOrUpdata(newUrl, paramsnainadd, "否", "生成");

            var url = "system/jdbc/save/batch/table";
            var paramsmx = { "tableName": "BIO_DNA_RNA_QC", "objects": newMxs };
            putAddOrUpdata(url, paramsmx, "是", "");
        });

    }


    //样品核验
    var checkSample = function () {
        var arrIds = getGridSelectData(gridNameD6Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行核验");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行核验操作!");
            return;
        }

        var winOpts = {
            url: "biomarker/dispatch/qc/task/check/check",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"] });//传递
    }


    //PE任务待处理提交
    var pesubmit = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameD7Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行提交!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行提交!");
            return;
        }
        if (arrIds[0]["PLATE_CODE"] == null) {

            alertMsg("执行单" + arrIds[0]["EX_DH_NO"] + "尚未分配板孔号，不能提交");
            return;
        }
        var PEtoken;
        var PEVariable = { "ClientName": PE_ClientName, ClientPwd: ClientPwd };
        var inobjjson = { "url": PE_URL + "api/clientInfo/login", "PEVariable": PEVariable }
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                PEtoken = rs.apiData.result.token;
            }
        });
        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[arrIds[0]["ID"]]], "search": { "EXE_TQQC2_ID": [arrIds[0]["ID"]] } },
            succeed: function (rs) {
                sample = rs.rows;             //样品
            }
        });
        var ares;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: {
                "query": "query_BIO_PE_LIMS_SCAR_list",
                "objects": [],
                "search": { "LIMS_SCAR": sample[0]["AMPLIFY_REGIONAL"] }
            },
            succeed: function (rs) {
                ares = rs.rows;             //PE扩增区域
            }
        });

        var PEsamples = [];
        for (var j = 0; j < sample.length; j++) {
            PEsamples.push({
                "Well": sample[j]["PLATE_WELL"],
                "SampleName": sample[j]["SAMPLE_NAME"],
                "SampleNo": sample[j]["SAMPLE_CODE"],
                "Area": ares[0]["PE_SCAR"],
                "Program": ares[0]["PE_PROCEDURE"],
                "NucleicAcidCode": sample[j]["SAMPLE_GENNO"]
            });
        }

        time = sysNowTimeFuncParams["sysNowTime"];
        var PEVariable = { 
            "TimeStamp": time,
            "Token": PEtoken,
            "ClientId": "",
            "Cmd":  ares[0]["MCD_TYPE"],
            "RQData": {
                "TaskNo": arrIds[0]["EX_DH_NO"],
                "BarCode": arrIds[0]["PLATE_CODE"],
                "Samples": PEsamples,
                "IndexBarCode": ""
            }
        };
        var inobjjson = { "url": PE_URL + "api/order/create", "PEVariable": PEVariable }
        var RValue;
        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/rowsingle",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson,
            succeed: function (rs) {
                RValue = rs;
            }
        });
        if (!RValue.apiData.success) {
            alertMsg(RValue.apiData.msg);
            return;
        }
        var objectSheet = [];
        objectSheet.push({
            "ID": arrIds[0]["ID"],//id
            "EX_RE_STATUS2": "核酸检测中"       //状态
        });
        var urlsend = "system/jdbc/save/batch/table";
        var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
        putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
    }
    //批量填写
    var editjc = function () {
        var arrIds = [];
        console.log(gridNameS);
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
        }

        var winOpts = {
            url: "biomarker/dispatch/qc/task/editjc/editjc",
            title: "修改检测方法.."
        };
        openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
    }

    //自动化排单
    var editB = function () {
        var gridData = getGridSelectData(gridNameD6Grid);
        debugger;
        if (gridData.length == 0) {
            alertMsg("至少选择一个样本");
            return;

        }

        if (gridData.length > 999) {
            alertMsg("单次只能排小于1000个样本！");
            return;
        }

        var mode = "MCD";
        var params = { "ids": [], "qcids": [], "keys": [], "mode": mode, "EX_DH_NOS": [], };

        for (var j = 0; j < gridData.length; j++) {



            var EX_DH_NO = gridData[j]["EX_DH_NO"];
            var id = gridData[j]["ID"];
            var qcid = gridData[j]["QCID"];
            var lfo2 = gridData[j]["MEHOD_JCFLOW"]; //工序
            var wor2 = gridData[j]["MEHOD_TJPLAT"]; //样品执行组
            var met2 = gridData[j]["CKDR_METHOD"]; //样品执行方法
            var are = gridData[j]["PE_PROCEDURE"]; //PE扩增程序



            if (params.EX_DH_NOS.indexOf(EX_DH_NO) == -1) {
                params.EX_DH_NOS.push(EX_DH_NO);
            }

            params.ids.push(id);
            params.qcids.push(qcid);
            if (params.keys.indexOf(lfo2 + "-" + wor2 + "-" + met2 + "-" + are) < 0) {
                params.keys.push(lfo2 + "-" + wor2 + "-" + met2 + "-" + are);
                params[lfo2 + "-" + wor2 + "-" + met2 + "-" + are] = {
                    lfo: lfo2,
                    met: met2,
                    wor: wor2,
                    are: are,
                    qcids: [],
                    ids: []
                };

            }
            params[lfo2 + "-" + wor2 + "-" + met2 + "-" + are].ids.push(id);
            params[lfo2 + "-" + wor2 + "-" + met2 + "-" + are].qcids.push(qcid);


        }

        var winOpts = {
            url: "biomarker/dispatch/qc/task/automate/automate",
            title: "自动排单明细.."
        };
        openWindow(winOpts, params);//传递
    }
    //自动化排单
    var editA = function () {

        var gridData = getGridSelectData(gridNameD5Grid);
        debugger;
        if (gridData.length == 0) {
            alertMsg("至少选择一个样本");
            return;

        }

        if (gridData.length > 999) {
            alertMsg("单次只能排小于1000个样本！");
            return;
        }

        var mode = "常规";
        var params = { "ids": [], "keys": [], "mode": mode };

        for (var j = 0; j < gridData.length; j++) {
            var id = gridData[j]["ID"];
            var lfo2 = gridData[j]["MEHOD_JCFLOW"]; //工序
            var wor2 = gridData[j]["MEHOD_TJPLAT"]; //样品执行组
            var met2 = gridData[j]["CKDR_METHOD"]; //样品执行方法
            //       var mp2= gridData[j]["MEHOD_PLAT"]; //提取流向
            //          var mj2= gridData[j]["MEHOD_JCFLOW"]; //检测流向


            params.ids.push(id);
            if (params.keys.indexOf(lfo2 + "-" + wor2 + "-" + met2) < 0) {
                params.keys.push(lfo2 + "-" + wor2 + "-" + met2);
                params[lfo2 + "-" + wor2 + "-" + met2] = {
                    lfo: lfo2,
                    met: met2,
                    wor: wor2,
                    ids: []
                };

            }
            params[lfo2 + "-" + wor2 + "-" + met2].ids.push(id);

        }

        var winOpts = {
            url: "biomarker/dispatch/qc/task/automate/automate",
            title: "自动排单明细.."
        };
        openWindow(winOpts, params);//传递

    }
    //批量填写
    var editjc2 = function () {
        var arrIds = [];
        console.log(gridNameS);
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getGridSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var ids = [];
        for (var i = 0; i < arrIds.length; i++) {
            ids.push(arrIds[i]["ID"]);
        }

        var winOpts = {
            url: "biomarker/dispatch/qc/task/editjc/editjc",
            title: "修改检测方法.."
        };
        openWindow(winOpts, { "IDS": ids, "ID": ids[0] });
    }
    //修改实验员
    var doUpdate = function () {
        var arrIds = getGridSelectData(gridNameD2Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/qc/task/updateman/updateman",
            title: "自动排单明细.."
        };
        openWindow(winOpts, { "ID": arrIds[0]["ID"], "EX_MAN": arrIds[0]["EX_MAN2"] });//传递
    }
    //撤回
    var doReturn = function () {
        var g = getGridSelectData(gridNameD3Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        var ids = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["EX_RE_STATUS2"] != "核酸检测待接收" && g[i]["EX_RE_STATUS2"] != "已分配") {
                alertMsg("操作失败,所选记录存在非“核酸检测中”状态!");
                return;
            } else {
                objectup.push({
                    "ID": g[i]["ID"],
                    "EX_RE_STATUS2": "核酸检测排单"
                });
            }
            ids.push(g[i]["ID"]);
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
        doRequeDoUpTaskmxSmStatus(ids, "待检测");
    }
    //任务单-对应执行单下明细状态修改BIO_DNA_RNA_QC
    var doRequeDoUpTaskmxSmStatus = function (mainExIds, status) {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var params = { "query": "doRequeDoUpTaskmxSmStatus", "objects": [mainExIds] };
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];
                        //更新记录---明细
                        objectup.push({
                            "ID": row["TASKMXID"],
                            "TASK_SM_STATUS": status
                        });
                    }
                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "否", "同步更新任务明细:");
                }
            },
            failed: function (result) {
                alertMsg("提示:操作异常!", "error");
            }
        });
    }
    //移至待审核
    var doReturn2 = function () {
        var g = getGridSelectData(gridNameD4Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {
            if (g[i]["TASK_STATUS"] == "暂停" || g[i]["TASK_STATUS"] == "未检测" || g[i]["TASK_STATUS"] == "结单") {
                objectup.push({
                    "ID": g[i]["ID"],
                    "TASK_STATUS": "已审核"
                });
            } else {
                alertMsg("操作失败,只有“<font color=#ff0000>暂停、未检测</font>”状态方允许操作!");
                return;
            }
        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "BIO_TQ_TASK", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }
    //样本状态修改
    var upsmStatus = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/qc/task/upsmstatus/upsmstatus",
            title: "修改样本状态.."
        };
        openWindow(winOpts, { "IDS": arrIds, "ID": arrIds[0] });
    }

    var addToEx = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS.length; i++) {
            var arrSubID = getGridSelectData(gridNameS[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }
        var ids = [];
        var codes = [];
        var tqffs = [];
        var chkffs = [];
        var TASK_ID = "";
        var winOpts = {
            url: "biomarker/dispatch/qc/task/addtoex/addtoex",
            title: "追加样本到执行单.."
        };
        for (var i = 0; i < arrIds.length; i++) {
            if (arrIds[i]["TASK_SM_STATUS"] == "检测完成" || arrIds[i]["TASK_SM_STATUS"] == "提取中") {
                alertMsg("提示:样品“" + arrIds[i]["SAMPLE_CODE"] + arrIds[i]["TASK_SM_STATUS"] + "”");
                return;
            }
            TASK_ID = arrIds[i]["TASK_ID"];
            ids.push(arrIds[i]["ID"]);
            tqffs.push(arrIds[i]["EXTDR_METHOD"]);
            chkffs.push(arrIds[i]["CKDR_METHOD"]);
            codes.push(arrIds[i]["SAMPLE_CODE"]);
        }
        openWindow(winOpts, { "IDS": ids, "TQFFS": tqffs, "CHKFFS": chkffs, "CODES": codes, "TASK_ID": TASK_ID });

    }

    //删除执行单
    var doDelete = function () {
        var arrIds = getSelectData(gridNameD2Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        confirmMsg("确认", "确定要对选中的记录进行删除吗?", "warn", function () {
            var url = "system/jdbc/delete/one/table/where";
            var params1 = { "tableName": "BIO_DNA_RNA_QC", "where": { "EXE_TQQC_ID": arrIds } };
            deleteGridDataByIds(url, params1, refreshGrid);
            var params2 = { "tableName": "EXE_TQQC_SHEET", "where": { "ID": arrIds } };
            deleteGridDataByIds(url, params2, refreshGrid);
        });
    }

    //预处理提交
    var submit = function () {
        var arrIds = getGridSelectData(gridNameD6Grid);
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条数据进行修改!");
            return;
        } else if (arrIds.length != 1) {
            alertMsg("请只选择一条数据进行修改操作!");
            return;
        }

        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: { "query": "query_YPD_BIO_TQ_TASK_MX_list_Check", "objects": [[arrIds[0]["ID"]]] },
            succeed: function (rs) {
                sample = rs.rows;             //样品
                var num = 0;
                for (var j = 0; j < sample.length; j++) {
                    if (sample[j]["JK_CHECK"] == "OK") {
                        num = num + 1;

                    }
                }
                if (num < sample.length) {
                    alertMsg("还有" + (sample.length - num) + "条未核验，不能提交");
                    return;
                }
                var objectSheet = [];
                objectSheet.push({
                    "ID": arrIds[0]["ID"],//id
                    "EX_RE_STATUS2": "核酸检测排单"       //状态

                });
                var urlsend = "system/jdbc/save/batch/table";
                var paramsadd1 = { "tableName": "EXE_TQQC_SHEET", "objects": objectSheet };
                putAddOrUpdata(urlsend, paramsadd1, "是", "提交");
            }
        });

    }

    //核酸编号
    var doGenNoold = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getGridSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }


        var objectup = [];
        var tasks = [];
        var taskjtnos = [];

        var codes = [];
        var codesdon = [];
        for (var i = 0; i < arrIds.length; i++) {
            //更新记录
            var jtno = 0;
            var taskid = arrIds[i]["TASK_ID"];
            var maxjt = arrIds[i]["DONJT"];
            var dotqnum = arrIds[i]["DOTQN"];
            var smcode = arrIds[i]["SAMPLE_CODE"];
            var flwtype = arrIds[i]["MEHOD_JCFLOW"];
            var indexn = tasks.indexOf(taskid);
            var sano = arrIds[i]["SAMPLE_GENNO"];
            if (sano == null) {
                if (maxjt >= 1) {//存在最大编号了
                    if (indexn > -1) {
                        jtno = parseInt(taskjtnos[indexn]) + 1;
                        taskjtnos[indexn] = parseInt(jtno);
                    } else {
                        jtno = parseInt(maxjt);
                        tasks.push(taskid);
                        taskjtnos.push(parseInt(jtno));
                    }
                } else {
                    if (indexn > -1) {
                        jtno = parseInt(taskjtnos[indexn]) + 1;
                        taskjtnos[indexn] = parseInt(jtno);
                    } else {
                        jtno = 1;
                        tasks.push(taskid);
                        taskjtnos.push(parseInt(jtno));
                    }
                }

                if (flwtype == "DNA检测-MCD检测") {
                    var smn = codes.indexOf(smcode);
                    var smno = 1;
                    if (smn > -1) {
                        smno = parseInt(codesdon[smn]) + 1;
                        codesdon[smn] = parseInt(codesdon[smn]) + 1;
                    } else {
                        if (!dotqnum || dotqnum < 1) {
                            dotqnum = 1;
                        }
                        else {
                            dotqnum = dotqnum + 1;
                        }
                        smno = dotqnum;
                        codes.push(smcode);
                        codesdon.push(smno);
                    }
                    objectup.push({
                        "ID": arrIds[i]["LIBID"],//关联更新ID
                        "DJC_JT_NO": jtno,
                        "SAMPLE_GENNO": smcode + getXX(smno) + "-1" //核酸编号
                    });
                } else {
                    var smn = codes.indexOf(smcode);
                    var smno = 1;
                    if (smn > -1) {
                        smno = parseInt(codesdon[smn]) + 1;
                        codesdon[smn] = parseInt(codesdon[smn]) + 1;
                    } else {
                        if (!dotqnum || dotqnum < 1) {
                            dotqnum = 1;
                        }
                        else {
                            dotqnum = dotqnum + 1;
                        }
                        smno = dotqnum;
                        codes.push(smcode);
                        codesdon.push(smno);
                    }
                    if (smno == 0) smno = 1;
                    objectup.push({
                        "ID": arrIds[i]["LIBID"],//关联更新ID
                        "SAMPLE_GENNO": smcode + "-" + smno //核酸编号
                    });

                }

            }
        }
        if (objectup.length == 0) {
            alertMsg("请选择未生成核酸编号的样品！");
        } else {
            var paramsup = { "tableName": "BIO_DNA_RNA_QC", "objects": objectup };
            var url = "system/jdbc/save/batch/table";
            putAddOrUpdata(url, paramsup, "是", "提取");
        }
    }

    ////////////---

    //核酸编号 xing
    var doGenNo = function () {
        var arrIds = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getGridSelectData(gridNameS2[i]);
            if (arrSubID.length != 0) {
                arrIds = arrIds.concat(arrSubID);
            }
        }
        if (arrIds.length == 0) {
            alertMsg("请至少选择一条样本记录进行操作!");
            return;
        }


        var objectup = [];
        var tasks = [];
        var taskjtnos = [];

        var codes = [];
        var codesdon = [];
        for (var i = 0; i < arrIds.length; i++) {
            //更新记录
            var jtno = 0;
            var taskid = arrIds[i]["TASK_ID"];
            var maxjt = arrIds[i]["DONJT"];
            var dotqnum = arrIds[i]["DOTQN"];
            var smcode = arrIds[i]["SAMPLE_CODE"];//样本编号
            var flwtype = arrIds[i]["MEHOD_JCFLOW"];
            var indexn = tasks.indexOf(taskid);
            var sano = arrIds[i]["SAMPLE_GENNO"];
            if (sano == null) {
                if (maxjt >= 1) {//存在最大编号了
                    if (indexn > -1) {
                        jtno = parseInt(taskjtnos[indexn]) + 1;
                        taskjtnos[indexn] = parseInt(jtno);
                    } else {
                        jtno = parseInt(maxjt);
                        tasks.push(taskid);
                        taskjtnos.push(parseInt(jtno));
                    }
                } else {
                    if (indexn > -1) {
                        jtno = parseInt(taskjtnos[indexn]) + 1;
                        taskjtnos[indexn] = parseInt(jtno);
                    } else {
                        jtno = 1;
                        tasks.push(taskid);
                        taskjtnos.push(parseInt(jtno));
                    }
                }

                if (flwtype == "DNA检测-MCD检测") {
                    var smn = codes.indexOf(smcode);
                    var smno = 1;
                    if (smn > -1) {
                        smno = parseInt(codesdon[smn]) + 1;
                        codesdon[smn] = parseInt(codesdon[smn]) + 1;
                    } else {
                        if (!dotqnum || dotqnum < 1) {
                            dotqnum = 1;
                        }
                        else {
                            dotqnum = dotqnum + 1;
                        }
                        smno = dotqnum;
                        codes.push(smcode);
                        codesdon.push(smno);
                    }
                    objectup.push({
                        "ID": arrIds[i]["LIBID"],//关联更新ID
                        "DJC_JT_NO": jtno,
                        "SAMPLE_GENNO": smcode + getXX(smno) //核酸编号
                    });
                } else {
                    var smn = codes.indexOf(smcode);
                    var smno = 1;
                    if (smn > -1) {
                        smno = parseInt(codesdon[smn]) + 1;
                        codesdon[smn] = parseInt(codesdon[smn]) + 1;
                    } else {
                        if (!dotqnum || dotqnum < 1) {
                            dotqnum = 1;
                        }
                        else {
                            dotqnum = dotqnum + 1;
                        }
                        smno = dotqnum;
                        codes.push(smcode);
                        codesdon.push(smno);
                    }
                    if (smno == 0) smno = 1;
                    objectup.push({
                        "ID": arrIds[i]["LIBID"],//关联更新ID
                        "SAMPLE_GENNO": smcode //核酸编号
                    });

                }

            }
        }
        if (objectup.length == 0) {
            alertMsg("请选择未生成核酸编号的样品！");
        } else {
            var paramsup = { "tableName": "BIO_DNA_RNA_QC", "objects": objectup };
            var url = "system/jdbc/save/batch/table";
            putAddOrUpdata(url, paramsup, "是", "提取");
        }
    }



    //////////////////////////////////////////////////////////////////////////////////////
    var getXX = function (n) {
        var s = "";
        for (var i = 1; i < n; i++) {
            s += "X";
        }
        return s;
    }
    //任务单状态修改
    var doTaskStatus = function () {
        debugger;
        var arrIds = getSelectData(gridNameDGrid);
        var arrIds1 = getGridSelectData(gridNameDGrid);
        if (arrIds.length != 1) {
            alertMsg("请选择一条记录进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/qc/task/uptaskstatus/uptaskstatus",
            title: "修改任务单状态.."
        };
        openWindow(winOpts, { "IDS": arrIds, "LSM_KEY": arrIds1[0]["LSM_KEY"], "CUSTOMFIELD_13229": arrIds1[0]["QCRESULT"], "SAMPLE_BATCHNO": arrIds1[0]["SAMPLE_BATCHNO"] });
    }


    var callBack = function () {
        refreshGrid();
    };

    var refreshGrid = function () {
        gridNameS = [];
        gridNameS2 = [];
        gridNameS3 = [];
        if (gridNameDGrid) {
            gridNameDGrid.dataSource.read();
        }
        if (gridNameD2Grid) {
            gridNameD2Grid.dataSource.read();
        }

        if (gridNameD3Grid) {
            gridNameD3Grid.dataSource.read();
        }
        if (gridNameD4Grid) {
            gridNameD4Grid.dataSource.read();
        }
        if (gridNameD5Grid) {
            gridNameD5Grid.dataSource.read();
        }
        if (gridNameD6Grid) {
            gridNameD6Grid.dataSource.read();
        }
        if (gridNameD7Grid) {
            gridNameD7Grid.dataSource.read();
        }
        if (gridNameD8Grid) {
            gridNameD8Grid.dataSource.read();
        }
    }

    var remove = function () {
        var arrg = [];
        var arrIds = [];
        var obj = [];
        for (var i = 0; i < gridNameS2.length; i++) {
            var arrSubID = getGridSelectData(gridNameS2[i]);
            arrg = arrg.concat(arrSubID);

        }
        if (arrg.length == 0) {
            alertMsg("请至少选择一条数据进行操作!");
            return;
        }
        for (var i = 0; i < arrg.length; i++) {
            arrIds.push(arrg[i]["LIBID"]);
            obj.push({ "ID": arrg[i]["ID"], "TASK_SM_STATUS": "待检测" });
        }
        confirmMsg("确认", "确定要对选中的记录进行移除吗?", "warn", function () {
            var urlsend = "system/jdbc/save/batch/table";
            var paramsadd1 = { "tableName": "BIO_TQ_TASK_MX", "objects": obj };
            putAddOrUpdata(urlsend, paramsadd1, "否", "提交");
            var params = { "tableName": "BIO_DNA_RNA_QC", "ids": arrIds };
            var url = "system/jdbc/delete/batch/table";
            deleteGridDataByIds(url, params, refreshGrid);
        });
    }
    var setjira = function () {
        debugger;
        var arrIds = getGridSelectData(gridNameDGrid);
        if (arrIds.length != 1) {
            alertMsg("请选择一条数据进行操作!");
            return;
        }
        var winOpts = {
            url: "biomarker/dispatch/qc/task/jira/jira",
            title: "jira信息填写..",
            width: 1280,
            height: 480,
            position: { "top": 100, "left": 30 }
        };

        var p = arrIds[0];
        openWindow(winOpts, {
            "ID": p["LSMID"],
            "TASK_TYPE": p["TASK_TYPE"],
            "MAIN_ID": p["ID"],
            "LSM_KEY": p["LSM_KEY"],//LSM关键字
            "LSM_KEY_P": p["LSM_KEY_P"],//LSM关键字
            "SAMPLE_BATCHNO": p["SAMPLE_BATCHNO"],//期号
            //"CUSTOMFIELD_13226":p[""],//提取完成日期
            "CUSTOMFIELD_14300": p["TASK_LDATE"],//提取检测标准完成日期--
            //"CUSTOMFIELD_13254":p[""],//提取检测暂停开始日期
            "CUSTOMFIELD_13237": p["TASK_REMARKS_DES"],//提取具体情况描述
            // "CUSTOMFIELD_13229"://样品合格情况
        });

    }

    //批量执行插入
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        refreshGrid();
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    function getRandomId() {
        return (('FDSX-HSEXE-') + new Date().getTime().toString(36) + Math.random().toString(36).slice(2));
    };
    var collection = function () {
        debugger;
        var PSLMap = {
            "SampleName": "PE_SAMPLE_NAME",
            "WellLabel": "PE_WELL_LABEL",
            "Comment": "PE_COMMENT",
            "%Purity": "PE_PURITY",
            "Type": "PE_TYPE",
            "Size[BP]": "PE_REGION_SIZE_BP",
            "Conc.(ng/ul)": "PE_CONC",
            "MigrationTime(sec)-Start": "PE_START",
            "MigrationTime(sec)-End": "PE_END",
            "UserComment": "PE_USERCOMMENT",
            "PeakCount": "PE_PEAKCOUNT",
            "TotalConc.(ng/ul)": "PE_TOTALCONC",
            "UpperMarkerTime(sec)": "PE_UPPERMARKERTIME",
            "EP650Size[BP]": "PE_EP650_SIZE",
            "EP650Conc.(ng/ul)": "PE_EP650_CONC",
            "EP400Size[BP]": "PE_EP400_SIZE",
            "EP400Conc.(ng/ul)": "PE_EP400_CONC",
            "EP1550Size[BP]": "PE_EP1550_SIZE",
            "EP1550Conc.(ng/ul)": "PE_EP1550_CONC",
            "Region[360-560]Size[BP]": "PE_REGION_360_560_SIZE_BP",
            "Region[360-560]Conc.(ng/ul)": "PE_REGION_360_560_CONC",
            "Region[450-650]Size[BP]": "PE_REGION_450_650_SIZE_BP",
            "Region[450-650]Conc.(ng/ul)": "PE_REGION_450_650_CONC",
            "Region[585-715]Size[BP]": "PE_REGION_585_715_SIZE_BP",
            "Region[585-715]Conc.(ng/ul)": "PE_REGION_585_715_CONC",
            "Result": "PE_RESULT",
            "EP580Size[BP]": "PE_EP580_SIZE",
            "EP580Conc.(ng/ul)": "PE_EP580_CONC",
            "EP550Size[BP]": "PE_EP550_SIZE",
            "EP550Conc.(ng/ul)": "PE_EP550_CONC",
            "EP450Size[BP]": "PE_EP450_SIZE",
            "EP450Conc.(ng/ul)": "PE_EP450_CONC",
            "EP500Size[BP]": "PE_EP500_SIZE",
            "EP500Conc.(ng/ul)": "PE_EP500_CONC",
            "Region[360-600]Size[BP]": "PE_REGION_360_600_SIZE_BP",
            "Region[360-600]Conc.(ng/ul)": "PE_REGION_360_600_CONC",
            "REGION[100-300]Size[BP]": "PE_REGION_100_300_SIZE_BP",
            "REGION[100-300]Conc.(ng/ul)": "PE_REGION_100_300_CONC",
            "Region[360-650]Size[BP]": "PE_REGION_360_650_SIZE_BP",
            "Region[360-650]Conc.(ng/ul)": "PE_REGION_360_650_CONC",
        };















        var arrIds = getGridSelectData(gridNameD8Grid);
        var sample;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: { "query": "query_YPD_BIO_TQ_TASK_MX_list", "objects": [[arrIds[0]["ID"]]], "search": { "EXE_TQQC2_ID": [arrIds[0]["ID"]] } },
            succeed: function (rs) {
                sample = rs.rows;             //样品
            }
        });
        var ares;
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: {
                "query": "query_BIO_PE_LIMS_SCAR_list",
                "objects": [],
                "search": { "LIMS_SCAR": sample[0]["AMPLIFY_REGIONAL"] }
            },
            succeed: function (rs) {
                ares = rs.rows;             //PE扩增区域
            }
        });
        var str = "MCD2";
        if (ares[0]["PE_SCAR"].indexOf("MCD2") == -1) { str = "MCD3" }

        var inobjjson;
        inobjjson = { "EXE_TQQC_ID": arrIds[0]["EX_DH_NO"], "Way": "手动", "PSLMap": PSLMap, "REGION": str }

        $.fn.ajaxPost({
            ajaxUrl: "/berry/automation/rowsingle/peMCDCollection",
            ajaxType: "post",
            ajaxAsync: false,
            ajaxData: inobjjson
        });
        alertMsg("提示:操作成功!");

    }
    var doUp2date = function () {
        var g = getGridSelectData(gridNameD2Grid);
        if (g.length == 0) {
            alertMsg("请至少选择一条记录进行操作!");
            return;
        }
        var objectup = [];
        for (var i = 0; i < g.length; i++) {

            objectup.push({
                "ID": g[i]["ID"],
                "EX_RE_STATUS2": "已分配"
            });

        }
        var urlsend = "system/jdbc/save/batch/table";
        var paramsup = { "tableName": "EXE_TQQC_SHEET", "objects": objectup };
        putAddOrUpdata(urlsend, paramsup, "是", "提交");
    }


    funcPushs(pathValue, {
        "initData": initData,
        "init": init,
        "doUp2date": doUp2date,
        "upsmStatus": upsmStatus,
        "addToEx": addToEx,
        "doTaskStatus": doTaskStatus,
        "edit": edit,
        "doOK": doOK,
        "doOK2": doOK2,
        "submit": submit,
        "editA": editA,
        "editB": editB,
        "doUpdate": doUpdate,
        "pesubmit": pesubmit,
        "doReturn": doReturn,
        "checkSample": checkSample,
        "collection": collection,
        "doReturn2": doReturn2,
        "remove": remove,
        "refreshGrid": refreshGrid,
        "callBack": callBack,
        "editjc": editjc,
        "editjc2": editjc2,
        "doDelete": doDelete,
        "doGenNo": doGenNo,
        "setjira": setjira,
    });
});