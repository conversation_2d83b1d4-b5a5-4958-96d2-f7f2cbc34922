$(document).ready(function() {
    var pathValue="biomarker-dispatch-qc-task-wwinfor-wwinfor";
        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"BIO_EXPERIMENT_OUTSOURCING"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */


var paramsValue;
    var init=function(params){
debugger;
	paramsValue=params;
      $("#EX_DH_NO"+pathValue).val(paramsValue);
             var time = new Date();
            var date;

               if(date>=26){

              date=nextMonthFirstDay();

               }else{
         date=sysNowTimeFuncParams["sysNowTime"];

             }

   getInfo("form",pathValue,{"W_ORDER_TIME":date});
    }
 
 
    var submit=function(){
        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }
   var nextMonthFirstDay=function(){
        var time = new Date();
        var year = time.getFullYear();
        var month = time.getMonth() + 2;
        if (month > 12) {
            month = month - 12;
            year = year + 1;
        }
        var day = 1;
        return year + ',' + month + ',' + day;
    }

     var func=function(){

    var wdc=$("#WW_DEPT_CODE"+pathValue).val(); 

       if(wdc=="521A001"){      $("#WW_DEPT"+pathValue).val("分子实验管理");  }      
       if(wdc=="430200103"){      $("#WW_DEPT"+pathValue).val("农学-DNA生信平台-基因组信息部");  }      
       if(wdc=="430200104"){      $("#WW_DEPT"+pathValue).val("农学-DNA生信平台-群体信息部");  }      
       if(wdc=="4403"){      $("#WW_DEPT"+pathValue).val("医学事业部-信息部");  }      
       if(wdc=="430200105"){      $("#WW_DEPT"+pathValue).val("农学-DNA生信平台-微生物二代");  }      
       if(wdc=="430200202"){      $("#WW_DEPT"+pathValue).val("农学-RNA生信平台-转录调控二代");  }      


     }

    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
		"func":func,
    });
 
 });