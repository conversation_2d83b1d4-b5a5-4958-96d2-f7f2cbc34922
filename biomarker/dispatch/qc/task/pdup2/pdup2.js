$(document).ready(function () {
    var pathValue = "biomarker-dispatch-qc-task-pdup2-pdup2";
    var paramsValue;
    var initData = function () {
        return {
            tableName: "EXE_TQQC_SHEET"
        };
    }
    var init = function (params) {
        paramsValue = params;
        getInfo("form", pathValue, params);
        var url = "system/jdbc/query/info/" + initData().tableName;
        getInfo("form", pathValue, params, url);
        var username = getLimsUser()["name"];
        var userid = getLimsUser()["id"];

    }
    var submit = function () {
        $("#EX_RE_STATUS2" + pathValue).val("核酸检测待接收");
        var ids = paramsValue["IDS"];
        var sdID = paramsValue["sdID"];
        var zdID = paramsValue["zdID"];
        var jsonData = getJsonByForm("form", pathValue);
        var object = [];
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = $("#EX_MAN2" + pathValue).val();
        for (var i = 0; i < ids.length; i++) {
            object.push($.extend({}, jsonData, { "ID": ids[i] }));
        }
       
        var params = { "tableName": "EXE_TQQC_SHEET", "objects": object };
        var url = "system/jdbc/save/batch/table";
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: url,
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var url = "system/jdbc/update/one/table/where";
                    if(sdID.length>0){
                        var  paramsup = { "tableName": "BIO_DNA_RNA_QC", "DJC_MAN": username, "DJC_ST_DATE": time, "where": { "EXE_TQQC_ID": sdID } }; //
                        putAddOrUpdata(url, paramsup, "否", "更新");
                    }
                    if(zdID.length>0){
                        var  paramsup = { "tableName": "BIO_DNA_RNA_QC", "DJC_MAN": username, "DJC_ST_DATE": time, "where": { "EXE_TQQC2_ID": zdID } }; //
                        putAddOrUpdata(url, paramsup, "否", "更新");
                    } 
                    doRequeDoUpTaskmxSmStatus(ids, "检测中");
                } else {
                    alertMsg("提示:操作失败!");
                }
            }
        });
    }
    //任务单-对应执行单下明细状态修改BIO_DNA_RNA_QC
    var doRequeDoUpTaskmxSmStatus = function (mainExIds, status) {
        var time = sysNowTimeFuncParams["sysNowTime"];
        var username = getLimsUser()["name"];
        var params = { "query": "doRequeDoUpTaskmxSmStatus", "objects": [mainExIds, mainExIds] }
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/query/one/table",
            ajaxType: "post",
            ajaxData: params,
            succeed: function (result) {
                if (result["code"] > 0) {
                    var objectup = [];
                    var objectupmain = [];
                    debugger;
                    var rows = result["rows"];
                    for (var i = 0; i < rows.length; i++) {
                        var row = rows[i];

                        if (objectupmain.indexOf(row["TASK_ID"]) < 0) {
                            objectupmain.push(row["TASK_ID"]);
                        }
                        //更新记录---明细
                        objectup.push({
                            "ID": row["TASKMXID"],
                            "TASK_SM_STATUS": status
                        });
                    }

                    var urlsend = "system/jdbc/save/batch/table";
                    var paramsup = { "tableName": "BIO_TQ_TASK_MX", "objects": objectup };
                    putAddOrUpdata(urlsend, paramsup, "是", "同步更新任务明细");

                    var urlmain = "system/jdbc/update/one/table/where";
                    var paramsupmain = {
                        "tableName": "BIO_TQ_TASK",
                        "TASK_STATUS": status,
                        "SYS_MAN_L": username,
                        "SYS_INSERTTIME_L": time,
                        "where": { "ID": objectupmain }
                    };
                    if(objectupmain.length>0){
                        putAddOrUpdata(urlmain, paramsupmain, "否", "同步更新任务");
                    }

                }
            },
            failed: function (result) {
                alertMsg("提示:操作异常!", "error");
            }
        });
    }
    var putAddOrUpdata = function (urls, inobjjson, isDoCallBack, errMsg) {
        $.fn.ajaxPost({
            ajaxType: "post",
            ajaxUrl: urls,
            ajaxData: inobjjson,
            succeed: function (result) {
                if (result["code"] > 0) {
                    if (isDoCallBack == "是") {
                        alertMsg("提示:操作成功!");
                        funcExce(pathValue + "pageCallBack");
                        funcExce(pathValue + "close");
                    }
                } else {
                    alertMsg(errMsg + "操作失败!");
                }
            }
        });
    }
    funcPushs(pathValue, {
        "init": init,
        "submit": submit,
    });

});