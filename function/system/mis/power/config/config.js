$(document).ready(function() {
    var pathValue="function-system-mis-power-config-config";

    var paramsValue;

    var manyUserGrid;
    var menuPointGrid;
    var queryListGrid;
    var operButtonGrid;

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        paramsValue=params;
        initManyUserGrid(params);
        initMenuPointGrid(params);
        initQueryListGrid(params);
        // initOperButtonGrid(params);
    }
    
    var initManyUserGrid=function(params){
        /**
          * 列表-按钮-定义-用户
          */
         var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addUserOpen"},
            {name:"delete",target:"deleteUserInfo"},
        ]);//工具条

        //请求参数
        var manyUserGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-192,
            read:{"query":"query_user_power_tmp_by_power","objects":[params["ID"]]},
        };
        manyUserGrid = initKendoGrid("#manyUserGrid"+pathValue,manyUserGridJson);//初始化表格的方法
    }
    var initMenuPointGrid=function(params){
        /**
         * 列表-按钮-定义-菜单
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addMenuOpen"},
            {name:"delete",target:"deleteMenuInfo"},
        ]);//工具条
        //请求参数
        var menuPointGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-192,
            read:{"query":"query_menu_power_tmp_by_power","objects":[params["ID"]]},
        };
        menuPointGrid = initKendoGrid("#menuPointGrid"+pathValue,menuPointGridJson);//初始化表格的方法
    }
    var initQueryListGrid=function(params){
        /**
         * 列表-按钮-定义-菜单
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"addQueryListOpen"},
            {name:"delete",target:"deleteQueryListInfo"},
            {name:"setting",target:"openQueryListColsConfig",title:"配置列表对应列权限"},
        ]);//工具条
        //请求参数
        var queryListGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "",//排序
            toolbar: toolbar,
            height: fullh-192,
            read:{"query":"query_power_grids_tmp_by_power","objects":[params["ID"]]},
        };
        queryListGrid = initKendoGrid("#queryListGrid"+pathValue,queryListGridJson);//初始化表格的方法
    }
    /**
     * 权限用户
     */
     var addUserOpen=function(componentId){
        openComponent({
            name:"数据源",//组件名称
            title:"用户",
            componentId:componentId,
            params:{"query":"query_component_mis_user_by_role_tmp"},
            settings:function(obj){
                var MIS_USER_IDS=[];
                if(isArray(obj)){
                    for(var index in obj){
                        MIS_USER_IDS.push(obj[index]["ID"]);
                    }
                }else{
                    MIS_USER_IDS.push(obj["ID"]);
                }
                //用户关联权限
                $.fn.ajaxPost({
                    ajaxType:"post",
                    ajaxUrl:"system/config/power/save/userTemp",
                    ajaxData:{"MIS_POWER_ID":paramsValue["ID"],"MIS_USER_IDS":MIS_USER_IDS},
                    succeed:function(result){
                        if(result["code"]>0){
                            refreshGrid(manyUserGrid);
                            alertMsg("添加成功","success");
                        }else{
                            alertMsg("添加失败","error");
                        }
                    }
                });
            }
        });
     }
     /**
      * 菜单权限
      */
     var addMenuOpen=function(componentId){
        openComponent({
            name:"数据源",//组件名称
            title:"菜单分层级",
            componentId:componentId,
            params:{"query":"query_component_mis_menu_level"},
            settings:function(obj){
                var MIS_MENU_IDS=[];
                if(isArray(obj)){
                    for(var index in obj){
                        MIS_MENU_IDS.push(obj[index]["ID"]);
                    }
                }else{
                    MIS_MENU_IDS.push(obj["ID"]);
                }
                //用户关联权限
                $.fn.ajaxPost({
                    ajaxType:"post",
                    ajaxUrl:"system/config/power/save/menuTemp",
                    ajaxData:{"MIS_POWER_ID":paramsValue["ID"],"MIS_MENU_IDS":MIS_MENU_IDS},
                    succeed:function(result){
                        if(result["code"]>0){
                            refreshGrid(menuPointGrid);
                            alertMsg("添加成功","success");
                        }else{
                            alertMsg("添加失败","error");
                        }
                    }
                });
            }
        });
    }
    /**
      * 查询列表-权限
      */
    var addQueryListOpen=function(componentId){
        openComponent({
            name:"数据源",//组件名称
            title:"列表",
            componentId:componentId,
            params:{"query":"query_component_sys_func_grids"},
            settings:function(obj){
                var SYS_FUNC_GRIDS_IDS=[];
                if(isArray(obj)){
                    for(var index in obj){
                        SYS_FUNC_GRIDS_IDS.push(obj[index]["ID"]);
                    }
                }else{
                    SYS_FUNC_GRIDS_IDS.push(obj["ID"]);
                }
                //用户关联权限
                $.fn.ajaxPost({
                    ajaxType:"post",
                    ajaxUrl:"system/config/power/save/queryListTemp",
                    ajaxData:{"MIS_POWER_ID":paramsValue["ID"],"SYS_FUNC_GRIDS_IDS":SYS_FUNC_GRIDS_IDS},
                    succeed:function(result){
                        if(result["code"]>0){
                            refreshGrid(queryListGrid);
                            alertMsg("添加成功","success");
                        }else{
                            alertMsg("添加失败","error");
                        }
                    }
                });
            }
        });
    }

    var openQueryListColsConfig=function(){
        var arrIds=getSelectData(queryListGrid,"SYS_FUNC_GRIDS_ID");
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行操作!");
            return ;
        }
        openWindow({
            url:"function/system/mis/power/config/listColsConfig",
            title:"列表-列-权限配置.."
        },{"MIS_POWER_ID":paramsValue["ID"],"SYS_FUNC_GRIDS_IDS":arrIds});
    }

    var callBack=function(){
        if(manyUserGrid){
            manyUserGrid.dataSource.read();//重新读取
        }
    }

    /**
     * 刷新
     * @param {} grid 
     */
    var refreshGrid=function(grid){
        if(grid){
            if(grid.dataSource){
                grid.dataSource.read();//重新读取--刷新
                return;
            }
        }
        if(manyUserGrid){
            manyUserGrid.dataSource.read();//重新读取--刷新
        }
        if(menuPointGrid){
            menuPointGrid.dataSource.read();//重新读取--刷新
        }
        if(queryListGrid){
            queryListGrid.dataSource.read();//重新读取--刷新
        }
    }

    var deleteUserInfo=function(){
        var arrIds=getSelectData(manyUserGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"USER_POWER_TMP","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
    }

    var deleteMenuInfo=function(){
        var arrIds=getSelectData(menuPointGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"MENU_POWER_TMP","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
    }

    var deleteQueryListInfo=function(){
        var arrIds=getSelectData(queryListGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"POWER_GRIDS_TMP","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
    }
 
    funcPushs(pathValue,{
        "init":init,//初始化方法-在加载完初始化数据之后执行
        "addUserOpen":addUserOpen,//打开添加-用户
        "deleteUserInfo":deleteUserInfo,//删除此权限对应用户
        "addMenuOpen":addMenuOpen,//打开添加-菜单
        "deleteMenuInfo":deleteMenuInfo,//删除此权限对应菜单
        "addQueryListOpen":addQueryListOpen,//打开添加-列表查询权
        "deleteQueryListInfo":deleteQueryListInfo,//删除此权限对应列表查询权
        "openQueryListColsConfig":openQueryListColsConfig,
        "refreshGrid":refreshGrid,
        "callBack":callBack,//回调方法
    });
 });
 