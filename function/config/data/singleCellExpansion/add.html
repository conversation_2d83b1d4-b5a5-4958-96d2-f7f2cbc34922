<script id="function-config-data-singleCellExpansion-addTemp" type="text/x-kendo-template">
    
    <div class="card">
            <h5 class="card-header text-sm-right">
               
                
                        <button class="k-button k-button-lg k-state-selected mt-3 mt-lg-0" id="searchBtn" type="button" onclick="$('#window').data('kendoWindow').close();">保存</button>
                
                    

            </h5>
            <div class="card-body">
                <form class="form-horizontal">
                    <div class="form-group row col-sm-12">
                            
                          <div class="col-sm-4">
                                <label class="d-block">任务单编号：</label>
                            <input class="k-textbox" id="domValidator1" name="domValidator1" type="text" placeholder="任务单编号" required data-required-msg="请输入姓名！" pattern="[\u4E00-\u9FA5]{1,10}" data-pattern-msg="请输入1-10个汉字！"/>
                            
                          </div>
                          
                          <div class="col-sm-4">
                                <label class="d-block">重做原因：</label>
                                <input class="k-textbox" id="domValidator1" name="domValidator1" type="text" placeholder="重做原因" required data-required-msg="请输入姓名！" pattern="[\u4E00-\u9FA5]{1,10}" data-pattern-msg="请输入1-10个汉字！"/>
                        <span class="k-invalid-msg" data-for="birthday"></span>
                          </div>
                    </div>
                  
                    <div class="form-group row col-sm-12">
                            <div class="col-sm-4">
                                    <label class="d-block">指派实验员：</label>
                                    <input class="k-textbox" id="domValidator1" name="domValidator1" type="text" placeholder="指派实验员" required data-required-msg="请输入姓名！" pattern="[\u4E00-\u9FA5]{1,10}" data-pattern-msg="请输入1-10个汉字！"/>
                            <span class="k-invalid-msg" data-for="birthday"></span>
                              </div>
                              <div class="col-sm-4">
                                    <label class="d-block">样品位置：</label>
                                    <input class="k-textbox" id="domValidator1" name="domValidator1" type="text" placeholder="样品位置" required data-required-msg="请输入姓名！" pattern="[\u4E00-\u9FA5]{1,10}" data-pattern-msg="请输入1-10个汉字！"/>
                            <span class="k-invalid-msg" data-for="birthday"></span>
                              </div>


                    </div>
                    <div class="form-group row col-sm-12">
                            
                        <div class="col-sm-4">
                                <label class="d-block">开始时间：</label>
                                <input   id="birthday" name="domValidator1" type="date"/>
                        <span class="k-invalid-msg" data-for="birthday"></span>
                          </div>
                          <div class="col-sm-4">
                                <label class="d-block">结束时间：</label>
                                <input id="birthday1"  type="date"/>
                        <span class="k-invalid-msg" data-for="birthday"></span>
                          </div>


                </div>
                        
               
            <div class="form-group row col-sm-12">
                 <div class="col-sm-4">
                        <label class="d-block">是否加急办理：</label>
                    <select  id="isno" name="isno" required data-required-msg="请选择！">
                          
                            <option value="">[--请选择--]</option>
                             <option value="正常周期">正常周期</option>
                             <option value="10天">10天</option>
                             <option value="20天">20天</option>
                             <option value="加急">加急</option>
                             <option value="关注">关注</option>
                             
                       
                             
                         </select>
                         <span class="k-invalid-msg" data-for="isno"></span>
                 </div>
                
                 <div class="col-sm-4">
                        <label class="d-block">检测地区：</label>
                        <select  id="fenbianlv" class="w-200" name="fenbianlv" required data-required-msg="请选择分辨率！">
                                
                                <option value="">[--请选择--]</option>
                                <option value="苏州">苏州</option>
                                
                            </select>
                            <span class="k-invalid-msg" data-for="fenbianlv"></span>
                 </div>
        </div>
        
        <div class="form-group row col-sm-12">
                <div class="col-sm-4">
                    <label class="d-block">附件：</label>
                    <span class="k-textbox hk-file">
                           
                        <input id="inputBoxMap" name="inputBoxMap" type="text" placeholder="请点击按右侧按钮操作上传文件">
                        
                  <!--       //清空 -->
                        <a class="k-icon k-i-close k-required hk-file-icon-0" style="cursor :pointer;" onclick="$(this).prev().val('');"></a>
                    <!--     //下载 -->
                        <a class="k-icon  k-i-download k-required hk-file-icon-1" style="cursor :pointer;" onclick="∑download|$(this).parent().find('input[type=text]').val());"></a>
                   <!--      //上传 -->
                        <a class="k-icon  k-i-folder-open k-required hk-file-icon-2" style="cursor :pointer;" onclick="$(this).parent().find('input[type=file]').click();"></a>
                        <div style="display:none;" > <input class="w-100" onchange="∑upload|this);" multiple="files"   type="file"> </div>
                     </span>
            </div>     
            <div class="col-sm-4">
                    <label class="d-block">备注：</label> 
                    <textarea style="resize:none;" class="k-textarea w-100" name="summary" placeholder="文本域框" required data-required-msg="请输入自我介绍！"></textarea>
                </div>       
        </div>
        
        <div class="form-group row col-sm-12">
                
            </div>

                        <!-- <div class="col-lg-2 text-center text-lg-left">
                            <button class="k-button k-button-lg k-state-selected mt-3 mt-lg-0" id="searchBtn" type="button" onclick="conditionSearch();">保存</button>
                        </div> -->
                    </div>                    
                </form>
            </div>
        </div>

    <div id="detailGrid"></div>

    <div id="sub-window"></div>

    <template id="sub-detail-template"></template>


</script>

