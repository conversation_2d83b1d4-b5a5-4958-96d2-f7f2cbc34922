$(document).ready(function() {

    // 获取数据源生成表格
    $("#detailGrid").kendoGrid({
        dataSource: {
           
            data:[{
                NUM:"1",
                S_MAN_NAME:"苏州实验室301",
                S_CODE:"YK_SZ_001",
                S_TIME:"扩增仪6500-1",
                S_TYPE:"A公司"
            },{
                NUM:"2",
                S_MAN_NAME:"苏州实验室302",
                S_CODE:"YK_SZ_002",
                S_TIME:"扩增仪9800-1",
                S_TYPE:"A公司"
            },{
                NUM:"3",
                S_MAN_NAME:"苏州实验室303",
                S_CODE:"YK_SZ_003",
                S_TIME:"扩增仪4500-1",
                S_TYPE:"A公司"
            }]
        },
        height: 700,
        toolbar:[
            {
              template: '<a class="k-button" href="javascript:addUpdateMX()"><span class="k-icon k-i-plus"></span>添加</a>'
            },"search"
        ],
        scrollable: true,
        selectable:"multiple",
        batch:true,
        filterable: {
		  
                //extra: false,//是否双过滤条件，默认为true
                //multi:false,//多过滤条件
                search:true//查询框
                //mode: "menu"//过滤的显示方式，可选值有"row"、"menu"、"menu, row"，row是标题行下加过滤单元格，menu是菜单
                
            },
        sortable: true,//是否可排序
        pageable: true,
        
        columns: [{ field: "ID", title: "唯一标识",hidden: true },
        {  selectable: true, width: "60px" },

        { field: "NUM", title: "序号",width:"60px" ,enableColumnResizing: false },
        { field: "S_MAN_NAME", title: "位置",width:"180px" },
        { field: "S_CODE", title: "仪器型号",width:"180px" },
        { field: "S_TIME", title: "仪器名称",width:"180px" },
        { field: "S_TYPE", title: "厂家",width:"180px" }
        // { field: "S_MAN_NAME", title: "样本浓度(ng/μl)",width:"180px" ,type:"string"},
        // { field: "S_WOMEN_NAME", title: "男方姓名",width:"180px",type:"string" },
        // { field: "S_WOMEN_AGE", title: "女方姓名",width:"180px",type:"string" },
        // { field: "S_MAN_AGE", title: "男方年龄",width:"180px" ,type:"string"},
        // { field: "S_WOMEN_AGE", title: "女方年龄",width:"180px" ,type:"string"},
        // { field: "S_MAIN_JY", title: "关注基因",width:"180px",type:"string" },
        // { field: "S_TB_WD", title: "突变位点",width:"180px" ,type:"string"},
        // { field: "S_S_TYPE", title: "样本类型",width:"180px" ,type:"string"},
        // { field: "S_MX_NAME", title: "样本名称",width:"180px" ,type:"string"},
        // { field: "S_T_CODE", title: "样本条码",width:"180px" ,type:"string"},
        
        ],

    });
});

 