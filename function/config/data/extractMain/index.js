$(document).ready(function() {
    var pathValue="function-config-data-extractMain-index";

    /**
     * 初始加载
     * @param {*} params 
     */
    var init=function(params){

   
   
    //待分派
    // 获取数据源生成表格
    $("#powerGrid"+pathValue).kendoGrid({
        dataSource: {
             
            data:[
                {ID:"1",NUM:"1",TQ_CODE:"TQ201911004",TASK_MAN:"张三",OPERATION_AREA:"301实验室",TASK_TIME:"2019-11-19",TASK_STA:"未审核"},
                {ID:"2",NUM:"2",TQ_CODE:"TQ201911005",TASK_MAN:"李四",OPERATION_AREA:"302实验室",TASK_TIME:"2019-11-19",TASK_STA:"已审核"}]
        
        },
        
        height: 750,
        toolbar:[{
            template: '<a class="k-button" href="javascript:funcExce(\''+pathValue+'open\');"><span class="k-icon k-i-plus"></span>添加</a>'
          },{
            template: '<a class="k-button" href="javascript:funcExce(\''+pathValue+'open3\');"><span class="k-icon k-i-save"></span>保存</a>'
          },{
            template: '<a class="k-button" href="javascript:funcExce(\''+pathValue+'gotoNext\');"><span class="k-icon k-i-plus"></span>提交</a>'
          },"excel"],
          scrollable: true,
          //selectable: true,
          selectable:"multiple",
          //resizable:true,
          // filterable: true,
          sortable: true,
          pageable: true,
        columns: [
            { field: "ID", title: "唯一标识",hidden: true },
            { selectable: true, width: '20px' },      
            { field: "NUM", title: "序号",width:"40px" },                                                  
            { field: "TQ_CODE", title: "任务单号",width:"160px" ,template:"<a href='javascript:funcExce(\""+pathValue+"open\");' style='color:blue;cursor:pointer'>#:TQ_CODE#</a>"},
            { field: "TASK_MAN", title: "操作人",width:"180px" , template:"<a href='javascript:funcExce(\""+pathValue+"open\");' style='color:blue;cursor:pointer'>#:TASK_MAN#</a>"},
            { field: "OPERATION_AREA", title: "操作地点",width:"180px"},
            { field: "TASK_TIME", title: "操作时间",width:"180px"}

        ],

    });

    //已处理
    $("#userGrid"+pathValue).kendoGrid({
        dataSource: {
             
            data:[
                // 操作区域 试剂盒批次 试剂盒名称	试剂盒号			试剂效期	开瓶日期
                // OPERATION_AREA KIT_BATCH KIT_NAME  KIT_NUMBER   KIT_TIME KIT_FIRST_TIME
                {ID:"1",NUM:"1",TQ_CODE:"TQ201911001",TASK_MAN:"张三",OPERATION_AREA:"301实验室",TASK_TIME:"2019-11-19",TASK_STA:"已审核"},
                {ID:"2",NUM:"2",TQ_CODE:"TQ201911002",TASK_MAN:"路易斯",OPERATION_AREA:"301实验室",TASK_TIME:"2019-11-19",TASK_STA:"已审核"},
                {ID:"3",NUM:"3",TQ_CODE:"TQ201911003",TASK_MAN:"商卢",OPERATION_AREA:"301实验室",TASK_TIME:"2019-11-19",TASK_STA:"已审核"}]
      
        },
        
        height: 750,
        toolbar:["excel"],
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
            { field: "ID", title: "唯一标识",hidden: true },
            { locked: false, selectable: true, width: '20px' },
            { field: "NUM", title: "序号",width:"40px" },     
            { field: "TQ_CODE", title: "任务单号",width:"160px" },
            { field: "TASK_MAN", title: "操作人",width:"180px" },
            { field: "OPERATION_AREA", title: "操作地点",width:"180px"},
            { field: "TASK_TIME", title: "操作时间",width:"180px"},
            { field: "TASK_STA", title: "审核状态",width:"180px"}

        ],

    });
}

    var open=function(){
        // loadWindow(winOpts);
         var params={"id":"xxxx"};
         var winOpts={
            url:"function/config/data/extractMain/addSampleMain",
            title:"提取任务分派"
         };
         
         var dialog = openWindow(winOpts,params);
         //console.log(globalTabParameters[replaceUrl(winOpts.url)]);
     }

    var gotoNext = function(){
        var params={"id":"xxxx"};
        var winOpts={
           url:"function/config/data/extractMain/selectMan",
           title:"操作人选择"
        };
        
        var dialog = openWindow(winOpts,params);
    }

     funcPushs(pathValue,{
         "init":init,
         "gotoNext":gotoNext,
         "open":open
     });
     

});


 




