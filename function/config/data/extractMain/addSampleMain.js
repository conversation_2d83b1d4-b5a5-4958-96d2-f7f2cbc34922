$(document).ready(function() {
    var pathValue="function-config-data-extractMain-addSampleMain";
 
    var initData=function(){
        // return{
        //     TASK_CODE:"xxxxx",
        //     REDO_INFO:{
        //         id:"dropDownList",//下拉
        //         dataSource: [],
        //         value:""
        //     },
        //     tableName:"TQ_TASK"
        // };
    }

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     * @param {*} path 当前路径 "function-product-sampleManager-clinicalSample-add"
     */
    var init=function(params){
        
   
        // var REDO_INFODataSource = new kendo.data.DataSource({
        //     data: [
        //         { text: '数据量不足', value: '数据量不足' },
        //         { text: '操作不规范', value: '操作不规范' },
        //         { text: '数据污染', value: '数据污染' },
        //     ]
        // });
        // $("#grid").data("kendoGrid").dataSource.data(bob);
        // $("#REDO_INFO"+pathValue).data("dropDownList").setDataSource(
        //     data:REDO_INFODataSource
        // );
    var jsonData = $("#form"+pathValue).serializeArray();
    console.log(jsonData);

    $("#maindiv").hide();
        

    $('#bloodType').kendoDropDownList();
    $('#fenbianlv').kendoDropDownList();
    $('#isno').kendoDropDownList();
    $("#birthday").kendoDatePicker(); 
    $("#customValidator").mouseover(function(){
        $("#dddd").show();
          
    });
    $("#customValidator").mouseout(function(){
        $("#dddd").hide();
    });
    
    $('#domValidator').kendoValidator();
    // 自定义提示验证
    $('#domValidator1').kendoValidator({
        rules: {
            required: function (input) {
                if (!input.is('#domValidator1')) {
                    return true;
                }
                input.attr('data-required-msg', '请输入姓名！');
                return input.val() !== '';
            },
            pattern: function (input) {
                if (!input.is('#domValidator1')) {
                    return true;
                }
                input.attr('data-pattern-msg', '请输入1-10个汉字！');
                return input.val().match(/^[\u4E00-\u9FA5]{1,10}$     /) !== null;
            }
        },
        errorTemplate:
            '<div class="k-tooltip" >' +
                '<div class="k-tooltip-content">' +
                    '<i class="fas fa-exclamation-triangle mr-2"></i>#= message #' +
                '</div>' +
                '<div class="k-callout k-callout-2"></div>' +
            '</div>'
    });


    /**
     * 列表-按钮-定义
     */

    var toolbar=getButtonTemplates(pathValue,[

        // {name:"add",target:"lookSample",title:"查看样本"},
        //{name:"edit",target:"open"},
        {name:"save",target:"yes",title:"显示/关闭主单"},
        {name:"delete",target:"deleteMX",title:"删除"}

    ]);//工具条

    //获取数据源生成表格
    $("#detailGrid"+pathValue).kendoGrid({
        
        dataSource: {

             data:[
                
                 {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小月",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小雪",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
         },
        height: 550,
        toolbar:toolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
        { field: "ID", title: "唯一标识",hidden: true },
        { locked: true, selectable: true, width: '40px' },
        { field: "NUM", title: "序号",width:"60px" },
        { field: "SAMPLE_BOX_CODE", title: "送检单编号",width:"180px" },
        { field: "JC_ITEM", title: "女方姓名",width:"180px" },
        { field: "C_SOP_CODE", title: "样本名称",width:"180px" },
        { field: "C_SOP_NAME", title: "样本类型",width:"180px" },
        { field: "SAMPLE_SJYY", title: "样本条码",width:"180px" },
        { field: "SAMPLE_SJYY", title: "基因",width:"180px" },
        { field: "C_SJ_ADRRESS", title: "是否重提",width:"180px" },
        { field: "SAMPLE_NUM", title: "是否要求剩余原始样本",width:"180px" },
        { field: "C_YS_CON", title: "是否要求返样",width:"180px" },
        { field: "SAMPLE_SJYS", title: "备注",width:"180px" }

      

        ],

    });

    $("#powerGrid"+pathValue).kendoGrid({
        dataSource: {
             
            data:[
                {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小白",
                 C_SOP_CODE:"小黑",C_SOP_NAME:"DNA",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小白",
                 C_SOP_CODE:"小白",C_SOP_NAME:"DNA",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
        },
        
        height: 750,
        toolbar:[
        //     {
        //     template: '<a class="k-button" href="javascript:funcExce(\'open\');"><span class="k-icon k-i-plus"></span>添加</a>'
        //   },
          {
            template: '<a class="k-button" href="javascript:open3()"><span class="k-icon k-i-save"></span>添加样本</a>'
          }
        //   ,"excel","pdf"
        ],
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
            { field: "ID", title: "唯一标识",hidden: true },
            { locked: true, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "SAMPLE_BOX_CODE", title: "送检单编号",width:"180px" },
            { field: "JC_ITEM", title: "女方姓名",width:"180px" },
            { field: "C_SOP_CODE", title: "样本名称",width:"180px" },
            { field: "C_SOP_NAME", title: "样本类型",width:"180px" },
            { field: "SAMPLE_SJYY", title: "样本条码",width:"180px" },
            { field: "SAMPLE_SJYY", title: "基因",width:"180px" },
            { field: "SAMPLE_SJYS", title: "备注",width:"180px" }


        ],

    });

    //已处理
    $("#userGrid"+pathValue).kendoGrid({
        dataSource: {
             
            data:[

                {ID:"1",NUM:"1",TQ_CODE:"YK2019-001",TASK_MAN:"3",OPERATION_AREA:"张三",TASK_TIME:"2019-11-29",TASK_STA:"DNA"},
                {ID:"2",NUM:"2",TQ_CODE:"YK2019-002",TASK_MAN:"15",OPERATION_AREA:"李四",TASK_TIME:"2019-11-29",TASK_STA:"DAN"},
                {ID:"3",NUM:"3",TQ_CODE:"YK2019-003",TASK_MAN:"24",OPERATION_AREA:"王麻子",TASK_TIME:"2019-11-29",TASK_STA:"血夜"}]
      
        },
        
        height: 750,
        toolbar:[{
            template: '<a class="k-button" href="javascript:open3()"><span class="k-icon k-i-save"></span>添加样本</a>'
          }],
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
            { field: "ID", title: "唯一标识",hidden: true },
            { locked: false, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "TQ_CODE", title: "项目编号",width:"160px" },
            { field: "TASK_STA", title: "项目类型",width:"180px"},
            { field: "TASK_MAN", title: "样本数",width:"180px" },
            { field: "OPERATION_AREA", title: "操作人",width:"180px"},
            { field: "TASK_TIME", title: "操作时间",width:"180px"}

        ],

    });
    

    $("#YXpowerGrid"+pathValue).kendoGrid({
        dataSource: {
             
            data:[
                {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小白",
                 C_SOP_CODE:"小黑",C_SOP_NAME:"DNA",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小白",
                 C_SOP_CODE:"小白",C_SOP_NAME:"DNA",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
        },
        
        height: 750,
        toolbar:toolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
            { field: "ID", title: "唯一标识",hidden: true },
            { locked: true, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "SAMPLE_BOX_CODE", title: "送检单编号",width:"180px" },
            { field: "JC_ITEM", title: "女方姓名",width:"180px" },
            { field: "C_SOP_CODE", title: "样本名称",width:"180px" },
            { field: "C_SOP_NAME", title: "样本类型",width:"180px" },
            { field: "SAMPLE_SJYY", title: "样本条码",width:"180px" },
            { field: "SAMPLE_SJYY", title: "基因",width:"180px" },
            { field: "SAMPLE_SJYY", title: "是否剩余原始样本",width:"180px" },
            { field: "SAMPLE_SJYY", title: "是否返样",width:"180px" },
            { field: "SAMPLE_SJYY", title: "是否重提",width:"180px" },
           
            { field: "SAMPLE_SJYS", title: "备注",width:"180px" }


        ],

    });
    
    }


    var consoleJsonFrom=function(){
        var jsonData = getJsonByForm("form",pathValue);
        console.log(jsonData);
    }


    var lookSample=function(){
        var params={"id":"xxxx"};
        var winOpts={
            url:"function/config/data/extractMain/listSampleMx",
            title:"添加样本",
            window:"#sub-window",
            windowTemplate:"#sub-detail-template"
        };
        var dialog = openWindow(winOpts,params);
    }


    var submit=function(){
        alert("123");  

        formSubmit({
            url:"system/jdbc/save/one/table",
            formId:"form",
            pathValue:pathValue,
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("提交成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                    });
                }else{
                    alertMsg("提交失败","error");
                }
            }
        });
    }

    var deleteMX = function(){
        alert("提示：删除成功！");
         
    }

    var yes=function(){

        if($("#maindiv").is(":hidden")){
            $("#maindiv").show();    //如果元素为隐藏,则将它显现
        }else{
            $("#maindiv").hide();     //如果元素为显现,则将其隐藏
        }
    }

    var gotoNext = function(){
        var params={"id":"xxxx"};
        var winOpts={
           url:"function/config/data/extractMain/selectMan",
           title:"操作人选择"
        };
        
        var dialog = openWindow(winOpts,params);
    }

    /**
     * 注册方法
     */
    funcPushs(pathValue,{
        "init":init,
        "lookSample":lookSample,
        "yes":yes,
        "gotoNext":gotoNext,
        "deleteMX":deleteMX,
        "consoleJsonFrom":consoleJsonFrom,
        //删除
        "submit":submit,
        "initData":initData
    });


});
