$(document).ready(function() {
    
    
    var pathValue="function-config-data-instrumentUpPooling-addSampleMain";

    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     * @param {*} path 当前路径 
     */
    var init=function(params){
        
    var jsonData = $("#form"+pathValue).serializeArray();
    console.log(jsonData);

    $('#bloodType').kendoDropDownList();
    $('#fenbianlv').kendoDropDownList();
    $('#isno').kendoDropDownList();
    $("#birthday").kendoDatePicker(); 
    $("#customValidator").mouseover(function(){
        $("#dddd").show();
          
    });
    $("#customValidator").mouseout(function(){
        $("#dddd").hide();
    });
    
    $('#domValidator').kendoValidator();
    // 自定义提示验证
     


    /**
     * 列表-按钮-定义
     */

    var toolbar=getButtonTemplates(pathValue,[

        {name:"add",target:"lookSample",title:"一次pooling"},
        {name:"add",target:"",title:"自动计算"},
        {name:"excel",target:"",title:"修改"},
        {name:"add",target:"lookSample",title:"批量修改"},
        {name:"add",target:"",title:"导出样本位置表"},
        {name:"excel",target:"",title:"导出文库pooling表"},
        {name:"add",target:"lookSample",title:"导出文库QPCR表"}
        
    ]);//工具条

    //获取数据源生成表格
    $("#detailGrid"+pathValue).kendoGrid({
        
        dataSource: {

             data:[
                
                 {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小月",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小雪",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
         },
        height: 550,
        toolbar:toolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
        { field: "ID", title: "唯一标识",hidden: true },
        { locked: true, selectable: true, width: '40px' },
        { field: "NUM", title: "序号",width:"60px" },
        { field: "SAMPLE_NUM", title: "pooling文库名称",width:"180px" },
        { field: "SAMPLE_BOX_CODE", title: "混样文库名称",width:"180px" },
        { field: "SAMPLE_SJYY", title: "建库方法",width:"180px" },
        { field: "JC_ITEM", title: "数据量",width:"180px" },
        { field: "SAMPLE_SJYS", title: "混样文库浓度",width:"180px" },
        { field: "C_SJ_ADRRESS", title: "混样文库体积",width:"180px" },
        { field: "C_YS_CON", title: "实际浓度",width:"180px" },
        { field: "C_W_NAME", title: "校准浓度",width:"180px" },
        { field: "C_W_AGE", title: "稀释倍数",width:"180px" },
        { field: "C_W_SEX", title: "混合量",width:"80px" },
        // { field: "C_W_PHONE", title: "受检人联系电话",width:"180px" },
        // { field: "C_CASH_TYPE", title: "回款方式",width:"180px" },
        // { field: "C_IS_ZQ", title: "有无帐期",width:"180px" },
        // { field: "C_CLENT_PRICE", title: "送检总价",width:"180px" },
        // { field: "C_SALE_MAN", title: "销售员",width:"180px" },
        // { field: "C_IS_CHECK", title: "审核状态",width:"180px" },
        // { field: "C_SALE_REMARK", title: "备注",width:"180px" },
        // { field: "C_IS_PACKING_CHARGE", title: "是否打包收费",width:"180px" },
        // { field: "C_SEPARATE_BILL", title: "是否拆单",width:"180px" }

        ],

    });

    var HYtoolbar=getButtonTemplates(pathValue,[

        {name:"add",target:"",title:"二次pooling"},
        {name:"add",target:"",title:"pooling移除"},
        {name:"add",target:"",title:"自动计算"},
        {name:"excel",target:"",title:"修改"},
        {name:"add",target:"lookSample",title:"批量修改"},
        {name:"add",target:"",title:"导出样本位置表"},
        {name:"excel",target:"",title:"导出文库pooling表"},
        {name:"add",target:"lookSample",title:"导出文库QPCR表"}
        
    ]);//工具条

    //获取数据源生成表格
    $("#HYdetailGrid"+pathValue).kendoGrid({
        
        dataSource: {

             data:[
                
                 {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小月",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小雪",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
         },
        height: 550,
        toolbar:HYtoolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [							

            { field: "ID", title: "唯一标识",hidden: true },
            { locked: true, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "SAMPLE_NUM", title: "pooling文库名称",width:"180px" },
            { field: "SAMPLE_BOX_CODE", title: "混样文库名称",width:"180px" },
            { field: "SAMPLE_SJYY", title: "建库方法",width:"180px" },
            { field: "JC_ITEM", title: "数据量",width:"180px" },
            { field: "SAMPLE_SJYS", title: "混样文库浓度",width:"180px" },
            { field: "C_SJ_ADRRESS", title: "混样文库体积",width:"180px" },
            { field: "C_YS_CON", title: "实际浓度",width:"180px" },
            { field: "C_W_NAME", title: "校准浓度",width:"180px" },
            { field: "C_W_AGE", title: "稀释倍数",width:"180px" },
            { field: "C_W_SEX", title: "混合量",width:"80px" },
        
        // { field: "C_CLENT_PRICE", title: "送检总价",width:"180px" },
        // { field: "C_SALE_MAN", title: "销售员",width:"180px" },
        // { field: "C_IS_CHECK", title: "审核状态",width:"180px" },
        // { field: "C_SALE_REMARK", title: "备注",width:"180px" },
        // { field: "C_IS_PACKING_CHARGE", title: "是否打包收费",width:"180px" },
        // { field: "C_SEPARATE_BILL", title: "是否拆单",width:"180px" }

        ],

    });

    var TWOtoolbar=getButtonTemplates(pathValue,[

        {name:"add",target:"",title:"三次pooling"},
        {name:"add",target:"",title:"pooling移除"},
        {name:"add",target:"",title:"自动计算"},
        {name:"excel",target:"",title:"修改"},
        {name:"add",target:"lookSample",title:"批量修改"},
        {name:"add",target:"",title:"导出样本位置表"},
        {name:"excel",target:"",title:"导出文库pooling表"},
        {name:"add",target:"lookSample",title:"导出文库QPCR表"}
        
    ]);//工具条

    //获取数据源生成表格
    $("#TWOdetailGrid"+pathValue).kendoGrid({
        
        dataSource: {

             data:[
                
                 {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小月",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小雪",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
         },
        height: 550,
        toolbar:TWOtoolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [							

            { field: "ID", title: "唯一标识",hidden: true },
            { locked: true, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "SAMPLE_NUM", title: "pooling文库名称",width:"180px" },
            { field: "SAMPLE_BOX_CODE", title: "混样文库名称",width:"180px" },
            { field: "SAMPLE_SJYY", title: "建库方法",width:"180px" },
            { field: "JC_ITEM", title: "数据量",width:"180px" },
            { field: "SAMPLE_SJYS", title: "混样文库浓度",width:"180px" },
            { field: "C_SJ_ADRRESS", title: "混样文库体积",width:"180px" },
            { field: "C_YS_CON", title: "实际浓度",width:"180px" },
            { field: "C_W_NAME", title: "校准浓度",width:"180px" },
            { field: "C_W_AGE", title: "稀释倍数",width:"180px" },
            { field: "C_W_SEX", title: "混合量",width:"80px" },
        
        // { field: "C_CLENT_PRICE", title: "送检总价",width:"180px" },
        // { field: "C_SALE_MAN", title: "销售员",width:"180px" },
        // { field: "C_IS_CHECK", title: "审核状态",width:"180px" },
        // { field: "C_SALE_REMARK", title: "备注",width:"180px" },
        // { field: "C_IS_PACKING_CHARGE", title: "是否打包收费",width:"180px" },
        // { field: "C_SEPARATE_BILL", title: "是否拆单",width:"180px" }

        ],

    });

    var THREEtoolbar=getButtonTemplates(pathValue,[

        {name:"add",target:"",title:"pooling移除"},
        {name:"add",target:"",title:"自动计算"},
        {name:"excel",target:"",title:"修改"},
        {name:"add",target:"lookSample",title:"批量修改"},
        {name:"add",target:"",title:"导出样本位置表"},
        {name:"excel",target:"",title:"导出文库pooling表"},
        {name:"add",target:"lookSample",title:"导出文库QPCR表"}
        
    ]);//工具条

    //获取数据源生成表格
    $("#THREEdetailGrid"+pathValue).kendoGrid({
        
        dataSource: {

             data:[
                
                 {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小月",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小雪",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
         },
        height: 550,
        toolbar:THREEtoolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [							

            { field: "ID", title: "唯一标识",hidden: true },
            { locked: true, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "SAMPLE_NUM", title: "pooling文库名称",width:"180px" },
            { field: "SAMPLE_BOX_CODE", title: "混样文库名称",width:"180px" },
            { field: "SAMPLE_SJYY", title: "建库方法",width:"180px" },
            { field: "JC_ITEM", title: "数据量",width:"180px" },
            { field: "SAMPLE_SJYS", title: "混样文库浓度",width:"180px" },
            { field: "C_SJ_ADRRESS", title: "混样文库体积",width:"180px" },
            { field: "C_YS_CON", title: "实际浓度",width:"180px" },
            { field: "C_W_NAME", title: "校准浓度",width:"180px" },
            { field: "C_W_AGE", title: "稀释倍数",width:"180px" },
            { field: "C_W_SEX", title: "混合量",width:"80px" },
        
        // { field: "C_CLENT_PRICE", title: "送检总价",width:"180px" },
        // { field: "C_SALE_MAN", title: "销售员",width:"180px" },
        // { field: "C_IS_CHECK", title: "审核状态",width:"180px" },
        // { field: "C_SALE_REMARK", title: "备注",width:"180px" },
        // { field: "C_IS_PACKING_CHARGE", title: "是否打包收费",width:"180px" },
        // { field: "C_SEPARATE_BILL", title: "是否拆单",width:"180px" }

        ],

    });

    var WKZHtoolbar=getButtonTemplates(pathValue,[

        {name:"add",target:"",title:"pooling移除"},
        {name:"add",target:"",title:"自动计算"},
        {name:"excel",target:"",title:"修改"},
        {name:"add",target:"lookSample",title:"批量修改"}
      
        
    ]);//工具条

    //获取数据源生成表格
    $("#WKZHdetailGrid"+pathValue).kendoGrid({
        
        dataSource: {

            
         },
        height: 550,
        toolbar:WKZHtoolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [							
							
            { field: "ID", title: "唯一标识",hidden: true },
            { locked: true, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "SAMPLE_NUM", title: "文库浓度（ng/ul）",width:"180px" },
            { field: "SAMPLE_BOX_CODE", title: "文库投入量（ng）",width:"180px" },
            { field: "SAMPLE_SJYY", title: "接头转换PCR产物纯化浓度（ng/ul）",width:"180px" },
            { field: "JC_ITEM", title: "文库变性加样体积（ul）",width:"180px" },
            { field: "SAMPLE_SJYS", title: "酶切消化产物纯化浓度（ng/ul）",width:"180px" },
            { field: "C_SJ_ADRRESS", title: "makeDNB投入量ng",width:"180px" },
            { field: "C_YS_CON", title: "DNB浓度（ng/ul）",width:"180px" },

        // { field: "C_W_NAME", title: "校准浓度",width:"180px" },
        // { field: "C_W_AGE", title: "稀释倍数",width:"180px" },
        // { field: "C_W_SEX", title: "混合量",width:"80px" },
        // { field: "C_CLENT_PRICE", title: "送检总价",width:"180px" },
        // { field: "C_SALE_MAN", title: "销售员",width:"180px" },
        // { field: "C_IS_CHECK", title: "审核状态",width:"180px" },
        // { field: "C_SALE_REMARK", title: "备注",width:"180px" },
        // { field: "C_IS_PACKING_CHARGE", title: "是否打包收费",width:"180px" },
        // { field: "C_SEPARATE_BILL", title: "是否拆单",width:"180px" }

        ],

    });
    
    var NCtoolbar=getButtonTemplates(pathValue,[

        {name:"add",target:"",title:"新增"},
        {name:"add",target:"",title:"修改内参信息"},
        {name:"excel",target:"",title:"移除"}
        
    ]);//工具条

    //获取数据源生成表格
    $("#NCdetailGrid"+pathValue).kendoGrid({
        
        dataSource: {

             data:[
                
                 {ID:"1",NUM:"1",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小月",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-001",
                 SAMPLE_SJYS:""},
                 {ID:"2",NUM:"2",SAMPLE_BOX_CODE:"YK-20191128",JC_ITEM:"小月",
                 C_SOP_CODE:"小雪",C_SOP_NAME:"血夜",SAMPLE_SJYY:"YK-20191128-002",
                 SAMPLE_SJYS:""}]
         },
        height: 550,
        toolbar:NCtoolbar,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [							

            { field: "ID", title: "唯一标识",hidden: true },
            { locked: true, selectable: true, width: '40px' },
            { field: "NUM", title: "序号",width:"60px" },
            { field: "SAMPLE_NUM", title: "内参名称",width:"180px" },
            { field: "SAMPLE_SJYY", title: "建库方式",width:"180px" },
            { field: "C_W_NAME", title: "校准浓度",width:"180px" },
            { field: "C_YS_CON", title: "实测浓度",width:"180px" },
            { field: "SAMPLE_BOX_CODE", title: "校准比值",width:"180px" },

            // { field: "JC_ITEM", title: "数据量",width:"180px" },
            // { field: "SAMPLE_SJYS", title: "混样文库浓度",width:"180px" },
            // { field: "C_SJ_ADRRESS", title: "混样文库体积",width:"180px" },
            // { field: "C_W_AGE", title: "稀释倍数",width:"180px" },
            // { field: "C_W_SEX", title: "混合量",width:"80px" },
            // { field: "C_CLENT_PRICE", title: "送检总价",width:"180px" },
            // { field: "C_SALE_MAN", title: "销售员",width:"180px" },
            // { field: "C_IS_CHECK", title: "审核状态",width:"180px" },
            // { field: "C_SALE_REMARK", title: "备注",width:"180px" },
            // { field: "C_IS_PACKING_CHARGE", title: "是否打包收费",width:"180px" },
            // { field: "C_SEPARATE_BILL", title: "是否拆单",width:"180px" }

        ],

    });

     //仪器型号
     var YQXH =getButtonTemplates(pathValue,[

        {name:"add",target:"lookYQXH",title:"新增"},
        {name:"edit",target:"lookSample",title:"修改"},
        {name:"delete",target:"lookSample",title:"删除"}
    
        ]);//工具条
    //仪器型号
     //获取数据源生成表格
     $("#YQdetailGrid"+pathValue).kendoGrid({
        
        dataSource: {
            
            data:[{
                
                NUM:"1",
                S_MAN_NAME:"苏州实验室301",
                S_CODE:"YK_SZ_002",
                S_TIME:"扩增仪6500-1",
                S_TYPE:"A公司"
                
            }]
         },
        height: 550,
        toolbar:YQXH,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
            { field: "ID", title: "唯一标识",hidden: true },
            {  selectable: true, width: "60px" },
            { field: "NUM", title: "序号",width:"60px" ,enableColumnResizing: false },
            { field: "S_MAN_NAME", title: "位置",width:"180px" },
            { field: "S_CODE", title: "仪器型号",width:"180px" },
            { field: "S_TIME", title: "仪器名称",width:"180px" },
            { field: "S_TYPE", title: "厂家",width:"180px" }

        ],

    });

    //物料试剂
    var WLSJ =getButtonTemplates(pathValue,[

        {name:"add",target:"lookWLSJ",title:"新增"},
        {name:"edit",target:"lookSample",title:"修改"},
        {name:"delete",target:"lookSample",title:"删除"}
    
        ]);//工具条
    //仪器型号
     //获取数据源生成表格
     $("#WLdetailGrid"+pathValue).kendoGrid({
        
        dataSource: {
            
            data:[
               
                {
                    NUM:"1",
                    S_CODE:"YK_WL_001",
                    S_TIME:"A",
                    S_TYPE:"A",
                    S_NAME:"A公司",
                    S_MAN_NAME:"301",
                    S_WOMEN_NAME:"1",
                    S_WOMEN_AGE:""
                }, {
                    NUM:"2",
                    S_CODE:"YK_WL_002",
                    S_TIME:"B",
                    S_TYPE:"A",
                    S_NAME:"A公司",
                    S_MAN_NAME:"301",
                    S_WOMEN_NAME:"1",
                    S_WOMEN_AGE:""
                }, {
                    NUM:"3",
                    S_CODE:"YK_WL_003",
                    S_TIME:"C",
                    S_TYPE:"A",
                    S_NAME:"A公司",
                    S_MAN_NAME:"301",
                    S_WOMEN_NAME:"1",
                    S_WOMEN_AGE:""
                }, {
                    NUM:"4",
                    S_CODE:"YK_WL_004",
                    S_TIME:"D",
                    S_TYPE:"A",
                    S_NAME:"A公司",
                    S_MAN_NAME:"301",
                    S_WOMEN_NAME:"1",
                    S_WOMEN_AGE:""
                },
                
        ]
         },
        height: 550,
        toolbar:WLSJ,
        scrollable: true,
        selectable: true,
        resizable:true,
        // filterable: true,
        sortable: true,
        pageable: true,
        editable: true,
        columns: [
            { field: "ID", title: "唯一标识",hidden: true },
            {  selectable: true, width: "60px" },
            { field: "NUM", title: "序号",width:"60px" ,enableColumnResizing: false },
            { field: "S_CODE", title: "物料编号",width:"180px" },
            { field: "S_TIME", title: "物料名称",width:"180px" },
            { field: "S_TYPE", title: "物料类型",width:"180px" },
            { field: "S_NAME", title: "物料品牌",width:"180px" },
            { field: "S_MAN_NAME", title: "物料批次",width:"180px" },
            { field: "S_WOMEN_NAME", title: "使用量",width:"180px" },
            { field: "S_WOMEN_AGE", title: "备注",width:"180px" }

        ],

    });
    
    }


    var consoleJsonFrom=function(){
        var jsonData = getJsonByForm("form",pathValue);
        console.log(jsonData);
    }


    var lookSample=function(){
        var params={"id":"xxxx"};
        var winOpts={
            url:"function/config/data/instrumentUpPooling/listSampleMx",
            title:"添加样本",
            window:"#sub-window",
            windowTemplate:"#sub-detail-template"
        };
        var dialog = openWindow(winOpts,params);
    }

    var lookYQXH = function(){
        var params={"id":"xxxx"};
        var winOpts={
            url:"function/config/data/instrumentUpPooling/instrument",
            title:"添加仪器型号",
            window:"#sub-window",
            windowTemplate:"#sub-detail-template"
        };
        var dialog = openWindow(winOpts,params);
    }

    var lookWLSJ = function(){
        var params={"id":"xxxx"};
        var winOpts={
            url:"function/config/data/instrumentUpPooling/materiel",
            title:"添加物料试剂",
            window:"#sub-window",
            windowTemplate:"#sub-detail-template"
        };
        var dialog = openWindow(winOpts,params);
    }


    var submit=function(){
        alert("123");  
    }

    var deleteMX = function(){
        alert("提示：删除成功！");
    }

    var yes=function(times){
        var s="";
        for(var i=0;i<times;i++){
            s+="yes,";
        }
        console.log("sssssssss===>"+s);
        return s;
    }

    /**
     * 注册方法
     */
    funcPushs(pathValue,{
        "init":init,
        "lookSample":lookSample,
        "lookYQXH":lookYQXH,
        "lookWLSJ":lookWLSJ,
        "yes":yes,
        "deleteMX":deleteMX,
        "consoleJsonFrom":consoleJsonFrom,
        //删除
        "submit":submit
    });


});
