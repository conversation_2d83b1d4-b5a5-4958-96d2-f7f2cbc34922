$(document).ready(function() {
    var pathValue="function-config-system-sampleMain-add-add";
    var mainSampleMxGrid ;
    var mainGeneMxGrid;
    var paramsValue;
    var formID = "form1";

        /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"CQ_SAMPLE",
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        setJsonParam(sysNowTimeFuncParams,pathValue+"SYS_DATE",function(sysNowTime){
            for(var i=1;i<=9;i++){
                getInfo("form"+i,pathValue,{SYS_DATE:sysNowTime});
            }
        });
        paramsValue=params;
        showOrHidePage();
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form1",pathValue,params,url,function(v,p){
            setJsonParam(p,"SYS_MAN",getLimsUser()["name"]);
            for(var i=1;i<=10;i++){
                getInfo("form"+i,pathValue,p);//传入id
            }
            showOrHidePage(p["SAMPLE_PROJECT_TYPE"]);
        });
        initmainSampleMxGrid(paramsValue);
        // initmainGeneMxGrid(paramsValue);
    }
    //样本明细
    var initmainSampleMxGrid=function(params){
        var S_TYPE_dataSource=[];
        $.fn.ajaxPost({
            ajaxSync:false,
            ajaxType:"post",
            ajaxUrl:"system/jdbc/query/one/table",
            ajaxData:{"query":"query_maintain_sample_type","objects":[]},
            succeed:function(result){
                var rows=result["rows"];
                for(var i=0;i<rows.length;i++){
                    S_TYPE_dataSource.push({"text":rows[i]["SAMPLE_TYPE"]});
                }
            }
        });
        var  data= getDatasArrayByFormElement("SAMPLE_DEC_WAY",formID,pathValue);
        var SAMPLE_DEC_WAY_MX=[];
        for(var i=0;i<data.length;i++){
            SAMPLE_DEC_WAY_MX.push(data[i]["text"]);
        }
        
        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
            {name:"add",target:"add",title:"添加.."},
            {name:"add",target:"PLadd",title:"批量添加.."},
            {name:"add",target:"PLZLadd",title:"肿瘤批量添加.."},
            {name:"save",target:"saveInfo",title:"修改保存.."},
            {name:"add",target:"addCopySample",title:"样本复制"},
            {name:"add",target:"addDJZCopySample",title:"单精子样本复制"},
            {name:"add",target:"addNewProjectSingle",title:"样本项目拆单"},
            {name:"add",target:"addTumourSample",title:"添加肿瘤样本"},
            // {name:"add",target:"addProjectSingle",title:"样本项目拆单"},
            // {name:"add",target:"addSampleProjectSingle",title:"新样本项目拆单"},
            {name:"delete",target:"deleteInfo"},
            // {name:"add",target:"addGene",title:"添加基因位点"},
            // {name:"import",target:"importData",title:"导入"},
       ]);//工具条
       //请求参数
       var mainSampleMxJson={
           url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
           sort: "sm.S_CODE",//排序
           toolbar: toolbar,
           read:{"query":"query_main_sample_mx","objects":[paramsValue["ID"]]},
           
           headerFilter:function(cols,i){
            if(i){
                if(cols[i]["field"]&&cols[i]["field"]=="S_TYPE"){
                    setJsonParam(cols[i],"template",function (dataItem) {
                                        var value="";
                                        if(dataItem && dataItem["S_TYPE"]){
                                                value = dataItem["S_TYPE"]["value"] ? dataItem["S_TYPE"]["value"] : dataItem["S_TYPE"];
                                            }
                                        return value;
                        });
                    setJsonParam(cols[i],"editor",function (container, options) {
                        $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
                            .appendTo(container)
                            .kendoDropDownList({
                                dataSource: {
                                    data: S_TYPE_dataSource
                                },
                                index: 0,
                                dataValueField: 'value',
                                dataTextField: 'text'
                            });
                    });
                }else if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_DEC_WAY_MX"){
                    setJsonParam(cols[i],"template",function (dataItem) {
                                        var value="";
                                        if(dataItem && dataItem["SAMPLE_DEC_WAY_MX"]){
                                                value = dataItem["SAMPLE_DEC_WAY_MX"]["value"] ? dataItem["SAMPLE_DEC_WAY_MX"]["value"] : dataItem["SAMPLE_DEC_WAY_MX"];
                                            }
                                        return value;
                        });
                    setJsonParam(cols[i],"editor",function (container, options) {
                        $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                            .appendTo(container)
                            .kendoAutoComplete({
                                dataSource: {
                                    data: SAMPLE_DEC_WAY_MX
                                },
                                // index: 0,
                                // dataValueField: 'value',
                                // dataTextField: 'text'
                            });
                    });

                }else if(cols[i]["field"]&&cols[i]["field"]=="S_DATA_VOLUME"){
                    setJsonParam(cols[i],"editor",function (container, options) {
                        $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                            .appendTo(container)
                            .kendoNumericTextBox({});
                    });
                }else if(cols[i]["field"]&&cols[i]["field"]=="S_ZZSL"){
                    setJsonParam(cols[i],"editor",function (container, options) {
                        $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                            .appendTo(container)
                            .kendoNumericTextBox({});
                    });                
                }else if(cols[i]["field"]&&cols[i]["field"]=="S_ZZ_USE"){
                    setJsonParam(cols[i],"editor",function (container, options) {
                        $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                            .appendTo(container)
                            .kendoNumericTextBox({});
                    });                
                }else if(cols[i]["field"]&&cols[i]["field"]=="S_ZZ_SURPLUS"){
                    setJsonParam(cols[i],"editor",function (container, options) {
                        $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                            .appendTo(container)
                            .kendoNumericTextBox({});
                    });                
                }else if(cols[i]["field"]&&cols[i]["field"]=="S_SYDATE"){
                    setJsonParam(cols[i],"editor",function (container, options) {
                        $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                            .appendTo(container)
                            .kendoDatePicker({
                                format: 'yyyy-MM-dd',
                                min: new Date(2000, 0, 1),
                                max: new Date()
                            });
                    });
                }else if(cols[i]["field"]&&cols[i]["field"]=="S_TSAMPLE_DATE"){//采样时间
                    setJsonParam(cols[i],"editor",function (container, options) {
                        //console.log("container",container);
                        $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                            .appendTo(container)
                            .kendoDateTimePicker({
                                format: 'yyyy-MM-dd HH:mm:ss',
                                min: new Date(2000, 0, 1),
                                max: new Date()
                            });
                    });             
                }
            }
        },
        editable: true //编辑行
       };
       mainSampleMxGrid = initKendoGrid("#mainSampleMxGrid"+pathValue,mainSampleMxJson);//初始化表格的方法
   }
   //基因明细
   var initmainGeneMxGrid=function(params){

        /**
         * 列表-按钮-定义
         */
        var toolbar=getButtonTemplates(pathValue,[
                {name:"add",target:"updateGene",title:"编辑.."},
                {name:"delete",target:"deleteGeneInfo"},
            ]);//工具条
        //请求参数
        var mainGeneMxGridJson={
            url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
            sort: "sgm.G_SAMPLE_CODE",//排序
            toolbar: toolbar,
            read:{"query":"query_sy_sample_gene_mx","objects":[paramsValue["ID"]]},
        };
        mainGeneMxGrid = initKendoGrid("#mainGeneMxGrid"+pathValue,mainGeneMxGridJson);//初始化表格的方法
    }
   
   var add=function(){
        if(paramsValue["ID"]==""){
            alert("请先提交主单信息！");
            return ;
        }
        var winOpts={
            url:"function/config/system/sampleMain/add/addSample",
            title:"新增..",
            currUrl:"function/config/system/sampleMain/add/add",
        };
        openWindow(winOpts,{"S_ID":paramsValue["ID"]});
    }

    var PLadd = function(){
        if(paramsValue["ID"]==""){
            alert("请先提交主单信息！");
            return ;
        }
        var winOpts={
            url:"function/config/system/sampleMain/add/pl_add",
            title:"新增..",
            currUrl:"function/config/system/sampleMain/add/add",
        };
        openWindow(winOpts,{"S_ID":paramsValue["ID"]});
    }

    var PLZLadd = function(){
        if(paramsValue["ID"]==""){
            alert("请先提交主单信息！");
            return ;
        }
        var winOpts={
            url:"function/config/system/sampleMain/add/pl_zladd",
            title:"新增..",
            currUrl:"function/config/system/sampleMain/add/add",
        };
        openWindow(winOpts,{"S_ID":paramsValue["ID"]});
    }

    var edit=function(){
        var arrIds=getSelectData(mainSampleMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        openWindow({
            url:"function/config/system/sampleMain/add/update",
            title:"修改..",
            currUrl:"function/config/system/sampleMain/add/add",
        },{"ID":arrIds[0]});
    }


    /**
     * 批量修改保存
     */
    var saveInfo=function(){
        // var arrDatas=getGridItemsData(mainSampleMxGrid);
        // if(arrDatas.length ==0){
        //     alertMsg("请至少选择一条数据进行修改保存");
        //     return;
        // }
        // var params={"datas":arrDatas};
        // var url="modular/sy/control/saveInfo";
        // $.fn.ajaxPost({
        //     ajaxType:"post",
        //     ajaxUrl:url,
        //     ajaxData:params,
        //     succeed:function(result){
        //         if(result["code"]>0){
        //             alertMsg("修改成功")
        //         }
        //         refreshGrid();
        //     }
        // });
        var itemDatas=getGridItemsData(mainSampleMxGrid);
        for(var i=0;i<itemDatas.length;i++){
            var json=itemDatas[i];
            if(formID == "form9"){
                //ERT大类
                if(!json["S_TSAMPLE_DATE"]||json["S_TSAMPLE_DATE"]==""||json["S_TSAMPLE_DATE"]==null){
                    alertMsg("ERT项目样本【采样时间】必填","error");
                    return;
                }
            }
            itemDatas[i]["S_SYDATE"]=toDateFormatByZone(json["S_SYDATE"],'yyyy-MM-dd');
            itemDatas[i]["S_TSAMPLE_DATE"]=toDateFormatByZone(json["S_TSAMPLE_DATE"],'yyyy-MM-dd HH:mm:ss');
        }
        var params={"tableName":"SAMPLE_MX","objects":itemDatas};
        $.fn.ajaxPost({
            ajaxUrl: "system/jdbc/save/batch/table",
            ajaxType: "post",
            ajaxData: params,
            succeed:function(result){
                if(result["code"]>0){
                    alertMsg("保存成功!", "success", function(){
                        refreshGrid();
                    });
                }else{
                    alertMsg("保存失败!","error");
                }
            },
            failed:function(result){
                alertMsg("提示:操作异常!","error");
            }
        });
    }
    
    var addGene=function(componentId){
        var arrIds=getSelectData(mainSampleMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
        openGene(arrIds,componentId);
    }

    var openGene=function(arrIds,componentId){
        openComponent({
            name:"数据源",//组件名称
            title:"基因位点",
            componentId:componentId,
            params:{"query":"query_sample_gene"},
            settings:function(obj,value){
                //选择的数据obj
                var datas=[];
                for(var index in obj){
                    datas.push(obj[index]["ID"])
                }
                $.fn.ajaxPost({
                    ajaxType:"post",
                    ajaxUrl:"modular/sy/control/workflowPlanSettings",
                    ajaxData:{"MAIN_ID":paramsValue["ID"],"IDS":arrIds,"GENE_IDS":datas},
                    succeed:function(result){
                        console.log(result)
                        refreshGrid();
                        alertMsg("添加成功","success");
                        if(result["code"]>0){
                            
                        }else{
                            alertMsg("流程方案设置失败","error");
                        }
                    }
                });
            }
        });
    }

   

    var open=function(ID){
        openWindow({
            url:"function/config/system/sampleMain/add/addSample",
            title:"修改..",
            currUrl:"function/config/system/sampleMain/add/add",
        },{"ID":ID});
    }


    var deleteInfo=function(){
        var arrIds=getSelectData(mainSampleMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"SAMPLE_MX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
    }

    var deleteGeneInfo=function(){
        var arrIds=getSelectData(mainGeneMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行删除操作!");
            return ;
        }
        var params={"tableName":"SY_GENE_MX","ids":arrIds};
        var url="system/jdbc/delete/batch/table";
        deleteGridDataByIds(url,params,refreshGrid);
    }

    var callBack=function(){

        
        if(mainSampleMxGrid){
            /**
             * 列表-按钮-定义
             */
            var toolbar=getButtonTemplates(pathValue,[
                {name:"add",target:"add",title:"添加.."},
                {name:"add",target:"PLadd",title:"批量添加.."},
                {name:"add",target:"PLZLadd",title:"肿瘤批量添加.."},
                {name:"save",target:"saveInfo",title:"修改保存.."},
                {name:"add",target:"addCopySample",title:"样本复制"},
                {name:"add",target:"addDJZCopySample",title:"单精子样本复制"},
                {name:"add",target:"addNewProjectSingle",title:"样本项目拆单"},
                // {name:"add",target:"addProjectSingle",title:"样本项目拆单"},
                // {name:"add",target:"addSampleProjectSingle",title:"新样本项目拆单"},
                {name:"delete",target:"deleteInfo"},
                // {name:"add",target:"addGene",title:"添加基因位点"},
                // {name:"import",target:"importData",title:"导入"},
            ]);//工具条
            //请求参数
            var mainSampleMxJson={
                url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
                sort: "sm.S_CODE",//排序
                toolbar: toolbar,
                read:{"query":"query_main_sample_mx","objects":[paramsValue["ID"]]},
                headerFilter:function(cols,i){
                    if(i){
                        if(cols[i]["field"]&&cols[i]["field"]=="S_TYPE"){
                            setJsonParam(cols[i],"template",function (dataItem) {
                                                var value="";
                                                if(dataItem && dataItem["S_TYPE"]){
                                                        value = dataItem["S_TYPE"]["value"] ? dataItem["S_TYPE"]["value"] : dataItem["S_TYPE"];
                                                    }
                                                return value;
                                });
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<select name="'+ options.field +'" data-bind="value: '+ options.field +'"></select>')
                                    .appendTo(container)
                                    .kendoDropDownList({
                                        dataSource: {
                                            data: S_TYPE_dataSource
                                        },
                                        index: 0,
                                        dataValueField: 'value',
                                        dataTextField: 'text'
                                    });
                            });
                        }else if(cols[i]["field"]&&cols[i]["field"]=="SAMPLE_DEC_WAY_MX"){
                            setJsonParam(cols[i],"template",function (dataItem) {
                                                var value="";
                                                if(dataItem && dataItem["SAMPLE_DEC_WAY_MX"]){
                                                        value = dataItem["SAMPLE_DEC_WAY_MX"]["value"] ? dataItem["SAMPLE_DEC_WAY_MX"]["value"] : dataItem["SAMPLE_DEC_WAY_MX"];
                                                    }
                                                return value;
                            });
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                                    .appendTo(container)
                                    .kendoAutoComplete({
                                        dataSource: {
                                            data: SAMPLE_DEC_WAY_MX
                                        },
                                        index: 0,
                                        dataValueField: 'value',
                                        dataTextField: 'text'
                                    });
                            });
                        }else if(cols[i]["field"]&&cols[i]["field"]=="S_DATA_VOLUME"){
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                                    .appendTo(container)
                                    .kendoNumericTextBox({});
                            });
                        }else if(cols[i]["field"]&&cols[i]["field"]=="S_ZZSL"){
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                                    .appendTo(container)
                                    .kendoNumericTextBox({});
                            });                
                        }else if(cols[i]["field"]&&cols[i]["field"]=="S_ZZ_USE"){
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                                    .appendTo(container)
                                    .kendoNumericTextBox({});
                            });                
                        }else if(cols[i]["field"]&&cols[i]["field"]=="S_ZZ_SURPLUS"){
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                                    .appendTo(container)
                                    .kendoNumericTextBox({});
                            });                
                        }else if(cols[i]["field"]&&cols[i]["field"]=="S_SYDATE"){
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                                    .appendTo(container)
                                    .kendoDatePicker({
                                        format: 'yyyy-MM-dd',
                                        min: new Date(2000, 0, 1),
                                        max: new Date()
                                    });
                            });
                        }else if(cols[i]["field"]&&cols[i]["field"]=="S_TSAMPLE_DATE"){//采样时间
                            //setJsonParam(cols[i],"attributes",{"onmouseover":"$(this).addClass('unclick');"});     
                            setJsonParam(cols[i],"editor",function (container, options) {
                                $('<input name="'+ options.field +'" data-bind="value: '+ options.field +'"/>')
                                    .appendTo(container)
                                    .kendoDateTimePicker({
                                        format: 'yyyy-MM-dd',
                                        min: new Date(2000, 0, 1),
                                        max: new Date()
                                    });
                            });         
                        }
                    }
                },
                editable: true //编辑行
            };
            setGridDataSource("#mainSampleMxGrid"+pathValue,mainSampleMxJson)
        }
        // if(mainGeneMxGrid){
        //     /**
        //      * 列表-按钮-定义
        //      */
        //     var toolbar=getButtonTemplates(pathValue,[
        //         {name:"add",target:"updateGene",title:"编辑.."},
        //         {name:"delete",target:"deleteGeneInfo"},
        //     ]);//工具条
        //     //请求参数
        //     var mainGeneMxGridJson={
        //         url: "system/jdbc/query/one/table",//请求json的路径-针对的查询
        //         sort: "sgm.G_SAMPLE_CODE",//排序
        //         toolbar: toolbar,
        //         read:{"query":"query_sy_sample_gene_mx","objects":[paramsValue["ID"]]},
        //     };
        //     setGridDataSource("#mainGeneMxGrid"+pathValue,mainGeneMxGridJson)
        // }
    };

    var refreshGrid=function(){
        if(mainSampleMxGrid){
            mainSampleMxGrid.dataSource.read({"objects":[paramsValue["ID"]]});//重新读取--刷新
        }
        // if(mainGeneMxGrid){
        //     mainGeneMxGrid.dataSource.read({"objects":[paramsValue["ID"]]});//重新读取--刷新
        // }
    }


    //ERP物料编码
    var setC_SOP_CODE=function(){
        var formJson=getJsonByForm(formID,pathValue);
        var SAMPLE_DEC_WAY=formJson["SAMPLE_DEC_WAY"];
        var data=getDatasArrayByFormElement("SAMPLE_DEC_WAY",formID,pathValue);
        for(var i=0;i<data.length;i++){
            if(data[i]["value"]==SAMPLE_DEC_WAY){//存在值
                getInfo(formID,pathValue,{"C_SOP_CODE":data[i]["MATERIAL_ENCODING"]});
                break;
            }
        }
    }

    
    
    var submit=function(){
        var formJson=getJsonByForm(formID,pathValue);
        var SAMPLE_DEC_WAY=formJson["SAMPLE_DEC_WAY"];
        var SAMPLE_SERIAL_NUMBER=formJson["SAMPLE_SERIAL_NUMBER"];
        if(SAMPLE_SERIAL_NUMBER.indexOf(SAMPLE_DEC_WAY)>-1){

        }else{
            alertMsg("送检单号里面不包含产品英文简写！","error");
            return;
        }
        var N_PERIODIC_INFORMATION=formJson["N_PERIODIC_INFORMATION"];
        if(SAMPLE_DEC_WAY=="ERT" || SAMPLE_DEC_WAY=="ERTSR" || SAMPLE_DEC_WAY=="ERTFREE"){
            if(N_PERIODIC_INFORMATION==""){
                alertMsg("ERT项目样本周期信息必填","error");
                return;
            }
        }
        if(formID == "form9"){
            //ERT大类
            if(formJson["SAMPLE_FIRSTYT_DATE"]==""&&formJson["SAMPLE_LHMAX_DATE"]==""&&formJson["SAMPLE_PL_DATE"]==""){
                alertMsg("ERT项目【首次使用孕酮时间】 【LH峰值出现时间】 【排卵时间】 必填其一","error");
                return;
            }
        }
          
        var ID = $.trim( $("#ID"+pathValue).val() );
        if(ID==""){ 
            $.fn.ajaxPost({
                ajaxUrl:"modular/sy/control/consultManUnit",
                ajaxType: "post",
                ajaxData: getJsonByForm(formID,pathValue),
                succeed:function(result){
                     if(!result["message"]==""){
                        // alertMsg(result["message"]);
                        confirmMsg("提示",result["message"]+",是否进行流程提交操作?","question",function(){
                            SaveMain();
                        });
                        return;
                     }else{
                        SaveMain();
                     }
                 }
            });
        }else{
            setC_SOP_CODE();
            $.fn.ajaxPost({
                ajaxUrl:"modular/sy/control/checkSame",
                ajaxType: "post",
                ajaxData: getJsonByForm(formID,pathValue),
                succeed:function(result){
                    if(result["code"]>0){
                        formSubmit({
                            url:"system/jdbc/save/one/table",
                            formId:formID,
                            pathValue:pathValue,
                            succeed:function(result){
                                if(result["code"]>0){
                                    //提交成功
                                    alertMsg("提交成功","success",function(){
                                        funcExce(pathValue+"pageCallBack");//执行回调
                                        // funcExce(pathValue+"close");//关闭页面
                                        
                                        getInfo(formID,pathValue,{ID:result["ID"]});//传入id
                                        paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});

                                    });
                                }else{
                                    alertMsg("提交失败","error");
                                }
                            }
                        });
                    }
                }
        
                // url:"system/jdbc/save/one/table",
                // formId:formID,
                // pathValue:pathValue,
                // succeed:function(result){
                //     if(result["code"]>0){
                //         //提交成功
                //         alertMsg("提交成功","success",function(){
                //             funcExce(pathValue+"pageCallBack");//执行回调
                //             // funcExce(pathValue+"close");//关闭页面
                            
                //             getInfo(formID,pathValue,{ID:result["ID"]});//传入id
                //             paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});

                //         });
                //     }else{
                //         alertMsg("提交失败","error");
                //     }
                // }
            });
        }
    }

    var SaveMain =function(){
        setC_SOP_CODE();
        $.fn.ajaxPost({
            ajaxUrl:"modular/sy/control/checkSame",
            ajaxType: "post",
            ajaxData: getJsonByForm(formID,pathValue),
             succeed:function(result){
                 if(result["code"]>0){
                    formSubmit({
                        url:"system/jdbc/save/one/table",
                        formId:formID,
                        pathValue:pathValue,
                        succeed:function(result){
                            if(result["code"]>0){
                                //提交成功
                                // alertMsg("提交成功","success",function(){
                                    funcExce(pathValue+"pageCallBack",[result["ID"]]);//执行回调
                                    funcExce(pathValue+"close");//关闭页面

                                    // getInfo(formID,pathValue,{ID:result["ID"]});//传入id    回填
                                    // paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});

                                // });
                            }else{
                                alertMsg("提交失败","error");
                            }
                        }
                    });
                 }
             }
        });
    }

    var submitAndSave=function(){
        var formJson=getJsonByForm(formID,pathValue);
        var SAMPLE_DEC_WAY=formJson["SAMPLE_DEC_WAY"];
        var SAMPLE_SERIAL_NUMBER=formJson["SAMPLE_SERIAL_NUMBER"];
        if(SAMPLE_SERIAL_NUMBER.indexOf(SAMPLE_DEC_WAY)>-1){

        }else{
            alertMsg("送检单号里面不包含产品英文简写！","error");
            return;
        }
        var N_PERIODIC_INFORMATION=formJson["N_PERIODIC_INFORMATION"];
        if(SAMPLE_DEC_WAY=="ERT" || SAMPLE_DEC_WAY=="ERTSR" || SAMPLE_DEC_WAY=="ERTFREE"){
            if(N_PERIODIC_INFORMATION==""){
                alertMsg("ERT项目样本周期信息必填","error");
                return;
            }
        }
        if(formID == "form9"){
            //ERT大类
            if(formJson["SAMPLE_FIRSTYT_DATE"]==""&&formJson["SAMPLE_LHMAX_DATE"]==""&&formJson["SAMPLE_PL_DATE"]==""){
                alertMsg("ERT项目【首次使用孕酮时间】 【LH峰值出现时间】 【排卵时间】 必填其一","error");
                return;
            }
        }
        setC_SOP_CODE();
        $.fn.ajaxPost({
            ajaxUrl:"modular/sy/control/checkSame",
            ajaxType: "post",
            ajaxData: getJsonByForm(formID,pathValue),
             succeed:function(result){
                 if(result["code"]>0){
                    formSubmit({
                        url:"system/jdbc/save/one/table",
                        formId:formID,
                        pathValue:pathValue,
                        succeed:function(result){
                            if(result["code"]>0){
                                //提交成功
                                // alertMsg("提交成功","success",function(){
                                    funcExce(pathValue+"pageCallBack",[result["ID"]]);//执行回调
                                    funcExce(pathValue+"close");//关闭页面

                                    // getInfo(formID,pathValue,{ID:result["ID"]});//传入id    回填
                                    // paramsValue=$.extend({}, paramsValue,{"ID":result["ID"]});

                                // });
                            }else{
                                alertMsg("提交失败","error");
                            }
                        }
                    });
                 }
             }
        });
    }




    var upload=function upload(file){
        var json={ // 参数默认值
            //url: 'system/config/upload/fileUpload',//上传的路径-默认
            uploadPath: 'default-path',//默认路径
            saveMode: 'default-path-save-mode',//文件-保存-方式
            //fileTypeLimit: '*.doc;*.docx',//上传文件的限制-后缀格式分号（;）分隔 格式 -- *.doc;*.docx
            handlerKey: 'file-log',//业务处理的key - 如果没有 -则后续的参数无效
            handlerParams:'123',//业务处理的参数 - 可以自定义
            callBack: null //回调方法
        };
        uploadFile(file);
    }

    var download=function(fileNames){
        var files = fileNames.split(";");
        for(var index in files){
            downloadFile(files[index]);
        }
    }

    var displayPage = function(obj){
        console.log(obj);
        console.log(obj.value);
        // var SAMPLE_PROJECT_TYPE = $("#SAMPLE_PROJECT_TYPE").val();
        // var formJson1=getJsonByForm("form1",pathValue);
        // var viewModel=funcExce(pathValue+"getViewModel."+formID);
        // var SAMPLE_TYPE_OBJ=viewModel.get("SAMPLE_PROJECT_TYPE");
        // alert(obj.value);
        showOrHidePage(obj.value);
    }

    var showOrHidePage = function(type){
        for(var i=1;i<=10;i++){
            $("#form"+i+pathValue).hide();
        }
        
        if(type=="NIPT"){
            formID = "form1";
        }else if(type=="PGD_MaReCs"){
            formID = "form2";
        }else if(type=="PGS"){
            formID = "form3";
        }else if(type=="SF"){
            formID = "form4";
        }else if(type=="NICS"){
            formID = "form5";
        }else if(type=="PGD_JX"){
            formID = "form6";
        }else if(type=="肿瘤"){
            formID = "form7";
        }else if(type=="通用模块"){
            formID = "form8";
        }else if(type=="ERT"){
            formID = "form9";
        }else if(type=="科研"){
            formID = "form10";
        }else{
            formID = "form1";
        }
        $("#"+formID+pathValue).show();
        if(type){
            var formJson=getJsonByForm(formID,pathValue);
            var SAMPLE_DEC_WAY=formJson["SAMPLE_DEC_WAY"];
            var data = getDatasArrayByFormElement("SAMPLE_DEC_WAY",formID,pathValue);
            var array=[];
            var b=false;
            for(var i=0;i<data.length;i++){
                if(data[i]["SAMPLE_PROJECT_TYPE"]==type){//产品类型相同
                    array.push(data[i]);
                }
                if(data[i]["SAMPLE_PROJECT_TYPE"]==SAMPLE_DEC_WAY){//存在值
                    b=true;
                }
            }
            if(b){
                setDataSourceToFormElement("SAMPLE_DEC_WAY",formID,pathValue,array);
                getInfo(formID,pathValue,{"SAMPLE_PROJECT_TYPE":type,"SAMPLE_DEC_WAY":SAMPLE_DEC_WAY});
            }else{
                setDataSourceToFormElement("SAMPLE_DEC_WAY",formID,pathValue,array)
                getInfo(formID,pathValue,{"SAMPLE_PROJECT_TYPE":type});
            }
        }
    }

    var addProjectSingle = function(){
       
        var arrIds=getSelectData(mainSampleMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
        // var winOpts={
        //     url:"function/config/system/sampleMain/add/addProjectSingle",
        //     title:"修改..",
        //     currUrl:"function/config/system/sampleMain/add/add",
        // };
        // openWindow(winOpts,{"IDS":arrIds});

        var params={"IDS":arrIds};
        var url="modular/sy/control/addProjectSingle";
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:url,
            ajaxData:params,
            succeed:function(result){
                console.log(result);
                refreshGrid();
            }
        });
       
    }

    var addSampleProjectSingle = function(){
       
        if(paramsValue["ID"]==""){
            alert("请先提交主单信息！");
            return ;
        }
        var winOpts={
            url:"function/config/system/sampleMain/add/addProjectSingle",
            title:"修改..",
            currUrl:"function/config/system/sampleMain/add/add",
        };
        openWindow(winOpts,{"S_ID":paramsValue["ID"]});

        // var params={"IDS":arrIds};
        // var url="modular/sy/control/addProjectSingle";
        // $.fn.ajaxPost({
        //     ajaxType:"post",
        //     ajaxUrl:url,
        //     ajaxData:params,
        //     succeed:function(result){
        //         console.log(result);
        //         refreshGrid();
        //     }
        // });
       
    }

    var updateGene = function(){
       
        var arrIds=getSelectData(mainGeneMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }else if(arrIds.length!=1){
            alertMsg("请只选择一条数据进行修改操作!");
            return ;
        }
        
        var winOpts={
            url:"function/config/system/sampleMain/add/updateGene",
            title:"修改..",
            currUrl:"function/config/system/sampleMain/add/add",
        };
        openWindow(winOpts,{"ID":arrIds[0]});
    }

    /**
     * 从SRS选择
     */
    var openSrs=function(componentId){
        openComponent({
            name:"数据源",//组件名称
            title:"SRS选择",
            componentId:componentId,
            params:{"query":"query_component_srs_sample_temp","limit":1},
            settings:function(obj,value){
                var FLOW_NAME;
                var FLOW_PLAN_ID;
                if(isArray(obj)){
                    FLOW_NAME=obj[0]["FLOW_NAME"];
                    FLOW_PLAN_ID=obj[0]["ID"];
                    obj=obj[0];
                }else{
                    FLOW_NAME=obj["FLOW_NAME"];
                    FLOW_PLAN_ID=obj["ID"];
                }
                var sampleJson={};
                setJsonParam(sampleJson,"SAMPLE_DEC_WAY",obj["C_DEC_WAY"]);
                setJsonParam(sampleJson,"C_TSAMPLE_DATE",obj["C_TSAMPLE_DATE"]);
                setJsonParam(sampleJson,"SAMPLE_SJYY_CODE",obj["SAMPLE_SJYY_CODE"]);//送检单位代码
                setJsonParam(sampleJson,"N_W_NUMBER",obj["C_W_PHONE"]);//受检人联系电话
                // setJsonParam(sampleJson,"",obj["C_SOP_CODE"]);//检测项目
                setJsonParam(sampleJson,"C_IF_PACKING_CHARGE",obj["C_IS_PACKING_CHARGE"]);//是否打包收费
                setJsonParam(sampleJson,"N_W_GENDER",obj["C_W_SEX"]);//性别
                // setJsonParam(sampleJson,"C_SJ_ADRRESS",obj["C_SJ_ADRRESS"]);//送检地
                setJsonParam(sampleJson,"CUSTOMITEM127__C",obj["CUSTOMITEM127__C"]);//合作类型
                setJsonParam(sampleJson,"C_CASH_TYPE",obj["C_CASH_TYPE"]);//回款方式
                setJsonParam(sampleJson,"C_CHARGE_NUMBER",obj["CUSTOMITEM62__C"]);//计价数量(对应lims收费样本数量)
                setJsonParam(sampleJson,"N_W_AGE",obj["C_W_AGE"]);//受检年龄
                setJsonParam(sampleJson,"C_SALE_MAN",obj["C_SALE_MAN"]);//销售员
                setJsonParam(sampleJson,"C_SRS_ORDER_NOTE",obj["C_SALE_REMARK"]);//备注
                // setJsonParam(sampleJson,"SAMPLE_SJYY",obj["SAMPLE_SJYY"]);//送检单位
                setJsonParam(sampleJson,"C_IS_ZQ",obj["C_IS_ZQ"]);//有无帐期
                // setJsonParam(sampleJson,"",obj["C_DEC_WAY_NAME"]);//检测方案名称
                setJsonParam(sampleJson,"SAMPLE_BOX_CODE",obj["SAMPLE_BOX_CODE"]);//送检码
                // setJsonParam(sampleJson,"",obj["C_CLENT_PRICE"]);//送检总价
                setJsonParam(sampleJson,"C_SJ_ADRRESS",obj["C_SJ_ADRRESS_CHINESE"]);//送检地中文
                setJsonParam(sampleJson,"SAMPLE_SJYS",obj["SAMPLE_SJYS"]);//送检医师
                setJsonParam(sampleJson,"N_W_NAME",obj["C_W_NAME"]);//受检姓名
                setJsonParam(sampleJson,"C_SEPARATE_BILL",obj["C_SEPARATE_BILL"]);//是否拆单
                setJsonParam(sampleJson,"SAMPLE_PROJECT_TYPE",obj["C_ORDER_TYPE"]);//订单类型
                setJsonParam(sampleJson,"CUSTOMER_ADD",obj["CUSTOMER_ADD"]);//客户地址
                setJsonParam(sampleJson,"SAMPLE_NUM",obj["SAMPLE_NUM"]);//样本数量
                setJsonParam(sampleJson,"C_YS_CON",obj["C_YS_CON"]);//收样运输条件
                setJsonParam(sampleJson,"SAMPLE_SJYY",obj["C_SJYY"]);//送检医院


                getInfo("form1",pathValue, sampleJson);//传入参数
                getInfo("form2",pathValue, sampleJson);//传入参数
                getInfo("form3",pathValue, sampleJson);//传入参数
                getInfo("form4",pathValue, sampleJson);//传入参数
                getInfo("form5",pathValue, sampleJson);//传入参数
                getInfo("form6",pathValue, sampleJson);//传入参数
                getInfo("form7",pathValue, sampleJson);//传入参数
                getInfo("form8",pathValue, sampleJson);//传入参数
                getInfo("form9",pathValue, sampleJson);//传入参数
                // //指定了流程的方案名称
                // $.fn.ajaxPost({
                //     ajaxType:"post",
                //     ajaxUrl:"modular/yy/control/workflowPlanSettings",
                //     ajaxData:{"PROJECT_IDS":arrIds,"IDS":arrMxIds,"FLOW_NAME":FLOW_NAME,"FLOW_PLAN_ID":FLOW_PLAN_ID},
                //     succeed:function(result){
                //         if(result["code"]>0){
                //             refreshGrid(projectPendingGrid);
                //             alertMsg("流程方案设置成功","success");
                //         }else{
                //             alertMsg("流程方案设置失败","error");
                //         }
                //     }
                // });
            }
        });
    }
    
    var dailogOpenCallBack=function(callBackJson){
        if(callBackJson){
            var id=callBackJson["id"];
            if(id=="SAMPLE_DEC_WAY"){//弹框回调方法-检测方案弹框选择的回调
                var viewModel=callBackJson["viewModel"];
                var C_SJ_CYCLE = getNumberByRemoveLetter(viewModel.get("C_SJ_CYCLE"));//周期-天
                var RECEIVE_SAMPLE_DATE=viewModel.get("RECEIVE_SAMPLE_DATE");//收样时间
                if(RECEIVE_SAMPLE_DATE&&RECEIVE_SAMPLE_DATE!=""){
                    var startTime = new Date(RECEIVE_SAMPLE_DATE).getTime();
                    var diff = C_SJ_CYCLE*86400*1000;
                    var endTime = startTime + diff;
                    var d = new Date(endTime);
                    viewModel.set("C_SJ_DEADLINE",d);//截止时间
                }
            }
        }
    }

    //表格导入
    var importData=function(componentId){
        var gridOptions=mainSampleMxGrid.getOptions();
        var formJson=getJsonByForm(formID,pathValue);
        openComponent({
            name:"导入数据",//组件名称
            componentId:componentId,
            params:{
            // "template":"样本批量模板导入20200512155153503.xlsx",//第一种方式
             "template":function(){
                 downloadFile("样本批量模板导入20200616110031866.xls","report-template-path");
             },//第二种方式
            "import":function(info,importPathValue){
                $.fn.ajaxPost({
                    ajaxUrl: "modular/sy/control/addBatchSample",
                    ajaxType: "post",
                    ajaxData: {"info":info,"columns":gridOptions.columns,"S_ID":formJson["ID"]},
                    succeed:function(result){
                        if(result["code"]>0){
                            refreshGrid();
                            alertMsg("导入成功!","success",funcExce(importPathValue+"close"));
                        }else{
                            alertMsg("导入失败!","success");
                        }
                    },
                    failed:null
                });
            }}
        });
    }

    var endDateHandler=function(){
        var data = getDatasArrayByFormElement("SAMPLE_DEC_WAY",formID,pathValue);
        var formJson=getJsonByForm(formID,pathValue);
        var SAMPLE_DEC_WAY=formJson["SAMPLE_DEC_WAY"];//检测方案
        for(var i=0;i<data.length;i++){
            if(data[i]["text"]==SAMPLE_DEC_WAY){
                getInfo(formID,pathValue,{"C_SJ_CYCLE":data[i]["C_SJ_CYCLE"]});//检测方案对应的-周期
                countEndDate();
                break;
            }
        }
    }

    var countEndDate=function(){
        var formJson=getJsonByForm(formID,pathValue);
        var C_SJ_CYCLE = formJson["C_SJ_CYCLE"];//周期-天
        var RECEIVE_SAMPLE_DATE=formJson["RECEIVE_SAMPLE_DATE"];//收样时间
        if(RECEIVE_SAMPLE_DATE&&RECEIVE_SAMPLE_DATE!=""){
            var startTime = new Date(RECEIVE_SAMPLE_DATE).getTime();
            var diff = C_SJ_CYCLE*86400*1000;
            var endTime = startTime + diff;
            var d = new Date(endTime);
            getInfo(formID,pathValue,{"C_SJ_DEADLINE":toDateFormatByZone(d)});//截止时间
        }
    }

    var addCopySample = function(){
        var arrIds=getSelectData(mainSampleMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条样本数据进行操作!");
            return ;
        }
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:"modular/sy/control/addCopySample",
            ajaxData:{"IDS":arrIds},
            succeed:function(result){
                if(result["code"]>0){
                    refreshGrid();
                    alertMsg("复制成功","success");
                }else{
                    alertMsg(result["message"],"error");
                }
            }
        });
    }

    var addDJZCopySample = function(){
        var arrIds=getSelectData(mainSampleMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条样本数据进行操作!");
            return ;
        }
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:"modular/sy/control/addDJZCopySample",
            ajaxData:{"IDS":arrIds},
            succeed:function(result){
                if(result["code"]>0){
                    refreshGrid();
                    alertMsg("复制成功","success");
                }else{
                    alertMsg(result["message"],"error");
                }
            }
        });
    }
        
    var addNewProjectSingle = function(){
       
        var arrIds=getSelectData(mainSampleMxGrid);
        if(arrIds.length==0){
            alertMsg("请至少选择一条数据进行修改!");
            return ;
        }
        // var winOpts={
        //     url:"function/config/system/sampleExamine/add/addProjectSingle",
        //     title:"修改..",
        //     currUrl:"function/config/system/sampleExamine/add/add",
        // };
        // openWindow(winOpts,{"IDS":arrIds});

        var params={"IDS":arrIds,"S_ID":paramsValue["ID"]};
        var url="modular/sy/control/addNewProjectSingle";
        $.fn.ajaxPost({
            ajaxType:"post",
            ajaxUrl:url,
            ajaxData:params,
            succeed:function(result){
                console.log(result);
                refreshGrid();
            }
        });
    }

    /**
     * 添加肿瘤样本
     */
    var addTumourSample=function(componentId){
        var formJson=getJsonByForm(formID,pathValue);
        var SAMPLE_SERIAL_NUMBER=formJson["SAMPLE_SERIAL_NUMBER"];
        var N_W_NAME=formJson["N_W_NAME"];

        var C_SAMPLE_PHOTO=formJson["C_SAMPLE_PHOTO"];
        var C_SJD_FILE=formJson["C_SJD_FILE"];
        
        var number8="";//8位码-外送的会没有
        number8+=SAMPLE_SERIAL_NUMBER?SAMPLE_SERIAL_NUMBER.length>=8?SAMPLE_SERIAL_NUMBER.substring(SAMPLE_SERIAL_NUMBER.length-8):SAMPLE_SERIAL_NUMBER:"";
        number8=number8.substring(number8.length-8);
        //"ZL_SUBJECT":N_W_NAME
        openComponent({
            name:"数据源",//组件名称
            title:"肿瘤样本:选择",
            componentId:componentId,
            params:{"query":"query_component_tumour_sample_choice","search":{}},
            settings:function(obj,value){
                var arrIds=[];
                var MAIN_FILES=[];
                var SAMPLE_PHOTOS=[];
                if(C_SJD_FILE!=""){
                    MAIN_FILES=MAIN_FILES.concat(C_SJD_FILE.split(";"));
                }
                if(C_SAMPLE_PHOTO!=""){
                    SAMPLE_PHOTOS=SAMPLE_PHOTOS.concat(C_SAMPLE_PHOTO.split(";"));
                }
                if(isArray(obj)){
                    for(var index in obj){
                        arrIds.push(obj[index]["ID"]);

                        var MAIN_FILE=obj[index]["MAIN_FILE"];
                        var SAMPLE_PHOTO=obj[index]["SAMPLE_PHOTO"];
                        if(MAIN_FILE!=""){
                            MAIN_FILES=MAIN_FILES.concat(MAIN_FILE.split(";"));
                        }
                        if(SAMPLE_PHOTO!=""){
                            SAMPLE_PHOTOS=SAMPLE_PHOTOS.concat(SAMPLE_PHOTO.split(";"));
                        }
                    }
                }else{
                    arrIds.push(obj["ID"]);
                    var MAIN_FILE=obj["MAIN_FILE"];
                    var SAMPLE_PHOTO=obj["SAMPLE_PHOTO"];
                    if(MAIN_FILE!=""){
                        MAIN_FILES=MAIN_FILES.concat(MAIN_FILE.split(";"));
                    }
                    if(SAMPLE_PHOTO!=""){
                        SAMPLE_PHOTOS=SAMPLE_PHOTOS.concat(SAMPLE_PHOTO.split(";"));
                    }
                }
                console.log("SAMPLE_PHOTOS",SAMPLE_PHOTOS);
                console.log("MAIN_FILES",MAIN_FILES);
                getInfo(formID,pathValue,{"C_SAMPLE_PHOTO":unique(SAMPLE_PHOTOS).join(";"),"C_SJD_FILE":unique(MAIN_FILES).join(";")});
                $.fn.ajaxPost({
                    ajaxType:"post",
                    ajaxUrl:"modular/sy/control/addTumourSample",
                    ajaxData:{"MAIN_ID":paramsValue["ID"],"IDS":arrIds,"NUMBER8":number8},
                    succeed:function(result){
                        if(result["code"]>0){
                            refreshGrid();
                            alertMsg("添加成功","success",function(){
                                //回填部分主单信息 //TODO
                            });
                        }else{
                            alertMsg(result["message"],"error");
                        }
                    }
                });
            }
        });
    }

    funcPushs(pathValue,{
        "init":init,
        "add":add,
        "SaveMain":SaveMain,
        "PLadd":PLadd,
        "PLZLadd":PLZLadd,
        "upload":upload,
        "download":download,
        "addProjectSingle":addProjectSingle,
        "addNewProjectSingle":addNewProjectSingle,
        "addSampleProjectSingle":addSampleProjectSingle,
        "displayPage":displayPage,
        "edit":edit,
        "addCopySample":addCopySample,
        "addDJZCopySample":addDJZCopySample,
        "addGene":addGene,
        "updateGene":updateGene,
        "openGene":openGene,
        "deleteGeneInfo":deleteGeneInfo,
        "deleteInfo":deleteInfo,
        "callBack":callBack,
        "initmainSampleMxGrid":initmainSampleMxGrid,
        "initmainGeneMxGrid":initmainGeneMxGrid,
        "submit":submit,
        "openSrs":openSrs,
        "dailogOpenCallBack":dailogOpenCallBack,
        "importData":importData,
        "endDateHandler":endDateHandler,
        "countEndDate":countEndDate,
        "submitAndSave":submitAndSave,
        "saveInfo":saveInfo,
        "addTumourSample":addTumourSample,
    });
 
 });
 