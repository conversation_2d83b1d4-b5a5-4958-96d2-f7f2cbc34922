$(document).ready(function() {
    var pathValue="function-config-system-sampleMain-add-pl_zladd";
    var paramsValue; 

    /**
     * 初始化数据-无参
     */
    var initData=function(){
        return {
            tableName:"SAMPLE_MX"
        };
    }
    /**
     * 初始化-获取参数-并执行调用
     * @param {*} params 
     */
    var init=function(params){
        paramsValue = params;
        getInfo("form",pathValue,params);
        // 传入数组ids
        var url="system/jdbc/query/info/"+initData().tableName;//后端请求路径
        getInfo("form",pathValue,params,url);//传入id
    }
 
 
    var submit=function(){
        formSubmit({
            url:"modular/sy/control/zlpladdSample",
            formId:"form",
            pathValue:pathValue,
            jsonData:paramsValue,//覆盖，用于传ID
            succeed:function(result){
                if(result["code"]>0){
                    //提交成功
                    alertMsg("保存成功","success",function(){
                        funcExce(pathValue+"pageCallBack");//执行回调
                        funcExce(pathValue+"close");//关闭页面
                        //getInfo("form",pathValue,{ID:result["ID"]});//传入id
                    });
                }else{
                    alertMsg("保存失败","error");
                }
            }
        });
        
    }


    funcPushs(pathValue,{
        "init":init,
        "submit":submit,
    });
 
 });
 